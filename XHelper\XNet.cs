﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Threading;
using System.Net;
using System.Net.Http.Headers;
using System.Diagnostics;
using XHelper.DanDingNet;

namespace XHelper
{
    public static class XNet
    {
        private static readonly HttpClient _httpClient;
        private static readonly JsonSerializerOptions _jsonOptions;

        static XNet()
        {
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromMinutes(5) // 下载文件时需要更长的超时时间
            };

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                WriteIndented = true
            };

            // 配置默认请求头
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            _httpClient.DefaultRequestHeaders.AcceptEncoding.Add(new StringWithQualityHeaderValue("gzip"));
            _httpClient.DefaultRequestHeaders.AcceptEncoding.Add(new StringWithQualityHeaderValue("deflate"));
            _httpClient.DefaultRequestHeaders.Connection.Add("keep-alive");
        }

        private static async Task<T> RetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3)
        {
            using var diagnosticListener = new DiagnosticListener("XNet.Http");

            for (int i = 0; i < maxRetries; i++)
            {
                var sw = Stopwatch.StartNew();
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                    var task = operation();

                    if (diagnosticListener.IsEnabled("HttpRequest.Start"))
                    {
                        diagnosticListener.Write("HttpRequest.Start", new { Attempt = i + 1 });
                    }

                    var result = await task.WaitAsync(cts.Token);

                    if (diagnosticListener.IsEnabled("HttpRequest.Stop"))
                    {
                        diagnosticListener.Write("HttpRequest.Stop", new { Attempt = i + 1, ElapsedMs = sw.ElapsedMilliseconds });
                    }

                    return result;
                }
                catch (Exception e) when (
                    e is HttpRequestException ||
                    e is TaskCanceledException ||
                    e is OperationCanceledException ||
                    e is TimeoutException)
                {
                    if (diagnosticListener.IsEnabled("HttpRequest.Error"))
                    {
                        diagnosticListener.Write("HttpRequest.Error", new
                        {
                            Attempt = i + 1,
                            Exception = e,
                            ElapsedMs = sw.ElapsedMilliseconds
                        });
                    }

                    if (i == maxRetries - 1)
                    {
                        XLogger.Error($"重试{maxRetries}次后仍然失败: {e.Message}");
                        throw;
                    }

                    int delayMs = (int)Math.Pow(2, i) * 1000;
                    XLogger.Debug($"请求失败，等待 {delayMs / 1000} 秒后进行第 {i + 1} 次重试: {e.Message}");
                    await Task.Delay(delayMs);
                }
                finally
                {
                    sw.Stop();
                }
            }
            throw new Exception($"重试{maxRetries}次后仍然失败");
        }

        public static async Task<string?> GetStringAsync(string url)
        {
            try
            {
                return await RetryAsync(async () =>
                {
                    using var request = new HttpRequestMessage(HttpMethod.Get, url);
                    request.Headers.AcceptEncoding.Add(new StringWithQualityHeaderValue("gzip"));
                    request.Headers.AcceptEncoding.Add(new StringWithQualityHeaderValue("deflate"));

                    using var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                    response.EnsureSuccessStatusCode();

                    // 获取响应内容
                    var contentStream = await response.Content.ReadAsStreamAsync();

                    // 检查是否是GZIP压缩
                    var contentEncoding = response.Content.Headers.ContentEncoding;
                    if (contentEncoding.Contains("gzip"))
                    {
                        using var gzipStream = new System.IO.Compression.GZipStream(contentStream, System.IO.Compression.CompressionMode.Decompress);
                        using var decompressedStream = new MemoryStream();
                        await gzipStream.CopyToAsync(decompressedStream);
                        decompressedStream.Position = 0;
                        using var reader = new StreamReader(decompressedStream, Encoding.UTF8);
                        return await reader.ReadToEndAsync();
                    }

                    // 非压缩内容
                    using var streamReader = new StreamReader(contentStream, Encoding.UTF8);
                    return await streamReader.ReadToEndAsync();
                });
            }
            catch (Exception ex)
            {
                XLogger.Error($"GET请求失败 ({url}): {ex.Message}");
                if (ex is HttpRequestException httpEx)
                {
                    XLogger.Debug($"HTTP状态码: {httpEx.StatusCode}, 原因: {httpEx.Message}");
                }
                return null;
            }
        }

        public static async Task<string?> PostStringAsync(string url, string postData)
        {
            try
            {
                return await RetryAsync(async () =>
                {
                    using var content = new StringContent(postData, Encoding.UTF8, "application/json");
                    using var request = new HttpRequestMessage(HttpMethod.Post, url)
                    {
                        Content = content
                    };

                    using var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                    response.EnsureSuccessStatusCode();

                    using var stream = await response.Content.ReadAsStreamAsync();
                    using var reader = new StreamReader(stream);
                    return await reader.ReadToEndAsync();
                });
            }
            catch (Exception ex)
            {
                XLogger.Error($"POST请求失败 ({url}): {ex.Message}");
                if (ex is HttpRequestException httpEx)
                {
                    XLogger.Debug($"HTTP状态码: {httpEx.StatusCode}, 原因: {httpEx.Message}");
                }
                return null;
            }
        }

        /// <summary>
        /// 下载文件并汇报进度
        /// </summary>
        /// <param name="url"></param>
        /// <param name="destinationPath"></param>
        /// <param name="progress"></param>
        /// <returns></returns>
        public static async Task DownloadFileAsync(string url, string destinationPath, IProgress<double> progress = null)
        {
            try
            {
                // 获取文件大小
                using var response = await _httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Head, url));
                response.EnsureSuccessStatusCode();
                long? totalBytes = response.Content.Headers.ContentLength;

                if (totalBytes == null)
                {
                    throw new Exception("无法获取文件大小");
                }

                long totalSize = totalBytes.Value;

                // 根据文件大小动态调整分段数，最小4段，最大16段
                int numberOfParts = Math.Min(16, Math.Max(4, (int)(totalSize / (1024 * 1024 * 5)))); // 每5MB一个分段
                long partSize = totalSize / numberOfParts;

                List<Task> downloadTasks = new List<Task>();
                long totalRead = 0;
                object progressLock = new object();

                // 创建取消令牌，用于出错时取消所有任务
                using var cts = new CancellationTokenSource();

                // 下载每个部分并行
                for (int i = 0; i < numberOfParts; i++)
                {
                    long startByte = i * partSize;
                    long endByte = (i == numberOfParts - 1) ? totalSize - 1 : (startByte + partSize - 1);

                    int partIndex = i;
                    downloadTasks.Add(Task.Run(async () =>
                    {
                        var buffer = new byte[81920];
                        var request = new HttpRequestMessage(HttpMethod.Get, url);
                        request.Headers.Range = new System.Net.Http.Headers.RangeHeaderValue(startByte, endByte);

                        using var partResponse = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                        partResponse.EnsureSuccessStatusCode();

                        string partPath = $"{destinationPath}.part{partIndex}";
                        using var stream = await partResponse.Content.ReadAsStreamAsync();
                        using var fileStream = new FileStream(partPath, FileMode.Create, FileAccess.Write, FileShare.None, 81920, true);

                        long partBytesRead = 0;
                        int bytesRead;

                        while ((bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                        {
                            if (cts.Token.IsCancellationRequested) break;

                            await fileStream.WriteAsync(buffer, 0, bytesRead);
                            partBytesRead += bytesRead;

                            lock (progressLock)
                            {
                                totalRead += bytesRead;
                                double percentage = (double)totalRead / totalSize * 100;
                                progress?.Report(Math.Min(99, percentage)); // 保留最后1%给合并操作
                            }

                            // 模拟网络延迟，防止CPU过载
                            if (partBytesRead % (1024 * 1024) == 0) // 每1MB
                            {
                                await Task.Delay(1);
                            }
                        }
                    }, cts.Token));
                }

                try
                {
                    await Task.WhenAll(downloadTasks);
                }
                catch (Exception)
                {
                    // 如果有任何任务失败，取消所有其他任务
                    cts.Cancel();
                    throw;
                }

                // 合并文件
                await Task.Run(() => MergeFileParts(destinationPath, numberOfParts));

                // 报告100%完成
                progress?.Report(100);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"下载文件时出错: {ex.Message}");
                throw; // 重新抛出异常，让调用者知道下载失败
            }
        }

        /// <summary>
        /// 合并分段文件
        /// </summary>
        private static void MergeFileParts(string destinationPath, int numberOfParts)
        {
            using (var destinationStream = new FileStream(destinationPath, FileMode.Create))
            {
                for (int i = 0; i < numberOfParts; i++)
                {
                    string partPath = $"{destinationPath}.part{i}";
                    using (var partStream = new FileStream(partPath, FileMode.Open))
                    {
                        partStream.CopyTo(destinationStream);
                    }
                    File.Delete(partPath); // 合并后删除分段文件
                }
            }
        }

        // 下载文件
        public static async Task DownloadFileAsync(string url, string destinationPath)
        {
            try
            {
                using HttpResponseMessage response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();
                using Stream responseStream = await response.Content.ReadAsStreamAsync();
                using FileStream fileStream = new(destinationPath, FileMode.Create, FileAccess.Write);
                responseStream.CopyTo(fileStream);
            }
            catch (Exception e)
            {
                Console.WriteLine("下载文件时出错: " + e.Message);
            }
        }

        // 上传文件
        public static async Task UploadFileAsync(string url, string filePath)
        {
            try
            {
                using (var fileStream = new FileStream(filePath, FileMode.Open))
                {
                    using (var fileContent = new StreamContent(fileStream))
                    {
                        fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");
                        HttpResponseMessage response = await _httpClient.PostAsync(url, fileContent);
                        response.EnsureSuccessStatusCode();
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine("上传文件时出错: " + e.Message);
            }
        }

        /// <summary>
        /// 存储cookie到文件
        /// </summary>
        /// <param name="cookies"></param>
        /// <param name="filePath"></param>
        public static void SaveCookiesToFile(IEnumerable<string> cookies, string filePath)
        {
            // 序列化cookie
            string cookieJson = JsonSerializer.Serialize(cookies, _jsonOptions);

            // 将序列化后的字符串保存到文件
            File.WriteAllText(filePath, cookieJson);
        }

        /// <summary>
        /// 读取cookie文件到cookie
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static IEnumerable<string> LoadCookiesFromFile(string filePath)
        {
            if (!File.Exists(filePath))
                return [];

            // 从文件中读取cookie字符串
            string cookieJson = File.ReadAllText(filePath);

            // 反序列化字符串回Cookie集合
            List<string>? cookies = JsonSerializer.Deserialize<List<string>>(cookieJson, _jsonOptions);
            if (cookies is null) return [];
            return cookies;
        }

        /// <summary>
        /// 是否为邮箱
        /// </summary>
        /// <param name="inputData"></param>
        /// <returns></returns>
        public static bool IsEmail(string inputData)
        {
            Regex RegEmail = new Regex("^[\\w-]+@[\\w-]+\\.(com|net|org|edu|mil|tv|biz|info)$");
            //w 英文字母或数字的字符串，和 [a-zA-Z0-9] 语法一样
            Match m = RegEmail.Match(inputData);
            return m.Success;
        }

        /// <summary>
        /// Ping ！！
        /// </summary>
        /// <param name="host"></param>
        /// <returns></returns>
        public static bool PingHost(string host)
        {
            try
            {
                using Ping ping = new();
                PingReply reply = ping.Send(host);
                return reply.Status == IPStatus.Success;
            }
            catch (PingException)
            {
                // 捕获 Ping 失败的异常
                return false;
            }
            catch (Exception ex)
            {
                // 处理其他可能的异常
                Console.WriteLine($"An error occurred: {ex.Message}");
                return false;
            }
        }
    }
}