﻿using DanDing1.Models;
using DanDing1.Views.Windows;
using ScriptEngine.Model;
using System.Collections.ObjectModel;
using System.Runtime.InteropServices;
using ScriptEngine.MuMu;
using XHelper;
using System.Windows;
using System.Diagnostics;

namespace DanDing1.Helpers
{
    internal class Utils
    {
        public static string UserGameName1 => XConfig.LoadValueFromFile<string>("游戏1", "GameName") ?? "游戏1";
        public static string UserGameName2 => XConfig.LoadValueFromFile<string>("游戏2", "GameName") ?? "游戏2";
        public static string UserGameName3 => XConfig.LoadValueFromFile<string>("游戏3", "GameName") ?? "游戏3";
        public static string UserGameName4 => XConfig.LoadValueFromFile<string>("游戏4", "GameName") ?? "游戏4";

        private static AppConfig _config => GlobalData.Instance.appConfig;

        /// <summary>
        /// 显示消息
        /// </summary>
        /// <param name="title"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static void ShowMessage(string title, string message)
        {
            var uiMessageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = title,
                Content = message,
            };
            uiMessageBox.ShowDialogAsync().Wait();
        }

        /// <summary>
        /// 显示消息
        /// </summary>
        /// <param name="title"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static void ShowMessage(string message)
        {
            var uiMessageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = "蛋定助手 - 提示",
                Content = message,
            };
            uiMessageBox.ShowDialogAsync().Wait();
        }

        /// <summary>
        /// 进程是否在运行
        /// </summary>
        /// <param name="processName"></param>
        /// <returns></returns>
        public static bool IsProcessRunning(string processName)
        {
            foreach (Process process in Process.GetProcesses())
            {
                if (process.ProcessName.Equals(processName, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }
            return false;
        }

        // 导入 SetWindowDisplayAffinity API
        [DllImport("user32.dll", SetLastError = true)]
        public static extern bool SetWindowDisplayAffinity(IntPtr hwnd, uint dwAffinity);

        // 定义亲和性常量
        private const uint WDA_NONE = 0x00000000;

        private const uint WDA_MONITOR = 0x00000001;

        /// <summary>
        /// 隐藏窗口
        /// </summary>
        /// <param name="hwnd"></param>
        public static void ApplyDisplayAffinity(IntPtr hwnd)
        {
            SetWindowDisplayAffinity(hwnd, WDA_MONITOR);
        }

        /// <summary>
        /// 取消隐藏窗口
        /// </summary>
        /// <param name="hwnd"></param>
        public static void RemoveDisplayAffinity(IntPtr hwnd)
        {
            SetWindowDisplayAffinity(hwnd, WDA_NONE);
        }

        /// <summary>
        /// 检查最新版本 并弹出更新
        /// </summary>
        public static async Task<bool> CheckNewVer()
        {
            //检查版本号是否需要更新
            if (!GlobalData.Instance.UserConfig.BetaTesterToggle && _config.Info.Ver != "" && _config.Info.IsNeedUpData)
            {
                //检查是否应该显示更新窗口
                if (UpdateWindow.ShouldShowUpdateWindow(_config.Info.Ver))
                {
                    //弹出更新界面
                    var updateWindow = new UpdateWindow();
                    updateWindow.ShowDialog(); // 使用 ShowDialog 使窗口成为模式窗口
                    return true;
                }
            }
            //检查Beta版本是否需要更新
            if (GlobalData.Instance.UserConfig.BetaTesterToggle)
            {
                var vs = await _config.dNet.System.GetInfoBetaAsync();
                if (vs[0] != "" && vs[0] != _config.Info.Now_Ver)
                {
                    //检查是否应该显示更新窗口
                    if (UpdateWindow.ShouldShowUpdateWindow(vs[0]))
                    {
                        //弹出更新界面
                        var updateWindow = new UpdateWindow(_config.Info.Now_Ver, vs[0], vs[2], vs[1]);
                        updateWindow.ShowDialog(); // 使用 ShowDialog 使窗口成为模式窗口
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 将文本写到桌面的txt文件中，没有txt文件就新建
        /// </summary>
        /// <param name="text"></param>
        public static void WriteToDesktopTxt(string text)
        {
            string path = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + "\\DanDing-UserPwd.txt";
            System.IO.File.WriteAllText(path, text + "\r\n");
        }

        /// <summary>
        /// 将文本置剪贴板
        /// </summary>
        /// <param name="text"></param>
        /// <param name="errorStr"></param>
        /// <returns></returns>
        public static bool SetClipboardText(string text, out string errorStr)
        {
            errorStr = "";
            int maxRetries = 3;
            int retries = 0;
            try
            {
                Clipboard.Clear();
                Clipboard.SetText(text);
            }
            catch (COMException ex)
            {
                if ((uint)ex.ErrorCode == 0x800401D0) // CLIPBRD_E_CANT_OPEN
                {
                    retries++;
                    if (retries > maxRetries)
                    {
                        errorStr = $"剪贴板繁忙！无法将内容置于您的剪贴板中，请自行截图复制...或多试几次！\r\n{text}";
                        return false;
                    }
                    Thread.Sleep(100); // 等待100毫秒后重试
                }
                else
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 检验数据
        /// </summary>
        /// <param name="number"></param>
        /// <param name="min"></param>
        /// <param name="max"></param>
        /// <returns></returns>
        public static bool CheckNumber(int number, int min, int max)
        {
            if (number < min || number > max) return false;
            return true;
        }

        /// <summary>
        /// 检验数据
        /// </summary>
        /// <param name="number"></param>
        /// <param name="min"></param>
        /// <param name="max"></param>
        /// <returns></returns>
        public static bool CheckNumber(int number, Range range)
        {
            if (number < range.Start.Value || number > range.End.Value) return false;
            return true;
        }

        /// <summary>
        /// 获取任务类
        /// </summary>
        /// <param name="configs"></param>
        /// <returns></returns>
        public static TaskConfigsModel GetTaskConfigs(ObservableCollection<TaskConfigsModel.Configs> configs)
        {
            TaskConfigsModel TaskConfigs = new();
            foreach (var config in configs)
                TaskConfigs.AddConfig(config.Name, config);
            return TaskConfigs;
        }

        public static void GetAppKeyAndIV(out string appKey, out string appIV)
        {
            appKey = "blog.x-tools.top";
            appIV = ">yys.x-tools.top";
            Debug.WriteLine("应用Key以及IV被获取！");
        }

        /// <summary>
        /// 打开文件夹，如果文件夹不存在则自动创建
        /// </summary>
        /// <param name="baseDirectory">要打开的文件夹路径</param>
        /// <exception cref="NotImplementedException">当前操作系统不支持时抛出异常</exception>
        internal static void OpenFolder(string baseDirectory)
        {
            // 检查目录是否存在，不存在则创建
            if (!Directory.Exists(baseDirectory))
            {
                Directory.CreateDirectory(baseDirectory);
            }

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                Process.Start("explorer.exe", baseDirectory);
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                Process.Start("open", baseDirectory);
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                Process.Start("xdg-open", baseDirectory);
            }
            else
            {
                throw new NotImplementedException();
            }
        }

        public static void CreateShortcut(string shortcutName, string targetPath, string description = null, string iconLocation = null)
        {
            try
            {
                // 获取桌面路径
                string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);

                // 快捷方式完整路径
                string shortcutPath = Path.Combine(desktopPath, $"{shortcutName}.lnk");

                // 获取目标程序所在目录作为工作目录
                string workingDirectory = Path.GetDirectoryName(targetPath);

                // 使用命令行创建快捷方式
                string command = $@"
                $WshShell = New-Object -ComObject WScript.Shell
                $Shortcut = $WshShell.CreateShortcut('{shortcutPath.Replace("'", "''")}')
                $Shortcut.TargetPath = '{targetPath.Replace("'", "''")}'
                $Shortcut.WorkingDirectory = '{workingDirectory.Replace("'", "''")}'";

                // 添加描述（如果有）
                if (!string.IsNullOrEmpty(description))
                {
                    command += $@"
                    $Shortcut.Description = '{description.Replace("'", "''")}'";
                }

                // 添加图标（如果有）
                if (!string.IsNullOrEmpty(iconLocation))
                {
                    command += $@"
                    $Shortcut.IconLocation = '{iconLocation.Replace("'", "''")}'";
                }

                // 保存快捷方式
                command += @"
                $Shortcut.Save()";

                // 创建并启动PowerShell进程
                using (Process process = new Process())
                {
                    process.StartInfo.FileName = "powershell.exe";
                    process.StartInfo.Arguments = $"-Command {command}";
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.CreateNoWindow = true;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.RedirectStandardError = true;

                    process.Start();
                    process.WaitForExit();

                    // 检查是否有错误
                    string error = process.StandardError.ReadToEnd();
                    if (!string.IsNullOrEmpty(error))
                    {
                        XLogger.Error($"创建快捷方式时出错: {error}");
                    }
                }

                XLogger.Info($"已在桌面创建快捷方式: {shortcutName}");
            }
            catch (Exception ex)
            {
                XLogger.Error($"创建快捷方式异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 清空临时文件夹
        /// </summary>
        internal static void ClearTempFolder()
        {
            //临时文件夹 .\\Temp
            string tempFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Temp");
            if (Directory.Exists(tempFolder))
                Directory.Delete(tempFolder, true);
        }

        /// <summary>
        /// 获取临时文件夹路径
        /// </summary>
        internal static string GetTempFolder => Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Temp");

        /// <summary>
        /// 过滤 获取 匹配句柄 的 MuMuAdb 端口
        /// </summary>
        /// <returns></returns>
        public static int FilterMuMuAdbPort(int RenderWnd, MuMuInstances? muMuInstances)
        {
            if (muMuInstances == null || muMuInstances.Instances == null || muMuInstances.Instances.Count == 0)
            {
                XLogger.Warn("未找到有效的MuMu模拟器实例！无法实现卡屏重开游戏的功能！请检查模拟器是否正常启动。");
                return -1;
            }

            foreach (var instance in muMuInstances.Instances)
            {
                //RenderWnd转16进制字符串 保证字符长度为8位，不足8位的前面补0 在转成大写
                string renderWndHex = Convert.ToString(RenderWnd, 16).PadLeft(8, '0').ToUpper();
                if (instance.Value.RenderWnd == renderWndHex)
                    return instance.Value.AdbPort ?? -1; // 使用可空类型的安全访问
            }

            XLogger.Warn("未找到匹配的MuMuAdb端口！无法实现卡屏重开游戏的功能！请注意设置中的模拟器配置~");
            return -1;
        }

        /// <summary>
        /// 过滤 获取 匹配句柄 的 MuMuIndex 索引
        /// </summary>
        /// <returns></returns>
        public static int FilterMuMuIndex(int RenderWnd, MuMuInstances? muMuInstances)
        {
            if (muMuInstances == null || muMuInstances.Instances == null || muMuInstances.Instances.Count == 0)
            {
                XLogger.Warn("未找到有效的MuMu模拟器实例！无法实现卡屏重开游戏的功能！请检查模拟器是否正常启动。");
                return -1;
            }

            foreach (var instance in muMuInstances.Instances)
            {
                //RenderWnd转16进制字符串 保证字符长度为8位，不足8位的前面补0 在转成大写
                string renderWndHex = Convert.ToString(RenderWnd, 16).PadLeft(8, '0').ToUpper();
                if (instance.Value.RenderWnd == renderWndHex)
                    return int.Parse(instance.Value.Index); // 使用可空类型的安全访问
            }

            XLogger.Warn("未找到匹配的MuMuIndex索引！无法实现离线值守的功能！请注意设置中的模拟器配置~");
            return -1;
        }

        /// <summary>
        /// 备份AppConfig配置文件到AppData路径
        /// </summary>
        public static void BackupAppConfig()
        {
            // 获取应用数据根目录
            var roamingPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);

            // 获取源路径（程序目录下的./runtimes/AppConfig/）
            string sourceDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "runtimes", "AppConfig");

            // 构建目标路径（roamingPath路径的DanDing1文件夹下的AppConfig）
            string targetDir = Path.Combine(roamingPath, "DanDing1", "AppConfig");

            // 确保目标目录存在
            if (!Directory.Exists(targetDir))
            {
                Directory.CreateDirectory(targetDir);
            }

            // 如果源目录不存在，记录警告并返回
            if (!Directory.Exists(sourceDir))
            {
                XLogger.Warn("备份AppConfig失败：源目录不存在 - " + sourceDir);
                return;
            }

            try
            {
                // 复制源目录中的所有文件到目标目录，覆盖已存在的文件
                foreach (string sourceFile in Directory.GetFiles(sourceDir, "*", SearchOption.AllDirectories))
                {
                    // 获取相对路径
                    string relativePath = sourceFile.Substring(sourceDir.Length + 1);
                    string targetFile = Path.Combine(targetDir, relativePath);

                    // 确保目标子目录存在
                    string targetSubDir = Path.GetDirectoryName(targetFile);
                    if (!Directory.Exists(targetSubDir))
                    {
                        Directory.CreateDirectory(targetSubDir);
                    }

                    // 复制文件，覆盖已存在的文件
                    System.IO.File.Copy(sourceFile, targetFile, true);
                }

                XLogger.Info("AppConfig配置文件已成功备份到: " + targetDir);
            }
            catch (Exception ex)
            {
                XLogger.Error("备份AppConfig时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 清理空目录
        /// </summary>
        /// <param name="directory">要清理的目录</param>
        private static void CleanEmptyDirectories(string directory)
        {
            foreach (var dir in Directory.GetDirectories(directory))
            {
                CleanEmptyDirectories(dir);

                // 如果目录为空，则删除
                if (!Directory.EnumerateFileSystemEntries(dir).Any())
                {
                    try
                    {
                        Directory.Delete(dir, false);
                    }
                    catch (Exception ex)
                    {
                        XLogger.Warn($"删除空目录失败: {dir}, 错误: {ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 检查是否需要提示用户导入缓存的配置文件
        /// </summary>
        /// <returns>如果本地配置为空但缓存中有配置，则返回true；否则返回false</returns>
        public static bool ShouldPromptImportConfig()
        {
            // 获取应用数据根目录
            var roamingPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);

            // 获取本地配置路径（程序目录下的./runtimes/AppConfig/）
            string localConfigDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "runtimes", "AppConfig");

            // 获取缓存配置路径
            string cachedConfigDir = Path.Combine(roamingPath, "DanDing1", "AppConfig");

            // 检查本地配置目录是否不存在或为空
            bool isLocalConfigEmpty = !Directory.Exists(localConfigDir) ||
                                     !Directory.GetFiles(localConfigDir, "*", SearchOption.AllDirectories).Any();

            // 检查缓存配置目录是否存在且不为空
            bool hasCachedConfig = Directory.Exists(cachedConfigDir) &&
                                  Directory.GetFiles(cachedConfigDir, "*", SearchOption.AllDirectories).Any();

            // 如果本地配置为空但缓存中有配置，则返回true
            return isLocalConfigEmpty && hasCachedConfig;
        }

        /// <summary>
        /// 从AppData缓存导入AppConfig配置文件
        /// </summary>
        /// <returns>导入是否成功</returns>
        public static bool ImportCachedAppConfig()
        {
            // 获取应用数据根目录
            var roamingPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);

            // 获取缓存配置路径（源路径）
            string sourceDir = Path.Combine(roamingPath, "DanDing1", "AppConfig");

            // 获取本地配置路径（目标路径）
            string targetDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "runtimes", "AppConfig");

            // 如果缓存目录不存在或为空，记录警告并返回
            if (!Directory.Exists(sourceDir) || !Directory.GetFiles(sourceDir, "*", SearchOption.AllDirectories).Any())
            {
                XLogger.Warn("导入AppConfig失败：缓存目录不存在或为空 - " + sourceDir);
                return false;
            }

            try
            {
                // 复制源目录中的所有文件到目标目录，覆盖已存在的文件
                foreach (string sourceFile in Directory.GetFiles(sourceDir, "*", SearchOption.AllDirectories))
                {
                    // 获取相对路径
                    string relativePath = sourceFile.Substring(sourceDir.Length + 1);
                    string targetFile = Path.Combine(targetDir, relativePath);

                    // 确保目标子目录存在
                    string targetSubDir = Path.GetDirectoryName(targetFile);
                    if (!Directory.Exists(targetSubDir))
                    {
                        Directory.CreateDirectory(targetSubDir);
                    }

                    // 复制文件，覆盖已存在的文件
                    System.IO.File.Copy(sourceFile, targetFile, true);
                }

                XLogger.Info("AppConfig配置文件已成功备份到: " + targetDir);
                return true;
            }
            catch (Exception ex)
            {
                XLogger.Error("从缓存导入AppConfig时发生错误: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 检查是否需要导入缓存的配置文件并显示提示
        /// </summary>
        /// <returns>如果导入了配置返回true，否则返回false</returns>
        public static bool PromptImportConfigIfNeeded()
        {
            // 检查是否需要提示用户导入缓存的配置
            if (ShouldPromptImportConfig())
            {
                // 获取应用数据根目录
                var roamingPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                string cachedConfigDir = Path.Combine(roamingPath, "DanDing1", "AppConfig");

                // 弹出提示窗口询问用户是否导入缓存的配置
                MessageBoxResult result = MessageBox.Show(
                    $"发现您在 {cachedConfigDir} 有缓存的配置文件，是否导入这些配置？\n\n" +
                    "是 - 导入缓存的配置\n否 - 使用默认配置",
                    "发现缓存的配置",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                // 如果用户选择是，则导入配置
                if (result == MessageBoxResult.Yes)
                {
                    bool importSuccess = ImportCachedAppConfig();
                    if (importSuccess)
                    {
                        MessageBoxResult restartResult = MessageBox.Show(
                            "配置文件导入成功！应用需要重启才能应用您之前的配置。点击确定立即重启应用。",
                            "导入成功 - 需要重启",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);

                        if (restartResult == MessageBoxResult.OK)
                        {
                            // 重启应用程序
                            string appPath = System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName;
                            System.Diagnostics.Process.Start(appPath);
                            Application.Current.Shutdown();
                        }
                        return true;
                    }
                    else
                    {
                        MessageBox.Show(
                            "配置文件导入失败，应用将使用默认配置。请查看日志了解详细信息。",
                            "导入失败",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                }
            }

            return false;
        }
    }
}