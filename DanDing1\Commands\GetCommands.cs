using XHelper;
using System.Management;
using System.Text;
using System.Diagnostics;

namespace DanDing1.Commands
{
    /// <summary>
    /// Get命令系列处理类，用于获取系统各种信息
    /// </summary>
    internal class GetCommands : BaseCommand
    {
        public override void Execute(string[] parameters)
        {
            if (!ValidateParameterCount(parameters, 2))
                return;

            // 获取子命令（如果有）
            string subCommand = parameters.Length > 1 ? parameters[1].ToLower() : "";

            switch (subCommand)
            {
                case "systeminfo":
                    HandleSystemInfoCommand();
                    break;
                case "help":
                case "?":
                    PrintHelp();
                    break;
                default:
                    XLogger.Error($"未知的Get命令: {subCommand}");
                    PrintHelp();
                    break;
            }
        }

        /// <summary>
        /// 处理systeminfo命令，获取并输出系统信息
        /// </summary>
        private void HandleSystemInfoCommand()
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine("======== 系统信息 ========");

                // 获取操作系统信息
                sb.AppendLine($"操作系统版本: {GetOSInfo()}");

                // 获取CPU信息
                sb.AppendLine($"CPU型号: {GetCPUModel()}");

                // 获取显卡信息
                sb.AppendLine($"显卡信息: {GetGPUName()}");

                // 获取内存信息
                sb.AppendLine($"物理内存: {GetPhysicalMemory()} GB");

                // 获取磁盘信息
                sb.AppendLine($"系统盘剩余空间: {GetSystemDriveSpace()} GB");

                // 获取计算机名和用户名
                sb.AppendLine($"计算机名: {Environment.MachineName}");
                sb.AppendLine($"用户名: {Environment.UserName}");

                // 获取.NET版本
                sb.AppendLine($".NET版本: {Environment.Version}");

                // 获取屏幕分辨率
                sb.AppendLine($"屏幕分辨率: {GetScreenResolution()}");

                // 获取网络信息
                sb.AppendLine($"IP地址: {GetIPAddress()}");

                // 获取并添加设备码信息
                sb.AppendLine($"设备码: {GetDeviceCode()}");

                sb.AppendLine("==========================");

                XLogger.Info(sb.ToString());
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取系统信息时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取设备码
        /// </summary>
        /// <returns>设备码字符串</returns>
        private string GetDeviceCode()
        {
            try
            {
                // 使用XHelper.MachineIdentifier类获取设备码
                return MachineIdentifier.GetMachineCode();
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取设备码失败: {ex.Message}");
                return "获取失败";
            }
        }

        /// <summary>
        /// 打印帮助信息
        /// </summary>
        private void PrintHelp()
        {
            XLogger.Info(@"Get命令用法:
get systeminfo  - 获取系统信息
get help        - 显示帮助信息");
        }

        /// <summary>
        /// 获取操作系统版本信息
        /// </summary>
        private string GetOSInfo()
        {
            try
            {
                FileVersionInfo versionInfo = FileVersionInfo.GetVersionInfo(
                    Path.Combine(Environment.SystemDirectory, "kernel32.dll"));
                string osVersion = versionInfo?.FileVersion ?? "未知";

                // 获取更详细的系统信息
                ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem");
                foreach (ManagementObject obj in searcher.Get())
                {
                    string caption = obj["Caption"]?.ToString() ?? "";
                    string servicePackMajor = obj["ServicePackMajorVersion"]?.ToString() ?? "";

                    if (!string.IsNullOrEmpty(servicePackMajor) && servicePackMajor != "0")
                    {
                        caption += $" Service Pack {servicePackMajor}";
                    }

                    return $"{caption} (内核版本: {osVersion})";
                }

                return $"Windows (内核版本: {osVersion})";
            }
            catch
            {
                return "获取操作系统信息失败";
            }
        }

        /// <summary>
        /// 获取CPU型号信息
        /// </summary>
        private string GetCPUModel()
        {
            try
            {
                string cpuModel = "";
                ManagementClass mc = new("Win32_Processor");
                ManagementObjectCollection moc = mc.GetInstances();
                foreach (ManagementObject mo in moc.Cast<ManagementObject>())
                {
                    cpuModel = mo["Name"]?.ToString() ?? "型号未知";
                    break;
                }
                return cpuModel;
            }
            catch
            {
                return "获取CPU信息失败";
            }
        }

        /// <summary>
        /// 获取显卡信息
        /// </summary>
        private string GetGPUName()
        {
            try
            {
                ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController");
                StringBuilder gpuList = new StringBuilder();

                foreach (ManagementObject obj in searcher.Get())
                {
                    if (gpuList.Length > 0)
                        gpuList.Append(", ");

                    string gpuName = obj["Name"]?.ToString() ?? "未知显卡";
                    // 排除虚拟显示适配器
                    if (!gpuName.Contains("Virtual") && !gpuName.Contains("OrayIddDriver"))
                        gpuList.Append(gpuName);
                }

                return gpuList.Length > 0 ? gpuList.ToString() : "未找到GPU信息";
            }
            catch (Exception ex)
            {
                return $"获取GPU信息出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取物理内存大小（GB）
        /// </summary>
        private string GetPhysicalMemory()
        {
            try
            {
                ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem");
                foreach (ManagementObject obj in searcher.Get())
                {
                    if (obj["TotalPhysicalMemory"] != null)
                    {
                        ulong totalMemory = Convert.ToUInt64(obj["TotalPhysicalMemory"]);
                        double memoryInGB = totalMemory / (1024.0 * 1024.0 * 1024.0);
                        return Math.Round(memoryInGB, 2).ToString();
                    }
                }
                return "未知";
            }
            catch
            {
                return "获取失败";
            }
        }

        /// <summary>
        /// 获取系统盘剩余空间（GB）
        /// </summary>
        private string GetSystemDriveSpace()
        {
            try
            {
                DriveInfo systemDrive = new DriveInfo(Path.GetPathRoot(Environment.SystemDirectory));
                double freeSpaceGB = systemDrive.AvailableFreeSpace / (1024.0 * 1024.0 * 1024.0);
                return Math.Round(freeSpaceGB, 2).ToString();
            }
            catch
            {
                return "获取失败";
            }
        }

        /// <summary>
        /// 获取屏幕分辨率
        /// </summary>
        private string GetScreenResolution()
        {
            try
            {
                ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController");
                foreach (ManagementObject obj in searcher.Get())
                {
                    int currentHorizontalResolution = Convert.ToInt32(obj["CurrentHorizontalResolution"] ?? 0);
                    int currentVerticalResolution = Convert.ToInt32(obj["CurrentVerticalResolution"] ?? 0);

                    if (currentHorizontalResolution > 0 && currentVerticalResolution > 0)
                    {
                        return $"{currentHorizontalResolution} x {currentVerticalResolution}";
                    }
                }
                return "未知";
            }
            catch
            {
                return "获取失败";
            }
        }

        /// <summary>
        /// 获取IP地址
        /// </summary>
        private string GetIPAddress()
        {
            try
            {
                StringBuilder ipAddresses = new StringBuilder();

                // 获取所有网络适配器的IP地址
                foreach (var networkInterface in System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces())
                {
                    // 仅获取已启用的网络适配器
                    if (networkInterface.OperationalStatus == System.Net.NetworkInformation.OperationalStatus.Up)
                    {
                        foreach (var ip in networkInterface.GetIPProperties().UnicastAddresses)
                        {
                            // 仅获取IPv4地址，排除回环地址
                            if (ip.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork
                                && !ip.Address.ToString().StartsWith("169.254."))
                            {
                                if (ipAddresses.Length > 0)
                                    ipAddresses.Append(", ");

                                ipAddresses.Append(ip.Address.ToString());
                            }
                        }
                    }
                }

                return ipAddresses.Length > 0 ? ipAddresses.ToString() : "未获取到IP地址";
            }
            catch
            {
                return "获取失败";
            }
        }
    }
}