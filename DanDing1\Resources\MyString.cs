﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DanDing1.Resources
{
    public static class MyString
    {
        public static readonly string GameTip = "1. 请务必设置模拟器、游戏为指定配置或设置！\r\n" +
            "2. 出现闪退请排查：网络不良、软件权限、杀软拦截；\r\n" +
            "3. 博客、文档上没有的问题请在群中反馈！";

        public static readonly string Fast_MiWenTip = "1. 请务必设置模拟器、游戏为指定配置或设置！\r\n" +
            "2. 将游戏场景调整到每周秘闻挑战界面后；\r\n" +
            "3. 点击右下角\"小飞机\"按钮即可开始；\r\n" +
            "4. 快速任务=半自动任务，请人工值守，谢谢！\r\n";

        public static readonly string Fast_CeZhiTip = "1. 请务必设置模拟器、游戏为指定配置或设置！\r\n" +
            "2. 游戏当前场景必须为召唤界面开始；\r\n" +
            "3. 请注意召唤主题需要使用默认主题；\r\n" +
            "4. 快速任务=半自动任务，请人工值守，谢谢！\r\n";

        public static readonly string Fast_BanZdTip = "请务必设置模拟器、游戏为指定配置或设置！\r\n" +
            "> 半自动模式可以干：\r\n" +
            "1. 自动点击胜利、达摩、准备等图标\r\n" +
            "2. 御魂、觉醒等通用任务\r\n" +
            "3. 半自动道馆（未测试）\r\n" +
            "4. 组队御魂";

        public static readonly string Record_Tip = "您开启了录制本次任务！\r\n" +
            "录制后的文件会保存在\\runtimes\\Debug目录下，后缀为.sr\r\n" +
            "录制文件的大小取决于录制时长。\r\n" +
            "原生画质录制文件大小：1分钟左右的录制文件大小约为100MB左右。适合作者采集、作图。\r\n" +
            "压缩画质录制文件大小：1分钟左右的录制文件大小约为2-3MB左右。适合长时间录制流程。\r\n" +
            "任务结束后会自动打开录制后的文件夹！\r\n\r\n" +
            "请尽量减少任务运行时长，保证录制内容准确、精简的展示错误内容！";
    }
}