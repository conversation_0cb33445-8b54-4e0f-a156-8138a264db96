﻿<ui:FluentWindow
    x:Class="DanDing1.Views.Windows.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:DanDing1.Views.Windows"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:localcs="clr-namespace:DanDing1.ViewModels.Pages"
    Title="{Binding ViewModel.ApplicationTitle, Mode=OneWay}"
    Width="850"
    Height="642"
    MinWidth="850"
    MinHeight="642"
    MaxWidth="850"
    MaxHeight="642"
    d:DataContext="{d:DesignInstance local:MainWindow,
    IsDesignTimeCreatable=True}"
    ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
    ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    Closing="FluentWindow_Closing"
    ExtendsContentIntoTitleBar="True"
    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    ResizeMode="CanMinimize"
    WindowBackdropType="Mica"
    WindowCornerPreference="Round"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">
    <Window.Resources>
        <localcs:ViewVisibilityConverter x:Key="ViewVisibilityConverter"/>
        <localcs:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <ui:TitleBar
            x:Name="TitleBar"
            Title="{Binding ViewModel.ApplicationTitle}"
            Grid.Row="0"
            ShowMaximize="False"
            CloseWindowByDoubleClickOnIcon="True">
            <ui:TitleBar.Icon>
                <ui:ImageIcon Source="pack://application:,,,/Assets/egg-icon-256.png"/>
            </ui:TitleBar.Icon>
        </ui:TitleBar>

        <ui:NavigationView
            x:Name="RootNavigation"
            Padding="42,0,42,0"
            BreadcrumbBar="{Binding ElementName=BreadcrumbBar}"
            FooterMenuItemsSource="{Binding ViewModel.FooterMenuItems, Mode=OneWay}"
            FrameMargin="0"
            IsBackButtonVisible="Visible"
            IsPaneToggleVisible="True"
            MenuItemsSource="{Binding ViewModel.MenuItems, Mode=OneWay}"
            OpenPaneLength="62"
            PaneDisplayMode="LeftFluent"
            SelectionChanged="RootNavigation_SelectionChanged"
            TitleBar="{Binding ElementName=TitleBar, Mode=OneWay}">
            <ui:NavigationView.Header>
                <ui:BreadcrumbBar x:Name="BreadcrumbBar"
                                  Margin="42,10,42,20"/>
            </ui:NavigationView.Header>
            <ui:NavigationView.AutoSuggestBox>
                <ui:AutoSuggestBox
                    x:Name="AutoSuggestBox"
                    Margin="0,15,0,0"
                    PlaceholderText="Search"
                    Visibility="Hidden">
                    <ui:AutoSuggestBox.Icon>
                        <ui:IconSourceElement>
                            <ui:SymbolIconSource Symbol="Search24"/>
                        </ui:IconSourceElement>
                    </ui:AutoSuggestBox.Icon>
                </ui:AutoSuggestBox>
            </ui:NavigationView.AutoSuggestBox>
            <ui:NavigationView.ContentOverlay>
                <Grid>
                    <ui:SnackbarPresenter x:Name="SnackbarPresenter"/>
                </Grid>
            </ui:NavigationView.ContentOverlay>
        </ui:NavigationView>
        <StackPanel
            Margin="5,0,5,125"
            VerticalAlignment="Bottom">
            <ui:HyperlinkButton
                x:Name="AddGamesPanel"
                Width="60"
                Height="50"
                Command="{Binding ViewModel.AddGamesCommand}">
                <StackPanel>
                    <TextBlock Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                               Text="多开"/>
                    <TextBlock Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                               Text="加一"/>
                </StackPanel>
            </ui:HyperlinkButton>
            <ui:HyperlinkButton
                x:Name="SuperPanel"
                Width="60"
                Height="50"
                Command="{Binding ViewModel.OpenSuperMultiGamesWindowCommand}">
                <Grid>
                    <StackPanel Panel.ZIndex="10">
                        <TextBlock FontWeight="Bold"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   Text="超级"/>
                        <TextBlock FontWeight="Bold"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   Text="多开"/>
                    </StackPanel>
                    <Grid
                        Visibility="{Binding ViewModel.IsRunning, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=True}"
                        Panel.ZIndex="0">
                        <ui:ProgressRing
                            Width="28"
                            Height="28"
                            IsIndeterminate="True"/>
                    </Grid>
                    <Grid
                        Visibility="{Binding ViewModel.IsRunning, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=True}"
                        Panel.ZIndex="11">
                        <ui:TextBlock VerticalAlignment="Center"
                                      HorizontalAlignment="Center"
                                      FontWeight="Black"
                                      FontSize="18"
                                      Text="{Binding ViewModel.RunningCount}"/>
                    </Grid>
                </Grid>
            </ui:HyperlinkButton>
            <ui:HyperlinkButton
                Width="60"
                Height="50"
                Command="{Binding ViewModel.OpenSchedulerWindowCommand}">
                <Grid>
                    <StackPanel Panel.ZIndex="10">
                        <TextBlock FontWeight="Bold"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   Text="定时"/>
                        <TextBlock FontWeight="Bold"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   Text="调度"/>
                    </StackPanel>
                    <Grid
                        Visibility="{Binding ViewModel.TrayIconService.SchedulerViewModel.IsSchedulerRunning, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Panel.ZIndex="0">
                        <ui:ProgressRing
                            Width="28"
                            Height="28"
                            IsIndeterminate="True"/>
                    </Grid>
                </Grid>
            </ui:HyperlinkButton>
        </StackPanel>

        <ContentPresenter x:Name="RootContentDialog"
                          Grid.Row="0"/>
    </Grid>
</ui:FluentWindow>