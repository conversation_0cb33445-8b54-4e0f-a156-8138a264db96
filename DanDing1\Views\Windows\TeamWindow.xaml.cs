﻿using DanDing1.Models;
using ShareX.HelpersLib;
using ShareX.ScreenCaptureLib;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using XHelper;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// TeamWindow.xaml 的交互逻辑
    /// </summary>
    public partial class TeamWindow : Window
    {
        private byte[] Now_Bytes;
        private string Now_Key = "";
        private List<int> Start_X = new List<int>() { 442, 734, 444, 734, 444, 735, 442, 737 };
        private List<int> Start_Y = new List<int>() { 198, 196, 291, 292, 388, 387, 485, 484 };
        private TeamKeysModel teamKeys;

        public WindowInfo? SelectedWindow { get; private set; }

        [DllImport("user32.dll")]
        private static extern IntPtr WindowFromPoint(int x, int y);

        public TeamWindow(int Hwnd, Func<string, int> oKCallBack)
        {
            InitializeComponent();
            teamKeys = new();
            if (Hwnd == 0)
            {
                Screenshot_HWND.Text = "点击绑定窗口";
                Screenshot_HWND.MouseDown += (sender, e) =>
                {
                    SelectedWindow = null;
                    RegionCaptureOptions Options = new();
                    Options.DetectControls = false;
                    SimpleWindowInfo simpleWindowInfo = RegionCaptureTasks.GetWindowInfo(Options, out int x, out int y);
                    if (simpleWindowInfo != null)
                    {
                        IntPtr windowHandle = WindowFromPoint(x, y);
                        SelectedWindow = new WindowInfo(windowHandle);
                        Hwnd = (int)SelectedWindow.Handle;
                        this.Hwnd = Hwnd;
                        Screenshot_HWND.Text = Hwnd.ToString();
                    }
                };
            }
            else
                Screenshot_HWND.Text = Hwnd.ToString();

            Screenshot_ID.ItemsSource = new List<string>() { "1", "2", "3", "4", "5", "6", "7", "8" };
            Screenshot_ID.SelectedIndex = 0;

            TeamName.ItemsSource = new List<string>() { "好友", "最近", "寮友", "跨区" };
            TeamName.SelectedIndex = 0;

            this.Hwnd = Hwnd;
            OKCallBack = oKCallBack;
            ListBox_TeamName.SelectionChanged += ListBox_TeamName_SelectionChanged;
            foreach (var item in teamKeys.TeamKeys)
                ListBox_TeamName.Items.Add(item.Key);
        }

        public int Hwnd { get; private set; }

        public Func<string, int> OKCallBack { get; }

        public ImageSource ByteArrayToImageSource(byte[] imageData)
        {
            if (imageData == null || imageData.Length == 0)
                return null;

            BitmapImage image = new BitmapImage();
            using (MemoryStream ms = new MemoryStream(imageData))
            {
                image.BeginInit();
                image.CacheOption = BitmapCacheOption.OnLoad;
                image.StreamSource = ms;
                image.EndInit();
                image.Freeze(); // 防止跨线程操作异常
            }
            return image;
        }

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool GetWindowRect(IntPtr hWnd, ref RECT lpRect);

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            if (Hwnd == 0)
            {
                MessageBox.Show("你还没有选择一个窗口哦！请先绑定窗口！");
                return;
            }
            RECT Game_rc = new RECT();
            GetWindowRect(Hwnd, ref Game_rc);
            int Game_x = Game_rc.Left;
            int Game_y = Game_rc.Top;
            int id = Screenshot_ID.SelectedIndex;

            //Screen screen = Screen.AllScreens.FirstOrDefault();//获取当前第一块屏幕(根据需求也可以换其他屏幕)
            //创建需要截取的屏幕区域
            System.Drawing.Rectangle rc = new System.Drawing.Rectangle(Game_x + Start_X[id], Game_y + Start_Y[id], 176, 28);
            //生成截图的位图容器
            Bitmap bitmap = new Bitmap(176, 28, System.Drawing.Imaging.PixelFormat.Format24bppRgb);
            //GDI+图像画布
            using (Graphics memoryGrahics = Graphics.FromImage(bitmap))
            {
                memoryGrahics.CopyFromScreen(rc.X, rc.Y, 0, 0, rc.Size, CopyPixelOperation.SourceCopy);//对屏幕指定区域进行图像复制
            }
            //载入  bitmap 到 Image 控件
            Screenshot.Source = System.Windows.Interop.Imaging.CreateBitmapSourceFromHBitmap(bitmap.GetHbitmap(), IntPtr.Zero, Int32Rect.Empty, BitmapSizeOptions.FromEmptyOptions());
            Now_Bytes = bitmap.ToByteArray();
            string ocrname = XOcr.Local_Ocr_String(Now_Bytes);
            Now_Key = ocrname.Replace("\r\n", null);
            if (Now_Key == "")
                Ocr_Name.Text = " Ocr异常，请确认是否截取到了ID";
            else
                Ocr_Name.Text = " Key：" + Now_Key + " (单击修改)";
            Status_Tip.Text = "未保存";
        }

        /// <summary>
        /// 保存daoliebiao
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Button_Click_1(object sender, RoutedEventArgs e)
        {
            //查重
            if (ListBox_TeamName.Items.Contains(Now_Key))
            {
                Status_Tip.Text = "列表已存在";
                return;
            }
            if (Ocr_Name.Text == " Ocr异常，请确认是否截取到了ID")
            {
                Status_Tip.Text = "无法保存";
                return;
            }
            ListBox_TeamName.Items.Add(Now_Key);
            Status_Tip.Text = "已保存";
            //teamKeys
            teamKeys.AddKey(Now_Key, Now_Bytes, TeamName.Text);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Button_Click_2(object sender, RoutedEventArgs e)
        {
            if (ListBox_TeamName.SelectedIndex != -1)
            {
                teamKeys.RemoveKey(ListBox_TeamName.SelectedItem.ToString());
                ListBox_TeamName.Items.RemoveAt(ListBox_TeamName.SelectedIndex);
                Status_Tip.Text = "已删除";
            }
        }

        /// <summary>
        /// 选中
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Button_Click_3(object sender, RoutedEventArgs e)
        {
            if (ListBox_TeamName.SelectedIndex != -1)
            {
                //检查teamKeys中有没有key
                if (teamKeys.TeamKeys.ContainsKey(ListBox_TeamName.SelectedItem.ToString()))
                {
                    OKCallBack.Invoke(ListBox_TeamName.SelectedItem.ToString());
                    this.Close();
                }
                else
                {
                    Status_Tip.Text = "该项已不存在！";
                }
            }
        }

        private void ListBox_TeamName_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            //如果没有
            if (ListBox_TeamName.SelectedItem == null) return;
            Now_Key = ListBox_TeamName.SelectedItem.ToString();
            Now_Bytes = teamKeys.GetKey_Bytes(Now_Key);
            // 将byte[]转换为ImageSource
            this.Screenshot.Source = ByteArrayToImageSource(Now_Bytes);
            Status_Tip.Text = "已保存";
            Ocr_Name.Text = " Key：" + Now_Key + " (单击修改)";
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct RECT
        {
            public int Left;                             //最左坐标
            public int Top;                             //最上坐标
            public int Right;                           //最右坐标
            public int Bottom;                        //最下坐标
        }

        /// <summary>
        /// Ocr_Name 双击标签后弹出弹窗
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Ocr_Name_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (Now_Key == "") return;
            if (Ocr_Name.Text == " Ocr异常，请确认是否截取到了ID") return;

            InputWindow inputWindow = new InputWindow(Now_Key);
            inputWindow.ShowDialog();
            if (inputWindow.IsOK)
            {
                string Last_Key = Now_Key;
                Now_Key = inputWindow.InputText;
                Ocr_Name.Text = " Key：" + Now_Key + " (单击修改)";
                teamKeys.UpdateKey(Last_Key, Now_Key, Now_Bytes);
            }
            ListBox_TeamName.Items.Clear();
            foreach (var item in teamKeys.TeamKeys)
                ListBox_TeamName.Items.Add(item.Key);
        }
    }
}