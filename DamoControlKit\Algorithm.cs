﻿using DamoControlKit.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DamoControlKit
{
    /// <summary>
    /// 算法
    /// </summary>
    public static class Algorithm
    {
        /// <summary>
        /// 计算两点之间的欧几里得距离
        /// </summary>
        /// <param name="point1"></param>
        /// <param name="point2"></param>
        /// <returns></returns>
        private static double CalculateDistance(Point point1, Point point2)
        {
            return Math.Sqrt(Math.Pow(point1.X - point2.X, 2) + Math.Pow(point1.Y - point2.Y, 2));
        }

        /// <summary>
        /// 查找最近的大框，如果距离小于user_minDistance则返回null
        /// </summary>
        /// <param name="smallBox"></param>
        /// <param name="largeBoxes"></param>
        /// <param name="user_minDistance"></param>
        /// <returns></returns>
        public static Position? FindClosestBox(Position smallBox, List<Position> largeBoxes, double user_minDistance)
        {
            if (largeBoxes.Count == 0)
                return null;

            var smallCenter = smallBox.GetCenterPoint();
            Position? closestBox = null;
            double minDistance = double.MaxValue;

            foreach (var box in largeBoxes)
            {
                var largeCenter = box.GetCenterPoint();
                double distance = CalculateDistance(smallCenter, largeCenter);

                if (distance < minDistance)
                {
                    minDistance = distance;
                    closestBox = box;
                }
            }
            if (user_minDistance < minDistance)
                return null;

            return closestBox;
        }

        /// <summary>
        /// 查找最近的大框
        /// </summary>
        /// <param name="smallBox"></param>
        /// <param name="largeBoxes"></param>
        /// <returns></returns>
        public static Position? FindClosestBox(Position smallBox, List<Position> largeBoxes)
        {
            if (largeBoxes.Count == 0)
                return null;

            var smallCenter = smallBox.GetCenterPoint();
            Position? closestBox = null;
            double minDistance = double.MaxValue;

            foreach (var box in largeBoxes)
            {
                var largeCenter = box.GetCenterPoint();
                double distance = CalculateDistance(smallCenter, largeCenter);

                if (distance < minDistance)
                {
                    minDistance = distance;
                    closestBox = box;
                }
            }
            Debug.WriteLine("最短的OK斜线长度：" + minDistance);
            return closestBox;
        }

        /// <summary>
        /// 查找上方区域最近的大框，如果距离小于user_minDistance则返回null
        /// </summary>
        /// <param name="smallBox">参考位置</param>
        /// <param name="largeBoxes">目标位置列表</param>
        /// <param name="user_minDistance">最小距离限制</param>
        /// <returns>返回上方区域最近的目标位置，如果没有则返回null</returns>
        public static Position? FindClosestBoxAbove(Position smallBox, List<Position> largeBoxes, double user_minDistance)
        {
            if (largeBoxes.Count == 0)
                return null;

            var smallCenter = smallBox.GetCenterPoint();
            Position? closestBox = null;
            double minDistance = double.MaxValue;

            // 只考虑Y坐标小于参考点的目标（上方区域）
            foreach (var box in largeBoxes)
            {
                var largeCenter = box.GetCenterPoint();

                // 只处理上方区域的目标
                if (largeCenter.Y < smallCenter.Y)
                {
                    double distance = CalculateDistance(smallCenter, largeCenter);

                    if (distance < minDistance)
                    {
                        minDistance = distance;
                        closestBox = box;
                    }
                }
            }

            // 如果没有找到上方区域的目标，或距离超过限制，返回null
            if (closestBox == null || user_minDistance < minDistance)
                return null;

            return closestBox;
        }

        /// <summary>
        /// 查找上方区域最近的大框
        /// </summary>
        /// <param name="smallBox">参考位置</param>
        /// <param name="largeBoxes">目标位置列表</param>
        /// <returns>返回上方区域最近的目标位置，如果没有则返回null</returns>
        public static Position? FindClosestBoxAbove(Position smallBox, List<Position> largeBoxes)
        {
            if (largeBoxes.Count == 0)
                return null;

            var smallCenter = smallBox.GetCenterPoint();
            Position? closestBox = null;
            double minDistance = double.MaxValue;

            // 只考虑Y坐标小于参考点的目标（上方区域）
            foreach (var box in largeBoxes)
            {
                var largeCenter = box.GetCenterPoint();

                // 只处理上方区域的目标
                if (largeCenter.Y < smallCenter.Y)
                {
                    double distance = CalculateDistance(smallCenter, largeCenter);

                    if (distance < minDistance)
                    {
                        minDistance = distance;
                        closestBox = box;
                    }
                }
            }

            if (closestBox != null)
            {
                Debug.WriteLine("上方区域最短的OK斜线长度：" + minDistance);
            }

            return closestBox;
        }
    }
}