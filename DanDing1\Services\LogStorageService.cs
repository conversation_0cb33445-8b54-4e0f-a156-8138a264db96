using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using XHelper.Models;

namespace DanDing1.Services
{
    /// <summary>
    /// 日志存储服务，用于存储和检索日志条目
    /// </summary>
    public class LogStorageService : ILogStorage, ILogForwarder
    {
        // 使用循环缓冲区存储最近的日志条目
        private readonly ConcurrentQueue<RemoteLogEntry> _logEntries = new ConcurrentQueue<RemoteLogEntry>();
        private readonly int _maxCapacity;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="maxCapacity">日志存储的最大容量</param>
        public LogStorageService(int maxCapacity = 1000)
        {
            _maxCapacity = maxCapacity > 0 ? maxCapacity : 1000;
        }

        /// <summary>
        /// 添加日志条目
        /// </summary>
        /// <param name="logEntry">要添加的日志条目</param>
        public void AddLogEntry(RemoteLogEntry logEntry)
        {
            if (logEntry == null)
                return;

            // 添加日志条目
            _logEntries.Enqueue(logEntry);

            // 如果超出容量，移除最旧的日志条目
            while (_logEntries.Count > _maxCapacity)
            {
                _logEntries.TryDequeue(out _);
            }
        }

        /// <summary>
        /// 获取最近的日志条目
        /// </summary>
        /// <param name="count">要获取的日志条目数量</param>
        /// <returns>日志条目列表</returns>
        public List<RemoteLogEntry> GetRecentLogs(int count)
        {
            if (count <= 0)
                return new List<RemoteLogEntry>();

            // 获取最新的日志条目（按时间戳降序排序）
            return _logEntries
                .OrderByDescending(log => log.Timestamp)
                .Take(count)
                .ToList();
        }

        /// <summary>
        /// 清除所有日志条目
        /// </summary>
        public void ClearLogs()
        {
            while (_logEntries.TryDequeue(out _)) { }
        }

        /// <summary>
        /// 实现ILogForwarder接口，将日志条目加入存储
        /// </summary>
        /// <param name="timestamp">日志时间戳</param>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="taskId">可选的任务ID</param>
        public void EnqueueLog(DateTime timestamp, string level, string message, string? taskId = null)
        {
            var logEntry = new RemoteLogEntry(timestamp, level, message, taskId);
            AddLogEntry(logEntry);
        }
    }
}