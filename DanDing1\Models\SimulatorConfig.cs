﻿using ScriptEngine.MuMu;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace DanDing1.Models
{
    public class SimulatorConfig : INotifyPropertyChanged
    {
        private int _index;
        private string _name;
        private int _adbPort;
        private bool _isMain;
        private bool _isRunning;
        private string _renderWnd;

        public int Index
        {
            get => _index;
            set
            {
                if (_index != value)
                {
                    _index = value;
                    OnPropertyChanged(nameof(Index));
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged(nameof(Name));
                }
            }
        }

        public int AdbPort
        {
            get => _adbPort;
            set
            {
                if (_adbPort != value)
                {
                    _adbPort = value;
                    OnPropertyChanged(nameof(AdbPort));
                }
            }
        }

        public bool IsMain
        {
            get => _isMain;
            set
            {
                if (_isMain != value)
                {
                    _isMain = value;
                    OnPropertyChanged(nameof(IsMain));
                }
            }
        }

        public bool IsRunning
        {
            get => _isRunning;
            set
            {
                if (_isRunning != value)
                {
                    _isRunning = value;
                    OnPropertyChanged(nameof(IsRunning));
                }
            }
        }

        /// <summary>
        /// 渲染窗口句柄，只有启动才会有
        /// </summary>
        [JsonProperty("render_wnd")]
        public string RenderWnd
        {
            get => _renderWnd;
            set
            {
                if (_renderWnd != value)
                {
                    _renderWnd = value;
                    OnPropertyChanged(nameof(RenderWnd));
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 设置数据
        /// </summary>
        public SimulatorConfig SetData(MuMuInstance instance)
        {
            Index = int.Parse(instance.Index);
            Name = instance.Name;
            AdbPort = instance.AdbPort ?? 0;
            IsMain = instance.IsMain;
            RenderWnd = instance.RenderWnd;
            IsRunning = !string.IsNullOrEmpty(instance.RenderWnd);
            return this;
        }

        public override string ToString()
        {
            return $"Index: {Index}, Name: {Name}, AdbPort: {AdbPort}, IsMain: {IsMain}, IsRunning: {IsRunning}";
        }
    }
}