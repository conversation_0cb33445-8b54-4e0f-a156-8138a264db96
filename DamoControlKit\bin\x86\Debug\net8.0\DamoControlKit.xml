<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DamoControlKit</name>
    </assembly>
    <members>
        <member name="T:DamoControlKit.Algorithm">
            <summary>
            算法
            </summary>
        </member>
        <member name="M:DamoControlKit.Algorithm.CalculateDistance(DamoControlKit.Model.Point,DamoControlKit.Model.Point)">
            <summary>
            计算两点之间的欧几里得距离
            </summary>
            <param name="point1"></param>
            <param name="point2"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Algorithm.FindClosestBox(DamoControlKit.Model.Position,System.Collections.Generic.List{DamoControlKit.Model.Position},System.Double)">
            <summary>
            查找最近的大框，如果距离小于user_minDistance则返回null
            </summary>
            <param name="smallBox"></param>
            <param name="largeBoxes"></param>
            <param name="user_minDistance"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Algorithm.FindClosestBox(DamoControlKit.Model.Position,System.Collections.Generic.List{DamoControlKit.Model.Position})">
            <summary>
            查找最近的大框
            </summary>
            <param name="smallBox"></param>
            <param name="largeBoxes"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Algorithm.FindClosestBoxAbove(DamoControlKit.Model.Position,System.Collections.Generic.List{DamoControlKit.Model.Position},System.Double)">
            <summary>
            查找上方区域最近的大框，如果距离小于user_minDistance则返回null
            </summary>
            <param name="smallBox">参考位置</param>
            <param name="largeBoxes">目标位置列表</param>
            <param name="user_minDistance">最小距离限制</param>
            <returns>返回上方区域最近的目标位置，如果没有则返回null</returns>
        </member>
        <member name="M:DamoControlKit.Algorithm.FindClosestBoxAbove(DamoControlKit.Model.Position,System.Collections.Generic.List{DamoControlKit.Model.Position})">
            <summary>
            查找上方区域最近的大框
            </summary>
            <param name="smallBox">参考位置</param>
            <param name="largeBoxes">目标位置列表</param>
            <returns>返回上方区域最近的目标位置，如果没有则返回null</returns>
        </member>
        <member name="T:DamoControlKit.Control.Operational">
            <summary>
            操作类
            </summary>
        </member>
        <member name="M:DamoControlKit.Control.Operational.#ctor(dmsoft)">
            <summary>
            操作类
            </summary>
        </member>
        <member name="M:DamoControlKit.Control.Operational.Click(DamoControlKit.Model.Point)">
            <summary>
            点击精准坐标
            </summary>
            <param name="x"></param>
            <param name="y"></param>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:DamoControlKit.Control.Operational.Click(System.Int32,System.Int32)">
            <summary>
            点击精准坐标
            </summary>
            <param name="x"></param>
            <param name="y"></param>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:DamoControlKit.Control.Operational.Click(DamoControlKit.Model.Position)">
            <summary>
            点击范围
            </summary>
            <param name="pos"></param>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:DamoControlKit.Control.Operational.Click(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            点击范围
            </summary>
            <param name="pos"></param>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:DamoControlKit.Control.Operational.Click(DamoControlKit.Model.Position,System.Int32,System.Int32)">
            <summary>
            多次点击
            </summary>
            <param name="pos"></param>
            <param name="count"></param>
            <param name="delay"></param>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:DamoControlKit.Control.Operational.ClickEx(DamoControlKit.Model.Position,System.Int32,System.Int32)">
            <summary>
            多次点击 概率减少点击次数 至少1次
            </summary>
            <param name="pos"></param>
            <param name="count"></param>
            <param name="delay"></param>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:DamoControlKit.Control.Operational.Slide_Pos(DamoControlKit.Model.Position,DamoControlKit.Model.Position)">
            <summary>
            范围滑动屏幕
            </summary>
            <param name="pos1"></param>
            <param name="pos2"></param>
        </member>
        <member name="M:DamoControlKit.Control.Operational.Slide_Pos(DamoControlKit.Model.Point,DamoControlKit.Model.Point)">
            <summary>
            范围滑动屏幕
            </summary>
            <param name="pos1"></param>
            <param name="pos2"></param>
        </member>
        <member name="P:DamoControlKit.DamoKit.IsReg">
            <summary>
            是否已经处于注册成功状态
            True:已经注册 False:没有注册
            </summary>
        </member>
        <member name="M:DamoControlKit.DamoKit.InitDll(System.String@)">
            <summary>
            注册Dll
            </summary>
            <param name="errorStr">错误描述字符串</param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.DamoKit.GetRegError(System.Int32)">
            <summary>
            获取注册错误的描述匹配
            </summary>
            <param name="ret_Code">错误ID</param>
            <returns>错误描述</returns>
        </member>
        <member name="M:DamoControlKit.DamoKit.GetLastErrorStr(System.Int32)">
            <summary>
            获取最后错误的描述匹配
            </summary>
            <param name="ret_Code">错误ID</param>
            <returns>错误描述</returns>
        </member>
        <member name="F:DamoControlKit.DamoKit._dmsoft">
            <summary>
            全局保留的一个dmsoft对象
            </summary>
        </member>
        <member name="M:DamoControlKit.DamoKit.Reg(System.String,System.String@,System.String)">
            <summary>
            注册插件
            </summary>
            <param name="code">注册码</param>
            <param name="errorStr">错误字符串</param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.DamoKit.ManualRegisterDll(System.String)">
            <summary>
            手动注册DLL方法
            </summary>
            <param name="dllPath">DLL的完整路径，默认为当前目录下的runtimes/dm.dll</param>
            <returns>注册是否成功</returns>
        </member>
        <member name="M:DamoControlKit.DamoKit.BindHwnd(DamoControlKit.Model.BindModel,System.String@)">
            <summary>
            绑定窗口
            </summary>
            <param name="bindModel">绑定对象</param>
            <param name="errorStr">错误描述字符串</param>
            <returns>X对象</returns>
            <exception cref="T:System.Exception">绑定发生的错误</exception>
        </member>
        <member name="M:DamoControlKit.DamoKit.GetXsoft">
            <summary>
            获取一个X对象
            </summary>
            <returns>X对象</returns>
        </member>
        <member name="M:DamoControlKit.DamoKit.BindHwndWithObject(dmsoft,DamoControlKit.Model.BindModel,System.String@)">
            <summary>
            使用已有的dmsoft对象绑定窗口
            </summary>
            <param name="dm">已存在的dmsoft对象</param>
            <param name="bindModel">绑定对象</param>
            <param name="errorStr">错误描述字符串</param>
            <returns>绑定是否成功</returns>
            <exception cref="T:System.Exception">绑定发生的错误</exception>
        </member>
        <member name="M:DamoControlKit.DamoKit.TryManualRegisterAndRegAsync(System.String,System.String)">
            <summary>
            尝试手动注册并重新注册插件
            </summary>
            <param name="code">注册码</param>
            <param name="fujia">附加参数</param>
            <returns>注册是否成功</returns>
        </member>
        <member name="T:DamoControlKit.DmSettings">
            <summary>
            大漠基础设置
            </summary>
        </member>
        <member name="M:DamoControlKit.DmSettings.Init(System.Boolean)">
            <summary>
            大漠基础配置初始化
            </summary>
            <param name="isDebug">是否开启调试模式</param>
        </member>
        <member name="M:DamoControlKit.Extensions.GetCenterPoint(DamoControlKit.Model.Position)">
            <summary>
            获取范围中心点坐标
            </summary>
            <param name="Pos"></param>
            <returns></returns>
        </member>
        <member name="P:DamoControlKit.Function.Flow_L_Find_MemPic.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:DamoControlKit.Function.Flow_L_Find_MemPic.FindYepDelayTime">
            <summary>
            找到图片执行后延迟时间 ms
            </summary>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_MemPic.SetEndTime(System.Int32)">
            <summary>
            设置超时时间
            默认结束时间30min
            </summary>
            <param name="timeSec">秒</param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_MemPic.SetFindYepDelayTime(System.Int32)">
            <summary>
            设置 找到图片执行后延迟时间
            单位 ms
            </summary>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_MemPic.SetXsoft(dmsoft)">
            <summary>
            设置所有Pic的dmsoft对象
            </summary>
            <param name="x"></param>
            <returns></returns>
        </member>
        <member name="P:DamoControlKit.Function.Flow_L_Find_MemPic.PicList">
            <summary>
            循环图片组
            </summary>
        </member>
        <member name="P:DamoControlKit.Function.Flow_L_Find_MemPic.ExitPicList">
            <summary>
            循环退出条件组
            循环条件中的图片不支持点击
            </summary>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_MemPic.AddExitPic(DamoControlKit.Model.MemPic)">
            <summary>
            添加退出条件 图片
            </summary>
            <param name="pic"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_MemPic.AddExitPics(DamoControlKit.Model.MemPics)">
            <summary>
            添加退出条件 图片
            </summary>
            <param name="pic"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_MemPic.AddPic(DamoControlKit.Model.MemPic)">
            <summary>
            添加图片
            </summary>
            <param name="pic"></param>
            <param name="pos"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_MemPic.AddPics(DamoControlKit.Model.MemPics)">
            <summary>
            添加图片集合
            </summary>
            <param name="pic"></param>
            <param name="pos"></param>
            <returns></returns>
        </member>
        <member name="P:DamoControlKit.Function.Flow_L_Find_MemPic.PicYepCallBackDic">
            <summary>
            AddPicAndFun函数的回调方法字典
            通过Name索引调用函数
            </summary>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_MemPic.AddPicAndFun(DamoControlKit.Model.MemPic,DamoControlKit.Function.FLFP_PicYep_Delegate)">
            <summary>
            添加图片 并执行回调函数
            </summary>
            <param name="pic"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_MemPic.AddPicsAndFun(DamoControlKit.Model.MemPics,DamoControlKit.Function.FLFP_PicYep_Delegate)">
            <summary>
            添加图片 并执行回调函数
            </summary>
            <param name="pic"></param>
            <returns></returns>
        </member>
        <member name="P:DamoControlKit.Function.Flow_L_Find_MemPic.EndCallBack">
            <summary>
            任务结束时调用此函数
            </summary>
        </member>
        <member name="F:DamoControlKit.Function.Flow_L_Find_MemPic.RunningWatch">
            <summary>
            运行时长计时
            </summary>
        </member>
        <member name="F:DamoControlKit.Function.Flow_L_Find_MemPic.isStop">
            <summary>
            外部控制结束变量
            </summary>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_MemPic.Sleep(System.Int32)">
            <summary>
            延迟
            </summary>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_MemPic.FindYep(System.String,DamoControlKit.Model.MemPic)">
            <summary>
            查找到图片后操作
            </summary>
        </member>
        <member name="T:DamoControlKit.Function.FLFP_PicYep_Delegate">
            <summary>
            循环 图片 找到 后执行 委托
            </summary>
            <param name="x"></param>
            <param name="y"></param>
        </member>
        <member name="P:DamoControlKit.Function.Flow_L_Find_Pic.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:DamoControlKit.Function.Flow_L_Find_Pic.FindYepDelayTime">
            <summary>
            找到图片执行后延迟时间 ms
            </summary>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_Pic.SetEndTime(System.Int32)">
            <summary>
            设置超时时间
            默认结束时间30min
            </summary>
            <param name="timeSec">秒</param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_Pic.SetFindYepDelayTime(System.Int32)">
            <summary>
            设置 找到图片执行后延迟时间
            单位 ms
            </summary>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_Pic.SetXsoft(dmsoft)">
            <summary>
            设置所有Pic的dmsoft对象
            </summary>
            <param name="x"></param>
            <returns></returns>
        </member>
        <member name="P:DamoControlKit.Function.Flow_L_Find_Pic.PicList">
            <summary>
            循环图片组
            </summary>
        </member>
        <member name="P:DamoControlKit.Function.Flow_L_Find_Pic.PosList">
            <summary>
            点击位置组
            </summary>
        </member>
        <member name="P:DamoControlKit.Function.Flow_L_Find_Pic.ExitPicList">
            <summary>
            循环退出条件组
            循环条件中的图片不支持点击
            </summary>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_Pic.AddExitPic(DamoControlKit.Model.Pic)">
            <summary>
            添加退出条件 图片
            </summary>
            <param name="pic"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_Pic.AddPicAndClick(DamoControlKit.Model.Pic,DamoControlKit.Model.Position)">
            <summary>
            添加图片 并找到图片 点击
            </summary>
            <param name="pic"></param>
            <param name="pos"></param>
            <returns></returns>
        </member>
        <member name="P:DamoControlKit.Function.Flow_L_Find_Pic.PicYepCallBackDic">
            <summary>
            AddPicAndFun函数的回调方法字典
            通过Name索引调用函数
            </summary>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_Pic.AddPicAndFun(DamoControlKit.Model.Pic,DamoControlKit.Function.FLFP_PicYep_Delegate)">
            <summary>
            添加图片 并执行回调函数
            </summary>
            <param name="pic"></param>
            <returns></returns>
        </member>
        <member name="F:DamoControlKit.Function.Flow_L_Find_Pic.RunningWatch">
            <summary>
            运行时长计时
            </summary>
        </member>
        <member name="M:DamoControlKit.Function.Flow_L_Find_Pic.FindYep(System.String,DamoControlKit.Model.Pic)">
            <summary>
            查找到图片后操作
            </summary>
        </member>
        <member name="T:DamoControlKit.Function.Flow_Q_Find_Pic">
            <summary>
            顺序执行模式 查找图片队列
            </summary>
        </member>
        <member name="P:DamoControlKit.Function.Flow_Q_Find_Pic.OutTime">
            <summary>
            超时时间 默认10秒
            </summary>
        </member>
        <member name="P:DamoControlKit.Function.Flow_Q_Find_Pic.OutTimeReStart">
            <summary>
            超时是否重新开始
            </summary>
        </member>
        <member name="P:DamoControlKit.Function.Flow_Q_Find_Pic.OutTimeGoBack">
            <summary>
            超时返回查找上一张
            </summary>
        </member>
        <member name="P:DamoControlKit.Function.Flow_Q_Find_Pic.PicList">
            <summary>
            图片列表
            </summary>
        </member>
        <member name="P:DamoControlKit.Function.Flow_Q_Find_Pic.PosList">
            <summary>
            位置列表
            </summary>
        </member>
        <member name="M:DamoControlKit.Function.Flow_Q_Find_Pic.Init">
            <summary>
            初始化日志等接口
            </summary>
        </member>
        <member name="M:DamoControlKit.Function.Flow_Q_Find_Pic.SetOutTime(System.Int32)">
            <summary>
            设置超时时间
            </summary>
            <param name="outTime"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_Q_Find_Pic.SetOutTimeReStart(System.Boolean)">
            <summary>
            设置 超时是否重新开始
            </summary>
            <param name="reStart"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_Q_Find_Pic.SetOutTimeGoBack(System.Boolean)">
            <summary>
            设置 超时是否返回上一张图片查找
            </summary>
            <param name="goBack"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_Q_Find_Pic.AddPicAndClick(DamoControlKit.Model.Pic,DamoControlKit.Model.Position)">
            <summary>
            添加图片 并提供相应的点击位置
            </summary>
            <param name="pic">图片</param>
            <param name="clickPos">点击位置</param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_Q_Find_Pic.AddPicNotClick(DamoControlKit.Model.Pic)">
            <summary>
            添加图片 不执行点击
            </summary>
            <param name="pic"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Function.Flow_Q_Find_Pic.Find_Yep(System.String)">
            <summary>
            等待到图片后执行动作
            </summary>
            <param name="identifier">点击标识</param>
        </member>
        <member name="T:DamoControlKit.Interface.IClick">
            <summary>
            点击功能 接口
            </summary>
        </member>
        <member name="M:DamoControlKit.Interface.IClick.Click">
            <summary>
            点击
            </summary>
            <param name="x"></param>
            <param name="y"></param>
        </member>
        <member name="M:DamoControlKit.Interface.IClick.Click(System.Int32,System.Int32)">
            <summary>
            精准点击
            </summary>
            <param name="x"></param>
            <param name="y"></param>
        </member>
        <member name="M:DamoControlKit.Interface.IClick.Click(DamoControlKit.Model.Position)">
            <summary>
            范围点击
            </summary>
        </member>
        <member name="M:DamoControlKit.Interface.IClick.Click(DamoControlKit.Model.Position,System.Int32,System.Int32)">
            <summary>
            范围点击
            </summary>
            <param name="position">范围</param>
            <param name="count">点击次数</param>
            <param name="delay">间隔</param>
        </member>
        <member name="M:DamoControlKit.Interface.IClick.ClickEx(DamoControlKit.Model.Position,System.Int32,System.Int32)">
            <summary>
            范围点击_防封有时count-1点击
            </summary>
            <param name="position">范围</param>
            <param name="count">点击次数</param>
            <param name="delay">间隔</param>
        </member>
        <member name="P:DamoControlKit.Interface.IDmInterface.dmsoft">
            <summary>
            大漠对象
            </summary>
        </member>
        <member name="M:DamoControlKit.Interface.IDmInterface.SetXsoft">
            <summary>
            设置新的大漠对象
            </summary>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Interface.IDmInterface.SetXsoft(dmsoft)">
            <summary>
            设置存储一个新的大漠对象
            </summary>
            <param name="x">大漠对象</param>
            <returns></returns>
        </member>
        <member name="T:DamoControlKit.Interface.IFind">
            <summary>
            查找功能 接口
            </summary>
        </member>
        <member name="M:DamoControlKit.Interface.IFind.Find(dmsoft)">
            <summary>
            查找一次
            </summary>
            <param name="x">大漠对象</param>
            <returns>查找结果</returns>
        </member>
        <member name="M:DamoControlKit.Interface.IFind.FindPoint(dmsoft)">
            <summary>
            查找坐标
            </summary>
            <param name="x">大漠对象</param>
            <returns>坐标</returns>
        </member>
        <member name="M:DamoControlKit.Interface.IFind.Await(dmsoft,System.Int32)">
            <summary>
            等待出现
            </summary>
            <param name="x">大漠对象</param>
            <param name="period">超时时间</param>
            <returns>超时未等到 返回False 等到立即返回True</returns>
        </member>
        <member name="T:DamoControlKit.Interface.IFlow">
            <summary>
            流程功能接口
            </summary>
        </member>
        <member name="M:DamoControlKit.Interface.IFlow.Start">
            <summary>
            启动
            </summary>
        </member>
        <member name="M:DamoControlKit.Interface.IFlow.Stop">
            <summary>
            停止
            </summary>
        </member>
        <member name="M:DamoControlKit.Interface.IFlow.Init">
            <summary>
            初始化
            </summary>
        </member>
        <member name="T:DamoControlKit.Model.Display">
            <summary>
            屏幕颜色获取方式
            </summary>
        </member>
        <member name="T:DamoControlKit.Model.Keypad">
            <summary>
            键盘仿真模式
            </summary>
        </member>
        <member name="T:DamoControlKit.Model.Mouse">
            <summary>
            鼠标仿真模式
            </summary>
        </member>
        <member name="T:DamoControlKit.Model.Bind_SetModel">
            <summary>
            绑定模式
            </summary>
        </member>
        <member name="T:DamoControlKit.Model.BindModel">
            <summary>
            后台绑定模式
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.BindModel.SetModel(DamoControlKit.Model.Bind_SetModel)">
            <summary>
            设置绑定模式
            </summary>
            <param name="model"></param>
        </member>
        <member name="P:DamoControlKit.Model.BindModel.Display">
            <summary>
            屏幕颜色获取方式
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.BindModel.Hwnd">
            <summary>
            句柄
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.BindModel.IsReady">
            <summary>
            是否准备就绪
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.BindModel.Keypad">
            <summary>
            键盘仿真模式
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.BindModel.Mode">
            <summary>
            绑定模式
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.BindModel.Mouse">
            <summary>
            鼠标仿真模式
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.BindModel.Public">
            <summary>
            公共属性
            </summary>
        </member>
        <member name="T:DamoControlKit.Model.Pixel">
            <summary>
            像素点颜色 适用于 CmpColor
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.Pixel.#ctor(System.Int32,System.Int32,System.String,System.Double)">
            <summary>
            像素点颜色 适用于 CmpColor
            </summary>
        </member>
        <member name="T:DamoControlKit.Model.Pixels">
            <summary>
            像素颜色集合
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.Pixels.PixelList">
            <summary>
            像素颜色集合
            </summary>
        </member>
        <member name="T:DamoControlKit.Model.MemPic">
            <summary>
            内存地址图片类
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPic.#ctor(System.String,System.String)">
            <summary>
            初始化图片资源路径
            </summary>
            <param name="name">图片名称</param>
            <param name="meminfo">内存信息</param>
        </member>
        <member name="P:DamoControlKit.Model.MemPic._Name">
            <summary>
            Log用图片名
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MemPic.ClickPositions">
            <summary>
            点击范围
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MemPic.Delta_Color">
            <summary>
            图片识别时偏色
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MemPic.Dir">
            <summary>
            图片识别时扫描查找方向
            0左到右、上到下
            1左到右、下到上
            2右到左、上到下
            3右到左、下到上
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MemPic.FindPosition">
            <summary>
            查找范围
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MemPic.Meminfo">
            <summary>
            内存地址 ptr,size
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MemPic.Name">
            <summary>
            图片名字
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MemPic.Sim">
            <summary>
            图片识别时相似度
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MemPic._Rename">
            <summary>
            真实的图片名
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MemPic.Remedy_Pics">
            <summary>
            补偿查找的图片
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MemPic.PositionOld">
            <summary>
            替换前的旧范围
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPic.AddClickPosition(DamoControlKit.Model.Position)">
            <summary>
            添加点击位置
            </summary>
            <param name="pos"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.MemPic.Click(System.Boolean)">
            <summary>
            点击操作
            </summary>
            <param name="isFindPos">是否使用查找位置进行点击</param>
        </member>
        <member name="M:DamoControlKit.Model.MemPic.Clone">
            <summary>
            深拷贝
            </summary>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.MemPic.Find(DamoControlKit.Model.Position)">
            <summary>
            在指定范围里找图
            </summary>
            <param name="pos">查找范围</param>
            <returns>是否找到图片</returns>
        </member>
        <member name="M:DamoControlKit.Model.MemPic.RecoverPosition">
            <summary>
            恢复原始范围
            </summary>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.MemPic.SetFindPosition(DamoControlKit.Model.Position)">
            <summary>
            设置图片查找范围
            </summary>
            <param name="pos"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.MemPic.SetParameter(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            设置其它参数
            delta_color|delta
            sim
            dir
            pos
            </summary>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.MemPic.SetPosition(DamoControlKit.Model.Position)">
            <summary>
            设置临时范围
            </summary>
            <returns></returns>
        </member>
        <member name="T:DamoControlKit.Model.MemPics">
            <summary>
            内存图片集合
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.Add(DamoControlKit.Model.MemPic)">
            <summary>
            添加图片
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.Add(System.Collections.Generic.List{DamoControlKit.Model.MemPic})">
            <summary>
            添加图片组
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.Add(DamoControlKit.Model.MemPics)">
            <summary>
            添加图片组
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.Filter(System.String)">
            <summary>
            筛选返回合集
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.FindAll">
            <summary>
            查找所有图片，找到任意一个返回True
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.FindAsPosition(DamoControlKit.Model.Position)">
            <summary>
            在指定范围内查找图片
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.FindAll(System.Int32@,System.Int32@)">
            <summary>
            查找所有图片并返回找到的第一个图片的坐标
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.FindAllAndClick">
            <summary>
            查找并点击所有找到的图片
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.FindAllE(System.Collections.Generic.List{System.String}@)">
            <summary>
            查找所有图片并返回找到的图片名称列表
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.FindAllEa">
            <summary>
            查找所有图片并返回第一个找到的图片
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.FindAllEx">
            <summary>
            查找所有图片并返回找到的图片集合
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.GetMemPic(System.String,System.Boolean)">
            <summary>
            获取指定名称的图片
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.SetAllXsoft(dmsoft)">
            <summary>
            设置所有图片的大漠对象
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.Wait(System.Int32)">
            <summary>
            等待图片集合中的任意一张图片出现
            </summary>
            <param name="seconds">等待时长（秒），默认3秒</param>
            <returns>在指定时间内找到图片返回True，否则返回False</returns>
        </member>
        <member name="M:DamoControlKit.Model.MemPics.Clone">
            <summary>
            深拷贝
            </summary>
        </member>
        <member name="T:DamoControlKit.Model.Pic">
            <summary>
            基本图片类型
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.Pic.#ctor(System.String)">
            <summary>
            初始化图片资源路径
            </summary>
            <param name="pathName"></param>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="P:DamoControlKit.Model.Pic.Delta_Color">
            <summary>
            图片识别时偏色
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.Pic.Dir">
            <summary>
            图片识别时扫描查找方向
            0左到右、上到下
            1左到右、下到上
            2右到左、上到下
            3右到左、下到上
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.Pic.Name">
            <summary>
            图片名字
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.Pic.Path">
            <summary>
            图片路径
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.Pic.Position">
            <summary>
            查找范围
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.Pic.Sim">
            <summary>
            图片识别时相似度
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.Pic.ErrorPicList">
            <summary>
            当查找失败时 另外查找的图片资源
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.Pic.GetPathName">
            <summary>
            获取全部路径 用于识别
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.Pic.IsSetPath">
            <summary>
            是否设置了路径
            只能设置一次路径
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.Pic.PositionOld">
            <summary>
            替换前的旧范围
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.Pic.AddErrorPic(DamoControlKit.Model.Pic)">
            <summary>
            添加 查找失败时 另外查找的图片资源
            </summary>
            <param name="pic"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.Pic.RecoverPosition">
            <summary>
            恢复原始范围
            </summary>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.Pic.SetFindPosition(DamoControlKit.Model.Position)">
            <summary>
            设置图片查找范围
            </summary>
            <param name="pos"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.Pic.SetParameter(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            设置其它参数
            delta_color|delta
            sim
            dir
            pos
            </summary>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.Pic.SetPath(System.String)">
            <summary>
            设置图片路径
            </summary>
            <param name="pathName"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:DamoControlKit.Model.Pic.SetPosition(DamoControlKit.Model.Position)">
            <summary>
            设置临时范围
            </summary>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.Pic.ErrorFind">
            <summary>
            找图失败补充查找
            </summary>
        </member>
        <member name="T:DamoControlKit.Model.Pics">
            <summary>
            图片类型集合
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.Pics.Add(DamoControlKit.Model.Pic)">
            <summary>
            添加图片
            </summary>
            <param name="pic"></param>
        </member>
        <member name="M:DamoControlKit.Model.Pics.Add(System.Collections.Generic.List{DamoControlKit.Model.Pic})">
            <summary>
            添加图片组
            </summary>
            <param name="pic"></param>
        </member>
        <member name="M:DamoControlKit.Model.Pics.Add(DamoControlKit.Model.Pics)">
            <summary>
            添加图片组
            </summary>
            <param name="pic"></param>
        </member>
        <member name="M:DamoControlKit.Model.Pics.Filter(System.String,System.Boolean)">
            <summary>
            筛选返回合集
            </summary>
            <param name="name"></param>
            <param name="isPath"></param>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.Pics.FindAll">
            <summary>
            查找所有 找到任意一个返回 True
            </summary>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.Pics.FindAllE(System.Collections.Generic.List{System.String}@)">
            <summary>
            查找所有 找到返回一个Name_Lists
            </summary>
            <returns>一个没找到返回False</returns>
        </member>
        <member name="M:DamoControlKit.Model.Pics.FindAllEx">
            <summary>
            查找所有 找到返回一个Name_Lists
            </summary>
            <returns>一个没找到返回False</returns>
        </member>
        <member name="M:DamoControlKit.Model.Pics.Remove(System.String)">
            <summary>
            删除图片 筛选名字
            </summary>
            <param name="name"></param>
        </member>
        <member name="M:DamoControlKit.Model.Pics.SetAllXsoft(dmsoft)">
            <summary>
            设置所有大漠对象
            </summary>
        </member>
        <member name="T:DamoControlKit.Model.MultiColor">
            <summary>
            多点找色类
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MultiColor.#ctor(DamoControlKit.Model.Position,System.String,System.String,System.Double,System.Int32)">
            <summary>
            多点找色类
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MultiColor.Dir">
            <summary>
            扫描方向
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MultiColor.First">
            <summary>
            首偏色点颜色
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MultiColor.Isready">
            <summary>
            是否准备好
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MultiColor.Offset">
            <summary>
            偏移颜色描述
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MultiColor.Pos">
            <summary>
            识别位置
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MultiColor.Sim">
            <summary>
            相似度
            </summary>
        </member>
        <member name="P:DamoControlKit.Model.MultiColor.Identifier">
            <summary>
            唯一标识符 队列用
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MultiColor.GetIdentifier">
            <summary>
            获取唯一标识符
            </summary>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.MultiColor.SetIdentifier(System.String)">
            <summary>
            设置唯一标识符
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MultiColor.SetIdentifier(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            设置唯一标识符
            </summary>
            <param name="identifier">唯一标识符</param>
            <param name="identifiers">已有的标识符列表</param>
            <returns>设置失败返回False</returns>
        </member>
        <member name="T:DamoControlKit.Model.MultiColors">
            <summary>
            多点找色类集合
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.MultiColors.FindAll">
            <summary>
            查找所有多点找色 遇到成功即返回True
            </summary>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.MultiColors.FindAll(System.Int32@,System.Int32@)">
            <summary>
            查找所有多点找色 遇到成功即返回True
            </summary>
            <returns></returns>
        </member>
        <member name="T:DamoControlKit.Model.Point">
            <summary>
            坐标_点
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.Point.CmpColor(System.String,System.Double)">
            <summary>
            比色
            </summary>
            <param name="color_str"></param>
            <returns></returns>
        </member>
        <member name="T:DamoControlKit.Model.Position">
            <summary>
            范围
            </summary>
        </member>
        <member name="F:DamoControlKit.Model.Position.isReady">
            <summary>
            是否可以点击
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.Position.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            初始化1
            </summary>
            <param name="x">坐标1_x</param>
            <param name="y">坐标1_y</param>
            <param name="x1">坐标2_x</param>
            <param name="y1">坐标2_y</param>
        </member>
        <member name="M:DamoControlKit.Model.Position.#ctor(System.String)">
            <summary>
            初始化2
            </summary>
            <param name="Str">字符串格式"x,y,x1,y1"</param>
            <exception cref="T:System.Exception">vs.Length != 4</exception>
        </member>
        <member name="P:DamoControlKit.Model.Position.isGS">
            <summary>
            是否为跟随模式
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.Position.#ctor(DamoControlKit.Model.Point,DamoControlKit.Model.Point)">
            <summary>
            初始化3
            </summary>
            <param name="p1">xy点1</param>
            <param name="p2">xy点2</param>
        </member>
        <member name="P:DamoControlKit.Model.Position.Identifier">
            <summary>
            唯一标识符 队列用
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.Position.GetIdentifier">
            <summary>
            获取唯一标识符
            </summary>
            <returns></returns>
        </member>
        <member name="M:DamoControlKit.Model.Position.SetIdentifier(System.String)">
            <summary>
            设置唯一标识符
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.Position.SetIdentifier(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            设置唯一标识符
            </summary>
            <param name="identifier">唯一标识符</param>
            <param name="identifiers">已有的标识符列表</param>
            <returns>设置失败返回False</returns>
        </member>
        <member name="M:DamoControlKit.Model.Position.Clone">
            <summary>
            深拷贝
            </summary>
            <returns></returns>
        </member>
        <member name="T:DamoControlKit.Model.Positions">
            <summary>
            范围集合
            </summary>
        </member>
        <member name="M:DamoControlKit.Model.Positions.Click(System.String)">
            <summary>
            点击 根据唯一标识符
            </summary>
            <param name="identifier"></param>
        </member>
        <member name="M:DamoControlKit.Model.Positions.Queue_Click(System.Int32)">
            <summary>
            顺序点击队列
            </summary>
            <param name="delay">间隔时间</param>
        </member>
        <member name="T:DamoControlKit.Model.RecordData">
            <summary>
            用户通知+记录数据类
            </summary>
        </member>
        <member name="F:DamoControlKit.Model.RecordData.SubClick">
            <summary>
            辅助点击次数
            </summary>
        </member>
        <member name="F:DamoControlKit.Model.RecordData.SubPoints">
            <summary>
            辅助点击坐标列表
            </summary>
        </member>
        <member name="F:DamoControlKit.Model.RecordData.MainClick">
            <summary>
            主键点击次数
            </summary>
        </member>
        <member name="F:DamoControlKit.Model.RecordData.MainPoints">
            <summary>
            主键点击坐标列表
            </summary>
        </member>
        <member name="F:DamoControlKit.Model.RecordData.TaskNames">
            <summary>
            用户任务名
            </summary>
        </member>
        <member name="T:DamoControlKit.BindModels">
            <summary>
            获取内置的绑定模式
            </summary>
        </member>
        <member name="T:DamoControlKit.runtimes.Record">
            <summary>
            运行过程中记录点击次数类
            </summary>
        </member>
        <member name="M:DamoControlKit.runtimes.Record.ReSet(System.Boolean)">
            <summary>
            重新记录
            </summary>
        </member>
        <member name="M:DamoControlKit.runtimes.Record.ReSet(System.Int32,System.Boolean)">
            <summary>
            重新记录
            </summary>
        </member>
        <member name="M:DamoControlKit.runtimes.Record.SaveClick(System.Int32,System.Int32)">
            <summary>
            存储点击数据
            </summary>
        </member>
        <member name="M:DamoControlKit.runtimes.Record.SaveClick(System.Int32,System.Int32,System.Int32)">
            <summary>
            根据大漠ID 存储点击数据
            </summary>
        </member>
        <member name="M:DamoControlKit.runtimes.Record.SaveClick(System.Int32,System.String)">
            <summary>
            根据大漠ID 存储点击数据
            </summary>
        </member>
        <member name="M:DamoControlKit.runtimes.Record.SaveClick(System.String)">
            <summary>
            存储点击数据
            </summary>
        </member>
    </members>
</doc>
