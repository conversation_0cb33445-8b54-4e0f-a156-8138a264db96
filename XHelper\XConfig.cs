﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace XHelper
{
    /// <summary>
    /// 配置项读写
    /// </summary>
    public class XConfig
    {
        private static string Base_Path = ".\\runtimes\\AppConfig\\";

        private static void CheckPath() => XPath.CheckPathIsExist(Base_Path);

        /// <summary>
        /// 保存变量至文件
        /// </summary>
        public static void SaveValueToFile(string name, object obj)
        {
            CheckPath();
            XSerializer.SerializeObjectToJsonFile(obj, Path.Combine(Base_Path, name));
        }

        /// <summary>
        /// 保存变量至文件-加密
        /// </summary>
        public static void SaveValueToFile_Encrypt(string name, object obj, string key, string iv)
        {
            CheckPath();
            if (obj == null)
                return;
            XSerializer.SerializeObjectToJsonFile(AesEncryption.Encrypt(obj.ToString(), key, iv), Path.Combine(Base_Path, name));
        }

        /// <summary>
        /// 加载对于变量
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="name"></param>
        /// <returns></returns>
        public static string? LoadValueFromFile_Decrypt(string name, string key, string iv)
        {
            string filePath = Path.Combine(Base_Path, name);
            if (!File.Exists(filePath))
                return default;
            var str = XSerializer.DeserializeJsonFileToObject<string>(filePath);
            try
            {
                return AesEncryption.Decrypt(str, key, iv);
            }
            catch (Exception)
            {
                return "";
            }
        }

        /// <summary>
        /// 保存变量至文件
        /// </summary>
        public static bool SaveValueToFile(string _class, string name, object obj)
        {
            try
            {
                string dirPath = Path.Combine(Base_Path, _class);
                
                // 确保目录结构完整存在
                if (!Directory.Exists(dirPath))
                {
                    Directory.CreateDirectory(dirPath);
                }
                
                string filePath = Path.Combine(dirPath, name);
                XSerializer.SerializeObjectToJsonFile(obj, filePath);
                return File.Exists(filePath);
            }
            catch (Exception ex)
            {
                XLogger.Error($"保存配置到文件失败: {_class}/{name}, 错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 加载对于变量
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="name"></param>
        /// <returns></returns>
        public static T? LoadValueFromFile<T>(string name)
        {
            string filePath = Path.Combine(Base_Path, name);
            if (!File.Exists(filePath))
                return default;

            return XSerializer.DeserializeJsonFileToObject<T>(filePath);
        }

        /// <summary>
        /// 加载对于变量
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="name"></param>
        /// <returns></returns>
        public static T? LoadValueFromFile<T>(string _class, string name)
        {
            string filePath = Path.Combine(Base_Path, _class, name);
            if (!File.Exists(filePath))
                return default;

            return XSerializer.DeserializeJsonFileToObject<T>(filePath);
        }

        /// <summary>
        /// 删除指定的配置文件
        /// </summary>
        /// <param name="name">配置文件名</param>
        /// <param name="_class">可选的分类名</param>
        /// <returns>如果文件删除成功或文件原本就不存在，则返回 true；否则返回 false。</returns>
        public static bool DeleteConfigFile(string name, string? _class = null)
        {
            string filePath;
            if (string.IsNullOrEmpty(_class))
            {
                filePath = Path.Combine(Base_Path, name);
            }
            else
            {
                filePath = Path.Combine(Base_Path, _class, name);
            }

            if (!File.Exists(filePath))
            {
                return true;
            }

            try
            {
                File.Delete(filePath);
                return !File.Exists(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting file {filePath}: {ex.Message}");
                return false;
            }
        }
    }
}