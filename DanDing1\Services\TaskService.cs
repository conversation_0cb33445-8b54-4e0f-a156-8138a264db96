using DanDing1.Helpers;
using DanDing1.Models;
using ScriptEngine.Model;
using System.Collections.ObjectModel;

namespace DanDing1.ViewModels.Services
{
    /// <summary>
    /// 任务服务类 - 处理所有任务添加和配置相关的操作
    /// </summary>
    public class TaskService
    {
        private readonly InfoBarModel _infoBar;
        private readonly PresetConfigManager _presetManager;

        public TaskService(InfoBarModel infoBar)
        {
            _infoBar = infoBar;
            _presetManager = new PresetConfigManager();
        }

        /// <summary>
        /// 解析分析任务配置 返回任务
        /// </summary>
        /// <param name="name">任务名称</param>
        /// <param name="viewModel">包含任务配置的视图模型</param>
        /// <returns>任务配置</returns>
        public TaskConfigsModel.Configs? GetConfig(string name, dynamic viewModel)
        {
            name = name.Replace("True", "1").Replace("False", "0");
            string errorStr = "";

            try
            {
                var cc = name.Split("|")[0] switch
                {
                    "御魂" => HandleYhunConfig(viewModel, out errorStr),
                    "御灵" => HandleYlingConfig(viewModel, out errorStr),
                    "突破" => HandleTpoConfig(viewModel, out errorStr),
                    "寮突" => HandleLTpoConfig(viewModel, out errorStr),
                    "探索" => HandleTsuoConfig(viewModel, out errorStr),
                    "斗技" => HandleDjiConfig(viewModel, out errorStr),
                    "六道" => HandleLiudaoConfig(viewModel, out errorStr),
                    "英杰" => HandleYjieConfig(viewModel, out errorStr),
                    "契灵" => HandleQilingConfig(viewModel, out errorStr),
                    "觉醒" => HandleJxingConfig(viewModel, out errorStr),
                    "业原火" => HandleBaseConfig("业原火", viewModel.Yyhuo_Count, viewModel.Qta_Biaoji, viewModel, out errorStr),
                    "日轮" => HandleBaseConfig("日轮", viewModel.Rlun_Count, viewModel.Qta_Biaoji, viewModel, out errorStr),
                    "永生" => HandleBaseConfig("永生", viewModel.Ysheng_Count, viewModel.Qta_Biaoji, viewModel, out errorStr),
                    "百鬼" => HandleBaseConfig("百鬼", viewModel.Bgui_Count, viewModel.Qta_Biaoji, viewModel, out errorStr),
                    "Buff" => AddTask.Buff(name.Split("|"), out errorStr),
                    "等待" => AddTask.Delay(double.Parse(viewModel.Delay_Time), out errorStr),
                    "每周秘闻" => AddTask.FastTask("每周秘闻", viewModel.MeiZhouMiWen_Biaoji),
                    "抽厕纸" => AddTask.FastTask("抽厕纸"),
                    "半自动" => AddTask.FastTask("半自动", viewModel.Bzidong_Biaoji, viewModel.Bzidong_AutoEndTime),
                    "日常" => HandleDailyTaskConfig(viewModel, out errorStr),
                    "定时" => HandleTimerConfig(viewModel, out errorStr),
                    "活动" => HandleActiveConfig("活动", viewModel.HDong_Count, viewModel.HDong_Biaoji, viewModel.HDong_Fake_Mode, viewModel.HDong_FirstEnd, viewModel, out errorStr),//TODO 添加参数Preset => 取HDong_YuShe的值，然后获取 UniqueKey，多个使用|分割
                    _ => throw new ArgumentException("未知的任务类型")
                };
                if (cc != null)
                    return cc;
                else
                    _infoBar?.Show("无法添加", errorStr, "Warning", 3000);
                return null;
            }
            catch (Exception ex)
            {
                _infoBar?.Show("无法添加", ex.Message, "Warning", 3000);
                return null;
            }
        }

        /// <summary>
        /// 添加任务
        /// </summary>
        /// <param name="name">任务名称</param>
        /// <param name="viewModel">包含任务配置的视图模型</param>
        /// <returns>是否成功添加任务</returns>
        public async Task<bool> OnAddTask(string name, dynamic viewModel)
        {
            // 检查六道任务是否需要 PaddleOCR
            if (name == "六道" && !await CheckPaddleOCR())
            {
                return false;
            }

            TaskConfigsModel.Configs? configs = GetConfig(name, viewModel);
            if (configs is not null)
            {
                viewModel.GameTaskLists.Add(configs);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 检查是否安装PaddleOCR
        /// </summary>
        private async Task<bool> CheckPaddleOCR()
        {
            // 实现检查PaddleOCR的逻辑
            // 此处需要根据实际情况实现
            return true;
        }

        private void DelTimerTask(dynamic viewModel, string name = "定时任务")
        {
            //GameTaskLists
            ObservableCollection<TaskConfigsModel.Configs>? configs = viewModel.GameTaskLists as ObservableCollection<TaskConfigsModel.Configs>;

            // 检查configs是否为空
            if (configs == null || configs.Count == 0)
                return;

            // 创建一个列表存储需要删除的任务
            var tasksToRemove = configs.Where(config => config.Name == name).ToList();

            // 删除所有匹配的任务
            foreach (var task in tasksToRemove)
            {
                configs.Remove(task);
            }
        }

        /// <summary>
        /// 获取预设参数的UniqueKey列表
        /// </summary>
        /// <param name="presetName">预设名称</param>
        /// <param name="sceneType">场景类型</param>
        /// <returns>UniqueKey列表，以|分隔</returns>
        private string GetPresetUniqueKeys(string presetName, string sceneType = "")
        {
            //重载配置
            _presetManager.ReloadConfigs();

            // 如果预设名称为"不换预设"或为空，返回空字符串
            if (string.IsNullOrEmpty(presetName) || presetName == "不换预设")
                return string.Empty;

            // 查找预设配置
            var preset = _presetManager.Configs.FirstOrDefault(c => c.Name == presetName);
            if (preset == null)
                return string.Empty;

            // 检查场景限制
            if (!string.IsNullOrEmpty(sceneType) && !preset.AllScenes)
            {
                // 根据场景类型检查是否应该应用此预设
                bool applyPreset = false;

                switch (sceneType.ToLower())
                {
                    case "御魂":
                        applyPreset = preset.Yuhun;
                        break;
                    case "突破":
                        applyPreset = preset.Tupo;
                        break;
                    case "探索":
                        applyPreset = preset.Tansuo;
                        break;
                    case "御灵":
                        applyPreset = preset.Yuling;
                        break;
                    case "斗技":
                        applyPreset = preset.Douji;
                        break;
                    case "英杰":
                        applyPreset = preset.Yingxiong;
                        break;
                    case "六道":
                        applyPreset = preset.Liudao;
                        break;
                    case "契灵":
                        applyPreset = preset.Qiling;
                        break;
                    case "日常":
                        applyPreset = preset.Richang;
                        break;
                    default:
                        // 未知场景类型，默认应用
                        applyPreset = true;
                        break;
                }

                // 如果不应该应用此预设，返回空字符串
                if (!applyPreset)
                {
                    return string.Empty;
                }
            }

            // 提取UniqueKey列表，以|分隔
            return string.Join("|", preset.Parameters.Select(p => p.UniqueKey));
        }

        /// <summary>
        /// 活动任务配置
        /// </summary>
        private TaskConfigsModel.Configs? HandleActiveConfig(string taskName, string countStr, bool hdongBiaoji, bool hdongFakeMode, bool hdongFirstEnd, dynamic viewModel, out string errorStr)
        {
            if (DateTime.Now > GlobalData.ActiveEndTime)
            {
                errorStr = "活动已结束！";
                _infoBar?.Show("无法添加", "当前脚本内置的活动任务已结束！如果有最新活动，请催更！", "Warning", 3000);
                return null;
            }

            int count = ParseInt(countStr, $"{taskName}次数不合法！请重新输入数字！");
            var config = AddTask.Hdong(taskName, count, out errorStr);
            if (config != null)
            {
                config.Others.Add("Biaoji", hdongBiaoji.ToString()); // 活动标记
                config.Others.Add("Mode", hdongFakeMode ? "普通体力" : "活动体力"); // 活动模式
                config.Others.Add("FirstEnd", hdongFirstEnd.ToString()); // 提前结束
                config.Others.Add("FakeTime", viewModel.HDong_Fake_Time.ToString()); // 提前结束时间
                config.Others.Add("HDong_Class", viewModel.HDong_Class.ToString()); // 活动类型
                // 添加预设参数
                string presetUniqueKeys = GetPresetUniqueKeys(viewModel.HDong_YuShe, "活动");
                if (!string.IsNullOrEmpty(presetUniqueKeys))
                {
                    config.Others.Add("Preset", presetUniqueKeys);
                }
                // 设置显示名
                config.ShowName = viewModel.HDong_Class != "" ? "活动-" + viewModel.HDong_Class : "活动";
            }
            return config;
        }

        private TaskConfigsModel.Configs? HandleBaseConfig(string taskName, string countStr, bool qtaBiaoji, dynamic viewModel, out string errorStr)
        {
            int count = ParseInt(countStr, $"{taskName}次数不合法！请重新输入数字！");
            var config = AddTask.Base(taskName, count, out errorStr);
            if (config != null)
            {
                config.Others.Add("Biaoji", qtaBiaoji.ToString());

                // 添加预设参数
                string presetUniqueKeys = GetPresetUniqueKeys(viewModel.Qta_YuShe, "日常");
                if (!string.IsNullOrEmpty(presetUniqueKeys))
                {
                    config.Others.Add("Preset", presetUniqueKeys);
                }
            }
            if (config != null && taskName == "百鬼")
                config.Others.Add("Inv", viewModel.Bgui_Inv.ToString());
            return config;
        }

        /// <summary>
        /// 解析日常任务
        /// </summary>
        /// <param name="viewModel"></param>
        /// <param name="errorStr"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private TaskConfigsModel.Configs HandleDailyTaskConfig(dynamic viewModel, out string errorStr)
        {
            DelTimerTask(viewModel, "日常任务");
            errorStr = "";
            int count = 1;
            var config = AddTask.Base("日常任务", count, out errorStr);
            if (config == null) return null;
            // 添加定时任务
            config.Others.TryAdd("DiGui", viewModel.DailyTask_DiGui.ToString());
            config.Others.TryAdd("ChouKa", viewModel.DailyTask_ChouKa.ToString());
            config.Others.TryAdd("Befriendly", viewModel.DailyTask_Befriendly.ToString());
            config.Others.TryAdd("Reward", viewModel.DailyTask_Reward.ToString());
            config.Others.TryAdd("FreeGift", viewModel.DailyTask_FreeGift.ToString());
            config.Others.TryAdd("Checkin", viewModel.DailyTask_Checkin.ToString());
            config.Others.TryAdd("Liao30SE", viewModel.DailyTask_Liao30SE.ToString());

            // 添加预设参数
            string presetUniqueKeys = GetPresetUniqueKeys(viewModel.RChang_YuShe, "日常");
            if (!string.IsNullOrEmpty(presetUniqueKeys))
                config.Others.Add("Preset", presetUniqueKeys);

            _infoBar.Show("日常任务提示：", "配置日常任务成功！", "Success", 3000);
            return config;
        }

        private TaskConfigsModel.Configs? HandleDjiConfig(dynamic viewModel, out string errorStr)
        {
            int count = ParseInt(viewModel.Dji_Count, "斗技次数不合法！请重新输入数字！");
            var config = AddTask.Dji(count, viewModel.Dji_Biaoji, viewModel.Dji_ManTing, out errorStr);
            if (config != null)
            {
                config.Others.Add("MingShi", viewModel.Dji_MingShi.ToString());

                // 添加预设参数
                string presetUniqueKeys = GetPresetUniqueKeys(viewModel.Dji_YuShe, "斗技");
                if (!string.IsNullOrEmpty(presetUniqueKeys))
                {
                    config.Others.Add("Preset", presetUniqueKeys);
                }
            }
            return config;
        }

        private TaskConfigsModel.Configs? HandleJxingConfig(dynamic viewModel, out string errorStr)
        {
            int count = ParseInt(viewModel.Jxing_Count, "觉醒次数不合法！请重新输入数字！");
            var config = AddTask.Jxing(count, new() {
                { "Class", viewModel.Jxing_Class },
                { "Biaoji", viewModel.Qta_Biaoji.ToString() },
                { "AutoBuff", viewModel.Jxing_AutoBuff.ToString() }
            }, out errorStr);

            if (config != null)
            {
                // 添加预设参数
                string presetUniqueKeys = GetPresetUniqueKeys(viewModel.Qta_YuShe, "觉醒");
                if (!string.IsNullOrEmpty(presetUniqueKeys))
                {
                    config.Others.Add("Preset", presetUniqueKeys);
                }
            }

            return config;
        }

        /// <summary>
        /// 六道任务处理
        /// </summary>
        private TaskConfigsModel.Configs? HandleLiudaoConfig(dynamic viewModel, out string errorStr)
        {
            int count = ParseInt(viewModel.Liudao_Count, "六道次数不合法！请重新输入数字！");
            //处理六道额外技能
            string extraSkill = "";
            if (viewModel.Liudao_Extra_Skill_YL) extraSkill += "妖力化生|";
            if (viewModel.Liudao_Extra_Skill_XY) extraSkill += "细雨化屏|";

            // 获取预设参数
            string presetUniqueKeys = GetPresetUniqueKeys(viewModel.Liudao_YuShe, "六道");

            var parameters = new Dictionary<string, string>
            {
                { "Biaoji", viewModel.Liudao_Biaoji.ToString() },
                { "XingZhiDao_Damo", viewModel.Liudao_XingZhiDao_Damo.ToString() },
                { "ShuangBeiJiaCheng", viewModel.Liudao_ShuangBeiJiaCheng.ToString() },
                { "ShuangBeiJiaCheng_Buy", viewModel.Liudao_ShuangBeiJiaCheng_Buy.ToString() },
                { "HunDunBuKaiXiang", viewModel.Liudao_HunDunBuKaiXiang.ToString() },
                { "Extra_Skill", extraSkill }
            };

            // 添加预设参数
            if (!string.IsNullOrEmpty(presetUniqueKeys))
            {
                parameters.Add("Preset", presetUniqueKeys);
            }

            return AddTask.Liudao(count, parameters, out errorStr);
        }

        private TaskConfigsModel.Configs? HandleLTpoConfig(dynamic viewModel, out string errorStr)
        {
            int count = ParseInt(viewModel.LTpo_Count, "寮突次数不合法！请重新输入数字！");

            // 获取预设参数
            string presetUniqueKeys = GetPresetUniqueKeys(viewModel.Tpo_YuShe, "寮突");

            var parameters = new Dictionary<string, string>
            {
                { "Biaoji", viewModel.Tpo_Biaoji },
                { "Tui4", viewModel.Tpo_Tui4.ToString() },
                { "EndStartLtu", viewModel.Tpo_RunLtu.ToString() },
                { "LtuCount", viewModel.LTpo_Count }
            };

            // 添加预设参数
            if (!string.IsNullOrEmpty(presetUniqueKeys))
            {
                parameters.Add("Preset", presetUniqueKeys);
            }

            return AddTask.LTpo(count, parameters, out errorStr);
        }

        /// <summary>
        /// 契灵任务处理
        /// </summary>
        private TaskConfigsModel.Configs? HandleQilingConfig(dynamic viewModel, out string errorStr)
        {
            int count = ParseInt(viewModel.Qiling_Count, "契灵次数不合法！请重新输入数字！");

            // 获取预设参数
            string presetUniqueKeys = GetPresetUniqueKeys(viewModel.Qiling_YuShe, "契灵");

            var parameters = new Dictionary<string, string>
            {
                { "Biaoji", viewModel.Qiling_Biaoji.ToString() },
                { "TaskType", viewModel.Qiling_TaskType },
                { "AutoBuySummon", viewModel.Qiling_AutoBuySummon.ToString() }
            };

            // 添加预设参数
            if (!string.IsNullOrEmpty(presetUniqueKeys))
            {
                parameters.Add("Preset", presetUniqueKeys);
            }

            return AddTask.Qiling(count, parameters, out errorStr);
        }

        /// <summary>
        /// 定时任务
        /// </summary>
        /// <param name="viewModel"></param>
        /// <param name="errorStr"></param>
        /// <returns></returns>
        private TaskConfigsModel.Configs HandleTimerConfig(dynamic viewModel, out string errorStr)
        {
            DelTimerTask(viewModel);
            errorStr = "";
            int count = 1;
            var config = AddTask.Base("定时任务", count, out errorStr);
            if (config == null) return null;
            // 添加定时任务
            config.Others.TryAdd("OffLine", viewModel.Timer_OffLine.ToString());
            // 寄养任务
            config.Others.TryAdd("JiYang", viewModel.Timer_JiYang.ToString());
            config.Others.TryAdd("JiYang_Priority", viewModel.Timer_JiYang_Priority);
            config.Others.TryAdd("JiYang_PassLowLevel", viewModel.Timer_JiYang_PassLowLevel.ToString());
            config.Others.TryAdd("JiYang_DesignatedName", viewModel.Timer_JiYang_DesignatedName); // 指定名称
            config.Others.TryAdd("JiYang_DesignatedTactics", viewModel.Timer_JiYang_DesignatedTactics); // 指定策略
            // 放卡任务
            config.Others.TryAdd("FangKa", viewModel.Timer_FangKa.ToString());
            config.Others.TryAdd("FangKa_Class", viewModel.Timer_FangKa_Class); // 指定类型
            config.Others.TryAdd("FangKa_Level", viewModel.Timer_FangKa_Level); // 指定等级
            config.Others.TryAdd("FangKa_HuaDongCount", viewModel.Timer_FangKa_HuaDongCount); // 限制最大滑动次数

            _infoBar.Show("定时任务提示：", "配置定时任务成功！", "Success", 3000);
            return config;
        }

        private TaskConfigsModel.Configs? HandleTpoConfig(dynamic viewModel, out string errorStr)
        {
            int count = ParseInt(viewModel.Tpo_Count, "突破次数不合法！请重新输入数字！");

            // 获取预设参数
            string presetUniqueKeys = GetPresetUniqueKeys(viewModel.Tpo_YuShe, "突破");

            var parameters = new Dictionary<string, string>
            {
                { "Biaoji", viewModel.Tpo_Biaoji },                 //标记
                { "Tui4", viewModel.Tpo_Tui4.ToString() },          //卡级
                { "Affirm", viewModel.Tpo_Affirm.ToString() },      //刷新前等待确认
                { "EndStartLtu", viewModel.Tpo_RunLtu.ToString() }, //结束后寮突
                { "LtuCount", viewModel.LTpo_Count }                //寮突次数
            };

            // 添加预设参数
            if (!string.IsNullOrEmpty(presetUniqueKeys))
            {
                parameters.Add("Preset", presetUniqueKeys);
            }

            return AddTask.Tpo(count, parameters, out errorStr);
        }

        private TaskConfigsModel.Configs? HandleTsuoConfig(dynamic viewModel, out string errorStr)
        {
            int count = ParseInt(viewModel.Tsuo_Count, "探索次数不合法！请重新输入数字！");
            if (viewModel.TSuo_ZuDui_IsChecked)
            {
                var Tk = new TeamKeysModel();
                if (viewModel.TSuo_ZuDui_Location == "队长")
                {
                    if (string.IsNullOrEmpty(viewModel.TSuo_ZuDui_Name))
                    {
                        errorStr = "无法添加，请选择队员名字！";
                        _infoBar?.Show("无法添加", "请选择队员名字！", "Warning", 3000);
                        return null;
                    }
                    var config = AddTask.TSun_ZuDui(count, viewModel.Tsuo_CombatStr, viewModel.Tsuo_Counting_Mode, viewModel.TSuo_ZuDui_Location, Tk.GetKey_Name(viewModel.TSuo_ZuDui_Name), viewModel.TSuo_ZuDui_Name
                        , Tk.GetKey_Bytes(viewModel.TSuo_ZuDui_Name), out errorStr);

                    // 添加预设参数
                    if (config != null)
                    {
                        string presetUniqueKeys = GetPresetUniqueKeys(viewModel.TSuo_YuShe, "探索");
                        if (!string.IsNullOrEmpty(presetUniqueKeys))
                        {
                            config.Others.Add("Preset", presetUniqueKeys);
                        }
                    }

                    return config;
                }
                else
                {
                    var config = AddTask.TSun_ZuDui(count, viewModel.Tsuo_CombatStr, viewModel.Tsuo_Counting_Mode, viewModel.TSuo_ZuDui_Location, "", ""
                        , new byte[0], out errorStr);

                    // 添加预设参数
                    if (config != null)
                    {
                        string presetUniqueKeys = GetPresetUniqueKeys(viewModel.TSuo_YuShe, "探索");
                        if (!string.IsNullOrEmpty(presetUniqueKeys))
                        {
                            config.Others.Add("Preset", presetUniqueKeys);
                        }
                    }

                    return config;
                }
            }
            var c = AddTask.Tsuo(count, viewModel.Tsuo_CombatStr, viewModel.Tsuo_Counting_Mode, out errorStr);
            if (c != null)
            {
                c.Others.Add("AutoBuff", viewModel.Tsuo_AutoBuff.ToString());

                // 添加预设参数
                string presetUniqueKeys = GetPresetUniqueKeys(viewModel.TSuo_YuShe, "探索");
                if (!string.IsNullOrEmpty(presetUniqueKeys))
                {
                    c.Others.Add("Preset", presetUniqueKeys);
                }
            }
            return c;
        }

        private TaskConfigsModel.Configs? HandleYhunConfig(dynamic viewModel, out string errorStr)
        {
            int count = ParseInt(viewModel.Yhun_Count, "御魂次数不合法！请重新输入数字！");
            if (viewModel.Yhun_ZuDui_IsChecked)
            {
                var Tk = new TeamKeysModel();
                if (viewModel.Yhun_ZuDui_Location == "队长")
                {
                    if (string.IsNullOrEmpty(viewModel.Yhun_ZuDui_Name))
                    {
                        errorStr = "无法添加，请选择队员名字！";
                        _infoBar?.Show("无法添加", "请选择队员名字！", "Warning", 3000);
                        return null;
                    }
                    var config = AddTask.Yhun_ZuDui(count, viewModel.Yhun_Level, viewModel.Yhun_Biaoji, viewModel.Yhun_ZuDui_Location, Tk.GetKey_Name(viewModel.Yhun_ZuDui_Name), viewModel.Yhun_ZuDui_Name
                        , Tk.GetKey_Bytes(viewModel.Yhun_ZuDui_Name), out errorStr);

                    // 添加预设参数
                    if (config != null)
                    {
                        string presetUniqueKeys = GetPresetUniqueKeys(viewModel.Yhun_YuShe, "御魂");
                        if (!string.IsNullOrEmpty(presetUniqueKeys))
                        {
                            config.Others.Add("Preset", presetUniqueKeys);
                        }
                    }

                    return config;
                }
                else
                {
                    var config = AddTask.Yhun_ZuDui(count, viewModel.Yhun_Level, viewModel.Yhun_Biaoji, viewModel.Yhun_ZuDui_Location, "", ""
                        , new byte[0], out errorStr);

                    // 添加预设参数
                    if (config != null)
                    {
                        string presetUniqueKeys = GetPresetUniqueKeys(viewModel.Yhun_YuShe, "御魂");
                        if (!string.IsNullOrEmpty(presetUniqueKeys))
                        {
                            config.Others.Add("Preset", presetUniqueKeys);
                        }
                    }

                    return config;
                }
            }

            // 获取预设参数
            string yhunPresetUniqueKeys = GetPresetUniqueKeys(viewModel.Yhun_YuShe, "御魂");

            var parameters = new Dictionary<string, string>
            {
                { "Level", viewModel.Yhun_Level },
                { "Biaoji", viewModel.Yhun_Biaoji.ToString() },
                { "AutoBuff", viewModel.Yhun_AutoBuff.ToString() }
            };

            // 添加预设参数
            if (!string.IsNullOrEmpty(yhunPresetUniqueKeys))
            {
                parameters.Add("Preset", yhunPresetUniqueKeys);
            }

            return AddTask.Yhun(count, parameters, out errorStr);
        }

        /// <summary>
        /// 英杰试炼配置
        /// </summary>
        /// <param name="viewModel"></param>
        /// <param name="errorStr"></param>
        /// <returns></returns>
        private dynamic HandleYjieConfig(dynamic viewModel, out string errorStr)
        {
            int count = ParseInt(viewModel.Yjie_Count, "英杰试炼次数不合法！请重新输入数字！");

            // 获取预设参数
            string yjiePresetUniqueKeys = GetPresetUniqueKeys(viewModel.Yjie_YuShe, "英杰");

            // 设置英杰试炼参数
            var parameters = new Dictionary<string, string>
            {
                { "Class", viewModel.Yjie_Class }, // 战斗类别
                { "Biaoji", viewModel.Yjie_Biaoji.ToString() } // 标记
            };

            // 添加预设参数
            if (!string.IsNullOrEmpty(yjiePresetUniqueKeys))
            {
                parameters.Add("Preset", yjiePresetUniqueKeys);
            }

            // 使用通用的添加任务方法
            var config = AddTask.Base("英杰", count, out errorStr);
            if (config != null)
            {
                config.ShowName = "英杰-" + parameters["Class"]; // 设置显示名称
                foreach (var param in parameters)
                {
                    config.Others[param.Key] = param.Value;
                }
            }

            return config;
        }

        private TaskConfigsModel.Configs? HandleYlingConfig(dynamic viewModel, out string errorStr)
        {
            int count = ParseInt(viewModel.Yling_Count, "御灵次数不合法！请重新输入数字！");

            // 获取预设参数
            string presetUniqueKeys = GetPresetUniqueKeys(viewModel.Yling_YuShe, "御灵");

            var parameters = new Dictionary<string, string>
            {
                { "Class", viewModel.Yling_Class },
                { "Biaoji", viewModel.Yling_Biaoji.ToString() }
            };

            // 添加预设参数
            if (!string.IsNullOrEmpty(presetUniqueKeys))
            {
                parameters.Add("Preset", presetUniqueKeys);
            }

            return AddTask.Yling(count, parameters, out errorStr);
        }

        private int ParseInt(string input, string errorMessage)
        {
            if (!int.TryParse(input, out int result) || result <= 0)
            {
                throw new ArgumentException(errorMessage);
            }
            return result;
        }
    }
}