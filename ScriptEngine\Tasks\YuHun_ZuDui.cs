﻿using DamoControlKit.Model;
using DamoControlKit.runtimes;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System.Diagnostics.Metrics;
using System.Linq;
using static ScriptEngine.Model.TaskConfigsModel;

namespace ScriptEngine.Tasks
{
    /// <summary>
    /// 组队御魂
    /// </summary>
    internal class YuHun_ZuDui : BaseTask
    {
        private bool BiaoJi_Status;

        private int count = 0;

        private List<string> DontSendLog = ["标记", "协站队伍界面", "组队接受邀请", "组队默认接受邀请", "邀请位置", "喂食"];

        private Dictionary<string, Position> InvFriendPoss = new()
        {
            {"好友",new Position(349,101,411,132) },
            {"最近",new Position(470,98,533,133) },
            {"寮友",new Position(579,101,639,132) },
            {"跨区",new Position(692,98,754,130) }
        };

        /// <summary>
        /// 队长任务-手动邀请任务中的队友人数
        /// </summary>
        private int InvHand_Count = 0;

        private bool MR_Friend = false;

        private bool MR_IncFriend = false;

        private MultiColors multiColors = new()
        {
            MultiColorList = [
                new MultiColor(new Position(106,111,178,452),"5ab565-101010","0|5|58b462-101010,3|2|5fba6b-101010,5|1|5db868-101010,0|1|5ab565-101010",0.9,0)
                ,new MultiColor(new Position(106,111,178,452),"746350-101010","0|8|59b564-101010,4|8|5db768-101010,8|4|7bbe82-101010,0|5|5ab665-101010",0.9,0)
            ]
        };

        private Dictionary<int, Position> SelectLevels = new()
        {
            {12,new Position(446,523,546,553) },
            {11,new Position(438,450,537,479) },
            {10,new Position(450,384,537,419) },
            {9,new Position(451,310,542,345) },
            {8,new Position(454,249,533,272) },
            {7,new Position(453,197,543,212) }
        };

#pragma warning disable CS8618
        private YuHun_ZuDuiConfig _config { get; set; }
#pragma warning restore CS8618

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "御魂组队");
            foreach (var item in InvFriendPoss)
                item.Value.SetXsoft(dm);
            foreach (var item in SelectLevels)
                item.Value.SetXsoft(dm);
            multiColors.SetXsoft(dm);
        }

        /// <summary>
        /// 初始化队长任务状态
        /// </summary>
        public bool Inv_Init_Status()
        {
            if (!NowISMainScene())
            {
                log.Error("无法继续，当前不在御魂组队房间内！");
                return false;
            }
            InvHand_Count = GetMainSceneCount();
            if (InvHand_Count == 0)
            {
                log.Error("无法继续，当前房间没有任何好友，无法开始！");
                return false;
            }
            log.Info($"当前房间已经存在{InvHand_Count}个队员，后续当队友个数达到{InvHand_Count}时才开始战斗！");
            return true;
        }

        /// <summary>
        /// 队长任务
        /// </summary>
        public void Main_Inv()
        {
        ReJoin:
            JoinMainScene();//进入房间场景
        ReInv:
            if (!InviteName())
            {
                Sleep(2000);
                goto ReJoin;
            }
            log.Info("已邀请队友，等待15秒！");
        ReFake:
            if (!WaitFake())
            {
                //重新邀请队友
                log.Info("未等待到全部队友，重新邀请队友！");
                goto ReInv;
            }

            if (Combat())
            {
                //胜利
                count++;
                log.Info_Green("战斗胜利，战斗胜利次数：" + count);
                if (!MR_IncFriend)
                {
                    MR_IncFriend = true;
                    int tryCount = 0;
                    while (!new Pixel(802, 428, "f3b25e", 0.9).Find(Dm) && tryCount < 5)
                    {
                        Sleep(1000);
                        Fast.Click(548, 614, 700, 643);
                        tryCount++;
                    }
                    if (tryCount < 5)
                    {
                        log.Info("默认邀请队友");
                        Fast.Click(549, 349, 570, 369);
                        Sleep(100);
                        Fast.Click(718, 412, 799, 448);
                        Sleep(1000);
                    }
                }
            }
            else
            {
                //失败
                log.Warn("战斗失败，重新邀请队友!");
                int tryCount = 0;
                while (!new Pixel(802, 424, "f3b25e", 0.9).Find(Dm) && tryCount < 5)
                {
                    Sleep(1000);
                    Fast.Click(716, 414, 800, 445);
                    tryCount++;
                }
                Defeated();
            }
            ;
            log.Info("等待回到组队房间！");
            WaitMainScene(1);
            if (count >= _config.Ncount)
            {
                log.Info_Green("任务次数达到设置次数，当前任务结束！");
                EndCallBack();
                return;
            }
            goto ReFake;
        }

        /// <summary>
        /// 成员任务
        /// </summary>
        public void Mian_Friend()
        {
            int j = 0;
        ReJoin:
            if (!NowISMainScene())
            {
                log.Info("成员任务开始，等待接受邀请！");
                var mps = Mp.Filter("组队接受邀请");
                int x, y;
                while (!mps.FindAll(out x, out y))
                {
                    Sleep(500);
                    if (multiColors.FindAll(out x, out y)) break;
                }
                log.Info_Green("接受邀请！");
                Fast.Click(x, y);
                Sleep(1000);
            }
        ReFake:
            log.Info("等待准备战斗、战斗结果！");
            if (Combat())
            {//胜利
                count++;
                log.Info_Green("战斗胜利，战斗胜利次数：" + count);

                //再检测一下当前是否退出了
                if (Mp.Filter("达摩")
                .Add(Mp.Filter("发现宝藏"))
                .Add(Mp.Filter("胜利"))
                .Add(Mp.Filter("失败")).FindAllEx() is not null)
                {
                    log.Warn("队员还没有退出，再尝试点一下退出！");
                    Fast.Click(840, 141, 980, 160);
                    Sleep(50, true);
                    Fast.Click(840, 141, 980, 160);
                }

                if (!MR_Friend)
                {
                    Sleep(2000);
                    int xx, yy, i = 0;
                    log.Info("查询是否有默认邀请可以接受..");
                    var ptm = Mp.Filter("组队默认接受邀请");
                    while (!ptm.FindAll(out xx, out yy))
                    {
                        i++;
                        j++;
                        Sleep(500);
                        if (i >= 5)
                            break;
                    }
                    if (i >= 5)
                    {
                        if (j >= 3)
                        {
                            log.Warn($"没有找到默认邀请... 已经第{j}次胜利之后没有找到默认邀请了，设置状态为已接受了默认邀请！");
                            MR_Friend = true;
                            goto ReFake;
                        }
                        log.Warn($"没有找到默认邀请... 开始等待战斗胜利结果（{j}/3）");
                        goto ReFake;
                    }
                    log.Info("默认接受邀请..");
                    MR_Friend = true;
                    Fast.Click(xx, yy);
                    Sleep(1500);
                    Fast.Click(575, 353, 600, 375);
                    Sleep(666);
                    Fast.Click(713, 413, 817, 454);
                    Sleep(666);
                }
            }
            else
            {//失败
                log.Warn("战斗失败，重新接受邀请!");
                Defeated();
            }
            ;
            if (count >= _config.Ncount)
            {
                log.Info_Green("任务次数达到设置次数，当前任务结束！");
                EndCallBack();
                return;
            }
            goto ReFake;
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            _config = new(configs);
            if (_config.Level != -1 && _config.Level < 7)
            {
                log.Error("组队暂不支持1-6层的组队战斗！无法继续！");
                return;
            }

            log.Info(_config.ToString());
            if (_config.Location == "队长")
                Main_Inv();
            else if (_config.Location == "队员")
                Mian_Friend();
            else if (_config.Location == "队长-手动邀请")
                Main_Inv_Hand();
            UserNotificationMessage = $"共战斗{count}/{_config.Ncount}次.";
        }

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            var pics = Mp.Filter("达摩")
                    .Add(Mp.Filter("发现宝藏"))
                    .Add(Mp.Filter("胜利"))
                    .Add(Mp.Filter("喂食"))
                    .Add(Mp.Filter("失败"))
                    .Add(Mp.Filter("活动"))
                    .Add(Mp.Filter("准备"));

            //点击开始
            if (_config.Location is "队长" or "队长-手动邀请")
            {
                pics.Add(Mp.Filter("战斗开始"))
                    .Add(Mp.Filter("标记"));

                Fast.Click("1193,623,1253,675");
                log.Info("战斗点击开始");
            }

            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置
            while (!isbreak)
            {
                Sleep(500);
                var ps = pics.FindAllEx();
                if (ps is not null && ps.Count == 0) continue;
                ps?.PicList.ForEach(p =>
                {
                    FindOkFun(p.Name, p);
                    if (!DontSendLog.Any(p.Name.Contains)) log.Info($"发现：{p._Name}");
                    //优化多线程点击方案1
                    if (p.ClickPositions is not null) Operational.Click(p.ClickPositions[new Random().Next(0, p.ClickPositions.Count)]);
                    else Operational.Click(p.FindPosition);

                    if (p.Name.Contains("胜利") || p.Name.Contains("达摩"))
                    {
                        ret_bol = true;
                        isbreak = true;
                        Sleep(150);
                    }
                    if (p.Name.Contains("失败"))
                    {
                        ret_bol = false;
                        isbreak = true;
                    }
                });
            }
            if (ret_bol && _config.Location == "队员")
                Combat_End();//等待Yuhun界面

            return ret_bol;
        }

        /// <summary>
        /// 御魂胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("达摩")
                .Add(Mp.Filter("发现宝藏"))
                .Add(Mp.Filter("胜利"))
                .Add(Mp.Filter("失败"));
            MemPic? p = null;
            Fast.Click(840, 141, 980, 160);
            Sleep(1000);
            while ((p = pics.FindAllEa()) != null)
            {
                Fast.Click(840, 141, 980, 160);
                Sleep(500);
                log.Debug($"while Ing...{p._Rename}");
            }
            log.Debug("while End...");
            Sleep(300);
            Fast.Click(840, 141, 980, 160);
            Sleep(300);
        }

        /// <summary>
        /// 结束任务方法
        /// </summary>
        private void EndCallBack()
        {
            log.Info("执行御魂组队任务收尾方法,等待返回后,退出到探索。");
            if (_config.Location == "队长")
            {
                var mps = new MemPics()
                .Add(Mp.Filter("御魂组队.达摩"));

                while (!Mp.Filter("御魂组队.协站队伍界面").FindAll())
                {
                    mps.FindAllAndClick();
                    Sleep(1000);
                }
            }
            else
            {
                while (!Mp.Filter("御魂组队.协站队伍界面").FindAll()) Sleep(20, true);
                log.Info("退出到探索..1");
                Sleep(30, true);
                //Position pos1 = new(37, 22, 69, 57);
                Record.SaveClick(Dm.GetID(), Dm.MoveToEx(37, 22, 69 - 37, 57 - 22));
                Dm.LeftClick();
                //Sleep(20, true);
                //pos1 = new(717, 418, 797, 446);
                Record.SaveClick(Dm.GetID(), Dm.MoveToEx(717, 418, 797 - 717, 446 - 418));
                Dm.LeftClick();
                log.Info("退出到探索..2");
                Sleep(1200);
                return;
            }

            log.Info("退出到探索..");
            if (_config.Location == "队长") Sleep(500); else Sleep(100, true);
            Position pos = new("37,22,69,57");
            string ret = Dm.MoveToEx(pos.X, pos.Y, pos.X1 - pos.X, pos.Y1 - pos.Y);
            Record.SaveClick(Dm.GetID(), ret);
            Dm.LeftClick();
            if (_config.Location == "队长") Sleep(500); else Sleep(30, true);
            pos = new("717,418,797,446");
            ret = Dm.MoveToEx(pos.X, pos.Y, pos.X1 - pos.X, pos.Y1 - pos.Y);
            Record.SaveClick(Dm.GetID(), ret);
            Dm.LeftClick();
            Sleep(1200);
        }

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            //判断喂食
            if (Db.UserConfigs.TryGetValue("YuhunFood", out object? _T) && _T is bool T && T && name.Contains("喂食"))
            {
                log.Info("宠物饿了，开始自动喂食");
                Sleep(200);
                pic.FindAndClick(Dm);
                Sleep(500);
                Fast.Click(925, 528, 973, 566);
                Sleep(1500);
                Fast.Click(695, 105, 846, 151);
                if (Random.Shared.Next(0, 100) < 50)
                    Fast.Click("695,105,846,151");
                return false;
            }

            if (_config.BiaoJi && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.2秒，标记位置：5号位");
                Sleep(200);
                Fast.Click("1013,454,1082,551");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 获取房间人数
        /// </summary>
        /// <returns></returns>
        private int GetMainSceneCount()
        {
            Pixel color1 = new(643, 278, "fffde9-020202", 0.99);
            Pixel color2 = new(1088, 277, "fffce4-020202", 0.99);
            int count = 2;
            if (color1.Find(Dm)) count -= 1;
            if (color2.Find(Dm)) count -= 1;
            //var mps = Mp.Filter("邀请位置");
            //if (mps is null)
            //    return 0;

            return count;// 2 - mps?.FindAllEx()?.Count ?? 2;
        }

        /// <summary>
        /// 邀请成员
        /// </summary>
        private bool InviteName()
        {
            string Team1_Orig = _config.Team1;
            if (!NowISMainScene())
            {
                log.Warn("不在组队界面内，无法邀请成员！");
                return false;
            }
            MR_IncFriend = false;
            int Now_count = GetMainSceneCount();
            log.Info("打开邀请界面");
            Fast.Click(1060, 229, 1114, 276);
            Sleep(1000);
            List<string> Team1 = ["好友", "最近", "寮友", "跨区"];
            Team1.Remove(Team1_Orig);
            int i = -1;
        ReTry1:
            if (_config.Name1 != "")
            {
                string team_show = i != -1 ? Team1[i - 1] : Team1_Orig;
                log.Info("开始邀请队友1，" + _config.Name1 + "|" + team_show);
                if (i == -1)
                {
                    InvFriendPoss[Team1_Orig].Click();
                    Sleep(1000);
                }
                var point = _config.Name1_Pic.FindPoint(Dm);
                if (point is null)
                {
                    //不存在
                    log.Warn("队友没有找到，或不在线？可能在其它区域，更换队友所在区域再试一下！");
                    if (i == -1) i = 0;
                    if (i >= Team1.Count)
                    {
                        log.Warn("尝试邀请队友失败，队友可能真的不在线！或请检查队友名称是否正确！点击 取消");
                        Fast.Click(469, 557, 535, 592);
                        return false;
                    }
                    InvFriendPoss[Team1[i++]].Click();
                    Sleep(1000);
                    goto ReTry1;
                }
                if (i != -1)
                    _config.Team1 = Team1[i - 1];
                Operational.Click(point);
                log.Info("点击 邀请");
                Fast.Click(741, 559, 810, 592);
                Sleep(1200);
            }

            Sleep(1000);
            return true;
        }

        /// <summary>
        /// 进入房间场景
        /// </summary>
        private void JoinMainScene()
        {
            if (NowISMainScene())
            {
                log.Info_Green("当前在组队房间内，跳过进入房间场景，直接邀请队友！");
                return;
            }

            if (new Pixel(884, 570, "f3b25e", 0.9).Find(Dm))
            {
                log.Info("点击 创建");
                Fast.Click(850, 573, 905, 599);
                Sleep(1000);
                if (NowISMainScene())
                    return;
            }
            Scene.TO.TingYuan();
            log.Info("点击 组队");
            Fast.Click(437, 616, 473, 666);
            Sleep(500);
            log.Info("将列表滑动到最上面..");
            Operational.Slide_Pos(new Position("247,164,275,187"), new Position("246,611,268,629"));
            Sleep(1000);
            //高级的查找御魂位置方案，后续再优化
            var res = Fast.Ocr_String(142, 121, 366, 664, (t) =>
            {
                //找到御魂 点击御魂位置
                foreach (var item in t)
                    if (item.Text.Contains("御魂"))
                    {
                        log.Info("点击 御魂");
                        Fast.Click(142 + item.Center.X, 121 + item.Center.Y);
                    }
            });
            while (!res.Contains("御魂"))
            {
                Operational.Slide_Pos(new Position("196,242,319,257"), new Position("196,128,314,139"));
                Sleep(1000);
                res = Fast.Ocr_String(142, 121, 366, 664, (t) =>
                {
                    //找到御魂 点击御魂位置
                    foreach (var item in t)
                        if (item.Text.Contains("御魂"))
                        {
                            log.Info("点击 御魂");
                            Fast.Click(142 + item.BoxPoints[0].X, 121 + item.BoxPoints[0].Y);
                        }
                });
            }

            Sleep(500);
            log.Info("选择 层数");
            if (_config.Level != -1 && _config.Level >= 7)
                SelectLevels[_config.Level].Click();
            else if (_config.Level == -1)
            {
                log.Info("组队任务不选层，默认选择11-魂土！");
                SelectLevels[11].Click();
            }
            Sleep(500);
            log.Info("点击 创建队伍");
            Fast.Click(1012, 613, 1123, 637);
            Sleep(500);
            log.Info("点击 不公开");
            Fast.Click(750, 499, 780, 520);
            log.Info("点击 创建");
            Fast.Click(831, 573, 912, 610);
            WaitMainScene();
            if (NowISMainScene())
                log.Info("已进入到组队房间！");
        }

        /// <summary>
        /// 队长任务-手动邀请任务
        /// 需要队长已经拉好好友
        /// </summary>
        private void Main_Inv_Hand()
        {
            log.Info("您当前的御魂组队模式为手动模式！");
            log.Info("需要您在房间内，拉好队友后开始！");
            if (!Inv_Init_Status()) return;

            ReFake:
            WaitFake(true);
            if (Combat())
            {//胜利
                count++;
                log.Info_Green("战斗胜利，战斗胜利次数：" + count);
                if (!MR_IncFriend)
                {
                    MR_IncFriend = true;
                    while (!new Pixel(802, 428, "f3b25e", 0.9).Find(Dm))
                    {
                        //检测是否回到组队界面
                        if (NowISMainScene())
                            goto Con;
                        Sleep(1000);
                        Fast.Click(548, 614, 700, 643);
                    }
                    log.Info("默认邀请队友");
                    Fast.Click(549, 349, 570, 369);
                    Sleep(100);
                    Fast.Click(718, 412, 799, 448);
                    Sleep(1000);
                }
            }
            else
            {//失败
                log.Warn("战斗失败，手动邀请模式下无法继续执行!");
                while (!new Pixel(802, 424, "f3b25e", 0.9).Find(Dm))
                {
                    Sleep(1000);
                    Fast.Click(716, 414, 800, 445);
                }
                Defeated();
            }
            ;
            log.Info("等待回到组队房间！");
            WaitMainScene(1);
        Con:
            if (count >= _config.Ncount)
            {
                log.Info_Green("任务次数达到设置次数，当前任务结束！");
                EndCallBack();
                return;
            }
            goto ReFake;
        }

        /// <summary>
        /// 当前是否在房间场景
        /// </summary>
        /// <returns></returns>
        private bool NowISMainScene()
        {
            var mps = Mp.Filter("协站队伍界面");
            return mps.FindAll();
        }

        /// <summary>
        /// 等待战斗
        /// </summary>
        private bool WaitFake(bool isHand = false)
        {
            bool ret_bol = false;
            if (isHand)
            {
            ReTry:
                if (!NowISMainScene())
                {
                    log.Warn($"当前不在组队房间内，等待返回到组队房间再开始判定能否开始战斗..");
                    Sleep(1000);
                    goto ReTry;
                }
                while (GetMainSceneCount() - InvHand_Count != 0)
                {
                    log.Info($"等待队友全部到齐..{GetMainSceneCount()}/{InvHand_Count}");
                    Sleep(1000);
                    ret_bol = false;
                }
                if (!ret_bol) { ret_bol = true; goto ReTry; }
                log.Info_Green("已等待到全部队友，开始执行战斗..");
                return true;
            }

            int count = 0;
            while (GetMainSceneCount() - _config.FriendCount() != 0)
            {
                if (count >= 15)
                    return false;
                count++;
                Sleep(1000);
            }
            log.Info_Green("已等待到全部队友，开始执行战斗..");
            return true;
        }

        /// <summary>
        /// 等待房间场景
        /// </summary>
        private void WaitMainScene(int max = 3)
        {
            int count = 0;
            var mps = Mp.Filter("协站队伍界面");
            while (!mps.FindAll())
            {
                Sleep(1000);
                count++;
                if (count >= max)
                {
                    count = 0;
                    Operational.Click(610, 20, 685, 46);
                }
            }
        }
    }

    /// <summary>
    /// 组队御魂任务参数
    /// </summary>
    internal class YuHun_ZuDuiConfig : BaseTaskConfig
    {
        private readonly Configs configs;
#pragma warning disable CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑添加 "required" 修饰符或声明为可为 null。

        public YuHun_ZuDuiConfig(TaskConfigsModel.Configs configs)
#pragma warning restore CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑添加 "required" 修饰符或声明为可为 null。
        {
            this.configs = configs;
            Ncount = configs.Count;
            BiaoJi = bool.Parse(configs.Others.TryGetValue("Biaoji", out string? value) ? value : "False");
            try { Level = int.Parse(configs.Others.TryGetValue("Level", out value) ? value : "-1"); } catch (Exception) { Level = -1; }
            Location = configs.Others.TryGetValue("Location", out value) ? value : "";
            if (Location == "") throw new Exception("御魂组队任务无法继续，未查询到组队身份！(队长？队员？)");
            if (Location == "队长") InitName_Pic1();
        }

        /// <summary>
        /// 层数
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 组队任务身份
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// 队友1号
        /// </summary>
        public string Name1 { get; set; }

        /// <summary>
        /// 成员1名字截图
        /// </summary>
        public MemPic Name1_Pic { get; private set; }

        /// <summary>
        /// 队友2号- 闲置
        /// </summary>
        public string Name2 { get; set; }

        /// <summary>
        /// 成员2名字截图- 闲置
        /// </summary>
        public MemPic Name2_Pic { get; private set; }

        /// <summary>
        /// 队友1 邀请队友所在区域
        /// </summary>
        public string Team1 { get; set; }

        /// <summary>
        /// 队友2 邀请队友所在区域- 闲置
        /// </summary>
        public string Team2 { get; set; }

        public int FriendCount()
        {
            int count = 0;
            if (Name1 is not null) count++;
            if (Name2 is not null) count++;
            return count;
        }

        public override string ToString()
        {
            return $"御魂组队配置-总次数:{Ncount}-标记:{BiaoJi}-选层:{Level}-身份:{Location}-成员1名:{Name1} 所在:{Team1}";
        }

        /// <summary>
        /// 初始化成员名字截图
        /// </summary>
        /// <exception cref="Exception"></exception>
        /// <exception cref="InvalidCastException"></exception>
        private void InitName_Pic1()
        {
            Team1 = configs.Others.TryGetValue("Team", out string? value) ? value : "";
            if (Team1 == "") throw new Exception("御魂组队任务无法继续，队长必须设置邀请队友所在区域！");
            Name1 = configs.Others.TryGetValue("Name", out value) ? value : "";
            if (Name1 == "") throw new Exception("御魂组队任务无法继续，未查询到其它成员名字！");
            configs.Others_Obj.TryGetValue(Name1, out object? obj);
            byte[]? bytes = null;
            if (obj is System.Text.Json.JsonElement jsonElement)
                if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.String)
                {
                    string base64String = jsonElement.GetString() ?? throw new Exception("御魂组队任务无法继续，JsonElement 中数据为空！");
                    bytes = Convert.FromBase64String(base64String);
                }
                else throw new InvalidCastException("JsonElement 不包含 Base64 编码的字符串。");
            else
                bytes = obj as byte[];
            if (bytes is null) throw new Exception("御魂组队任务无法继续，成员没有对应截图数据！");
            int ptr, size;
            ptr = new Data_Pic("成员1", "御魂组队", bytes).GetMemPtr(out size);
            Name1_Pic = new MemPic("御魂组队.成员1.bmp", $"{ptr},{size}").SetFindPosition(new(0, 0, 2000, 2000));
        }
    }
}