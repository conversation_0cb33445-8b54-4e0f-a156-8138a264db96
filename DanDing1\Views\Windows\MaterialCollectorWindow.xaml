<Window
    x:Class="DanDing1.Views.Windows.MaterialCollectorWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:DanDing1.Views.Windows"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="素材收集工具"
    Width="800"
    Height="600"
    ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
    ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    Background="{DynamicResource ApplicationBackgroundBrush}"
    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <Grid Margin="12">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0"
                    Margin="0,0,0,12">
            <StackPanel Orientation="Horizontal"
                        Margin="0,0,0,8">
                <ui:Button
                    x:Name="BindWindow"
                    Content="绑定窗口"
                    Click="BindWindow_Click"
                    Height="32"
                    Width="100"/>
                <TextBlock
                    x:Name="WindowInfo"
                    Margin="12,0,0,0"
                    VerticalAlignment="Center"
                    Text="未绑定窗口"/>
            </StackPanel>

            <StackPanel Orientation="Horizontal">
                <ui:Button
                    x:Name="CaptureButton"
                    Content="截图"
                    Click="CaptureButton_Click"
                    IsEnabled="False"
                    Height="32"
                    Width="100"/>
                <ui:Button
                    x:Name="SaveButton"
                    Content="保存"
                    Click="SaveButton_Click"
                    IsEnabled="False"
                    Margin="8,0,0,0"
                    Height="32"
                    Width="100"/>
                <ui:Button
                    x:Name="DeleteButton"
                    Content="删除"
                    Click="DeleteButton_Click"
                    IsEnabled="False"
                    Margin="8,0,0,0"
                    Height="32"
                    Width="100"/>
                <CheckBox
                    x:Name="AutoCapture"
                    Content="自动截图(每秒)"
                    Margin="8,0,0,0"
                    IsEnabled="False"
                    VerticalAlignment="Center"
                    Checked="AutoCapture_CheckedChanged"
                    Unchecked="AutoCapture_CheckedChanged"/>
            </StackPanel>
        </StackPanel>

        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <ListBox
                x:Name="ImageList"
                Grid.Column="0"
                Margin="0,0,12,0"
                SelectionChanged="ImageList_SelectionChanged"/>

            <Border
                Grid.Column="1"
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                CornerRadius="4">
                <Image
                    x:Name="PreviewImage"
                    Stretch="Uniform"
                    Margin="4"/>
            </Border>
        </Grid>
    </Grid>
</Window> 