﻿using DanDing1.Models;
using ScriptEngine.Model;
using System.Collections.ObjectModel;
using XHelper.Models;
using XHelper;
using DanDing1.Views.UserControls;
using Newtonsoft.Json.Linq;

namespace DanDing1.ViewModels.Base
{
    /// <summary>
    /// MVVM属性
    /// </summary>
    public partial class GameViewBaseModel
    {
        /// <summary>
        /// 添加任务控件
        /// </summary>
        public AddTaskControl? AddTaskControl;

        private ObservableCollection<TaskConfigsModel.Configs> _GameTaskLists;

        public ObservableCollection<TaskConfigsModel.Configs> GameTaskLists
        {
            get
            {
                if (AddTaskControl is null)
                {
                    return [];
                }
                return AddTaskControl.ViewModel.GameTaskLists;
            }
            set
            {
                if (AddTaskControl is not null) AddTaskControl.ViewModel.GameTaskLists = value;
                SetProperty(ref _GameTaskLists, value);
            }
        }

        /// <summary>
        /// 任务列表索引
        /// </summary>
        [ObservableProperty]
        private int _GameTaskListsIndex;

        [ObservableProperty]
        private InfoBarModel _infoBar = new();

        /// <summary>
        /// 录制开关
        /// </summary>
        [ObservableProperty]
        private bool _isRecord = false;

        /// <summary>
        /// 是否需要单独适配体服
        /// </summary>
        [ObservableProperty]
        private bool _isTifu = false;

        /// <summary>
        /// 结束后关闭模拟器
        /// </summary>
        [ObservableProperty]
        private bool _endCloseGame = false;

        [ObservableProperty]
        private string _LastShowTime = "";

        [ObservableProperty]
        private string _RunningTaskName = "未运行";

        /// <summary>
        /// 发送验证码开停控制
        /// </summary>
        [ObservableProperty]
        private string _LoginStatus = "未登录状态";

        /// <summary>
        /// 任务列表循环次数
        /// </summary>
        [ObservableProperty]
        private string _LoopCount = "1";

        /// <summary>
        /// 是否开启通知
        /// </summary>
        [ObservableProperty]
        private bool _Notice_IsChecked;

        /// <summary>
        /// 通知方式
        /// </summary>
        [ObservableProperty]
        private List<string> _Notice_Lists = ["邮件", "自定义"];

        /// <summary>
        /// 通知方式选中项目
        /// </summary>
        [ObservableProperty]
        private string _Notice_SelectItem = "邮件";

        /// <summary>
        /// 模拟器分辨率
        /// </summary>
        [ObservableProperty]
        private string _SelectDpi = "";

        /// <summary>
        /// 模拟器句柄
        /// </summary>
        [ObservableProperty]
        private string _SelectHwnd = "";

        [ObservableProperty]
        public string _ShowTime = "00:00:00";

        /// <summary>
        /// 变速开关
        /// </summary>
        [ObservableProperty]
        private bool _speedSwitch = false;

        private bool _StartButtonEnabled;

        /// <summary>
        /// 开始任务后控制按钮是否可用
        /// </summary>
        public bool StartButtonEnabled
        {
            get
            {
                if (AddTaskControl is null)
                    return true;
                return AddTaskControl.ViewModel.StartButtonEnabled;
            }
            set
            {
                if (AddTaskControl is not null) AddTaskControl.ViewModel.StartButtonEnabled = value;
                _StartButtonEnabled = value;
                SetProperty(ref _StartButtonEnabled, value);

                // 显式通知UI更新
                OnPropertyChanged(nameof(StartButtonEnabled));
            }
        }
    }
}