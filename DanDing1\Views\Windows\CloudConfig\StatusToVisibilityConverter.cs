using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// 将状态字符串转换为可见性的转换器
    /// </summary>
    public class StatusToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string status || parameter is not string param)
                return Visibility.Collapsed;

            switch (param)
            {
                case "active":
                    // 仅当状态为active时显示
                    return status == "active" ? Visibility.Visible : Visibility.Collapsed;
                
                case "notDeleted":
                    // 当状态不为deleted时显示
                    return status != "deleted" ? Visibility.Visible : Visibility.Collapsed;
                
                case "notExpired":
                    // 当状态不为expired时显示
                    return status != "expired" ? Visibility.Visible : Visibility.Collapsed;
                
                default:
                    return Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 