﻿using DamoControlKit;
using DamoControlKit.Model;
using ScriptEngine;
using ScriptEngine.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace CmdTester
{
    public class main
    {
        public main()
        {
            XLogger.Info("CTL Main Running...");
        }

        /// <summary>
        /// 图片数据集合
        /// </summary>
        private Data_Pics Pics = new Data_Pics();

        /// <summary>
        /// 范围数据集合
        /// </summary>
        private Data_Poss Poss = new Data_Poss();

        /// <summary>
        /// 打包图片、色彩资源
        /// </summary>
        public void dabao()
        {
            while (true)
            {
                Pics = new();
                Poss = new();

                XLogger.Info("请输入版本号（输入 'exit' 退出）：");
                string? ver = Console.ReadLine();

                if (string.IsNullOrEmpty(ver) || ver.To<PERSON>er() == "exit")
                {
                    XLogger.Info("退出打包程序");
                    break;
                }

                string path = $"D:\\DD_Core\\{ver}\\";
                //遍历文件夹中的文件夹
                string[] dirs = Directory.GetDirectories(path, "*", SearchOption.AllDirectories);
                foreach (var _path in dirs)
                {
                    string _class = _path.Split("\\")[^1];//类名
                    XLogger.Info("处理类：{0}", _class);
                    Pack(_path, _class);
                }

                XSerializer.SerializeObjectToJsonFile(Pics, $"{path}\\Pics.json", true);//写到json文件
                XSerializer.SerializeObjectToJsonFile(Poss, $"{path}\\Pos.json", true);//写到json文件
                XLogger.Info("打包完成！打包文件名：'Pics.json'、'Pos.json'\r\n");
                XLogger.Info("开始测试是否能被反序列化存储：");

                Data_Pics pics = XSerializer.DeserializeJsonFileToObject<Data_Pics>($"{path}\\Pics.json", true);//解析json文件
                XLogger.Info("pics对象Count数量：{0}", pics.Count);

                Data_Poss poss = XSerializer.DeserializeJsonFileToObject<Data_Poss>($"{path}\\Pos.json", true);//解析json文件
                XLogger.Info("poss对象Count数量：{0}", poss.Count);

                XLogger.Info("打包功能执行完毕 End.....");
            }
        }

        private void Pack(string path, string _class)
        {
            // 特殊处理Remedy类
            if (_class == "Remedy")
            {
                XLogger.Info("检测到Remedy类，将重写Pos.txt文件");
                List<string> bmpFiles = new List<string>();
                
                // 先收集所有bmp文件名
                string[] files = Directory.GetFiles(path, "*.bmp", SearchOption.AllDirectories);
                foreach (var file in files)
                {
                    string name = file.Split("\\")[^1];
                    string nameWithoutExt = name.Split(".")[0]; // 获取不带扩展名的文件名
                    bmpFiles.Add(nameWithoutExt);
                    
                    // 添加图片到Pics集合
                    Pics.Add(new Data_Pic()
                    {
                        Name = name,
                        Class = _class,
                        ImgBytes = File.ReadAllBytes(file)
                    });
                }
                
                // 生成Pos.txt文件内容
                List<string> posLines = new List<string>();
                foreach (var bmpName in bmpFiles)
                {
                    posLines.Add($"{bmpName}|0,0,0,0");
                    
                    // 同时添加到Poss集合
                    Poss.Add(new Data_Pos()
                    {
                        Class = _class,
                        Name = bmpName,
                        FindPos = "0,0,0,0",
                        ClickPos = ""
                    });
                }
                
                // 写入Pos.txt文件
                string posTxtPath = Path.Combine(path, "Pos.txt");
                File.WriteAllLines(posTxtPath, posLines);
                XLogger.Info("已重写Remedy类的Pos.txt文件，共处理{0}个bmp文件", bmpFiles.Count);
                
                return; // 跳过默认处理
            }
            
            // 默认处理逻辑
            string[] allFiles = Directory.GetFiles(path, "*", SearchOption.AllDirectories);
            foreach (var file in allFiles)
            {
                XLogger.Info("处理文件：{0}", file);
                string name = file.Split("\\")[^1];
                string ext = name.Split(".")[^1];
                if (ext == "bmp")
                {
                    //图片
                    Pics.Add(new Data_Pic()
                    {
                        Name = name,
                        Class = _class,
                        ImgBytes = File.ReadAllBytes(file)
                    });
                }
                else if (name == "Pos.txt")
                {
                    //逐行遍历Pos.txt 利用'|'分割文本 位置
                    string[] lines = File.ReadAllLines(file);
                    foreach (var line in lines)
                    {
                        if (line == "")
                            continue;

                        string[] pos = line.Split("|");
                        if (pos.Length == 2)
                            Poss.Add(new Data_Pos()
                            {
                                Class = _class,
                                Name = pos[0],
                                FindPos = pos[1],
                                ClickPos = ""
                            });
                        else
                            Poss.Add(new Data_Pos()
                            {
                                Class = _class,
                                Name = pos[0],
                                FindPos = pos[1],
                                ClickPos = pos[2] == "GS" ? "GS" : pos[2]
                            });
                    }
                }
            }
        }

        private const int HWND = 132402;

        /// <summary>
        /// 调试1脚本流程测试
        /// </summary>
        public async void Debug1()
        {
            DmSettings.Init(true);

            //开始配置任务清单
            TaskConfigsModel tcm = new();
            tcm.Add("调试1", 0);

            DDBuilder dBuilder = new();
            dBuilder.SetSimulator("mumu", HWND)
                .SetBindSetting(BindModels.GetBindModel("mumu", 1))
                .SetPicsVer("1.0")
                .SetTaskList(tcm)
                .SetGameSettings(new() { XuanShang = true });

        Recheck:
            //检查句柄
            if (!dBuilder.Check(""))
            {
                XLogger.Info("句柄已经失效，请重新输入：");
                int it = int.Parse(Console.ReadLine() ?? "0");
                dBuilder.SetSimulator("mumu", it);
                goto Recheck;
            }

            DDScript dScript = new();
            dScript.TaskEnded = () =>
            {
                XLogger.Debug("dScript.TaskEnded");
            };
            dScript.Start(dBuilder);
            XLogger.Debug("流程结束！");
        }

        /// <summary>
        /// AES加密
        /// </summary>
        public void aes_jiami()
        {
            string str = "9361276054124d2d4942e282009ec26100e8400e";
            string key = "blog.x-tools.top";
            string iv = "file.x-tools.top";
            string result = AesEncryption.Encrypt(str, key, iv);
            XLogger.Info("AES加密结果：{0}", result);
        }

        /// <summary>
        /// AES解密
        /// </summary>
        public void aes_jiemi()
        {
            string str = "47StoAlMvvRJVBltudNxoD0PV8Xs3JEhamHo9qmmFeDDPcWdP5Q7rqkcOkibdcSq";
            string key = "blog.x-tools.top";
            string iv = "file.x-tools.top";
            string result = AesEncryption.Decrypt(str, key, iv);
            XLogger.Info("AES解密结果：{0}", result);
        }
    }
}