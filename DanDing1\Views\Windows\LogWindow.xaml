﻿<Window
    x:Class="DanDing1.Views.Windows.LogWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:DanDing1.Views.Windows"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="{Binding ViewModel.Title_Str}"
    Width="500"
    Height="547"
    d:DataContext="{d:DesignInstance local:LogWindow,
    IsDesignTimeCreatable=False}"
    d:DesignHeight="278"
    d:DesignWidth="500"
    ui:Design.Background="Transparent"
    ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    Background="Transparent"
    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    MouseDown="Window_MouseDown"
    Opacity="1"
    ResizeMode="CanResize"
    ScrollViewer.CanContentScroll="False"
    WindowStyle="None"
    AllowsTransparency="True"
    mc:Ignorable="d">
    <Window.Effect>
        <DropShadowEffect BlurRadius="20"
                          ShadowDepth="1"
                          Direction="315"
                          Color="#88000000" />
    </Window.Effect>
    <WindowChrome.WindowChrome>
        <WindowChrome
            CaptionHeight="0"
            CornerRadius="12"
            GlassFrameThickness="0"
            ResizeBorderThickness="5" />
    </WindowChrome.WindowChrome>
    <Window.Resources>
        <local:MaxWidthConverter x:Key="MaxWidthConverter" />
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <local:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
        <local:StringNotEqualToVisibilityConverter x:Key="StringNotEqualToVisibilityConverter" />
        <Style x:Key="TitleBarButtonStyle"
               TargetType="ui:Button">
            <Setter Property="Width"
                    Value="35" />
            <Setter Property="Height"
                    Value="35" />
            <Setter Property="FontSize"
                    Value="16" />
            <Setter Property="Background"
                    Value="Transparent" />
            <Setter Property="BorderThickness"
                    Value="0" />
            <Setter Property="Padding"
                    Value="8" />
        </Style>
    </Window.Resources>
    <Border CornerRadius="12"
            Background="{DynamicResource ApplicationBackgroundBrush}"
            BorderThickness="1.5"
            BorderBrush="#33000000"
            Margin="0">
        <Border.Effect>
            <DropShadowEffect BlurRadius="12"
                              ShadowDepth="0"
                              Direction="0"
                              Color="#33000000" />
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="45" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Grid Grid.Row="0">
                <StackPanel Margin="16,0,0,0"
                            Orientation="Horizontal"
                            VerticalAlignment="Center">
                    <Grid>
                        <ui:TextBlock
                            x:Name="Title"
                            FontSize="18"
                            FontWeight="SemiBold"
                            Text="{Binding ViewModel.Title_Str}"
                            MouseLeftButtonDown="Title_MouseLeftButtonDown"
                            Visibility="{Binding ElementName=TitleEdit, Path=IsVisible, Converter={StaticResource InverseBooleanToVisibilityConverter}}" />
                        <TextBox
                            x:Name="TitleEdit"
                            FontSize="18"
                            FontWeight="SemiBold"
                            Text="{Binding ViewModel.Title_Str, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                            Visibility="Collapsed"
                            KeyDown="TitleEdit_KeyDown"
                            LostFocus="TitleEdit_LostFocus"
                            Background="Transparent"
                            BorderThickness="0"
                            Padding="0"
                            VerticalContentAlignment="Center" />
                    </Grid>
                </StackPanel>
                <ui:Button
                    x:Name="close"
                    Style="{StaticResource TitleBarButtonStyle}"
                    HorizontalAlignment="Right"
                    Click="close_Click"
                    Content="✕"
                    Margin="0,0,5,0" />
            </Grid>

            <!-- 窗口置顶控制 -->
            <Border Grid.Row="1"
                    Margin="8,0,8,8"
                    Background="{DynamicResource ControlFillColorDefaultBrush}"
                    CornerRadius="6"
                    Padding="12">
                <StackPanel Margin="-10 0 0 0" Orientation="Horizontal">
                    <CheckBox x:Name="Zd" Content="窗口置顶" Checked="Zd_Checked" Unchecked="Zd_Checked" />
                    <ui:Button
                        Margin="-15 0 0 0"
                        x:Name="AdhesionButton"
                        Content="吸附窗口"
                        Click="AdhesionButton_Click" />
                    <ui:Button Visibility="{Binding ViewModel.GameName, Converter={StaticResource StringNotEqualToVisibilityConverter}, ConverterParameter=全部}"
                               Margin="5 0 0 0" Padding="3" x:Name="TasksManager" Click="TasksManager_Click" MouseRightButtonUp="TasksManager_MouseRightButtonUp" ToolTip="打开任务方案管理">
                        <ui:SymbolIcon FontSize="21" Symbol="TaskListSquareLtr20" />
                    </ui:Button>
                </StackPanel>
            </Border>

            <!-- 游戏快捷控制 -->
            <Grid Grid.Row="1"
                  Margin="16,0,16,8" HorizontalAlignment="Right"
                  Visibility="{Binding ViewModel.GameName, Converter={StaticResource StringNotEqualToVisibilityConverter}, ConverterParameter=全部}">
                <StackPanel Orientation="Horizontal">
                    <ui:TextBlock
                        Margin="0 0 10 0 "
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        FontSize="18"
                        Text="{Binding ViewModel.ShowTime}" />
                    <ui:Button Margin="0 0 10 0" x:Name="GameControl_Button" Padding="3" Click="GameControl_Button_Click">
                        <ui:SymbolIcon Foreground="Green"  x:Name="GameControl_Icon" FontSize="23" Symbol="Play24" />
                    </ui:Button>
                </StackPanel>
            </Grid>

            <!-- 日志内容 -->
            <ScrollViewer
                Grid.Row="2"
                Margin="16,0,16,16"
                VerticalScrollBarVisibility="Auto">
                <ItemsControl x:Name="itemsControl"
                              ItemsSource="{Binding ViewModel.Log}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Margin="0,2"
                                    Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                    CornerRadius="6"
                                    Padding="12,4">
                                <StackPanel Orientation="Horizontal">
                                    <ui:TextBlock
                                        VerticalAlignment="Top"
                                        FontSize="14"
                                        Foreground="{Binding Color}"
                                        Text="{Binding Tip}"
                                        FontWeight="SemiBold" />
                                    <ui:TextBlock
                                        MaxWidth="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource MaxWidthConverter}}"
                                        Margin="8,0,0,0"
                                        VerticalAlignment="Center"
                                        FontSize="14"
                                        Foreground="{Binding Color}"
                                        Text="{Binding Text}"
                                        FontWeight="Medium"
                                        TextWrapping="Wrap" />
                                </StackPanel>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Grid>
    </Border>
</Window>