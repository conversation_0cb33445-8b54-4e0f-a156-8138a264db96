using System;
using System.Collections.Generic;
using System.Linq;
using XHelper.DanDingNet;
using XHelper;

namespace DanDing1.Services.Notification
{
    /// <summary>
    /// 通知发送器工厂，负责创建不同类型的通知发送器
    /// </summary>
    public class NotificationSenderFactory
    {
        private readonly Dictionary<string, INotificationSender> _senders = new Dictionary<string, INotificationSender>();
        private readonly Dictionary<string, string> _displayNameToType = new Dictionary<string, string>();

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="userService">用户服务实例</param>
        public NotificationSenderFactory(UserService userService)
        {
            // 注册默认的通知发送器
            RegisterSender(new EmailNotificationSender(userService));
            RegisterSender(new AppNotificationSender(userService));

            // 获取微信推送配置
            string wxPushAppToken = GetWxPushAppToken();

            // 如果微信推送配置有效，则注册WxPush发送器
            if (!string.IsNullOrEmpty(wxPushAppToken))
            {
                try
                {
                    var wxPushSender = new WxPushNotificationSender(wxPushAppToken);
                    RegisterSender(wxPushSender);
                    //XLogger.Info("微信推送通知发送器注册成功");
                }
                catch (Exception ex)
                {
                    XLogger.Error($"注册微信推送通知发送器失败: {ex.Message}");
                }
            }
            else
            {
                XLogger.Warn("未找到有效的微信推送AppToken，微信推送通知功能将不可用");
            }

            // 注册Pushplus发送器
            try
            {
                var pushplusSender = new PushplusNotificationSender();
                RegisterSender(pushplusSender);
                //XLogger.Info("Pushplus推送通知发送器注册成功");
            }
            catch (Exception ex)
            {
                XLogger.Error($"注册Pushplus推送通知发送器失败: {ex.Message}");
            }

            // 注册喵提醒发送器
            try
            {
                var miaotixingSender = new MiaotixingNotificationSender();
                RegisterSender(miaotixingSender);
                //XLogger.Info("喵提醒通知发送器注册成功");
            }
            catch (Exception ex)
            {
                XLogger.Error($"注册喵提醒通知发送器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取微信推送AppToken
        /// </summary>
        /// <returns>AppToken</returns>
        private string GetWxPushAppToken()
        {
            return "AT_3VCBuZy9seMzN6bxolf8NA6mm8BsvelW";
        }

        /// <summary>
        /// 注册通知发送器
        /// </summary>
        /// <param name="sender">通知发送器实例</param>
        public void RegisterSender(INotificationSender sender)
        {
            if (sender != null)
            {
                _senders[sender.NoticeType] = sender;
                _displayNameToType[sender.DisplayName] = sender.NoticeType;
            }
        }

        /// <summary>
        /// 获取所有已注册的通知发送器
        /// </summary>
        /// <returns>通知发送器集合</returns>
        public IEnumerable<INotificationSender> GetAllSenders()
        {
            return _senders.Values;
        }

        /// <summary>
        /// 获取所有通知类型的显示名称
        /// </summary>
        /// <returns>显示名称集合</returns>
        public IEnumerable<string> GetAllDisplayNames()
        {
            return _displayNameToType.Keys;
        }

        /// <summary>
        /// 根据类型获取通知发送器
        /// </summary>
        /// <param name="type">通知类型标识符</param>
        /// <returns>通知发送器</returns>
        public INotificationSender GetSender(string type)
        {
            if (string.IsNullOrEmpty(type))
            {
                // 默认使用邮件通知
                return _senders.TryGetValue("email", out var defaultSender)
                    ? defaultSender
                    : _senders.Values.FirstOrDefault();
            }

            return _senders.TryGetValue(type, out var sender) ? sender : null;
        }

        /// <summary>
        /// 根据显示名称获取通知发送器
        /// </summary>
        /// <param name="displayName">显示名称</param>
        /// <returns>通知发送器</returns>
        public INotificationSender GetSenderByDisplayName(string displayName)
        {
            if (string.IsNullOrEmpty(displayName))
            {
                // 默认使用邮件通知
                return _senders.TryGetValue("email", out var defaultSender)
                    ? defaultSender
                    : _senders.Values.FirstOrDefault();
            }

            if (_displayNameToType.TryGetValue(displayName, out var type))
            {
                return GetSender(type);
            }

            return null;
        }

        /// <summary>
        /// 根据显示名称获取通知类型标识符
        /// </summary>
        /// <param name="displayName">显示名称</param>
        /// <returns>通知类型标识符</returns>
        public string GetTypeByDisplayName(string displayName)
        {
            return _displayNameToType.TryGetValue(displayName, out var type) ? type : "email";
        }
    }
}