using System.Net;
using System.Security.Authentication;
using System.Net.Security;

namespace XHelper.DanDingNet
{
    /// <summary>
    /// 蛋定网络服务类，提供与蛋定服务器交互的功能
    /// </summary>
    public class DanDingNet : IDisposable
    {
        // 是否启用调试日志
        public static bool EnableDebugLog { get; set; } = false;

        // 版本号
        private readonly string _version;

        // 机器码
        private readonly string _macCode;

        // HTTP客户端
        private readonly HttpClient _client;

        // 服务器主机地址
        private string _host;

        // 取消令牌源
        private readonly CancellationTokenSource _cts;

        /// <summary>
        /// 认证服务
        /// </summary>
        public AuthService Auth { get; }

        /// <summary>
        /// 用户服务
        /// </summary>
        public UserService User { get; }

        /// <summary>
        /// 活动服务
        /// </summary>
        public ActivityService Activity { get; }

        /// <summary>
        /// 系统服务
        /// </summary>
        public SystemService System { get; }

        /// <summary>
        /// 配置共享服务
        /// </summary>
        public ConfigService Config { get; }

        /// <summary>
        /// 初始化蛋定网络服务
        /// </summary>
        /// <param name="version">版本号</param>
        public DanDingNet(string version)
        {
            _version = version;
            _macCode = MachineIdentifier.GetMachineCode();
            if (string.IsNullOrEmpty(_macCode))
            {
                throw new Exception("程序获取机器码失败，请尝试使用管理员身份运行！");
            }

            // 配置全局网络设置
            NetworkConfig.ConfigureGlobalNetworkSettings();

            // 从配置文件加载初始host设置
            string initialHost = XConfig.LoadValueFromFile<string>("ServerHost") ?? "港式接口1";
            if (EnableDebugLog) XLogger.Debug($"初始host: {initialHost}");
            _host = DDApi.ServerHost.TryGetValue(initialHost, out string? hostUrl)
                ? hostUrl
                : "https://api.danding.vip/";

            _cts = new CancellationTokenSource();

            // 创建 HttpClient
            var handler = NetworkConfig.CreateClientHandler();
            _client = new HttpClient(handler)
            {
                Timeout = TimeSpan.FromSeconds(30)
            };

            // 设置默认请求头
            _client.DefaultRequestHeaders.Add("User-Agent", $"DanDing-Client/V{_version}");
            _client.DefaultRequestHeaders.Add("X-Machine-ID", _macCode);
            _client.DefaultRequestHeaders.Add("Accept", "application/json");

            // 初始化各个服务
            Auth = new AuthService(_client, _macCode, _host, _version);
            User = new UserService(_client, _macCode, _host, _version);
            Activity = new ActivityService(_client, _macCode, _host, _version);
            System = new SystemService(_client, _macCode, _host, _version);
            Config = new ConfigService(_client, _macCode, _host, _version);
        }

        /// <summary>
        /// 初始化网络连接
        /// </summary>
        /// <param name="hostname">服务器主机名，默认为"港式接口1"</param>
        /// <returns>初始化任务</returns>
        public async Task InitializeAsync(string hostname = "港式接口1")
        {
            if (EnableDebugLog) XLogger.Debug($"InitializeAsync，{hostname}");
            const int maxRetries = 3; // 增加最大重试次数
            const int maxNetworkCheckRetries = 3;
            Exception? lastException = null;

            // 网络连接预检，增加重试
            bool networkValid = false;
            for (int i = 0; i < maxNetworkCheckRetries; i++)
            {
                try
                {
                    networkValid = await NetworkConfig.ValidateNetworkConnectivity(_host);
                    if (networkValid) break;

                    if (EnableDebugLog) XLogger.Debug($"网络连接检查失败，{(i < maxNetworkCheckRetries - 1 ? "正在重试..." : "已达到最大重试次数")}");
                    await Task.Delay(1000 * (i + 1)); // 递增延迟
                }
                catch (Exception ex)
                {
                    XLogger.Error($"网络检查异常: {ex.Message}");
                    if (i == maxNetworkCheckRetries - 1) throw;
                }
            }

            if (!networkValid)
            {
                throw new Exception("网络连接检查失败，请确保网络正常并且可以访问服务器");
            }

            // 添加随机抖动的延迟基数
            var random = new Random();

            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(_cts.Token);
                    timeoutCts.CancelAfter(TimeSpan.FromSeconds(45));

                    bool success = await System.InitializeAsync(hostname).ConfigureAwait(false);
                    if (!success)
                    {
                        throw new Exception("无法连接到任何服务器节点，请检查网络连接！");
                    }

                    // 获取系统服务确定的Host
                    string newHost = System.GetCurrentHost();

                    // 只有当新Host与当前Host不同时才进行验证
                    if (newHost != _host && !await NetworkConfig.ValidateNetworkConnectivity(newHost))
                    {
                        throw new Exception($"新服务器({newHost})连接验证失败");
                    }

                    // 更新所有服务的Host
                    _host = newHost;
                    //再保存到本地
                    XConfig.SaveValueToFile("ServerHost", DDApi.ServerHost.FirstOrDefault(x => x.Value == newHost).Key);
                    if (EnableDebugLog) XLogger.Debug($"更新host: {DDApi.ServerHost.FirstOrDefault(x => x.Value == newHost).Key}");
                    Auth.UpdateHost(newHost);
                    User.UpdateHost(newHost);
                    Activity.UpdateHost(newHost);
                    System.UpdateHost(newHost);
                    Config.UpdateHost(newHost);

                    var info = await System.GetInfoAsync();
                    if (string.IsNullOrEmpty(info.AppInfo))
                    {
                        throw new Exception("获取系统信息失败！");
                    }

                    // 检查响应内容是否包含非法字符
                    if (info.AppInfo[0] == 0x1F)
                    {
                        throw new Exception("接收到压缩数据，请检查响应解压配置");
                    }

                    if (EnableDebugLog) XLogger.Debug($"网络初始化成功，当前服务器：{newHost}");
                    return;
                }
                catch (OperationCanceledException) when (_cts.Token.IsCancellationRequested)
                {
                    throw new Exception("初始化被用户取消");
                }
                catch (HttpRequestException httpEx)
                {
                    lastException = httpEx;
                    XLogger.Error($"HTTP请求失败: {httpEx.Message}");
                    if (httpEx.InnerException != null)
                    {
                        XLogger.Error($"内部错误: {httpEx.InnerException.Message}");
                    }
                }
                catch (System.Text.Json.JsonException jsonEx)
                {
                    lastException = jsonEx;
                    XLogger.Error($"JSON解析失败: {jsonEx.Message}");
                    if (jsonEx.Message.Contains("0x1F"))
                    {
                        XLogger.Error("疑似收到未解压的GZIP数据");
                    }
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    XLogger.Error($"初始化失败: {ex.Message}");
                }

                if (i < maxRetries - 1)
                {
                    // 使用指数退避 + 随机抖动
                    int baseDelay = (int)Math.Pow(2, i) * 1000;
                    int jitter = random.Next(-500, 500);
                    int delayMs = Math.Max(1000, baseDelay + jitter);

                    if (EnableDebugLog) XLogger.Debug($"等待 {delayMs / 1000.0:F1} 秒后进行第 {i + 2} 次重试");
                    await Task.Delay(delayMs);

                    // 在重试前切换到其他可用节点
                    var availableHosts = DDApi.ServerHost.Values.ToList();
                    if (availableHosts.Count > 1)
                    {
                        var currentIndex = availableHosts.IndexOf(_host);
                        var nextIndex = (currentIndex + 1) % availableHosts.Count;
                        _host = availableHosts[nextIndex];
                        if (EnableDebugLog) XLogger.Debug($"切换到备用节点: {_host}");
                    }
                }
            }

            var errorMessage = lastException is HttpRequestException httpException
                ? $"HTTP错误 {httpException.StatusCode}: {httpException.Message}"
                : lastException?.Message ?? "未知错误";

            throw new Exception($"连接初始化失败，已重试{maxRetries}次。{errorMessage}", lastException);
        }

        public void Dispose()
        {
            _cts.Cancel();
            _cts.Dispose();
            _client.Dispose();
        }

        /// <summary>
        /// 检查是否存在Cookie文件
        /// </summary>
        /// <returns>如果Cookie文件存在返回true，否则返回false</returns>
        public bool HaveCookie() => File.Exists(".\\runtimes\\cookies.data");

        /// <summary>
        /// 绑定QQ
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="qq">QQ号</param>
        /// <param name="code">验证码</param>
        /// <returns>绑定QQ结果</returns>
        public async Task<XHelper.Models.Response_BindQQData?> BindQQAsync(string username, string qq, string code) =>
            await User.BindQQAsync(username, qq, code);

        /// <summary>
        /// 存储用户临时日志到服务器
        /// </summary>
        /// <param name="logContent">日志内容</param>
        /// <returns>存储日志结果</returns>
        public async Task<XHelper.Models.Response_StoreLogData?> StoreLogAsync(string logContent) =>
            await User.StoreLogAsync(logContent);

        /// <summary>
        /// 设置WebToken
        /// </summary>
        /// <param name="webToken">用户自定义WebToken，长度8-20个字符</param>
        /// <returns>设置WebToken的结果</returns>
        public async Task<XHelper.Models.Response_SetWebTokenData?> SetWebTokenAsync(string webToken) =>
            await User.SetWebTokenAsync(webToken);

        /// <summary>
        /// 获取WebToken状态
        /// </summary>
        /// <returns>WebToken状态信息</returns>
        public async Task<XHelper.Models.Response_WebTokenStatusData?> GetWebTokenStatusAsync() =>
            await User.GetWebTokenStatusAsync();
    }
}