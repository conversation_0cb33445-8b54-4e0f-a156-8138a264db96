using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DanDing1.Models.Ws_Models;
using XHelper;

namespace DanDing1.Services
{
    /// <summary>
    /// 脚本状态同步服务，处理服务器的脚本状态同步请求
    /// </summary>
    public class ScriptSyncService
    {
        private readonly ScriptStatusReporterService _statusReporter;
        private const string SYNC_TAG = "ScriptSyncService:";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="statusReporter">脚本状态上报服务</param>
        public ScriptSyncService(ScriptStatusReporterService statusReporter)
        {
            _statusReporter = statusReporter ?? throw new ArgumentNullException(nameof(statusReporter));

            // 注册处理器
            RegisterMessageHandlers();

            XLogger.OnlyWrite($"{SYNC_TAG} 已初始化，已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器");
        }

        /// <summary>
        /// 注册WebSocket消息处理器
        /// </summary>
        public void RegisterMessageHandlers()
        {
            // 先取消注册，避免重复
            XWebsocket.UnregisterMessageHandler("TRIGGER_SCRIPT_STATUS_SYNC_COMMAND");

            // 重新注册处理器
            XWebsocket.RegisterMessageHandler("TRIGGER_SCRIPT_STATUS_SYNC_COMMAND", HandleScriptStatusSyncCommand);

            XLogger.OnlyWrite($"{SYNC_TAG} 已注册TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息处理器");
        }

        /// <summary>
        /// 处理TRIGGER_SCRIPT_STATUS_SYNC_COMMAND消息
        /// </summary>
        /// <param name="message">WebSocket消息内容</param>
        private void HandleScriptStatusSyncCommand(string message)
        {
            try
            {
                XLogger.OnlyWrite($"{SYNC_TAG} 收到脚本状态同步请求: {message}");

                // 解析请求消息
                var request = JsonConvert.DeserializeObject<ScriptStatusSyncCommand>(message);

                // 收集所有脚本状态
                var scriptStatuses = _statusReporter.GetAllScriptStatuses();

                // 构造响应消息
                var response = new ScriptStatusSyncBatchUpdate
                {
                    type = "SCRIPT_STATUS_SYNC_BATCH_UPDATE",
                    payload = new ScriptStatusSyncBatchUpdatePayload
                    {
                        // 如果请求中包含requestId，则在响应中也包含
                        requestId = request?.requestId,
                        statuses = scriptStatuses
                    }
                };

                // 异步发送响应
                _ = SendScriptStatusBatchResponseAsync(response);
            }
            catch (Exception ex)
            {
                XLogger.Error($"{SYNC_TAG} 处理脚本状态同步请求时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送脚本状态批量响应
        /// </summary>
        /// <param name="response">响应对象</param>
        private async Task SendScriptStatusBatchResponseAsync(ScriptStatusSyncBatchUpdate response)
        {
            try
            {
                bool success = await XWebsocket.SendObjectAsync(response);
                if (success)
                {
                    XLogger.OnlyWrite($"{SYNC_TAG} 已成功发送脚本状态同步响应: 状态数={response.payload.statuses.Count}");
                }
                else
                {
                    XLogger.Error($"{SYNC_TAG} 发送脚本状态同步响应失败");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"{SYNC_TAG} 发送脚本状态同步响应时出错: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 脚本状态同步命令
    /// </summary>
    public class ScriptStatusSyncCommand
    {
        public string type { get; set; } = "TRIGGER_SCRIPT_STATUS_SYNC_COMMAND";
        public string requestId { get; set; }
    }

    /// <summary>
    /// 脚本状态批量更新
    /// </summary>
    public class ScriptStatusSyncBatchUpdate
    {
        public string type { get; set; } = "SCRIPT_STATUS_SYNC_BATCH_UPDATE";
        public ScriptStatusSyncBatchUpdatePayload payload { get; set; }
    }

    /// <summary>
    /// 脚本状态批量更新载荷
    /// </summary>
    public class ScriptStatusSyncBatchUpdatePayload
    {
        public string requestId { get; set; }
        public List<ScriptStatusData> statuses { get; set; } = new List<ScriptStatusData>();
    }
}