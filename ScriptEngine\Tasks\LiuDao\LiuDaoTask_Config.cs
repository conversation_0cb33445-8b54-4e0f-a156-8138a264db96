using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ScriptEngine.Model;

namespace ScriptEngine.Tasks.LiuDao
{
    /// <summary>
    /// 六道配置
    /// </summary>
    internal class LiuDaoTask_Config : BaseTaskConfig
    {
        private readonly TaskConfigsModel.Configs configs;
        public static string LiuDaoTask_Config_Name = "六道";

        public LiuDaoTask_Config(TaskConfigsModel.Configs configs)
        {
            this.configs = configs;
            Ncount = configs.Count;
            BiaoJi = bool.Parse(configs.Others.TryGetValue("Biaoji", out string? value1) ? value1 : "False");//是否标记
            XingZhiDao_Damo = bool.Parse(configs.Others.TryGetValue("XingZhiDao_Damo", out string? value2) ? value2 : "True");//星之岛是否打达摩
            ShuangBeiJiaCheng = bool.Parse(configs.Others.TryGetValue("ShuangBeiJiaCheng", out string? value3) ? value3 : "True");//是否使用双倍加成(极)
            ShuangBeiJiaCheng_Buy = bool.Parse(configs.Others.TryGetValue("ShuangBeiJiaCheng_Buy", out string? value4) ? value4 : "False");//双倍加成不足时，是否购买双倍加成
            HunDunBuKaiXiang = bool.Parse(configs.Others.TryGetValue("HunDunBuKaiXiang", out string? value6) ? value6 : "False");//混沌不开宝箱
            if (configs.Others.TryGetValue("Extra_Skill", out string? value5))//额外选择技能
            {
                Extra_Skill = value5 ?? "";
                var extraSkills = value5.Split('|');
                foreach (var skill in extraSkills)
                {
                    if (!string.IsNullOrEmpty(skill))
                    {
                        Buff_Priority.Add(skill);
                    }
                }
            }
        }
        /// <summary>
        /// 额外选择技能
        /// </summary>
        public string Extra_Skill = "";

        /// <summary>
        /// 混沌不开宝箱
        /// </summary>
        public bool HunDunBuKaiXiang = false;

        /// <summary>
        /// 双倍加成不足时，是否购买双倍加成
        /// </summary>
        public bool ShuangBeiJiaCheng_Buy = false;

        /// <summary>
        /// 是否使用双倍加成
        /// </summary>
        public bool ShuangBeiJiaCheng = true;

        /// <summary>
        /// 是否可以刷新选择技能
        /// </summary>
        public bool ShuaXingJiNeng => ZhanDou_Count > 5;

        /// <summary>
        /// 已经战斗次数
        /// </summary>
        public int ZhanDou_Count = 0;

        /// <summary>
        /// 岛屿选择优先级
        /// </summary>
        public List<string> DaoYu_Priority = ["星之屿", "鏖战", "神秘", "宁息", "混沌"];

        /// <summary>
        /// Buff优先级
        /// </summary>
        public List<string> Buff_Priority = ["柔风抱暖", "暴虐"];//, "细雨化屏"

        /// <summary>
        /// 星之岛打达摩
        /// </summary>
        public bool XingZhiDao_Damo = true;

        public override string ToString()
        {
            return $"六道配置-总次数:{Ncount}-标记:{BiaoJi}-星之岛达摩:{XingZhiDao_Damo}-双倍加成:{ShuangBeiJiaCheng}-购买双倍:{ShuangBeiJiaCheng_Buy}-混沌不开宝箱:{HunDunBuKaiXiang}-战斗次数:{ZhanDou_Count}";
        }

        /// <summary>
        /// 重置战斗次数
        /// </summary>
        public void Reset()
        {
            ZhanDou_Count = 0;
        }
    }
}