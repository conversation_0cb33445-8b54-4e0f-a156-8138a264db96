using DanDing1.Models.Super;
using ScriptEngine.MuMu;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DanDing1.Helpers
{
    /// <summary>
    /// 模拟器工具类 - 处理模拟器相关操作的通用方法
    /// </summary>
    public static class MumuUtils
    {
        /// <summary>
        /// 尝试解析十六进制格式的窗口句柄
        /// </summary>
        /// <param name="hexString">十六进制字符串</param>
        /// <param name="handle">解析后的句柄值</param>
        /// <returns>解析是否成功</returns>
        public static bool TryParseHexHandle(string hexString, out int handle)
        {
            handle = 0;
            if (string.IsNullOrEmpty(hexString))
                return false;

            return int.TryParse(hexString, System.Globalization.NumberStyles.HexNumber, null, out handle);
        }

        /// <summary>
        /// 查找匹配的模拟器实例
        /// </summary>
        /// <param name="mumuInstances">模拟器实例集合</param>
        /// <param name="game">游戏数据模型</param>
        /// <param name="logAction">日志记录委托，可为null</param>
        /// <returns>匹配实例的索引和实例，如果未找到则返回null</returns>
        public static KeyValuePair<string, MuMuInstance>? FindMatchingMumuInstance(
            Dictionary<string, MuMuInstance> mumuInstances,
            SuperMultiGame_DataModel game,
            Action<string> logAction = null)
        {
            if (mumuInstances == null || mumuInstances.Count == 0 || game == null)
                return null;

            string indexKey = string.Empty;
            bool isZeroHandle = game.MumuHandle == 0;

            // 记录日志的辅助方法
            void Log(string message)
            {
                logAction?.Invoke(message);
            }

            // 获取句柄的十六进制表示
            string hexHandle = game.MumuHandle.ToString("X").PadLeft(8, '0');

            // 如果句柄不为0，尝试通过MainWnd匹配
            if (!isZeroHandle)
            {
                // 方法1: 通过MainWnd匹配（十进制值）
                foreach (var instance in mumuInstances)
                {
                    if (!string.IsNullOrEmpty(instance.Value.MainWnd))
                    {
                        if (TryParseHexHandle(instance.Value.MainWnd, out int mainWnd) &&
                            mainWnd == game.MumuHandle)
                        {
                            indexKey = instance.Key;
                            Log($"通过十进制MainWnd匹配成功: {mainWnd} == {game.MumuHandle}");
                            break;
                        }
                    }
                }

                // 方法2: 如果方法1失败，尝试通过十六进制字符串匹配
                if (string.IsNullOrEmpty(indexKey))
                {
                    foreach (var instance in mumuInstances)
                    {
                        if (instance.Value.MainWnd == hexHandle)
                        {
                            indexKey = instance.Key;
                            Log($"通过十六进制字符串MainWnd匹配成功: {instance.Value.MainWnd} == {hexHandle}");
                            break;
                        }
                    }
                }
            }

            // 方法3: 通过MumuInstance的索引匹配
            if (string.IsNullOrEmpty(indexKey) && game.MumuInstance != null &&
                !string.IsNullOrEmpty(game.MumuInstance.Index))
            {
                // 直接尝试使用原始索引
                if (mumuInstances.ContainsKey(game.MumuInstance.Index))
                {
                    indexKey = game.MumuInstance.Index;
                    Log($"通过原始索引匹配成功: {indexKey}");
                }
                // 如果原始索引是数字且没有直接匹配，尝试数字和字符串之间的转换
                else if (int.TryParse(game.MumuInstance.Index, out int numericIndex))
                {
                    // 尝试将数字索引转为字符串形式
                    string stringIndex = numericIndex.ToString();
                    if (mumuInstances.ContainsKey(stringIndex))
                    {
                        indexKey = stringIndex;
                        Log($"通过数字索引匹配成功(数字转字符串): {game.MumuInstance.Index} -> {stringIndex}");
                    }
                }
                // 特殊处理: 如果保存的索引是"0"但当前实例只有一个且索引可能不是"0"
                else if (game.MumuInstance.Index == "0" && mumuInstances.Count == 1)
                {
                    indexKey = mumuInstances.Keys.First();
                    Log($"特殊处理: 索引为0但只有一个模拟器实例，使用该实例: {indexKey}");
                }
            }

            // 方法4: 通过真实模拟器名称匹配
            if (string.IsNullOrEmpty(indexKey) && !string.IsNullOrEmpty(game.MumuRealName))
            {
                foreach (var instance in mumuInstances)
                {
                    if (!string.IsNullOrEmpty(instance.Value.Name) &&
                        instance.Value.Name == game.MumuRealName)
                    {
                        indexKey = instance.Key;
                        Log($"通过真实模拟器名称匹配成功: {instance.Value.Name} == {game.MumuRealName}");
                        break;
                    }
                }
            }

            // 方法5: 如果只有一个模拟器实例且句柄为0或未找到，直接使用该实例
            if (string.IsNullOrEmpty(indexKey) && (isZeroHandle || game.MumuInstance == null) && mumuInstances.Count == 1)
            {
                indexKey = mumuInstances.Keys.First();
                Log($"只有一个模拟器实例且没有其他匹配条件，直接使用该实例: {indexKey}");
            }

            // 如果找到对应实例，返回该实例
            if (!string.IsNullOrEmpty(indexKey) && mumuInstances.ContainsKey(indexKey))
            {
                return new KeyValuePair<string, MuMuInstance>(indexKey, mumuInstances[indexKey]);
            }

            // 如果都没有匹配到，作为最后的尝试，直接检查是否有索引为"0"的实例
            if (mumuInstances.ContainsKey("0"))
            {
                Log($"未找到匹配实例，但存在索引为'0'的实例，使用该实例作为最后的尝试");
                return new KeyValuePair<string, MuMuInstance>("0", mumuInstances["0"]);
            }
            
            return null;
        }

        /// <summary>
        /// 更新游戏模型的模拟器句柄信息
        /// </summary>
        /// <param name="game">要更新的游戏模型</param>
        /// <param name="instance">模拟器实例</param>
        /// <returns>更新是否成功</returns>
        public static bool UpdateGameModelHandles(SuperMultiGame_DataModel game, MuMuInstance instance)
        {
            if (game == null || instance == null)
                return false;

            bool updated = false;

            // 更新主窗口句柄
            if (!string.IsNullOrEmpty(instance.MainWnd))
            {
                if (TryParseHexHandle(instance.MainWnd, out int mainWnd))
                {
                    game.MumuHandle = mainWnd;
                    updated = true;
                }
            }
            else
            {
                game.MumuHandle = 0;
                updated = true;
            }


            // 更新渲染窗口句柄
            if (!string.IsNullOrEmpty(instance.RenderWnd))
            {
                if (TryParseHexHandle(instance.RenderWnd, out int renderWnd))
                {
                    game.GameHandle = renderWnd;
                    updated = true;
                }
            }
            else
            {
                game.GameHandle = 0;
                updated = true;
            }

            // 更新模拟器实例信息
            game.MumuInstance = instance;
            
            // 更新模拟器真实名称
            if (!string.IsNullOrEmpty(instance.Name))
            {
                game.MumuRealName = instance.Name;
                updated = true;
            }

            return updated;
        }

        /// <summary>
        /// 检查模拟器实例是否正在运行
        /// </summary>
        /// <param name="instance">模拟器实例</param>
        /// <param name="mainWndHandle">输出：主窗口句柄</param>
        /// <param name="renderWndHandle">输出：渲染窗口句柄</param>
        /// <returns>是否正在运行</returns>
        public static bool IsSimulatorRunning(MuMuInstance instance, out int mainWndHandle, out int renderWndHandle)
        {
            mainWndHandle = 0;
            renderWndHandle = 0;
            bool isRunning = false;

            if (instance == null)
                return false;

            // 检查进程相关参数
            bool processStarted = instance.IsProcessStarted;
            bool androidStarted = instance.IsAndroidStarted;
            bool hasPid = instance.Pid.HasValue && instance.Pid.Value > 0;
            bool hasHeadlessPid = instance.HeadlessPid.HasValue && instance.HeadlessPid.Value > 0;
            
            // 检查窗口句柄
            bool hasMainWnd = !string.IsNullOrEmpty(instance.MainWnd);
            bool hasRenderWnd = !string.IsNullOrEmpty(instance.RenderWnd);

            if (hasMainWnd)
            {
                if (TryParseHexHandle(instance.MainWnd, out mainWndHandle))
                {
                    // 如果主窗口句柄大于0，则认为模拟器正在运行
                    isRunning = mainWndHandle > 0;
                }
            }

            if (hasRenderWnd)
            {
                if (TryParseHexHandle(instance.RenderWnd, out renderWndHandle))
                {
                    // 如果渲染窗口句柄大于0，则认为模拟器渲染窗口有效
                    // 但不影响总的运行状态判断（即使渲染窗口无效，只要主窗口有效，仍认为模拟器正在运行）
                    if (renderWndHandle <= 0)
                    {
                        // 渲染窗口无效，记录警告但不影响总的运行状态
                    }
                }
            }

            // 综合判断模拟器是否运行
            // 如果主窗口有效，或者进程已启动且Android已启动，则认为模拟器正在运行
            isRunning = isRunning || (processStarted && androidStarted && (hasPid || hasHeadlessPid));

            return isRunning;
        }
    }
}