using OpenCvSharp;
using System;
using System.IO;

namespace XHelper.OCR
{
    /// <summary>
    /// 专门为PaddleOCR优化的图像处理器
    /// </summary>
    internal class PaddleOcrImageProcessor : OpenCvImageProcessor
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">OCR配置</param>
        public PaddleOcrImageProcessor(OcrConfiguration config) : base(config)
        {
        }

        /// <summary>
        /// 专门为Local_Ocr_String_Pro优化的图像处理方法
        /// 在图像的上下左右各添加25像素的黑色边框
        /// </summary>
        /// <param name="imagePath">图像文件路径</param>
        /// <returns>优化后的图像字节数组</returns>
        public byte[] OptimizeImageForPaddleOcr(string imagePath)
        {
            if (string.IsNullOrEmpty(imagePath) || !File.Exists(imagePath))
            {
                XLogger.Error($"图像文件不存在: {imagePath}");
                return new byte[0];
            }

            try
            {
                // 加载图片
                using Mat image = Cv2.ImRead(imagePath, ImreadModes.Color);
                if (image == null || image.Empty())
                {
                    XLogger.Error($"无法读取图像文件: {imagePath}");
                    return new byte[0];
                }

                return ProcessImageForPaddleOcr(image);
            }
            catch (OpenCVException ex)
            {
                XLogger.Error($"OpenCV处理图像异常: {ex.Message}");
                return new byte[0];
            }
            catch (Exception ex)
            {
                XLogger.Error($"图像优化异常: {ex.Message}");
                return new byte[0];
            }
        }

        /// <summary>
        /// 专门为Local_Ocr_String_Pro优化的图像处理方法
        /// 在图像的上下左右各添加25像素的黑色边框
        /// </summary>
        /// <param name="imageData">图像字节数组</param>
        /// <returns>优化后的图像字节数组</returns>
        public byte[] OptimizeImageForPaddleOcr(byte[] imageData)
        {
            if (imageData == null || imageData.Length == 0)
            {
                XLogger.Error("输入图像数据为空");
                return new byte[0];
            }

            try
            {
                // 加载图片 bytes
                using Mat image = Cv2.ImDecode(imageData, ImreadModes.Color);
                if (image == null || image.Empty())
                {
                    XLogger.Error("无法解码图像数据");
                    return new byte[0];
                }

                return ProcessImageForPaddleOcr(image);
            }
            catch (OpenCVException ex)
            {
                XLogger.Error($"OpenCV处理图像异常: {ex.Message}");
                return new byte[0];
            }
            catch (Exception ex)
            {
                XLogger.Error($"图像优化异常: {ex.Message}");
                return new byte[0];
            }
        }

        /// <summary>
        /// 专门为PaddleOCR优化的图像处理共享逻辑
        /// 在图像的上下左右各添加25像素的黑色边框
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <returns>处理后的图像字节数组</returns>
        private byte[] ProcessImageForPaddleOcr(Mat image)
        {
            if (image == null || image.Empty())
                return new byte[0];

            Mat? paddedImage = null;

            try
            {
                // 直接在原始图像上添加25像素的黑色边框
                paddedImage = new Mat();
                Cv2.CopyMakeBorder(image, paddedImage, 25, 25, 25, 25, BorderTypes.Constant, new Scalar(26, 33, 41));

                //XLogger.Debug($"为PaddleOCR添加黑色边框: 原图尺寸: {image.Width}x{image.Height}, 添加边框后尺寸: {paddedImage.Width}x{paddedImage.Height}");

                // 保存临时图片用于调试
                SaveTempImageIfEnabled(paddedImage, image, "_paddle_padded");

                // 返回带边框图片的字节
                return paddedImage.ToBytes(".jpg");
            }
            catch (Exception ex)
            {
                XLogger.Error($"PaddleOCR图像处理异常: {ex.Message}");
                return new byte[0];
            }
            finally
            {
                // 显式释放资源
                paddedImage?.Dispose();
            }
        }

        /// <summary>
        /// 如果启用了临时图像保存，则保存图像
        /// </summary>
        /// <param name="processedImage">处理后的图像</param>
        /// <param name="originalImage">原始图像</param>
        /// <param name="suffix">文件名后缀</param>
        private void SaveTempImageIfEnabled(Mat processedImage, Mat originalImage, string suffix)
        {
            if (!IsSaveTempImagesEnabled())
                return;

            try
            {
                string configPath = GetTempImagePath();
                string debugDir = Path.GetDirectoryName(configPath);
                if (!string.IsNullOrEmpty(debugDir) && !Directory.Exists(debugDir))
                {
                    Directory.CreateDirectory(debugDir);
                }

                // 构建处理后图像的保存路径
                string processedPath = Path.Combine(
                    Path.GetDirectoryName(configPath) ?? string.Empty,
                    Path.GetFileNameWithoutExtension(configPath) + suffix + Path.GetExtension(configPath));

                // 构建原始图像的保存路径
                string originalPath = Path.Combine(
                    Path.GetDirectoryName(configPath) ?? string.Empty,
                    Path.GetFileNameWithoutExtension(configPath) + suffix + "_original" + Path.GetExtension(configPath));

                // 保存图像
                Cv2.ImWrite(processedPath, processedImage);
                Cv2.ImWrite(originalPath, originalImage);

                //XLogger.Debug($"PaddleOCR处理后图像保存路径: {processedPath}");
                //XLogger.Debug($"PaddleOCR原始图像保存路径: {originalPath}");
            }
            catch (Exception ex)
            {
                XLogger.Error($"保存PaddleOCR临时图像失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取是否启用临时图像保存
        /// </summary>
        /// <returns>是否启用</returns>
        protected virtual bool IsSaveTempImagesEnabled()
        {
            // 这个方法可以被子类重写以提供不同的实现
            return false;
        }

        /// <summary>
        /// 获取临时图像保存路径
        /// </summary>
        /// <returns>保存路径</returns>
        protected virtual string GetTempImagePath()
        {
            // 这个方法可以被子类重写以提供不同的实现
            return string.Empty;
        }

        /// <summary>
        /// 获取上边框尺寸
        /// </summary>
        /// <returns>像素值</returns>
        public override int GetTopPadding()
        {
            return 25; // PaddleOCR的上边框为25像素
        }

        /// <summary>
        /// 获取下边框尺寸
        /// </summary>
        /// <returns>像素值</returns>
        public override int GetBottomPadding()
        {
            return 25; // PaddleOCR的下边框为25像素
        }

        /// <summary>
        /// 获取左边框尺寸
        /// </summary>
        /// <returns>像素值</returns>
        public override int GetLeftPadding()
        {
            return 25; // PaddleOCR的左边框为25像素
        }

        /// <summary>
        /// 获取右边框尺寸
        /// </summary>
        /// <returns>像素值</returns>
        public override int GetRightPadding()
        {
            return 25; // PaddleOCR的右边框为25像素
        }
    }
}