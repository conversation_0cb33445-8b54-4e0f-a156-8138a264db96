﻿using DamoControlKit.Interface;
using DamoControlKit.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DamoControlKit.Function
{
    /// <summary>
    /// 循环 图片 找到 后执行 委托
    /// </summary>
    /// <param name="x"></param>
    /// <param name="y"></param>
    public delegate void FLFP_PicYep_Delegate();

    public class Flow_L_Find_Pic : IFlow, IDmInterface
    {
        /// <summary>
        /// 结束时间
        /// </summary>
        private int EndTime { get; set; } = 1800;

        /// <summary>
        /// 找到图片执行后延迟时间 ms
        /// </summary>
        private int FindYepDelayTime { get; set; } = 500;
        public dmsoft? dmsoft { get; set; }

        public void Init() { }

        public bool SetXsoft() => false;

        /// <summary>
        /// 设置超时时间
        /// 默认结束时间30min
        /// </summary>
        /// <param name="timeSec">秒</param>
        /// <returns></returns>
        public Flow_L_Find_Pic SetEndTime(int timeSec)
        {
            EndTime = timeSec;
            return this;
        }

        /// <summary>
        /// 设置 找到图片执行后延迟时间
        /// 单位 ms
        /// </summary>
        /// <returns></returns>
        public Flow_L_Find_Pic SetFindYepDelayTime(int ms)
        {
            FindYepDelayTime = ms;
            return this;
        }

        /// <summary>
        /// 设置所有Pic的dmsoft对象
        /// </summary>
        /// <param name="x"></param>
        /// <returns></returns>
        public bool SetXsoft(dmsoft x)
        {
            PicList.SetAllXsoft(x);
            ExitPicList.SetAllXsoft(x);
            PosList.SetAllXsoft(x);
            dmsoft = x;
            return true;
        }

        /// <summary>
        /// 循环图片组
        /// </summary>
        private Pics PicList { get; set; } = new();

        /// <summary>
        /// 点击位置组
        /// </summary>
        private Positions PosList { get; set; } = new();

        /// <summary>
        /// 循环退出条件组
        /// 循环条件中的图片不支持点击
        /// </summary>
        private Pics ExitPicList { get; set; } = new();

        /// <summary>
        /// 添加退出条件 图片
        /// </summary>
        /// <param name="pic"></param>
        /// <returns></returns>
        public Flow_L_Find_Pic AddExitPic(Pic pic)
        {
            if (dmsoft is not null) pic.SetXsoft(dmsoft);
            ExitPicList.Add(pic);
            PicList.Add(pic);
            return this;
        }

        /// <summary>
        /// 添加图片 并找到图片 点击
        /// </summary>
        /// <param name="pic"></param>
        /// <param name="pos"></param>
        /// <returns></returns>
        public Flow_L_Find_Pic AddPicAndClick(Pic pic, Position? pos)
        {
            if (dmsoft is not null)
            {
                pic.SetXsoft(dmsoft);
                pos?.SetXsoft(dmsoft);
            }
            pos ??= pic.Position;
            pos.SetIdentifier(pic.Name);
            PicList.Add(pic);
            PosList.Add(pos);
            return this;
        }

        /// <summary>
        /// AddPicAndFun函数的回调方法字典
        /// 通过Name索引调用函数
        /// </summary>
        private Dictionary<string, FLFP_PicYep_Delegate> PicYepCallBackDic { get; set; } = new();

        /// <summary>
        /// 添加图片 并执行回调函数
        /// </summary>
        /// <param name="pic"></param>
        /// <returns></returns>
        public Flow_L_Find_Pic AddPicAndFun(Pic pic, FLFP_PicYep_Delegate CallBack)
        {
            if (dmsoft is not null) pic.SetXsoft(dmsoft);
            PicList.Add(pic);
            PicYepCallBackDic.Add(pic.Name, CallBack);
            return this;
        }

        public void Start()
        {
            if (PicList.Count == 0)
            {
                Debug.WriteLine("Flow_L_Find_Pic 循环图片列表为空，无法继续执行！");
                return;
            }
            if (ExitPicList.Count == 0)
            {
                Debug.WriteLine("Flow_L_Find_Pic 循环退出条件图片列表为空，无法继续执行！");
                return;
            }
            Main();
        }

        /// <summary>
        /// 运行时长计时
        /// </summary>
        private Stopwatch RunningWatch = new();
        private void Main()
        {
            bool tmp;
            RunningWatch.Start();
            while (RunningWatch.Elapsed.TotalSeconds <= EndTime)
            {
                foreach (var t in PicList.PicList)
                {
                    tmp = t.Find(null);
                    if (tmp)
                    {
                        FindYep(t.Name, t).Wait();
                        if (!RunningWatch.IsRunning) return;
                        continue;
                    }
                }
            }

            RunningWatch.Stop();
        }

        /// <summary>
        /// 查找到图片后操作
        /// </summary>
        private async Task FindYep(string identifier, Pic pic)
        {
            if (PicYepCallBackDic.TryGetValue(identifier, out FLFP_PicYep_Delegate? value))
            {
                Debug.WriteLine($"Flow_L_Find_Pic {identifier}执行回调函数");
                value();
            }
            var tmp = PosList.GetAllIdentifier();
            if (tmp?.Contains(identifier) ?? false)
            {
                Debug.WriteLine($"Flow_L_Find_Pic {identifier}执行范围点击");
                PosList.Click(identifier);
            }

            if (dmsoft is not null) dmsoft.delay(FindYepDelayTime);
            else await Task.Delay(FindYepDelayTime);

            if (ExitPicList.PicList.Contains(pic))
            {
                Debug.WriteLine($"Flow_L_Find_Pic {identifier}检测到退出图片，结束流程.");
                RunningWatch.Stop();
            }
        }

        public void Stop() { }
    }
}
