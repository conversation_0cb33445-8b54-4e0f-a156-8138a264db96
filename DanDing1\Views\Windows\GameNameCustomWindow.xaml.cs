﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using XHelper;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// GameNameCustomWindow.xaml 的交互逻辑
    /// </summary>
    public partial class GameNameCustomWindow : Window
    {
        public GameNameCustomWindow()
        {
            InitializeComponent();
            LoadGameNames();
        }

        private void LoadGameNames()
        {
            // 加载游戏1-4的名称
            Game1Name.Text = XConfig.LoadValueFromFile<string>("游戏1", "GameName") ?? "游戏1";
            Game2Name.Text = XConfig.LoadValueFromFile<string>("游戏2", "GameName") ?? "游戏2";
            Game3Name.Text = XConfig.LoadValueFromFile<string>("游戏3", "GameName") ?? "游戏3";
            Game4Name.Text = XConfig.LoadValueFromFile<string>("游戏4", "GameName") ?? "游戏4";

            // 加载默认游戏数量
            int defaultGameCount = XConfig.LoadValueFromFile<int>("Common", "DefaultGameCount");
            // 确保值在1-4之间，默认为1
            if (defaultGameCount < 1 || defaultGameCount > 4)
            {
                defaultGameCount = 1;
            }
            // ComboBox的索引从0开始，而游戏数量从1开始
            DefaultGameCount.SelectedIndex = defaultGameCount - 1;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            // 保存游戏1-4的名称
            XConfig.SaveValueToFile("游戏1", "GameName", Game1Name.Text == "" ? "游戏1" : Game1Name.Text);
            XConfig.SaveValueToFile("游戏2", "GameName", Game2Name.Text == "" ? "游戏2" : Game2Name.Text);
            XConfig.SaveValueToFile("游戏3", "GameName", Game3Name.Text == "" ? "游戏3" : Game3Name.Text);
            XConfig.SaveValueToFile("游戏4", "GameName", Game4Name.Text == "" ? "游戏4" : Game4Name.Text);

            // 保存默认游戏数量
            // ComboBox选中项索引从0开始，加1得到实际游戏数量
            int defaultGameCount = DefaultGameCount.SelectedIndex + 1;
            XConfig.SaveValueToFile("Common", "DefaultGameCount", defaultGameCount);

            MessageBox.Show("保存成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            this.Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}