﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DanDing1.Models.Ws_Models
{
    /// <summary>
    /// 脚本状态数据模型
    /// </summary>
    public class ScriptStatusData
    {
        /// <summary>
        /// 当前正在执行的任务名 (例如 "御魂", "探索")
        /// </summary>
        public string? CurrentTask { get; set; }

        /// <summary>
        /// 用户友好的名称 (例如 "游戏1" 或 超级多开中用户自定义的名称)
        /// </summary>
        public string FriendlyName { get; set; }

        /// <summary>
        /// Scripts类管理的数字ID
        /// </summary>
        public int InternalScriptId { get; set; }

        /// <summary>
        /// 附加信息，如错误消息、用户通知 (例如 "御魂已满", "等待游戏重启")
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// // 已运行时间（秒），可选
        /// </summary>
        public double? RunningDurationSeconds { get; set; }

        /// <summary>
        /// 脚本实例的唯一ID (对于常规脚本可以是GameName，对于超级多开是 "SuperMultiGame_{GameId}")
        /// </summary>
        public string ScriptInstanceId { get; set; }

        /// <summary>
        /// 脚本的所有任务名
        /// </summary>
        public List<string> ScriptTasks { get; set; } = [];

        /// <summary>
        /// "Regular" 或 "SuperMulti"
        /// </summary>
        public string ScriptType { get; set; }

        /// <summary>
        ///  标准化后的状态 (枚举值)
        /// </summary>
        public ScriptRuntimeStatus Status { get; set; }

        /// <summary>
        /// 状态更新的时间戳 (UTC)
        /// </summary>
        public DateTime Timestamp { get; set; }

        // 深拷贝方法，用于存储上一次状态
        public ScriptStatusData Clone()
        {
            return new ScriptStatusData
            {
                ScriptInstanceId = this.ScriptInstanceId,
                InternalScriptId = this.InternalScriptId,
                ScriptType = this.ScriptType,
                FriendlyName = this.FriendlyName,
                Status = this.Status,
                CurrentTask = this.CurrentTask,
                ScriptTasks = this.ScriptTasks,
                Timestamp = this.Timestamp,
                Message = this.Message,
                RunningDurationSeconds = this.RunningDurationSeconds
            };
        }

        // 判断状态是否与另一个状态相同的方法（用于比较是否需要上报）
        public bool SignificantlyDifferentFrom(ScriptStatusData other)
        {
            if (other == null) return true;

            // 以下字段变化视为重要变化，需要上报
            return this.Status != other.Status ||
                   this.CurrentTask != other.CurrentTask ||
                   this.ScriptTasks.Count != other.ScriptTasks.Count ||
                   this.Message != other.Message;
        }
    }
}