﻿using DanDing1.ViewModels.Pages;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace DanDing1.Models;

public partial class UserConfigModel : ObservableObject, INotifyPropertyChanged, ICloneable
{
    /// <summary>
    /// 若失败次数大于该值，则结束脚本
    /// </summary>
    [ObservableProperty]
    public string _defeatedEnd = "0";

    /// <summary>
    /// 随机等待时间概率
    /// </summary>
    [ObservableProperty]
    public float _randomWait = 0.1f;

    /// <summary>
    /// 随机穿插小纸人概率
    /// </summary>
    [ObservableProperty]
    public float _randomYysAuto = 0.1f;

    /// <summary>
    /// 突破卷30张打突破
    /// </summary>
    [ObservableProperty]
    public bool _tupo30run;

    /// <summary>
    /// 满御魂后执行的操作
    /// </summary>
    [ObservableProperty]
    public string _fullYuHun_Do = "结束任务";

    /// <summary>
    /// 御魂是否自动喂养
    /// </summary>
    [ObservableProperty]
    public bool _yuhunFood = true;

    /// <summary>
    /// 是否是内测者
    /// </summary>
    [ObservableProperty]
    public bool _betaTesterToggle = false;

    /// <summary>
    /// 突破卷30张的数量
    /// </summary>
    [ObservableProperty]
    public string _tupo30_Count = "30";

    /// <summary>
    /// 突破卷30张的标记
    /// </summary>
    [ObservableProperty]
    public string _tupo30_Biaoji = "位置5";

    /// <summary>
    /// 突破卷30张是否退出
    /// </summary>
    [ObservableProperty]
    public bool _tupo30_Tui4 = false;

    /// <summary>
    /// KaPing_Do 操作
    /// </summary>
    [ObservableProperty]
    public string _kaPing_Do = "结束任务";

    /// <summary>
    /// 自定义点击范围-达摩
    /// </summary>
    [ObservableProperty]
    public string _custClick_Damo = "关闭";

    /// <summary>
    /// 自定义点击范围-结果
    /// </summary>
    [ObservableProperty]
    public string _custClick_Result = "关闭";

    /// <summary>
    /// 是否启用微信推送通知
    /// </summary>
    [ObservableProperty]
    public bool _notice_WxPush = false;

    /// <summary>
    /// 是否启用Ntfy通知
    /// </summary>
    [ObservableProperty]
    public bool _notice_Ntfy = false;

    /// <summary>
    /// 微信推送通知的用户ID
    /// </summary>
    [ObservableProperty]
    public string _notice_WxPush_UID = "";

    /// <summary>
    /// 是否启用Pushplus推送通知
    /// </summary>
    [ObservableProperty]
    public bool _notice_Pushplus = false;

    /// <summary>
    /// Pushplus推送通知的Token
    /// </summary>
    [ObservableProperty]
    public string _notice_Pushplus_Token = "";

    /// <summary>
    /// 是否启用喵提醒通知
    /// </summary>
    [ObservableProperty]
    public bool _notice_Miaotixing = false;

    /// <summary>
    /// 喵提醒的喵码ID
    /// </summary>
    [ObservableProperty]
    public string _notice_Miaotixing_ID = "";

    /// <summary>
    /// 是否启用全局预设
    /// </summary>
    [ObservableProperty]
    public bool _gal_YuShe = false;

    /// <summary>
    /// 御魂全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Yuhun = "";

    /// <summary>
    /// 觉醒全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Juexing = "";

    /// <summary>
    /// 探索全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Tansuo = "";

    /// <summary>
    /// 日轮全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Rilun = "";

    /// <summary>
    /// 永生全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Yongsheng = "";

    /// <summary>
    /// 御灵全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Yuling = "";

    /// <summary>
    /// 业原火全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Yeyuanhuo = "";

    /// <summary>
    /// 突破全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Tupo = "";

    /// <summary>
    /// 悬赏全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Xuanshang = "";

    /// <summary>
    /// 六道全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Liudao = "";

    /// <summary>
    /// 契灵全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Qiling = "";

    /// <summary>
    /// 英杰试炼全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Yingjie = "";

    /// <summary>
    /// 斗技全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Douji = "";

    /// <summary>
    /// 日常全局预设
    /// </summary>
    [ObservableProperty]
    public string _gal_YuShe_Daily = "";

    /// <summary>
    /// 将配置输出成字典格式
    /// </summary>
    /// <returns>包含所有配置属性的字典</returns>
    public Dictionary<string, object> Pairs
    {
        get
        {
            // 创建字典存储配置
            Dictionary<string, object> pairs = new();

            // 获取当前类型的所有公共属性
            var properties = GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => !p.Name.StartsWith("_") && p.Name != "Pairs"); // 排除私有字段和Pairs属性本身

            // 遍历所有属性并添加到字典中
            foreach (var prop in properties)
            {
                var value = prop.GetValue(this);

                // 对DefeatedEnd特殊处理
                if (prop.Name == "DefeatedEnd" && value is string strValue)
                {
                    if (int.TryParse(strValue, out int result))
                        pairs.Add("DefeatedEnd", result);
                    else
                        pairs.Add("DefeatedEnd", 0);
                }
                else
                {
                    // 使用原始的属性名称格式作为键，与原方法保持一致
                    pairs.Add(prop.Name, value);
                }
            }

            return pairs;
        }
    }

    public object Clone()
    {
        return this.MemberwiseClone();
    }

    /// <summary>
    /// 比较两个UserConfigModel对象是否相等
    /// </summary>
    /// <param name="obj">要比较的对象</param>
    /// <returns>如果所有属性值相等则返回true，否则返回false</returns>
    public override bool Equals(object? obj)
    {
        // 如果对象不是UserConfigModel类型，返回false
        if (obj is not UserConfigModel other)
            return false;

        // 检查所有需要比较的属性
        if (other.Tupo30run != Tupo30run)
            return false;
        if (other.DefeatedEnd != DefeatedEnd)
            return false;
        if (other.BetaTesterToggle != BetaTesterToggle)
            return false;
        if (other.Tupo30_Count != Tupo30_Count)
            return false;
        if (other.Tupo30_Biaoji != Tupo30_Biaoji)
            return false;
        if (other.Tupo30_Tui4 != Tupo30_Tui4)
            return false;
        if (other.RandomWait != RandomWait)
            return false;
        if (other.RandomYysAuto != RandomYysAuto)
            return false;
        if (other.FullYuHun_Do != FullYuHun_Do)
            return false;
        if (other.YuhunFood != YuhunFood)
            return false;
        if (other.KaPing_Do != KaPing_Do)
            return false;
        if (other.CustClick_Damo != CustClick_Damo)
            return false;
        if (other.CustClick_Result != CustClick_Result)
            return false;
        if (other.Notice_WxPush != Notice_WxPush)
            return false;
        if (other.Notice_Ntfy != Notice_Ntfy)
            return false;
        if (other.Notice_WxPush_UID != Notice_WxPush_UID)
            return false;
        if (other.Notice_Pushplus != Notice_Pushplus)
            return false;
        if (other.Notice_Pushplus_Token != Notice_Pushplus_Token)
            return false;
        if (other.Notice_Miaotixing != Notice_Miaotixing)
            return false;
        if (other.Notice_Miaotixing_ID != Notice_Miaotixing_ID)
            return false;
        if (other.Gal_YuShe != Gal_YuShe)
            return false;
        if (other.Gal_YuShe_Yuhun != Gal_YuShe_Yuhun)
            return false;
        if (other.Gal_YuShe_Juexing != Gal_YuShe_Juexing)
            return false;
        if (other.Gal_YuShe_Tansuo != Gal_YuShe_Tansuo)
            return false;
        if (other.Gal_YuShe_Rilun != Gal_YuShe_Rilun)
            return false;
        if (other.Gal_YuShe_Yongsheng != Gal_YuShe_Yongsheng)
            return false;
        if (other.Gal_YuShe_Yuling != Gal_YuShe_Yuling)
            return false;
        if (other.Gal_YuShe_Yeyuanhuo != Gal_YuShe_Yeyuanhuo)
            return false;
        if (other.Gal_YuShe_Tupo != Gal_YuShe_Tupo)
            return false;
        if (other.Gal_YuShe_Xuanshang != Gal_YuShe_Xuanshang)
            return false;
        if (other.Gal_YuShe_Liudao != Gal_YuShe_Liudao)
            return false;
        if (other.Gal_YuShe_Qiling != Gal_YuShe_Qiling)
            return false;
        if (other.Gal_YuShe_Yingjie != Gal_YuShe_Yingjie)
            return false;
        if (other.Gal_YuShe_Douji != Gal_YuShe_Douji)
            return false;
        if (other.Gal_YuShe_Daily != Gal_YuShe_Daily)
            return false;

        // 所有属性值都相等，返回true
        return true;
    }
}