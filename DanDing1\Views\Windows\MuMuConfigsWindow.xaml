﻿<Window x:Class="DanDing1.Views.Windows.MuMuConfigsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        d:DataContext="{d:DesignInstance local:MuMuConfigsWindow, IsDesignTimeCreatable=True}"
        ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
        ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        ScrollViewer.CanContentScroll="False"
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen"
        Closing="Window_Closing"
        Title="mumu模拟器配置"
        Width="800"
        Height="470">
    <Window.Resources>
        <!-- 定义统一的列内容样式 -->
        <Style x:Key="CenteredCellStyle"
               TargetType="DataGridCell">
            <Setter Property="HorizontalContentAlignment"
                    Value="Center" />
            <Setter Property="VerticalContentAlignment"
                    Value="Center" />
            <Setter Property="Height"
                    Value="25" />
            <Setter Property="Background"
                    Value="Transparent" />
            <Setter Property="BorderBrush"
                    Value="{DynamicResource ControlElevationBorderBrush}" />
            <Setter Property="Foreground"
                    Value="{DynamicResource TextFillColorPrimaryBrush}" />
        </Style>

        <!-- 定义统一的列标题样式 -->
        <Style x:Key="CenteredColumnHeaderStyle"
               TargetType="DataGridColumnHeader">
            <Setter Property="HorizontalContentAlignment"
                    Value="Center" />
            <Setter Property="VerticalContentAlignment"
                    Value="Center" />
            <Setter Property="Background"
                    Value="Transparent" />
            <Setter Property="BorderBrush"
                    Value="{DynamicResource ControlElevationBorderBrush}" />
            <Setter Property="Foreground"
                    Value="{DynamicResource TextFillColorPrimaryBrush}" />
            <Setter Property="Padding"
                    Value="10,5" />
        </Style>

        <!-- 定义 DataGridCheckBoxColumn 的 ElementStyle 和 EditingElementStyle -->
        <Style x:Key="ReadOnlyCheckBoxStyle"
               TargetType="CheckBox">
            <Setter Property="HorizontalAlignment"
                    Value="Center" />
            <Setter Property="VerticalAlignment"
                    Value="Center" />
            <Setter Property="IsEnabled"
                    Value="False" />
            <Setter Property="Foreground"
                    Value="{DynamicResource TextFillColorPrimaryBrush}" />
        </Style>
    </Window.Resources>
    <StackPanel Margin="5">
        <StackPanel Orientation="Horizontal">
            <ui:TextBlock VerticalAlignment="Center"
                          Text="MuMu模拟器配置 | 模拟器路径："
                          FontSize="20" />
            <ui:TextBlock VerticalAlignment="Center"
                          x:Name="MuMuPathTip"
                          Text="未设置"
                          FontSize="20" />
            <ui:Button Click="Button_Click"
                       Margin="10 0 0 0">
                <ui:TextBlock FontWeight="Bold"
                              VerticalAlignment="Center"
                              Text="选择路径"
                              FontSize="16" />
            </ui:Button>
        </StackPanel>

        <!-- 添加初始提示信息面板 -->
        <Border x:Name="InitialTipPanel"
                Margin="0,20,0,20"
                Padding="20"
                BorderThickness="1"
                BorderBrush="{DynamicResource ControlElevationBorderBrush}"
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                CornerRadius="8">
            <StackPanel HorizontalAlignment="Center">
                <ui:SymbolIcon Symbol="ArrowUp48"
                               FontSize="48"
                               Foreground="{DynamicResource AccentFillColorDefaultBrush}" />
                <ui:TextBlock Text="请点击上方的《选择路径》按钮来初始化模拟器数据"
                              FontSize="18"
                              Margin="0,10,0,0"
                              TextAlignment="Center" />
                <ui:TextBlock Text="选择您的 MuMuPlayer.exe 文件位置即可开始使用"
                              FontSize="14"
                              Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                              Margin="0,5,0,0"
                              TextAlignment="Center" />
            </StackPanel>
        </Border>

        <StackPanel x:Name="MainContent">
            <ui:TextBlock Foreground="Red"
                          Margin="0 5 0 0"
                          TextWrapping="Wrap"
                          Text="1、这是我们在您电脑上获取到的所有MuMu模拟器的信息，若模拟器处于运行状态，ADB端口会自动获取，除了自动获取ADB端口外，您还可以双击编辑ADB端口。"
                          FontSize="15" />
            <ui:TextBlock Foreground="Red"
                          Margin="0 5 0 5"
                          TextWrapping="Wrap"
                          Text="2、ADB端口用于控制模拟器中的阴阳师应用，可以实现卡住重启阴阳师的操作。（若ADB端口不存在，则卡住后直接结束任务）"
                          FontSize="15" />
            <ui:TextBlock x:Name="SelectorModeHint"
                          Foreground="Blue"
                          Margin="0 5 0 10"
                          TextWrapping="Wrap"
                          Text="3、双击表格行选择该模拟器。添加后，模拟器将被用于创建新的游戏实例。"
                          FontSize="15" />
            <ui:DataGrid x:Name="DataGrid"
                         CanUserAddRows="False"
                         RowHeaderWidth="0"
                         ItemsSource="{Binding Simulators}"
                         AutoGenerateColumns="False"
                         MouseDoubleClick="DataGrid_MouseDoubleClick"
                         ColumnHeaderStyle="{StaticResource CenteredColumnHeaderStyle}"
                         Background="Transparent"
                         BorderBrush="{DynamicResource ControlElevationBorderBrush}"
                         Foreground="{DynamicResource TextFillColorPrimaryBrush}">
                <ui:DataGrid.Columns>
                    <DataGridTextColumn IsReadOnly="True"
                                        Header="索引"
                                        Binding="{Binding Index}"
                                        Width="*"
                                        CellStyle="{StaticResource CenteredCellStyle}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="TextAlignment"
                                        Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                        <DataGridTextColumn.EditingElementStyle>
                            <Style TargetType="TextBox">
                                <Setter Property="TextAlignment"
                                        Value="Center" />
                            </Style>
                        </DataGridTextColumn.EditingElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn IsReadOnly="True"
                                        Header="模拟器名字"
                                        Binding="{Binding Name}"
                                        Width="*"
                                        CellStyle="{StaticResource CenteredCellStyle}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="TextAlignment"
                                        Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                        <DataGridTextColumn.EditingElementStyle>
                            <Style TargetType="TextBox">
                                <Setter Property="TextAlignment"
                                        Value="Center" />
                            </Style>
                        </DataGridTextColumn.EditingElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="ADB 端口"
                                        Binding="{Binding AdbPort}"
                                        Width="*"
                                        CellStyle="{StaticResource CenteredCellStyle}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="TextAlignment"
                                        Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                        <DataGridTextColumn.EditingElementStyle>
                            <Style TargetType="TextBox">
                                <Setter Property="TextAlignment"
                                        Value="Center" />
                            </Style>
                        </DataGridTextColumn.EditingElementStyle>
                    </DataGridTextColumn>
                    <DataGridCheckBoxColumn Header="主模拟器"
                                            IsReadOnly="True"
                                            Binding="{Binding IsMain}"
                                            Width="*"
                                            CellStyle="{StaticResource CenteredCellStyle}">
                        <DataGridCheckBoxColumn.ElementStyle>
                            <StaticResource ResourceKey="ReadOnlyCheckBoxStyle" />
                        </DataGridCheckBoxColumn.ElementStyle>
                        <DataGridCheckBoxColumn.EditingElementStyle>
                            <StaticResource ResourceKey="ReadOnlyCheckBoxStyle" />
                        </DataGridCheckBoxColumn.EditingElementStyle>
                    </DataGridCheckBoxColumn>
                    <DataGridCheckBoxColumn Header="运行状态"
                                            Binding="{Binding IsRunning, Mode=OneWay}"
                                            Width="*"
                                            IsReadOnly="True"
                                            CellStyle="{StaticResource CenteredCellStyle}">
                        <DataGridCheckBoxColumn.ElementStyle>
                            <StaticResource ResourceKey="ReadOnlyCheckBoxStyle" />
                        </DataGridCheckBoxColumn.ElementStyle>
                        <DataGridCheckBoxColumn.EditingElementStyle>
                            <StaticResource ResourceKey="ReadOnlyCheckBoxStyle" />
                        </DataGridCheckBoxColumn.EditingElementStyle>
                    </DataGridCheckBoxColumn>
                </ui:DataGrid.Columns>
            </ui:DataGrid>
        </StackPanel>
    </StackPanel>
</Window>