<Window x:Class="DanDing1.Views.CaptchaWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
        ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Title="验证"
        Height="250"
        Width="350"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <ui:TextBlock Text="请输入验证码"
                   FontSize="16"
                   Margin="0,0,0,15" />

        <Border Grid.Row="1"
                BorderThickness="1"
                Width="180"
                Height="50">
            <ui:Image x:Name="CaptchaImage"
                   Stretch="None"
                   RenderOptions.BitmapScalingMode="NearestNeighbor" />
        </Border>

        <ui:TextBox Grid.Row="2"
                    Margin="0 5 0 0"
                 x:Name="CaptchaInput"
                 MaxLength="6" />

        <StackPanel Grid.Row="3"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right">
            <ui:Button Content="刷新"
                    x:Name="RefreshButton"
                        Margin="10,15,0,0"
                       Height="30"
                       Width="65"
                    Click="RefreshButton_Click" />
            <ui:Button Content="确定"
                        Margin="10,15,0,0"
                       Height="30"
                       Width="100"
                    x:Name="ConfirmButton"
                    Click="ConfirmButton_Click" />
        </StackPanel>
    </Grid>
</Window> 