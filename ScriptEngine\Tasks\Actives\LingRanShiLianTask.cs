﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System.Text.RegularExpressions;

namespace ScriptEngine.Tasks.Actives
{
    /// <summary>
    /// 灵染试炼任务
    /// </summary>
    internal class LingRanShiLianTask : BaseTask
    {
        /// <summary>
        /// 是否开启标记功能
        /// </summary>
        private bool Biaoji = false;

        private bool BiaoJi_Status = false;

        private List<string> DontSendLog = ["标记"];

        /// <summary>
        /// 任务次数
        /// </summary>
        private int Ncount = 0;

        /// <summary>
        /// OCR识别区域
        /// </summary>
        private Dictionary<string, Position> OcrPosition = new()
        {
            {"自选次数",new(1145,409,1243,440 )}
        };

        public bool FirstEnd = false;
        private bool Automated = false;

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "活动");
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Ncount = GetConfig.Count;
            GetConfig.Others.TryGetValue("Biaoji", out string? s);
            try { Biaoji = s is null ? false : bool.Parse(s); } catch (Exception) { Biaoji = false; }

            if (NowisMainScene())
            {
                log.Info_Green("当前在灵染试炼场景内，无需调整界面，直接开始！");
                goto Start;
            }
            string nows = Scene.NowScene;
            if (nows.Contains("探索"))
            {
                log.Info("进入场景：庭院");
                if (!Scene.TO.TingYuan())
                {
                    log.Warn("活动任务无法继续，当前游戏所在场景未知，请调整到庭院界面开始脚本！");
                    return;
                }
            }
            //进入活动界面
            log.Info("进入活动界面..");
            TOActivity();
            Sleep(1000);
        Start:
            main();
            UserNotificationMessage = $"共战斗{count}/{Ncount}次.";
        }

        /// <summary>
        /// 进入活动界面
        /// </summary>
        private void TOActivity()
        {
            // 最大尝试次数
            int maxAttempts = 4;
            int currentAttempt = 0;
            // 查找活动入口图片
            var pics = Mp.Filter("活动.灵染_入口");
            var finsh_pics = Mp.Filter("活动.活动入口_刷新");
            while (currentAttempt < maxAttempts)
            {
                if (pics != null && pics.FindAllAndClick())
                {
                    Sleep(2500);
                    Fast.Click(308, 460, 337, 534);
                    Sleep(2000);
                    return;
                }

                while (!finsh_pics.FindAllAndClick()) Sleep(1000);
                Sleep(2000);
                currentAttempt++;
            }

            // 3次尝试后仍未找到，结束任务
            throw new Exception("未能找到活动入口，任务结束");
        }

        private int count = 0;

        /// <summary>
        /// 主流程
        /// </summary>
        private void main()
        {
            if (UserConfig_Preset != null)
            {
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入式神录，开始应用预设{UserConfig_Preset}");
                Fast.Click(1173, 467, 1209, 505);
                Sleep(1500);
                Tmp.Do_Preset(preset);
            }
        Re:
            while (count < Ncount)
            {
                if (!WaitMainScene()) goto Re;
                Anti.RandomDelay(); //防封等待
                if (Anti.ShouldTriggerRandomYysAuto())//判断是否需要穿插纸人
                {
                    Fast.Click(1023, 649, 1054, 682); //打开小纸人
                    Sleep(500);
                    int do_Count = Random.Shared.Next(1, 5); // 1-4次随机次数
                    if (Tmp.Do_YysAuto(do_Count))
                    {
                        count += do_Count;
                        Combat_End();
                        log.Info($"触发随机穿插纸人战斗结束..脚本继续接管..");
                        Anti.ResetRandomYysAuto();
                        goto Re;
                    }
                    else Sleep(1000);
                }
                if (Db.PendingTimerTask) //执行定时任务
                {
                    Db.PendingTimerTask = false;
                    log.Info("暂停当前任务，执行定时任务，退出到探索..");
                    EndCallBack();
                    Db?.TimerTask?.DoAllTask();
                    Sleep(1000);
                    throw new Exception("定时任务执行结束，重新执行当前的主任务..");
                }
                log.Info_Green($"当前灵染自选已战斗次数：{Ocr()}");
                if (Combat())
                {
                    count++;
                    log.Info($"活动战斗胜利，战斗次数：{count}/{Ncount}");
                }
                else
                {
                    log.Warn($"活动战斗失败，请检查您的队伍配置是否正常！战斗次数：{count}/{Ncount}");
                    Defeated();
                }
            }
            EndCallBack();
        }

        /// <summary>
        /// OCR次数
        /// </summary>
        /// <returns></returns>
        private int Ocr()
        {
            var str = Fast.Ocr_Local(OcrPosition["自选次数"].X, OcrPosition["自选次数"].Y, OcrPosition["自选次数"].X1, OcrPosition["自选次数"].Y1);
            int.TryParse(str.Split('/')[0], out int count);
            return count;
        }

        /// <summary>
        /// 退出活动界面
        /// </summary>
        private void EndCallBack()
        {
            log.Info("执行活动任务收尾方法,等待返回后,退出到探索。");
            var mps = new MemPics()
                .Add(Mp.Filter("活动.准备"))
                .Add(Mp.Filter("活动.达摩"));

            while (!Mp.Filter("活动.挑战").FindAll())
            {
                if (NowisMainScene())
                    break;
                mps.FindAllAndClick();
                Sleep(1000);
            }

            log.Info("退出到庭院..");
            Sleep(500);
            Fast.Click("25,20,57,51");
            Sleep(1200);
            Fast.Click("25,20,57,51");
            Sleep(1200);
        }

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            //点击开始
            Fast.Click(1146, 602, 1232, 677);
            log.Info("战斗点击开始");
            var pics = Mp.Filter("活动.战斗_");
            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                FindOkFun(p.Name, p);
                if (!DontSendLog.Any(p.Name.Contains)) log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("胜利") || p.Name.Contains("达摩")
                    || p.Name.Contains("获得奖励"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败"))
                {
                    ret_bol = false;
                    isbreak = true;
                }
            }
            if (ret_bol)
                Combat_End();

            return ret_bol;
        }

        /// <summary>
        /// 胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("活动.战斗_");
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                if (p.Name.Contains("挑战"))
                {
                    isbreak = true;
                    continue;
                }
                log.Info($"执行点击：{p._Name}");
                p.Click();
            }
        }

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            if (Biaoji && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.2秒，标记位置：5号位");
                Sleep(100);
                Fast.Click(944, 504, 973, 539);
                return false;
            }
            return true;
        }

        /// <summary>
        /// 等待活动场景
        /// </summary>
        /// <returns></returns>
        public bool WaitMainScene()
        {
            var pics = Mp.Filter("活动.灵染_场景");
            if (pics.Wait()) return true;
            return false;
        }

        /// <summary>
        /// 是否在活动场景
        /// </summary>
        /// <returns></returns>
        public bool NowisMainScene()
        {
            var pics = Mp.Filter("活动.灵染_场景");
            return pics.FindAll();
        }
    }
}