using System.Windows;
using System.Windows.Media;

namespace DanDing1.Helpers
{
    /// <summary>
    /// UI控件辅助工具类
    /// </summary>
    public static class UIHelper
    {
        /// <summary>
        /// 在可视化树中通过名称查找子控件
        /// </summary>
        /// <param name="parent">父级依赖对象</param>
        /// <param name="name">要查找的控件名称</param>
        /// <returns>找到的依赖对象，未找到则返回null</returns>
        public static DependencyObject FindChildByName(DependencyObject parent, string name)
        {
            if (parent == null || string.IsNullOrEmpty(name))
                return null;

            // 检查当前对象是否具有指定名称
            FrameworkElement element = parent as FrameworkElement;
            if (element != null && element.Name == name)
                return parent;

            // 获取子元素数量
            int childCount = VisualTreeHelper.GetChildrenCount(parent);

            // 遍历所有子元素
            for (int i = 0; i < childCount; i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(parent, i);

                // 递归查找子元素
                DependencyObject result = FindChildByName(child, name);
                if (result != null)
                    return result;
            }

            return null;
        }

        /// <summary>
        /// 在逻辑树中查找特定类型的父控件
        /// </summary>
        /// <typeparam name="T">父控件类型</typeparam>
        /// <param name="child">子依赖对象</param>
        /// <returns>找到的父控件实例，未找到则返回null</returns>
        public static T FindParent<T>(DependencyObject child) where T : DependencyObject
        {
            DependencyObject parent = LogicalTreeHelper.GetParent(child);

            if (parent == null)
                return null;

            T result = parent as T;
            if (result != null)
                return result;

            return FindParent<T>(parent);
        }
    }
}