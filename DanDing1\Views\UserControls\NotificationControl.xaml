<UserControl x:Class="DanDing1.Views.UserControls.NotificationControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             x:Name="NotificationControlInstance"
             Margin="0,8,0,0"
             Opacity="0"
             MaxWidth="350">
    <UserControl.Resources>
        <!-- 淡入动画 -->
        <Storyboard x:Key="FadeInStoryboard">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                             From="0" To="1" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            
            <!-- 添加上移动画效果 -->
            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.(TranslateTransform.Y)"
                            From="20" To="0" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
        
        <!-- 淡出动画 -->
        <Storyboard x:Key="FadeOutStoryboard">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                             From="1" To="0" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            
            <!-- 添加下移动画效果 -->
            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.(TranslateTransform.Y)"
                            From="0" To="20" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </UserControl.Resources>
    
    <UserControl.RenderTransform>
        <TranslateTransform X="0" Y="0" />
    </UserControl.RenderTransform>
    
    <!-- 通知内容 -->
    <Border CornerRadius="8" 
            Background="{DynamicResource ControlFillColorDefaultBrush}"
            BorderBrush="{DynamicResource ControlElevationBorderBrush}"
            BorderThickness="1"
            Padding="12"
            MinWidth="250">
        <!-- 阴影效果 -->
        <Border.Effect>
            <DropShadowEffect ShadowDepth="1" 
                              Direction="315" 
                              Color="#22000000" 
                              BlurRadius="6" 
                              Opacity="0.3"/>
        </Border.Effect>
        
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- 图标 -->
            <ui:SymbolIcon x:Name="NotificationIcon" 
                          Grid.Column="0" 
                          Symbol="{Binding IconSymbol}"
                          Foreground="{Binding IconColor}"
                          Margin="0,0,10,0"
                          FontSize="20"/>
            
            <!-- 内容 -->
            <StackPanel Grid.Column="1">
                <TextBlock x:Name="TitleText" 
                           Text="{Binding Title}" 
                           FontWeight="SemiBold" 
                           Margin="0,0,0,4"
                           TextWrapping="Wrap"
                           Visibility="{Binding Title, Converter={StaticResource EmptyStringToVisibilityConverter}}"/>
                <TextBlock x:Name="MessageText" 
                           Text="{Binding Message}" 
                           TextWrapping="Wrap"/>
            </StackPanel>
            
            <!-- 关闭按钮 -->
            <ui:Button Grid.Column="2" 
                      Command="{Binding CloseCommand}"
                      VerticalAlignment="Top"
                      HorizontalAlignment="Right"
                      Appearance="Transparent"
                      Padding="4">
                <ui:SymbolIcon Symbol="Dismiss12"/>
            </ui:Button>
        </Grid>
    </Border>
</UserControl> 