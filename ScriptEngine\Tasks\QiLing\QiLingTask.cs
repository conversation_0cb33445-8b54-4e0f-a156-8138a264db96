﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;

namespace ScriptEngine.Tasks.QiLing
{
#pragma warning disable CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑添加 "required" 修饰符或声明为可为 null。

    internal class QiLingTask : BaseTask
    {
        /// <summary>
        /// 战斗图片
        /// </summary>
        internal MemPics ZhanDou_Pics;

        /// <summary>
        /// 镇墓兽图片
        /// </summary>
        internal MemPics ZhenMuShou_Pics;

        private CancellationTokenSource _ct;

        private Dictionary<string, Pixel> Colors = new Dictionary<string, Pixel>()
        {
            {"购买鸣契石",new Pixel(694,597,"f4b25d-101010",0.97) },
            {"千咒式盘上限提示",new Pixel(831,447,"f3b25e-101010",0.97) },
        };

        private QiLingTask_Config Config;
#pragma warning restore CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑添加 "required" 修饰符或声明为可为 null。

        private bool IsBuZhuo = false;

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "契灵");
            _ct = ct;
            ZhanDou_Pics = Mp.Filter("契灵.战斗_")
                .Add(Mp.Filter("活动"));
            ZhenMuShou_Pics = Mp.Filter("契灵_镇墓兽");
            //初始化组件
            foreach (var item in Colors)
                item.Value.SetXsoft(Dm);
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Config = new QiLingTask_Config(configs);
            log.Warn("开始全自动契灵任务，请注意以上几点：");
            log.Warn("1.镇墓兽模式：请开启自动结契；");
            log.Warn("2.预设更换操作只能在契灵之境中完成；");

            if (Config.TaskType == "镇墓兽")
                HandleZhenMuShou();
            else if (Config.TaskType == "探查")
                HandleTanCha();
            UserNotificationMessage = $"共战斗{Config.VictoryCount}/{Config.Ncount}次.捕捉成功{Config.CaptureSuccessCount}次.";
        }

        private void Exit()
        {
            //如果在结契场景多点一下
            if (Mp.Filter("场景_结契").FindAll())
            {
                Sleep(1000);
                Fast.Click(34, 25, 74, 60);
                Fast.Click(34, 25, 74, 60);
            }
            Sleep(1000);
            Fast.Click(34, 25, 74, 60);
            Fast.Click(34, 25, 74, 60);
            Sleep(3000);
        }

        private void HandleTanCha()
        {
            // 进入契灵场景
            EnterQiLingScene();

            if (UserConfig_Preset != null)
            {
                Sleep(1500);
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入式神录，开始应用预设{UserConfig_Preset}");
                Fast.Click("1039,571,1070,594");
                Sleep(1500);
                Tmp.Do_Preset(preset);
            }

            while (!_ct.Token.IsCancellationRequested && Config.VictoryCount < Config.MaxVictoryCount)
            {
                //防封等待
                Anti.RandomDelay();
                if (Anti.ShouldTriggerRandomYysAuto())//判断是否需要穿插纸人
                {
                    Fast.Click(1046, 651, 1077, 684); //打开小纸人
                    Sleep(500);
                    int do_Count = Random.Shared.Next(1, 8); // 1-7次随机次数
                    if (Tmp.Do_YysAuto(do_Count))
                    {
                        Config.VictoryCount += do_Count;
                        log.Info($"触发随机穿插纸人战斗结束..脚本继续接管..");
                        Anti.ResetRandomYysAuto();
                    }
                    else Sleep(1000);
                }
                // 执行探查
                if (DoTanCha())
                {
                    Config.VictoryCount++;
                    log.Info_Green($"当前胜利次数：{Config.VictoryCount}/{Config.MaxVictoryCount}");
                }
            }
            log.Info("探查任务完成，退出到探索..");
            Sleep(1000);
            Fast.Click(34, 25, 74, 60);
            Fast.Click(34, 25, 74, 60);
            Sleep(3000);
        }

        private void HandleZhenMuShou()
        {
            EnterQiLingScene();

            if (UserConfig_Preset != null)
            {
                Sleep(1500);
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入式神录，开始应用预设{UserConfig_Preset}");
                Fast.Click("1039,571,1070,594");
                Sleep(1500);
                Tmp.Do_Preset(preset);
            }

            while (!_ct.Token.IsCancellationRequested)
            {
                // 检查战斗次数是否OK
                if (Config.VictoryCount >= Config.MaxVictoryCount)
                {
                    log.Info("战斗次数达到上限，退出");
                    break;
                }
                else
                    log.Info($"当前战斗次数：{Config.VictoryCount}/{Config.MaxVictoryCount}");

                // 检查战斗状态
                if (HasZhenMuShou())
                {
                    Anti.RandomDelay();//防封等待
                    if (Anti.ShouldTriggerRandomYysAuto())//判断是否需要穿插纸人
                    {
                        Fast.Click(873, 645, 895, 671); //打开小纸人
                        Sleep(500);
                        int do_Count = Random.Shared.Next(1, 5); // 1-4次随机次数
                        if (Tmp.Do_YysAuto(do_Count))
                        {
                            Config.VictoryCount += do_Count;
                            log.Info($"触发随机穿插纸人战斗结束..脚本继续接管..");
                            Anti.ResetRandomYysAuto();
                        }
                        else Sleep(1000);
                    }
                    if (Db.PendingTimerTask) //执行定时任务
                    {
                        Db.PendingTimerTask = false;
                        log.Info("暂停当前任务，执行定时任务，退出到探索..");
                        Exit();
                        Db?.TimerTask?.DoAllTask();
                        Sleep(1000);
                        throw new Exception("定时任务执行结束，重新执行当前的主任务..");
                    }
                    Tmp.Do_ClearYuHun(); //执行临时执行御魂

                    if (FightZhenMuShou())
                    {
                        if (IsBuZhuo)
                        {
                            Config.CaptureSuccessCount++;
                            IsBuZhuo = false;
                        }
                        Config.VictoryCount++;
                        log.Info_Green($"当前胜利次数：{Config.VictoryCount}，当前捕捉成功次数：{Config.CaptureSuccessCount}");
                    }
                    continue;
                }

                // 处理召唤逻辑
                if (HasSummonCount())
                {
                    SummonZhenMuShou();
                    continue;
                }

                // 处理购买逻辑
                if (!Config.AutoBuySummon)
                {
                    log.Info("自动购买未开启，任务终止");
                    break;
                }

                if (!BuySummonCount())
                {
                    log.Info("购买次数失败，任务终止");
                    break;
                }

                // 添加循环延迟避免高频检测
                Sleep(1000);
            }
            log.Info("契灵任务完成，退出到探索..");
            Exit();
        }

        #region 具体操作方法

        private bool BiaoJi_Status = false;

        private List<string> DontSendLog = ["标记"];

        private bool BuySummonCount()
        {
            log.Info("购买鸣契石...进入商店..");
            Fast.Click(269, 639, 321, 678);
            Sleep(1500);
            log.Info("OCR识别商店中的商品..[建议安装内置插件进行识别]");
            var str = Fast.Ocr_String(328, 416, 583, 462);
            if (!str.Contains("鸣"))
            {
                log.Info("商店中没有鸣契石，点击返回");
                Sleep(800);
                Fast.Click(22, 9, 63, 43);
                Sleep(1500);
                return false;
            }

            log.Info("点击兑换鸣契石..");
            Fast.Click(499, 479, 574, 508);
            //等待 购买鸣契石
            Colors["购买鸣契石"].Await(null, 3000);
            Fast.Click(765, 465, 805, 500);
            Sleep(500);
            Fast.Click(581, 560, 707, 599);
            Sleep(1500);
            //还是 购买鸣契石
            if (Colors["购买鸣契石"].Await(null, 3000))
            {
                log.Info("购买失败，可能是没有钱了..退出");
                Fast.Click(948, 84, 1076, 136);
                Sleep(1500);
            }
            Sleep(1000);
            Fast.Click(22, 9, 63, 43);
            //50% 再带点一次
            if (new Random().Next(0, 100) < 50)
                Fast.Click(22, 9, 63, 43);
            log.Info("点击返回");
            Fast.Click(22, 9, 63, 43);
            Sleep(1500);
            log.Info("重新获取召唤次数..");
            if (!HasSummonCount())
            {
                log.Info("获取召唤次数失败，退出");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            var Lp_Pics = Mp.Filter("场景_罗盘");
            int Lp_Count = 0;

            log.Info("战斗开始...");
            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置
            while (!isbreak)
            {
                Sleep(500);
                //罗盘判断
                if (Lp_Pics.FindAll())
                {
                    Lp_Count++;
                    log.Debug($"罗盘识别次数：{Lp_Count}/5");
                    if (Lp_Count >= 5)
                    {
                        log.Info("卡住，疑似罗盘数量为0，手动选择千-罗盘。");
                        Fast.Click(607, 423, 707, 515);
                        Sleep(1000);
                        Lp_Count = 0;
                    }
                }

                var p = ZhanDou_Pics.FindAllEa();
                if (p is null) continue;
                FindOkFun(p.Name, p);
                if (!DontSendLog.Any(p.Name.Contains)) log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("成功") || p.Name.Contains("达摩"))
                {
                    ret_bol = true;
                    isbreak = true;
                    IsBuZhuo = true;
                    continue;
                }
                if (p.Name.Contains("胜利") || p.Name.Contains("失败"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
            }
            if (ret_bol)
                Combat_End();//等待契灵界面

            return ret_bol;
        }

        /// <summary>
        /// 契灵胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(500);
                if (Mp.Filter("场景_结契").FindAll() || Scene.NowScene == "契灵")
                {
                    isbreak = true;
                    continue;
                }
                var p = ZhanDou_Pics.FindAllEa();
                if (p is null) continue;
                log.Info($"执行点击：{p._Name}");
                p.Click();
            }
        }

        private bool DoTanCha()
        {
            Fast.Click(1151, 599, 1219, 655);
            Sleep(1000);
            return Combat();
        }

        private void EnterQiLingScene()
        {
            string nows = Scene.NowScene;
            if (nows != "契灵")
            {
                //先去探索，获取突破卷数量
                if (!Scene.TO.TanSuo())
                {
                    log.Warn("契灵任务无法继续，当前游戏所在场景未知，请调整到庭院或探索主界面开始脚本！");
                    return;
                }
                var tupo_Count = Fast.Scence.TanSuo_GetTuPoCount();
                log.Debug("本地Ocr识别突破卷结果：" + tupo_Count);
                if (tupo_Count == 30)
                    Tmp.Do_Tupo(); //执行临时执行突破

                //再去契灵
                Scene.TO.QiLing();
            }
        }

        /// <summary>
        /// 是否捕捉了契灵-镇墓兽
        /// </summary>
        private bool FightZhenMuShou()
        {
            //是否不在 场景_结契 中
            if (!Mp.Filter("场景_结契").FindAll())
            {
                return false;
            }
            Fast.Click(1159, 592, 1228, 660);
            Sleep(1500);
            if (Colors["千咒式盘上限提示"].Find(null))
            {
                log.Info("关闭千咒式盘上限的提示");
                Fast.Click(718, 411, 811, 450);
                Sleep(1000);
            }
            return Combat();
        }

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            if (Config.BiaoJi && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.2秒，标记位置：5号位");
                Sleep(200);
                Fast.Click("1004,527,1032,579");
                return false;
            }
            return true;
        }

        private bool HasSummonCount()
        {
            var str = Fast.Ocr_Local(839, 24, 941, 57);
            if (string.IsNullOrEmpty(str))
            {
                log.Warn("无法识别召唤次数");
                return false;
            }

            var parts = str.Split('/');
            if (parts.Length != 2)
            {
                log.Warn("召唤次数格式错误");
                return false;
            }

            if (!int.TryParse(parts[0], out int count))
            {
                log.Warn("召唤次数解析失败");
                return false;
            }

            Config.RemainingSummonCount = count;
            log.Info($"当前剩余鸣契石：{count}");
            return count > 0;
        }

        private bool HasZhenMuShou()
        {
            //如果在结契场景直接True
            if (Mp.Filter("场景_结契").FindAll())
            {
                log.Info("在镇墓兽_结契场景中，开始战斗");
                return true;
            }

            int x, y;
            if (ZhenMuShou_Pics.FindAll(out x, out y))
            {
                log.Info("发现镇墓兽，点击镇墓兽位置");
                Fast.Click(x, y);
                Sleep(1000);
                //等待 场景_结契
                if (Mp.Filter("场景_结契").Wait(3))
                {
                    log.Info("进入镇墓兽_结契场景");
                    return true;
                }
                else return HasZhenMuShou();
            }
            return false;
        }

        private void SummonZhenMuShou()
        {
            if (Config.RemainingSummonCount <= 0)
            {
                log.Info("召唤次数不足，召唤操作结束..");
                return;
            }
            log.Info("召唤镇墓兽...");
            Fast.Click(1166, 476, 1203, 516);
            Sleep(1500);
            //判断是否还是契灵场景
            if (Scene.NowScene == "契灵")
            {
                log.Info("召唤失败，可能达到场景契灵上限，继续执行任务..");
                return;
            }
            log.Info("选择镇墓兽...");
            Fast.Click(945, 286, 1035, 401);
            Sleep(500);
            log.Info("点击最大上限...");
            Fast.Click(770, 542, 806, 580);
            Sleep(500);
            log.Info("点击召唤...");
            Fast.Click(568, 623, 721, 655);
            Sleep(1500);
            log.Info("点击确定...");
            Fast.Click(705, 413, 814, 449);
            Sleep(1500);
            Config.RemainingSummonCount--;
            SummonZhenMuShou();
        }

        #endregion 具体操作方法
    }
}