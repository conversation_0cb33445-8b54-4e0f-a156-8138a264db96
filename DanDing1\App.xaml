﻿<Application
    x:Class="DanDing1.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:converters="clr-namespace:DanDing1.Converters"
    xmlns:helpers="clr-namespace:DanDing1.Helpers"
    DispatcherUnhandledException="OnDispatcherUnhandledException"
    Exit="OnExit"
    Startup="OnStartup">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ui:ThemesDictionary Theme="Light"/>
                <ui:ControlsDictionary/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 全局转换器 -->
            <converters:EmptyStringToVisibilityConverter x:Key="EmptyStringToVisibilityConverter"/>
            <helpers:NullToBoolConverter x:Key="NullToBoolConverter"/>
            <helpers:TimeSpanWithDaysConverter x:Key="TimeSpanWithDaysConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
