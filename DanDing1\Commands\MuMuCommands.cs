using XHelper;
using ScriptEngine.MuMu;
using System.Collections.ObjectModel;
using DanDing1.Models;

namespace DanDing1.Commands
{
    /// <summary>
    /// MuMu模拟器相关命令处理类
    /// </summary>
    internal class MuMuCommands : BaseCommand
    {
        public override void Execute(string[] parameters)
        {
            if (!ValidateParameterCount(parameters, 3))
                return;

            if (!int.TryParse(parameters[2], out int testNumber))
            {
                XLogger.Error("MuMu命令参数必须是数字");
                return;
            }

            switch (testNumber)
            {
                case 1:
                    CheckMuMuPath();
                    break;
                case 2:
                    ShowMuMuInstances();
                    break;
                case 3:
                    ShowLocalMuMuConfigs();
                    break;
                default:
                    XLogger.Error($"未知的MuMu测试项目: {testNumber}");
                    break;
            }
        }

        /// <summary>
        /// 检查MuMu路径配置
        /// </summary>
        private void CheckMuMuPath()
        {
            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (string.IsNullOrEmpty(path))
            {
                XLogger.Warn("MuMu路径未设置");
            }
            else
            {
                XLogger.Info($"MuMu路径已设置: {path}");
            }
        }

        /// <summary>
        /// 显示MuMu实例信息
        /// </summary>
        private void ShowMuMuInstances()
        {
            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (string.IsNullOrEmpty(path))
            {
                XLogger.Error("请先设置MuMu路径");
                return;
            }

            MuMu mumu = new();
            if (!mumu.Init(path))
            {
                XLogger.Error("MuMu初始化失败");
                return;
            }

            var instances = mumu._GetInstances();
            if (instances == null || instances.Instances.Count == 0)
            {
                XLogger.Warn("未找到任何MuMu实例");
                return;
            }

            XLogger.Info($"找到 {instances.Instances.Count} 个MuMu实例:");
            foreach (var instance in instances.Instances)
            {
                XLogger.Info(instance.Value.ToString());
            }
        }

        /// <summary>
        /// 显示本地MuMu配置信息
        /// </summary>
        private void ShowLocalMuMuConfigs()
        {
            var configs = XConfig.LoadValueFromFile<ObservableCollection<SimulatorConfig>>("MuMuConfigs");
            if (configs == null || configs.Count == 0)
            {
                XLogger.Warn("未找到本地MuMu配置信息");
                return;
            }

            XLogger.Info($"找到 {configs.Count} 个本地MuMu配置:");
            foreach (var config in configs)
            {
                XLogger.Info(config.ToString());
            }
        }
    }
}