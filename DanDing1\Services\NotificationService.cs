using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using DanDing1.Models;

namespace DanDing1.Services
{
    /// <summary>
    /// 通知服务 - 管理应用程序通知
    /// </summary>
    public class NotificationService
    {
        public ObservableCollection<NotificationModel> Notifications { get; } = new ObservableCollection<NotificationModel>();

        // 保存定时器引用，避免被垃圾回收
        private readonly Dictionary<NotificationModel, System.Threading.Timer> _timers = new Dictionary<NotificationModel, System.Threading.Timer>();

        // 单例模式
        private static NotificationService _instance;
        private static readonly object _lock = new object();

        public static NotificationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new NotificationService();
                        }
                    }
                }
                return _instance;
            }
        }

        private NotificationService() { }

        /// <summary>
        /// 显示通知
        /// </summary>
        /// <param name="message">通知消息</param>
        /// <param name="title">通知标题（可选）</param>
        /// <param name="type">通知类型</param>
        /// <param name="durationMs">显示时长（毫秒），0表示不自动关闭</param>
        public void Show(string message, string title = "", NotificationType type = NotificationType.Info, int durationMs = 3000)
        {
            var notification = new NotificationModel
            {
                Message = message,
                Title = title,
                Type = type,
                CreatedAt = DateTime.Now
            };

            // 创建关闭命令
            notification.CloseCommand = new RelayCommand(param => CloseNotification(notification));

            // 在UI线程中添加通知
            Application.Current.Dispatcher.Invoke(() =>
            {
                Notifications.Add(notification);

                // 设置自动移除
                if (durationMs > 0)
                {
                    // 创建定时器并保存引用
                    var timer = new System.Threading.Timer(state =>
                    {
                        CloseNotification((NotificationModel)state);

                        // 移除并释放定时器
                        if (_timers.TryGetValue((NotificationModel)state, out var t))
                        {
                            t.Dispose();
                            _timers.Remove((NotificationModel)state);
                        }
                    }, notification, durationMs, System.Threading.Timeout.Infinite);

                    // 保存定时器引用
                    _timers[notification] = timer;
                }
            });
        }

        /// <summary>
        /// 关闭指定通知
        /// </summary>
        private void CloseNotification(NotificationModel notification)
        {
            if (notification == null) return;

            // 使用同步执行，确保通知被移除
            Application.Current.Dispatcher.Invoke(() =>
            {
                try
                {
                    if (Notifications.Contains(notification))
                    {
                        Notifications.Remove(notification);
                        System.Diagnostics.Debug.WriteLine($"通知已移除: {notification.Title}");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"关闭通知时发生错误: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 显示成功通知
        /// </summary>
        public void Success(string message, string title = "", int durationMs = 3000)
            => Show(message, title, NotificationType.Success, durationMs);

        /// <summary>
        /// 显示警告通知
        /// </summary>
        public void Warning(string message, string title = "", int durationMs = 3000)
            => Show(message, title, NotificationType.Warning, durationMs);

        /// <summary>
        /// 显示错误通知
        /// </summary>
        public void Error(string message, string title = "", int durationMs = 3000)
            => Show(message, title, NotificationType.Error, durationMs);

        /// <summary>
        /// 显示信息通知
        /// </summary>
        public void Info(string message, string title = "", int durationMs = 3000)
            => Show(message, title, NotificationType.Info, durationMs);
    }

    /// <summary>
    /// 简单的命令实现（可以替换为项目中已有的命令实现）
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action<object> _execute;
        private readonly Predicate<object> _canExecute;

        public RelayCommand(Action<object> execute, Predicate<object> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public bool CanExecute(object parameter) => _canExecute == null || _canExecute(parameter);
        public void Execute(object parameter) => _execute(parameter);
        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }
    }
}