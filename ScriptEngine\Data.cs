﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine
{
    /// <summary>
    /// 全局数据存储类
    /// </summary>
    internal class G_Data
    {
        private Dictionary<string, object> _data = new Dictionary<string, object>();

        /// <summary>
        /// 存储_data
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        public void Set(string key, object value)
        {
            if (_data.ContainsKey(key))
            {
                _data[key] = value;
            }
            else
            {
                _data.Add(key, value);
            }
        }

        /// <summary>
        /// 读取_data
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public object Get(string key)
        {
            if (_data.ContainsKey(key))
            {
                return _data[key];
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 读取_data
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        public T? Get<T>(string key)
        {
            if (_data.ContainsKey(key))
            {
                return (T)_data[key];
            }
            else
            {
                return default(T);
            }
        }

        /// <summary>
        /// 删除指定键的数据
        /// </summary>
        /// <param name="key">要删除的键</param>
        /// <returns>如果成功删除返回true，如果键不存在返回false</returns>
        public bool Remove(string key)
        {
            if (_data.ContainsKey(key))
            {
                _data.Remove(key);
                return true;
            }
            return false;
        }
    }
}