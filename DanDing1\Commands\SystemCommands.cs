using XHelper;

namespace DanDing1.Commands
{
    /// <summary>
    /// 系统相关命令处理类
    /// </summary>
    internal class SystemCommands : BaseCommand
    {
        public override void Execute(string[] parameters)
        {
            if (!ValidateParameterCount(parameters, 2))
                return;

            switch (parameters[1].ToLower())
            {
                case "获取图库":
                    HandleFetchPicsCommand();
                    break;
                case "log":
                case "日志":
                    HandleLogCommand();
                    break;
                default:
                    XLogger.Error($"未知的系统命令: {parameters[1]}");
                    break;
            }
        }

        /// <summary>
        /// 处理获取图库命令
        /// </summary>
        private void HandleFetchPicsCommand()
        {
            new Test_InitPics().Start(GlobalData.Instance.appConfig.dNet.System.GetCurrentHost());
            XLogger.Info("获取图库指令执行完成..(后台可能有正在执行的线程)");
        }

        /// <summary>
        /// 处理日志命令
        /// </summary>
        private void HandleLogCommand()
        {
            XLogger.Info("这是正常信息.");
            XLogger.Warn("这是警告信息.");
            XLogger.Error("这是错误信息.");
            XLogger.Info_Green("这是绿色的正常信息.");
        }
    }
}