﻿using DamoControlKit.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DamoControlKit.Interface
{
    /// <summary>
    /// 查找功能 接口
    /// </summary>
    interface IFind : IDmInterface
    {
        /// <summary>
        /// 查找一次
        /// </summary>
        /// <param name="x">大漠对象</param>
        /// <returns>查找结果</returns>
        bool Find(dmsoft? x);

        /// <summary>
        /// 查找坐标
        /// </summary>
        /// <param name="x">大漠对象</param>
        /// <returns>坐标</returns>
        Point? FindPoint(dmsoft? x);

        /// <summary>
        /// 等待出现
        /// </summary>
        /// <param name="x">大漠对象</param>
        /// <param name="period">超时时间</param>
        /// <returns>超时未等到 返回False 等到立即返回True</returns>
        bool Await(dmsoft? x, int period);

    }
}
