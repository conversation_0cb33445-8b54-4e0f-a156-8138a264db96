﻿<Page
    x:Class="DanDing1.Views.Pages.LoadPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:DanDing1.Views.Pages"
    xmlns:localcs="clr-namespace:DanDing1.ViewModels.Pages"
    xmlns:root="clr-namespace:DanDing1"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:converters="clr-namespace:DanDing1.Helpers"
    Title="LoadPage"
    d:DataContext="{d:DesignInstance local:LoadPage,
    IsDesignTimeCreatable=False}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
    ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    ScrollViewer.CanContentScroll="False"
    mc:Ignorable="d">
    <Page.Resources>
        <localcs:ViewVisibilityConverter x:Key="ViewVisibilityConverter" />
        <localcs:LoginStatusToBoolConverter x:Key="LoginStatusToBoolConverter" />
        <localcs:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <converters:MinutesToHoursConverter x:Key="MinutesToHoursConverter" />
        <converters:NumberToKConverter x:Key="NumberToKConverter" />
        <converters:InfoBarSeverityConverter x:Key="InfoBarSeverityConverter" />
    </Page.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition />
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>

        <StackPanel
            x:Name="LoadScreen"
            Grid.ColumnSpan="2"
            HorizontalAlignment="Left">
            <TabControl
                Width="710"
                Height="265"
                Margin="0,5,0,0"
                IsEnabled="{Binding ViewModel.Netisok}"
                SelectedIndex="{Binding ViewModel.Select_index}">
                <!--  登录  -->
                <TabItem Width="85"
                         Visibility="{Binding ViewModel.Show_Login, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=未登录}">
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Margin="0,0,6,0"
                                           Symbol="PersonLink20" />
                            <TextBlock Padding="5,0,5,0"
                                       Text="登录" />
                        </StackPanel>
                    </TabItem.Header>
                    <StackPanel Margin="0,20,0,0"
                                HorizontalAlignment="Center">
                        <StackPanel VerticalAlignment="Top"
                                    Orientation="Horizontal">
                            <ui:TextBlock
                                Width="70"
                                Margin="12"
                                Text="用户名：" />
                            <ui:TextBox
                                Width="200"
                                Height="40"
                                FontSize="16"
                                Text="{Binding ViewModel.Load_Username}" />
                        </StackPanel>
                        <StackPanel
                            Margin="0,5,0,0"
                            VerticalAlignment="Top"
                            Orientation="Horizontal">
                            <ui:TextBlock
                                Width="70"
                                Margin="12"
                                Text="密码：" />
                            <ui:PasswordBox
                                x:Name="登录"
                                Width="200"
                                Height="40"
                                FontSize="16"
                                KeyDown="TextBox_KeyDown"
                                Password="{Binding ViewModel.Load_Password, Mode=TwoWay}" />
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    Orientation="Horizontal">
                            <ui:Button
                                Width="75"
                                Height="40"
                                Margin="0,20,0,0"
                                Command="{Binding ViewModel.LoginUserCommand}"
                                Content="登录"
                                FontSize="16" />
                            <ui:Button
                                Width="95"
                                Height="40"
                                Margin="10,20,0,0"
                                Command="{Binding ViewModel.FreeUserCommand}"
                                Content="每日试用"
                                FontSize="16" />
                        </StackPanel>
                    </StackPanel>
                </TabItem>
                <!--  注册  -->
                <TabItem Width="85"
                         Visibility="{Binding ViewModel.Show_Login, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=未登录}">
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Margin="0,0,6,0"
                                           Symbol="VideoPersonSparkle20" />
                            <ui:TextBlock Padding="5,0,5,0"
                                          Text="注册" />
                        </StackPanel>
                    </TabItem.Header>
                    <Grid Margin="0,20,0,0">
                        <StackPanel Margin="0,5,0,0"
                                    HorizontalAlignment="Center">
                            <StackPanel VerticalAlignment="Top"
                                        Orientation="Horizontal">
                                <ui:TextBlock
                                    Width="42"
                                    Margin="12"
                                    Text="用户名：" />
                                <ui:TextBox
                                    Width="200"
                                    Height="40"
                                    FontSize="16"
                                    Text="{Binding ViewModel.Reg_Username}" />

                                <ui:TextBlock
                                    Width="42"
                                    Margin="12"
                                    Text="  密码：" />
                                <ui:PasswordBox
                                    Width="200"
                                    Height="40"
                                    FontSize="16"
                                    Password="{Binding ViewModel.Reg_Password, Mode=TwoWay}" />
                            </StackPanel>
                            <StackPanel
                                Margin="0,5,0,0"
                                VerticalAlignment="Top"
                                Orientation="Horizontal">
                                <ui:TextBlock
                                    Width="42"
                                    Margin="12"
                                    Text="卡密：" />
                                <ui:PasswordBox
                                    Width="200"
                                    Height="40"
                                    FontSize="16"
                                    Password="{Binding ViewModel.Reg_Kami, Mode=TwoWay}" />

                                <ui:TextBlock
                                    Width="42"
                                    Margin="12"
                                    Text="  邮箱：" />
                                <ui:TextBox
                                    Name="EmailText"
                                    Width="200"
                                    Height="40"
                                    FontSize="14"
                                    Text="{Binding ViewModel.Reg_Email}" />
                            </StackPanel>

                            <StackPanel Margin="0,5,0,0"
                                        Orientation="Horizontal">
                                <StackPanel HorizontalAlignment="Left"
                                            Orientation="Horizontal">
                                    <ui:TextBlock
                                        Width="50"
                                        Margin="4,12,12,12"
                                        Text="  验证码：" />
                                    <ui:TextBox
                                        Name="CodeText"
                                        Width="95"
                                        Height="40"
                                        Margin="0,0,5,0"
                                        FontSize="16"
                                        Text="{Binding ViewModel.Reg_Code}" />
                                    <ui:Button
                                        Height="40"
                                        Margin="0,0,5,0"
                                        Command="{Binding ViewModel.RegSendEmailCodeCommand}"
                                        CommandParameter="{Binding Text, ElementName=EmailText}"
                                        Content="发送验证码"
                                        Icon="Fluent24"
                                        IsEnabled="{Binding ViewModel.RegSendCodeButton}" />
                                </StackPanel>
                                <ui:Button
                                    Width="75"
                                    Height="40"
                                    Margin="192,0,0,0"
                                    Command="{Binding ViewModel.RegCommand}"
                                    CommandParameter="{Binding Text, ElementName=CodeText}"
                                    Content="注册"
                                    Icon="Fluent24" />
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  修改密码  -->
                <TabItem Width="95"
                         Visibility="{Binding ViewModel.Show_Login, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=未登录}">
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Margin="0,0,6,0"
                                           Symbol="ShieldPerson20" />
                            <ui:TextBlock Padding="5,0,5,0"
                                          Text="修改密码" />
                        </StackPanel>
                    </TabItem.Header>
                    <Grid>
                        <StackPanel Margin="0,25,0,0"
                                    HorizontalAlignment="Center">
                            <StackPanel VerticalAlignment="Top"
                                        Orientation="Horizontal">
                                <ui:TextBlock
                                    Width="42"
                                    Margin="12"
                                    Text="用户名：" />
                                <ui:TextBox
                                    Width="200"
                                    Height="40"
                                    FontSize="16"
                                    Text="{Binding ViewModel.Change_Username}" />

                                <ui:TextBlock
                                    Width="42"
                                    Margin="12"
                                    Text="现密码：" />
                                <ui:PasswordBox
                                    Width="200"
                                    Height="40"
                                    FontSize="16"
                                    Password="{Binding ViewModel.Change_OldPassword, Mode=TwoWay}" />
                            </StackPanel>
                            <StackPanel
                                Margin="0,5,0,0"
                                VerticalAlignment="Top "
                                Orientation="Horizontal">
                                <ui:TextBlock
                                    Width="42"
                                    Margin="12"
                                    Text="新密码：" />
                                <ui:PasswordBox
                                    Width="200"
                                    Height="40"
                                    FontSize="16"
                                    Password="{Binding ViewModel.Change_NewPassword, Mode=TwoWay}" />

                                <ui:TextBlock
                                    Width="42"
                                    Margin="12"
                                    Text="重新输：" />
                                <ui:PasswordBox
                                    Width="200"
                                    Height="40"
                                    FontSize="16"
                                    Password="{Binding ViewModel.Change_NewPassword_Again, Mode=TwoWay}" />
                            </StackPanel>
                            <StackPanel HorizontalAlignment="Right"
                                        Orientation="Horizontal">
                                <ui:Button
                                    Height="40"
                                    Margin="0,5,0,0"
                                    Command="{Binding ViewModel.ChangePasswordCommand}"
                                    Content="修改密码"
                                    FontSize="16" />
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  忘记密码  -->
                <TabItem Width="95"
                         Visibility="{Binding ViewModel.Show_Login, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=未登录}">
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Margin="0,0,6,0"
                                           Symbol="PersonQuestionMark16" />
                            <ui:TextBlock Padding="5,0,5,0"
                                          Text="忘记密码" />
                        </StackPanel>
                    </TabItem.Header>
                    <Grid>
                        <StackPanel
                            HorizontalAlignment="Center">
                            <StackPanel
                                Margin="-5,20,0,0"
                                VerticalAlignment="Top"
                                Orientation="Horizontal">
                                <ui:TextBlock
                                    Width="48"
                                    Margin="12"
                                    Text="用户名： " />
                                <ui:TextBox
                                    Width="200"
                                    Height="40"
                                    FontSize="16"
                                    Text="{Binding ViewModel.Reset_Username}" />
                            </StackPanel>
                            <StackPanel
                                Margin="0,5,0,0"
                                VerticalAlignment="Top"
                                Orientation="Horizontal">
                                <ui:TextBlock
                                    Width="42"
                                    Margin="12"
                                    Text="  邮箱：" />
                                <ui:TextBox
                                    x:Name="Reset_Email"
                                    Width="200"
                                    Height="40"
                                    FontSize="16"
                                    Text="{Binding ViewModel.Reset_Email}" />
                            </StackPanel>
                            <StackPanel
                                Margin="0,5,0,0"
                                HorizontalAlignment="Left"
                                Orientation="Horizontal">
                                <ui:TextBlock
                                    Width="50"
                                    Margin="4,12,12,12"
                                    Text="  验证码：" />
                                <ui:TextBox
                                    Width="100"
                                    Height="40"
                                    Margin="0,0,5,0"
                                    FontSize="16"
                                    Text="{Binding ViewModel.Reset_Code}" />
                                <ui:Button
                                    Height="40"
                                    Margin="0,0,5,0"
                                    Command="{Binding ViewModel.ResetSendEmailCodeCommand}"
                                    CommandParameter="{Binding Text, ElementName=Reset_Email}"
                                    Content="发送验证码"
                                    Icon="Fluent24"
                                    IsEnabled="{Binding ViewModel.ResetSendCodeButton}" />
                            </StackPanel>
                            <StackPanel HorizontalAlignment="Right"
                                        Orientation="Horizontal">
                                <ui:Button
                                    Height="40"
                                    Margin="0,3,5,0"
                                    Command="{Binding ViewModel.ResetPasswordCommand}"
                                    CommandParameter="{Binding Text, ElementName=Reset_Email}"
                                    Content="重置密码"
                                    FontSize="16" />
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  账号信息  -->
                <TabItem Width="95"
                         Visibility="{Binding ViewModel.Show_Login, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=已登录}">
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Margin="0,0,6,0"
                                           Symbol="Person20" />
                            <ui:TextBlock Padding="5,0,5,0"
                                          Text="账号信息" />
                        </StackPanel>
                    </TabItem.Header>
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <Grid Margin="20,20,20,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!-- 左侧用户基本信息 -->
                            <StackPanel Grid.Column="0"
                                        Margin="0,0,20,0">
                                <DockPanel LastChildFill="True">
                                    <ui:Button DockPanel.Dock="Right"
                                               Margin="10,0,0,0"
                                               Command="{Binding ViewModel.OutLoginCommand}"
                                               Content="退出登录" />
                                    <TextBlock Text="{Binding ViewModel.Info_UserName}"
                                               FontSize="24"
                                               FontWeight="SemiBold"
                                               Margin="0,0,0,10"
                                               MaxWidth="200"
                                               TextTrimming="CharacterEllipsis" />
                                </DockPanel>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="100" />
                                        <ColumnDefinition Width="200" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="30" />
                                        <RowDefinition Height="30" />
                                        <RowDefinition Height="30" />
                                        <RowDefinition Height="30" />
                                        <RowDefinition Height="30" />
                                        <RowDefinition Height="30" />
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0"
                                               Grid.Column="0"
                                               Text="用户ID："
                                               VerticalAlignment="Center" />
                                    <StackPanel  Grid.Row="0" Grid.Column="1" Orientation="Horizontal">
                                        <TextBlock
                                            Text="{Binding ViewModel.Info_Id}"
                                            VerticalAlignment="Center" />

                                        <TextBlock Margin="10 0 0 0"
                                                   Text="（"
                                                   VerticalAlignment="Center" />
                                        <TextBlock
                                            Text="{Binding ViewModel.Info_Status}"
                                            VerticalAlignment="Center" />
                                        <TextBlock
                                            Text="）"
                                            VerticalAlignment="Center" />
                                    </StackPanel>

                                    <TextBlock Grid.Row="1"
                                               Grid.Column="0"
                                               Text="用户积分："
                                               VerticalAlignment="Center" />
                                    <StackPanel Grid.Row="1"
                                                Grid.Column="1"
                                                Orientation="Horizontal">
                                        <TextBlock Text="{Binding ViewModel.Info_Points}"
                                                   VerticalAlignment="Center" />
                                        <ui:Button Margin="10,0,0,0"
                                                   Padding="-5"
                                                   Width="30"
                                                   Appearance="Secondary"
                                                   Command="{Binding ViewModel.ShowUserRightsRecordsCommand}"
                                                   ToolTip="查看账号权益">
                                            <ui:SymbolIcon Margin="-2 0 0 0" FontSize="22" Symbol="PremiumPerson20" />
                                        </ui:Button>
                                        <ui:Button Margin="3,0,0,0"
                                                   Padding="-5"
                                                   Width="30"
                                                   Appearance="Secondary"
                                                   Command="{Binding ViewModel.ShowPointsRecordsCommand}"
                                                   ToolTip="查看积分记录">
                                            <ui:SymbolIcon Margin="-2 0 0 0" FontSize="22" Symbol="DocumentText20" />
                                        </ui:Button>
                                    </StackPanel>

                                    <TextBlock Grid.Row="2"
                                               Grid.Column="0"
                                               Text="邮箱："
                                               VerticalAlignment="Center" />
                                    <TextBlock Grid.Row="2"
                                               Grid.Column="1"
                                               Text="{Binding ViewModel.Info_Email}"
                                               VerticalAlignment="Center" />

                                    <TextBlock Grid.Row="3"
                                               Grid.Column="0"
                                               Text="登录IP："
                                               VerticalAlignment="Center" />
                                    <TextBlock Grid.Row="3"
                                               Grid.Column="1"
                                               Text="{Binding ViewModel.Info_Ip}"
                                               VerticalAlignment="Center" />

                                    <TextBlock Grid.Row="4"
                                               Grid.Column="0"
                                               Text="到期时间："
                                               VerticalAlignment="Center" />
                                    <TextBlock Grid.Row="4"
                                               Grid.Column="1"
                                               Text="{Binding ViewModel.Info_Viptime}"
                                               VerticalAlignment="Center" />
                                </Grid>
                            </StackPanel>

                            <!-- 右侧欢迎消息 -->
                            <Border Grid.Column="1"
                                    BorderThickness="1,0,0,0"
                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    Padding="20,0,0,0">
                                <StackPanel>
                                    <TextBlock Text="{Binding ViewModel.WelcomeText}"
                                               MouseDown="TextBlock_MouseDown"
                                               FontFamily="Comic Sans MS"
                                               FontSize="15"
                                               FontStyle="Italic"
                                               FontWeight="Bold"
                                               TextWrapping="Wrap"
                                               VerticalAlignment="Top"
                                               Margin="0,0,0,10" />

                                    <Grid Margin="0,0,0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="0"
                                                   Text="累积开始任务次数："
                                                   Margin="0,0,10,5" />
                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="1"
                                                   Text="{Binding Source={x:Static root:GlobalData.Instance}, Path=UserProfile.TotalTaskStartCount}"
                                                   Margin="0,0,0,5" />

                                        <TextBlock Grid.Row="1"
                                                   Grid.Column="0"
                                                   Text="累积脚本点击次数："
                                                   Margin="0,0,10,5" />
                                        <TextBlock Grid.Row="1"
                                                   Grid.Column="1"
                                                   Text="{Binding Source={x:Static root:GlobalData.Instance},
                                                   Path=UserProfile.TotalScriptClickCount,
                                                   Converter={StaticResource NumberToKConverter}}"
                                                   Margin="0,0,0,5" />

                                        <TextBlock Grid.Row="2"
                                                   Grid.Column="0"
                                                   Text="累积脚本运行时长："
                                                   Margin="0,0,10,5" />
                                        <TextBlock Grid.Row="2"
                                                   Grid.Column="1"
                                                   Text="{Binding Source={x:Static root:GlobalData.Instance}, Path=UserProfile.TotalScriptRunningMinutes, Converter={StaticResource MinutesToHoursConverter}}"
                                                   Margin="0,0,0,5" />

                                        <TextBlock Grid.Row="3"
                                                   Grid.Column="0"
                                                   Text="累积处理悬赏次数："
                                                   Margin="0,0,10,5" />
                                        <TextBlock Grid.Row="3"
                                                   Grid.Column="1"
                                                   Text="{Binding Source={x:Static root:GlobalData.Instance}, Path=UserProfile.TotalBountyCount}"
                                                   Margin="0,0,0,5" />

                                        <TextBlock Grid.Row="4"
                                                   Grid.Column="0"
                                                   Text="最长脚本运行时长："
                                                   Margin="0,0,10,5" />
                                        <TextBlock Grid.Row="4"
                                                   Grid.Column="1"
                                                   Text="{Binding Source={x:Static root:GlobalData.Instance}, Path=UserProfile.LongestScriptRunningMinutes, Converter={StaticResource MinutesToHoursConverter}}"
                                                   Margin="0,0,0,5" />
                                    </Grid>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </ScrollViewer>
                </TabItem>
                <!--  卡密充值  -->
                <TabItem Width="95"
                         GotFocus="KamiTab_GotFocus">
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Margin="0,0,6,0"
                                           Symbol="PremiumPerson20" />
                            <ui:TextBlock Padding="5,0,5,0"
                                          Text="卡密充值" />
                        </StackPanel>
                    </TabItem.Header>
                    <Grid>
                        <StackPanel Margin="0,20,0,0"
                                    HorizontalAlignment="Center">
                            <StackPanel
                                Margin="0,5,0,0"
                                VerticalAlignment="Top"
                                Orientation="Horizontal">
                                <ui:TextBlock Margin="6,12,12,12"
                                              Text="用户名：" />
                                <ui:TextBox
                                    Width="200"
                                    Height="40"
                                    FontSize="16"
                                    IsReadOnly="{Binding ViewModel.Show_Login, Converter={StaticResource LoginStatusToBoolConverter}}"
                                    Text="{Binding ViewModel.UseKami_User}" />
                            </StackPanel>
                            <StackPanel
                                Margin="0,5,0,0"
                                VerticalAlignment="Top"
                                Orientation="Horizontal">
                                <ui:TextBlock Margin="-21,12,12,12"
                                              Text="重复用户名：" />
                                <ui:TextBox
                                    Width="200"
                                    Height="40"
                                    FontSize="16"
                                    IsReadOnly="{Binding ViewModel.Show_Login, Converter={StaticResource LoginStatusToBoolConverter}}"
                                    Text="{Binding ViewModel.UseKami_User_Again}" />
                            </StackPanel>
                            <StackPanel
                                Margin="0,5,0,0"
                                VerticalAlignment="Top"
                                Orientation="Horizontal">
                                <ui:TextBlock Margin="22,12,12,12"
                                              Text="卡密：" />
                                <ui:PasswordBox
                                    Width="200"
                                    Height="40"
                                    FontSize="16"
                                    Password="{Binding ViewModel.UseKami_Kami, Mode=TwoWay}" />
                            </StackPanel>
                            <StackPanel HorizontalAlignment="Right"
                                        Orientation="Horizontal">
                                <ui:HyperlinkButton VerticalAlignment="Center"
                                                    Margin="0,3,10,0"
                                                    Content="购买入口"
                                                    NavigateUri="https://shop.danding.vip" />
                                <ui:Button
                                    Height="40"
                                    Margin="0,3,0,0"
                                    Command="{Binding ViewModel.UseKamiCommand}"
                                    Content="卡密充值"
                                    FontSize="16" />
                            </StackPanel>
                        </StackPanel>
                        <Border Width="150"
                                Height="100"
                                VerticalAlignment="Bottom"
                                HorizontalAlignment="Right"
                                Visibility="{Binding ViewModel.ShowActivityButton, Converter={StaticResource BooleanToVisibilityConverter}}"
                                Background="{DynamicResource ApplicationBackgroundBrush}"
                                BorderBrush="{DynamicResource ControlElevationBorderBrush}"
                                BorderThickness="1"
                                CornerRadius="8"
                                Margin="10 10 10 10">
                            <StackPanel>
                                <ui:HyperlinkButton
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Margin="0 15 0 5"
                                    Content="限时-活动说明"
                                    NavigateUri="https://api.danding.vip/DD/activity?id=1" />
                                <ui:Button
                                    HorizontalAlignment="Center"
                                    Height="40"
                                    Command="{Binding ViewModel.ActivityDoCommand}"
                                    Content="{Binding ViewModel.ActivityButtonText}"
                                    IsEnabled="{Binding ViewModel.ActivityButtonEnabled}" />
                            </StackPanel>
                        </Border>
                    </Grid>
                </TabItem>
                <!--  云服务  -->
                <TabItem Width="95"
                         Visibility="{Binding ViewModel.Show_Login, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=已登录}">
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Margin="0,0,6,0"
                                           Symbol="Cloud20" />
                            <ui:TextBlock Padding="5,0,5,0"
                                          Text="云服务" />
                        </StackPanel>
                    </TabItem.Header>
                    <StackPanel Margin="20,20,0,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock VerticalAlignment="Center"
                                       Text="绑定的QQ号：" />
                            <TextBox IsReadOnly="True"
                                     Text="{Binding ViewModel.Qq_ID, Mode=OneWay}" />
                            <ui:Button
                                x:Name="WhyBingQQ"
                                Margin="4,0,0,0"
                                Width="30"
                                Height="30"
                                Padding="-10 0 -10 0"
                                ToolTip="为什么要绑定QQ？"
                                Click="WhyBingQQ_Click">
                                <ui:SymbolIcon Symbol="Question28" FontSize="18" FontWeight="Black" />
                            </ui:Button>

                            <ui:Button
                                x:Name="Bind_QQ"
                                Margin="4,0,0,0"
                                Click="Button_Click"
                                Content="点我绑定">
                                <ui:Button.Style>
                                    <Style TargetType="ui:Button" BasedOn="{StaticResource {x:Type ui:Button}}">
                                        <Setter Property="Visibility" Value="Collapsed" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding ViewModel.Qq_ID}" Value="未绑定">
                                                <Setter Property="Visibility" Value="Visible" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ui:Button.Style>
                            </ui:Button>
                        </StackPanel>
                        <StackPanel x:Name="WebTokenShow" Margin=" 0 10 0 0 " Visibility="{Binding ViewModel.ShowWebTokenControl, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <ui:TextBlock Text="★★蛋定邀请您体验云端日志查询功能★★" />
                            <StackPanel Margin=" -5 5 0 0" Orientation="Horizontal">
                                <ui:Button x:Name="SetWebTokenButton"
                                           Margin="5 0"
                                           Content="{Binding ViewModel.WebTokenButtonText}"
                                           IsEnabled="{Binding ViewModel.WebTokenButtonEnabled}"
                                           Command="{Binding ViewModel.SetWebTokenCommand}" />
                                <ui:Button Margin="5 0"
                                           Content="跳转到云端监控网页"
                                           Command="{Binding ViewModel.OpenWebLogPageCommand}" />
                            </StackPanel>
                            <ui:TextBlock Margin="0 5 0 0" VerticalAlignment="Center" Text="最新Web地址：https://web.danding.vip" />
                        </StackPanel>
                    </StackPanel>
                </TabItem>
            </TabControl>
        </StackPanel>

        <Grid
            Grid.Row="2"
            Grid.Column="1"
            Margin="0,-5,0,5"
            HorizontalAlignment="Left"
            VerticalAlignment="Bottom">
            <ui:TextBox
                Width="344"
                Height="235"
                AcceptsReturn="True"
                IsReadOnly="True"
                Text="{Binding ViewModel.Notice}"
                TextWrapping="Wrap" />
        </Grid>
        <Grid
            Grid.Row="2"
            Grid.Column="2"
            Margin="0,-5,0,5"
            HorizontalAlignment="Right"
            VerticalAlignment="Bottom">
            <ui:TextBox
                Width="344"
                Height="235"
                AcceptsReturn="True"
                IsReadOnly="True"
                Text="{Binding ViewModel.UpdataLog}"
                TextWrapping="Wrap" />
        </Grid>
        <StackPanel
            Grid.RowSpan="2"
            Grid.ColumnSpan="2"
            Margin="0,0,0,20"
            HorizontalAlignment="Center"
            VerticalAlignment="Bottom">
            <ui:InfoBar
                Title="{Binding ViewModel.InfoBar.Title}"
                IsOpen="{Binding ViewModel.InfoBar.IsOpen}"
                Message="{Binding ViewModel.InfoBar.Message}"
                Severity="{Binding ViewModel.InfoBar.Severity, Converter={StaticResource InfoBarSeverityConverter}, FallbackValue=Informational}" />
        </StackPanel>
    </Grid>
</Page>