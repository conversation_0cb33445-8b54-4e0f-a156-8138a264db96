using DanDing1.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Wpf.Ui.Controls;
using Wpf.Ui;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Wpf.Ui.Extensions;

namespace DanDing1.ViewModels.Pages
{
    /// <summary>
    /// 预设控件的ViewModel
    /// </summary>
    public partial class PresetControlViewModel : ObservableObject
    {
        private readonly PresetConfigManager _configManager;

        /// <summary>
        /// 所有预设配置
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<PresetConfig> _presetConfigs;

        /// <summary>
        /// 当前选中的预设配置
        /// </summary>
        [ObservableProperty]
        private PresetConfig? _selectedPresetConfig;

        /// <summary>
        /// 新预设配置名称
        /// </summary>
        [ObservableProperty]
        private string _newConfigName = string.Empty;

        /// <summary>
        /// 当前选中的预设参数
        /// </summary>
        [ObservableProperty]
        private PresetParameter? _selectedParameter;

        /// <summary>
        /// 新参数 - 套号
        /// </summary>
        [ObservableProperty]
        private int _newSetIndex = 1;

        /// <summary>
        /// 新参数 - 预设号
        /// </summary>
        [ObservableProperty]
        private int _newPresetIndex = 1;

        /// <summary>
        /// 可用的套号列表（1-8）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<int> _setIndexes;

        /// <summary>
        /// 可用的预设号列表（1-4）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<int> _presetIndexes;

        /// <summary>
        /// 对话框服务，用于显示各种对话框
        /// 可以在运行时重新赋值，以使用特定窗口作为对话框的所有者
        /// </summary>
        public IContentDialogService ContentDialogService { get; set; }

        /// <summary>
        /// 刷新NumberBox值的委托方法
        /// 由View设置，用于在执行命令前强制刷新控件值
        /// </summary>
        public Action RefreshNumberBoxValues { get; set; }

        public PresetControlViewModel(IContentDialogService contentDialogService)
        {
            ContentDialogService = contentDialogService;
            _configManager = new PresetConfigManager();
            _presetConfigs = _configManager.Configs;

            // 初始化套号和预设号列表
            _setIndexes = new ObservableCollection<int>(Enumerable.Range(1, 8));
            _presetIndexes = new ObservableCollection<int>(Enumerable.Range(1, 4));

            // 初始化刷新方法为空操作
            RefreshNumberBoxValues = () => { };

            // 监听选中配置的变化
            this.PropertyChanged += PresetControlViewModel_PropertyChanged;
        }

        /// <summary>
        /// 属性变更事件处理
        /// </summary>
        private void PresetControlViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            // 当选中的配置变化时，需要订阅其属性变更事件以保存配置
            if (e.PropertyName == nameof(SelectedPresetConfig))
            {
                // 订阅新选中配置的属性变更事件
                if (SelectedPresetConfig != null)
                {
                    SelectedPresetConfig.PropertyChanged += SelectedPresetConfig_PropertyChanged;
                }
            }
        }

        /// <summary>
        /// 选中配置的属性变更事件处理
        /// </summary>
        private void SelectedPresetConfig_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            // 当场景限制相关属性发生变化时，保存配置
            if (e.PropertyName == nameof(PresetConfig.AllScenes) ||
                e.PropertyName == nameof(PresetConfig.Yuhun) ||
                e.PropertyName == nameof(PresetConfig.Tupo) ||
                e.PropertyName == nameof(PresetConfig.Tansuo) ||
                e.PropertyName == nameof(PresetConfig.Yuling) ||
                e.PropertyName == nameof(PresetConfig.Douji) ||
                e.PropertyName == nameof(PresetConfig.Yingxiong) ||
                e.PropertyName == nameof(PresetConfig.Liudao) ||
                e.PropertyName == nameof(PresetConfig.Qiling) ||
                e.PropertyName == nameof(PresetConfig.Richang))
            {
                SaveSceneConfigs();
            }
        }

        /// <summary>
        /// 保存场景配置
        /// </summary>
        private void SaveSceneConfigs()
        {
            if (SelectedPresetConfig != null)
            {
                _configManager.SaveConfigs();
            }
        }

        /// <summary>
        /// 创建新的预设配置
        /// </summary>
        [RelayCommand]
        private async Task CreatePresetConfig()
        {
            if (string.IsNullOrWhiteSpace(NewConfigName))
            {
                await ShowDialogAsync("提示", "请输入预设配置名称");
                return;
            }

            try
            {
                var newConfig = new PresetConfig
                {
                    Name = NewConfigName,
                    AllScenes = true // 默认选中全部场景
                };
                _configManager.AddConfig(newConfig);
                NewConfigName = string.Empty;
                SelectedPresetConfig = newConfig;
            }
            catch (InvalidOperationException ex)
            {
                await ShowDialogAsync("错误", ex.Message);
            }
        }

        /// <summary>
        /// 删除预设配置
        /// </summary>
        [RelayCommand]
        private async Task DeletePresetConfig()
        {
            if (SelectedPresetConfig == null)
            {
                await ShowDialogAsync("提示", "请先选择要删除的预设配置");
                return;
            }

            var result = await ShowConfirmDialogAsync("确认", $"确定要删除预设配置 \"{SelectedPresetConfig.Name}\" 吗？");
            if (result)
            {
                string name = SelectedPresetConfig.Name;
                SelectedPresetConfig = null;
                _configManager.DeleteConfig(name);
            }
        }

        /// <summary>
        /// 添加参数到当前预设配置
        /// </summary>
        [RelayCommand]
        private async Task AddParameter()
        {
            if (SelectedPresetConfig == null)
            {
                await ShowDialogAsync("提示", "请先选择或创建一个预设配置");
                return;
            }

            // 在进行任何操作前，强制刷新NumberBox的值
            RefreshNumberBoxValues();

            // 检查是否已存在相同的套号/预设号组合
            string newKey = $"{NewSetIndex}-{NewPresetIndex}";
            if (SelectedPresetConfig.Parameters.Any(p => p.UniqueKey == newKey))
            {
                await ShowDialogAsync("提示", $"套号 {NewSetIndex} 的预设号 {NewPresetIndex} 已存在，不能重复添加");
                return;
            }

            var newParameter = new PresetParameter
            {
                SetIndex = NewSetIndex,
                PresetIndex = NewPresetIndex
            };

            SelectedPresetConfig.Parameters.Add(newParameter);
            _configManager.SaveConfigs();
        }

        /// <summary>
        /// 从当前预设配置中删除参数
        /// </summary>
        [RelayCommand]
        private async Task DeleteParameter()
        {
            if (SelectedPresetConfig == null || SelectedParameter == null)
            {
                await ShowDialogAsync("提示", "请先选择要删除的参数");
                return;
            }

            SelectedPresetConfig.Parameters.Remove(SelectedParameter);
            SelectedParameter = null;
            _configManager.SaveConfigs();
        }

        /// <summary>
        /// 显示一个简单对话框
        /// </summary>
        private async Task ShowDialogAsync(string title, string message)
        {
            await ContentDialogService.ShowSimpleDialogAsync(
                new SimpleContentDialogCreateOptions
                {
                    Title = title,
                    Content = message,
                    CloseButtonText = "确定"
                });
        }

        /// <summary>
        /// 显示一个确认对话框
        /// </summary>
        private async Task<bool> ShowConfirmDialogAsync(string title, string message)
        {
            var result = await ContentDialogService.ShowSimpleDialogAsync(
                new SimpleContentDialogCreateOptions
                {
                    Title = title,
                    Content = message,
                    PrimaryButtonText = "确定",
                    CloseButtonText = "取消"
                });

            return result == ContentDialogResult.Primary;
        }

        /// <summary>
        /// 获取所有预设配置名称
        /// </summary>
        public IEnumerable<string> GetAllPresetNames()
        {
            return _configManager.GetAllConfigNames();
        }
    }
}