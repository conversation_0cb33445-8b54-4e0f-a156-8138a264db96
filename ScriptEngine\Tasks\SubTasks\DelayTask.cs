﻿using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;

namespace ScriptEngine.Tasks
{
    internal class DelayTask : BaseTask
    {
        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "等待");
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            log.Info("开始等待 " + configs.Count + "毫秒");
            Sleep(configs.Count);
            log.Info("等待任务结束");
            UserNotificationMessage = "OK.";
        }
    }
}