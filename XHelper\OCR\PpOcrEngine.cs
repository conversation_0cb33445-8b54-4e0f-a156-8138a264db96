using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using XHelper.Models;

namespace XHelper.OCR
{
    /// <summary>
    /// PpOcr引擎实现类，用于替代OcrLiteEngine，改善文本识别效果
    /// </summary>
    internal class PpOcrEngine : IOcrEngine
    {
        private readonly OcrConfiguration _config;
        private readonly IImageProcessor _imageProcessor;
        private PpOcr? _ocrEngine;
        private bool _initialized = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">OCR配置</param>
        /// <param name="imageProcessor">图像处理器</param>
        public PpOcrEngine(OcrConfiguration config, IImageProcessor imageProcessor)
        {
            _config = config;
            _imageProcessor = imageProcessor;
        }

        /// <summary>
        /// 初始化OCR引擎
        /// </summary>
        /// <returns>是否初始化成功</returns>
        public bool Initialize()
        {
            // 已初始化则直接返回
            if (_initialized)
                return true;

            try
            {
                // 检查模型文件是否存在
                if (!_config.CheckModelFilesExist("ocrlite"))
                {
                    return false;
                }

                // 初始化PpOcr引擎
                _ocrEngine = new PpOcr(
                    _config.DetModelPath,
                    _config.RecModelPath,
                    _config.KeysPath);

                _initialized = true;
                return true;
            }
            catch (Exception ex)
            {
                XLogger.Error($"PpOcr引擎初始化异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从图像路径识别文本
        /// </summary>
        /// <param name="imagePath">图像路径</param>
        /// <param name="callback">可选的回调函数，用于接收文本块信息</param>
        /// <returns>识别结果文本</returns>
        public string RecognizeText(string imagePath, Action<List<XOcr_TextBlock>>? callback = null)
        {
            try
            {
                // 优化图像
                byte[] imageData = _imageProcessor.OptimizeImage(imagePath);
                if (imageData.Length == 0)
                {
                    XLogger.Error("图像优化失败");
                    return string.Empty;
                }

                return RecognizeText(imageData, callback);
            }
            catch (Exception ex)
            {
                XLogger.Error($"从图像路径识别文本异常: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 从图像字节数组识别文本
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <param name="callback">可选的回调函数，用于接收文本块信息</param>
        /// <returns>识别结果文本</returns>
        public string RecognizeText(byte[] imageData, Action<List<XOcr_TextBlock>>? callback = null)
        {
            try
            {
                // 检查输入
                if (imageData == null || imageData.Length == 0)
                {
                    XLogger.Warn("输入图像数据为空");
                    return string.Empty;
                }

                // 初始化OCR引擎
                if (!Initialize())
                {
                    XLogger.Error("PpOcr引擎初始化失败");
                    return string.Empty;
                }

                // 直接对字节数组执行OCR识别
                List<OcrResult> ocrResults = _ocrEngine!.RunOCR(imageData);

                // 创建文本块列表
                if (callback != null && ocrResults.Count > 0)
                {
                    try
                    {
                        List<XOcr_TextBlock> blocks = new List<XOcr_TextBlock>();
                        foreach (var result in ocrResults)
                        {
                            blocks.Add(new XOcr_TextBlock(
                                result.Text,
                                new System.Drawing.Rectangle(
                                    result.Box.X,
                                    result.Box.Y + result.Box.Height,
                                    result.Box.Width,
                                    result.Box.Height)
                            ));
                        }

                        blocks.Reverse();

                        // 使用图像处理器和OCR配置矫正坐标
                        var correctedBlocks = OcrCoordinateCorrector.CorrectCoordinates(blocks, _imageProcessor);
                        callback(correctedBlocks);
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"执行回调时发生异常: {ex.Message}");
                    }
                }

                // 拼接识别结果
                StringBuilder sb = new StringBuilder();
                foreach (var result in ocrResults)
                {
                    sb.Append(result.Text).Append(' ');
                }

                return sb.ToString().Trim();
            }
            catch (Exception ex)
            {
                XLogger.Error($"文本识别异常: {ex.Message}");
                return string.Empty;
            }
        }
    }
}