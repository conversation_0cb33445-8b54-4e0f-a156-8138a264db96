﻿using Compunet.YoloSharp;
using Compunet.YoloSharp.Data;
using System.Drawing;

namespace XHelper
{
    public static class XExtensions
    {
        public static string GetPosStr(this SixLabors.ImageSharp.Rectangle Rect)
        {
            int x = Rect.X;
            int y = Rect.Y;
            int x1 = x + Rect.Width;
            int y1 = y + Rect.Height;
            return x + "," + y + "," + x1 + "," + y1;
        }

        public static byte[] ToByteArray(this Bitmap bitmap)
        {
            using (MemoryStream ms = new MemoryStream())
            {
                bitmap.Save(ms, System.Drawing.Imaging.ImageFormat.Bmp);
                byte[] imageBytes = ms.ToArray();
                return imageBytes;
            }
        }

        /// <summary>
        /// 获取置信度最高的一个Name
        /// </summary>
        /// <param name="classifications"></param>
        /// <param name="confidence"></param>
        /// <returns></returns>
        public static string YoloClass_GetTopName(this YoloResult<Classification> classifications, double confidence)
        {
            if (classifications is null)
            {
                return "未知";
            }
            var result = classifications.Where(x => x.Confidence > confidence).OrderByDescending(x => x.Confidence).FirstOrDefault();
            if (result is null)
            {
                return "未知";
            }
            return result.Name.Name;
        }
    }
}