<UserControl x:Class="DanDing1.Views.UserControls.SingleGamePreviewItem"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:DanDing1.Views.UserControls"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             Width="300"
             Height="250"
             Margin="5"
             ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
             ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <local:PreviewButtonTextConverter x:Key="PreviewButtonTextConverter"/>
    </UserControl.Resources>
    <Border BorderBrush="{DynamicResource ControlElevationBorderBrush}"
            BorderThickness="1"
            CornerRadius="6"
            Background="Transparent">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Grid Grid.Row="0" Margin="0,0,0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!-- 游戏名称 -->
                <TextBlock x:Name="GameNameText"
                           Margin="10 5 5 5"
                           Grid.Column="0"
                           Text="未知游戏"
                           VerticalAlignment="Center"
                           FontWeight="SemiBold" />

                <!-- 同步按钮 -->
                <ui:Button x:Name="SyncButton"
                           Margin="5"
                           Grid.Column="1"
                           Click="SyncButton_Click"
                           Content="{Binding RelativeSource={RelativeSource AncestorType=local:SingleGamePreviewItem}, Path=IsPreviewActive, Converter={StaticResource PreviewButtonTextConverter}}" />
            </Grid>

            <!-- 预览区域 -->
            <Border Grid.Row="1"
                    x:Name="PreviewBorder"
                    Background="Black"
                    BorderBrush="{DynamicResource ControlElevationBorderBrush}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Margin="0"
                    ClipToBounds="True">
                <Grid ClipToBounds="True">
                    <!-- 预览内容会通过DWM API直接渲染到此区域 -->
                    <Canvas x:Name="PreviewCanvas"
                            ClipToBounds="True"
                            Background="Transparent" />

                    <TextBlock x:Name="NoPreviewText"
                               Text="未开始预览"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="Gray" />

                    <TextBlock x:Name="PreviewErrorText"
                               Visibility="Collapsed"
                               TextWrapping="Wrap"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="Red"
                               Margin="10" />
                </Grid>
            </Border>

            <!-- 最新日志显示区域 -->
            <Border Grid.Row="2"
                    Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource ControlElevationBorderBrush}"
                    CornerRadius="4"
                    Margin="0,2,0,0">
                <TextBlock x:Name="LastLogText"
                           Text="暂无日志"
                           Padding="8,4"
                           TextTrimming="CharacterEllipsis"
                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                           FontSize="11"
                           ToolTip="{Binding Text, RelativeSource={RelativeSource Self}}" />
            </Border>
        </Grid>
    </Border>
</UserControl>