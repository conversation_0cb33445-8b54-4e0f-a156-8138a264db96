﻿using DamoControlKit.Interface;
using DamoControlKit.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DamoControlKit.Function
{
    /// <summary>
    /// 顺序执行模式 查找图片队列
    /// </summary>
    public class Flow_Q_Find_Pic : IFlow, IDmInterface
    {
        /// <summary>
        /// 超时时间 默认10秒
        /// </summary>
        private int OutTime { get; set; } = 10000;

        /// <summary>
        /// 超时是否重新开始
        /// </summary>
        private bool OutTimeReStart { get; set; } = false;

        /// <summary>
        /// 超时返回查找上一张
        /// </summary>
        private bool OutTimeGoBack { get; set; } = false;

        /// <summary>
        /// 图片列表
        /// </summary>
        public Pics PicList { get; private set; } = new();

        /// <summary>
        /// 位置列表
        /// </summary>
        public Positions PosList { get; private set; } = new();

        public dmsoft? dmsoft { get; set; }

        /// <summary>
        /// 初始化日志等接口
        /// </summary>
        public void Init()
        { }

        public void Start()
        {
            if (PicList.Count == 0)
                return;
            Main();
        }


        /// <summary>
        /// 设置超时时间
        /// </summary>
        /// <param name="outTime"></param>
        /// <returns></returns>
        public Flow_Q_Find_Pic SetOutTime(int outTime)
        {
            OutTime = outTime * 1000;
            return this;
        }

        /// <summary>
        /// 设置 超时是否重新开始
        /// </summary>
        /// <param name="reStart"></param>
        /// <returns></returns>
        public Flow_Q_Find_Pic SetOutTimeReStart(bool reStart)
        {
            OutTimeReStart = reStart;
            return this;
        }

        /// <summary>
        /// 设置 超时是否返回上一张图片查找
        /// </summary>
        /// <param name="goBack"></param>
        /// <returns></returns>
        public Flow_Q_Find_Pic SetOutTimeGoBack(bool goBack)
        {
            OutTimeGoBack = goBack;
            return this;
        }

        /// <summary>
        /// 添加图片 并提供相应的点击位置
        /// </summary>
        /// <param name="pic">图片</param>
        /// <param name="clickPos">点击位置</param>
        /// <returns></returns>
        public Flow_Q_Find_Pic AddPicAndClick(Pic pic, Position? clickPos)
        {
            if (dmsoft is not null) { pic.SetXsoft(dmsoft); clickPos?.SetXsoft(dmsoft); }
            PicList.Add(pic);
            clickPos ??= pic.Position;
            clickPos.SetIdentifier(pic.Name);
            PosList.Add(clickPos);
            return this;
        }

        /// <summary>
        /// 添加图片 不执行点击
        /// </summary>
        /// <param name="pic"></param>
        /// <returns></returns>
        public Flow_Q_Find_Pic AddPicNotClick(Pic pic)
        {
            if (dmsoft is not null) pic.SetXsoft(dmsoft);
            PicList.Add(pic);
            return this;
        }

        private void Main()
        {
        Re:
            for (int i = 0; i < PicList.Count; i++)
            {
                Pic pic = PicList.PicList[i];

                var ret = pic.Await(null, OutTime);
                if (!ret && OutTimeReStart)
                {
                    Debug.WriteLine("Flow_Q_Find_Pic 等待图片超时，重新开始.");
                    goto Re;
                }
                if (!ret && OutTimeGoBack)
                {
                    Debug.WriteLine("Flow_Q_Find_Pic 等待图片超时，重新查找上一张图片.");
                    if (i == 0)
                    {
                        Debug.WriteLine("Flow_Q_Find_Pic 当前为第一张图片无法继续，结束流程.");
                        return;
                    }
                    i -= 2;
                    continue;
                }
                if (!ret) { Debug.WriteLine("Flow_Q_Find_Pic 等待图片超时，结束流程."); return; }
                if (ret) Find_Yep(pic.Name);//点击
            }
        }

        /// <summary>
        /// 等待到图片后执行动作
        /// </summary>
        /// <param name="identifier">点击标识</param>
        private void Find_Yep(string identifier)
        {
            var ret = PosList.GetAllIdentifier()?.Contains(identifier) ?? false;
            if (ret)
                PosList.Click(identifier);
        }

        public void Stop()
        { }

        public bool SetXsoft()
        {
            throw new NotImplementedException();
        }

        public bool SetXsoft(dmsoft x)
        {
            dmsoft = x;
            PicList.SetAllXsoft(x);
            PosList.SetAllXsoft(x);
            return true;
        }
    }
}