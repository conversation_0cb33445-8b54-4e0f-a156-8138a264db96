﻿using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using ScriptEngine.Tasks.TimerTasks;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Tasks
{
    internal class TimerTask : BaseTask
    {
        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, className);
        }

        public JiYangConfig? Config_JiYang => jiYangTask?.Config;
        public FangKaTaskConfig? Config_FangKa => fangKaTask?.Config;

        /// <summary>
        /// 离线寄养功能
        /// </summary>
        public bool OffLine { get; set; }

        private JiYangTask? jiYangTask;
        private FangKaTask? fangKaTask;

        /// <summary>
        /// 是否使用辅助线程扫描
        /// </summary>
        public bool isSubScan = false;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="configs"></param>
        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);

            // 设置是否离线寄养，默认为false
            if (configs.Others.TryGetValue("OffLine", out string? offLineStr) && bool.TryParse(offLineStr, out bool offLine))
                OffLine = offLine;
            else
                OffLine = false;

            if (configs.Others.TryGetValue("JiYang", out string? JiYang) && bool.Parse(JiYang))
            {
                log.Info("[定时任务] 定时任务启用了寄养，现在初始化寄养..");
                jiYangTask = new JiYangTask();
                jiYangTask.Init(Db, Dm, Ct, "寄养");
                jiYangTask.Start(GetConfig);
            }

            if (configs.Others.TryGetValue("FangKa", out string? FangKa) && bool.Parse(FangKa))
            {
                log.Info("[定时任务] 定时任务启用了放卡，现在初始化放卡..");
                fangKaTask = new FangKaTask();
                fangKaTask.Init(Db, Dm, Ct, "放卡");
                fangKaTask.Start(GetConfig);
            }

            isSubScan = true;
            log.Info("[定时任务] 定时任务初始化完成.. 计时中..");
        }

        /// <summary>
        /// 是否需要执行定时任务
        /// </summary>
        public bool Check_AllTask()
        {
            var t = false;
            if (jiYangTask?.Config?.isDo ?? false)
            {
                log.Info_Green("[定时任务] 有寄养任务需要执行...已通知主任务队列交接..");
                isSubScan = false; t = true;
            }
            if (fangKaTask?.Config?.isDo ?? false)
            {
                log.Info_Green("[定时任务] 有放卡任务需要执行...已通知主任务队列交接..");
                isSubScan = false; t = true;
            }
            return t;
        }

        /// <summary>
        /// 执行定时任务
        /// </summary>
        public void DoAllTask()
        {
            jiYangTask?.CheckAndDo();
            fangKaTask?.CheckAndDo();
        }
    }
}