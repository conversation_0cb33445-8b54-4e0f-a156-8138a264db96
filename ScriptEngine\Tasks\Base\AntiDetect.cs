﻿namespace ScriptEngine.Tasks.Base
{
    /// <summary>
    /// 反检测类
    /// </summary>
    internal class AntiDetect
    {
        /// <summary>
        /// 配置检查间隔时间（秒）
        /// </summary>
        private const int CONFIG_CHECK_INTERVAL_SECONDS = 60;

        /// <summary>
        /// 默认增量倍率
        /// </summary>
        private const float DEFAULT_INCREMENT_MULTIPLIER = 1.5f;

        /// <summary>
        /// 默认初始增量值
        /// </summary>
        private const float DEFAULT_INITIAL_INCREMENT = 0.01f;

        /// <summary>
        /// 默认最大延迟时间（秒）
        /// </summary>
        private const int DEFAULT_MAX_DELAY_SECONDS = 21;

        /// <summary>
        /// 默认最小延迟时间（秒）
        /// </summary>
        private const int DEFAULT_MIN_DELAY_SECONDS = 5;

        /// <summary>
        /// 默认概率增量值
        /// </summary>
        private const float DEFAULT_PROBABILITY_INCREMENT = 0.1f;

        /// <summary>
        /// 增量倍率配置键名
        /// </summary>
        private const string INCREMENT_MULTIPLIER_KEY = "RandomWaitIncrementMultiplier";

        /// <summary>
        /// 初始增量配置键名
        /// </summary>
        private const string INITIAL_INCREMENT_KEY = "RandomWaitInitialIncrement";

        /// <summary>
        /// 最大延迟时间配置键名
        /// </summary>
        private const string MAX_DELAY_CONFIG_KEY = "RandomWaitMaxSeconds";

        /// <summary>
        /// 最大概率值（百分比）
        /// </summary>
        private const float MAX_PROBABILITY = 100f;

        /// <summary>
        /// 最小延迟时间配置键名
        /// </summary>
        private const string MIN_DELAY_CONFIG_KEY = "RandomWaitMinSeconds";

        /// <summary>
        /// 概率增量配置键名
        /// </summary>
        private const string PROBABILITY_INCREMENT_KEY = "RandomWaitIncrement";

        /// <summary>
        /// 随机等待配置键名
        /// </summary>
        private const string RANDOM_WAIT_CONFIG_KEY = "RandomWait";

        /// <summary>
        /// 线程本地随机数生成器，确保每个线程有自己的Random实例，避免多线程问题
        /// </summary>
        private static readonly ThreadLocal<Random> ThreadRandom = new ThreadLocal<Random>(() =>
            new Random(Guid.NewGuid().GetHashCode() ^ Thread.CurrentThread.ManagedThreadId));

        /// <summary>
        /// 随机数生成器
        /// </summary>
        private readonly Random random;

        /// <summary>
        /// 关联的基础任务实例
        /// </summary>
        private readonly BaseTask task;

        /// <summary>
        /// 当前增量值
        /// </summary>
        private float currentIncrement;

        /// <summary>
        /// 当前随机等待的触发概率
        /// </summary>
        private float currentRandomWait;

        /// <summary>
        /// 延迟时间增加的次数计数
        /// </summary>
        private int delayIncrementCount = 0;

        /// <summary>
        /// 增量倍率
        /// </summary>
        private float incrementMultiplier;

        /// <summary>
        /// 初始增量值
        /// </summary>
        private float initialIncrement;

        /// <summary>
        /// 是否启用调试模式
        /// </summary>
        private bool isDebugMode = false;

        /// <summary>
        /// 上次检查配置的时间
        /// </summary>
        private DateTime lastConfigCheckTime = DateTime.MinValue;

        /// <summary>
        /// 最大延迟时间（秒）
        /// </summary>
        private int maxDelaySeconds;

        /// <summary>
        /// 最小延迟时间（秒）
        /// </summary>
        private int minDelaySeconds;

        /// <summary>
        /// 概率增量值
        /// </summary>
        private float probabilityIncrement;

        /// <summary>
        /// 用户配置的随机等待概率
        /// </summary>
        private float userConfiguredRandomWait;

        /// <summary>
        /// 当前随机穿插小纸人的触发概率
        /// </summary>
        private float currentRandomYysAuto;

        /// <summary>
        /// 用户配置的随机穿插小纸人概率
        /// </summary>
        private float userConfiguredRandomYysAuto;

        /// <summary>
        /// 随机穿插小纸人的当前增量值
        /// </summary>
        private float currentYysAutoIncrement;

        /// <summary>
        /// 是否已触发随机穿插小纸人事件（需要重置才能继续累加概率）
        /// </summary>
        private bool yysAutoTriggered = false;

        // 每分钟检查一次配置变更
        // 记录延迟时间增加的次数
        public AntiDetect(BaseTask task)
        {
            this.task = task;
            this.random = ThreadRandom.Value ?? new Random(Guid.NewGuid().GetHashCode());
            this.userConfiguredRandomWait = GetUserConfiguredRandomWait();
            this.currentRandomWait = userConfiguredRandomWait;
            this.minDelaySeconds = GetConfiguredDelaySeconds(MIN_DELAY_CONFIG_KEY, DEFAULT_MIN_DELAY_SECONDS);
            this.maxDelaySeconds = GetConfiguredDelaySeconds(MAX_DELAY_CONFIG_KEY, DEFAULT_MAX_DELAY_SECONDS);
            this.probabilityIncrement = GetConfiguredProbabilityIncrement();
            this.initialIncrement = GetConfiguredInitialIncrement();
            this.currentIncrement = initialIncrement;
            this.incrementMultiplier = GetConfiguredIncrementMultiplier();
            this.isDebugMode = GetDebugMode();
            this.lastConfigCheckTime = DateTime.Now;
            this.delayIncrementCount = 0;

            // 初始化随机穿插小纸人相关字段
            this.userConfiguredRandomYysAuto = GetUserConfiguredRandomYysAuto();
            this.currentRandomYysAuto = userConfiguredRandomYysAuto;
            this.currentYysAutoIncrement = initialIncrement;
            this.yysAutoTriggered = false;

            // 记录初始配置
            LogConfiguration();
        }

        /// <summary>
        /// 执行随机延迟等待
        /// </summary>
        /// <returns>是否执行了等待</returns>
        public bool RandomDelay()
        {
            if (userConfiguredRandomWait <= 0) return false;

            float randomValue = (float)random.NextDouble() * MAX_PROBABILITY;
            if (isDebugMode)
            {
                task.log.Debug($"随机值: {randomValue:F2}%, 当前触发阈值: {currentRandomWait:F2}%, 当前增量: {currentIncrement:F4}%");
            }

            if (randomValue >= currentRandomWait)
            {
                float oldRandomWait = currentRandomWait;

                // 使用当前增量更新概率
                currentRandomWait = Math.Min(currentRandomWait + currentIncrement, MAX_PROBABILITY);

                // 更新增量（指数增长）
                currentIncrement = currentIncrement * incrementMultiplier;

                // 如果概率提高了，增加延迟时间范围
                if (currentRandomWait > oldRandomWait)
                {
                    delayIncrementCount++;
                    if (isDebugMode)
                    {
                        task.log.Debug($"概率提高，延迟范围增加次数: {delayIncrementCount}，" +
                                      $"当前延迟范围: {minDelaySeconds + delayIncrementCount}-{maxDelaySeconds + delayIncrementCount}秒");
                    }
                }

                task.log.Debug($"未触发随机延迟，当前触发概率已提升至：{currentRandomWait:F2}%，下次增量：{currentIncrement:F4}%");
                return false;
            }

            // 确保最大延迟大于最小延迟
            int adjustedMinDelay = minDelaySeconds + delayIncrementCount;
            int adjustedMaxDelay = maxDelaySeconds + delayIncrementCount;

            if (adjustedMaxDelay <= adjustedMinDelay)
            {
                adjustedMaxDelay = adjustedMinDelay + 1;
            }

            int delaySeconds = random.Next(adjustedMinDelay, adjustedMaxDelay);
            task.log.Info($"触发随机延迟等待，当前触发概率：{currentRandomWait:F2}%，等待时长：{delaySeconds}秒");

            task.Sleep(delaySeconds * 1000);
            // 重置为初始状态
            currentRandomWait = userConfiguredRandomWait;
            currentIncrement = initialIncrement;
            delayIncrementCount = 0;
            return true;
        }

        /// <summary>
        /// 获取用户配置的延迟秒数
        /// </summary>
        private int GetConfiguredDelaySeconds(string key, int defaultValue)
        {
            if (task.Db.UserConfigs.TryGetValue(key, out var delayObj) && delayObj != null)
            {
                try
                {
                    int value = Convert.ToInt32(delayObj);
                    return value > 0 ? value : defaultValue;
                }
                catch
                {
                    return defaultValue;
                }
            }
            return defaultValue;
        }

        /// <summary>
        /// 获取用户配置的增量倍率
        /// </summary>
        private float GetConfiguredIncrementMultiplier()
        {
            if (task.Db.UserConfigs.TryGetValue(INCREMENT_MULTIPLIER_KEY, out var multiplierObj) && multiplierObj != null)
            {
                try
                {
                    float value = Convert.ToSingle(multiplierObj);
                    // 确保倍率在合理范围内，必须大于1
                    return value > 1 && value <= 5 ? value : DEFAULT_INCREMENT_MULTIPLIER;
                }
                catch
                {
                    return DEFAULT_INCREMENT_MULTIPLIER;
                }
            }
            return DEFAULT_INCREMENT_MULTIPLIER;
        }

        /// <summary>
        /// 获取用户配置的初始增量值
        /// </summary>
        private float GetConfiguredInitialIncrement()
        {
            if (task.Db.UserConfigs.TryGetValue(INITIAL_INCREMENT_KEY, out var incrementObj) && incrementObj != null)
            {
                try
                {
                    float value = Convert.ToSingle(incrementObj);
                    // 确保增量在合理范围内
                    return value > 0 && value <= 1 ? value : DEFAULT_INITIAL_INCREMENT;
                }
                catch
                {
                    return DEFAULT_INITIAL_INCREMENT;
                }
            }
            return DEFAULT_INITIAL_INCREMENT;
        }

        /// <summary>
        /// 获取用户配置的概率增量
        /// </summary>
        private float GetConfiguredProbabilityIncrement()
        {
            if (task.Db.UserConfigs.TryGetValue(PROBABILITY_INCREMENT_KEY, out var incrementObj) && incrementObj != null)
            {
                try
                {
                    float value = Convert.ToSingle(incrementObj);
                    // 确保增量在合理范围内
                    return value > 0 && value <= 10 ? value : DEFAULT_PROBABILITY_INCREMENT;
                }
                catch
                {
                    return DEFAULT_PROBABILITY_INCREMENT;
                }
            }
            return DEFAULT_PROBABILITY_INCREMENT;
        }

        /// <summary>
        /// 获取是否启用调试模式
        /// </summary>
        private bool GetDebugMode()
        {
            if (task.Db.UserConfigs.TryGetValue("AntiDetectDebug", out var debugObj) && debugObj != null)
            {
                try
                {
                    return Convert.ToBoolean(debugObj);
                }
                catch
                {
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取用户配置的随机等待概率
        /// </summary>
        private float GetUserConfiguredRandomWait()
        {
            if (task.Db.UserConfigs.TryGetValue(RANDOM_WAIT_CONFIG_KEY, out var randomWaitObj))
            {
                return randomWaitObj != null ? Convert.ToSingle(randomWaitObj) : 0f;
            }
            return 0f;
        }

        /// <summary>
        /// 获取用户配置的随机穿插小纸人概率
        /// </summary>
        private float GetUserConfiguredRandomYysAuto()
        {
            if (task.Db.UserConfigs.TryGetValue("RandomYysAuto", out var randomYysAutoObj))
            {
                return randomYysAutoObj != null ? Convert.ToSingle(randomYysAutoObj) : 0f;
            }
            return 0f;
        }

        /// <summary>
        /// 记录当前配置信息
        /// </summary>
        private void LogConfiguration()
        {
            task.log.Debug($"反检测配置 - 基础触发概率: {userConfiguredRandomWait:F2}%, " +
                          $"初始增量: {initialIncrement:F4}%, " +
                          $"增量倍率: {incrementMultiplier:F2}, " +
                          $"延迟范围: {minDelaySeconds}-{maxDelaySeconds}秒, " +
                          $"调试模式: {(isDebugMode ? "开启" : "关闭")}");

            task.log.Debug($"小纸人配置 - 基础触发概率: {userConfiguredRandomYysAuto:F2}%");
        }

        /// <summary>
        /// 判断是否需要执行随机穿插小纸人事件
        /// </summary>
        /// <returns>是否应该触发随机穿插小纸人事件</returns>
        public bool ShouldTriggerRandomYysAuto()
        {
            // 如果用户未配置概率或概率为0，则不触发
            if (userConfiguredRandomYysAuto <= 0) return false;

            // 如果已经触发过，需要重置后才能再次触发
            if (yysAutoTriggered) return false;

            // 生成随机值（0-100之间）
            float randomValue = (float)random.NextDouble() * MAX_PROBABILITY;

            if (isDebugMode)
            {
                task.log.Debug($"小纸人随机值: {randomValue:F2}%, 当前触发阈值: {currentRandomYysAuto:F2}%, 当前增量: {currentYysAutoIncrement:F4}%");
            }

            // 如果随机值大于等于触发概率，则累加概率但不触发
            if (randomValue >= currentRandomYysAuto)
            {
                // 使用当前增量更新概率
                currentRandomYysAuto = Math.Min(currentRandomYysAuto + currentYysAutoIncrement, MAX_PROBABILITY);

                // 更新增量（指数增长）
                currentYysAutoIncrement = currentYysAutoIncrement * incrementMultiplier;

                if (isDebugMode)
                {
                    task.log.Debug($"未触发随机穿插小纸人，当前触发概率已提升至：{currentRandomYysAuto:F2}%，下次增量：{currentYysAutoIncrement:F4}%");
                }

                return false;
            }

            // 触发事件
            task.log.Info($"触发随机穿插小纸人事件，当前触发概率：{currentRandomYysAuto:F2}%");

            // 标记为已触发，需要手动重置
            yysAutoTriggered = true;

            return true;
        }

        /// <summary>
        /// 重置随机穿插小纸人状态，允许再次累加概率和触发
        /// </summary>
        public void ResetRandomYysAuto()
        {
            // 重置为初始状态
            currentRandomYysAuto = userConfiguredRandomYysAuto;
            currentYysAutoIncrement = initialIncrement;
            yysAutoTriggered = false;

            if (isDebugMode)
            {
                task.log.Debug($"已重置随机穿插小纸人状态，当前触发概率：{currentRandomYysAuto:F2}%");
            }
        }
    }
}