﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace DamoControlKit.Model
{
    /// <summary>
    /// 屏幕颜色获取方式
    /// </summary>
    public enum Display
    { normal, gdi, gdi2, dx2, dx3 }

    /// <summary>
    /// 键盘仿真模式
    /// </summary>
    public enum Keypad
    { normal, windows }

    /// <summary>
    /// 鼠标仿真模式
    /// </summary>
    public enum Mouse
    { normal, windows, windows3, dx_mouse_api }

    /// <summary>
    /// 绑定模式
    /// </summary>
    public enum Bind_SetModel
    { normal, speed }

    /// <summary>
    /// 后台绑定模式
    /// </summary>
    public class BindModel
    {
        /// <summary>
        /// 设置绑定模式
        /// </summary>
        /// <param name="model"></param>
        public BindModel SetModel(Bind_SetModel model)
        {
            if (model == Bind_SetModel.speed)
            {
                // 速度模式
                Display = Display.dx2;
                Mouse = Mouse.windows;
                Keypad = Keypad.windows;
                Public = "dx.public.hack.speed|dx.public.anti.api";
            }
            return this;
        }

        public override string ToString()
        {
            return $"[BindModel] BindModel:{Hwnd}|{Mode}|{Display}|{Mouse}|{Keypad}|{Public}";
        }

        /// <summary>
        /// 屏幕颜色获取方式
        /// </summary>
        public Display Display { get; set; } = Display.dx2;

        /// <summary>
        /// 句柄
        /// </summary>
        public int Hwnd { get; set; }

        /// <summary>
        /// 是否准备就绪
        /// </summary>
        public bool IsReady
        {
            get
            {
                IntPtr _hwnd = Hwnd;
                if (!IsWindow(_hwnd)) return false;
                else return true;
            }
            private set { }
        }

        /// <summary>
        /// 键盘仿真模式
        /// </summary>
        public Keypad Keypad { get; set; } = Keypad.windows;

        /// <summary>
        /// 绑定模式
        /// </summary>
        public int Mode { get; set; } = 0;

        /// <summary>
        /// 鼠标仿真模式
        /// </summary>
        public Mouse Mouse { get; set; } = Mouse.windows;

        /// <summary>
        /// 公共属性
        /// </summary>
        public string Public { get; set; } = "";

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool IsWindow(IntPtr hWnd);
    }
}