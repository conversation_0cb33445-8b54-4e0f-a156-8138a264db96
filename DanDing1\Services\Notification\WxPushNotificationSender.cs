using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using XHelper;

namespace DanDing1.Services.Notification
{
    /// <summary>
    /// 微信推送通知发送器
    /// 当用户选择"自定义"通知方式并启用微信推送开关时使用
    /// 基于WxPusher API实现：https://wxpusher.zjiecode.com/docs
    /// </summary>
    public class WxPushNotificationSender : BaseNotificationSender
    {
        private readonly HttpClient _httpClient;
        private readonly string _appToken; // WxPusher的AppToken
        private const string API_URL = "https://wxpusher.zjiecode.com/api/send/message"; // WxPusher官方API地址

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="appToken">WxPusher平台获取的AppToken</param>
        public WxPushNotificationSender(string appToken)
        {
            _httpClient = new HttpClient();
            _appToken = appToken;
        }

        /// <summary>
        /// 通知类型标识符
        /// </summary>
        public override string NoticeType => "wxpush";

        /// <summary>
        /// 通知类型显示名称
        /// </summary>
        public override string DisplayName => "微信推送";

        /// <summary>
        /// 格式化微信推送内容
        /// </summary>
        /// <param name="content">原始内容</param>
        /// <returns>格式化后的内容</returns>
        public override string FormatContent(string content)
        {
            // 使用微信支持的换行符格式
            return content.Replace("#换行", "\n");
        }

        /// <summary>
        /// 发送微信推送的核心实现
        /// 基于WxPusher官方API：https://wxpusher.zjiecode.com/docs/#/?id=发送消息
        /// </summary>
        /// <param name="title">通知标题</param>
        /// <param name="content">通知内容</param>
        /// <param name="extraParams">额外参数，可包含uids(List<string>)或topicIds(List<int>)</param>
        /// <returns>发送是否成功</returns>
        protected override async Task<bool> SendNotificationCoreAsync(string title, string content, object extraParams)
        {
            try
            {
                // 提取接收人信息
                var uids = extraParams is Dictionary<string, object> dict && dict.TryGetValue("uids", out var uidsObj)
                    ? uidsObj as List<string>
                    : null;

                var topicIds = extraParams is Dictionary<string, object> dict2 && dict2.TryGetValue("topicIds", out var topicIdsObj)
                    ? topicIdsObj as List<int>
                    : null;

                // 配置用户UID（如果未指定接收人，则从配置读取）
                if (uids == null || uids.Count == 0)
                {
                    string configUid = GlobalData.Instance.UserConfig.Notice_WxPush_UID;
                    if (!string.IsNullOrEmpty(configUid))
                    {
                        uids = new List<string> { configUid };
                    }
                }

                // 至少需要指定UID或主题ID其中之一
                if ((uids == null || uids.Count == 0) && (topicIds == null || topicIds.Count == 0))
                {
                    XLogger.Error("微信推送失败：未指定接收人UID或主题ID");
                    return false;
                }

                // 构建请求数据，严格按照WxPusher API文档要求
                var requestData = new
                {
                    appToken = _appToken,         // 应用Token，必填
                    content = content,            // 消息内容，必填
                    summary = title,              // 消息摘要，显示在通知栏
                    contentType = 1,              // 内容类型：1表示文本，2表示HTML
                    topicIds = topicIds,          // 主题ID，可选
                    uids = uids,                  // 用户UID，可选，与topicIds至少需要一个
                    url = string.Empty            // 点击消息跳转的URL，可选
                };

                var json = JsonSerializer.Serialize(requestData);
                var requestContent = new StringContent(json, Encoding.UTF8, "application/json");

                // 发送请求
                var response = await _httpClient.PostAsync(API_URL, requestContent);

                // 解析响应
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<WxPushResponse>(responseContent);

                    if (result?.Code == 1000 || result?.Code == 0)
                    {
                        //XLogger.Info($"微信推送成功，消息ID: {result.Data?.MessageId}, 成功发送人数: {result.Data?.Count}");
                        return true;
                    }
                    else
                    {
                        XLogger.Error($"微信推送API返回错误: {result?.Msg}, 代码: {result?.Code}");
                        return false;
                    }
                }

                XLogger.Error($"微信推送请求失败，HTTP状态码: {response.StatusCode}");
                return false;
            }
            catch (Exception ex)
            {
                XLogger.Error($"发送微信推送异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 微信推送响应数据结构
        /// 根据WxPusher API实际响应格式定义
        /// </summary>
        private class WxPushResponse
        {
            /// <summary>
            /// 响应代码，1000或0表示成功
            /// </summary>
            public int Code { get; set; }

            /// <summary>
            /// 响应消息
            /// </summary>
            public string Msg { get; set; }

            /// <summary>
            /// 响应数据
            /// </summary>
            public WxPushResponseData Data { get; set; }

            /// <summary>
            /// 是否成功
            /// </summary>
            public bool Success => Code == 1000 || Code == 0;
        }

        /// <summary>
        /// 微信推送响应数据详情
        /// </summary>
        private class WxPushResponseData
        {
            /// <summary>
            /// 消息ID
            /// </summary>
            public string MessageId { get; set; }

            /// <summary>
            /// 成功发送的人数
            /// </summary>
            public int Count { get; set; }
        }
    }
}