﻿using DamoControlKit.Model;
using OpenCvSharp;
using ScriptEngine.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using XHelper;

namespace ScriptEngine.Model
{
    /// <summary>
    /// bmp数据图片
    /// 用于序列化存储
    /// </summary>
    public class Data_Pic : IData
    {
        public string Name { get; set; }
        public string Class { get; set; }
        public byte[] ImgBytes { get; set; }
        public Data_Pic() { }

        public Data_Pic(string name, string _class, byte[] imgBytes)
        {
            Name = name;
            Class = _class;
            ImgBytes = imgBytes;
        }

        /// <summary>
        /// 写到临时文件
        /// </summary>
        public void WriteTmp()
        {
            string tmpPath = XPath.GetTempPath();
            if (!Name.Contains(".bmp"))
                Name += ".bmp";
            var path = $"{tmpPath}\\DanDing\\{Class}\\{Name}";
            if (File.Exists(path))
                return;
            XPath.CheckPathIsExist(path);
            File.WriteAllBytes(path, ImgBytes);
        }

        private int Ptr = 0;

        private bool isHavePtr = false;
        /// <summary>
        /// 载入内存 返回指针
        /// </summary>
        /// <param name="size"></param>
        /// <returns></returns>
        public int GetMemPtr(out int size)
        {
            if (isHavePtr)
            {
                size = ImgBytes.Length;
                return Ptr; //已经有指针
            }
            isHavePtr = true;
            size = ImgBytes.Length;
            int ptr = Marshal.AllocHGlobal(size).ToInt32();
            Marshal.Copy(ImgBytes, 0, new IntPtr(ptr), size);
            Ptr = ptr;
            return ptr;
        }
    }

    /// <summary>
    /// bmp数据图片集合
    /// 用于序列化存储
    /// </summary>
    public class Data_Pics
    {
        [JsonPropertyName("DataPics")]
        public List<Data_Pic> DataPics { get; set; } = new();
        public int Count => DataPics.Count;
        public Data_Pics() { }
        public Data_Pics Add(Data_Pic pic) { DataPics.Add(pic); return this; }
        public Data_Pics Add(List<Data_Pic> pics) { DataPics.AddRange(pics); return this; }
        public void WriteAllTmp() => DataPics.ForEach(p => p.WriteTmp());

        private Dictionary<string, string> Pics_PtrAndSize = new();
        /// <summary>
        /// 加载所有内存地址
        /// </summary>
        public Dictionary<string, string> GetPicsPtrAndSize()
        {
            if (Count == 0) return new();
            DataPics.ForEach(p =>
            {
                if (Pics_PtrAndSize.TryGetValue(p.Name, out _))
                    Free(p.Name);//释放

                int ptr = p.GetMemPtr(out int size);
                Pics_PtrAndSize.TryAdd(p.Name, $"{ptr},{size}");
            });
            return Pics_PtrAndSize;
        }
        /// <summary>
        /// 释放所有pic内存地址
        /// </summary>
        public void FreeAll()
        {
            foreach (var item in Pics_PtrAndSize)
            {
                int ptr = int.Parse(item.Value.Split(',')[0]);
                Marshal.FreeHGlobal(new IntPtr(ptr));//释放
            }
        }
        /// <summary>
        /// 释放单个pic内存地址
        /// </summary>
        /// <param name="name"></param>
        public void Free(string name)
        {
            if (Pics_PtrAndSize.TryGetValue(name, out string? ptrAndSize))
            {
                int ptr = int.Parse(ptrAndSize.Split(',')[0]);
                Marshal.FreeHGlobal(new IntPtr(ptr));//释放
            }
        }
    }
}
