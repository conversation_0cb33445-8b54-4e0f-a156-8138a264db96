﻿using DanDing1.ViewModels.Pages;
using Wpf.Ui.Abstractions.Controls;
using DanDing1.Helpers;
using DanDing1.Views.Windows;

namespace DanDing1.Views.Pages
{
    /// <summary>
    /// LoadPage.xaml 的交互逻辑
    /// </summary>
    public partial class LoadPage : System.Windows.Controls.Page, INavigableView<LoadViewModel>
    {
        private const int maxRetries = 5;

        private int retries = 0;

        public LoadPage(LoadViewModel loadViewModel)
        {
            ViewModel = loadViewModel;
            DataContext = this;

            InitializeComponent();
        }

        public LoadViewModel ViewModel { get; }

        private async void Button_Click(object sender, RoutedEventArgs e)
        {
            string username = ViewModel.Info_UserName.ToString() ?? "";
            if (string.IsNullOrEmpty(username))
            {
                Utils.ShowMessage("绑定失败", "用户名不能为空，请先登录");
                return;
            }

            try
            {
                var input_qq = new InputWindow("请输入您的QQ号码");
                input_qq.ShowDialog();
                string qq = input_qq.InputText;
                if (input_qq.IsOK && string.IsNullOrEmpty(qq) && qq != "请输入您的QQ号码")
                {
                    Utils.ShowMessage("绑定失败", "QQ号不能为空");
                    return;
                }

                var input_code = new InputWindow("请输入您群中获取到的验证码");
                input_code.ShowDialog();
                string code = input_code.InputText;
                if (input_code.IsOK && string.IsNullOrEmpty(code) && code != "请输入您群中获取到的验证码")
                {
                    Utils.ShowMessage("绑定失败", "验证码不能为空");
                    return;
                }

                // 验证输入
                if (string.IsNullOrEmpty(qq))
                {
                    Utils.ShowMessage("绑定失败", "QQ号不能为空");
                    return;
                }

                if (string.IsNullOrEmpty(code))
                {
                    Utils.ShowMessage("绑定失败", "验证码不能为空");
                    return;
                }

                try
                {
                    // 调用API绑定QQ
                    var bindResult = await GlobalData.Instance.appConfig.dNet.BindQQAsync(username, qq, code);

                    if (bindResult?.IsSuccess ?? false)
                    {
                        // 绑定成功
                        Utils.ShowMessage("绑定成功", $"已成功将QQ {qq} 绑定到账户 {username}");

                        // 刷新用户信息
                        await ViewModel.RefreshUserInfo();
                    }
                    else
                    {
                        // 绑定失败
                        string errorMsg = bindResult?.Message ?? "未知错误";
                        Utils.ShowMessage("绑定失败", errorMsg);
                    }
                }
                catch (Exception ex)
                {
                    // 处理异常
                    Utils.ShowMessage("绑定异常", $"绑定过程中发生错误: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Utils.ShowMessage("对话框错误", $"创建对话框时出错: {ex.Message}");
            }
        }

        private void Hyperlink_RequestNavigate(object sender, System.Windows.Navigation.RequestNavigateEventArgs e)
        {
            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
            {
                FileName = e.Uri.AbsoluteUri,
                UseShellExecute = true
            });
            e.Handled = true;
        }

        private void KamiTab_GotFocus(object sender, RoutedEventArgs e)
        {
            if (ViewModel.Show_Login == "已登录" && !GlobalData.Instance.appConfig.IsFree)
            {
                ViewModel.UseKami_User = ViewModel.Info_UserName?.ToString() ?? "";
                ViewModel.UseKami_User_Again = ViewModel.Info_UserName?.ToString() ?? "";
            }
        }

        private void TextBlock_MouseDown(object sender, MouseButtonEventArgs e)
        {
        }

        private void TextBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                ViewModel.LoginUserCommand.Execute(null);
            }
        }

        private void WhyBingQQ_Click(object sender, RoutedEventArgs e)
        {
            Utils.ShowMessage("为什么要绑定QQ", "绑定后，目前可以享受以下功能：\r\n" +
                "1、支持交流群中获取最新的日志；\r\n\r\n" +
                "以上功能仅支持已注册的蛋定用户！\r\n" +
                "更多功能正在加急开发中，敬请期待！");
        }
    }
}