using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace DanDing1.Helpers
{
    /// <summary>
    /// 布尔值到字符串的转换器
    /// </summary>
    public class BoolToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                // 如果参数是形如"TrueValue|FalseValue"的字符串，则根据布尔值返回对应部分
                if (parameter is string paramString && paramString.Contains("|"))
                {
                    string[] parts = paramString.Split('|');
                    if (parts.Length == 2)
                    {
                        return boolValue ? parts[0] : parts[1];
                    }
                }

                // 默认转换
                return boolValue.ToString();
            }

            return DependencyProperty.UnsetValue;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // 这里实现从字符串到布尔值的转换（如果需要的话）
            return DependencyProperty.UnsetValue;
        }
    }
}