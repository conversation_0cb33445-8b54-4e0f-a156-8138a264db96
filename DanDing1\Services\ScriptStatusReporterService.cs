﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.ComponentModel;
using DanDing1.Models;
using DanDing1.Models.Super;
using DanDing1.Models.Ws_Models;
using DanDing1.ViewModels.Base;
using DanDing1.ViewModels.Windows;
using ScriptEngine.Tasks.Base;
using ScriptEngine.Frames;
using XHelper;
using DanDing1.Helpers;

namespace DanDing1.Services
{
    /// <summary>
    /// WebSocket状态上报消息类型
    /// </summary>
    public class ScriptStatusUpdateMessage
    {
        public string Type { get; set; } = "SCRIPT_STATUS_UPDATE";
        public ScriptStatusData Payload { get; set; }
    }

    /// <summary>
    /// 脚本状态上报服务 - 负责收集和上报脚本执行状态
    /// </summary>
    public class ScriptStatusReporterService : IDisposable
    {
        #region 私有字段

        // 轮询定时器
        private Timer _pollingTimer;

        // 轮询间隔(毫秒)
        private int _pollingIntervalMs = 1000;

        // 上次上报状态记录（键为ScriptInstanceId）
        private ConcurrentDictionary<string, ScriptStatusData> _lastReportedStates = new ConcurrentDictionary<string, ScriptStatusData>();

        // 是否已启动
        private bool _isRunning = false;

        // 锁对象
        private readonly object _lock = new object();

        // 超级多开视图模型引用
        private SuperMultiGamesWindowViewModel _superMultiVM;

        // 错误信息缓存（脚本ID -> 错误信息）
        private ConcurrentDictionary<int, string> _errorMessages = new ConcurrentDictionary<int, string>();

        // 任务结束状态缓存（脚本ID -> 是否被取消(true=用户取消，false=正常完成)）
        private ConcurrentDictionary<int, bool> _taskEndedFlags = new ConcurrentDictionary<int, bool>();

        #endregion 私有字段

        #region 构造函数与初始化

        /// <summary>
        /// 构造函数
        /// </summary>
        public ScriptStatusReporterService()
        {
            // 初始化时不会立即启动服务，需调用Start方法
        }

        /// <summary>
        /// 初始化服务，设置监听
        /// </summary>
        /// <param name="superMultiVM">超级多开视图模型，若为null则不监控超级多开</param>
        /// <param name="pollingIntervalMs">轮询间隔(毫秒)，默认1000ms</param>
        /// <returns></returns>
        public ScriptStatusReporterService Initialize(SuperMultiGamesWindowViewModel superMultiVM = null, int pollingIntervalMs = 1000)
        {
            lock (_lock)
            {
                if (_isRunning)
                    return this;

                _superMultiVM = superMultiVM;
                _pollingIntervalMs = pollingIntervalMs;

                // 注册脚本结束回调
                Scripts.SetScriptCallBack_Data(ScriptTaskEndedCallback);

                // 如果有超级多开视图模型，监听集合变化
                if (_superMultiVM != null)
                {
                    // 若SuperMultiGame_DataModelCollection是ObservableCollection，可订阅CollectionChanged事件
                    // 此处简化处理，通过定时轮询检测

                    // 监听每个游戏模型的属性变化
                    AttachSuperMultiGameListeners();
                }

                return this;
            }
        }

        /// <summary>
        /// 启动状态上报服务
        /// </summary>
        public void Start()
        {
            lock (_lock)
            {
                if (_isRunning)
                    return;

                // 创建并启动轮询定时器
                _pollingTimer = new Timer(PollScriptStatus, null, 0, _pollingIntervalMs);
                _isRunning = true;
            }
        }

        /// <summary>
        /// 停止状态上报服务
        /// </summary>
        public void Stop()
        {
            lock (_lock)
            {
                if (!_isRunning)
                    return;

                // 停止定时器
                _pollingTimer?.Change(Timeout.Infinite, Timeout.Infinite);
                _isRunning = false;
            }
        }

        #endregion 构造函数与初始化

        #region 脚本状态轮询

        /// <summary>
        /// 定时轮询回调，检查所有脚本状态并上报变化
        /// </summary>
        private void PollScriptStatus(object state)
        {
            try
            {
                // 收集常规脚本(游戏1-4)的状态
                CollectRegularScriptStatus();

                // 如果有超级多开视图模型，收集超级多开脚本状态
                if (_superMultiVM != null)
                {
                    CollectSuperMultiScriptStatus();
                }
            }
            catch (Exception ex)
            {
                // 记录异常，但不影响服务运行
                System.Diagnostics.Debug.WriteLine($"状态上报服务轮询异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 收集常规脚本(游戏1-4)状态
        /// </summary>
        private void CollectRegularScriptStatus()
        {
            // 检查游戏1-4的状态
            for (int gameIndex = 0; gameIndex < 4; gameIndex++)
            {
                try
                {
                    // 从GlobalData获取游戏视图模型
                    var gameViewModel = GlobalData.Instance.GetGameViewModel(gameIndex);
                    if (gameViewModel == null) continue;

                    int scriptId = gameViewModel.ScriptId;
                    string scriptInstanceId = gameViewModel.GameName;

                    // 获取当前状态
                    var currentStatus = GetRegularScriptStatus(gameViewModel);
                    if (currentStatus != null)
                    {
                        // 比较并上报状态变化
                        ReportStatusIfChanged(currentStatus);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"获取常规脚本状态异常: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 获取单个常规脚本的状态
        /// </summary>
        private ScriptStatusData GetRegularScriptStatus(GameViewBaseModel gameViewModel)
        {
            if (gameViewModel == null) return null;

            int scriptId = gameViewModel.ScriptId;
            string gameName = gameViewModel.GameName;
            switch (gameName)
            {
                case "游戏1":
                    gameName = Utils.UserGameName1;
                    break;

                case "游戏2":
                    gameName = Utils.UserGameName2;
                    break;

                case "游戏3":
                    gameName = Utils.UserGameName3;
                    break;

                case "游戏4":
                    gameName = Utils.UserGameName4;
                    break;

                default:
                    break;
            }

            bool isRunning = Scripts.IsRunning(scriptId, out _);

            // 创建状态数据对象
            var statusData = new ScriptStatusData
            {
                CurrentTask = gameViewModel.RunningTaskName,
                ScriptTasks = gameViewModel.GameTaskLists.GetTasks(),
                ScriptInstanceId = gameName,
                InternalScriptId = scriptId,
                ScriptType = "Regular",
                FriendlyName = gameName,
                Timestamp = DateTime.UtcNow
            };

            // 获取运行时间
            if (gameViewModel.Run_watch != null && gameViewModel.isRunning)
                statusData.RunningDurationSeconds = gameViewModel.Run_watch.Elapsed.TotalSeconds;

            // 判断状态
            if (!isRunning)
            {
                // 脚本未运行
                if (_taskEndedFlags.TryGetValue(scriptId, out bool wasCancelled))
                {
                    // 任务结束标志存在，判断是用户取消还是正常完成
                    _taskEndedFlags.TryRemove(scriptId, out _); // 使用后移除标志

                    if (_errorMessages.TryGetValue(scriptId, out string errorMsg))
                    {
                        // 有错误信息，认为是错误终止
                        statusData.Status = ScriptRuntimeStatus.Error;
                        statusData.Message = errorMsg;
                        _errorMessages.TryRemove(scriptId, out _); // 使用后移除错误
                    }
                    else if (wasCancelled)
                    {
                        // 用户取消
                        statusData.Status = ScriptRuntimeStatus.Cancelled;
                    }
                    else
                    {
                        // 正常完成
                        statusData.Status = ScriptRuntimeStatus.Completed;
                    }
                }
                else
                {
                    // 无任务结束标志，认为是空闲状态
                    statusData.Status = ScriptRuntimeStatus.Idle;
                }
            }
            else
            {
                // 脚本运行中，尝试获取详细状态
                try
                {
                    // 尝试获取当前任务
                    BaseTask runningTask = null;
                    try
                    {
                        // 使用扩展方法获取DDBuilder
                        var db = ScriptExtensions.GetDDBuilder(gameViewModel);
                        if (db != null && db.DB.ShareData != null)
                        {
                            runningTask = db.DB.ShareData.RunningTask;

                            // 获取当前子任务
                            statusData.CurrentTask = db.DB.ShareData.MainNowTask;
                        }
                    }
                    catch
                    {
                        // 获取BaseTask失败时忽略异常
                    }

                    if (runningTask != null)
                    {
                        // 根据BaseTask状态判断
                        if (runningTask.Ct.IsCancellationRequested)
                        {
                            statusData.Status = ScriptRuntimeStatus.Stopping;
                        }
                        else if (runningTask.Restarting)
                        {
                            statusData.Status = ScriptRuntimeStatus.Paused;
                            statusData.Message = "正在重启游戏";
                        }
                        else if (runningTask.YuHunFulling)
                        {
                            statusData.Status = ScriptRuntimeStatus.Waiting;
                            statusData.Message = "御魂已满，等待处理";
                        }
                        else
                        {
                            // 获取用户通知信息
                            statusData.Status = ScriptRuntimeStatus.Running;
                            if (!string.IsNullOrEmpty(runningTask.UserNotificationMessage))
                            {
                                statusData.Message = runningTask.UserNotificationMessage;
                            }
                        }
                    }
                    else
                    {
                        // 无法获取BaseTask，但脚本运行中，可能是刚启动或状态转换中
                        statusData.Status = ScriptRuntimeStatus.Running;
                    }
                }
                catch (Exception ex)
                {
                    // 获取详细状态异常，使用默认Running状态
                    statusData.Status = ScriptRuntimeStatus.Running;
                    System.Diagnostics.Debug.WriteLine($"获取脚本{scriptId}详细状态异常: {ex.Message}");
                }
            }

            return statusData;
        }

        /// <summary>
        /// 收集超级多开脚本状态
        /// </summary>
        private void CollectSuperMultiScriptStatus()
        {
            if (_superMultiVM == null) return;
            // 安全获取超级多开游戏列表
            var games = _superMultiVM.SuperMultiGame_DataModelCollection?.ToList();
            if (games == null) return;

            foreach (var game in games)
            {
                try
                {
                    // 获取内部脚本ID
                    int scriptId = -1;

                    // 使用扩展方法获取游戏对应的scriptId
                    if (ScriptExtensions.TryGetGameScriptId(_superMultiVM, game.GameId, out int gameScriptId))
                    {
                        scriptId = gameScriptId;
                    }
                    else
                    {
                        continue; // 找不到scriptId则跳过此游戏
                    }

                    string scriptInstanceId = $"SuperMultiGame_{game.GameId}";

                    // 获取当前状态
                    var currentStatus = GetSuperMultiScriptStatus(game, scriptId, scriptInstanceId);
                    if (currentStatus != null)
                    {
                        // 比较并上报状态变化
                        ReportStatusIfChanged(currentStatus);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"获取超级多开脚本状态异常: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 获取单个超级多开脚本的状态
        /// </summary>
        private ScriptStatusData GetSuperMultiScriptStatus(SuperMultiGame_DataModel game, int scriptId, string scriptInstanceId)
        {
            if (game == null) return null;

            // 创建状态数据对象
            var statusData = new ScriptStatusData
            {
                ScriptTasks = game?.TaskConfiguration?.GameTaskLists?.GetTasks() ?? [],
                ScriptInstanceId = scriptInstanceId,
                InternalScriptId = scriptId,
                ScriptType = "SuperMulti",
                FriendlyName = game.GameName ?? $"超级多开_{game.GameId}",
                CurrentTask = game.CurrentTask,
                Timestamp = DateTime.UtcNow
            };

            // 获取运行时间
            if (game.RunningDuration != TimeSpan.Zero)
                statusData.RunningDurationSeconds = game.RunningDuration.TotalSeconds;

            bool isRunning = Scripts.IsRunning(scriptId, out _);

            // 判断状态，优先使用超级多开的内部状态
            if (!string.IsNullOrEmpty(game.RunningStatus))
            {
                switch (game.RunningStatus)
                {
                    case "启动中":
                        statusData.Status = ScriptRuntimeStatus.Starting;
                        break;

                    case "运行中":
                        statusData.Status = ScriptRuntimeStatus.Running;
                        break;

                    case "未运行":
                    default:
                        // 未运行时，根据任务结束标志判断具体状态
                        if (_taskEndedFlags.TryGetValue(scriptId, out bool wasCancelled))
                        {
                            _taskEndedFlags.TryRemove(scriptId, out _); // 使用后移除标志

                            if (_errorMessages.TryGetValue(scriptId, out string errorMsg))
                            {
                                // 有错误信息，认为是错误终止
                                statusData.Status = ScriptRuntimeStatus.Error;
                                statusData.Message = errorMsg;
                                _errorMessages.TryRemove(scriptId, out _); // 使用后移除错误
                            }
                            else if (wasCancelled)
                            {
                                // 用户取消
                                statusData.Status = ScriptRuntimeStatus.Cancelled;
                            }
                            else
                            {
                                // 正常完成
                                statusData.Status = ScriptRuntimeStatus.Completed;
                            }
                        }
                        else
                        {
                            // 默认空闲状态
                            statusData.Status = ScriptRuntimeStatus.Idle;
                        }
                        break;
                }
            }
            else
            {
                // 没有RunningStatus时，退回到通用逻辑
                if (!isRunning)
                {
                    // 默认空闲状态
                    statusData.Status = ScriptRuntimeStatus.Idle;

                    // 检查任务结束标志
                    if (_taskEndedFlags.TryGetValue(scriptId, out bool wasCancelled))
                    {
                        _taskEndedFlags.TryRemove(scriptId, out _);

                        if (_errorMessages.TryGetValue(scriptId, out string errorMsg))
                        {
                            statusData.Status = ScriptRuntimeStatus.Error;
                            statusData.Message = errorMsg;
                            _errorMessages.TryRemove(scriptId, out _);
                        }
                        else
                        {
                            statusData.Status = wasCancelled ?
                                ScriptRuntimeStatus.Cancelled :
                                ScriptRuntimeStatus.Completed;
                        }
                    }
                }
                else
                {
                    // 默认运行中状态
                    statusData.Status = ScriptRuntimeStatus.Running;
                }
            }

            // 尝试获取更详细的状态（如果可能）
            try
            {
                // 尝试获取超级多开游戏关联的BaseTask
                BaseTask runningTask = null;
                try
                {
                    // 使用扩展方法获取DDBuilder
                    var ddBuilder = ScriptExtensions.GetDDBuilder(game);
                    if (ddBuilder != null && ddBuilder.DB.ShareData != null)
                    {
                        runningTask = ddBuilder.DB.ShareData.RunningTask;
                    }
                }
                catch
                {
                    // 获取超级多开BaseTask失败时忽略异常
                }

                if (runningTask != null && statusData.Status == ScriptRuntimeStatus.Running)
                {
                    // 细化运行中状态
                    if (runningTask.Ct.IsCancellationRequested)
                    {
                        statusData.Status = ScriptRuntimeStatus.Stopping;
                    }
                    else if (runningTask.Restarting)
                    {
                        statusData.Status = ScriptRuntimeStatus.Paused;
                        statusData.Message = "正在重启游戏";
                    }
                    else if (runningTask.YuHunFulling)
                    {
                        statusData.Status = ScriptRuntimeStatus.Waiting;
                        statusData.Message = "御魂已满，等待处理";
                    }

                    // 获取用户通知信息
                    if (!string.IsNullOrEmpty(runningTask.UserNotificationMessage))
                    {
                        statusData.Message = runningTask.UserNotificationMessage;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取超级多开脚本{scriptId}详细状态异常: {ex.Message}");
            }

            return statusData;
        }

        /// <summary>
        /// 比较并上报状态变化
        /// </summary>
        private void ReportStatusIfChanged(ScriptStatusData currentStatus)
        {
            if (currentStatus == null) return;

            string scriptInstanceId = currentStatus.ScriptInstanceId;

            // 获取上一次上报的状态
            _lastReportedStates.TryGetValue(scriptInstanceId, out var lastReportedStatus);

            // 比较状态是否发生重要变化
            if (lastReportedStatus == null || currentStatus.SignificantlyDifferentFrom(lastReportedStatus))
            {
                // 状态有变化，上报并更新记录
                SendStatusUpdate(currentStatus);
                _lastReportedStates[scriptInstanceId] = currentStatus.Clone();
            }
        }

        #endregion 脚本状态轮询

        #region 事件监听与回调

        /// <summary>
        /// 脚本任务结束回调
        /// </summary>
        private void ScriptTaskEndedCallback(object sender, ScriptCallBackDataModel callBackData)
        {
            try
            {
                int scriptId = callBackData?.ScriptId ?? -1;
                if (scriptId < 0) return;

                // 判断任务结束原因
                bool isCancelled = callBackData.IsCancel;

                // 记录任务结束状态和错误信息
                _taskEndedFlags[scriptId] = isCancelled;

                // 如果有错误信息
                if (!string.IsNullOrEmpty(callBackData.ErrorMessage))
                {
                    _errorMessages[scriptId] = callBackData.ErrorMessage;
                }

                // 立即触发一次状态检查以上报结束状态
                Task.Run(() => PollScriptStatus(null));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理脚本结束回调异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 监听超级多开游戏列表中每个游戏的属性变化
        /// </summary>
        private void AttachSuperMultiGameListeners()
        {
            if (_superMultiVM == null || _superMultiVM.SuperMultiGame_DataModelCollection == null)
                return;

            // 现有游戏添加监听
            foreach (var game in _superMultiVM.SuperMultiGame_DataModelCollection)
            {
                AttachSuperMultiGamePropertyChangedHandler(game);
            }

            // 监听集合变化以处理新增游戏
            // 注意：此处假设SuperMultiGame_DataModelCollection是ObservableCollection
            // 如果不是，需要通过其他方式监测集合变化
            var collection = _superMultiVM.SuperMultiGame_DataModelCollection as System.Collections.Specialized.INotifyCollectionChanged;
            if (collection != null)
            {
                collection.CollectionChanged += (sender, e) =>
                {
                    // 处理新增项
                    if (e.NewItems != null)
                    {
                        foreach (SuperMultiGame_DataModel newGame in e.NewItems)
                        {
                            AttachSuperMultiGamePropertyChangedHandler(newGame);
                        }
                    }
                };
            }
        }

        /// <summary>
        /// 为单个超级多开游戏添加属性变化监听
        /// </summary>
        private void AttachSuperMultiGamePropertyChangedHandler(SuperMultiGame_DataModel game)
        {
            if (game == null) return;

            // 监听属性变化
            game.PropertyChanged += SuperMultiGame_PropertyChanged;
        }

        /// <summary>
        /// 超级多开游戏属性变化事件处理
        /// </summary>
        private void SuperMultiGame_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            try
            {
                if (sender is SuperMultiGame_DataModel game)
                {
                    // 只关注RunningStatus和CurrentTask属性变化
                    if (e.PropertyName == "RunningStatus" || e.PropertyName == "CurrentTask")
                    {
                        // 使用扩展方法获取游戏对应的脚本ID
                        if (ScriptExtensions.TryGetGameScriptId(_superMultiVM, game.GameId, out int scriptId))
                        {
                            string scriptInstanceId = $"SuperMultiGame_{game.GameId}";

                            // 获取当前状态并上报
                            var currentStatus = GetSuperMultiScriptStatus(game, scriptId, scriptInstanceId);
                            if (currentStatus != null)
                            {
                                ReportStatusIfChanged(currentStatus);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理超级多开属性变化异常: {ex.Message}");
            }
        }

        #endregion 事件监听与回调

        #region WebSocket消息发送

        /// <summary>
        /// 发送状态更新
        /// </summary>
        private void SendStatusUpdate(ScriptStatusData statusData)
        {
            if (statusData == null) return;

            try
            {
                // 创建WebSocket消息
                var message = new ScriptStatusUpdateMessage
                {
                    Payload = statusData
                };

                // 通过WebSocket发送
                Task.Run(async () =>
                {
                    try
                    {
                        // 使用XWebsocket发送消息
                        await XWebsocket.SendObjectAsync(message);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"发送WebSocket状态更新异常: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"准备WebSocket状态更新异常: {ex.Message}");
            }
        }

        #endregion WebSocket消息发送

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Stop();
            _pollingTimer?.Dispose();
            _pollingTimer = null;
        }

        #endregion IDisposable实现

        /// <summary>
        /// 获取所有脚本状态
        /// </summary>
        /// <returns>所有脚本状态的列表</returns>
        public List<ScriptStatusData> GetAllScriptStatuses()
        {
            var result = new List<ScriptStatusData>();

            try
            {
                // 获取常规脚本状态
                for (int gameIndex = 0; gameIndex < 4; gameIndex++)
                {
                    try
                    {
                        // 从GlobalData获取游戏视图模型
                        var gameViewModel = GlobalData.Instance.GetGameViewModel(gameIndex);
                        if (gameViewModel == null) continue;

                        int scriptId = gameViewModel.ScriptId;
                        string scriptInstanceId = gameViewModel.GameName;

                        // 获取当前状态
                        var statusData = GetRegularScriptStatus(gameViewModel);
                        if (statusData != null)
                        {
                            result.Add(statusData);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取常规脚本状态异常: {ex.Message}");
                    }
                }

                // 如果有超级多开视图模型，获取超级多开脚本状态
                if (_superMultiVM != null)
                {
                    // 安全获取超级多开游戏列表
                    var games = _superMultiVM.SuperMultiGame_DataModelCollection?.ToList();
                    if (games != null)
                    {
                        foreach (var game in games)
                        {
                            try
                            {
                                if (game == null) continue;

                                // 获取内部脚本ID
                                int scriptId = -1;

                                // 使用扩展方法获取游戏对应的scriptId
                                if (ScriptExtensions.TryGetGameScriptId(_superMultiVM, game.GameId, out int gameScriptId))
                                {
                                    scriptId = gameScriptId;
                                }
                                else
                                {
                                    continue; // 找不到scriptId则跳过此游戏
                                }

                                string scriptInstanceId = $"SuperMultiGame_{game.GameId}";

                                // 获取状态
                                var statusData = GetSuperMultiScriptStatus(game, scriptId, scriptInstanceId);
                                if (statusData != null)
                                {
                                    result.Add(statusData);
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"获取超级多开脚本状态异常: {ex.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取所有脚本状态时发生错误: {ex.Message}");
            }

            return result;
        }
    }
}