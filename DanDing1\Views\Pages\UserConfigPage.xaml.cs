﻿using DanDing1.ViewModels.Pages;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Wpf.Ui.Controls;
using Wpf.Ui.Abstractions.Controls;
using Microsoft.Extensions.DependencyInjection;
using Wpf.Ui;
using Image = System.Windows.Controls.Image;
using Button = System.Windows.Controls.Button;
using MessageBox = System.Windows.MessageBox;

namespace DanDing1.Views.Pages
{
    /// <summary>
    /// UserConfigPage.xaml 的交互逻辑
    /// </summary>
    public partial class UserConfigPage : INavigableView<UserConfigViewModel>
    {
        private readonly IContentDialogService _contentDialogService;

        public UserConfigPage(UserConfigViewModel viewModel)
        {
            ViewModel = viewModel;
            DataContext = this;
            InitializeComponent();

            // 获取ContentDialogService
            _contentDialogService = App.GetService<IContentDialogService>();
        }

        public UserConfigViewModel ViewModel { get; }

        private void TextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            System.Windows.Controls.TextBox textBox = sender as System.Windows.Controls.TextBox;
            if (textBox != null)
            {
                // 尝试将输入内容转换为整数
                if (!int.TryParse(textBox.Text, out int result))
                    textBox.Text = "0";
            }
        }

        private void DecimalTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            System.Windows.Controls.TextBox textBox = sender as System.Windows.Controls.TextBox;
            if (textBox != null)
            {
                // 尝试将输入内容转换为小数
                if (!double.TryParse(textBox.Text, out double result))
                    textBox.Text = "0.0";
            }
        }

        /// <summary>
        /// 显示达摩奖励界面图片
        /// </summary>
        private void ShowDamoHelpImage_Click(object sender, RoutedEventArgs e)
        {
            ShowImageDialog("达摩奖励界面说明", "pack://application:,,,/Assets/达摩奖励界面.jpg");
        }

        /// <summary>
        /// 显示胜利、失败界面图片
        /// </summary>
        private void ShowResultHelpImage_Click(object sender, RoutedEventArgs e)
        {
            ShowImageDialog("胜利、失败界面说明", "pack://application:,,,/Assets/胜利、失败界面.jpg");
        }

        /// <summary>
        /// 显示二维码图片，用于绑定公众号
        /// </summary>
        private void ShowQrCodeImage_Click(object sender, RoutedEventArgs e)
        {
            ShowQrCodeDialog("扫码绑定公众号", "https://wxpusher.zjiecode.com/api/qrcode/FN3S0OWlDdvgi5AbGMbB9Z5QIjskzzqj2EvkpwVoJVe9qugOh20Fn0KrIszLsytl.jpg");
        }

        /// <summary>
        /// 显示Ntfy帮助网页
        /// </summary>
        private void ShowNtfyHelp_Click(object sender, RoutedEventArgs e)
        {
            string url = "https://www.danding.vip/%E5%8A%9F%E8%83%BD%E5%B8%AE%E5%8A%A9/%E9%80%9A%E7%9F%A5%E6%96%B9%E5%BC%8F/";
            try
            {
                // 使用系统默认浏览器打开URL
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"无法打开帮助页面: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 显示Pushplus帮助网页
        /// </summary>
        private void ShowPushplusHelp_Click(object sender, RoutedEventArgs e)
        {
            string url = "https://www.pushplus.plus/doc/function/one.html";
            try
            {
                // 使用系统默认浏览器打开URL
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"无法打开帮助页面: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 显示喵提醒帮助网页
        /// </summary>
        private void ShowMiaotixingHelp_Click(object sender, RoutedEventArgs e)
        {
            string url = "https://www.showdoc.com.cn/miaotixing/9163883331817032";
            try
            {
                // 使用系统默认浏览器打开URL
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"无法打开帮助页面: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 显示二维码对话框方法
        /// </summary>
        /// <param name="title">对话框标题</param>
        /// <param name="imageUrl">二维码图片URL</param>
        private void ShowQrCodeDialog(string title, string imageUrl)
        {
            // 创建窗口显示二维码
            Window qrCodeWindow = new Window
            {
                Title = title,
                Width = 400,
                Height = 500,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                ResizeMode = ResizeMode.NoResize
            };

            // 创建主布局
            Grid grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // 创建图片控件
            Image image = new Image();
            image.Stretch = Stretch.Uniform;
            image.Margin = new Thickness(20);
            Grid.SetRow(image, 0);

            // 异步加载网络图片
            try
            {
                BitmapImage bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(imageUrl);
                bitmap.CacheOption = BitmapCacheOption.OnLoad;
                bitmap.EndInit();
                image.Source = bitmap;
            }
            catch (Exception ex)
            {
                // 加载失败时显示错误信息
                System.Windows.Controls.TextBlock errorText = new System.Windows.Controls.TextBlock
                {
                    Text = $"加载二维码失败: {ex.Message}",
                    TextWrapping = TextWrapping.Wrap,
                    Margin = new Thickness(20),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };
                Grid.SetRow(errorText, 0);
                grid.Children.Add(errorText);
            }

            // 创建说明文本
            System.Windows.Controls.TextBlock instructionText = new System.Windows.Controls.TextBlock
            {
                Text = "请使用微信扫描上方二维码绑定公众号",
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(20, 0, 20, 10),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            Grid.SetRow(instructionText, 1);

            // 创建按钮面板
            StackPanel buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            Grid.SetRow(buttonPanel, 2);

            // 创建关闭按钮
            System.Windows.Controls.Button closeButton = new System.Windows.Controls.Button
            {
                Content = "关闭",
                Padding = new Thickness(30, 5, 30, 5),
                Margin = new Thickness(10)
            };
            closeButton.Click += (s, args) => qrCodeWindow.Close();
            buttonPanel.Children.Add(closeButton);

            // 添加控件到布局
            grid.Children.Add(image);
            grid.Children.Add(instructionText);
            grid.Children.Add(buttonPanel);

            // 设置窗口内容并显示
            qrCodeWindow.Content = grid;
            qrCodeWindow.ShowDialog();
        }

        /// <summary>
        /// 通用图片显示对话框方法
        /// </summary>
        /// <param name="title">对话框标题</param>
        /// <param name="imagePath">图片资源路径</param>
        private void ShowImageDialog(string title, string imagePath)
        {
            // 创建窗口显示图片
            Window imageWindow = new Window
            {
                Title = title,
                Width = 800,
                Height = 600,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                ResizeMode = ResizeMode.NoResize
            };

            // 创建主布局
            Grid grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // 创建图片控件
            Image image = new Image();
            image.Stretch = Stretch.Uniform;
            Grid.SetRow(image, 0);

            // 加载图片
            BitmapImage bitmap = new BitmapImage();
            bitmap.BeginInit();
            bitmap.UriSource = new Uri(imagePath);
            bitmap.CacheOption = BitmapCacheOption.OnLoad;
            bitmap.EndInit();
            image.Source = bitmap;

            // 创建按钮面板
            StackPanel buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 10, 0, 10)
            };
            Grid.SetRow(buttonPanel, 1);

            // 创建关闭按钮
            System.Windows.Controls.Button closeButton = new System.Windows.Controls.Button
            {
                Content = "关闭",
                Padding = new Thickness(30, 5, 30, 5),
                Margin = new Thickness(10)
            };
            closeButton.Click += (s, args) => imageWindow.Close();
            buttonPanel.Children.Add(closeButton);

            // 添加控件到布局
            grid.Children.Add(image);
            grid.Children.Add(buttonPanel);

            // 设置窗口内容并显示
            imageWindow.Content = grid;
            imageWindow.ShowDialog();
        }

        /// <summary>
        /// 测试通知按钮点击事件
        /// </summary>
        /// <param name="sender">按钮</param>
        /// <param name="e">事件参数</param>
        private async void TestNotification_Click(object sender, RoutedEventArgs e)
        {
            if (sender is not Button button) return;

            // 获取通知类型（从按钮的Tag属性）
            string noticeType = button.Tag as string;
            if (string.IsNullOrEmpty(noticeType))
            {
                MessageBox.Show("未指定通知类型", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // 修改按钮内容和状态
            var originalContent = button.Content;
            var originalIsEnabled = button.IsEnabled;
            button.IsEnabled = false;

            // 如果是图标按钮，显示加载状态
            if (button.Content is SymbolIcon symbolIcon)
            {
                symbolIcon.Symbol = SymbolRegular.ArrowSync24;
                // 添加旋转动画
                if (button.Content is FrameworkElement element)
                {
                    var rotateAnimation = new RotateTransform();
                    element.RenderTransform = rotateAnimation;
                    element.RenderTransformOrigin = new Point(0.5, 0.5);
                    
                    var animation = new DoubleAnimation
                    {
                        From = 0,
                        To = 360,
                        Duration = TimeSpan.FromMilliseconds(1000),
                        RepeatBehavior = RepeatBehavior.Forever
                    };
                    rotateAnimation.BeginAnimation(RotateTransform.AngleProperty, animation);
                }
            }
            else
            {
                button.Content = "发送中...";
            }

            try
            {
                // 发送测试通知
                bool result = await ViewModel.TestNotificationAsync(noticeType);

                // 显示结果
                if (result)
                {
                    MessageBox.Show($"{noticeType}测试通知发送成功！", "通知测试成功", System.Windows.MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show($"{noticeType}测试通知发送失败，请检查配置和网络连接。", "通知测试失败", System.Windows.MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发送测试通知时出错: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 恢复按钮状态
                button.IsEnabled = originalIsEnabled;

                // 停止旋转动画
                if (button.Content is FrameworkElement element && element.RenderTransform is RotateTransform rotateTransform)
                {
                    rotateTransform.BeginAnimation(RotateTransform.AngleProperty, null);
                }

                // 恢复原始内容
                if (originalContent is SymbolIcon originalSymbolIcon)
                {
                    if (button.Content is SymbolIcon currentSymbolIcon)
                    {
                        currentSymbolIcon.Symbol = originalSymbolIcon.Symbol;
                    }
                }
                else
                {
                    button.Content = originalContent;
                }
            }
        }
    }
}