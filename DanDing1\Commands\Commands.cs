﻿using XHelper;
using ScriptEngine.MuMu;
using System.Collections.ObjectModel;
using DanDing1.Models;

namespace DanDing1.Commands
{
    /// <summary>
    /// 命令管理器
    /// </summary>
    internal class Commands
    {
        private readonly List<string> _commandHeaders = ["测试", "test", "get"];
        private readonly Dictionary<string, BaseCommand> _commands;
        private Task _currentTask;

        public Commands()
        {
            var getCommands = new GetCommands();

            _commands = new Dictionary<string, BaseCommand>
            {
                { "game1", new Game1Commands() },
                { "获取图库", new SystemCommands() },
                { "log", new SystemCommands() },
                { "日志", new SystemCommands() },
                { "mumu", new MuMuCommands() },
                { "ocr", new Test_Ocr() },
                { "yolo", new Test_Yolo() },
                { "bind", new Test_Bind() },
                { "systeminfo", getCommands },
                { "notif", new NotificationCommands() },
                { "notification", new NotificationCommands() },
                { "通知", new NotificationCommands() },
                { "ws", new WebSocketCommands() },
                { "websocket", new WebSocketCommands() }
            };
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="command">命令字符串</param>
        /// <returns>是否成功执行</returns>
        public async Task<bool> Do(string command)
        {
            var commandParts = command.Split(' ');
            if (!_commandHeaders.Contains(commandParts[0]))
                return false;

            if (commandParts.Length < 2)
            {
                XLogger.Error("命令格式不正确");
                return false;
            }

            // 特殊处理get命令
            if (commandParts[0].ToLower() == "get")
            {
                var getCommand = new GetCommands();
                _currentTask = Task.Run(() => getCommand.Execute(commandParts));
                await Task.Delay(500);
                return true;
            }

            string commandKey = commandParts[1].ToLower();
            if (!_commands.TryGetValue(commandKey, out var commandHandler))
            {
                XLogger.Error($"未知的命令: {commandParts[1]}");
                return false;
            }

            _currentTask = Task.Run(() => commandHandler.Execute(commandParts));
            await Task.Delay(500);
            return true;
        }
    }
}