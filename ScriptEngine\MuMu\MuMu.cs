﻿using ScriptEngine.Interface;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using XHelper;

namespace ScriptEngine.MuMu
{
    /// <summary>
    /// 命令执行结果类，统一处理命令输出
    /// </summary>
    public class CommandResult
    {
        /// <summary>
        /// 构造命令执行结果对象
        /// </summary>
        public CommandResult(bool success, string output, string error, int? exitCode = null)
        {
            Success = success;
            Output = output ?? string.Empty;
            Error = error ?? string.Empty;
            ExitCode = exitCode;
        }

        /// <summary>
        /// 包含有效JSON数据
        /// </summary>
        public bool ContainsValidJson => Output.Contains("{") && Output.Contains("}");

        /// <summary>
        /// 错误输出内容
        /// </summary>
        public string Error { get; }

        /// <summary>
        /// 退出代码
        /// </summary>
        public int? ExitCode { get; }

        /// <summary>
        /// 标准输出内容
        /// </summary>
        public string Output { get; }

        /// <summary>
        /// 命令是否成功执行
        /// </summary>
        public bool Success { get; }

        /// <summary>
        /// 静态方法: 创建失败结果
        /// </summary>
        public static CommandResult Failed(string error) => new CommandResult(false, string.Empty, error);

        /// <summary>
        /// 静态方法: 创建成功结果
        /// </summary>
        public static CommandResult Succeeded(string output) => new CommandResult(true, output, string.Empty);

        /// <summary>
        /// 转换为元组格式，兼容原有代码
        /// </summary>
        public void Deconstruct(out string output, out string error, out bool success)
        {
            output = Output;
            error = Error;
            success = Success;
        }
    }

    /// <summary>
    /// MuMu模拟器引擎ADB控制类
    /// </summary>
    public class MuMu : ISimulatorEngine
    {
        /// <summary>
        /// 命令执行超时时间（毫秒）
        /// </summary>
        private const int CommandTimeoutMs = 60000;

        private readonly object _lockObject = new object();

        private readonly Dictionary<string, string> _PackageInfoCache = new Dictionary<string, string>()
        {
            {"阴阳师","com.netease.onmyoji.wyzymnqsd_cps"},
            {"阴阳师-官服","com.netease.onmyoji.wyzymnqsd_cps"},
            {"阴阳师-小米","com.netease.onmyoji.mi"},
            {"阴阳师-华为","com.netease.onmyoji.huawei"},
            {"阴阳师-bilibili","com.netease.onmyoji.bili"},
        };

        /// <summary>
        /// mumu所在文件夹
        /// </summary>
        private string _exeDir;

        /// <summary>
        /// mumu可执行文件路径
        /// </summary>
        private string _exePath;

        /// <summary>
        /// 是否已连接到ADB设备
        /// </summary>
        private bool _isConnected = false;

        /// <summary>
        /// 端口
        /// </summary>
        private int _port = 5555;

        public MuMu()
        {
            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (path is not null)
                Init(path);
        }

        /// <summary>
        /// adb可执行文件路径
        /// </summary>
        private string _adbPath => System.IO.Path.Combine(_exeDir, "adb.exe");

        /// <summary>
        /// mumu管理器可执行文件路径
        /// </summary>
        private string _MuMuManagerPath => System.IO.Path.Combine(_exeDir, "MuMuManager.exe");

        /// <summary>
        /// 获取所有模拟器实例（同步方法，内部调用异步实现）
        /// </summary>
        /// <returns></returns>
        public MuMuInstances? _GetInstances()
        {
            // 使用异步方法的同步包装，通过等待异步任务完成来获取结果
            try
            {
                return Task.Run(async () => await _GetInstancesAsync()).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                XLogger.Debug($"同步调用异步方法时发生异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取所有模拟器实例
        /// </summary>
        /// <returns></returns>
        public async Task<MuMuInstances?> _GetInstancesAsync()
        {
            // ADB命令：MuMuManager.exe info -v all
            if (!CheckLegality()) return null;
            try
            {
                // 构建 ADB 连接命令
                string adbCommand = "info -v all";
                //XLogger.Debug($"开始执行命令获取模拟器实例: {_MuMuManagerPath} {adbCommand}");

                // 执行命令，使用带重试的版本
                var result = await ExecuteCommandWithRetryAsync($"\"{_MuMuManagerPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug("无法启动进程获取模拟器实例");
                    return null;
                }

                // 处理输出，移除warning信息（不再输出MuMuManager进程已完成执行的消息）
                var outputLines = result.Output.Split('\n')
                    .Where(line => !line.Contains("CPU random generator") &&
                                 !line.Contains("RDRND generated"))
                    .ToList();

                string cleanedOutput = string.Join("\n", outputLines).Trim();
                if (string.IsNullOrWhiteSpace(cleanedOutput))
                {
                    XLogger.Debug("没有获取到有效的模拟器实例信息");
                    return null;
                }

                // 记录JSON数据以便调试
                try
                {
                    string logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                    Directory.CreateDirectory(logPath);
                    string jsonLogFile = Path.Combine(logPath, "mumu_json_output.json");
                    File.WriteAllText(jsonLogFile, cleanedOutput);
                    //XLogger.Debug($"已保存MuMu JSON输出到: {jsonLogFile}");
                }
                catch (Exception ex)
                {
                    XLogger.Debug($"保存JSON日志失败: {ex.Message}");
                }

                // 尝试解析 JSON 输出
                MuMuInstances instances = null;
                try
                {
                    instances = MuMuInstances.FromJson(cleanedOutput);
                }
                catch (JsonException ex)
                {
                    XLogger.Debug($"解析JSON失败: {ex.Message}");
                    XLogger.Debug($"JSON内容片段: {(cleanedOutput.Length > 200 ? cleanedOutput.Substring(0, 200) + "..." : cleanedOutput)}");

                    // 尝试定位错误位置
                    if (ex.Message.Contains("line") && ex.Message.Contains("position"))
                    {
                        try
                        {
                            // 尝试提取行号
                            var lineMatch = Regex.Match(ex.Message, @"line (\d+)");
                            var posMatch = Regex.Match(ex.Message, @"position (\d+)");

                            if (lineMatch.Success && posMatch.Success)
                            {
                                int lineNum = int.Parse(lineMatch.Groups[1].Value);
                                int pos = int.Parse(posMatch.Groups[1].Value);

                                string[] lines = cleanedOutput.Split('\n');
                                if (lineNum <= lines.Length)
                                {
                                    string errorLine = lines[lineNum - 1];
                                    XLogger.Debug($"错误行({lineNum}): {errorLine}");

                                    if (pos <= errorLine.Length)
                                    {
                                        string beforeError = errorLine.Substring(0, Math.Min(pos, errorLine.Length));
                                        XLogger.Debug($"错误位置前的内容: {beforeError}");
                                    }
                                }
                            }
                        }
                        catch (Exception locateEx)
                        {
                            XLogger.Debug($"定位JSON错误位置时失败: {locateEx.Message}");
                        }
                    }

                    return null;
                }

                if (instances == null)
                    XLogger.Debug("解析JSON结果为空，请检查输出格式是否符合预期");
                //XLogger.Debug($"成功解析实例数：{instances.Instances?.Count ?? 0}");
                return instances;
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"Exception occurred while getting instances: {ex.Message}");
                XLogger.Debug($"Stack trace: {ex.StackTrace}");
                return null;
            }
        }

        /// <summary>
        /// 检查合法
        /// </summary>
        /// <returns></returns>
        public bool CheckLegality()
        {
            //检查Dir下有没有adb.exe
            if (_exeDir == null || _exePath == null)
            {
                XLogger.Debug("MuMu路径未设置");
                return false;
            }

            var adbPath = System.IO.Path.Combine(_exeDir, "adb.exe");
            var managerPath = System.IO.Path.Combine(_exeDir, "MuMuManager.exe");

            if (!System.IO.File.Exists(adbPath))
            {
                XLogger.Debug($"adb.exe不存在，路径: {adbPath}");
                return false;
            }

            if (!System.IO.File.Exists(managerPath))
            {
                XLogger.Debug($"MuMuManager.exe不存在，路径: {managerPath}");
                return false;
            }

            return true;
        }

        public void Close()
        {
            try
            {
                if (!CheckLegality()) return;

                // 构建 ADB 连接命令
                string arguments = $"api -v 0 shutdown_player";
                var result = ExecuteCommand($"\"{_MuMuManagerPath}\"", arguments);

                if (!result.Success)
                {
                    XLogger.Debug("无法启动进程关闭模拟器");
                    return;
                }

                // 检查是否有错误输出
                if (!string.IsNullOrEmpty(result.Error))
                {
                    XLogger.Debug($"Error occurred while executing command: {result.Error}");
                }

                _isConnected = false;
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"Exception occurred while closing emulator: {ex.Message}");
            }
        }

        /// <summary>
        /// 按照索引关闭指定模拟器
        /// </summary>
        /// <param name="index">模拟器索引</param>
        public void CloseByIndex(int index)
        {
            if (!CheckLegality()) return;

            try
            {
                // 构建关闭模拟器命令
                string adbCommand = $"control -v {index} shutdown";

                // 执行命令
                var result = ExecuteCommand($"\"{_MuMuManagerPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug($"无法启动进程关闭模拟器，索引: {index}");
                    return;
                }

                // 检查是否有错误
                if (!string.IsNullOrEmpty(result.Error))
                {
                    XLogger.Debug($"关闭模拟器时出错，索引: {index}, 错误: {result.Error}");
                    return;
                }

                XLogger.Debug($"已发送关闭命令给模拟器，索引: {index}");
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"关闭模拟器时出现异常，索引: {index}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 连接adb设备
        /// </summary>
        /// <param name="port"></param>
        /// <returns></returns>
        public bool ConnectToAdbDevice(int port)
        {
            if (!CheckLegality()) return false;
            try
            {
                // 构建 ADB 连接命令
                string adbCommand = $"connect 127.0.0.1:{port}";
                _port = port;

                // 执行命令
                var result = ExecuteCommand($"\"{_adbPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug("无法启动进程连接ADB设备");
                    return false;
                }

                // 检查输出，判断是否连接成功
                if (result.Output.Contains("connected") || result.Output.Contains("already connected"))
                {
                    // 初步连接成功，但需要验证设备状态是否正常
                    XLogger.Debug($"ADB连接命令执行成功，正在检查设备状态...");

                    // 给设备一些时间完成初始化
                    Thread.Sleep(1000);

                    // 检查设备状态
                    bool deviceReady = CheckDeviceStatus(port);

                    // 如果设备未就绪，尝试多次重试
                    if (!deviceReady)
                    {
                        XLogger.Debug($"设备连接但状态未就绪，开始重试...");
                        int maxRetries = 5;
                        for (int i = 0; i < maxRetries; i++)
                        {
                            XLogger.Debug($"等待设备就绪，尝试 {i + 1}/{maxRetries}");
                            Thread.Sleep(2000); // 等待2秒

                            deviceReady = CheckDeviceStatus(port);
                            if (deviceReady)
                            {
                                XLogger.Debug($"设备已就绪，状态正常");
                                break;
                            }
                        }
                    }

                    _isConnected = deviceReady;
                    if (deviceReady)
                    {
                        XLogger.Debug($"成功连接到ADB设备端口 {port}，设备状态正常");
                    }
                    else
                    {
                        XLogger.Debug($"虽然连接到ADB端口 {port}，但设备处于离线状态，请检查模拟器");
                    }

                    return deviceReady;
                }
                else
                {
                    // 如果连接失败，记录错误信息
                    XLogger.Debug($"无法连接到ADB设备，端口 {port}。错误: {result.Error}");

                    // 检查是否包含特定错误信息，提示用户重启电脑
                    if (result.Error.Contains("daemon not running; starting now at tcp:") &&
                        result.Error.Contains("could not read ok from ADB Server") &&
                        result.Error.Contains("failed to start daemon") &&
                        result.Error.Contains("error: cannot connect to daemon"))
                    {
                        XLogger.Debug("检测到ADB守护进程启动失败，需要重启电脑解决");
                    }

                    _isConnected = false;
                    return false;
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"Exception occurred while connecting to ADB device: {ex.Message}");
                _isConnected = false;
                return false;
            }
        }

        /// <summary>
        /// 异步连接adb设备
        /// </summary>
        /// <param name="port">端口号</param>
        /// <returns>连接是否成功</returns>
        public async Task<bool> ConnectToAdbDeviceAsync(int port)
        {
            if (!CheckLegality()) return false;
            try
            {
                // 构建 ADB 连接命令
                string adbCommand = $"connect 127.0.0.1:{port}";
                _port = port;

                // 执行命令
                var result = await ExecuteCommandWithRetryAsync($"\"{_adbPath}\"", adbCommand, maxRetries: 2);

                if (!result.Success)
                {
                    XLogger.Debug("无法启动进程连接ADB设备");
                    return false;
                }

                // 检查输出，判断是否连接成功
                if (result.Output.Contains("connected") || result.Output.Contains("already connected"))
                {
                    // 初步连接成功，但需要验证设备状态是否正常
                    XLogger.Debug($"ADB连接命令执行成功，正在检查设备状态...");

                    // 给设备一些时间完成初始化
                    await Task.Delay(1000);

                    // 检查设备状态
                    bool deviceReady = await CheckDeviceStatusAsync(port);

                    // 如果设备未就绪，尝试多次重试
                    if (!deviceReady)
                    {
                        XLogger.Debug($"设备连接但状态未就绪，开始重试...");
                        int maxRetries = 5;
                        for (int i = 0; i < maxRetries; i++)
                        {
                            XLogger.Debug($"等待设备就绪，尝试 {i + 1}/{maxRetries}");
                            await Task.Delay(2000); // 等待2秒

                            deviceReady = await CheckDeviceStatusAsync(port);
                            if (deviceReady)
                            {
                                XLogger.Debug($"设备已就绪，状态正常");
                                break;
                            }
                        }
                    }

                    _isConnected = deviceReady;
                    if (deviceReady)
                    {
                        XLogger.Debug($"成功连接到ADB设备端口 {port}，设备状态正常");
                    }
                    else
                    {
                        XLogger.Debug($"虽然连接到ADB端口 {port}，但设备处于离线状态，请检查模拟器");
                    }

                    return deviceReady;
                }
                else
                {
                    // 如果连接失败，记录错误信息
                    XLogger.Debug($"无法连接到ADB设备，端口 {port}。错误: {result.Error}");

                    // 检查是否包含特定错误信息，提示用户重启电脑
                    if (result.Error.Contains("daemon not running; starting now at tcp:") &&
                        result.Error.Contains("could not read ok from ADB Server") &&
                        result.Error.Contains("failed to start daemon") &&
                        result.Error.Contains("error: cannot connect to daemon"))
                    {
                        XLogger.Debug("检测到ADB守护进程启动失败，需要重启电脑解决");
                    }

                    _isConnected = false;
                    return false;
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"连接ADB设备时发生异常: {ex.Message}");
                _isConnected = false;
                return false;
            }
        }

        /// <summary>
        /// 同步获取指定索引模拟器的ADB端口
        /// </summary>
        /// <param name="index">模拟器索引</param>
        /// <returns>包含查找结果的元组(是否成功, ADB端口)</returns>
        public (bool success, int port) GetAdbPortByIndex(string index)
        {
            try
            {
                return Task.Run(async () => await GetAdbPortByIndexAsync(index)).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                XLogger.Debug($"同步调用异步方法时发生异常: {ex.Message}");
                return (false, 0);
            }
        }

        /// <summary>
        /// 同步获取指定索引模拟器的ADB端口
        /// </summary>
        /// <param name="index">模拟器索引（整数）</param>
        /// <returns>包含查找结果的元组(是否成功, ADB端口)</returns>
        public (bool success, int port) GetAdbPortByIndex(int index)
        {
            return GetAdbPortByIndex(index.ToString());
        }

        /// <summary>
        /// 异步获取指定索引模拟器的ADB端口
        /// </summary>
        /// <param name="index">模拟器索引</param>
        /// <returns>包含查找结果的元组(是否成功, ADB端口)</returns>
        public async Task<(bool success, int port)> GetAdbPortByIndexAsync(string index)
        {
            if (!CheckLegality()) return (false, 0);

            try
            {
                // 获取所有模拟器实例
                var instances = await _GetInstancesAsync();
                if (instances?.Instances == null || instances.Instances.Count == 0)
                {
                    XLogger.Debug("获取模拟器实例失败，无法获取ADB端口");
                    return (false, 0);
                }

                // 查找指定索引的模拟器
                if (instances.Instances.TryGetValue(index, out var instance))
                {
                    // 检查是否有ADB端口信息
                    if (instance.AdbPort.HasValue)
                    {
                        int port = instance.AdbPort.Value;
                        XLogger.Debug($"成功获取到模拟器(索引:{index})的ADB端口: {port}");
                        return (true, port);
                    }
                    else
                    {
                        XLogger.Debug($"模拟器(索引:{index})未包含ADB端口信息");
                        return (false, 0);
                    }
                }
                else
                {
                    XLogger.Debug($"未找到索引为 {index} 的模拟器实例");
                    return (false, 0);
                }
            }
            catch (Exception ex)
            {
                XLogger.Debug($"获取模拟器ADB端口时发生异常: {ex.Message}");
                return (false, 0);
            }
        }

        /// <summary>
        /// 异步获取指定索引模拟器的ADB端口
        /// </summary>
        /// <param name="index">模拟器索引（整数）</param>
        /// <returns>包含查找结果的元组(是否成功, ADB端口)</returns>
        public Task<(bool success, int port)> GetAdbPortByIndexAsync(int index)
        {
            return GetAdbPortByIndexAsync(index.ToString());
        }

        /// <summary>
        /// 同步获取所有在线模拟器的ADB端口信息
        /// </summary>
        /// <returns>模拟器索引和ADB端口的字典</returns>
        public Dictionary<string, int> GetAllAdbPorts()
        {
            try
            {
                return Task.Run(async () => await GetAllAdbPortsAsync()).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                XLogger.Debug($"同步调用异步方法时发生异常: {ex.Message}");
                return new Dictionary<string, int>();
            }
        }

        /// <summary>
        /// 异步获取所有在线模拟器的ADB端口信息
        /// </summary>
        /// <returns>模拟器索引和ADB端口的字典</returns>
        public async Task<Dictionary<string, int>> GetAllAdbPortsAsync()
        {
            if (!CheckLegality()) return new Dictionary<string, int>();

            try
            {
                // 获取所有模拟器实例
                var instances = await _GetInstancesAsync();
                if (instances?.Instances == null || instances.Instances.Count == 0)
                {
                    XLogger.Debug("获取模拟器实例失败，无法获取ADB端口");
                    return new Dictionary<string, int>();
                }

                // 创建索引-端口映射字典
                Dictionary<string, int> indexPortMap = new Dictionary<string, int>();

                // 遍历所有模拟器实例
                foreach (var kvp in instances.Instances)
                {
                    if (kvp.Value.AdbPort.HasValue)
                    {
                        indexPortMap[kvp.Key] = kvp.Value.AdbPort.Value;
                    }
                }

                return indexPortMap;
            }
            catch (Exception ex)
            {
                XLogger.Debug($"获取所有模拟器ADB端口时发生异常: {ex.Message}");
                return new Dictionary<string, int>();
            }
        }

        /// <summary>
        /// 获取模拟器序号（同步方法，内部调用异步实现）
        /// </summary>
        /// <returns></returns>
        public int GetEmulatorIndex()
        {
            try
            {
                return Task.Run(async () => await GetEmulatorIndexAsync()).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                XLogger.Debug($"同步调用异步方法时发生异常: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// 获取模拟器序号
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetEmulatorIndexAsync()
        {
            // ADB命令：MuMuManager.exe info -v all
            if (!CheckLegality()) return -1;
            try
            {
                var instances = await _GetInstancesAsync();

                // 检查是否有错误输出
                if (instances is null || instances.Instances == null || instances.Instances.Count == 0)
                {
                    XLogger.Debug($"Error occurred while executing command GetEmulatorIndex");
                    return -1;
                }

                // 遍历所有模拟器实例，找到与 _port 匹配的模拟器
                foreach (var kvp in instances.Instances)
                {
                    var instance = kvp.Value;
                    if (instance.AdbPort.HasValue && instance.AdbPort.Value == _port)
                    {
                        // 返回匹配的模拟器索引
                        if (int.TryParse(instance.Index, out int index))
                        {
                            return index;
                        }
                    }
                }

                // 如果没有找到匹配的模拟器，返回 -1
                return -1;
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"Exception occurred while getting emulator index: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// 获取当前打开的模拟器实例 并包括adb端口（同步方法，内部调用异步实现）
        /// </summary>
        /// <returns></returns>
        public List<MuMuInstance>? GetOnlineInstance()
        {
            try
            {
                return Task.Run(async () => await GetOnlineInstanceAsync()).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                XLogger.Debug($"同步调用异步方法时发生异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取当前打开的模拟器实例 并包括adb端口
        /// </summary>
        /// <returns></returns>
        public async Task<List<MuMuInstance>?> GetOnlineInstanceAsync()
        {
            var res = new List<MuMuInstance>();
            var instances = await _GetInstancesAsync();
            if (instances is null || instances.Instances is null) return null;
            foreach (var kvp in instances.Instances)
            {
                var instance = kvp.Value;
                if (instance.AdbPort.HasValue)
                    res.Add(instance);
            }
            return res;
        }

        /// <summary>
        /// 获取设备上安装的包
        /// </summary>
        /// <returns></returns>
        public List<string> GetPackages()
        {
            if (!CheckLegality()) return null;
            if (!_isConnected)
            {
                XLogger.Debug("未连接到ADB设备，无法获取包信息");
                return null;
            }

            try
            {
                // 构建 ADB 连接命令
                string adbCommand = "shell pm list packages";

                // 执行命令
                var result = ExecuteCommand($"\"{_adbPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug("无法启动进程获取包信息");
                    return null;
                }

                // 检查是否有错误
                if (!string.IsNullOrEmpty(result.Error))
                {
                    XLogger.Debug($"获取包信息时出错: {result.Error}");
                    return null;
                }

                // 解析输出，获取包名列表
                List<string> packages = new List<string>();
                foreach (string line in result.Output.Split('\n'))
                {
                    if (line.StartsWith("package:"))
                    {
                        string packageName = line.Substring("package:".Length).Trim();
                        packages.Add(packageName);
                    }
                }

                return packages;
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"Exception occurred while getting packages: {ex.Message}");
                return null;
            }
        }

        public bool Init(string Path)
        {
            if (string.IsNullOrEmpty(Path))
            {
                throw new ArgumentException("路径不能为空", nameof(Path));
            }

            _exePath = Path;
            _exeDir = System.IO.Path.GetDirectoryName(_exePath);

            if (string.IsNullOrEmpty(_exeDir))
            {
                throw new InvalidOperationException("无法获取mumu所在文件夹");
            }

            var res = CheckLegality();
            if (!res) throw new InvalidOperationException("路径、配置检测不通过，请重新选择！");
            return true;
        }

        /// <summary>
        /// 安装APK应用到设备
        /// </summary>
        /// <param name="apkPath">APK文件的完整路径</param>
        /// <param name="adbPort">可选的ADB端口参数，如果提供则会尝试自动连接该端口</param>
        /// <param name="options">安装选项，例如"-r"表示重新安装保留数据，"-g"表示授予所有权限</param>
        /// <returns>安装是否成功</returns>
        public bool InstallApp(string apkPath, int adbPort = 0, string options = "")
        {
            if (!CheckLegality()) return false;
            if (string.IsNullOrEmpty(apkPath))
            {
                XLogger.Debug("APK文件路径不能为空");
                return false;
            }

            if (!System.IO.File.Exists(apkPath))
            {
                XLogger.Debug($"APK文件不存在，路径: {apkPath}");
                return false;
            }

            // 如果提供了有效的ADB端口参数，尝试自动连接
            if (adbPort > 0)
            {
                XLogger.Debug($"提供了ADB端口参数 {adbPort}，尝试自动连接");

                // 检查是否需要切换到新端口
                if (_isConnected && _port != adbPort)
                {
                    XLogger.Debug($"已经连接到端口 {_port}，但需要切换到新端口 {adbPort}");
                    _isConnected = false;
                }

                // 如果未连接，尝试连接
                if (!_isConnected)
                {
                    bool connectionResult = ConnectToAdbDevice(adbPort);
                    if (!connectionResult)
                    {
                        XLogger.Debug($"无法连接到ADB设备端口 {adbPort}，安装应用失败");
                        return false;
                    }
                }
            }

            // 检查是否已连接
            if (!_isConnected)
            {
                XLogger.Debug("未连接到ADB设备，无法安装应用");
                return false;
            }

            // 安装应用前检查设备状态
            bool deviceReady = CheckDeviceStatus(_port);
            if (!deviceReady)
            {
                XLogger.Debug($"设备处于离线状态，尝试重新连接...");

                // 尝试重新连接设备
                bool reconnected = ConnectToAdbDevice(_port);
                if (!reconnected)
                {
                    XLogger.Debug($"重新连接失败，无法安装应用");
                    return false;
                }

                // 再次检查设备状态
                deviceReady = CheckDeviceStatus(_port);
                if (!deviceReady)
                {
                    XLogger.Debug($"重新连接后设备仍处于离线状态，请检查模拟器状态或重启模拟器");
                    return false;
                }
            }

            try
            {
                // 构建 ADB 安装命令
                string optionsString = string.IsNullOrEmpty(options) ? "" : options + " ";
                string adbCommand = $"-s 127.0.0.1:{_port} install {optionsString}\"{apkPath}\"";

                XLogger.Debug($"开始安装APK: {apkPath}");

                // 执行命令
                var result = ExecuteCommand($"\"{_adbPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug($"无法启动进程安装应用，APK路径: {apkPath}");
                    return false;
                }

                // 检查安装是否成功
                if (result.Output.Contains("Success") || result.Output.Contains("success"))
                {
                    XLogger.Debug($"成功安装应用: {apkPath}");
                    return true;
                }
                else
                {
                    XLogger.Debug($"安装应用失败，APK路径: {apkPath}, 错误: {result.Error}, 输出: {result.Output}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"安装应用时发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 异步安装APK应用到设备
        /// </summary>
        /// <param name="apkPath">APK文件的完整路径</param>
        /// <param name="adbPort">可选的ADB端口参数，如果提供则会尝试自动连接该端口</param>
        /// <param name="options">安装选项，例如"-r"表示重新安装保留数据，"-g"表示授予所有权限</param>
        /// <returns>安装是否成功的任务</returns>
        public async Task<bool> InstallAppAsync(string apkPath, int adbPort = 0, string options = "")
        {
            if (!CheckLegality()) return false;
            if (string.IsNullOrEmpty(apkPath))
            {
                XLogger.Debug("APK文件路径不能为空");
                return false;
            }

            if (!System.IO.File.Exists(apkPath))
            {
                XLogger.Debug($"APK文件不存在，路径: {apkPath}");
                return false;
            }

            // 如果提供了有效的ADB端口参数，尝试自动连接
            if (adbPort > 0)
            {
                XLogger.Debug($"提供了ADB端口参数 {adbPort}，尝试自动连接");

                // 检查是否需要切换到新端口
                if (_isConnected && _port != adbPort)
                {
                    XLogger.Debug($"已经连接到端口 {_port}，但需要切换到新端口 {adbPort}");
                    _isConnected = false;
                }

                // 如果未连接，尝试连接
                if (!_isConnected)
                {
                    bool connectionResult = await ConnectToAdbDeviceAsync(adbPort);
                    if (!connectionResult)
                    {
                        XLogger.Debug($"无法连接到ADB设备端口 {adbPort}，安装应用失败");
                        return false;
                    }
                }
            }

            // 检查是否已连接
            if (!_isConnected)
            {
                XLogger.Debug("未连接到ADB设备，无法安装应用");
                return false;
            }

            // 安装应用前检查设备状态
            bool deviceReady = await CheckDeviceStatusAsync(_port);
            if (!deviceReady)
            {
                XLogger.Debug($"设备处于离线状态，尝试重新连接...");

                // 尝试重新连接设备
                bool reconnected = await ConnectToAdbDeviceAsync(_port);
                if (!reconnected)
                {
                    XLogger.Debug($"重新连接失败，无法安装应用");
                    return false;
                }

                // 再次检查设备状态
                deviceReady = await CheckDeviceStatusAsync(_port);
                if (!deviceReady)
                {
                    XLogger.Debug($"重新连接后设备仍处于离线状态，请检查模拟器状态或重启模拟器");
                    return false;
                }
            }

            try
            {
                // 构建 ADB 安装命令
                string optionsString = string.IsNullOrEmpty(options) ? "" : options + " ";
                string adbCommand = $"-s 127.0.0.1:{_port} install {optionsString}\"{apkPath}\"";

                XLogger.Debug($"开始安装APK: {apkPath}");

                // 执行命令
                var result = await ExecuteCommandAsync($"\"{_adbPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug($"无法启动进程安装应用，APK路径: {apkPath}");
                    return false;
                }

                // 检查安装是否成功
                if (result.Output.Contains("Success") || result.Output.Contains("success"))
                {
                    XLogger.Debug($"成功安装应用: {apkPath}");
                    return true;
                }
                else
                {
                    XLogger.Debug($"安装应用失败，APK路径: {apkPath}, 错误: {result.Error}, 输出: {result.Output}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"安装应用时发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查指定索引的模拟器是否在运行
        /// </summary>
        /// <param name="index">模拟器索引</param>
        /// <returns>模拟器是否在运行</returns>
        public bool IsRunningByIndex(int index)
        {
            try
            {
                return Task.Run(async () => await IsRunningByIndexAsync(index)).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                XLogger.Debug($"同步调用异步方法时发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 异步检查指定索引的模拟器是否在运行
        /// </summary>
        /// <param name="index">模拟器索引</param>
        /// <returns>模拟器是否在运行</returns>
        public async Task<bool> IsRunningByIndexAsync(int index)
        {
            if (!CheckLegality()) return false;

            try
            {
                // 构建命令获取模拟器信息
                string command = $"info -v {index}";

                // 执行命令
                var infoResult = await ExecuteCommandAsync($"\"{_MuMuManagerPath}\"", command, 10000); // 使用较短的超时时间

                if (!infoResult.Success)
                {
                    XLogger.Debug($"无法获取模拟器状态，索引: {index}");
                    return false;
                }

                // 检查是否有错误
                if (!string.IsNullOrEmpty(infoResult.Error) && !infoResult.Error.Contains("CPU random generator"))
                {
                    XLogger.Debug($"检查模拟器状态时出错，索引: {index}, 错误: {infoResult.Error}");
                    return false;
                }

                // 检查输出是否包含render_wnd且值不为空
                bool hasRenderWnd = infoResult.Output.Contains("render_wnd") && !infoResult.Output.Contains("render_wnd\": \"\"");

                // 同时检查是否包含进程ID
                bool hasPid = infoResult.Output.Contains("\"pid\":") && !infoResult.Output.Contains("\"pid\": 0");

                // 模拟器在运行状态应该同时满足有渲染窗口和有进程
                bool isRunning = hasRenderWnd && hasPid;

                return isRunning;
            }
            catch (Exception ex)
            {
                XLogger.Debug($"检查模拟器[{index}]状态时出现异常: {ex.Message}");
                return false;
            }
        }

        public void Open()
        {
            if (!CheckLegality()) return;

            try
            {
                // 构建 ADB 连接命令
                string adbCommand = $"api -v 0 launch_player";
                XLogger.Debug($"开始执行命令启动模拟器: {_MuMuManagerPath} {adbCommand}");

                // 执行命令
                var result = ExecuteCommand($"\"{_MuMuManagerPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug("无法启动进程打开模拟器");
                    return;
                }

                XLogger.Debug("模拟器启动命令已执行完成");

                // 检查是否有错误
                if (!string.IsNullOrEmpty(result.Error))
                {
                    XLogger.Debug($"打开模拟器时出错: {result.Error}");
                    return;
                }

                // 等待模拟器启动
                int maxRetries = 10;
                for (int i = 0; i < maxRetries; i++)
                {
                    var instances = GetOnlineInstance();
                    if (instances != null && instances.Count > 0)
                    {
                        XLogger.Debug("模拟器已成功启动");
                        break;
                    }

                    XLogger.Debug($"等待模拟器启动，尝试 {i + 1}/{maxRetries}");
                    Thread.Sleep(2000); // 等待2秒
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"Exception occurred while opening emulator: {ex.Message}");
            }
        }

        /// <summary>
        /// 异步打开模拟器
        /// </summary>
        public async Task OpenAsync()
        {
            if (!CheckLegality()) return;

            try
            {
                // 构建 ADB 连接命令
                string adbCommand = $"api -v 0 launch_player";
                XLogger.Debug($"开始执行命令启动模拟器: {_MuMuManagerPath} {adbCommand}");

                // 执行命令
                var result = await ExecuteCommandAsync($"\"{_MuMuManagerPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug("无法启动进程打开模拟器");
                    return;
                }

                XLogger.Debug("模拟器启动命令已执行完成");

                // 检查是否有错误
                if (!string.IsNullOrEmpty(result.Error))
                {
                    XLogger.Debug($"打开模拟器时出错: {result.Error}");
                    return;
                }

                // 等待模拟器启动
                int maxRetries = 10;
                for (int i = 0; i < maxRetries; i++)
                {
                    var instances = await GetOnlineInstanceAsync();
                    if (instances != null && instances.Count > 0)
                    {
                        XLogger.Debug("模拟器已成功启动");
                        break;
                    }

                    XLogger.Debug($"等待模拟器启动，尝试 {i + 1}/{maxRetries}");
                    await Task.Delay(2000); // 等待2秒
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"Exception occurred while opening emulator: {ex.Message}");
            }
        }

        /// <summary>
        /// 优化版本：异步打开模拟器并等待其完全启动
        /// </summary>
        /// <param name="timeoutSeconds">等待超时时间（秒）</param>
        /// <returns>是否成功启动</returns>
        public async Task<bool> OpenAsyncAndWait(int timeoutSeconds = 60)
        {
            if (!CheckLegality()) return false;

            try
            {
                // 构建 ADB 连接命令
                string adbCommand = $"api -v 0 launch_player";
                XLogger.Debug($"开始执行命令启动模拟器: {_MuMuManagerPath} {adbCommand}");

                // 执行命令
                var result = await ExecuteCommandAsync($"\"{_MuMuManagerPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug("无法启动进程打开模拟器");
                    return false;
                }

                XLogger.Debug("模拟器启动命令已执行完成");

                // 检查是否有错误
                if (!string.IsNullOrEmpty(result.Error))
                {
                    XLogger.Debug($"打开模拟器时出错: {result.Error}");
                    return false;
                }

                XLogger.Debug("正在等待模拟器完全启动...");

                // 等待任意一个模拟器实例完全启动
                DateTime startTime = DateTime.Now;
                while ((DateTime.Now - startTime).TotalSeconds < timeoutSeconds)
                {
                    // 获取所有实例
                    var instances = await _GetInstancesAsync();
                    if (instances?.Instances != null && instances.Instances.Count > 0)
                    {
                        // 检查是否有任何实例完全启动
                        foreach (var kvp in instances.Instances)
                        {
                            var instance = kvp.Value;

                            // 检查main_wnd和render_wnd是否都存在并且不为空
                            bool hasMainWnd = !string.IsNullOrEmpty(instance.MainWnd);
                            bool hasRenderWnd = !string.IsNullOrEmpty(instance.RenderWnd);

                            if (hasMainWnd && hasRenderWnd)
                            {
                                XLogger.Debug($"模拟器已完全启动成功，索引: {instance.Index}，main_wnd和render_wnd均已就绪");
                                return true;
                            }
                        }

                        XLogger.Debug("等待模拟器窗口完全初始化...");
                    }
                    else
                    {
                        XLogger.Debug("等待模拟器进程启动...");
                    }

                    await Task.Delay(2000); // 每2秒检查一次
                }

                XLogger.Debug("等待模拟器完全启动超时");
                return false;
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"启动模拟器时出现异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 按照索引启动指定模拟器
        /// </summary>
        /// <param name="index">模拟器索引</param>
        public void OpenByIndex(int index)
        {
            if (!CheckLegality()) return;

            try
            {
                // 构建 ADB 连接命令
                string adbCommand = $"control -v {index} launch";

                // 执行命令
                var result = ExecuteCommand($"\"{_MuMuManagerPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug($"无法启动进程打开模拟器，索引: {index}");
                    return;
                }

                // 检查是否有错误
                if (!string.IsNullOrEmpty(result.Error))
                {
                    XLogger.Debug($"打开模拟器时出错，索引: {index}, 错误: {result.Error}");
                    return;
                }

                XLogger.Debug($"已发送启动命令给模拟器，索引: {index}");
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"启动模拟器时出现异常，索引: {index}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 按照索引启动指定模拟器并等待其完全启动（异步方法）
        /// </summary>
        /// <param name="index">模拟器索引</param>
        /// <param name="timeoutSeconds">等待超时时间（秒）</param>
        /// <returns>包含启动成功状态和窗口句柄的元组(success, mainWndHandle, renderWndHandle)</returns>
        public async Task<(bool success, int mainWndHandle, int renderWndHandle)> OpenByIndexAsync(int index, int timeoutSeconds = 60)
        {
            if (!CheckLegality()) return (false, 0, 0);

            try
            {
                // 构建启动模拟器命令
                string adbCommand = $"control -v {index} launch";

                // 执行命令
                var result = await ExecuteCommandAsync($"\"{_MuMuManagerPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug($"无法启动进程打开模拟器，索引: {index}");
                    return (false, 0, 0);
                }

                // 检查是否有错误
                if (!string.IsNullOrEmpty(result.Error))
                {
                    XLogger.Debug($"打开模拟器时出错，索引: {index}, 错误: {result.Error}");
                    return (false, 0, 0);
                }

                XLogger.Debug($"已发送启动命令给模拟器，索引: {index}，正在等待模拟器完全启动...");

                // 等待模拟器完全启动
                DateTime startTime = DateTime.Now;
                while ((DateTime.Now - startTime).TotalSeconds < timeoutSeconds)
                {
                    try
                    {
                        // 构建命令获取模拟器信息
                        string command = $"info -v {index}";

                        // 执行命令
                        var infoResult = await ExecuteCommandAsync($"\"{_MuMuManagerPath}\"", command, 10000); // 使用较短的超时时间

                        if (!infoResult.Success)
                        {
                            XLogger.Debug($"无法启动进程检查模拟器状态，索引: {index}");
                            await Task.Delay(2000);
                            continue;
                        }

                        // 检查是否有错误
                        if (!string.IsNullOrEmpty(infoResult.Error) && !infoResult.Error.Contains("CPU random generator"))
                        {
                            XLogger.Debug($"检查模拟器状态时出错，索引: {index}, 错误: {infoResult.Error}");
                            await Task.Delay(2000);
                            continue;
                        }

                        // 检查是否同时包含main_wnd和render_wnd并且它们都不为空
                        bool hasMainWnd = infoResult.Output.Contains("main_wnd") && !infoResult.Output.Contains("main_wnd\": \"\"");
                        bool hasRenderWnd = infoResult.Output.Contains("render_wnd") && !infoResult.Output.Contains("render_wnd\": \"\"");

                        if (hasMainWnd && hasRenderWnd)
                        {
                            XLogger.Debug($"模拟器已完全启动成功，索引: {index}，main_wnd和render_wnd均已就绪");

                            // 提取并解析main_wnd和render_wnd的值
                            int mainWndHandle = 0;
                            int renderWndHandle = 0;

                            try
                            {
                                // 使用正则表达式提取句柄值
                                var mainWndMatch = System.Text.RegularExpressions.Regex.Match(infoResult.Output, "\"main_wnd\"\\s*:\\s*\"([^\"]+)\"");
                                var renderWndMatch = System.Text.RegularExpressions.Regex.Match(infoResult.Output, "\"render_wnd\"\\s*:\\s*\"([^\"]+)\"");

                                if (mainWndMatch.Success)
                                {
                                    string mainWndStr = mainWndMatch.Groups[1].Value;
                                    // 尝试将16进制字符串转换为整数
                                    if (mainWndStr.StartsWith("0x"))
                                        mainWndStr = mainWndStr.Substring(2);
                                    mainWndHandle = Convert.ToInt32(mainWndStr, 16);
                                }

                                if (renderWndMatch.Success)
                                {
                                    string renderWndStr = renderWndMatch.Groups[1].Value;
                                    // 尝试将16进制字符串转换为整数
                                    if (renderWndStr.StartsWith("0x"))
                                        renderWndStr = renderWndStr.Substring(2);
                                    renderWndHandle = Convert.ToInt32(renderWndStr, 16);
                                }

                                XLogger.Debug($"成功解析窗口句柄 - 主窗口: 0x{mainWndHandle:X}, 渲染窗口: 0x{renderWndHandle:X}");
                            }
                            catch (Exception ex)
                            {
                                XLogger.Debug($"解析窗口句柄时出现异常: {ex.Message}");
                            }

                            return (true, mainWndHandle, renderWndHandle);
                        }
                        else
                        {
                            if (!hasMainWnd)
                                XLogger.Debug($"等待main_wnd就绪，索引: {index}");
                            if (!hasRenderWnd)
                                XLogger.Debug($"等待render_wnd就绪，索引: {index}");
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Debug($"检查模拟器状态时出现异常，索引: {index}, 错误: {ex.Message}");
                    }

                    await Task.Delay(2000); // 每2秒检查一次
                }

                XLogger.Debug($"等待模拟器完全启动超时，索引: {index}");
                return (false, 0, 0);
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"启动模拟器时出现异常，索引: {index}, 错误: {ex.Message}");
                return (false, 0, 0);
            }
        }

        /// <summary>
        /// 修改模拟器配置
        /// </summary>
        /// <param name="index">模拟器索引</param>
        /// <returns>是否成功修改配置</returns>
        public bool SetEmulatorConfig(int index)
        {
            if (!CheckLegality()) return false;

            try
            {
                // 步骤：修改模拟器配置
                string settingCommand = $"setting -v {index} -k resolution_width.custom -val 1280 -k resolution_height.custom -val 720 -k resolution_dpi.custom -val 240 -k renderer_mode -val dx -k renderer_strategy -val auto -k gpu_mode -val auto -k gpu_mem_size -val 512 -k cpu_mode -val host -k cpu_count -val 2 -k cpu_freq -val 2000 -k network_mode -val full -k network_delay -val 0 -k network_latency -val 0 -k network_loss -val 0 -k network_speed -val 100 -k network_profile -val gprs -k network_ssid -val perf";

                // 执行命令
                var result = ExecuteCommand($"\"{_MuMuManagerPath}\"", settingCommand);

                if (!result.Success)
                {
                    XLogger.Debug($"设置模拟器配置失败，索引: {index}");
                    return false;
                }

                // 检查是否有错误
                if (!string.IsNullOrEmpty(result.Error) && !result.Error.Contains("CPU random generator"))
                {
                    XLogger.Debug($"设置模拟器配置出错，索引: {index}, 错误: {result.Error}");
                    return false;
                }

                XLogger.Debug($"成功修改模拟器配置并重启，索引: {index}");
                return true;
            }
            catch (Exception ex)
            {
                XLogger.Debug($"修改模拟器配置时出现异常，索引: {index}, 错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 启动应用
        /// </summary>
        /// <param name="packageName">包名，支持直接传入应用名称如"阴阳师"等</param>
        /// <param name="adbPort">可选的ADB端口参数，如果提供则会尝试自动连接该端口</param>
        public void Start_App(string packageName, int adbPort = 0)
        {
            if (!CheckLegality()) return;

            // 如果提供了有效的ADB端口参数，尝试自动连接
            if (adbPort > 0)
            {
                XLogger.Debug($"提供了ADB端口参数 {adbPort}，尝试自动连接");

                // 检查是否需要切换到新端口
                if (_isConnected && _port != adbPort)
                {
                    XLogger.Debug($"已经连接到端口 {_port}，但需要切换到新端口 {adbPort}");
                    _isConnected = false;
                }

                // 如果未连接，尝试连接
                if (!_isConnected)
                {
                    bool connectionResult = ConnectToAdbDevice(adbPort);
                    if (!connectionResult)
                    {
                        XLogger.Debug($"无法连接到ADB设备端口 {adbPort}，启动应用失败");
                        return;
                    }
                }
            }

            // 检查是否已连接
            if (!_isConnected)
            {
                XLogger.Debug("未连接到ADB设备，无法启动应用");
                return;
            }

            // 启动应用前检查设备状态
            bool deviceReady = CheckDeviceStatus(_port);
            if (!deviceReady)
            {
                XLogger.Debug($"设备处于离线状态，尝试重新连接...");

                // 尝试重新连接设备
                bool reconnected = ConnectToAdbDevice(_port);
                if (!reconnected)
                {
                    XLogger.Debug($"重新连接失败，无法启动应用");
                    return;
                }

                // 再次检查设备状态
                deviceReady = CheckDeviceStatus(_port);
                if (!deviceReady)
                {
                    XLogger.Warn($"重新连接后设备仍处于离线状态，请检查模拟器状态或重启模拟器，或尝试重启电脑！");
                    return;
                }
            }

            string actualPackageName = packageName;
            lock (_lockObject)
            {
                if (_PackageInfoCache.ContainsKey(packageName))
                    actualPackageName = _PackageInfoCache[packageName];
            }

            try
            {
                // 构建 ADB 连接命令
                string adbCommand = $"-s 127.0.0.1:{_port} shell monkey -p {actualPackageName} -c android.intent.category.LAUNCHER --pct-syskeys 0 1";

                // 执行命令
                var result = ExecuteCommand($"\"{_adbPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug($"无法启动进程打开应用: {actualPackageName}");
                    return;
                }

                // 检查启动是否成功
                if (result.Output.Contains("No activities found to run") || result.Output.Contains("error") ||
                    (!string.IsNullOrEmpty(result.Error) && !result.Error.Contains("system_server")))
                {
                    XLogger.Debug($"启动应用 {actualPackageName} 失败: {result.Error}");
                }
                else
                {
                    XLogger.Debug($"成功启动应用: {actualPackageName}");
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"启动应用时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 异步启动应用
        /// </summary>
        /// <param name="packageName">包名，支持直接传入应用名称如"阴阳师"等</param>
        /// <param name="adbPort">可选的ADB端口参数，如果提供则会尝试自动连接该端口</param>
        /// <returns>启动是否成功的任务</returns>
        public async Task<bool> Start_AppAsync(string packageName, int adbPort = 0)
        {
            if (!CheckLegality()) return false;

            // 如果提供了有效的ADB端口参数，尝试自动连接
            if (adbPort > 0)
            {
                XLogger.Debug($"提供了ADB端口参数 {adbPort}，尝试自动连接");

                // 检查是否需要切换到新端口
                if (_isConnected && _port != adbPort)
                {
                    XLogger.Debug($"已经连接到端口 {_port}，但需要切换到新端口 {adbPort}");
                    _isConnected = false;
                }

                // 如果未连接，尝试连接
                if (!_isConnected)
                {
                    bool connectionResult = await ConnectToAdbDeviceAsync(adbPort);
                    if (!connectionResult)
                    {
                        XLogger.Debug($"无法连接到ADB设备端口 {adbPort}，启动应用失败");
                        return false;
                    }
                }
            }

            // 检查是否已连接
            if (!_isConnected)
            {
                XLogger.Debug("未连接到ADB设备，无法启动应用");
                return false;
            }

            // 启动应用前检查设备状态
            bool deviceReady = await CheckDeviceStatusAsync(_port);
            if (!deviceReady)
            {
                XLogger.Debug($"设备处于离线状态，尝试重新连接...");

                // 尝试重新连接设备
                bool reconnected = await ConnectToAdbDeviceAsync(_port);
                if (!reconnected)
                {
                    XLogger.Debug($"重新连接失败，无法启动应用");
                    return false;
                }

                // 再次检查设备状态
                deviceReady = await CheckDeviceStatusAsync(_port);
                if (!deviceReady)
                {
                    XLogger.Debug($"重新连接后设备仍处于离线状态，请检查模拟器状态或重启模拟器");
                    return false;
                }
            }

            string actualPackageName = packageName;
            lock (_lockObject)
            {
                if (_PackageInfoCache.ContainsKey(packageName))
                    actualPackageName = _PackageInfoCache[packageName];
            }

            try
            {
                // 获取已安装的应用包列表，检查目标应用是否已安装
                //List<string> installedPackages = GetPackages();
                //if (installedPackages == null)
                //{
                //    XLogger.Debug($"无法获取已安装的应用列表，启动应用 {actualPackageName} 失败");
                //    return false;
                //}

                //if (!installedPackages.Contains(actualPackageName))
                //{
                //    XLogger.Debug($"应用 {actualPackageName} 未安装在设备上，启动失败");
                //    return false;
                //}

                // 构建 ADB 连接命令
                string adbCommand = $"-s 127.0.0.1:{_port} shell monkey -p {actualPackageName} -c android.intent.category.LAUNCHER --pct-syskeys 0 1";

                // 执行命令
                var result = await ExecuteCommandAsync($"\"{_adbPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug($"无法启动进程打开应用: {actualPackageName}");
                    return false;
                }

                // 检查启动是否成功
                if (result.Output.Contains("No activities found to run") || result.Output.Contains("error") ||
                    (!string.IsNullOrEmpty(result.Error) && !result.Error.Contains("system_server")))
                {
                    XLogger.Debug($"启动应用 {actualPackageName} 失败: {result.Error}");
                    return false;
                }
                else
                {
                    XLogger.Debug($"成功启动应用: {actualPackageName}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"启动应用时发生异常: {ex.Message}");
                return false;
            }
        }

        public void Stop_App(string packageName)
        {
            if (!CheckLegality()) return;
            if (!_isConnected)
            {
                XLogger.Debug("未连接到ADB设备，无法停止应用");
                return;
            }

            string actualPackageName = packageName;
            lock (_lockObject)
            {
                if (_PackageInfoCache.ContainsKey(packageName))
                    actualPackageName = _PackageInfoCache[packageName];
            }

            try
            {
                // 构建 ADB 连接命令
                string adbCommand = $"shell am force-stop {actualPackageName}";

                // 执行命令
                var result = ExecuteCommand($"\"{_adbPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug($"无法启动进程停止应用: {actualPackageName}");
                    return;
                }

                // 检查是否有错误
                if (!string.IsNullOrEmpty(result.Error))
                {
                    XLogger.Debug($"停止应用 {actualPackageName} 时出错: {result.Error}");
                }
                else
                {
                    XLogger.Debug($"成功停止应用: {actualPackageName}");
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Debug($"Exception occurred while stopping app: {ex.Message}");
            }
        }

        /// <summary>
        /// 等待模拟器启动成功
        /// </summary>
        /// <param name="index">模拟器索引</param>
        /// <param name="timeoutSeconds">超时时间（秒）</param>
        /// <returns>是否成功启动</returns>
        public bool WaitForEmulatorStarted(int index, int timeoutSeconds = 60)
        {
            if (!CheckLegality()) return false;

            DateTime startTime = DateTime.Now;
            while ((DateTime.Now - startTime).TotalSeconds < timeoutSeconds)
            {
                try
                {
                    // 构建命令获取模拟器信息
                    string command = $"info -v {index}";

                    // 执行命令
                    var result = ExecuteCommand($"\"{_MuMuManagerPath}\"", command, 10000); // 使用较短的超时时间

                    if (!result.Success)
                    {
                        XLogger.Debug($"无法启动进程检查模拟器状态，索引: {index}");
                        Thread.Sleep(2000);
                        continue;
                    }

                    // 检查是否有错误
                    if (!string.IsNullOrEmpty(result.Error) && !result.Error.Contains("CPU random generator"))
                    {
                        XLogger.Debug($"检查模拟器状态时出错，索引: {index}, 错误: {result.Error}");
                        Thread.Sleep(2000);
                        continue;
                    }

                    // 检查是否有 render_wnd 并且不为空
                    // 判断模拟器是否成功启动的关键是看输出中是否包含 render_wnd 字段且值不为空
                    if (result.Output.Contains("render_wnd") && !result.Output.Contains("render_wnd\": \"\""))
                    {
                        XLogger.Debug($"模拟器已成功启动，索引: {index}");
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Debug($"检查模拟器状态时出现异常，索引: {index}, 错误: {ex.Message}");
                }

                Thread.Sleep(2000); // 每2秒检查一次
            }

            XLogger.Debug($"等待模拟器启动超时，索引: {index}");
            return false;
        }

        /// <summary>
        /// 检查ADB设备状态
        /// </summary>
        /// <param name="port">ADB端口</param>
        /// <returns>设备是否处于正常状态</returns>
        private bool CheckDeviceStatus(int port)
        {
            try
            {
                // 构建 ADB 设备状态检查命令
                string adbCommand = "devices";

                // 执行命令
                var result = ExecuteCommand($"\"{_adbPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug("无法启动进程检查ADB设备状态");
                    return false;
                }

                // 查找特定设备的状态
                string deviceIdentifier = $"127.0.0.1:{port}";
                string[] lines = result.Output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string line in lines)
                {
                    if (line.StartsWith(deviceIdentifier))
                    {
                        // 设备行格式：127.0.0.1:5555 device/offline/unauthorized
                        string[] parts = line.Split(new[] { '\t', ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length >= 2)
                        {
                            string status = parts[1].Trim();
                            XLogger.Debug($"设备 {deviceIdentifier} 状态: {status}");

                            // 只有状态为"device"时才表示设备已就绪
                            return status.Equals("device", StringComparison.OrdinalIgnoreCase);
                        }
                    }
                }

                XLogger.Debug($"未找到设备 {deviceIdentifier} 的状态信息");
                return false;
            }
            catch (Exception ex)
            {
                XLogger.Debug($"检查ADB设备状态时发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 异步检查ADB设备状态
        /// </summary>
        /// <param name="port">ADB端口</param>
        /// <returns>设备是否处于正常状态</returns>
        private async Task<bool> CheckDeviceStatusAsync(int port)
        {
            try
            {
                // 构建 ADB 设备状态检查命令
                string adbCommand = "devices";

                // 执行命令
                var result = await ExecuteCommandAsync($"\"{_adbPath}\"", adbCommand);

                if (!result.Success)
                {
                    XLogger.Debug("无法启动进程检查ADB设备状态");
                    return false;
                }

                // 查找特定设备的状态
                string deviceIdentifier = $"127.0.0.1:{port}";
                string[] lines = result.Output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string line in lines)
                {
                    if (line.StartsWith(deviceIdentifier))
                    {
                        // 设备行格式：127.0.0.1:5555 device/offline/unauthorized
                        string[] parts = line.Split(new[] { '\t', ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length >= 2)
                        {
                            string status = parts[1].Trim();
                            XLogger.Debug($"设备 {deviceIdentifier} 状态: {status}");

                            // 只有状态为"device"时才表示设备已就绪
                            return status.Equals("device", StringComparison.OrdinalIgnoreCase);
                        }
                    }
                }

                XLogger.Debug($"未找到设备 {deviceIdentifier} 的状态信息");
                return false;
            }
            catch (Exception ex)
            {
                XLogger.Debug($"检查ADB设备状态时发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行命令行并返回输出结果
        /// </summary>
        /// <param name="fileName">可执行文件路径</param>
        /// <param name="arguments">命令行参数</param>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        /// <returns>包含标准输出和错误输出的元组</returns>
        private CommandResult ExecuteCommand(string fileName, string arguments, int timeoutMs = CommandTimeoutMs)
        {
            try
            {
                // 使用Process类执行命令
                var process = new System.Diagnostics.Process
                {
                    StartInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = fileName,
                        Arguments = arguments,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        StandardOutputEncoding = Encoding.UTF8,
                        StandardErrorEncoding = Encoding.UTF8
                    }
                };

                // 检查是否为MuMuManager.exe进程
                bool isMuMuManager = fileName.Contains("MuMuManager.exe") || fileName.Contains("MuMuManager");

                process.Start();

                // 尝试设置进程优先级
                try { process.PriorityClass = System.Diagnostics.ProcessPriorityClass.AboveNormal; }
                catch { /* 忽略设置优先级可能出现的异常 */ }

                string output;
                string error;

                if (isMuMuManager)
                {
                    // 针对MuMuManager.exe使用自定义读取方法
                    output = ReadProcessOutputNonBlocking(process, timeoutMs);
                    error = ReadProcessErrorNonBlocking(process);

                    // 获取到输出后立即终止进程
                    try { if (!process.HasExited) process.Kill(); }
                    catch { /* 忽略可能的异常 */ }
                }
                else
                {
                    // 对于其他进程使用原有的方法
                    var outputTask = process.StandardOutput.ReadToEndAsync();
                    var errorTask = process.StandardError.ReadToEndAsync();

                    // 等待输出读取完成（不等待进程退出）
                    Task.WaitAll(new Task[] { outputTask, errorTask }, timeoutMs);

                    output = outputTask.IsCompleted ? outputTask.Result : string.Empty;
                    error = errorTask.IsCompleted ? errorTask.Result : string.Empty;

                    // 给进程一个短暂的额外时间退出
                    if (!process.WaitForExit(2000))
                    {
                        try { process.Kill(); }
                        catch { /* 忽略终止进程可能出现的异常 */ }
                    }
                }

                int? exitCode = process.HasExited ? process.ExitCode : null;
                return new CommandResult(true, output, error, exitCode);
            }
            catch (Exception ex)
            {
                XLogger.Debug($"执行命令出错: {ex.Message}, 命令: {fileName} {arguments}");
                return new CommandResult(false, string.Empty, ex.Message);
            }
        }

        /// <summary>
        /// 异步执行命令行并返回输出结果
        /// </summary>
        /// <param name="fileName">可执行文件路径</param>
        /// <param name="arguments">命令行参数</param>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        /// <returns>包含标准输出和错误输出的元组</returns>
        private async Task<CommandResult> ExecuteCommandAsync(string fileName, string arguments, int timeoutMs = CommandTimeoutMs)
        {
            try
            {
                // 创建一个ProcessStartInfo对象来配置命令的执行
                System.Diagnostics.ProcessStartInfo processStartInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = fileName,
                    Arguments = arguments,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    StandardOutputEncoding = Encoding.UTF8,
                    StandardErrorEncoding = Encoding.UTF8
                };

                // 检查是否为MuMuManager.exe进程
                bool isMuMuManager = fileName.Contains("MuMuManager.exe") || fileName.Contains("MuMuManager");

                // 创建进程对象
                using System.Diagnostics.Process process = new System.Diagnostics.Process();
                process.StartInfo = processStartInfo;
                process.EnableRaisingEvents = true;

                if (!process.Start())
                {
                    XLogger.Debug($"无法启动进程执行命令: {fileName} {arguments}");
                    return CommandResult.Failed("无法启动进程");
                }

                // 尝试设置进程优先级
                try { process.PriorityClass = System.Diagnostics.ProcessPriorityClass.AboveNormal; }
                catch { /* 忽略设置优先级可能出现的异常 */ }

                string output;
                string error;

                if (isMuMuManager)
                {
                    await Task.Delay(1000); // 等待MuMuManager启动完成
                    // 针对MuMuManager.exe使用异步非阻塞读取方法
                    output = await ReadProcessOutputNonBlockingAsync(process, timeoutMs);

                    // 如果已经获取到有效的JSON输出，直接终止进程并返回
                    if (!string.IsNullOrEmpty(output) && output.Contains("{") && output.Contains("}"))
                    {
                        try { if (!process.HasExited) process.Kill(); }
                        catch { /* 忽略可能的异常 */ }
                        return CommandResult.Succeeded(output);
                    }

                    // 如果没有获取到JSON输出，才读取错误信息
                    error = await ReadProcessErrorNonBlockingAsync(process);

                    // 获取到输出后立即终止进程
                    try { if (!process.HasExited) process.Kill(); }
                    catch { /* 忽略可能的异常 */ }
                }
                else
                {
                    // 对于其他进程使用原有的方法
                    var outputTask = process.StandardOutput.ReadToEndAsync();
                    var errorTask = process.StandardError.ReadToEndAsync();

                    // 等待读取任务完成，设置超时
                    await Task.WhenAll(outputTask, errorTask).WaitAsync(TimeSpan.FromMilliseconds(timeoutMs));

                    output = await outputTask;
                    error = await errorTask;

                    // 给进程一个短暂的额外时间退出
                    var exitTask = Task.Run(() => process.WaitForExit(2000));
                    if (await Task.WhenAny(exitTask, Task.Delay(2000)) != exitTask)
                    {
                        try { process.Kill(); }
                        catch { /* 忽略终止进程可能出现的异常 */ }
                    }
                }

                int? exitCode = process.HasExited ? process.ExitCode : null;
                return new CommandResult(true, output, error, exitCode);
            }
            catch (Exception ex)
            {
                XLogger.Debug($"执行命令出错: {ex.Message}, 命令: {fileName} {arguments}");
                return CommandResult.Failed(ex.Message);
            }
        }

        /// <summary>
        /// 执行命令的统一方法，支持同步和异步调用
        /// </summary>
        /// <param name="fileName">可执行文件路径</param>
        /// <param name="arguments">命令行参数</param>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        /// <param name="useAsync">是否使用异步执行</param>
        /// <returns>命令执行结果</returns>
        private CommandResult ExecuteCommandCore(string fileName, string arguments, int timeoutMs = CommandTimeoutMs, bool useAsync = false)
        {
            if (useAsync)
            {
                // 异步运行但同步等待
                return ExecuteCommandAsync(fileName, arguments, timeoutMs).GetAwaiter().GetResult();
            }
            else
            {
                // 同步运行
                return ExecuteCommand(fileName, arguments, timeoutMs);
            }
        }

        /// <summary>
        /// 带有重试机制的命令执行方法
        /// </summary>
        /// <param name="fileName">可执行文件路径</param>
        /// <param name="arguments">命令行参数</param>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="retryDelayMs">重试延迟（毫秒）</param>
        /// <returns>命令执行结果</returns>
        private CommandResult ExecuteCommandWithRetry(string fileName, string arguments, int timeoutMs = CommandTimeoutMs,
                                                     int maxRetries = 3, int retryDelayMs = 500)
        {
            CommandResult result = null;
            Exception lastException = null;

            for (int retry = 0; retry <= maxRetries; retry++)
            {
                try
                {
                    // 执行命令
                    result = ExecuteCommand(fileName, arguments, timeoutMs);

                    // 如果成功或是最后一次尝试，直接返回结果
                    if (result.Success || retry == maxRetries)
                    {
                        return result;
                    }

                    // 记录错误并准备重试
                    XLogger.Debug($"命令执行失败，准备重试 ({retry + 1}/{maxRetries}): {fileName} {arguments}，错误: {result.Error}");
                    Thread.Sleep(retryDelayMs * (retry + 1)); // 指数级增加等待时间
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    XLogger.Debug($"命令执行异常，准备重试 ({retry + 1}/{maxRetries}): {fileName} {arguments}，异常: {ex.Message}");
                    Thread.Sleep(retryDelayMs * (retry + 1));
                }
            }

            // 如果所有重试都失败，返回最后一次结果或创建一个失败结果
            return result ?? CommandResult.Failed(lastException?.Message ?? "所有重试尝试均失败");
        }

        /// <summary>
        /// 带有重试机制的异步命令执行方法
        /// </summary>
        /// <param name="fileName">可执行文件路径</param>
        /// <param name="arguments">命令行参数</param>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="retryDelayMs">重试延迟（毫秒）</param>
        /// <returns>命令执行结果任务</returns>
        private async Task<CommandResult> ExecuteCommandWithRetryAsync(string fileName, string arguments, int timeoutMs = CommandTimeoutMs,
                                                                     int maxRetries = 3, int retryDelayMs = 500)
        {
            CommandResult result = null;
            Exception lastException = null;

            for (int retry = 0; retry <= maxRetries; retry++)
            {
                try
                {
                    // 异步执行命令
                    result = await ExecuteCommandAsync(fileName, arguments, timeoutMs);

                    // 如果成功或是最后一次尝试，直接返回结果
                    if (result.Success || retry == maxRetries)
                    {
                        return result;
                    }

                    // 记录错误并准备重试
                    XLogger.Debug($"命令执行失败，准备重试 ({retry + 1}/{maxRetries}): {fileName} {arguments}，错误: {result.Error}");
                    await Task.Delay(retryDelayMs * (retry + 1)); // 指数级增加等待时间
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    XLogger.Debug($"命令执行异常，准备重试 ({retry + 1}/{maxRetries}): {fileName} {arguments}，异常: {ex.Message}");
                    await Task.Delay(retryDelayMs * (retry + 1));
                }
            }

            // 如果所有重试都失败，返回最后一次结果或创建一个失败结果
            return result ?? CommandResult.Failed(lastException?.Message ?? "所有重试尝试均失败");
        }

        /// <summary>
        /// 检查JSON字符串中的花括号是否配对平衡
        /// </summary>
        /// <param name="jsonString">要检查的JSON字符串</param>
        /// <returns>如果所有花括号都配对，则返回true</returns>
        private bool IsJsonBracesBalanced(string jsonString)
        {
            // 使用堆栈来跟踪花括号的嵌套
            Stack<char> stack = new Stack<char>();
            bool inString = false; // 标记是否在字符串内
            char previousChar = '\0';

            // 遍历每个字符
            foreach (char c in jsonString)
            {
                // 处理字符串内的内容（忽略字符串内的花括号）
                if (c == '"' && previousChar != '\\')
                {
                    inString = !inString;
                }
                // 只在非字符串内处理花括号
                else if (!inString)
                {
                    if (c == '{')
                    {
                        stack.Push(c);
                    }
                    else if (c == '}')
                    {
                        // 如果栈为空或栈顶不是左花括号，则不平衡
                        if (stack.Count == 0 || stack.Pop() != '{')
                        {
                            return false;
                        }
                    }
                }

                previousChar = c;
            }

            // 如果栈为空，则所有花括号都配对
            return stack.Count == 0 && !inString;
        }

        /// <summary>
        /// 以非阻塞方式读取进程的错误输出
        /// </summary>
        private string ReadProcessErrorNonBlocking(System.Diagnostics.Process process)
        {
            // 尝试读取错误输出，但不阻塞
            try
            {
                // 如果进程已退出，直接读取全部
                if (process.HasExited)
                {
                    return process.StandardError.ReadToEnd();
                }

                // 否则尝试非阻塞读取可用数据
                var error = new StringBuilder();
                var buffer = new byte[4096];
                var stream = process.StandardError.BaseStream;
                var encoding = process.StandardError.CurrentEncoding;

                // 最多尝试几次读取
                for (int i = 0; i < 5; i++)
                {
                    if (stream.CanRead)
                    {
                        var readTask = stream.ReadAsync(buffer, 0, buffer.Length);
                        if (readTask.Wait(50)) // 很短的超时
                        {
                            int bytesRead = readTask.Result;
                            if (bytesRead > 0)
                            {
                                error.Append(encoding.GetString(buffer, 0, bytesRead));
                            }
                        }
                    }
                    Thread.Sleep(10);
                }

                return error.ToString();
            }
            catch
            {
                return string.Empty; // 忽略错误
            }
        }

        /// <summary>
        /// 以非阻塞方式异步读取进程的错误输出
        /// </summary>
        private async Task<string> ReadProcessErrorNonBlockingAsync(System.Diagnostics.Process process)
        {
            // 尝试读取错误输出，但不阻塞
            try
            {
                // 如果进程已退出，直接读取全部
                if (process.HasExited)
                {
                    return process.StandardError.ReadToEnd();
                }

                // 否则尝试非阻塞读取可用数据
                var error = new StringBuilder();
                var buffer = new byte[4096];
                var stream = process.StandardError.BaseStream;
                var encoding = process.StandardError.CurrentEncoding;

                // 最多尝试几次读取
                for (int i = 0; i < 5; i++)
                {
                    if (stream.CanRead)
                    {
                        var readTask = stream.ReadAsync(buffer, 0, buffer.Length);

                        // 等待读取完成或超时50毫秒
                        if (await Task.WhenAny(readTask, Task.Delay(50)) == readTask)
                        {
                            int bytesRead = await readTask;
                            if (bytesRead > 0)
                            {
                                error.Append(encoding.GetString(buffer, 0, bytesRead));
                            }
                        }
                    }
                    await Task.Delay(10);
                }

                return error.ToString();
            }
            catch
            {
                return string.Empty; // 忽略错误
            }
        }

        /// <summary>
        /// 以非阻塞方式读取进程的标准输出
        /// </summary>
        private string ReadProcessOutputNonBlocking(System.Diagnostics.Process process, int timeoutMs)
        {
            var output = new StringBuilder();
            var buffer = new byte[4096];
            var stream = process.StandardOutput.BaseStream;
            var encoding = process.StandardOutput.CurrentEncoding;

            DateTime startTime = DateTime.Now;
            bool hasCompleteJson = false;

            // 循环读取直到发现有完整的JSON或超时
            while (!hasCompleteJson && (DateTime.Now - startTime).TotalMilliseconds < timeoutMs)
            {
                try
                {
                    // 检查是否有可用数据(非阻塞)
                    if (stream.CanRead)
                    {
                        // 使用尽可能短的超时来读取数据
                        var readTask = stream.ReadAsync(buffer, 0, buffer.Length);
                        if (readTask.Wait(100)) // 短暂等待读取数据
                        {
                            int bytesRead = readTask.Result;
                            if (bytesRead > 0)
                            {
                                // 将读取到的数据添加到输出
                                string chunk = encoding.GetString(buffer, 0, bytesRead);
                                output.Append(chunk);

                                // 检查是否已包含完整JSON
                                if (output.ToString().Contains("{") && output.ToString().Contains("}"))
                                {
                                    // 检查花括号配对是否完整
                                    if (IsJsonBracesBalanced(output.ToString()))
                                    {
                                        hasCompleteJson = true;
                                        // 立即终止进程，防止卡住其他操作
                                        try { if (!process.HasExited) process.Kill(); }
                                        catch (Exception ex) { XLogger.Debug($"终止进程时出错: {ex.Message}"); }
                                        // 立即返回结果，不再继续循环
                                        return output.ToString();
                                    }
                                }
                            }
                        }
                    }

                    // 短暂停顿，减少CPU使用
                    Thread.Sleep(10);

                    // 检查进程是否已退出
                    if (process.HasExited)
                    {
                        // 进程已退出，读取剩余输出
                        string remainingOutput = process.StandardOutput.ReadToEnd();
                        output.Append(remainingOutput);
                        break;
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Debug($"读取进程输出时出错: {ex.Message}");
                    break;
                }
            }

            return output.ToString();
        }

        /// <summary>
        /// 以非阻塞方式异步读取进程的标准输出
        /// </summary>
        private async Task<string> ReadProcessOutputNonBlockingAsync(System.Diagnostics.Process process, int timeoutMs)
        {
            var output = new StringBuilder();
            var buffer = new byte[4096];
            var stream = process.StandardOutput.BaseStream;
            var encoding = process.StandardOutput.CurrentEncoding;

            DateTime startTime = DateTime.Now;
            bool hasCompleteJson = false;

            // 循环读取直到发现有完整的JSON或超时
            while (!hasCompleteJson && (DateTime.Now - startTime).TotalMilliseconds < timeoutMs)
            {
                try
                {
                    // 检查是否有可用数据
                    if (stream.CanRead)
                    {
                        // 使用短超时读取数据
                        var readTask = stream.ReadAsync(buffer, 0, buffer.Length);

                        // 等待读取完成或超时100毫秒
                        if (await Task.WhenAny(readTask, Task.Delay(100)) == readTask)
                        {
                            int bytesRead = await readTask;
                            if (bytesRead > 0)
                            {
                                // 将读取到的数据添加到输出
                                string chunk = encoding.GetString(buffer, 0, bytesRead);
                                output.Append(chunk);

                                // 检查是否已包含完整JSON
                                if (output.ToString().Contains("{") && output.ToString().Contains("}"))
                                {
                                    // 检查花括号配对是否完整
                                    if (IsJsonBracesBalanced(output.ToString()))
                                    {
                                        hasCompleteJson = true;
                                        // 立即终止进程，防止卡住其他操作
                                        try { if (!process.HasExited) process.Kill(); }
                                        catch (Exception ex) { XLogger.Debug($"终止进程时出错: {ex.Message}"); }
                                        // 立即返回结果，不再继续循环
                                        return output.ToString();
                                    }
                                }
                            }
                        }
                    }

                    // 短暂停顿，减少CPU使用
                    await Task.Delay(10);

                    // 检查进程是否已退出
                    if (process.HasExited)
                    {
                        // 进程已退出，读取剩余输出
                        string remainingOutput = process.StandardOutput.ReadToEnd();
                        output.Append(remainingOutput);
                        break;
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Debug($"异步读取进程输出时出错: {ex.Message}");
                    break;
                }
            }

            return output.ToString();
        }
    }
}