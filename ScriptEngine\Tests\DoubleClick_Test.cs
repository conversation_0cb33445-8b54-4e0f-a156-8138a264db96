﻿using ScriptEngine.Tasks;
using ScriptEngine.Tasks.FastTasks;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Tests
{
    internal class DoubleClick_Test
    {
        public DoubleClick_Test(Log log, ChouCeZhi _base)
        {
            Log = log;
            Base = _base;
            DB = _base.Db;
            Dm = _base.Dm;
            Ct = _base.Ct;
        }

        public Log Log { get; }
        public ChouCeZhi Base { get; }
        public DDBuilder DB { get; }
        public dmsoft Dm { get; }
        public CancellationTokenSource Ct { get; }

        internal void Start()
        {
            Log.Info("开始测试多开连续点击..");
            while (!Ct.IsCancellationRequested)
            {
                Base.Fast.Click(323, 194);
                Dm.delay(100);
            }
            throw new TaskCanceledException();
        }
    }
}