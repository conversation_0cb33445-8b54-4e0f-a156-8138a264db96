﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;

namespace ScriptEngine.Tasks
{
    internal class YuLing : BaseTask
    {
        /// <summary>
        /// 关卡层数
        /// </summary>
        private string _Class = "智能选择";

        /// <summary>
        /// 是否开启标记功能
        /// </summary>
        private bool Biaoji = false;

        private bool BiaoJi_Status = false;

        private List<string> DontSendLog = ["标记"];

        /// <summary>
        /// 任务次数
        /// </summary>
        private int Ncount = 0;

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "御灵");
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Ncount = GetConfig.Count;
            GetConfig.Others.TryGetValue("Class", out string? s);
            try { _Class = s is null ? "智能选择" : s; } catch (Exception) { _Class = "智能选择"; }
            GetConfig.Others.TryGetValue("Biaoji", out s);
            try { Biaoji = s is null ? false : bool.Parse(s); } catch (Exception) { Biaoji = false; }

            Dictionary<int, string> keys = new(){
                {2,"暗神龙" },
                {3,"暗白藏主" },
                {4,"暗黑豹" },
                {5,"暗孔雀" },
                {6,"暗神龙" },
                {0,"暗神龙" },
            };

            if (_Class == "智能选择")
            {
                int week = (int)DateTime.Now.DayOfWeek;
                if (week == 1)
                {
                    log.Warn("但是今天是星期一，不能打任何一个御灵副本！自动为您提前结束此任务！");
                    return;
                }
                log.Warn($"当前战斗对象为智能选择！今天是周{week}，为您选择\"{keys[week]}\"副本战斗！");
                _Class = keys[week];
            }

            //场景判断
            string nows = Scene.NowScene;
            if (nows != "御灵")
            {
                //先去探索，获取突破卷数量
                if (!Scene.TO.TanSuo())
                {
                    log.Warn("御灵任务无法继续，当前游戏所在场景未知，请调整到庭院或探索主界面开始脚本！");
                    return;
                }
                var tupo_Count = Fast.Scence.TanSuo_GetTuPoCount();
                log.Debug("本地Ocr识别突破卷结果：" + tupo_Count);
                if (tupo_Count == 30)
                    Tmp.Do_Tupo(); //执行临时执行突破

                //再去御灵
                Scene.TO.YuLing(_Class);
            }
            Fast.Click(233, 499, 390, 542);
            main();
            UserNotificationMessage = $"共战斗{count}/{Ncount}次.";
        }

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            //点击开始
            Fast.Click(1115, 600, 1199, 676);
            log.Info("战斗点击开始");
            var pics = Mp.Filter("御灵");
            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                FindOkFun(p.Name, p);
                if (!DontSendLog.Any(p.Name.Contains)) log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("胜利") || p.Name.Contains("通关时间") || p.Name.Contains("达摩"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败"))
                {
                    ret_bol = false;
                    isbreak = true;
                }
            }
            if (ret_bol)
                Combat_End();

            return ret_bol;
        }

        /// <summary>
        /// 御魂胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("御灵");
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                if (p.Name.Contains("挑战"))
                {
                    isbreak = true;
                    continue;
                }
                log.Info($"执行点击：{p._Name}");
                p.Click();
            }
        }

        /// <summary>
        /// 结束任务方法
        /// </summary>
        private void EndCallBack()
        {
            log.Info("执行御灵任务收尾方法,等待返回后,退出到探索。");
            while (Scene.NowScene != "御灵")
            {
                Sleep(2000);
                Fast.Click(468, 65, 636, 96);
            }
            ;
            log.Info("退出到探索..");
            Sleep(500);
            Fast.Click("28,22,73,61");
            Sleep(1200);
        }

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            if (Biaoji && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.2秒，标记位置：5号位");
                Sleep(200);
                Fast.Click(955, 428, 1018, 551);
                return false;
            }
            return true;
        }

        private int count = 0;

        /// <summary>
        /// 主流程
        /// </summary>
        private void main()
        {
            if (UserConfig_Preset != null)
            {
                Sleep(1000);
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入式神录，开始应用预设{UserConfig_Preset}");
                Fast.Click("958,649,996,684");
                Sleep(1500);
                Tmp.Do_Preset(preset);
            }
        Re:
            while (count < Ncount)
            {
                if (!WaitYuling()) goto Re;

                Anti.RandomDelay(); //防封等待
                if (Anti.ShouldTriggerRandomYysAuto())//判断是否需要穿插纸人
                {
                    Fast.Click(825, 562, 859, 595); //打开小纸人
                    Sleep(500);
                    int do_Count = Random.Shared.Next(1, 5); // 1-4次随机次数
                    if (Tmp.Do_YysAuto(do_Count))
                    {
                        count += do_Count;
                        log.Info($"触发随机穿插纸人战斗结束..脚本继续接管..");
                        Anti.ResetRandomYysAuto();
                        goto Re;
                    }
                    else Sleep(1000);
                }
                if (Db.PendingTimerTask) //执行定时任务
                {
                    Db.PendingTimerTask = false;
                    log.Info("暂停当前任务，执行定时任务，退出到探索..");
                    EndCallBack();
                    Db?.TimerTask?.DoAllTask();
                    Sleep(1000);
                    throw new Exception("定时任务执行结束，重新执行当前的主任务..");
                }
                Tmp.Do_ClearYuHun(); //执行临时执行御魂

                if (Combat())
                {
                    count++;
                    log.Info($"御灵战斗胜利，战斗次数：{count}/{Ncount}");
                }
                else
                {
                    log.Warn($"御灵战斗失败，请检查您的队伍配置是否正常！战斗次数：{count}/{Ncount}");
                    Defeated();
                }
            }
            EndCallBack();
        }

        /// <summary>
        /// 等待御灵开始界面
        /// </summary>
        /// <returns></returns>
        private bool WaitYuling()
        {
            var pics = Mp.Filter("挑战");
            if (pics.Wait()) return true;
            return false;
        }
    }
}