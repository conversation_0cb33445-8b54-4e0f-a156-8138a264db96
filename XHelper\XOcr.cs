﻿using OcrLiteLib;
using OpenCvSharp;
using Tesseract;
using XHelper.Models;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using XHelper.OCR;
using System.Threading;
using System.Threading.Tasks;

namespace XHelper
{
    /// <summary>
    /// OCR文字识别工具类
    /// </summary>
    public static class XOcr
    {
        #region 私有成员

        /// <summary>
        /// 初始化标志
        /// </summary>
        private static bool _Init = false;

        /// <summary>
        /// 获取OCR管理器
        /// </summary>
        private static OCR.OcrManager OcrManager => OCR.OcrManager.Instance;

        /// <summary>
        /// 线程信号量，限制最多4个线程同时调用OCR识别方法
        /// </summary>
        private static readonly SemaphoreSlim _ocrSemaphore = new SemaphoreSlim(4, 4);

        #endregion 私有成员

        #region 公共方法

        /// <summary>
        /// 是否使用Pro版本 OCR识别
        /// </summary>
        /// <returns></returns>
        private static bool is_Use_Pro()
        {
            try
            {
                // 使用新的配置系统
                string Exe = OcrManager.Configuration.PaddleExePath;

                // 添加额外的诊断信息
                //XLogger.Debug($"检查Pro版本OCR路径: {Exe}");

                return OcrManager.Configuration.CheckModelFilesExist("paddle");
            }
            catch (Exception ex)
            {
                XLogger.Error($"检查Pro版本OCR时发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 使用Pro版本OCR识别图片
        /// </summary>
        /// <param name="imgPath">图片路径</param>
        /// <param name="callback">回调函数</param>
        /// <returns>识别结果</returns>
        public static string Local_Ocr_String_Pro(string imgPath, Action<List<XOcr_TextBlock>>? callback = null)
        {
            try
            {
                // 使用PaddleOCR引擎
                IOcrEngine engine = OcrManager.GetEngine(OcrEngineType.PaddleOcr);
                return engine.RecognizeText(imgPath, callback);
            }
            catch (Exception ex)
            {
                XLogger.Error($"Local_Ocr_String_Pro异常: {ex.Message}");
                return "-1";
            }
        }

        /// <summary>
        /// 初始化OCR引擎
        /// </summary>
        /// <returns>是否初始化成功</returns>
        private static bool Init()
        {
            if (_Init)
                return true;

            try
            {
                // 使用OcrLite引擎
                IOcrEngine engine = OcrManager.GetEngine(OcrEngineType.OcrLite);
                _Init = engine.Initialize();
                return _Init;
            }
            catch (Exception ex)
            {
                XLogger.Error($"OCR初始化异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 识别文本
        /// </summary>
        /// <param name="bytes">图片字节数组</param>
        /// <param name="callback">回调函数</param>
        /// <returns>识别结果</returns>
        public static string Local_Ocr_String(byte[] bytes, Action<List<XOcr_TextBlock>>? callback = null)
        {
            try
            {
                // 获取信号量，限制并发
                _ocrSemaphore.Wait();
                try
                {
                    // 使用OcrLite引擎
                    IOcrEngine engine = OcrManager.GetEngine(OcrEngineType.OcrLite);
                    return engine.RecognizeText(bytes, callback);
                }
                finally
                {
                    // 释放信号量
                    _ocrSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"文本识别异常: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 识别文本（异步版本）
        /// </summary>
        /// <param name="bytes">图片字节数组</param>
        /// <param name="callback">回调函数</param>
        /// <returns>识别结果</returns>
        public static async Task<string> Local_Ocr_StringAsync(byte[] bytes, Action<List<XOcr_TextBlock>>? callback = null)
        {
            try
            {
                // 获取信号量，限制并发
                await _ocrSemaphore.WaitAsync();
                try
                {
                    // 使用OcrLite引擎
                    IOcrEngine engine = OcrManager.GetEngine(OcrEngineType.OcrLite);
                    return engine.RecognizeText(bytes, callback);
                }
                finally
                {
                    // 释放信号量
                    _ocrSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"文本识别异常: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 识别路径图片中的数字
        /// </summary>
        /// <param name="path">图片路径</param>
        /// <returns>识别结果</returns>
        public static string Local_Ocr_Number(string path)
        {
            try
            {
                // 使用Tesseract引擎
                IOcrEngine engine = OcrManager.GetEngine(OcrEngineType.Tesseract);
                return engine.RecognizeText(path);
            }
            catch (Exception ex)
            {
                XLogger.Error($"数字识别异常: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 识别图片字节数组中的数字
        /// </summary>
        /// <param name="bytes">图片字节数组</param>
        /// <returns>识别结果</returns>
        public static string Local_Ocr_Number(byte[] bytes)
        {
            try
            {
                // 使用Tesseract引擎
                IOcrEngine engine = OcrManager.GetEngine(OcrEngineType.Tesseract);
                return engine.RecognizeText(bytes);
            }
            catch (Exception ex)
            {
                XLogger.Error($"数字识别异常: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 设置是否保存优化图片备份
        /// </summary>
        /// <param name="isSave">是否保存</param>
        public static void SetSaveTmp(bool isSave)
        {
            OcrManager.SetSaveTempImages(isSave);
        }

        #endregion 公共方法

        #region 原有的辅助方法，保留用于兼容性，实际不再使用

        // 为了兼容性考虑，这些字段保留但不再实际使用
        private static bool isSaveTmp = false;

        private static OcrLite ocrEngin;
        private static int padding = 50;
        private static int imgResize = 1024;
        private static float boxScoreThresh = 0.618F;
        private static float boxThresh = 0.300F;
        private static float unClipRatio = 2.0F;
        private static bool doAngle = true;
        private static bool mostAngle = true;

        // 这些方法只是包装，实际逻辑已经移到新架构中
        private static string Ocrx64_Cmd(string cmd)
        {
            if (!is_Use_Pro()) return "-1";

            try
            {
                // 使用PaddleOCR引擎
                IOcrEngine engine = OcrManager.GetEngine(OcrEngineType.PaddleOcr);
                return engine.RecognizeText(cmd);
            }
            catch (Exception ex)
            {
                XLogger.Error($"执行OCR命令时异常: {ex.Message}, 命令: {cmd}");
                return "-1";
            }
        }

        // 这些方法为了保持代码完整性而保留，但实际已不再使用
        private static byte[] OptimizeImage(string path)
        {
            return OcrManager.ImageProcessor.OptimizeImage(path);
        }

        private static byte[] OptimizeImage(byte[] bytes)
        {
            return OcrManager.ImageProcessor.OptimizeImage(bytes);
        }

        private static byte[] ProcessImage(Mat image)
        {
            // 此方法仅用于参考，实际逻辑已移至OpenCvImageProcessor
            throw new NotImplementedException("此方法已废弃，请使用XHelper.OCR.OpenCvImageProcessor中的方法");
        }

        private static bool IsWhiteBackground(Mat image)
        {
            // 此方法仅用于参考，实际逻辑已移至OpenCvImageProcessor
            throw new NotImplementedException("此方法已废弃，请使用XHelper.OCR.OpenCvImageProcessor中的方法");
        }

        #endregion 原有的辅助方法，保留用于兼容性，实际不再使用
    }
}