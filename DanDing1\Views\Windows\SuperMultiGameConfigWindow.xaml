﻿<Window x:Class="DanDing1.Views.Windows.SuperMultiGameConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        xmlns:super="clr-namespace:DanDing1.Models.Super"
        xmlns:viewModels="clr-namespace:DanDing1.ViewModels.Windows"
        x:Name="SuperMultiGamesWindowInstance"
        Title="{Binding ApplicationTitle}"
        Width="275"
        Height="320"
        d:DataContext="{d:DesignInstance local:SuperMultiGameConfigWindow,IsDesignTimeCreatable=True}"
        ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
        ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        ScrollViewer.CanContentScroll="False"
        WindowStartupLocation="CenterScreen"
        mc:Ignorable="d">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <ui:TextBlock Grid.Row="0"
                      Text="游戏独立配置"
                      FontSize="20"
                      FontWeight="Bold"
                      Margin="0,0,0,10" />

        <ScrollViewer Grid.Row="1"
                      VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <Border
                    Padding="0 0 0 10"
                    Width="220"
                    Margin="5,0,0,0"
                    Background="{ui:ThemeResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{ui:ThemeResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="4">
                    <StackPanel Margin="10,0,0,0">
                        <ui:TextBlock Margin="0,2,0,0"
                                      Text="任务设置："
                                      FontSize="16" />
                        <StackPanel Margin="0,5,0,0"
                                    Orientation="Horizontal">
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="循环次数：" />
                            <TextBox FontSize="12"
                                     Margin="-5 0 0 0"
                                     Text="{Binding LoopCount, Mode=TwoWay}" />

                            <CheckBox
                                Content="涡轮增压"
                                Margin="-3 0 0 0"
                                Checked="OverNotice_Unchecked"
                                Unchecked="OverNotice_Unchecked"
                                ToolTip="这是一个支持变速的设置，会让脚本等待时间变短，响应时间变快！并不支持自动变速，请自己变速，本脚本不承担风险！"
                                IsChecked="{Binding SpeedSwitch, Mode=TwoWay}" />
                        </StackPanel>
                        <StackPanel Margin="-10,5,0,0"
                                    Orientation="Horizontal">
                            <CheckBox
                                x:Name="OverNotice"
                                Content="结束通知："
                                Checked="OverNotice_Unchecked"
                                Unchecked="OverNotice_Unchecked"
                                IsChecked="{Binding Notice_IsChecked, Mode=TwoWay}" />
                            <ComboBox
                                Margin="-15,0,0,0"
                                IsEnabled="{Binding ElementName=OverNotice, Path=IsChecked}"
                                ItemsSource="{Binding Notice_Lists}"
                                SelectedItem="{Binding Notice_SelectItem}" />
                        </StackPanel>
                        <StackPanel Margin="-10,0,0,0"
                                    Orientation="Horizontal">
                            <CheckBox IsChecked="{Binding IsRecord, Mode=TwoWay}"
                                      Content="录制" />
                            <CheckBox Margin="-50,0,0,0" IsChecked="{Binding EndCloseGame, Mode=TwoWay}"
                                      Content="结束后关模拟器" />
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <ui:Button
                                Width="108"
                                Margin="0,5,3,0"
                                Command="{Binding OpenPresetCommand}"
                                Content="预设管理" />
                            <CheckBox
                                Margin="-8 0 0 0"
                                IsChecked="{Binding IsTifu, Mode=TwoWay}"
                                Content="体服适配" />
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    Margin="0,10,0,0">
            <ui:Button Content="确定"
                       Margin="0,0,10,0"
                       Command="{Binding SaveCommand}" />
            <ui:Button Content="取消"
                       Command="{Binding CancelCommand}" />
        </StackPanel>
    </Grid>
</Window>