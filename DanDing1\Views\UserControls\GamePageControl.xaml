﻿<UserControl
    x:Class="DanDing1.Views.UserControls.GamePageControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:localcs="clr-namespace:DanDing1.ViewModels.Pages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:localControl="clr-namespace:DanDing1.Views.UserControls"
    xmlns:mystr="clr-namespace:DanDing1.Resources"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:converters="clr-namespace:DanDing1.Helpers"
    d:DesignHeight="450"
    d:DesignWidth="800"
    ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
    ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    ScrollViewer.CanContentScroll="False"
    mc:Ignorable="d">
    <UserControl.Resources>
        <localcs:ViewVisibilityConverter x:Key="ViewVisibilityConverter" />
        <localcs:MultiStringConcatConverter x:Key="MultiStringConcatConverter" />
        <converters:InfoBarSeverityConverter x:Key="InfoBarSeverityConverter" />
    </UserControl.Resources>
    <Grid>
        <TextBlock x:Name="GameModelName"
                   Text=""
                   Visibility="Hidden" />
        <StackPanel Margin="0 -20 0 0">

            <!-- 任务控制区 -->
            <localControl:AddTaskControl Height="261"
                                         x:Name="AddTask" />

            <StackPanel  Orientation="Horizontal">
                <Border
                    Width="300"
                    Background="{ui:ThemeResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{ui:ThemeResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    Padding="0 0 0 10"
                    CornerRadius="4">
                    <StackPanel>
                        <StackPanel Margin="10,2,0,0" Orientation="Horizontal">
                            <ui:TextBlock Text="执行任务：" />
                            <ui:TextBlock Margin="0 1 1 0" FontWeight="Black" Text="《" />
                            <ui:TextBlock Margin="0 1 0 0" FontWeight="Black" Text="{Binding ViewModel.RunningTaskName}" />
                            <ui:TextBlock Margin="1 1 0 0" FontWeight="Black" Text="》" />
                        </StackPanel>

                        <ui:ListView
                            x:Name="GameTaskLists"
                            Height="185"
                            ItemsSource="{Binding ViewModel.GameTaskLists}"
                            SelectedIndex="{Binding ViewModel.GameTaskListsIndex}">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel VerticalAlignment="Center"
                                                Orientation="Horizontal">
                                        <ui:TextBlock
                                            Margin="10,0,0,0"
                                            FontSize="15"
                                            FontWeight="Bold"
                                            Text="{Binding ShowName}" />
                                        <ui:TextBlock
                                            Margin="7,0,0,0"
                                            FontSize="15"
                                            Text="{Binding Count}" />
                                    </StackPanel>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ui:ListView>
                        <StackPanel
                            Margin="0,5,0,0"
                            HorizontalAlignment="Center"
                            Orientation="Horizontal">
                            <ui:Button
                                Command="{Binding ViewModel.MoveUpCommand}"
                                CommandParameter="{Binding ElementName=GameTaskLists, Path=SelectedIndex}"
                                Content="↑"
                                ToolTip="上移选中" />
                            <ui:Button
                                Margin="5,0,0,0"
                                Command="{Binding ViewModel.MoveDownCommand}"
                                CommandParameter="{Binding ElementName=GameTaskLists, Path=SelectedIndex}"
                                Content="↓"
                                ToolTip="下移选中" />
                            <ui:Button
                                Margin="5,0,0,0"
                                Command="{Binding ViewModel.DelTaskCommand}"
                                CommandParameter="{Binding ElementName=GameTaskLists, Path=SelectedIndex}"
                                Content="×"
                                ToolTip="删除选中" />
                            <ui:Button
                                Margin="5,0,0,0"
                                Command="{Binding ViewModel.DelAllTaskCommand}"
                                Content="清空"
                                ToolTip="清空任务列表" />
                            <ui:Button
                                x:Name="TasksManager"
                                Margin="5,0,0,0"
                                Command="{Binding ViewModel.OpenTaskPageCommand}"
                                Content="详情"
                                MouseRightButtonUp="TasksManager_MouseRightButtonUp"
                                ToolTip="打开详细任务列表，现在右键此按钮，支持快速导入任务列表哦！" />
                        </StackPanel>
                    </StackPanel>
                </Border>
                <Border
                    Padding="0 0 0 10"
                    Width="220"
                    Margin="5,0,0,0"
                    Background="{ui:ThemeResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{ui:ThemeResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="4">
                    <StackPanel Margin="10,0,0,0">
                        <ui:TextBlock Margin="0,2,0,0"
                                      Text="任务设置："
                                      FontSize="16" />
                        <StackPanel Margin="0,5,0,0"
                                    Orientation="Horizontal">
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="循环次数：" />
                            <TextBox FontSize="12"
                                     Margin="-5 0 0 0"
                                     Text="{Binding ViewModel.LoopCount, Mode=TwoWay}" />

                            <CheckBox
                                Checked="OverNotice_Unchecked"
                                Content="涡轮增压"
                                Margin="-3 0 0 0"
                                ToolTip="这是一个支持变速的设置，会让脚本等待时间变短，响应时间变快！并不支持自动变速，请自己变速，本脚本不承担风险！"
                                IsChecked="{Binding ViewModel.SpeedSwitch, Mode=OneWayToSource}"
                                Unchecked="OverNotice_Unchecked" />
                        </StackPanel>
                        <StackPanel Margin="-10,5,0,0"
                                    Orientation="Horizontal">
                            <CheckBox
                                x:Name="OverNotice"
                                Checked="OverNotice_Unchecked"
                                Content="结束通知："
                                IsChecked="{Binding ViewModel.Notice_IsChecked, Mode=OneWayToSource}"
                                Unchecked="OverNotice_Unchecked" />
                            <ComboBox
                                x:Name="OverNotice_Type"
                                Margin="-15,0,0,0"
                                IsEnabled="{Binding ElementName=OverNotice, Path=IsChecked}"
                                ItemsSource="{Binding ViewModel.Notice_Lists}"
                                SelectedItem="{Binding ViewModel.Notice_SelectItem}" />
                        </StackPanel>
                        <StackPanel Margin="-10,0,0,0"
                                    Orientation="Horizontal">
                            <CheckBox IsChecked="{Binding ViewModel.IsRecord, Mode=TwoWay}"
                                      Content="录制" />
                            <CheckBox Margin="-50,0,0,0" IsChecked="{Binding ViewModel.EndCloseGame, Mode=TwoWay}"
                                      Content="结束后关模拟器" />
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <ui:Button
                                Width="108"
                                Margin="0,5,3,0"
                                Command="{Binding ViewModel.SaveTaskListCommand}"
                                Content="保存当前列表" />
                            <ui:Button
                                Margin="0,5,0,0"
                                Command="{Binding ViewModel.OpenTaskListCommand}"
                                MouseRightButtonUp="TasksManager_MouseRightButtonUp"
                                ToolTip="右键此按钮，支持快速导入任务列表哦！"
                                Content="方案管理" />
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <ui:Button
                                Width="108"
                                Margin="0,5,3,0"
                                Command="{Binding ViewModel.OpenPresetCommand}"
                                Content="预设管理" />
                            <CheckBox
                                Margin="-8 0 0 0"
                                IsChecked="{Binding ViewModel.IsTifu, Mode=TwoWay}"
                                Content="体服适配" />
                        </StackPanel>
                    </StackPanel>
                </Border>
                <StackPanel Margin="5,0,0,0">
                    <Border
                        Width="162"
                        Height="123"
                        Background="{ui:ThemeResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{ui:ThemeResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="4">
                        <Grid>
                            <StackPanel Margin="0,5,0,0">
                                <StackPanel Margin="5"
                                            Orientation="Horizontal">
                                    <TextBlock Text="已选句柄：" />
                                    <TextBlock Text="{Binding ViewModel.SelectHwnd}" />
                                </StackPanel>
                                <StackPanel Margin="5"
                                            Orientation="Horizontal">
                                    <TextBlock Text="分辨率：" />
                                    <TextBlock Text="{Binding ViewModel.SelectDpi}" />
                                </StackPanel>
                            </StackPanel>
                            <StackPanel HorizontalAlignment="Right"
                                        VerticalAlignment="Bottom"
                                        Orientation="Horizontal">
                                <ui:Button
                                    x:Name="AutoBindButton"
                                    Click="AutoBindButton_Click"
                                    Content="选择"
                                    IsEnabled="{Binding ViewModel.StartButtonEnabled}" />
                                <ui:Button
                                    Margin="5"
                                    Command="{Binding ViewModel.BindCommand}"
                                    Content="手动获取"
                                    IsEnabled="{Binding ViewModel.StartButtonEnabled}" />
                            </StackPanel>
                        </Grid>
                    </Border>
                    <Border
                        Width="162"
                        Height="128"
                        Margin="0,5,0,0"
                        Background="{ui:ThemeResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{ui:ThemeResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="4">
                        <StackPanel VerticalAlignment="Center">
                            <StackPanel HorizontalAlignment="Center"
                                        Orientation="Horizontal">
                                <ui:Button
                                    Command="{Binding ViewModel.StartCommand}"
                                    Content="开始"
                                    IsEnabled="{Binding ViewModel.StartButtonEnabled}" />
                                <ui:Button
                                    Margin="5,0,0,0"
                                    Command="{Binding ViewModel.StopCommand}"
                                    Content="停止" />
                            </StackPanel>
                            <StackPanel Margin="0,10,0,0"
                                        HorizontalAlignment="Center">
                                <ui:TextBlock
                                    HorizontalAlignment="Center"
                                    FontSize="18"
                                    Text="{Binding ViewModel.ShowTime}" />
                                <ui:TextBlock
                                    HorizontalAlignment="Center"
                                    FontSize="14"
                                    Text="{Binding ViewModel.LastShowTime}" />
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </StackPanel>
        </StackPanel>
        <Border
            Margin="0 -15 0 0"
            Grid.RowSpan="2"
            CornerRadius="10"
            Background="{DynamicResource ApplicationBackgroundBrush}"
            Visibility="{Binding ViewModel.LoginStatus, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=未登录状态}" />
        <StackPanel
            Margin="0,0,0,20"
            HorizontalAlignment="Center"
            VerticalAlignment="Bottom">
            <ui:InfoBar
                x:Name="InfoBar"
                Title="{Binding ViewModel.InfoBar.Title, Mode=TwoWay}"
                IsOpen="{Binding ViewModel.InfoBar.IsOpen, Mode=TwoWay}"
                Message="{Binding ViewModel.InfoBar.Message, Mode=TwoWay}"
                Severity="{Binding ViewModel.InfoBar.Severity, Mode=TwoWay, Converter={StaticResource InfoBarSeverityConverter}, FallbackValue=Informational}" />
        </StackPanel>
    </Grid>
</UserControl>