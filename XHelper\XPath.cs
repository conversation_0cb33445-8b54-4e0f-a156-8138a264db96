﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XHelper
{
    /// <summary>
    /// 路径操作类
    /// </summary>
    public static class XPath
    {
        /// <summary>
        /// 获取系统临时路径
        /// </summary>
        /// <returns></returns>
        public static string GetTempPath()
        {
            var tmp = Path.GetTempPath();
            //删除最后一个'\\'
            return tmp[..^1];
        }
        /// <summary>
        /// 获取桌面路径
        /// </summary>
        /// <returns></returns>
        public static string GetDesktopPath() => Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
        /// <summary>
        /// 获取当前目录
        /// </summary>
        /// <returns></returns>
        public static string GetCurrentPath() => Environment.CurrentDirectory;

        /// <summary>
        /// 检查目录是否存在，不存在则创建
        /// </summary>
        public static void CheckPathIsExist(string directoryPath)
        {
            try
            {
                string? path;
                
                // 判断传入的是文件路径还是目录路径
                if (Path.HasExtension(directoryPath))
                {
                    // 如果是文件路径，提取目录部分
                    path = Path.GetDirectoryName(directoryPath);
                }
                else
                {
                    // 如果是目录路径，直接使用
                    path = directoryPath;
                }
                
                // 确保目录存在
                if (!string.IsNullOrEmpty(path) && !Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                    XLogger.Info($"创建目录: {path}");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"创建目录失败: {directoryPath}, 错误: {ex.Message}");
                // 不抛出异常，让调用方决定如何处理
            }
        }

        /// <summary>
        /// 获取路径的目录
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public static string? GetDirectory(string path) => Path.GetDirectoryName(path);
    }
}
