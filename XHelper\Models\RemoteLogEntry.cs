using System;

namespace XHelper.Models
{
    public class RemoteLogEntry
    {
        public DateTime Timestamp { get; set; } // UTC时间戳
        public string Level { get; set; }       // 标准日志级别: "INFO", "ERROR", "DEBUG", "WARN"
        public string Message { get; set; }     // 实际日志内容
        public string? TaskId { get; set; }     // 可选: 如果日志与特定任务相关

        public RemoteLogEntry(DateTime timestamp, string level, string message, string? taskId = null)
        {
            Timestamp = timestamp;
            Level = level;
            Message = message;
            TaskId = taskId;
        }
    }
} 