﻿using System.Windows;

namespace DanDing1.Views.Windows
{
    public partial class InputWindow : Window
    {
        public string InputText { get; private set; } = string.Empty; // 存储用户输入的值
        public bool IsOK { get; private set; } = false; // 标识是否点击了"确认"

        public InputWindow(string currentKey, string windowname = "请输入文本")
        {
            InitializeComponent();
            InputTextBox.Focus(); // 聚焦到输入框
            InputTextBox.Text = currentKey; // 初始化输入框的默认值
            InputTextBox.SelectAll(); // 全选输入框的内容

            this.Title = windowname; // 设置窗口标题
        }

        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            InputText = InputTextBox.Text.Trim(); // 获取输入框的值
            IsOK = true; // 设置确认状态
            this.DialogResult = true; // 关闭窗口并返回成功状态
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            IsOK = false; // 设置取消状态
            this.DialogResult = false; // 关闭窗口并返回取消状态
        }

        private void InputTextBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            // 当用户按下回车键时，触发确认按钮点击事件
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                ConfirmButton_Click(this, new RoutedEventArgs());
            }
        }
    }
}