﻿using Microsoft.Win32;
using ScriptEngine.MuMu;
using ShareX.HelpersLib;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using XHelper;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// MuMuConfigsWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MuMuConfigsWindow : Window
    {
        public ObservableCollection<Models.SimulatorConfig> Simulators { get; set; }

        /// <summary>
        /// MuMu的路径
        /// </summary>
        public string MuMuPath { get; set; }

        /// <summary>
        /// 用户选择的模拟器
        /// </summary>
        public Models.SimulatorConfig SelectedSimulator { get; private set; }

        /// <summary>
        /// 是否选择了模拟器
        /// </summary>
        public bool HasSelectedSimulator { get; private set; } = false;

        /// <summary>
        /// 窗口模式：true = 选择器模式（用于添加游戏），false = 配置模式（用于配置模拟器）
        /// </summary>
        public bool SelectorMode { get; private set; } = false;

        /// <summary>
        /// 默认构造函数 - 配置模式
        /// </summary>
        public MuMuConfigsWindow()
        {
            InitializeComponent();
            SelectorMode = false;

            // 设置窗口置顶
            this.Topmost = true;

            CheckState();
            InitData();
            DataContext = this;

            // 隐藏选择器模式的提示信息
            SelectorModeHint.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 带参数的构造函数 - 可以指定模式
        /// </summary>
        /// <param name="selectorMode">true: 选择器模式，false: 配置模式</param>
        public MuMuConfigsWindow(bool selectorMode)
        {
            InitializeComponent();
            SelectorMode = selectorMode;

            // 设置窗口置顶
            this.Topmost = true;

            CheckState();
            InitData();
            DataContext = this;

            // 根据模式显示或隐藏提示信息
            SelectorModeHint.Visibility = selectorMode ? Visibility.Visible : Visibility.Collapsed;
        }

        /// <summary>
        /// 处理DataGrid的双击事件
        /// </summary>
        private void DataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            // 只在选择器模式下响应双击事件
            if (!SelectorMode) return;

            if (DataGrid.SelectedItem != null && DataGrid.SelectedItem is Models.SimulatorConfig simulator)
            {
                // 弹出确认对话框
                var result = MessageBox.Show(
                    $"确定要添加模拟器 {simulator.Name} (索引: {simulator.Index})吗？",
                    "添加模拟器",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // 用户确认，设置选择的模拟器并关闭窗口
                    SelectedSimulator = simulator;
                    HasSelectedSimulator = true;
                    Close();
                }
            }
        }

        /// <summary>
        /// 这是一个初始化数据的方法，不会显示在界面上
        /// </summary>
        public void InitData_DontShow()
        {
            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (path != null && path != "")
            {
                MuMuPath = path;
                InitData();
                if (Simulators != null && Simulators.Count > 0)
                {
                    XConfig.SaveValueToFile("MuMuConfigs", Simulators);
                }
            }
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitData()
        {
            Simulators = new();
            if (MuMuPath == null || MuMuPath == "") return;
            MuMu mumu = new();
            if (!mumu.Init(MuMuPath)) return;
            var datas = mumu._GetInstances();
            if (datas == null) return;
            foreach (var data in datas.Instances)
                Simulators.Add(new Models.SimulatorConfig().SetData(data.Value));

            //刷新DataGrid数据1
            DataGrid.ItemsSource = null;
            DataGrid.ItemsSource = Simulators;
            //如果adb端口为空 需要从配置文件中读取
            ObservableCollection<Models.SimulatorConfig>? configs = XConfig.LoadValueFromFile<ObservableCollection<Models.SimulatorConfig>>("MuMuConfigs");
            if (configs == null) return;
            foreach (var simulator in Simulators)
            {
                if (simulator.AdbPort <= 0)
                    configs.ForEach(config =>
                    {
                        if (config.Index == simulator.Index)
                        {
                            XLogger.Write("MuMuConfigsWindow: 从配置文件中读取adb端口：" + config.AdbPort);
                            simulator.AdbPort = config.AdbPort;
                        }
                    });
            }

            //刷新DataGrid数据2
            DataGrid.ItemsSource = null;
            DataGrid.ItemsSource = Simulators;
        }

        /// <summary>
        /// 关闭窗口事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Window_Closing(object sender, CancelEventArgs e)
        {
            //保存数据
            XConfig.SaveValueToFile("MuMuConfigs", Simulators);
        }

        /// <summary>
        /// 选择模拟器路径
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Button_Click(object sender, RoutedEventArgs e)
        {
            // 创建 OpenFileDialog 实例
            OpenFileDialog openFileDialog = new OpenFileDialog();

            // 设置文件类型过滤器
            openFileDialog.Filter = "MuMu|MuMuPlayer.exe";

            // 设置对话框标题
            openFileDialog.Title = "选择您的MuMuPlayer.exe路径";

            // 设置初始目录
            openFileDialog.InitialDirectory = "C:\\Program Files\\Netease\\MuMu Player 12\\shell\\";

            // 显示对话框并检查用户是否选择了文件
            if (openFileDialog.ShowDialog() == true)
            {
                // 获取用户选择的文件路径
                string filePath = openFileDialog.FileName;
                MuMuPath = filePath;
                XConfig.SaveValueToFile("MuMuPath", filePath);
                CheckState();
                InitData();
            }
        }

        /// <summary>
        /// 检查所需配置是否OK 并提示
        /// </summary>
        private void CheckState()
        {
            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (path == null || path == "")
            {
                MuMuPathTip.Text = "未设置";
                InitialTipPanel.Visibility = Visibility.Visible;
                MainContent.Visibility = Visibility.Collapsed;
            }
            else
            {
                MuMuPathTip.Text = "已设置";
                MuMuPath = path;
                InitialTipPanel.Visibility = Visibility.Collapsed;
                MainContent.Visibility = Visibility.Visible;
            }
        }
    }
}