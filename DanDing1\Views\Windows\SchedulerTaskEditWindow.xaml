<Window x:Class="DanDing1.Views.Windows.SchedulerTaskEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:uc="clr-namespace:DanDing1.Views.UserControls"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        xmlns:converters="clr-namespace:DanDing1.Helpers"
        Title="添加定时任务"
        Height="405"
        Width="971"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        WindowStartupLocation="CenterOwner">
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <converters:BoolToStringConverter x:Key="BoolToStringConverter" />

        <!-- 卡片样式 -->
        <Style x:Key="CardStyle"
               TargetType="Border">
            <Setter Property="Background"
                    Value="{DynamicResource ControlFillColorDefaultBrush}" />
            <Setter Property="BorderBrush"
                    Value="{DynamicResource ControlElevationBorderBrush}" />
            <Setter Property="BorderThickness"
                    Value="1" />
            <Setter Property="CornerRadius"
                    Value="8" />
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 嵌入现有的任务控件 -->
        <uc:AddTaskControl Margin="5 0 0 0"
                           x:Name="TaskControl"
                           Grid.Row="0" />

        <!-- 定时调度特有的设置 -->
        <Border Grid.Row="1"
                Style="{StaticResource CardStyle}"
                Margin="5 2 5 5">
            <Grid Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <StackPanel Orientation="Horizontal"
                                Margin="0,5,0,0">
                        <TextBlock VerticalAlignment="Center"
                                   Text="选择模拟器：" />
                        <ComboBox Width="150"
                                  ItemsSource="{Binding AvailableEmulators}"
                                  DisplayMemberPath="Name"
                                  SelectedItem="{Binding SelectedEmulator}" />
                    </StackPanel>

                    <StackPanel Orientation="Horizontal"
                                Margin="0,10,0,0">
                        <TextBlock VerticalAlignment="Center"
                                   Text="调度类型：" />
                        <ComboBox Width="150"
                                  ItemsSource="{Binding ScheduleTypes}"
                                  SelectedItem="{Binding SelectedScheduleType}" />
                    </StackPanel>
                </StackPanel>

                <Grid Grid.Column="1"
                      Margin="20,0,0,0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!-- 调度时间设置（根据选择的调度类型动态显示） -->
                    <StackPanel Grid.Row="0"
                                Visibility="{Binding IsOneTime, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock FontWeight="Black" Margin="0 0 0 5" Text="一次性任务时间：" />
                        <StackPanel Orientation="Horizontal">
                            <DatePicker SelectedDate="{Binding OneTimeDate}" />
                            <TextBlock Text=" "
                                       Width="10" />
                            <ComboBox Width="100"
                                      ItemsSource="{Binding HourOptions}"
                                      SelectedItem="{Binding SelectedHour}" />
                            <TextBlock Margin="5 0" Text=":"
                                       VerticalAlignment="Center" />
                            <ComboBox Width="100"
                                      ItemsSource="{Binding MinuteOptions}"
                                      SelectedItem="{Binding SelectedMinute}" />
                        </StackPanel>
                    </StackPanel>

                    <StackPanel Grid.Row="0"
                                Visibility="{Binding IsDaily, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock FontWeight="Black" Margin="0 0 0 5" Text="每日执行时间：" />
                        <StackPanel Orientation="Horizontal">
                            <ComboBox Width="100"
                                      ItemsSource="{Binding HourOptions}"
                                      SelectedItem="{Binding SelectedHour}" />
                            <TextBlock Margin="5 0" Text=":"
                                       VerticalAlignment="Center" />
                            <ComboBox Width="100"
                                      ItemsSource="{Binding MinuteOptions}"
                                      SelectedItem="{Binding SelectedMinute}" />
                        </StackPanel>
                    </StackPanel>

                    <StackPanel Grid.Row="0"
                                Visibility="{Binding IsWeekly, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock FontWeight="Black" Margin="0 0 0 5" Text="每周执行时间：" />
                        <StackPanel Orientation="Horizontal">
                            <ComboBox Width="120"
                                      ItemsSource="{Binding WeekdayOptions}"
                                      SelectedItem="{Binding SelectedWeekday}" />
                            <TextBlock Text=" "
                                       Width="10" />
                            <ComboBox Width="100"
                                      ItemsSource="{Binding HourOptions}"
                                      SelectedItem="{Binding SelectedHour}" />
                            <TextBlock Margin="5 0" Text=":"
                                       VerticalAlignment="Center" />
                            <ComboBox Width="100"
                                      ItemsSource="{Binding MinuteOptions}"
                                      SelectedItem="{Binding SelectedMinute}" />
                        </StackPanel>
                    </StackPanel>

                    <StackPanel Grid.Row="0"
                                Visibility="{Binding IsMonthly, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock FontWeight="Black" Margin="0 0 0 5" Text="每月执行时间：" />
                        <StackPanel Orientation="Horizontal">
                            <ComboBox Width="100"
                                      ItemsSource="{Binding DayOptions}"
                                      SelectedItem="{Binding SelectedDay}" />
                            <TextBlock Margin="5 0 0 0" Text="日 "
                                       VerticalAlignment="Center" />
                            <TextBlock Text=" "
                                       Width="10" />
                            <ComboBox Width="100"
                                      ItemsSource="{Binding HourOptions}"
                                      SelectedItem="{Binding SelectedHour}" />
                            <TextBlock Margin="6 0" Text=":"
                                       VerticalAlignment="Center" />
                            <ComboBox Width="100"
                                      ItemsSource="{Binding MinuteOptions}"
                                      SelectedItem="{Binding SelectedMinute}" />
                        </StackPanel>
                    </StackPanel>

                    <!-- 间隔执行设置面板 -->
                    <StackPanel Grid.Row="0"
                                Visibility="{Binding IsInterval, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock FontWeight="Black" Margin="0 0 0 5" Text="间隔执行设置：" />
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="每"
                                       VerticalAlignment="Center" />
                            <TextBox Width="80"
                                     Margin="5,0"
                                     Text="{Binding IntervalValue}" />
                            <ComboBox Width="100"
                                      ItemsSource="{Binding IntervalUnits}"
                                      SelectedItem="{Binding SelectedIntervalUnit}" />
                            <TextBlock Text="执行一次"
                                       VerticalAlignment="Center"
                                       Margin="5,0" />
                        </StackPanel>
                        <TextBlock Text="{Binding NextExecutionPreview}"
                                   Margin="0,5,0,0"
                                   Foreground="{DynamicResource SystemFillColorSuccessBrush}" />
                    </StackPanel>

                    <!-- 循环执行设置面板 -->
                    <StackPanel Grid.Row="0"
                                Visibility="{Binding IsCron, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock FontWeight="Black" Margin="0 0 0 5" Text="循环执行设置（Cron表达式）：" />
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="250" />
                                    <ColumnDefinition />
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock VerticalAlignment="Center" Text="自定义Cron表达式："
                                                   Margin="0,10,0,0" />
                                        <TextBox Width="400"
                                                 HorizontalAlignment="Left"
                                                 Text="{Binding CronExpression, UpdateSourceTrigger=PropertyChanged}" />
                                    </StackPanel>
                                    <StackPanel Margin="0 5 0 0" Orientation="Horizontal">
                                        <TextBlock VerticalAlignment="Center" Text="选择预设模板："
                                                   Margin="0,5,0,0" />
                                        <ComboBox Width="300"
                                                  HorizontalAlignment="Left"
                                                  ItemsSource="{Binding CronTemplates}"
                                                  DisplayMemberPath="Name"
                                                  SelectedItem="{Binding SelectedCronTemplate}" />
                                    </StackPanel>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <GroupBox
                                          Header="下次执行时间预览"
                                          Margin="0,-8,0,0"
                                          MaxWidth="500"
                                          HorizontalAlignment="Left">
                                        <TextBlock Text="{Binding NextExecutionPreview}"
                                               TextWrapping="Wrap"
                                               Foreground="{DynamicResource SystemFillColorSuccessBrush}" />
                                    </GroupBox>
                                    <Button Width="35"
                                                Height="35"
                                                Margin="5,0,0,0"
                                                ToolTipService.InitialShowDelay="0"
                                                ToolTipService.ShowDuration="20000"
                                                Content="?">
                                        <Button.ToolTip>
                                            <ToolTip>
                                                <Border BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                            BorderThickness="1"
                                                            Padding="5"
                                                            CornerRadius="3"
                                                            Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                                            MaxWidth="500">
                                                    <StackPanel>
                                                        <TextBlock TextWrapping="Wrap"
                                                                       FontSize="12"
                                                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}">
                                                                <Run FontWeight="Bold">Cron表达式格式：</Run>
                                                                <LineBreak />
                                                                <Run FontWeight="Bold">标准格式(5段):</Run> 分钟 小时 日期 月份 星期几
                                                                <LineBreak />
                                                                <Run Foreground="{DynamicResource SystemFillColorCriticalBrush}">注意：Cronos库使用标准的5段Cron格式，不包含"秒"！</Run>
                                                                <LineBreak />
                                                                <Run FontWeight="Bold">例如：</Run>
                                                                <LineBreak />
                                                                "0 12 * * *" = 每天中午12点
                                                                <LineBreak />
                                                                "*/30 * * * *" = 每30分钟
                                                                <LineBreak />
                                                                "0 8-18 * * 1-5" = 工作日8点至18点整点执行
                                                                <LineBreak />
                                                                <Run Foreground="{DynamicResource SystemFillColorCautionBrush}">如果输入了6段格式(带秒)，系统会自动去除首个"秒"字段</Run>
                                                        </TextBlock>
                                                    </StackPanel>
                                                </Border>
                                            </ToolTip>
                                        </Button.ToolTip>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </StackPanel>
                </Grid>

                <StackPanel Grid.Column="2"
                            Orientation="Horizontal"
                            VerticalAlignment="Bottom"
                            HorizontalAlignment="Right">
                    <Button Content="取消"
                            Margin="0,0,10,0"
                            Click="CancelButton_Click" />
                    <Button x:Name="ConfirmButton"
                            Content="确定添加"
                            Click="ConfirmButton_Click" />
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window> 