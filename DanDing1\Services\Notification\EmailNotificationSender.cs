using System.Threading.Tasks;
using XHelper.DanDingNet;

namespace DanDing1.Services.Notification
{
    /// <summary>
    /// 邮件通知发送器
    /// </summary>
    public class EmailNotificationSender : BaseNotificationSender
    {
        private readonly UserService _userService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="userService">用户服务实例</param>
        public EmailNotificationSender(UserService userService)
        {
            _userService = userService;
        }

        /// <summary>
        /// 通知类型标识符
        /// </summary>
        public override string NoticeType => "email";

        /// <summary>
        /// 通知类型显示名称
        /// </summary>
        public override string DisplayName => "邮件";

        /// <summary>
        /// 格式化邮件内容，使用HTML格式的换行
        /// </summary>
        /// <param name="content">原始内容</param>
        /// <returns>格式化后的内容</returns>
        public override string FormatContent(string content)
        {
            // 使用HTML的换行标签替换特定的换行标记
            return content.Replace("#换行", "<br>");
        }

        /// <summary>
        /// 发送邮件通知的核心实现
        /// </summary>
        /// <param name="title">邮件标题</param>
        /// <param name="content">邮件内容</param>
        /// <param name="extraParams">额外参数</param>
        /// <returns>发送是否成功</returns>
        protected override async Task<bool> SendNotificationCoreAsync(string title, string content, object extraParams)
        {
            // 调用API发送邮件通知
            return await _userService.SendNoticeAsync(NoticeType, title, content);
        }
    }
}