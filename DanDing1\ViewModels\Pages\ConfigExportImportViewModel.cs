using DanDing1.Models;
using DanDing1.Models.Super;
using DanDing1.Services;
using DanDing1.ViewModels.Windows;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace DanDing1.ViewModels.Pages
{
    /// <summary>
    /// 配置导出导入ViewModel
    /// </summary>
    public class ConfigExportImportViewModel : INotifyPropertyChanged
    {
        private readonly ConfigExportImportService _exportImportService;
        private readonly List<EmulatorItem> _emulators;
        private readonly List<ScheduledTask> _tasks;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="emulators">模拟器列表</param>
        /// <param name="tasks">任务列表</param>
        public ConfigExportImportViewModel(List<EmulatorItem> emulators, List<ScheduledTask> tasks)
        {
            _emulators = emulators;
            _tasks = tasks;
            _exportImportService = new ConfigExportImportService();

            ExportCommand = new Command(obj => ExecuteExportCommand());
            ImportCommand = new Command(obj => ExecuteImportCommand());
        }

        /// <summary>
        /// 属性更改事件
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 触发属性更改通知
        /// </summary>
        protected void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 导出命令
        /// </summary>
        public ICommand ExportCommand { get; }

        /// <summary>
        /// 导入命令
        /// </summary>
        public ICommand ImportCommand { get; }

        /// <summary>
        /// 执行导出命令
        /// </summary>
        private void ExecuteExportCommand()
        {
            _ = ExportConfig();
        }

        /// <summary>
        /// 执行导入命令
        /// </summary>
        private void ExecuteImportCommand()
        {
            _ = ImportConfig();
        }

        /// <summary>
        /// 导出配置
        /// </summary>
        private async Task ExportConfig()
        {
            try
            {
                // 显示保存文件对话框
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "JSON文件|*.json",
                    Title = "导出配置",
                    FileName = $"DanDing1配置备份_{DateTime.Now:yyyyMMdd}.json"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // 显示加载中提示
                    IsProcessing = true;
                    StatusMessage = "正在导出配置...";

                    // 执行导出
                    bool success = await _exportImportService.ExportConfigAsync(
                        saveFileDialog.FileName, _emulators, _tasks);

                    // 显示结果
                    if (success)
                    {
                        MessageBox.Show(
                            $"配置导出成功！\n\n文件保存在: {saveFileDialog.FileName}",
                            "导出成功",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show(
                            "配置导出失败！请查看日志了解详细信息。",
                            "导出失败",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }

                    // 清除加载状态
                    IsProcessing = false;
                    StatusMessage = string.Empty;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"导出过程中出错: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                IsProcessing = false;
                StatusMessage = string.Empty;
            }
        }

        /// <summary>
        /// 导入配置
        /// </summary>
        private async Task ImportConfig()
        {
            try
            {
                // 显示打开文件对话框
                var openFileDialog = new OpenFileDialog
                {
                    Filter = "JSON文件|*.json",
                    Title = "导入配置"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    // 显示警告
                    var confirmResult = MessageBox.Show(
                        "导入配置将覆盖现有设置，是否继续？\n\n注意：导入前请确保对应的模拟器已正确安装。",
                        "导入确认",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (confirmResult != MessageBoxResult.Yes)
                    {
                        return;
                    }

                    // 显示加载中提示
                    IsProcessing = true;
                    StatusMessage = "正在导入配置...";

                    // 执行导入
                    var report = await _exportImportService.ImportConfigAsync(
                        openFileDialog.FileName, _emulators, _tasks);

                    // 显示导入报告
                    _exportImportService.ShowImportReport(report);

                    // 如果导入成功并且需要刷新UI
                    if (report.RequiresUIRefresh)
                    {
                        // 通知外层ViewModel需要刷新
                        RefreshParentUI?.Invoke(this, EventArgs.Empty);

                        // 或者显示提示，要求用户刷新或重启应用
                        MessageBox.Show(
                            "配置已导入并保存，请点击刷新按钮或重启应用程序以应用更改。",
                            "导入完成",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }

                    // 清除加载状态
                    IsProcessing = false;
                    StatusMessage = string.Empty;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"导入过程中出错: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                IsProcessing = false;
                StatusMessage = string.Empty;
            }
        }

        private bool _isProcessing;

        /// <summary>
        /// 是否处理中
        /// </summary>
        public bool IsProcessing
        {
            get => _isProcessing;
            set
            {
                _isProcessing = value;
                OnPropertyChanged();
            }
        }

        private string _statusMessage;

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 通知父ViewModel刷新UI的事件
        /// </summary>
        public event EventHandler RefreshParentUI;
    }

    /// <summary>
    /// 简单命令实现
    /// </summary>
    public class Command : ICommand
    {
        private readonly Action<object> _execute;
        private readonly Func<object, bool> _canExecute;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="execute">执行方法</param>
        /// <param name="canExecute">可执行条件</param>
        public Command(Action<object> execute, Func<object, bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        /// <summary>
        /// 可执行性变更事件
        /// </summary>
        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        /// <summary>
        /// 判断命令是否可执行
        /// </summary>
        public bool CanExecute(object parameter)
        {
            return _canExecute == null || _canExecute(parameter);
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        public void Execute(object parameter)
        {
            _execute(parameter);
        }
    }
}