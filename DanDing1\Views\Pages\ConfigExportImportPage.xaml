<Page
    x:Class="DanDing1.Views.Pages.ConfigExportImportPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:DanDing1.Views.Pages"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    mc:Ignorable="d"
    d:DesignHeight="600"
    d:DesignWidth="800"
    Background="{DynamicResource {x:Static SystemColors.ControlBrushKey}}"
    Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
    Title="系统配置导出导入">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 页面标题 -->
        <TextBlock Grid.Row="0"
                   Text="系统配置导出导入"
                   FontSize="24"
                   FontWeight="Bold"
                   Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                   Margin="0,0,0,20"/>

        <!-- 主要内容 -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 导出配置部分 -->
            <Border Grid.Row="0"
                    BorderThickness="1"
                    BorderBrush="{DynamicResource {x:Static SystemColors.ControlLightBrushKey}}"
                    Background="{DynamicResource {x:Static SystemColors.ControlBrushKey}}"
                    Padding="20"
                    Margin="0,0,0,20"
                    CornerRadius="5">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0"
                                Margin="0,0,20,0">
                        <TextBlock Text="导出系统配置"
                                   FontSize="18"
                                   Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                                   FontWeight="SemiBold"/>
                        <TextBlock Text="将当前系统的所有模拟器和定时任务配置导出为JSON文件，用于备份或迁移"
                                   Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                                   TextWrapping="Wrap"
                                   Margin="0,10,0,0"/>
                        <TextBlock Text="导出内容包括："
                                   Margin="0,10,0,5"
                                   Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                                   FontWeight="SemiBold"/>
                        <TextBlock Text="• 所有模拟器配置"
                                   Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                                   Margin="20,0,0,0"/>
                        <TextBlock Text="• 所有定时任务设置"
                                   Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                                   Margin="20,0,0,0"/>
                        <TextBlock Text="• 定时系统配置参数"
                                   Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                                   Margin="20,0,0,0"/>
                    </StackPanel>

                    <ui:Button Grid.Column="1"
                               Content="导出配置"
                               Command="{Binding ExportCommand}"
                               Width="120"
                               Height="40"
                               Icon="ArrowExportUp24"
                               Appearance="Primary"
                               VerticalAlignment="Bottom"/>
                </Grid>
            </Border>

            <!-- 导入配置部分 -->
            <Border Grid.Row="1"
                    BorderThickness="1"
                    BorderBrush="{DynamicResource {x:Static SystemColors.ControlLightBrushKey}}"
                    Background="{DynamicResource {x:Static SystemColors.ControlBrushKey}}"
                    Padding="20"
                    Margin="0,0,0,20"
                    CornerRadius="5">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0"
                                Margin="0,0,20,0">
                        <TextBlock Text="导入系统配置"
                                   FontSize="18"
                                   Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                                   FontWeight="SemiBold"/>
                        <TextBlock Text="导入之前备份的系统配置，恢复或迁移所有模拟器和定时任务设置"
                                   Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                                   TextWrapping="Wrap"
                                   Margin="0,10,0,0"/>
                        <TextBlock Text="重要提示："
                                   Margin="0,10,0,5"
                                   FontWeight="SemiBold"
                                   Foreground="{DynamicResource SystemErrorTextBrush}"/>
                        <TextBlock TextWrapping="Wrap"
                                   Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                                   Margin="20,0,0,0"
                                   Text="• 导入前请确保模拟器已正确安装，且名称与导入文件中的匹配"/>
                        <TextBlock TextWrapping="Wrap"
                                   Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                                   Margin="20,0,0,0"
                                   Text="• 导入过程会自动重新分配模拟器索引，确保与当前系统匹配"/>
                        <TextBlock TextWrapping="Wrap"
                                   Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                                   Margin="20,0,0,0"
                                   Text="• 如未找到匹配的模拟器名称，相关任务将被跳过"/>
                    </StackPanel>

                    <ui:Button Grid.Column="1"
                               Content="导入配置"
                               Command="{Binding ImportCommand}"
                               Width="120"
                               Height="40"
                               Icon="ArrowImportDown24"
                               Appearance="Primary"
                               VerticalAlignment="Bottom"/>
                </Grid>
            </Border>

            <!-- 帮助提示 -->
            <Border Grid.Row="2"
                    BorderThickness="1"
                    BorderBrush="{DynamicResource SystemWarningColorBrush}"
                    Background="{DynamicResource SystemInfoBackgroundBrush}"
                    Padding="20"
                    Margin="0,20,0,0"
                    CornerRadius="5">
                <StackPanel>
                    <TextBlock Text="使用说明"
                               Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                               FontSize="16"
                               FontWeight="SemiBold"/>
                    <TextBlock TextWrapping="Wrap"
                               Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                               Margin="0,10,0,0">
                        <Run Text="1. 建议您定期导出系统配置作为备份，防止数据丢失"/>
                        <LineBreak/>
                        <Run Text="2. 迁移到新系统时，请先确保新系统已安装相同名称的模拟器"/>
                        <LineBreak/>
                        <Run Text="3. 导入完成后，将提供详细的导入报告，指明成功和失败的项目"/>
                        <LineBreak/>
                        <Run Text="4. 如遇到导入失败，请查看报告中的具体原因进行处理"/>
                    </TextBlock>
                </StackPanel>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2"
                BorderThickness="0,1,0,0"
                BorderBrush="{DynamicResource {x:Static SystemColors.ControlLightBrushKey}}"
                Padding="0,10,0,0"
                Margin="0,20,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Text="{Binding StatusMessage}"
                           Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                           VerticalAlignment="Center"/>

                <ProgressBar Grid.Column="1"
                             Width="100"
                             Height="10"
                             IsIndeterminate="{Binding IsProcessing}"
                             Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </Grid>
        </Border>

        <!-- 正在处理遮罩 -->
        <Grid Grid.Row="0"
              Grid.RowSpan="3"
              Background="#80000000"
              Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Border Background="{DynamicResource {x:Static SystemColors.ControlLightBrushKey}}"
                    Width="300"
                    Height="100"
                    CornerRadius="5"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center">
                <StackPanel VerticalAlignment="Center"
                            HorizontalAlignment="Center">
                    <TextBlock Text="{Binding StatusMessage}"
                               Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                               FontSize="16"
                               HorizontalAlignment="Center"/>
                    <ProgressBar Width="250"
                                 Height="5"
                                 Margin="0,15,0,0"
                                 IsIndeterminate="True"/>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Page> 