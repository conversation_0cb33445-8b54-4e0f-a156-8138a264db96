﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace XHelper
{
    public class XJsonEncryptor
    {
        private static string EncryptionKey = "E0D1B7941FDEE9CC923CCD680F4B50D8";
        private static string EncryptionIV = "blog.x-tools.top";
        /// <summary>
        /// 初始化 获取Key
        /// </summary>
        /// <returns></returns>
        public bool Init()
        {
#if DEBUG
            return true;
#else
            throw new Exception("发布前请开发安全获取Key的方法");
            return false;
#endif
        }

        private readonly byte[] _key;
        private readonly byte[] _iv;

        public XJsonEncryptor()
        {
            _key = new byte[16]; // AES-256需要的字节数
            Array.Copy(Encoding.UTF8.GetBytes(EncryptionKey), _key, Math.Min(Encoding.UTF8.GetBytes(EncryptionKey).Length, _key.Length));

            _iv = new byte[16]; // AES-256需要的字节数
            Array.Copy(Encoding.UTF8.GetBytes(EncryptionIV), _iv, Math.Min(Encoding.UTF8.GetBytes(EncryptionIV).Length, _iv.Length));
        }
        public string Encrypt(string plainText)
        {
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = _key;
                aesAlg.IV = _iv;

                ICryptoTransform encryptor = aesAlg.CreateEncryptor();
                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                        {
                            swEncrypt.Write(plainText);
                        }
                    }
                    byte[] encryptedBytes = msEncrypt.ToArray();
                    return Convert.ToBase64String(encryptedBytes);
                }
            }
        }

        public string Decrypt(string cipherText)
        {
            byte[] cipherBytes = Convert.FromBase64String(cipherText);
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = _key;
                aesAlg.IV = _iv;

                ICryptoTransform decryptor = aesAlg.CreateDecryptor();
                using (MemoryStream msDecrypt = new MemoryStream(cipherBytes))
                {
                    using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    {
                        using (StreamReader srDecrypt = new StreamReader(csDecrypt))
                        {
                            return srDecrypt.ReadToEnd();
                        }
                    }
                }
            }
        }
    }
}
