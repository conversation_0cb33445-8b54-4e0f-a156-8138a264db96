using System.Net.Http.Headers;
using System.Text.Json;

namespace XHelper.DanDingNet
{
    public abstract class BaseService : ChackBase
    {
        protected readonly HttpClient _client;
        protected readonly string _macCode;
        protected readonly string _version;
        protected readonly JsonSerializerOptions _jsonOptions;
        protected string _host;

        protected BaseService(HttpClient client, string macCode, string host, string version)
        {
            _client = client;
            _macCode = macCode;
            _host = host;
            _version = version;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        public virtual void UpdateHost(string newHost)
        {
            _host = newHost;
        }

        public virtual string GetCurrentHost() => _host;

        /// <summary>
        /// 获取服务器标识对应的实际URL
        /// </summary>
        protected string GetServerUrl(string identifier)
        {
            if (Uri.TryCreate(identifier, UriKind.Absolute, out _))
            {
                return identifier; // 如果已经是URL则直接返回
            }
            return DDApi.ServerHost.TryGetValue(identifier, out string? url) ? url : identifier;
        }

        protected async Task<T> RetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3)
        {
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    return await operation();
                }
                catch (TaskCanceledException e) when (e.CancellationToken == default)
                {
                    if (i == maxRetries - 1)
                        throw;

                    int delayMs = (int)Math.Pow(2, i) * 1000;
                    await Task.Delay(delayMs);

                    XLogger.Debug($"请求超时，正在进行第{i + 1}次重试: {e.Message}");
                }
            }
            throw new Exception($"重试{maxRetries}次后仍然失败");
        }

        protected async Task<HttpResponseMessage> SendRequestWithRetryAsync(HttpRequestMessage message)
        {
            return await RetryAsync(async () =>
            {
                var response = await _client.SendAsync(message);
                if(response.StatusCode == System.Net.HttpStatusCode.Unauthorized) return response;
                response.EnsureSuccessStatusCode();

                // 检查是否是GZIP压缩的响应
                if (response.Content.Headers.ContentEncoding.Contains("gzip"))
                {
                    var compressedStream = await response.Content.ReadAsStreamAsync();
                    using var gzipStream = new System.IO.Compression.GZipStream(compressedStream, System.IO.Compression.CompressionMode.Decompress);
                    using var decompressedStream = new MemoryStream();
                    await gzipStream.CopyToAsync(decompressedStream);
                    decompressedStream.Position = 0;

                    // 创建新的HttpResponseMessage并替换Content
                    var newResponse = new HttpResponseMessage(response.StatusCode)
                    {
                        Content = new StreamContent(decompressedStream),
                        RequestMessage = response.RequestMessage,
                        Version = response.Version
                    };
                    foreach (var header in response.Headers)
                    {
                        newResponse.Headers.TryAddWithoutValidation(header.Key, header.Value);
                    }
                    newResponse.Content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
                    return newResponse;
                }

                return response;
            });
        }

        protected HttpRequestMessage GetBaseRequest(HttpMethod method, string url)
        {
            if (CheckObjisNull(_client, "HttpClient is null"))
                throw new Exception("HttpClient is null");

            try
            {
                var tmp = new HttpRequestMessage
                {
                    Method = method,
                    RequestUri = new Uri(url),
                    Headers =
                    {
                        { "Accept", "*/*" },
                        { "User-Agent", $"DanDing-Client/V{_version}" },
                        { "Connection", "keep-alive" },
                    }
                };

                if (_client.DefaultRequestHeaders.Authorization != null)
                    tmp.Headers.Authorization = _client.DefaultRequestHeaders.Authorization;

                return tmp;
            }
            catch (Exception e)
            {
                var serverUrl = GetServerUrl(url);
                XLogger.Error($"构造请求失败！请检查网络连接是否正常！包括代理！({serverUrl})");
                XLogger.SaveException(e);
                throw;
            }
        }
    }

    public class ChackBase
    {
        /// <summary>
        /// 检查对象是否为空
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public bool CheckObjisNull(object? obj) => obj is null;

        /// <summary>
        /// 检查对象是否为空 为空时输出错误文本
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public bool CheckObjisNull(object? obj, string str)
        {
            bool i = obj is null;
            if (i) XLogger.Error(str);
            return i;
        }
    }
}