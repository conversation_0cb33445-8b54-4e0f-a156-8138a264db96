﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System.Text.RegularExpressions;

namespace ScriptEngine.Tasks.Actives
{
    internal class ActiveTask : BaseTask
    {
        public bool FirstEnd = false;

        /// <summary>
        /// 活动任务类型
        /// </summary>
        private string _Class = "";

        private bool Automated = false;

        /// <summary>
        /// 是否开启标记功能
        /// </summary>
        private bool Biaoji = false;

        private bool BiaoJi_Status = false;

        private Dictionary<string, Pixel> Colors = new Dictionary<string, Pixel>()
        {
            {"结算",new Pixel(726,412,"f3b25e-101010",0.96) },
        };

        private int count = 0;

        private List<string> DontSendLog = ["标记"];

        //半自动战斗时长
        private double fakeTime = 0;

        /// <summary>
        /// 使用体力模式
        /// </summary>
        private string Mode = "活动体力";

        /// <summary>
        /// 任务次数
        /// </summary>
        private int Ncount = 0;

        /// <summary>
        /// OCR识别区域
        /// </summary>
        private Dictionary<string, Position> OcrPosition = new()
        {
            {"活动体力",new(708,16,810,48 )},
            {"普通体力",new(1101,17,1223,49) },
        };

        /// <summary>
        /// 提前结算
        /// </summary>
        private int tiqianjiesuan5_15 = 0;

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "活动");
        }

        /// <summary>
        /// 是否在活动场景
        /// </summary>
        /// <returns></returns>
        public bool NowisMainScene()
        {
            var pics = Mp.Filter("活动.Main_主场景");
            return pics.FindAll();
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Ncount = GetConfig.Count;
            GetConfig.Others.TryGetValue("Biaoji", out string? s);
            try { Biaoji = s is null ? false : bool.Parse(s); } catch (Exception) { Biaoji = false; }
            GetConfig.Others.TryGetValue("Mode", out Mode);
            Mode ??= "活动体力";
            GetConfig.Others.TryGetValue("FirstEnd", out s);
            try { FirstEnd = s is null ? false : bool.Parse(s); } catch (Exception) { FirstEnd = false; }
            GetConfig.Others.TryGetValue("Automated", out s);
            try { Automated = s is null ? false : bool.Parse(s); } catch (Exception) { Automated = false; }
            GetConfig.Others.TryGetValue("FakeTime", out s);
            try { fakeTime = s is null ? 0 : double.Parse(s); } catch (Exception) { fakeTime = 0; }
            //_Class
            GetConfig.Others.TryGetValue("HDong_Class", out _Class);
            Mode ??= "日轮之影(爬塔)";

            //if (Automated)
            //{
            //    //半自动模式 4.25
            //    log.Info("当前为活动半自动模式，请保证游戏在指定Boss爬塔界面中！");
            //    _Automated();
            //    return;
            //}

            if (NowisMainScene())
            {
                log.Info_Green("当前在活动爬塔场景内，无需调整界面，直接开始！");
                goto Start;
            }

            string nows = Scene.NowScene;
            if (nows.Contains("探索"))
            {
                log.Info("进入场景：庭院");
                if (!Scene.TO.TingYuan())
                {
                    log.Warn("活动任务无法继续，当前游戏所在场景未知，请调整到庭院界面开始脚本！");
                    return;
                }
            }
            //进入活动界面
            log.Info("进入活动界面..");
            Scene.TO.Activity(_Class ?? "");
            Sleep(1000);
        Start:
            main();
            UserNotificationMessage = $"共战斗{count}/{Ncount}次. 提前结算{tiqianjiesuan5_15}次";
        }

        /// <summary>
        /// 等待活动场景
        /// </summary>
        /// <returns></returns>
        public bool WaitMainScene()
        {
            MemPics? pics = null;
            if (_Class.Contains("时间裂缝"))
                pics = Mp.Filter("活动.Main_主场景");
            else if (_Class.Contains("日轮之影"))
                pics = Mp.Filter("活动.Main_主爬塔场景");
            else if (_Class.Contains("阵练演武"))
                pics = Mp.Filter("活动.Main_阵练演武场景");

            if (pics?.Wait() ?? false) return true;
            return false;
        }

        /// <summary>
        /// 半自动模式
        /// </summary>
        private void _Automated()
        {
            //判断场景 场景_退治
            //int Gw_Count = 0;
            //int Zd_Count = 0;
            //var pics_TZ = Mp.Filter("挑战鬼王");
            //var pics_ZH = Mp.Filter("召唤鬼王");
            bool ret = false;
            while (!ret)
            {
                if (Combat())
                {
                    count++;
                    log.Info($"活动战斗胜利，战斗次数：{count}");
                }
                else
                {
                    log.Warn($"活动战斗失败，请检查您的队伍配置是否正常！战斗胜利次数：{count}");
                    Defeated();
                }

                //if (pics_TZ.FindAll())
                //{
                //    Zd_Count++;
                //    log.Info($"点击挑战..当前累计战斗次数：{Zd_Count}次..");
                //    Sleep(1000);
                //    Combat();//半自动模式
                //}
                //Sleep(1000);
                //if (pics_ZH.FindAll())
                //{
                //    Gw_Count++;
                //    log.Info($"点击召唤鬼王..当前已召唤：{Gw_Count}次鬼王..");
                //    Sleep(1000);
                //    Fast.Click(1141, 585, 1223, 665);
                //}
                Sleep(1000);
            }
        }

        /// <summary>
        /// 提前结束
        /// </summary>
        /// <returns></returns>
        private bool _FirstEnd()
        {
            var str = Fast.Ocr_Local(261, 588, 320, 618);
            if (!string.IsNullOrEmpty(str))
            {
                if (int.TryParse(str.Split('/')[0], out int count))
                {
                    log.Debug($"OCR内置识别结果为：{count}");
                    return count >= 50;
                }
            }
            var str1 = Fast.Ocr_String(161, 589, 360, 619);
            if (!string.IsNullOrEmpty(str1))
            {
                Match match = Regex.Match(str1, @":(\d+)/");
                if (match.Success)
                {
                    if (int.TryParse(match.Groups[1].Value, out int count))
                    {
                        log.Debug($"OCR-Str识别结果为：{count}");
                        return count >= 50;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 判断活动次数
        /// </summary>
        /// <returns></returns>
        private bool CheckCount()
        {
            return true;
        ReOcr:
            bool ret = true;
            int c = 0;
            if (Mode == "活动体力")
            {
                var v = Fast.Ocr_Local(OcrPosition["活动体力"].X, OcrPosition["活动体力"].Y, OcrPosition["活动体力"].X1, OcrPosition["活动体力"].Y1);
                try
                {
                    c = int.Parse(v.Split('/')[0]);
                }
                catch (Exception)
                {
                    log.Warn("当前体力信息无法识别，重新OCR！");
                    goto ReOcr;
                }
                if (c < 1)
                {
                    log.Warn("当前体力不足，退出活动！");
                    ret = false;
                }
            }
            else if (Mode == "普通体力")
            {
                var v = Fast.Ocr_Local(OcrPosition["普通体力"].X, OcrPosition["普通体力"].Y, OcrPosition["普通体力"].X1, OcrPosition["普通体力"].Y1);
                try
                {
                    c = int.Parse(v.Split('/')[0]);
                }
                catch (Exception)
                {
                    log.Warn("当前体力信息无法识别，重新OCR！");
                    goto ReOcr;
                }
                if (c == 300)
                {
                    log.Warn("当前次数达到上限，退出活动！");
                    ret = false;
                }
            }
            log.Info("当前活动剩余体力或已打次数为：" + c);
            return ret;
        }

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            //点击开始
            Fast.Click(1138, 601, 1228, 647);
            log.Info("战斗点击开始");

            // 记录战斗开始时间
            //DateTime startTime = DateTime.Now;

            //if (fakeTime > 0)
            //    log.Info($"半自动战斗模式，设定时长：{fakeTime}分钟");
            var biaoji_pics = Mp.Filter("活动.战斗_标记");
            var pics = Mp.Filter("活动.战斗_");
            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置

            // 添加标记图片超时检测
            DateTime lastBiaojiFoundTime = DateTime.Now;
            bool biaojiFound = false;

            while (!isbreak)
            {
                //// 计算战斗时长
                //TimeSpan duration = DateTime.Now - startTime;
                //double minutes = duration.TotalMinutes;
                //// 判断是否超时
                //if (fakeTime > 0 && minutes > fakeTime)
                //{
                //    log.Warn($"战斗时间({minutes:F2}分钟)超过设定时长({fakeTime}分钟)");
                //    // 这里调用用户自定义的方法
                //    OnCombatTimeout(minutes);
                //    ret_bol = true;
                //    isbreak = true;
                //    Sleep(150);
                //    break;
                //}

                // 检查标记图片
                if (biaoji_pics.FindAll())
                {
                    biaojiFound = true;
                    lastBiaojiFoundTime = DateTime.Now;
                }
                else if (biaojiFound && (DateTime.Now - lastBiaojiFoundTime).TotalSeconds > 5)
                {
                    // 如果曾经找到过标记图片，但现在超过5秒没找到，判定战斗结束
                    log.Info("标记图片超过5秒未出现，判定战斗结束");
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                    continue;
                }

                if (_Class.Contains("时间裂缝") && Colors["结算"].Find(Dm))
                {
                    log.Info("出现提前结算窗口，点击结算..");
                    Fast.Click(726, 412);
                    tiqianjiesuan5_15++;
                }

                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                FindOkFun(p.Name, p);
                if (!DontSendLog.Any(p.Name.Contains)) log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("胜利") || p.Name.Contains("达摩"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败"))
                {
                    ret_bol = false;
                    isbreak = true;
                }
            }
            if (ret_bol)
                Combat_End();

            return ret_bol;
        }

        /// <summary>
        /// 胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            //if (Automated) return; //半自动不执行

            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("活动.战斗_");
            //.Add(Mp.Filter("活动.半自动结束"));

            bool isbreak = false;
            DateTime lastClickTime = DateTime.Now.AddSeconds(-2); // 初始化时间，确保第一次循环就会点击
            DateTime startTime = DateTime.Now;
            int timeoutSeconds = 60; // 超时时间（秒），可根据需要调整

            while (!isbreak)
            {
                // 超时检测
                if ((DateTime.Now - startTime).TotalSeconds > timeoutSeconds)
                {
                    log.Warn($"Combat_End循环超时（>{timeoutSeconds}秒），自动退出收尾流程。");
                    break;
                }

                // 每2秒点击一次指定范围
                if ((DateTime.Now - lastClickTime).TotalSeconds >= 2)
                {
                    Fast.Click(1121, 135, 1169, 206);
                    lastClickTime = DateTime.Now;
                }

                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                if (p.Name.Contains("挑战"))
                {
                    isbreak = true;
                    continue;
                }
                log.Info($"执行点击：{p._Name}");
                p.Click();
            }
        }

        /// <summary>
        /// 退出活动界面
        /// </summary>
        private void EndCallBack()
        {
            log.Info("执行活动任务收尾方法,等待返回后,退出到探索。");
            var mps = new MemPics()
                .Add(Mp.Filter("活动.准备"))
                .Add(Mp.Filter("活动.达摩"));

            while (!Mp.Filter("活动.战斗_挑战").FindAll())
            {
                if (NowisMainScene())
                    break;
                mps.FindAllAndClick();
                Sleep(1000);
            }

            log.Info("退出到庭院..");
            Sleep(2000);
            Fast.Click(25, 20, 57, 51);
            Sleep(4000);
            Fast.Click(25, 20, 57, 51);
            Sleep(2000);
            if (_Class.Contains("日轮之影") || _Class.Contains("阵练演武"))
            {
                Fast.Click(25, 20, 57, 51);
                Sleep(2000);
            }
        }

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            if (Biaoji && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.2秒，标记位置：5号位");
                Sleep(100);
                Fast.Click(1004, 483, 1027, 535);
                return false;
            }
            return true;
        }

        /// <summary>
        /// 主流程
        /// </summary>
        private void main()
        {
            //ToggleState();

            if (UserConfig_Preset != null)
            {
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入式神录，开始应用预设{UserConfig_Preset}");
                if (_Class.Contains("时间裂缝"))
                    Fast.Click(850, 634, 885, 671);
                else if (_Class.Contains("日轮之影"))
                    Fast.Click(1022, 580, 1051, 609);
                Sleep(1500);
                Tmp.Do_Preset(preset);
            }
        Re:
            while (count < Ncount)
            {
                //if (!CheckCount()) break;
                //if (FirstEnd && _FirstEnd())
                //{
                //    log.Info_Green("当前票数已达到50，退出爬塔！");
                //    break;
                //}
                if (!WaitMainScene()) goto Re;
                //防封等待
                Anti.RandomDelay();
                if (Combat())
                {
                    count++;
                    log.Info($"活动战斗胜利，战斗次数：{count}/{Ncount}");
                }
                else
                {
                    log.Warn($"活动战斗失败，请检查您的队伍配置是否正常！战斗次数：{count}/{Ncount}");
                    Defeated();
                }
            }
            EndCallBack();
        }

        /// <summary>
        /// 战斗超时时的处理方法（用户自定义）
        /// </summary>
        /// <param name="actualTime">实际战斗时长（分钟）</param>
        private void OnCombatTimeout(double actualTime)
        {
            // 用户将在此处添加自己的代码
            log.Info($"战斗判定结束，提前退出，实际用时：{actualTime:F2}分钟，设定时长：{fakeTime}分钟");
            Fast.Click(25, 19, 54, 49);
            Sleep(1500);
            Fast.Click(700, 408, 782, 437);
            Sleep(1000);
        }

        /// <summary>
        /// 切换模式
        /// </summary>
        private void ToggleState()
        {
            if (Mode == "活动体力")
            {
                if (Mp.Filter("普通体力").FindAll())
                {
                    log.Warn("当前为普通体力模式，切换模式为：活动体力");
                    Fast.Click(1241, 671, 1265, 692);
                }
            }
            else
            {
                if (Mp.Filter("活动体力").FindAll())
                {
                    log.Warn("当前为活动体力模式，切换模式为：普通体力");
                    Fast.Click(1241, 671, 1265, 692);
                }
            }
            Sleep(1000);
        }
    }
}