﻿<Window x:Class="DanDing1.Views.Windows.GameNameCustomWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        mc:Ignorable="d"
        Title="自定义多开游戏名称"
        Height="390"
        Width="300"
        WindowStartupLocation="CenterScreen"
        d:DataContext="{d:DesignInstance local:GameNameCustomWindow,
        IsDesignTimeCreatable=True}"
        ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
        ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        ScrollViewer.CanContentScroll="False">

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <StackPanel>
            <ui:TextBlock Text="请为每个游戏设置自定义显示名称"
                          FontSize="16"
                          FontWeight="Bold" />
            <ui:TextBlock Text="保存设置后需重启才能生效！"
                          FontSize="14"
                          FontWeight="Bold"
                          Margin="0,5,0,10" />
        </StackPanel>

        <StackPanel Grid.Row="1"
                    Margin="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <ui:TextBlock Text="游戏1："
                              VerticalAlignment="Center"
                              Margin="0,0,10,0" />
                <ui:TextBox Grid.Column="1"
                            x:Name="Game1Name"
                            Height="30"
                            Padding="5" />
            </Grid>

            <Grid Margin="0 10 0 0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <ui:TextBlock Text="游戏2："
                              VerticalAlignment="Center"
                              Margin="0,0,10,0" />
                <ui:TextBox Grid.Column="1"
                            x:Name="Game2Name"
                            Height="30"
                            Padding="5" />
            </Grid>

            <Grid Margin="0 10 0 0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <ui:TextBlock Text="游戏3："
                              VerticalAlignment="Center"
                              Margin="0,0,10,0" />
                <ui:TextBox Grid.Column="1"
                            x:Name="Game3Name"
                            Height="30"
                            Padding="5" />
            </Grid>

            <Grid Margin="0 10 0 0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <ui:TextBlock Text="游戏4："
                              VerticalAlignment="Center"
                              Margin="0,0,10,0" />
                <ui:TextBox Grid.Column="1"
                            x:Name="Game4Name"
                            Height="30"
                            Padding="5" />
            </Grid>

            <Grid Margin="0 15 0 0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <ui:TextBlock Text="启动时展示多开游戏个数："
                              VerticalAlignment="Center"
                              Margin="0,0,5,0" />
                <ComboBox Grid.Column="1"
                          x:Name="DefaultGameCount"
                          Height="30"
                          Padding="5">
                    <ComboBoxItem Content="1" />
                    <ComboBoxItem Content="2" />
                    <ComboBoxItem Content="3" />
                    <ComboBoxItem Content="4" />
                </ComboBox>
            </Grid>

            <StackPanel Orientation="Horizontal"
                        HorizontalAlignment="Right"
                        Margin="0,20,0,0">
                <ui:Button Content="保存"
                           Width="80"
                           Height="30"
                           Margin="0,0,10,0"
                           Click="SaveButton_Click" />
                <ui:Button Content="取消"
                           Width="80"
                           Height="30"
                           Click="CancelButton_Click" />
            </StackPanel>
        </StackPanel>
    </Grid>
</Window>