﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Model
{
    /// <summary>
    /// 游戏执行中设置
    /// </summary>
    public class GameSettingsModel
    {
        private int adbPort;

        /// <summary>
        /// ADB端口
        /// </summary>
        public int AdbPort
        {
            get { return adbPort; }
            set { adbPort = value; }
        }

        private bool xuanShang = false;

        /// <summary>
        /// 悬赏状态
        /// </summary>
        public bool XuanShang
        {
            get { return xuanShang; }
            set { xuanShang = value; }
        }

        private string courtyardskin = "默认";

        /// <summary>
        /// 庭院皮肤
        /// </summary>
        public string CourtyardSkin
        {
            get { return courtyardskin; }
            set { courtyardskin = value; }
        }

        private int loopCount = 1;

        /// <summary>
        /// 任务循环次数
        /// </summary>
        public int LoopCount
        {
            get { return loopCount; }
            set { loopCount = value; }
        }

        private string recordQuality;

        /// <summary>
        /// 录制质量
        /// </summary>
        public string RecordQuality
        {
            get { return recordQuality; }
            set { recordQuality = value; }
        }

        private bool isRecord;

        /// <summary>
        /// 录制本次任务
        /// </summary>
        public bool IsRecord
        {
            get { return isRecord; }
            set { isRecord = value; }
        }

        private bool isTifu;

        /// <summary>
        /// 是否需要单独适配体服开始执行脚本
        /// </summary>
        public bool IsTifu
        {
            get { return isTifu; }
            set { isTifu = value; }
        }

        private bool endCloseGame;

        /// <summary>
        /// 任务结束后关闭模拟器
        /// </summary>
        public bool EndCloseGame
        {
            get { return endCloseGame; }
            set { endCloseGame = value; }
        }

        private bool speedSwitch;

        /// <summary>
        /// 变速开关
        /// </summary>
        public bool SpeedSwitch
        {
            get { return speedSwitch; }
            set { speedSwitch = value; }
        }

        private bool isStartYYSToCourtyard = false;

        /// <summary>
        /// 是否需要启动到庭院界面
        /// </summary>
        public bool IsStartYYSToCourtyard
        {
            get { return isStartYYSToCourtyard; }
            set { isStartYYSToCourtyard = value; }
        }
    }
}