using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using XHelper;
using XHelper.Models;
using static XHelper.Models.Response_MySharesData;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// MySharesWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MySharesWindow : Window
    {
        // 分页相关参数
        private int _currentPage = 1;
        private int _perPage = 10;
        private int _totalPages = 1;
        private string _currentStatus = "";
        private string _currentSortBy = "create_time";
        private string _currentOrder = "desc";

        // 数据集合
        private ObservableCollection<_MyShareItem> _sharesItems;

        public MySharesWindow()
        {
            InitializeComponent();
            
            // 初始化数据集合
            _sharesItems = new ObservableCollection<_MyShareItem>();
            SharesDataGrid.ItemsSource = _sharesItems;
            
            // 检查用户是否已登录
            if (!GlobalData.Instance.appConfig.IsLogin)
            {
                MessageBox.Show("请先登录后再使用此功能", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                Close();
                return;
            }
            
            // 检查是否为试用账号
            if (GlobalData.Instance.appConfig.IsFree)
            {
                MessageBox.Show("试用账号无法使用配置共享功能", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                Close();
                return;
            }
            
            // 加载数据
            LoadData();
        }

        /// <summary>
        /// 加载分享配置数据
        /// </summary>
        private async void LoadData()
        {
            try
            {
                // 显示加载中
                LoadingGrid.Visibility = Visibility.Visible;
                NoDataGrid.Visibility = Visibility.Collapsed;
                SharesDataGrid.Visibility = Visibility.Collapsed;
                
                // 禁用分页按钮
                UpdatePagingButtons(false);
                
                // 调用API获取数据
                var configService = GlobalData.Instance.appConfig.dNet.Config;
                var result = await configService.GetMySharesAsync(
                    _currentPage, 
                    _perPage, 
                    string.IsNullOrEmpty(_currentStatus) ? null : _currentStatus,
                    _currentSortBy,
                    _currentOrder);
                
                // 清空当前列表
                _sharesItems.Clear();
                
                // 检查结果
                if (result == null || !result.IsSuccess || result.Data == null)
                {
                    ShowErrorMessage(result?.Message ?? "获取数据失败", "获取失败");
                    NoDataGrid.Visibility = Visibility.Visible;
                    return;
                }
                
                // 更新分页信息
                _totalPages = result.Data.Pages;
                UpdatePageInfo();
                
                // 填充数据
                foreach (var item in result.Data.Items)
                {
                    _sharesItems.Add(item);
                }
                
                // 根据是否有数据显示相应的UI
                if (_sharesItems.Count > 0)
                {
                    SharesDataGrid.Visibility = Visibility.Visible;
                    NoDataGrid.Visibility = Visibility.Collapsed;
                }
                else
                {
                    SharesDataGrid.Visibility = Visibility.Collapsed;
                    NoDataGrid.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"加载数据时发生错误: {ex.Message}", "错误");
                XLogger.Error($"获取分享配置列表异常: {ex.Message}");
                NoDataGrid.Visibility = Visibility.Visible;
            }
            finally
            {
                // 隐藏加载中
                LoadingGrid.Visibility = Visibility.Collapsed;
                
                // 更新分页按钮状态
                UpdatePagingButtons(true);
            }
        }
        
        /// <summary>
        /// 更新分页信息文本
        /// </summary>
        private void UpdatePageInfo()
        {
            PageInfoText.Text = $"第{_currentPage}页/共{_totalPages}页";
        }
        
        /// <summary>
        /// 更新分页按钮状态
        /// </summary>
        private void UpdatePagingButtons(bool enabled)
        {
            PrevButton.IsEnabled = enabled && _currentPage > 1;
            NextButton.IsEnabled = enabled && _currentPage < _totalPages;
        }
        
        /// <summary>
        /// 状态筛选改变事件
        /// </summary>
        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (!IsLoaded) return;
            
            var selectedItem = StatusFilterComboBox.SelectedItem as ComboBoxItem;
            if (selectedItem != null)
            {
                _currentStatus = selectedItem.Tag?.ToString() ?? "";
                _currentPage = 1; // 重置为第一页
                LoadData();
            }
        }
        
        /// <summary>
        /// 排序字段改变事件
        /// </summary>
        private void SortByComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (!IsLoaded) return;
            
            var selectedItem = SortByComboBox.SelectedItem as ComboBoxItem;
            if (selectedItem != null)
            {
                _currentSortBy = selectedItem.Tag?.ToString() ?? "create_time";
                LoadData();
            }
        }
        
        /// <summary>
        /// 排序方向改变事件
        /// </summary>
        private void OrderComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (!IsLoaded) return;
            
            var selectedItem = OrderComboBox.SelectedItem as ComboBoxItem;
            if (selectedItem != null)
            {
                _currentOrder = selectedItem.Tag?.ToString() ?? "desc";
                LoadData();
            }
        }
        
        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
        }
        
        /// <summary>
        /// 上一页按钮点击事件
        /// </summary>
        private void PrevButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                LoadData();
            }
        }
        
        /// <summary>
        /// 下一页按钮点击事件
        /// </summary>
        private void NextButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage < _totalPages)
            {
                _currentPage++;
                LoadData();
            }
        }
        
        /// <summary>
        /// 复制共享码按钮点击事件
        /// </summary>
        private void CopyCodeButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button != null && button.Tag is string shareCode)
            {
                try
                {
                    Clipboard.SetText(shareCode);
                    MessageBox.Show($"共享码 {shareCode} 已复制到剪贴板", "复制成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception clipboardEx)
                {
                    XLogger.Warn($"复制到剪贴板失败: {clipboardEx.Message}");
                    // 所有重试都失败，显示文本分享窗口让用户手动复制
                    var textShareWindow = new TextShareWindow(shareCode, shareCode);
                    textShareWindow.ShowDialog();
                    return; // 直接返回，不显示成功消息
                }
            }
        }
        
        /// <summary>
        /// 设为过期按钮点击事件
        /// </summary>
        private async void ExpireNowButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button != null && button.Tag is string shareCode)
            {
                var result = MessageBox.Show(
                    $"确定要将共享码 {shareCode} 的配置设置为过期吗？\n此操作不可撤销。",
                    "确认操作",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);
                
                if (result != MessageBoxResult.Yes)
                    return;
                
                try
                {
                    // 显示等待光标
                    var cursor = Mouse.OverrideCursor;
                    Mouse.OverrideCursor = Cursors.Wait;
                    
                    // 调用API设置过期
                    var configService = GlobalData.Instance.appConfig.dNet.Config;
                    var apiResult = await configService.UpdateShareStatusAsync(shareCode, "expire_now");
                    
                    // 恢复光标
                    Mouse.OverrideCursor = cursor;
                    
                    // 检查结果
                    if (apiResult == null || !apiResult.IsSuccess)
                    {
                        ShowErrorMessage(apiResult?.Message ?? "设置失败", "操作失败");
                        return;
                    }
                    
                    // 显示成功消息并刷新数据
                    MessageBox.Show("配置已成功设置为过期", "操作成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    LoadData();
                }
                catch (Exception ex)
                {
                    ShowErrorMessage($"设置过期时发生错误: {ex.Message}", "错误");
                    XLogger.Error($"设置配置过期异常: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button != null && button.Tag is string shareCode)
            {
                var result = MessageBox.Show(
                    $"确定要删除共享码 {shareCode} 的配置吗？\n此操作不可撤销。",
                    "确认删除",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);
                
                if (result != MessageBoxResult.Yes)
                    return;
                
                try
                {
                    // 显示等待光标
                    var cursor = Mouse.OverrideCursor;
                    Mouse.OverrideCursor = Cursors.Wait;
                    
                    // 调用API删除配置
                    var configService = GlobalData.Instance.appConfig.dNet.Config;
                    var apiResult = await configService.UpdateShareStatusAsync(shareCode, "delete");
                    
                    // 恢复光标
                    Mouse.OverrideCursor = cursor;
                    
                    // 检查结果
                    if (apiResult == null || !apiResult.IsSuccess)
                    {
                        ShowErrorMessage(apiResult?.Message ?? "删除失败", "操作失败");
                        return;
                    }
                    
                    // 显示成功消息并刷新数据
                    MessageBox.Show("配置已成功删除", "操作成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    LoadData();
                }
                catch (Exception ex)
                {
                    ShowErrorMessage($"删除配置时发生错误: {ex.Message}", "错误");
                    XLogger.Error($"删除配置异常: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 显示错误消息
        /// </summary>
        private void ShowErrorMessage(string message, string title)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
} 