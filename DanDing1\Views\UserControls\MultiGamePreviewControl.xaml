<UserControl x:Class="DanDing1.Views.UserControls.MultiGamePreviewControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:DanDing1.Views.UserControls"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
             ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             ScrollViewer.CanContentScroll="False"
             mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Grid Grid.Row="0" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <TextBlock Text="多游戏预览"
                       FontSize="16"
                       FontWeight="SemiBold" />

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <ui:Button x:Name="SyncGameListButton"
                           Content="同步右侧游戏列表"
                           Appearance="Secondary"
                           Margin="0,0,8,0"
                           Click="SyncGameListButton_Click" />
                           
                <ui:Button x:Name="SyncAllButton"
                           Content="同步全部预览"
                           Appearance="Secondary"
                           IsEnabled="False"
                           Click="SyncAllButton_Click" />
            </StackPanel>
        </Grid>

        <!-- 大小控制栏 -->
        <Grid Grid.Row="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" 
                       Text="预览大小:" 
                       VerticalAlignment="Center" 
                       Margin="0,0,10,0" />
            
            <Slider x:Name="SizeSlider" 
                    Grid.Column="1"
                    Minimum="30" 
                    Maximum="100" 
                    Value="75" 
                    TickFrequency="5"
                    TickPlacement="BottomRight"
                    IsSnapToTickEnabled="True"
                    ValueChanged="SizeSlider_ValueChanged" />
            
            <TextBlock Grid.Column="2" 
                       Text="{Binding ElementName=SizeSlider, Path=Value, StringFormat={}{0:0}%}" 
                       VerticalAlignment="Center" 
                       Margin="10,0,0,0" 
                       MinWidth="40" />
                       
            <StackPanel Grid.Column="3" Orientation="Horizontal" Margin="15,0,0,0">
                <TextBlock Text="布局模式:" VerticalAlignment="Center" Margin="0,0,10,0" />
                <ui:ToggleSwitch x:Name="LayoutModeToggle" 
                                 OnContent="水平流" 
                                 OffContent="垂直流"
                                 Checked="LayoutModeToggle_Changed"
                                 Unchecked="LayoutModeToggle_Changed" />
            </StackPanel>
        </Grid>

        <!-- 预览区域 - 使用WrapPanel自动排列多个预览 -->
        <ScrollViewer Grid.Row="2" HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto" 
                      ScrollChanged="PreviewScrollViewer_ScrollChanged" x:Name="PreviewScrollViewer">
            <WrapPanel x:Name="PreviewPanel" Orientation="Horizontal" />
        </ScrollViewer>
    </Grid>
</UserControl>