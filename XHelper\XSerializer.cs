﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace XHelper
{
    /// <summary>
    /// 序列化工具类
    /// </summary>
    public static class XSerializer
    {
        // 默认序列化选项，支持字段和构造函数
        private static readonly JsonSerializerOptions DefaultOptions = new()
        {
            IncludeFields = true,
            PropertyNameCaseInsensitive = true,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            WriteIndented = true,
            AllowTrailingCommas = true,
            ReadCommentHandling = JsonCommentHandling.Skip,
            NumberHandling = JsonNumberHandling.AllowReadingFromString,
            PropertyNamingPolicy = null, // 保持原有属性名
            Converters = 
            {
                new JsonStringEnumConverter()
            }
        };

        /// <summary>
        /// 反序列化 文件到类
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public static T DeserializeJsonFileToObject<T>(string filePath, bool encrypt = false)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                throw new ArgumentNullException("File path cannot be null or empty.");
            }

            T? deserializedObject;

            try
            {
                // 从文件读取JSON字符串
                string jsonString = File.ReadAllText(filePath);
                if (encrypt) jsonString = new XJsonEncryptor().Decrypt(jsonString);

                // 尝试使用System.Text.Json反序列化
                try
                {
                    deserializedObject = JsonSerializer.Deserialize<T>(jsonString, DefaultOptions);
                }
                catch (JsonException jsonEx)
                {
                    XLogger.Error($"使用System.Text.Json反序列化失败: {jsonEx.Message}, 尝试使用Newtonsoft.Json");
                    
                    // 如果System.Text.Json失败，尝试使用Newtonsoft.Json
                    try
                    {
                        deserializedObject = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(jsonString);
                    }
                    catch (Exception newtonsoftEx)
                    {
                        XLogger.Error($"使用Newtonsoft.Json反序列化也失败: {newtonsoftEx.Message}");
                        throw;
                    }
                }
            }
            catch (Exception e)
            {
                XLogger.Error("反序列化失败，程序可能受损：" + e.Message);
                throw;
            }

            return deserializedObject ?? throw new Exception("反序列化失败，程序可能受损！");
        }

        /// <summary>
        /// 反序列化 文本到类
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public static T DeserializeJsonTxtToObject<T>(string txt, bool encrypt = false)
        {
            T? deserializedObject;
            try
            {
                // 从文件读取JSON字符串
                string jsonString = txt;
                if (encrypt) jsonString = new XJsonEncryptor().Decrypt(jsonString);

                // 尝试使用System.Text.Json反序列化
                try
                {
                    deserializedObject = JsonSerializer.Deserialize<T>(jsonString, DefaultOptions);
                }
                catch (JsonException jsonEx)
                {
                    XLogger.Error($"使用System.Text.Json反序列化失败: {jsonEx.Message}, 尝试使用Newtonsoft.Json");
                    
                    // 如果System.Text.Json失败，尝试使用Newtonsoft.Json
                    try
                    {
                        deserializedObject = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(jsonString);
                    }
                    catch (Exception newtonsoftEx)
                    {
                        XLogger.Error($"使用Newtonsoft.Json反序列化也失败: {newtonsoftEx.Message}");
                        throw;
                    }
                }
            }
            catch (Exception e)
            {
                XLogger.Error("反序列化失败，程序可能受损：" + e.Message);
                throw;
            }

            return deserializedObject ?? throw new Exception("反序列化失败，程序可能受损！");
        }


        /// <summary>
        /// 序列化 类到文件
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="filePath"></param>
        public static void SerializeObjectToJsonFile(object obj, string filePath, bool encrypt = false)
        {
            ArgumentNullException.ThrowIfNull(obj);
            try
            {
                // 确保目录存在
                string? directoryPath = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
                {
                    try
                    {
                        Directory.CreateDirectory(directoryPath);
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"创建目录失败: {directoryPath}, 错误: {ex.Message}");
                        throw;
                    }
                }

                // 序列化对象为JSON字符串
                string jsonString = JsonSerializer.Serialize(obj, DefaultOptions);
                if (encrypt)
                {
                    XPath.CheckPathIsExist(filePath);
                    //备份
                    File.WriteAllText(filePath + ".orig", jsonString);
                    jsonString = new XJsonEncryptor().Encrypt(jsonString);
                }
                // 将JSON字符串写入文件
                File.WriteAllText(filePath, jsonString);
            }
            catch (JsonException e)
            {
                XLogger.Error("序列化失败，程序可能受损: " + e.Message);
                throw;
            }
        }

    }
}
