﻿using DamoControlKit.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine
{
    /// <summary>
    /// 任务结束的回调函数
    /// </summary>
    public delegate void TaskEnded_CallBack();

    /// <summary>
    /// 任务结束回调
    /// </summary>
    /// <param name="recordData"></param>
    public delegate void TaskEnded_CallBackData(RecordData recordData);

    /// <summary>
    /// 任务用户通知信息
    /// </summary>
    public delegate void TaskUserNotificationMessage(Dictionary<string, string> messages);

    /// <summary>
    /// 御魂满了的通知委托
    /// </summary>
    /// <returns></returns>
    public delegate bool PushYuHunFull();
}