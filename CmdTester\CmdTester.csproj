﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x86</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="YoloSharp" Version="6.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DamoControlKit\DamoControlKit.csproj" />
    <ProjectReference Include="..\ScriptEngine\ScriptEngine.csproj" />
    <ProjectReference Include="..\XHelper\XHelper.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="OcrLiteLib">
      <HintPath>..\lib\OcrLiteLib.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
