using System.Net;
using System.Net.Security;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;
using System.Net.NetworkInformation;

namespace XHelper.DanDingNet
{
    public class NetworkConfig
    {
        public static void ConfigureGlobalNetworkSettings()
        {
            // 配置全局 TLS 设置
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;
            ServicePointManager.DefaultConnectionLimit = 20;
            ServicePointManager.Expect100Continue = false;
            ServicePointManager.DnsRefreshTimeout = 0;
            ServicePointManager.UseNagleAlgorithm = false;

            // 配置全局证书验证
            ServicePointManager.ServerCertificateValidationCallback = ValidateServerCertificate;
        }

        public static HttpClientHandler CreateClientHandler()
        {
            return new HttpClientHandler
            {
                AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
                ServerCertificateCustomValidationCallback = ValidateServerCertificate,
                SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls13,
                MaxConnectionsPerServer = 10,
                UseProxy = true,
                Proxy = HttpClient.DefaultProxy,
                AllowAutoRedirect = true,
                MaxAutomaticRedirections = 5,
                UseCookies = true,
                CookieContainer = new CookieContainer()
            };
        }

        private static bool ValidateServerCertificate(
            object sender,
            X509Certificate? certificate,
            X509Chain? chain,
            SslPolicyErrors sslPolicyErrors)
        {
            if (sslPolicyErrors == SslPolicyErrors.None)
                return true;

            // 记录证书验证错误
            if (DanDingNet.EnableDebugLog) XLogger.Debug($"SSL证书验证错误: {sslPolicyErrors}");
            if (certificate != null)
            {
                if (DanDingNet.EnableDebugLog) XLogger.Debug($"证书信息: 主题={certificate.Subject}, 颁发者={certificate.Issuer}, 过期时间={certificate.GetExpirationDateString()}");
            }

            // 开发环境允许自签名证书
#if DEBUG
            return true;
#else
                // 生产环境只允许特定的错误
                return sslPolicyErrors == SslPolicyErrors.RemoteCertificateNameMismatch;
#endif
        }

        public static async Task<bool> ValidateNetworkConnectivity(string host, int timeoutSeconds = 10)
        {
            try
            {
                var uri = new Uri(host);
                var hostName = uri.Host;

                // 记录开始检查
                if (DanDingNet.EnableDebugLog) XLogger.Debug($"开始网络连接检查: {host}");

                // 检查是否是IP地址
                bool isIpAddress = IPAddress.TryParse(hostName, out IPAddress? ipAddress);

                if (!isIpAddress)
                {
                    // 如果不是IP地址，进行DNS检查
                    try
                    {
                        if (DanDingNet.EnableDebugLog) XLogger.Debug($"正在进行DNS解析: {host}");
                        var hostEntry = await Dns.GetHostEntryAsync(hostName);
                        if (!hostEntry.AddressList.Any())
                        {
                            XLogger.Error($"DNS解析失败: 未能获取地址");
                            return false;
                        }
                        if (DanDingNet.EnableDebugLog) XLogger.Debug($"DNS解析成功");
                        ipAddress = hostEntry.AddressList[0];
                    }
                    catch (Exception dnsEx)
                    {
                        XLogger.Error($"DNS检查异常: {dnsEx.Message}");
                        // 继续执行HTTP检查，因为DNS解析失败可能是临时的
                    }
                }

                // PING检查
                if (ipAddress != null)
                {
                    if (DanDingNet.EnableDebugLog) XLogger.Debug($"正在PING主机: {host}");
                    if (await PingHostAsync(ipAddress.ToString(), host))
                    {
                        if (DanDingNet.EnableDebugLog) XLogger.Debug($"PING成功: {host}");
                    }
                    else
                    {
                        XLogger.Warn($"PING失败: {host}，但将继续尝试HTTP连接");
                        // 注意：这里不直接返回false，因为有些服务器可能禁用了PING
                    }
                }

                // HTTP连接检查
                try
                {
                    if (DanDingNet.EnableDebugLog) XLogger.Debug($"正在进行HTTP连接测试: {host}");
                    using var handler = CreateClientHandler();
                    using var client = new HttpClient(handler) { Timeout = TimeSpan.FromSeconds(timeoutSeconds) };

                    // 添加User-Agent避免被某些服务器拒绝
                    client.DefaultRequestHeaders.Add("User-Agent", "DanDing-Network-Check/1.0");

                    var response = await client.GetAsync(host);
                    if (DanDingNet.EnableDebugLog) XLogger.Debug($"HTTP连接测试结果: {response.StatusCode}");

                    // 修改判断逻辑：404也是正常的响应
                    return response.StatusCode == HttpStatusCode.OK ||
                           response.StatusCode == HttpStatusCode.NotFound;
                }
                catch (HttpRequestException httpEx)
                {
                    XLogger.Error($"HTTP连接失败: {httpEx.Message}");
                    if (httpEx.InnerException != null)
                    {
                        XLogger.Error($"详细错误: {httpEx.InnerException.Message}");
                    }
                    return false;
                }
                catch (TaskCanceledException)
                {
                    XLogger.Error($"HTTP连接超时: {timeoutSeconds}秒内未收到响应");
                    return false;
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"网络连接检查过程中发生未预期的错误: {ex.Message}");
                if (ex.InnerException != null)
                {
                    XLogger.Error($"内部错误: {ex.InnerException.Message}");
                }
                return false;
            }
        }

        private static async Task<bool> PingHostAsync(string host, string serverUrl)
        {
            try
            {
                using var ping = new Ping();
                var reply = await ping.SendPingAsync(host, 5000);
                if (DanDingNet.EnableDebugLog) XLogger.Debug($"PING结果: {reply.Status}, 往返时间: {reply.RoundtripTime}ms");
                return reply.Status == IPStatus.Success;
            }
            catch (Exception ex)
            {
                XLogger.Warn($"PING过程发生错误: {ex.Message}");
                return false;
            }
        }
    }
}