﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<targets async="true">
		<!--屏幕打印消息-->
		<target name="console" xsi:type="ColoredConsole"
						layout="${date:format=HH\:mm\:ss}> ${message}"/>

		<!--vs输出窗口-->
		<target name="vsdebugger" xsi:type="Debugger"
					   layout="[${date:format=HH\:mm\:ss}][${level:padding=5:uppercase=true}] ${message}"/>

		<!--保存debug至文件-->
		<target name="debugger" xsi:type="File" maxArchiveFiles="30"
				encoding="utf-8"
						fileName="${basedir}/Logs/${shortdate}/debug.txt"
						layout="[${date:format=HH\:mm\:ss}][${level:padding=5:uppercase=true}] ${message}"/>

		<!--保存erorr至文件-->
		<target name="error_file" xsi:type="File" maxArchiveFiles="30"
				encoding="utf-8"
						fileName="${basedir}/Logs/${shortdate}/error.txt"
						layout="[${longdate}][${level:uppercase=true:padding=5}] ${message}  ${onexception:${exception:format=message} ${newline} ${stacktrace}" />

		<!--保存warn至文件-->
		<target name="warn_file" xsi:type="File" maxArchiveFiles="30"
				encoding="utf-8"
						fileName="${basedir}/Logs/${shortdate}/warn.txt"
						layout="[${longdate}][${level:uppercase=true:padding=5}] ${message}  ${onexception:${exception:format=message} ${newline} ${stacktrace}" />


		<!--保存info至文件-->
		<target name="info_file" xsi:type="File" maxArchiveFiles="30"
				encoding="utf-8"
						fileName="${basedir}/Logs/${shortdate}/info.txt"
						layout="[${longdate}][${level:uppercase=true:padding=5}] ${message}  ${exception}" />

		<!--保存all至文件-->
		<target name="all_file" xsi:type="File" maxArchiveFiles="30"
				encoding="utf-8"
						fileName="${basedir}/Logs/${shortdate}/log_all.txt"
						layout="[${longdate}][${level:uppercase=true:padding=5}] ${message}  ${exception}" />
	</targets>
	<rules>
		<logger name="*" writeTo="console" />
		<logger name="*" writeTo="all_file" />
		<logger name="*" minlevel="Trace" writeTo="vsdebugger"/>
		<logger name="*" levels="Debug" writeTo="debugger" />
		<logger name="*" levels="Error" writeTo="error_file" />
		<logger name="*" levels="Warn" writeTo="warn_file" />
		<logger name="*" levels="Info" writeTo="info_file" />
	</rules>
</nlog>