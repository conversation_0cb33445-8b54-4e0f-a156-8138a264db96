﻿using DamoControlKit;
using DamoControlKit.Model;
using ScriptEngine.Factorys;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using XHelper;

namespace ScriptEngine.Tasks
{
    internal class TanSuo : BaseTask
    {
        private Dictionary<string, Pixel> Colors = new Dictionary<string, Pixel>()
        {
            {"探索开始",new Pixel(945,526,"f3b25e-101010",0.96) },
            {"退出探索",new Pixel(737,400,"f3b25e-101010",0.93) },
            {"轮换关闭",new Pixel(130,671,"1e245c-101010",0.98) },
        };

        private int count = 0;

        /// <summary>
        /// 计数模式
        /// True：仅已Boss计数
        /// False：Boss+小怪计数
        /// </summary>
        private bool Counting_Mode = false;

        private bool DaGuoBossLe = false;
        private int FindNowCount = 0;

        private bool isBoss = false;

        /// <summary>
        /// 任务次数
        /// </summary>
        private int Ncount = 0;

        private MemPics pics_Boss;

        private int Slide_Count = 0;

        /// <summary>
        /// 突破卷数量
        /// </summary>
        private int tupo_Count = -1;

        /// <summary>
        /// 战斗种类
        /// damo jingyan jinbi 空
        /// </summary>
        private string Yolo_ObjCombatStr = "";

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "探索");
            //初始化组件
            foreach (var item in Colors)
                item.Value.SetXsoft(dm);
            pics_Boss = Mp.Filter("找怪Boss");
            XYoloV8.Reset();
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Ncount = GetConfig.Count;
            Yolo_ObjCombatStr = GetConfig.Others.TryGetValue("CombatStr", out var yolo_objcombatstr) ? yolo_objcombatstr : "";
            Counting_Mode = GetConfig.Others.TryGetValue("Counting_Mode", out var counting_mode) ? counting_mode == "True" : false;

            //场景判断
            if (Mp.Filter("探索界面").FindAll())
            {
                log.Warn("当前场景在探索任务主界面，开始直接识别怪物并战斗！");
                //直接战斗
                main(true);
                return;
            }
            //先去探索
            if (!Scene.TO.TanSuo())
            {
                log.Warn("探索任务无法继续，当前游戏所在场景未知，请调整到庭院或探索主界面开始脚本！");
                return;
            }

            tupo_Count = Fast.Scence.TanSuo_GetTuPoCount();//获取突破卷数量
            log.Debug("本地Ocr识别突破卷结果：" + tupo_Count);
            if (tupo_Count == 30)
                Tmp.Do_Tupo(); //执行临时执行突破
            main();
            UserNotificationMessage = $"共战斗{count}/{Ncount}次.";
            if (Db.Data.Get("AutoBuff") is bool autoBuff && autoBuff)
                RunTaskFactory.Run<BuffTask>(Db, Dm, Ct, 1, "自动Buff", new() { { "所有", "0" } });
        }

        private void Check_GouLiang()
        {
            bool retry = false;
            log.Debug("检查自动轮换是否开启..");
        Re:
            if (Colors["轮换关闭"].Find(null) && retry == false)
            {
                log.Info("检测到狗粮轮换处于关闭状态,尝试开启..");
                Fast.Click(130, 671);
                Sleep(1000);
                retry = true;
                goto Re;
            }
            if (Colors["轮换关闭"].Find(null) && retry)
            {
                log.Info("开始添加轮换狗粮..");
                Fast.Click(65, 675);
                Sleep(1000);
                log.Info("点击一键清空并选中候补出战..");
                Fast.Click(1027, 430);
                Sleep(1000);
                Fast.Click(809, 281);
                Sleep(1000);
                log.Info("切换到素材..");
                Fast.Click(63, 649);
                Sleep(1000);
                Fast.Click(68, 308);
                Sleep(1000);
                for (int i = 0; i < 6; i++)
                {
                    log.Info($"开始添加狗粮{i + 1}..");
                    Fast.LongClick(182, 584, 1500);
                    Sleep(1000);
                    if (i != 5)
                        Fast.Click(182, 584);
                    Sleep(1000);
                }
                log.Info("点击确定..");
                Fast.Click(1159, 442);
                Sleep(1000);
                retry = false;
                goto Re;
            }
        }

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            //点击开始
            log.Info("战斗开始");
            var pics = Mp.Filter("战斗_")
                .Add(Mp.Filter("活动"));
            bool ret_bol = false;
            bool isbreak = false;
            while (!isbreak)
            {
                //场景判断
                if (Mp.Filter("探索界面").FindAll())
                {
                    log.Warn("当前场景在探索任务主界面，退出战斗！");
                    ret_bol = true;
                    isbreak = true;
                    break;
                }
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("胜利") || p.Name.Contains("达摩"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(120);
                }
                if (p.Name.Contains("失败"))
                {
                    ret_bol = false;
                    isbreak = true;
                }
            }
            if (ret_bol)
                Combat_End();

            return ret_bol;
        }

        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("战斗_")
                .Add(Mp.Filter("活动"));
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(500);
                if (Mp.Filter("探索界面").FindAll())
                {
                    isbreak = true;
                    continue;
                }
                var p = pics.FindAllEa();
                if (p is null) continue;
                log.Info($"执行点击：{p._Name}");
                p.Click();
            }
        }

        /// <summary>
        /// 退出任务到探索
        /// </summary>
        private void Exit()
        {
        Re:
            if (Mp.Filter("探索界面").FindAll())
            {
                Fast.Click(35, 48, 72, 80);
                while (!Colors["退出探索"].Await(null, 3000))//等待探索退出按钮
                    Fast.Click(35, 48, 72, 80);
                Fast.Click(728, 388, 826, 420);//点击确定
                Sleep(1000);
                if (!Colors["探索开始"].Await(null, 3000))//等待探索开始按钮
                    goto Re;//点击K28
                Fast.Click(1023, 128, 1068, 164);//点击确定
                DaGuoBossLe = false;
                Sleep(500);
                return;
            }
            if (Colors["探索开始"].Find(null))
            {
                Fast.Click(1030, 134, 1063, 165);
                Sleep(1000);
                return;
            }
            if (Scene.NowScene == "探索")
            {
                Sleep(1000);
                return;
            }
        }

        /// <summary>
        /// 查找战斗对象
        /// </summary>
        /// <returns>True:进入了战斗, False:需要滑动</returns>
        private bool Find_CombatObj()
        {
            FindNowCount++;
            bool findOK = false;
            //先扫一下Boss
            if (pics_Boss.FindAll(out int x, out int y))
            {
                log.Info("找到Boss,准备战斗..");
                Operational.Click(x, y);
                isBoss = true;
                findOK = true;
                Sleep(200);
            }
            else//Ai找图
            {
                var boxs = Fast.YoloV8Det_All(YoloModels.Detect);
                if (boxs != null && Yolo_ObjCombatStr == "")
                    foreach (var box in boxs)
                    {
                        if (box.Name.ToString().Contains("combat"))
                        {
                            var point = new Position(box.Bounds.GetPosStr());
                            //判断是否为Boss
                            if (pics_Boss.FindAsPosition(point))
                            {
                                log.Info("找到Boss,准备战斗..");
                                isBoss = true;
                            }
                            else log.Info("找到小怪,准备战斗..");
                            Operational.Click(point.GetCenterPoint());
                            findOK = true;
                            Sleep(200);
                            break;
                        }
                    }
                else if (boxs != null && Yolo_ObjCombatStr != "")
                {
                    List<Position> combatLists = [];
                    boxs.ToList().ForEach(t =>
                    {
                        if (t.Name.ToString().Contains("combat"))
                            combatLists.Add(new(t.Bounds.GetPosStr()));
                    });

                    foreach (var box in boxs)
                    {
                        if (box.Name.ToString().Contains(Yolo_ObjCombatStr))
                        {
                            var pos = new Position(box.Bounds.GetPosStr());
                            //输出对象置信度
                            log.Debug(Yolo_ObjCombatStr + "目标置信度：" + box.Confidence);

                            //找到最近的战斗坐标
                            var combat_pos = Algorithm.FindClosestBoxAbove(pos, combatLists, 250);
                            if (combat_pos == null)
                                continue;

                            //判断是否为Boss
                            if (pics_Boss.FindAsPosition(combat_pos))
                            {
                                log.Info("找到Boss,准备战斗..");
                                isBoss = true;
                            }
                            else log.Info("找到小怪,准备战斗..");
                            Operational.Click(combat_pos.GetCenterPoint());
                            findOK = true;
                            Sleep(200);
                            break;
                        }
                    }
                }
            }

            if (FindNowCount >= 3)
            {
                FindNowCount = 0;
                if (!findOK) return false;
            }
            if (!findOK)//重新查找
                return Find_CombatObj();
            else if (Mp.Filter("探索界面").FindAll())//检查当前是否进入了战斗
                return Find_CombatObj();

            FindNowCount = 0;
            return true;
        }

        /// <summary>
        /// 探索进入主场景
        /// </summary>
        private void JoinMainSecen()
        {
        Re:
            Sleep(1000);
            if (Scene.NowScene == "探索")
                PickBaoXiang();
            //如果已经在探索开始界面就不用点击K28了
            if (!Colors["探索开始"].Find(null))
            {
                Fast.Click(1075, 503, 1224, 547);//点击K28
                Sleep(200);
            }
            Sleep(200);
            while (!Colors["探索开始"].Await(null, 5000))//等待探索开始按钮
            {
                Sleep(100);
                Fast.Click(1075, 503, 1224, 547);//点击K28
            }
            DaGuoBossLe = false;
            Sleep(100);
            Fast.Click(902, 523, 988, 555);//点击探索
            if (!WaitMain())//等待探索界面
            {
                //超时的补偿判断
                string nows = Scene.NowScene;
                if (nows != "探索" && !Scene.TO.TanSuo())
                    throw new Exception("未知的场景,探索任务无法继续！");
                log.Warn("重试进入探索主界面！(JoinMainSecen)");
                goto Re;
            }
            Sleep(500);
        }

        /// <summary>
        /// 从庭院进入探索主场景
        /// </summary>
        private void JoinMainSecen_Base()
        {
            Scene.TO.TanSuo();
            JoinMainSecen();
        }

        /// <summary>
        /// 主流程
        /// </summary>
        /// <param name="DongMoveScene"></param>
        private void main(bool DongMoveScene = false)
        {
            YYongYuShe(DongMoveScene);
        ReTry:
            if (!DongMoveScene)
            {
                DongMoveScene = true;
                JoinMainSecen();//进入主场景
            }

        ReStart:
            Sleep(50);
            if (count >= Ncount)
            {
                Exit();
                return;
            }
            if (!Mp.Filter("探索界面").Wait(5)) JoinMainSecen();//进入主场景
            if (!WaitMain()) goto ReTry;

            if (Db.PendingTimerTask) //执行定时任务
            {
                Db.PendingTimerTask = false;
                log.Info("暂停当前任务,执行定时任务,退出到探索..");
                Exit();
                Db?.TimerTask?.DoAllTask();
                Sleep(1000);
                throw new Exception("定时任务执行结束,重新执行当前的主任务..");
            }
            Tmp.Do_ClearYuHun(); //执行临时执行御魂

            Check_GouLiang(); //检查自动轮换是否开启

            if (!Find_CombatObj())
            {
                if (!Slide()) ReGotoMainSecen();
                goto ReStart;
            }
            if (Combat())
            {
                if (!Counting_Mode)
                {
                    count++;
                    log.Info_Green($"探索战斗胜利,战斗次数：{count}/{Ncount}");
                }
                else
                    log.Info_Green($"探索战斗胜利");
                //防封等待
                Anti.RandomDelay();
                if (Anti.ShouldTriggerRandomYysAuto())//判断是否需要穿插纸人
                {
                    Fast.Click(1215, 654, 1243, 684); //打开小纸人
                    Sleep(500);
                    int do_Count = Random.Shared.Next(1, 8); // 1-7次随机次数
                    if (Tmp.Do_YysAuto(do_Count))
                    {
                        if (!Counting_Mode) count += do_Count;
                        log.Info($"触发随机穿插纸人战斗结束..脚本继续接管..");
                        Anti.ResetRandomYysAuto();
                    }
                    else Sleep(1000);
                }
            }
            else
            {
                log.Warn($"探索战斗失败,请检查您的队伍配置是否正常！战斗次数：{count}/{Ncount}");
                Defeated();
            }
            if (isBoss)
            {
                if (Counting_Mode)
                {
                    count++;
                    log.Info_Green($"当前Boss战斗次数：{count}/{Ncount}");
                }
                isBoss = false;
                Sleep(500);
                if (count >= Ncount)
                    goto ReStart;
                ReGotoMainSecen();
                Sleep(400);
            }
            Sleep(50);
            goto ReStart;
        }

        /// <summary>
        /// 领取宝箱
        /// </summary>
        private void PickBaoXiang()
        {
            log.Debug("尝试查找宝箱并领取..");
            while (Mp.Filter("宝箱领取").FindAllAndClick())
            {
                Sleep(1500);
                Fast.Click(1157, 145);
                Sleep(1500);
            }

            // 等待返回探索主界面
            int waitCount = 0;
            while (!Scene.NowIsScene("探索") && waitCount < 5)
            {
                log.Debug("等待返回探索主界面...");
                Fast.Click(1157, 145);
                Sleep(1000);
                waitCount++;
            }
        }

        /// <summary>
        /// 重新进入探索主界面
        /// </summary>
        private void ReGotoMainSecen()
        {
        ReTry:
            Sleep(500);
            if (Mp.Filter("探索界面").FindAll())
            {
                log.Debug("探索任务主场景的二次判断！(是否还在探索界面)");
                Sleep(400);
                if (!Mp.Filter("探索界面").FindAll())
                {
                    log.Debug("探索任务主场景的二次判断：不在探索主界面");
                    goto ReTry;
                }
                Sleep(400);
                log.Debug("探索任务主场景的三次判断！(是否为探索界面)");
                if (Scene.NowIsScene("探索"))
                {
                    log.Debug("探索任务主场景的三次判断：当前在为探索界面");
                    if (Scene.NowScene == "探索")
                        PickBaoXiang();
                    goto ReTry;
                }

                log.Debug("点一次返回..1");
                Fast.Click(35, 48, 72, 80);
                Sleep(400);
                while (!Colors["退出探索"].Await(Dm, 3000))//等待探索退出按钮
                {
                    log.Debug("探索任务主场景的二次判断！(是否还在探索主界面)");
                    if (!Mp.Filter("探索界面").FindAll())
                    {
                        log.Debug("探索任务主场景的二次判断：不在探索主界面");
                        goto ReTry;
                    }
                    Sleep(100);
                    log.Debug("探索任务主场景的三次判断！(是否为探索界面)");
                    if (Scene.NowIsScene("探索"))
                    {
                        log.Debug("探索任务主场景的三次判断：当前在为探索界面");
                        if (Scene.NowScene == "探索")
                            PickBaoXiang();
                        goto ReTry;
                    }
                    Sleep(50);
                    log.Debug("重复点一次返回..2");
                    Fast.Click(35, 48, 72, 80);
                    Sleep(400);
                }

                while (Colors["退出探索"].Find(Dm))//点击退出探索的确定
                {
                    Sleep(400);
                    //场景判断
                    if (Scene.NowIsScene("探索"))
                    {
                        if (Scene.NowScene == "探索")
                            PickBaoXiang();
                        break;
                    }
                    log.Debug("点确认退出按钮一次..1");
                    Fast.Click(707, 392, 735, 421);
                    Sleep(400);
                }

                while (!Colors["探索开始"].Await(Dm, 3000))//等待探索开始按钮
                {
                    Sleep(50);
                    log.Debug("点k28副本..1");
                    Fast.Click(1075, 503, 1224, 547);//点击K28
                    Sleep(50);
                }

                DaGuoBossLe = false;
                Sleep(100);
                tupo_Count = Fast.Scence.K28_GetTuPoCount();//获取突破卷数量
                log.Info("目前突破卷数量：" + tupo_Count);
                if (tupo_Count == 30)
                {
                    while (Colors["探索开始"].Find(Dm))
                    {
                        Sleep(400);
                        Fast.Click(1026, 128, 1071, 167);
                        Sleep(400);
                    }
                    Tmp.Do_Tupo(); //执行临时执行突破
                    YYongYuShe(false);
                    return;
                }
                Fast.Click(902, 523, 988, 555);//点击探索
                Slide_Count = 0;
            }
            else if (Colors["探索开始"].Find(Dm))
            {
                log.Info("当前位于K28进入界面,直接点击探索按钮..");
                Fast.Click(902, 523, 988, 555);//点击探索
                Slide_Count = 0;
            }
            else if (Scene.NowIsScene("探索"))
                JoinMainSecen();
            else if (Scene.NowIsScene("庭院"))
                JoinMainSecen_Base();
            else
            {
                log.Warn("重新进入探索出现未知错误,正在重新尝试...");
                goto ReTry;
            }
        }

        /// <summary>
        ///滑动界面
        /// </summary>
        /// <returns>true:OK false:需要重进</returns>
        private bool Slide()
        {
            if (Slide_Count >= 2)
            {
                if (Yolo_ObjCombatStr == "" && !DaGuoBossLe)//如果没打过Boss 多等一会再扫描
                {
                    log.Info("要重进了,但是还没看到boss,多等一会再扫怪~");
                    DaGuoBossLe = true;
                    Sleep(1500);
                    return true;
                }
                Slide_Count = 0;
                return false;
            }
            Operational.Slide_Pos(new Position("1271,665,1289,691"), new Position("115,385,219,468"));
            Sleep(500);
            Slide_Count++;
            return true;
        }

        /// <summary>
        /// 等待探索战斗主界面
        /// </summary>
        private bool WaitMain()
        {
            int ReTry = 3;
            int count = 0;
            while (!Mp.Filter("探索界面").Wait())
            {
                if (count >= ReTry)
                {
                    log.Warn("移动至探索场景主界面超时！");
                    return false;
                }
                count++;
            }
            return true;
        }

        /// <summary>
        /// 应用预设
        /// </summary>
        private void YYongYuShe(bool DongMoveScene)
        {
            if (UserConfig_Preset != null)
            {
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入式神录,开始应用预设{UserConfig_Preset}");
                if (DongMoveScene)
                {
                    Fast.Click(794, 652, 829, 683);
                    Sleep(1500);
                }
                else
                {
                    Fast.Click(254, 640, 305, 687);
                    Sleep(1500);
                    Fast.Click(1217, 621, 1248, 654);
                    Sleep(1500);
                }
                Tmp.Do_Preset(preset);
                Sleep(500);
                log.Info($"退出突破界面,继续探索任务...");
                Fast.Click(1191, 117, 1229, 151);
                Sleep(1500);
            }
        }
    }
}