using System;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Interop;
using System.Windows.Media.Imaging;

namespace DanDing1.Helpers
{
    public class DwmPreviewHelper
    {
        [DllImport("dwmapi.dll")]
        private static extern int DwmRegisterThumbnail(IntPtr dest, IntPtr src, out IntPtr thumb);

        [DllImport("dwmapi.dll")]
        private static extern int DwmUpdateThumbnailProperties(IntPtr hThumb, ref DWM_THUMBNAIL_PROPERTIES props);

        [DllImport("dwmapi.dll")]
        private static extern int DwmUnregisterThumbnail(IntPtr thumb);

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll")]
        private static extern IntPtr GetParent(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool IsWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool IsIconic(IntPtr hWnd);

        [StructLayout(LayoutKind.Sequential)]
        private struct DWM_THUMBNAIL_PROPERTIES
        {
            public uint dwFlags;
            public RECT rcDestination;
            public RECT rcSource;
            public byte opacity;
            public bool fVisible;
            public bool fSourceClientAreaOnly;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        private const uint DWM_TNP_SOURCECLIENTAREAONLY = 0x00000010;
        private const uint DWM_TNP_VISIBLE = 0x00000008;
        private const uint DWM_TNP_OPACITY = 0x00000004;
        private const uint DWM_TNP_RECTDESTINATION = 0x00000001;
        private const uint DWM_TNP_RECTSOURCE = 0x00000002;

        private IntPtr _thumbnailHandle;
        private readonly Window _targetWindow;
        private IntPtr _sourceHandle;
        private readonly Border _previewBorder;
        private string _lastError = string.Empty;

        public string LastError => _lastError;

        public DwmPreviewHelper(Window targetWindow, IntPtr sourceHandle, Border previewBorder = null)
        {
            _targetWindow = targetWindow;
            _sourceHandle = sourceHandle;
            _previewBorder = previewBorder;
        }

        public bool StartPreview()
        {
            try
            {
                // 检查源窗口是否有效
                if (!IsWindow(_sourceHandle))
                {
                    _lastError = "源窗口句柄无效";
                    return false;
                }

                // 检查源窗口是否可见
                if (!IsWindowVisible(_sourceHandle) || IsIconic(_sourceHandle))
                {
                    _lastError = "源窗口不可见或已最小化";
                    return false;
                }

                // 获取窗口句柄
                var windowHandle = new WindowInteropHelper(_targetWindow).Handle;
                if (windowHandle == IntPtr.Zero)
                {
                    _lastError = "目标窗口句柄无效";
                    return false;
                }

                // 尝试获取父窗口，可能对某些游戏窗口有帮助
                IntPtr parentHandle = GetParent(_sourceHandle);
                if (parentHandle != IntPtr.Zero)
                {
                    _sourceHandle = parentHandle;
                }

                // 释放之前的缩略图
                StopPreview();

                // 注册缩略图
                int result = DwmRegisterThumbnail(windowHandle, _sourceHandle, out _thumbnailHandle);
                if (result != 0)
                {
                    _lastError = $"DwmRegisterThumbnail失败，错误码：{result}";
                    return false;
                }

                var props = new DWM_THUMBNAIL_PROPERTIES
                {
                    dwFlags = DWM_TNP_VISIBLE | DWM_TNP_OPACITY | DWM_TNP_RECTDESTINATION | DWM_TNP_RECTSOURCE,
                    fVisible = true,
                    opacity = 255
                };

                // 设置目标区域
                if (_previewBorder != null)
                {
                    // 获取当前窗口的PresentationSource用于DPI转换
                    var source = PresentationSource.FromVisual(_targetWindow);
                    if (source == null)
                    {
                        _lastError = "无法获取PresentationSource";
                        return false;
                    }

                    // 获取DPI缩放转换矩阵
                    var transformToDevice = source.CompositionTarget.TransformToDevice;

                    // 计算Border在窗口中的精确位置
                    Point borderPos = _previewBorder.TranslatePoint(new Point(0, 0), _targetWindow);

                    // 获取Border的实际大小
                    double width = _previewBorder.ActualWidth;
                    double height = _previewBorder.ActualHeight;

                    // 确保宽高不为零
                    if (width < 1) width = 1;
                    if (height < 1) height = 1;

                    // 转换位置和尺寸为设备像素 (考虑DPI缩放)
                    var devicePoint = transformToDevice.Transform(borderPos);
                    var deviceSize = transformToDevice.Transform(new Point(width, height));

                    // 使用相对于窗口客户区的坐标
                    props.rcDestination = new RECT
                    {
                        Left = (int)devicePoint.X,
                        Top = (int)devicePoint.Y,
                        Right = (int)(devicePoint.X + deviceSize.X),
                        Bottom = (int)(devicePoint.Y + deviceSize.Y)
                    };
                }
                else
                {
                    var source = PresentationSource.FromVisual(_targetWindow);
                    if (source == null)
                    {
                        _lastError = "无法获取PresentationSource";
                        return false;
                    }

                    var transform = source.CompositionTarget.TransformFromDevice;
                    var size = transform.Transform(new Point(_targetWindow.ActualWidth, _targetWindow.ActualHeight));
                    double width = size.X;
                    double height = size.Y;

                    props.rcDestination = new RECT
                    {
                        Left = 0,
                        Top = 0,
                        Right = (int)width,
                        Bottom = (int)height
                    };
                }

                // 获取源窗口尺寸
                if (!GetWindowRect(_sourceHandle, out RECT sourceRect))
                {
                    _lastError = "无法获取源窗口尺寸";
                    return false;
                }

                // 设置源窗口区域，即指定要捕获源窗口的哪个部分
                int sourceWidth = sourceRect.Right - sourceRect.Left;
                int sourceHeight = sourceRect.Bottom - sourceRect.Top;

                props.rcSource = new RECT
                {
                    Left = 0,
                    Top = 0,
                    Right = sourceWidth,
                    Bottom = sourceHeight
                };

                // 更新缩略图属性
                result = DwmUpdateThumbnailProperties(_thumbnailHandle, ref props);
                if (result != 0)
                {
                    _lastError = $"DwmUpdateThumbnailProperties失败，错误码：{result}";
                    DwmUnregisterThumbnail(_thumbnailHandle);
                    _thumbnailHandle = IntPtr.Zero;
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _lastError = $"预览初始化异常：{ex.Message}";
                return false;
            }
        }

        public void StopPreview()
        {
            if (_thumbnailHandle != IntPtr.Zero)
            {
                DwmUnregisterThumbnail(_thumbnailHandle);
                _thumbnailHandle = IntPtr.Zero;
            }
        }

        // 更新预览区域大小
        public bool UpdateSize(double width, double height)
        {
            if (_thumbnailHandle == IntPtr.Zero)
                return false;

            try
            {
                RECT destination;

                if (_previewBorder != null)
                {
                    // 强制刷新布局以确保位置和大小准确
                    _previewBorder.UpdateLayout();

                    // 获取当前窗口的PresentationSource用于DPI转换
                    var source = PresentationSource.FromVisual(_targetWindow);
                    if (source == null) return false;

                    // 获取DPI缩放转换矩阵
                    var transformToDevice = source.CompositionTarget.TransformToDevice;

                    // 获取Border在窗口中的精确位置（注意：这是关键步骤）
                    Point borderPos = _previewBorder.TranslatePoint(new Point(0, 0), _targetWindow);

                    // 转换位置和尺寸为设备像素 (考虑DPI缩放)
                    var devicePoint = transformToDevice.Transform(borderPos);
                    var deviceSize = transformToDevice.Transform(new Point(width, height));

                    destination = new RECT
                    {
                        Left = (int)devicePoint.X,
                        Top = (int)devicePoint.Y,
                        Right = (int)(devicePoint.X + deviceSize.X),
                        Bottom = (int)(devicePoint.Y + deviceSize.Y)
                    };
                }
                else
                {
                    destination = new RECT
                    {
                        Left = 0,
                        Top = 0,
                        Right = (int)width,
                        Bottom = (int)height
                    };
                }

                var props = new DWM_THUMBNAIL_PROPERTIES
                {
                    dwFlags = DWM_TNP_RECTDESTINATION | DWM_TNP_VISIBLE | DWM_TNP_OPACITY,
                    rcDestination = destination,
                    fVisible = true,
                    opacity = 255
                };

                int result = DwmUpdateThumbnailProperties(_thumbnailHandle, ref props);
                return result == 0;
            }
            catch
            {
                return false;
            }
        }

        // 更新预览位置（不重新创建缩略图，只更新位置）
        public bool UpdatePosition()
        {
            if (_thumbnailHandle == IntPtr.Zero || _previewBorder == null)
                return false;

            try
            {
                // 强制刷新布局以确保位置和大小准确
                _previewBorder.UpdateLayout();

                // 获取当前窗口的PresentationSource用于DPI转换
                var source = PresentationSource.FromVisual(_targetWindow);
                if (source == null) return false;

                // 获取DPI缩放转换矩阵
                var transformToDevice = source.CompositionTarget.TransformToDevice;

                // 获取Border在窗口中的精确位置
                Point borderPos = _previewBorder.TranslatePoint(new Point(0, 0), _targetWindow);

                // 获取Border的实际大小
                double width = _previewBorder.ActualWidth;
                double height = _previewBorder.ActualHeight;

                // 确保宽高不为零
                if (width < 1) width = 1;
                if (height < 1) height = 1;

                // 转换位置和尺寸为设备像素 (考虑DPI缩放)
                var devicePoint = transformToDevice.Transform(borderPos);
                var deviceSize = transformToDevice.Transform(new Point(width, height));

                var destination = new RECT
                {
                    Left = (int)devicePoint.X,
                    Top = (int)devicePoint.Y,
                    Right = (int)(devicePoint.X + deviceSize.X),
                    Bottom = (int)(devicePoint.Y + deviceSize.Y)
                };

                var props = new DWM_THUMBNAIL_PROPERTIES
                {
                    dwFlags = DWM_TNP_RECTDESTINATION | DWM_TNP_VISIBLE,
                    rcDestination = destination,
                    fVisible = true
                };

                int result = DwmUpdateThumbnailProperties(_thumbnailHandle, ref props);
                return result == 0;
            }
            catch
            {
                return false;
            }
        }
    }
}