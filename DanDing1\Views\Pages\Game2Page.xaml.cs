﻿using DanDing1.ViewModels.Pages;
using Wpf.Ui.Abstractions.Controls;
using XHelper;

namespace DanDing1.Views.Pages
{
    /// <summary>
    /// Game2Page.xaml 的交互逻辑
    /// </summary>
    public partial class Game2Page : INavigableView<Game2ViewModel>
    {
        private PageBaseEvent baseEvent { get; set; }

        public Game2Page(Game2ViewModel viewModel)
        {
            ViewModel = viewModel;
            DataContext = this;
            InitializeComponent();
            ViewModel.Init("游戏2", GameView.AddTask);
            baseEvent = new(ViewModel);

            GameView.GameModelName.Text = "游戏2";

            //悬赏接受状态
            GameView.AddTask.XShang_Status.IsChecked = XConfig.LoadValueFromFile<bool>(ViewModel.GameName, "XShang");
            GameView.AddTask.XShang_Status.Checked += baseEvent.XShang_Status_Checked;
            GameView.AddTask.XShang_Status.Unchecked += baseEvent.XShang_Status_Checked;

            //庭院皮肤场景
            string GameScene_ComboBox_SelectedItem = XConfig.LoadValueFromFile<string>(ViewModel.GameName, "TYscene") ?? "";
            if (GameScene_ComboBox_SelectedItem != "")
                GameView.AddTask.GameScene_ComboBox.SelectedValue = GameScene_ComboBox_SelectedItem;
            else
                GameView.AddTask.GameScene_ComboBox.SelectedValue = "默认";

            //寄养队友名字：
            //XConfig.SaveValueToFile(GameName, "JiYang_DesignatedName", AddTaskControl.ViewModel.Timer_JiYang_DesignatedName);
            string Timer_JiYang_DesignatedName = XConfig.LoadValueFromFile<string>(ViewModel.GameName, "JiYang_DesignatedName") ?? "";
            if (Timer_JiYang_DesignatedName != "")
                GameView.AddTask.ViewModel.Timer_JiYang_DesignatedName = Timer_JiYang_DesignatedName;

            // 使用AddTaskPropertyViewModel的动态加载方法加载配置到当前实例
            GameView.AddTask.ViewModel.LoadConfig(ViewModel.GameName);
            GameView.AddTask.ViewModel.ResetAppStutas();

            GameView.AddTask.GameScene_ComboBox.SelectionChanged += baseEvent.GameScene_ComboBox_SelectionChanged;
        }

        public Game2ViewModel ViewModel { get; }
    }
}