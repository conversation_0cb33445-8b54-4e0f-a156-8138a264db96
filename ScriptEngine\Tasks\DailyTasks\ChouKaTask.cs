﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Tasks.DailyTasks
{
    internal class ChouKaTask : BaseTask
    {
        private Dictionary<string, Position> ClickPos_Pairs = new Dictionary<string, Position>()
        {
            {"默认",new(919,187,951,234) },
            {"枫色秋庭",new(726,177,757,215) },
            {"织梦莲庭",new(683,222,705,252) },
            {"笔墨山河",new(798,222,817,261) },
            {"缘结之庭",new(866,215,892,251) },
            {"点击蓝票",new(605,603,654,660) },
            {"点击确定",new(456,631,556,666) },
            {"点击返回",new(40,18,74,55) },
        };

        private Dictionary<string, Position> Find_Pairs = new()
        {
            {"免费次数",new(529,684,739,716) },
            {"蓝票数量",new(730,16,850,44) },
            {"主场景名字",new(86,7,264,57) },
            {"再次召唤",new(700,629,847,667) },
        };

        public string CourtyardSkin { get; private set; } = "默认";

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, className);
            CourtyardSkin = Db.GameSetting.CourtyardSkin ?? "默认";
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            log.Warn("当前进行的是每日免费抽卡任务，支持的召唤屋皮肤有：");
            log.Warn("故梦雅苑、四时雅苑、万事皆灵(3s后继续)");
            Sleep(3000);
            if (!ToMainScene()) return;
            Sleep(1500);
            GetBlueCount();
            Main();

            log.Info("抽卡任务结束..退出到庭院..");
            Fast.Click(ClickPos_Pairs["点击返回"]);
            Sleep(2000);
        }

        /// <summary>
        /// 获取蓝票数量
        /// </summary>
        /// <returns></returns>
        private int GetBlueCount()
        {
            var res = Fast.Ocr_Local(Find_Pairs["蓝票数量"]);
            log.Info_Green($"当前蓝票数量：{res}张");
            int.TryParse(res, out int count);
            return count;
        }

        /// <summary>
        /// 是否可以抽卡
        /// </summary>
        /// <returns></returns>
        private bool isChou()
        {
            var res = Fast.Ocr_String(Find_Pairs["免费次数"]);
            if (res.Contains("免费"))
            {
                log.Info_Green("可以抽卡");
                return true;
            }
            log.Info_Green("免费次数已用完，无法抽卡，结束任务..");
            return false;
        }

        /// <summary>
        /// 抽卡
        /// </summary>
        private void Main()
        {
            if (!NowIsMainScence()) return;
            if (!isChou()) return;

            log.Info("点击蓝票区域..");
            Fast.Click(ClickPos_Pairs["点击蓝票"]);
            Sleep(1000);
            log.Info("开始画符抽卡..");
            Dm.MoveTo(431, 138);
            Dm.LeftDown();
            Sleep(50, false);
            Dm.MoveTo(810, 479);
            Dm.LeftDown();
            Sleep(50, false);
            Dm.MoveTo(840, 144);
            Dm.LeftDown();
            Sleep(50, false);
            Dm.MoveTo(460, 474);
            Dm.LeftDown();
            Sleep(250, false);
            Dm.LeftUp();
            Sleep(1000);
            log.Info("等待结果..");
            WaitAgain();
            log.Info("再次召唤出现，截图，并点击确定..");
            //截图 保存到.\\抽卡 文件夹
            var path = Path.Combine(Environment.CurrentDirectory, "抽卡");
            if (!Directory.Exists(path))
                Directory.CreateDirectory(path);
            Dm.CaptureJpg(0, 0, 2000, 2000, path + $"\\[{log.LogClassName}] {DateTime.Now:yyyyMMddHHmmss}.jpg", 100);
            Fast.Click(ClickPos_Pairs["点击确定"]);
            Sleep(2000);
        }

        private bool NowIsMainScence()
        {
            var pics = Mp.Filter("主场景");
            if (pics.FindAll()) return true;
            var res = Fast.Ocr_String(Find_Pairs["主场景名字"]);
            if (res.Contains("召唤")) return true;
            return false;
        }

        /// <summary>
        /// 去主场景
        /// </summary>
        private bool ToMainScene()
        {
            if (NowIsMainScence()) return true;
            log.Info("前往抽卡任务场景..");
            Scene.TO.TingYuan();
            Sleep(1000);
            log.Info("滑动一下屏幕.");
            Operational.Slide_Pos(new Position("1169,86,1196,105"), new("159,134,186,155"));
            Sleep(1000, false);
            Fast.Click(ClickPos_Pairs[CourtyardSkin]);
            Sleep(1500);

            int maxRetries = 10;
            int currentRetry = 0;

            while (!NowIsMainScence() && currentRetry < maxRetries)
            {
                Sleep(1000);
                if (!NowIsMainScence())
                {
                    log.Info("滑动一下屏幕.");
                    Operational.Slide_Pos(new Position("1169,86,1196,105"), new("159,134,186,155"));
                    Sleep(1000, false);
                    Fast.Click(ClickPos_Pairs[CourtyardSkin]);
                }

                currentRetry++;
            }

            if (currentRetry >= maxRetries)
            {
                log.Warn("尝试进入抽卡场景失败，已达到最大尝试次数");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 等待再次召唤
        /// </summary>
        private void WaitAgain()
        {
            while (!Fast.Ocr_String(Find_Pairs["再次召唤"])
                .Contains("再次召唤"))
            {
                log.Info("等待出现再次召唤..");
                Sleep(1500);
            }
        }
    }
}