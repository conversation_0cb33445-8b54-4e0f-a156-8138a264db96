﻿using DamoControlKit;
using DamoControlKit.Model;
using DanDing1.Helpers;
using DanDing1.Models;
using DanDing1.ViewModels.Services;
using DanDing1.Views.Windows;
using ScriptEngine.Model;
using ShareX.HelpersLib;
using System.Collections.ObjectModel;
using System.Runtime.InteropServices;
using Wpf.Ui;
using Wpf.Ui.Abstractions.Controls;
using Wpf.Ui.Controls;
using Wpf.Ui.Extensions;
using XHelper;
using static ScriptEngine.Model.TaskConfigsModel;
using Application = System.Windows.Application;
using System.IO;
using System;

namespace DanDing1.ViewModels.Base
{
    /// <summary>
    /// 逻辑函数
    /// </summary>
    public partial class GameViewBaseModel : ObservableObject, INavigationAware
    {
        /// <summary>
        /// 任务服务
        /// </summary>
        private readonly TaskService _taskService;

        /// <summary>
        /// 仅用于UI关闭判断
        /// </summary>
        public bool isRunning = false;

        public LogGameControlModel LogGameControlModel { get; set; }

        private INavigationService navigationService;

        /// <summary>
        /// 执行任务的时间
        /// </summary>
        public Stopwatch Run_watch = new();

        private Task? runtime_task;
#pragma warning disable CS8618

        /// <summary>
        /// 缓存上次运行总秒数
        /// 目前用于运行汇总的发送
        /// </summary>
        private int RunTimed = 0;

        public GameViewBaseModel(INavigationService navigationService, IServiceProvider serviceProvider, IContentDialogService contentDialogService)
        {
            NavigationView = navigationService.GetNavigationControl() as NavigationView ?? throw new Exception("软件视图组件注册失败！无法继续！");
            ServiceProvider = serviceProvider;
            ContentDialogService = contentDialogService;

            LogGameControlModel = new(this);

            // 初始化任务服务
            _taskService = new TaskService(InfoBar);
        }

#pragma warning restore CS8618

        public IContentDialogService ContentDialogService { get; }

        /// <summary>
        /// 游戏配置名
        /// 游戏1 游戏2 游戏3
        /// </summary>
        public string GameName { get; private set; }

        private int scriptId;

        /// <summary>
        /// 当前脚本ID
        /// </summary>
        public int ScriptId
        {
            get
            {
                return scriptId;
            }
            set
            {
                scriptId = value;
            }
        }

        /// <summary>
        /// 选中的窗口信息
        /// bind用
        /// </summary>
        public WindowInfo? SelectedWindow { get; private set; }

        public IServiceProvider ServiceProvider { get; }

        /// <summary>
        /// 配置 简
        /// </summary>
        private AppConfig _config => GlobalData.Instance.appConfig;

        private NavigationView NavigationView { get; }

        /// <summary>
        /// 初始化模块
        /// 存储当前实例到全局数据中
        /// </summary>
        /// <param name="gameName">当前游戏名</param>
        private bool _isInitialized = false;

        public void Init(
            string gameName, Views.UserControls.AddTaskControl addTaskControl)
        {
            // 确保只初始化一次
            if (_isInitialized)
                return;

            GameName = gameName;

            // 添加对Scripts.AddScriptName返回值的检查
            int scriptId = Scripts.AddScriptName(GameName);
            if (scriptId == -1)
            {
                XLogger.Warn($"脚本名 {GameName} 已存在，尝试重新获取ID");
                // 根据名称重新获取ID的逻辑可以根据实际需求添加
            }
            ScriptId = scriptId;

            AddTaskControl = addTaskControl;
            //载入保存的任务列表
            GameTaskLists = XConfig.LoadValueFromFile<ObservableCollection<TaskConfigsModel.Configs>>("TaskList", GameName) ?? [];

            //载入通知设置
            var noticeConfigs = XConfig.LoadValueFromFile<Dictionary<string, object>>(GameName, "NoticeConfigs");
            if (noticeConfigs != null)
            {
                if (noticeConfigs.TryGetValue("Notice_IsChecked", out var isChecked))
                    Notice_IsChecked = bool.Parse(isChecked.ToString() ?? "False");

                if (noticeConfigs.TryGetValue("Notice_SelectItem", out var selectItem))
                    Notice_SelectItem = selectItem.ToString() ?? "邮件";
            }

            AddTaskControl.ViewModel.OnFastStartClicked_ToViewModel = async (configs) =>
            {
                await OnFastStart(configs);
            };

            switch (GameName)
            {
                case "游戏1":
                    GlobalData.Instance.Game1RunningConfig = this; break;
                case "游戏2":
                    GlobalData.Instance.Game2RunningConfig = this; break;
                case "游戏3":
                    GlobalData.Instance.Game3RunningConfig = this; break;
                case "游戏4":
                    GlobalData.Instance.Game4RunningConfig = this; break;
                default:
                    break;
            }

            // 标记为已初始化
            _isInitialized = true;
        }

        /// <summary>
        /// 离开到视图执行的方法
        /// </summary>
        public Task OnNavigatedFromAsync()
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// 切换到视图执行的方法
        /// 获取登录状态 未登录时不允许操作
        /// </summary>
        public Task OnNavigatedToAsync()
        {
            InfoBar?.Hidden();
            if (!_config.IsLogin)
            {
                LoginStatus = "未登录状态";
                InfoBar?.Show("无法继续..", "您当前处于未登录、试用状态，无法继续使用此程序功能！", "Warning");
                //创建定时器 定时获取登录状态
                DispatcherTimer timer = new DispatcherTimer();
                timer.Interval = new TimeSpan(0, 0, 1);
                timer.Tick += (s, e) =>
                {
                    //10秒后停止定时器
                    if (timer.Interval.TotalSeconds > 5)
                        timer.Stop();
                    if (_config.IsLogin)
                    {
                        timer.Stop();
                        LoginStatus = "登录状态";
                        InfoBar?.Hidden();
                    }
                };
                timer.Start();
            }
            else
            {
                LoginStatus = "登录状态";
                InfoBar?.Hidden();
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// 从本地导入当前的任务列表
        /// </summary>
        /// <param name="name"></param>
        public void SetNowTaskPlan(string name)
        {
            if (name == "" || name == null)
                return;
            string plan = name;
            if (plan.Contains('|'))
            {
                var tasklist = XConfig.LoadValueFromFile<ObservableCollection<Configs>>("TaskList", plan.Split('|')[0]);
                if (tasklist is not [] and not null)
                {
                    GameTaskLists = tasklist;
                    InfoBar?.Show("任务列表导入成功", $"{name} 任务列表已导入！", "Success", 1000);
                }
                return;
            }
            //读取用户自定义任务列表
            var usertasklist = XConfig.LoadValueFromFile<ObservableCollection<Configs>>("UserTaskList", plan);
            if (usertasklist is not [] and not null)
            {
                GameTaskLists = usertasklist;
                InfoBar?.Show("任务列表导入成功", $"{name} 任务列表已导入！", "Success", 1000);
            }
        }

        [DllImport("user32.dll")]
        private static extern IntPtr WindowFromPoint(int x, int y);

        /// <summary>
        /// 自动切换到日志界面
        /// </summary>
        /// <returns></returns>
        private void AutoMoveLogView()
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                if (XConfig.LoadValueFromFile<bool>(LocalConfig.StartOpenLog))
                {
                    InfoBar?.Show("任务正在执行", "1S后将为您切换至日志界面！", "Success", 3000);
                    Task.Delay(1000).Wait();
                    if ((XConfig.LoadValueFromFile<string>("OpenLogItems") ?? "直接切换") == "直接切换")
                        NavigationView.Navigate(typeof(Views.Pages.LogPage));
                    else
                        GlobalData.Instance.logWinControl.ShowLogWin(GameName);
                }
                else
                    InfoBar?.Show("任务正在执行", "您可以手动切换至日志界面查看详细进度！也可以在设置中设置开始任务后自动切换至日志界面！", "Success", 3000);
            });
        }

        /// <summary>
        /// 自动保存任务配置
        /// </summary>
        /// <param name="Fastconfigs"></param>
        private void AutoSaveTaskConfigs(ObservableCollection<Configs>? Fastconfigs)
        {
            if (Fastconfigs is null
                            && XConfig.LoadValueFromFile<bool>(LocalConfig.StartSaveTasks))
            {
                try
                {
                    // 假设 XConfig 将 TaskList 保存在 runtimes/AppConfig/TaskList 目录下
                    // 构建目标目录路径
                    string baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                    string taskListDirectory = Path.Combine(baseDirectory, "runtimes", "AppConfig", "TaskList");

                    // 确保目录存在
                    Directory.CreateDirectory(taskListDirectory);
                }
                catch (Exception ex)
                {
                    // 记录创建目录时可能发生的错误
                    XLogger.Error($"创建 TaskList 目录失败: {ex.Message}");
                    // 可以选择根据错误情况决定是否继续执行 SaveValueToFile
                }

                XConfig.SaveValueToFile("TaskList", GameName, GameTaskLists);
                XLogger.Info_Green("已经为您刚开始的任务列表进行了自动保存！");
            }
            XConfig.SaveValueToFile(GameName, "XShang", AddTaskControl.ViewModel.XShangChecked);
            XConfig.SaveValueToFile(GameName, "TYscene", AddTaskControl.ViewModel.Game_Scene);
            XConfig.SaveValueToFile(GameName, "JiYang_DesignatedName", AddTaskControl.ViewModel.Timer_JiYang_DesignatedName);

            AddTaskControl.ViewModel.SaveConfig(GameName);
        }

        /// <summary>
        /// 检查 PaddleOCR 是否已安装
        /// </summary>
        private async Task<bool> CheckPaddleOCR()
        {
            string paddleOcrPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "runtimes", "PaddleOCR_cpp.exe");
            if (!File.Exists(paddleOcrPath))
            {
                await ContentDialogService.ShowSimpleDialogAsync(new SimpleContentDialogCreateOptions()
                {
                    Title = "无法继续",
                    Content = "执行六道任务需要先安装 PaddleOCR 插件，请在设置->拓展插件中安装！",
                    CloseButtonText = "确定"
                });
                return false;
            }
            return true;
        }

        private void MoveTask(int index, int direction)
        {
            if (index < 0 || GameTaskLists == null || GameTaskLists.Count < 2)
                return;

            int newIndex = index + direction;
            if (newIndex < 0 || newIndex >= GameTaskLists.Count)
                return;

            var task = GameTaskLists[index];
            GameTaskLists.RemoveAt(index);
            GameTaskLists.Insert(newIndex, task);
            GameTaskListsIndex = newIndex;
        }

        /// <summary>
        /// 分辨率绑定错误提示
        /// </summary>
        internal void SendTipAboutHwnd()
        {
            if (SelectedWindow is null)
                return;
            if (SelectDpi != "720*1280" && SelectDpi != "1280*720")
                InfoBar?.Show("分辨率错误", "请参考文档，绑定符合要求的分辨率模拟器！", "Warning");
            else
                InfoBar?.Hidden();
        }

        /// <summary>
        /// 更新ShowTime
        /// <returns></returns>
        private async Task UpRunningTime()
        {
            RunTimed = 0;
            StartButtonEnabled = false;
            Run_watch.Start();
            ShowTime = $"{Run_watch.Elapsed:hh\\:mm\\:ss}";
            while (Scripts.IsRunning(ScriptId, out _))
            {
                Application.Current?.Dispatcher?.Invoke(() =>
                {
                    ShowTime = $"{Run_watch.Elapsed:hh\\:mm\\:ss}";
                });
                await Task.Delay(1000);
                RunTimed++;
            }
            Application.Current?.Dispatcher?.Invoke(() =>
            {
                ShowTime = $"00:00:00";
            });
            StartButtonEnabled = true;
            LastShowTime = $"Last：{Run_watch.Elapsed:hh\\:mm\\:ss}";
            Run_watch.Reset();
            runtime_task = null;
        }
    }
}