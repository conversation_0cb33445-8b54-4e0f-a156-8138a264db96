using XHelper;

namespace DanDing1.Commands
{
    /// <summary>
    /// 命令基类
    /// </summary>
    internal abstract class BaseCommand
    {
        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="parameters">命令参数</param>
        public abstract void Execute(string[] parameters);

        /// <summary>
        /// 验证参数数量
        /// </summary>
        protected bool ValidateParameterCount(string[] parameters, int expectedCount)
        {
            if (parameters.Length < expectedCount)
            {
                XLogger.Error($"命令参数不足，需要{expectedCount}个参数");
                return false;
            }
            return true;
        }
    }
}