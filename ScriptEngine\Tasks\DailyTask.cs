﻿using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using ScriptEngine.Tasks.DailyTasks;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Tasks
{
    internal class DailyTask : BaseTask
    {
        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, className);
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            if (UserConfig_Preset != null)
            {
                // 判断场景是否为庭院
                GoToTingYuan();

                Sleep(1000);
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入式神录，开始应用预设{UserConfig_Preset}");
                Fast.Click(1111, 630, 1148, 660);
                Sleep(1500);
                Tmp.Do_Preset(preset);
            }

            //每日签到 Checkin
            if (configs.Others.TryGetValue("Checkin", out string? checkin) && checkin == "True")
            {
                log.Info_Green("开始执行日常任务-每日签到！");
                var t = new CheckinTask();
                t.Init(Db, Dm, Ct, "签到");
                t.Start(configs);
                Sleep(2000);
            }

            //友情点
            if (configs.Others.TryGetValue("Befriendly", out string? befriendly) && befriendly == "True")
            {
                log.Info_Green("开始执行日常任务-友情点！");
                var t = new BefriendlyTask();
                t.Init(Db, Dm, Ct, "友情点");
                t.Start(configs);
                Sleep(2000);
            }
            //每日一抽
            if (configs.Others.TryGetValue("ChouKa", out string? chouKa) && chouKa == "True")
            {
                log.Info_Green("开始执行日常任务-每日一抽！");
                var t = new ChouKaTask();
                t.Init(Db, Dm, Ct, "抽卡");
                t.Start(configs);
                Sleep(2000);
            }
            //每日免费礼包
            if (configs.Others.TryGetValue("FreeGift", out string? freeGift) && freeGift == "True")
            {
                log.Info_Green("开始执行日常任务-每日免费礼包！");
                var t = new FreeGiftTask();
                t.Init(Db, Dm, Ct, "礼包");
                t.Start(configs);
                Sleep(2000);
            }
            //寮30交材料
            if (configs.Others.TryGetValue("Liao30SE", out string? liao30SE) && liao30SE == "True")
            {
                log.Info_Green("开始执行日常任务-寮30交材料！");
                var t = new Liao30Task();
                t.Init(Db, Dm, Ct, "寮30");
                t.Start(configs);
                Sleep(2000);
            }
            //悬赏
            if (configs.Others.TryGetValue("Reward", out string? reward) && reward == "True")
            {
                log.Info_Green("开始执行日常任务-悬赏！");
                var t = new RewardTask();
                t.Init(Db, Dm, Ct, "悬赏");
                t.Start(configs);
                Sleep(2000);
            }
            //地鬼
            if (configs.Others.TryGetValue("DiGui", out string? diGui) && diGui == "True")
            {
                log.Info_Green("开始执行日常任务-地域鬼王！");
                var t = new DiGuiTask();
                t.Init(Db, Dm, Ct, "地鬼");
                t.Start(configs);
                Sleep(2000);
            }

            log.Info_Green("所有日常任务已完成！");
        }

        /// <summary>
        /// 确保角色在庭院场景
        /// </summary>
        private bool GoToTingYuan()
        {
        Retry:
            Scene.TO.TingYuan();
            // 判断庭院位置
            if (Scene.NowScene != "庭院")
            {
                log.Info("当前不在庭院，尝试前往庭院");
                if (!Scene.TO.TingYuan())
                {
                    if (!Scene.TO.ResetScene(out var s))
                    {
                        log.Error("无法进入庭院，结束任务");
                        return false;
                    }
                    else
                        goto Retry;
                }
                Sleep(1500); // 等待1.5秒

                // 再次确认是否在庭院
                if (Scene.NowScene != "庭院")
                {
                    if (!Scene.TO.ResetScene(out var s))
                    {
                        log.Error("无法进入庭院，结束任务");
                        return false;
                    }
                    else
                        goto Retry;
                }
            }
            log.Info("已确认在庭院场景");
            Sleep(3000);
            return true;
        }
    }
}