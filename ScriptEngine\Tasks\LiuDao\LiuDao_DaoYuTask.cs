﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DamoControlKit;
using DamoControlKit.Model;
using OpenCvSharp.XImgProc;
using ScriptEngine.Tasks.Base;

namespace ScriptEngine.Tasks.LiuDao
{
    /// <summary>
    /// 子岛屿任务逻辑
    /// </summary>
    internal class LiuDao_DaoYuTask
    {
        /// <summary>
        /// 基础任务类引用
        /// </summary>
        internal LiuDaoTask _Base;

        /// <summary>
        /// 当前岛屿名称
        /// </summary>
        internal string DaoYuName;

        /// <summary>
        /// 标记状态
        /// </summary>
        private bool BiaoJi_Status = false;

        private Dictionary<string, Pixel> Colors = new Dictionary<string, Pixel>()
        {
            {"神秘_仿造OK",new Pixel(1201,632,"f0d8a4-101010",0.96) },
            {"购买万相赐福",new Pixel(817,451,"f3b25e-101010",0.96) },
            {"不再提醒",new Pixel(759,431,"f3b25e-101010",0.96) },
        };

        /// <summary>
        /// 不需要发送日志的操作列表
        /// </summary>
        private List<string> DontSendLog = ["标记", "选Buff"];

        public LiuDao_DaoYuTask(LiuDaoTask _base, string daoYuName)
        {
            _Base = _base;
            DaoYuName = daoYuName;
            //初始化组件
            foreach (var item in Colors)
                item.Value.SetXsoft(_Base.Dm);
        }

        /// <summary>
        /// 延迟函数委托
        /// </summary>
        internal Action<int, bool> _sleep => _Base.Sleep;

        /// <summary>
        /// 任务配置
        /// </summary>
        internal LiuDaoTask_Config Config => _Base.Config;

        /// <summary>
        /// 快速操作工具
        /// </summary>
        internal Fast Fast => _Base.Fast;

        /// <summary>
        /// 日志记录器
        /// </summary>
        internal Log log => _Base.log;

        /// <summary>
        /// 内存图片管理器
        /// </summary>
        internal MemPics Mp => _Base.Mp;

        public void Start()
        {
            if (DaoYuName == "选择")
                return;
            _Base.Anti.RandomDelay();//防封等待
            Sleep(500);
            switch (DaoYuName)
            {
                case "测试":
                    string str = NowInDaoYu();
                    log.Debug(str);
                    break;

                case "鏖战":

                    if (NowInDaoYu() != "鏖战")//判断是否在鏖战场景内
                    {
                        log.Info("当前不在鏖战场景内，退出..");
                        return;
                    }
                    Config.ZhanDou_Count++;
                    AoZhan();
                    break;

                case "混沌":

                    if (NowInDaoYu() != "混沌")//判断是否在混沌场景内
                    {
                        log.Info("当前不在混沌场景内，退出..");
                        return;
                    }
                    Config.ZhanDou_Count++;
                    HunDun();
                    break;

                case "神秘":

                    if (NowInDaoYu() != "神秘")//判断是否在神秘场景内
                    {
                        log.Info("当前不在神秘场景内，退出..");
                        return;
                    }
                    Config.ZhanDou_Count++;
                    ShenMi();
                    break;

                case "星之屿":

                    if (NowInDaoYu() != "星之屿") //判断是否在星之屿场景内
                    {
                        log.Info("当前不在星之屿场景内，退出..");
                        return;
                    }
                    Config.ZhanDou_Count++;
                    XingZhiDao();
                    break;

                case "宁息":

                    if (NowInDaoYu() != "宁息")//判断是否在宁息场景内
                    {
                        log.Info("当前不在宁息场景内，退出..");
                        return;
                    }
                    Config.ZhanDou_Count++;
                    NingXi();
                    break;

                case "Boss":

                    if (NowInDaoYu() != "Boss") //判断是否在Boss场景内
                    {
                        log.Info("当前不在Boss场景内，退出..");
                        return;
                    }
                    Config.ZhanDou_Count++;
                    Boss();
                    break;

                default:
                    log.Warn($"没有收录[{DaoYuName}]的岛屿子任务动作，无法继续，返回上一流程！");
                    break;
            }
        }

        private void AoZhan()
        {
            log.Info_Green($"开始执行[{DaoYuName}]的岛屿子任务动作！");
            log.Info("选择技能怪..");
            Fast.Click(807, 324, 872, 393);
            Fast.Click(807, 324, 872, 393);
            Sleep(1200);
            log.Info("点击挑战..");
            Fast.Click(1124, 588, 1230, 676);
            Sleep(600);
            Combat();//战斗
            SelectBuff();//选择Buff
        }

        /// <summary>
        /// 选择Boss技能
        /// </summary>
        private void SelectBossSkill()
        {
            //点击备战 858,635,903,669
            log.Info("点击备战..");
            Fast.Click(858, 635, 903, 669);
            Sleep(1000);
            //重置技能 1019,459,1050,486
            log.Info("重置技能");
            Fast.Click(1019, 459, 1050, 486); //进入选择技能
            Sleep(600);
            Fast.Click(712, 643, 748, 677); //重置
            Sleep(600);
            Fast.Click(697, 409, 795, 443); //确定
            Sleep(1000);
            var bossSkills = new List<string> { "六道.Boss_柔风饱暖", "六道.Boss_暴虐", "六道.Boss_细雨化屏", "六道.Boss_妖力化生" };
            foreach (var skill in bossSkills)
            {
                int x, y;
                if (Mp.Filter(skill).FindAll(out x, out y))
                {
                    //输出log
                    log.Info($"识别到技能：{skill}，点击位置：{x},{y}");
                    Fast.Click(x, y);
                    Sleep(600);
                    Fast.Click(456, 605);
                    Sleep(1500);
                }
            }
            //退出 23,21,61,58
            log.Info("退出..");
            Fast.Click(23, 21, 61, 58);
            Sleep(1600);
            Fast.Click(19, 27, 54, 55);
            Sleep(1000);
        }

        private void Boss()
        {
            log.Info_Green($"开始执行[{DaoYuName}]的岛屿子任务动作！");
            // 如果混沌不开宝箱设置为true，则跳过重置技能
            if (!Config.HunDunBuKaiXiang)
            {
                SelectBossSkill();
            }
            else
            {
                log.Info("混沌不开宝箱设置为true，跳过重置技能");
            }
            Sleep(600);
            Fast.Click(1144, 591, 1213, 665);
            Combat(true);//战斗
            Sleep(1000);
            //购买万相赐福
            if (Colors["购买万相赐福"].Find(null))
            {
                if (Config.ShuangBeiJiaCheng_Buy)
                {
                    log.Info("按照设置购买万相赐福..");
                    Fast.Click(705, 415, 812, 448);
                    Sleep(1000);
                    Fast.Click(586, 547, 690, 582);
                    Sleep(600);
                    Fast.Click(617, 83, 934, 146);
                    Sleep(1000);
                }
                else
                {
                    log.Info("不购买万相赐福..");
                    Sleep(200);
                    Fast.Click(552, 347, 569, 371);
                    Sleep(200);
                    Fast.Click(537, 413, 617, 450);
                    Sleep(200);
                }
            }
            log.Debug("等待事件_双倍..");
            if (Mp.Filter("事件_双倍").Wait(3))
            {
                //检查 ShuangBeiJiaCheng 是否为true
                if (Config.ShuangBeiJiaCheng)
                {
                    Sleep(1000);
                    //查找 事件_极 图片
                    if (Mp.Filter("事件_极").FindAll())
                    {
                        log.Info("当前为极，使用双倍掉落..");
                        _Base.Double_Count++;
                        Fast.Click(690, 426, 781, 462);
                    }
                    else Fast.Click(520, 432, 592, 458);
                }
                else
                {
                    log.Info("弹出了双倍提醒，您的设置为不使用双倍加成..");
                    Fast.Click(510, 427, 598, 459);
                }
                Sleep(1000);
            }
            else Fast.Click(520, 432, 592, 458);
            if (Mp.Filter("事件_Boss奖励").Wait(10))
            {
                Sleep(1000);
                Fast.Click(558, 63, 746, 127);
                Sleep(1000);
                //截图 保存到.\\六道 文件夹
                var path = Path.Combine(Environment.CurrentDirectory, "六道");
                if (!Directory.Exists(path))
                    Directory.CreateDirectory(path);
                _Base.Dm.CaptureJpg(0, 0, 2000, 2000, path + $"\\{DateTime.Now:yyyyMMddHHmmss}.jpg", 100);
                Fast.Click(558, 63, 746, 127);
                Sleep(600);
            }
            //等待六道场景  Scene.NowScene
            log.Info("等待六道开启的场景..");
            while (_Base.Scene.NowScene != "六道")
            {
                Sleep(1000);
                if (_Base.Scene.NowScene == "六道")
                    break;
                Fast.Click(558, 63, 746, 127);
            }
            log.Info("等到了六道开启的场景..继续流程");
        }

        /// <summary>
        /// 关闭提示
        /// </summary>
        private void CloseTip()
        {
            Sleep(600);
            if (Colors["不再提醒"].Find(null))
            {
                log.Info("点击不再提醒..");
                Fast.Click(575, 348, 597, 370);
                Sleep(600);
                log.Info("点击确定..");
                Fast.Click(709, 415, 814, 449);
                Sleep(600);
            }
        }

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat(bool isBoss = false)
        {
            Stopwatch? sw = null;
            if (isBoss) sw = new();
            sw?.Start();

            //点击开始
            log.Info("战斗点击开始");
            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置
            var boss_end_pics = Mp.Filter("事件_Boss奖励")
                .Add(Mp.Filter("事件_双倍"));
            if (isBoss) Sleep(10000);
            while (!isbreak)
            {
                if (isBoss && sw?.ElapsedMilliseconds / 1000 > 3 * 60)
                {
                    log.Info("Boss战斗超时3分钟，手动结束本次六道..点击返回..");
                    Fast.Click(20, 20, 51, 45);
                    Sleep(1500);
                    log.Info("点击确定..");
                    Fast.Click(701, 404, 786, 441);
                    Sleep(6000);
                    log.Info("点击返回..");
                    Fast.Click(20, 20, 51, 45);
                    Sleep(1500);
                    log.Info("点击放弃..");
                    Fast.Click(715, 343, 765, 390);
                    Sleep(1500);
                    log.Info("点击确定..");
                    Fast.Click(701, 404, 786, 441);
                    Sleep(1500);
                    isbreak = true;
                    continue;
                }
                Sleep(500);
                if (isBoss && (boss_end_pics.FindAllE(out var lists) || Colors["购买万相赐福"].Find(null)))
                {
                    if (Mp.Filter("标记").FindAll()) continue;
                    log.Info($"战斗胜利(Boss)..");
                    isbreak = true;
                    continue;
                }
                var p = _Base.ZhanDou_Pics.FindAllEa();
                if (p is null) continue;
                FindOkFun(p.Name, p);
                if (!DontSendLog.Any(p.Name.Contains)) log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("选Buff") || p.Name.Contains("奖励"))
                {
                    if (Mp.Filter("标记").FindAll()) continue;
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败"))
                {
                    if (Mp.Filter("标记").FindAll()) continue;
                    ret_bol = false;
                    isbreak = true;
                }
            }
            if (ret_bol)
                Combat_End();//等待liudao界面

            return ret_bol;
        }

        /// <summary>
        /// 六道胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            log.Info("战斗End(Combat_End)..");
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(500);
                var p = _Base.ZhanDou_Pics.FindAllEa();
                if (p is null) continue;
                if (p.Name.Contains("选Buff") || p.Name.Contains("奖励"))
                {
                    isbreak = true;
                    continue;
                }
                log.Info($"执行点击：{p._Name}");
                p.Click();
            }
        }

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            if (Config.BiaoJi && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.2秒，标记位置：5号位");
                Sleep(200);
                Fast.Click("840,409,881,471");
                return false;
            }
            return true;
        }

        private void HunDun()
        {
            log.Info_Green($"开始执行[{DaoYuName}]的岛屿子任务动作！");
            if (!Mp.Filter("混沌_幸运宝匣").FindAll())
            {
                log.Info("开始战斗..");
                Fast.Click(695, 283, 765, 357);
                Fast.Click(695, 283, 765, 357);
                Sleep(1000);
                log.Info("点击挑战..");
                Fast.Click(1124, 588, 1230, 676);
                Sleep(600);
                Combat();//战斗
                SelectBuff();//选择Buff
            }
            else
            {
                if (!Config.HunDunBuKaiXiang)
                {
                    log.Info("开启宝箱..");
                    Fast.Click(695, 283, 765, 357);
                    Fast.Click(695, 283, 765, 357);
                    Sleep(600);
                    Fast.Click(1156, 607, 1208, 657);
                    Sleep(2000);
                    WaitAndClickReward();
                }
                else
                {
                    log.Info("不开启宝箱..");
                    Fast.Click(1173, 614, 1222, 657);
                    Sleep(2000);
                }
            }
        }

        private void NingXi()
        {
            int ShuaXing = -1;
            log.Info_Green($"开始执行[{DaoYuName}]的岛屿子任务动作！");
        ReSelect:
            var jinBiCount = Fast.Ocr_Local(1150, 24, 1236, 54); // 当前剩余金币
            if (string.IsNullOrEmpty(jinBiCount)) jinBiCount = "0";
            log.Info($"当前剩余金币：{jinBiCount}");
            if (int.TryParse(jinBiCount, out int jinBiCountInt))
                if (jinBiCountInt <= 100)
                {
                    log.Info("当前剩余金币不足101，退出..");
                    Fast.Click(1179, 599, 1235, 648);
                    Sleep(600);
                    return;
                }
                else if (jinBiCountInt >= 500)
                {
                    if (ShuaXing == -1) ShuaXing = 3;
                }

            Fast.Ocr_String(669, 185, 1146, 578, t =>
            {
                foreach (var item in t)
                {
                    if (item.Text.Contains("柔风") || item.Text.Contains("暴虐"))
                    {
                        if (jinBiCountInt < 300 && item.Text.Contains("柔风")) continue;
                        if (jinBiCountInt < 200 && item.Text.Contains("暴虐")) continue;
                        log.Info($"识别到Buff:[{item.Text}]");
                        Fast.Click(669 + item.Center.X, 185 + item.Center.Y);
                        Sleep(1000);
                        Fast.Click(715, 417, 811, 447);
                        Sleep(1000);
                        return;
                    }
                }
            });
            if (ShuaXing > 0)
            {
                log.Info("刷新技能次数..");
                Fast.Click(545, 600, 591, 636);
                CloseTip();
                Sleep(400);
                ShuaXing--;
                goto ReSelect;
            }
            else
            {
                log.Info("刷新技能次数次数耗尽，退出..");
                Fast.Click(1179, 599, 1235, 648);
                CloseTip();
                Sleep(400);
            }
        }

        /// <summary>
        /// 返回当前在哪个岛屿内
        /// </summary>
        /// <returns></returns>
        private string NowInDaoYu()
        {
            if (_Base.ChangJingShiBie_Pics.FindAllE(out var lists))
            {
                foreach (var item in _Base.DaoYu_List)
                {
                    if (lists[0].Contains(item))
                        return item;
                }
            }
            return "";
        }

        /// <summary>
        /// 选择Buff
        /// </summary>
        private void SelectBuff()
        {
            int ReSelect_Count = 0;
        ReSelect:
            //判断是否在选择Buff场景内
            if (!Mp.Filter("事件_选Buff").FindAll())
            {
                log.Warn("当前不在选择Buff的场景内，无法选择Buff，跳出这个操作.");
                return;
            }
            string buff1 = Fast.Ocr_String(118, 311, 314, 385);
            string buff2 = Fast.Ocr_String(391, 311, 589, 385);
            string buff3 = Fast.Ocr_String(663, 315, 861, 387);

            log.Info($"识别到的Buff: [{buff1}] [{buff2}] [{buff3}] 开始选择..");
            var buffs = new List<(string buff, int x, int y)>
            {
                (buff1, 218,597),
                (buff2, 492,598),
                (buff3, 798,594)
            };

            var selectedBuff = buffs
                .Where(b => Config.Buff_Priority.Any(p => b.buff.Contains(p)))
                .OrderBy(b =>
                {
                    var index = Config.Buff_Priority.FindIndex(p => b.buff.Contains(p));
                    return index == -1 ? int.MaxValue : index;
                })
                .FirstOrDefault();

            if (selectedBuff != default)
            {
                log.Info($"选择Buff:[{selectedBuff.buff}]");
                Fast.Click(selectedBuff.x, selectedBuff.y);
                Sleep(600);
            }
            else
            {
                if (Config.ShuaXingJiNeng)
                {
                    if (ReSelect_Count >= 3)
                    {
                        log.Info("刷新技能次数次数耗尽，选择第四个技能..");
                        Fast.Click(1158, 603);
                        Sleep(500);
                    }
                    else
                    {
                        log.Info("未找到优先级内的Buff,刷新技能");
                        Fast.Click(1216, 644);
                        CloseTip();
                        Sleep(400);
                        ReSelect_Count++;
                        goto ReSelect;
                    }
                }
                else
                {
                    log.Info("未找到优先级内的Buff,随机选择第四个");
                    Fast.Click(1158, 603);
                    Sleep(500);
                }
            }
            Sleep(1000);
            //判断是否在选择Buff场景内
            if (Mp.Filter("事件_选Buff").FindAll())
            {
                log.Info("在选择Buff场景内，继续选择Buff");
                SelectBuff();
                return;
            }

            WaitAndClickReward();
        }

        private void ShenMi()
        {
            log.Info_Green($"开始执行[{DaoYuName}]的岛屿子任务动作！");

            // 提取重复的仿造操作到方法
            void PerformForgery()
            {
                Fast.Click(1179, 625, 1237, 681);
                Sleep(1000);
                log.Info("点击确定..");
                Fast.Click(705, 413, 816, 450);
                Sleep(5500);
                Fast.Click(486, 102, 712, 201); // 退出 "仿造成功" 界面
                Fast.Click(486, 102, 712, 201);
                Sleep(1000);
            }

            // 统一处理技能选择逻辑
            bool HandleSkill(string skillName, string skillDisplayName)
            {
                if (!Mp.Filter(skillName).FindAll(out int x, out int y))
                    return false;

                log.Info($"选择{skillDisplayName}..");
                Fast.Click(x, y);
                Sleep(600);

                if (Colors["神秘_仿造OK"].Find(null))
                {
                    log.Info($"{skillDisplayName}选择有效，执行仿造..");
                    PerformForgery();
                    return true;
                }

                log.Info($"{skillDisplayName}选择无效..");
                return false;
            }

            // 提取退出逻辑
            void ExitMysteryIsland()
            {
                log.Info("执行退出..");
                Fast.Click(25, 26, 52, 56);
                CloseTip();
            }

            if (Mp.Filter("神秘_仿造").FindAll())
            {
                log.Info("当前为仿造模式，选择主要技能升级..");

                // 尝试技能顺序处理
                var skills = new[] {
                    ("神秘_柔风饱暖", "柔风饱暖技能"),
                    ("神秘_暴虐", "暴虐技能")
                };

                foreach (var skill in skills)
                {
                    if (HandleSkill(skill.Item1, skill.Item2))
                    {
                        if (NowInDaoYu() == "神秘")
                        {
                            ExitMysteryIsland();
                        }
                        return;
                    }
                }

                log.Warn("没有可用的技能选择，退出..");
                ExitMysteryIsland();
            }
            else
            {
                log.Info("当前为转换模式，直接离开..");
                ExitMysteryIsland();
            }
        }

        /// <summary>
        /// 延迟函数
        /// </summary>
        /// <param name="time"></param>
        /// <param name="isreal"></param>
        private void Sleep(int time, bool isreal = false) => _sleep(time, isreal);

        /// <summary>
        /// 等待并点击奖励
        /// </summary>
        private void WaitAndClickReward()
        {
            log.Info("等待事件_获得奖励..");
            if (Mp.Filter("事件_获得奖励").Wait(5))
            {
                Sleep(100);
                Fast.Click(540, 87, 773, 133);
                //百分之50的概率再点一次
                if (new Random().Next(0, 100) < 50)
                    Fast.Click(540, 87, 773, 133);
                Sleep(200);
            }
            //等待岛屿切换
            while (NowInDaoYu() == "")
            {
                Sleep(500);
                if (new Random().Next(0, 100) < 50)
                    Fast.Click(540, 87, 773, 133);
            }
        }

        private void XingZhiDao()
        {
            log.Info_Green($"开始执行[{DaoYuName}]的岛屿子任务动作！");
            Sleep(1000);
            //判断 Config中的XingZhiDao_Damo 是否为true
            if (Config.XingZhiDao_Damo)
            {
                log.Info("开始打达摩..");
                Fast.Click(508, 288, 571, 345);
                Fast.Click(508, 288, 571, 345);
            }
            else
            {
                log.Info("开始打星之子..");
                Fast.Click(809, 276, 883, 370);
                Fast.Click(809, 276, 883, 370);
            }
            Sleep(1000);
            log.Info("点击挑战..");
            Fast.Click(1141, 595, 1214, 664);
            Sleep(600);
            Combat();//战斗
            WaitAndClickReward();
            if (!Config.XingZhiDao_Damo) SelectBuff();//选择Buff
        }
    }
}