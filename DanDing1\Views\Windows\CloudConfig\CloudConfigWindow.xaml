<Window x:Class="DanDing1.Views.Windows.CloudConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        Title="云端配置中心"
        Height="650"
        Width="900"
        d:DataContext="{d:DesignInstance local:CloudConfigWindow,
        IsDesignTimeCreatable=True}"
        ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
        ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        WindowStartupLocation="CenterScreen"
        mc:Ignorable="d">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 头部区域：标题和搜索栏 -->
        <Grid Grid.Row="0" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <StackPanel>
                <TextBlock Text="云端配置中心"
                           FontSize="24"
                           FontWeight="Bold"
                           Margin="0,0,0,5" />
                <TextBlock Text="浏览和导入其他用户分享的配置方案"
                           Opacity="0.7"
                           FontSize="14" />
            </StackPanel>

            <StackPanel Grid.Column="1"
                        Orientation="Horizontal"
                        VerticalAlignment="Center">
                <TextBox x:Name="SearchBox"
                         Width="200"
                         Height="32"
                         Margin="0,0,10,0"
                         Tag="搜索版本号..."
                         KeyDown="SearchBox_KeyDown" />
                <Button x:Name="SearchButton"
                        Content="搜索"
                        Height="32"
                        Click="SearchButton_Click" />
            </StackPanel>
        </Grid>

        <!-- 内容区域：配置列表 -->
        <Border Grid.Row="1"
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                CornerRadius="8"
                Padding="0">
            <Grid>
                <DataGrid x:Name="ConfigsDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="None"
                          HeadersVisibility="Column"
                          BorderThickness="0"
                          IsReadOnly="True"
                          Background="Transparent"
                          RowHeight="50"
                          MouseDoubleClick="ConfigsDataGrid_MouseDoubleClick">
                    <DataGrid.Resources>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="Padding" Value="15,10" />
                            <Setter Property="FontWeight" Value="SemiBold" />
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="配置名称"
                                            Binding="{Binding ConfigName}"
                                            Width="*">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Margin" Value="15,0" />
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="TextTrimming" Value="CharacterEllipsis" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="版本"
                                            Binding="{Binding Version}"
                                            Width="67">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="分享者"
                                            Binding="{Binding SharedByUsername}"
                                            Width="80">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="分享时间"
                                            Binding="{Binding SharedAt}"
                                            Width="210">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="使用次数"
                                            Binding="{Binding GetCount}"
                                            Width="85">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 加载指示器 -->
                <Grid x:Name="LoadingGrid"
                      Visibility="Collapsed"
                      Background="{DynamicResource ControlFillColorDefaultBrush}">
                    <StackPanel VerticalAlignment="Center"
                                HorizontalAlignment="Center">
                        <ui:ProgressRing IsIndeterminate="True"
                                         Width="40"
                                         Height="40"
                                         Margin="0,0,0,10" />
                        <TextBlock Text="加载中..."
                                   HorizontalAlignment="Center" />
                    </StackPanel>
                </Grid>

                <!-- 无数据提示 -->
                <Grid x:Name="NoDataGrid"
                      Visibility="Collapsed"
                      Background="{DynamicResource ControlFillColorDefaultBrush}">
                    <TextBlock Text="暂无配置数据"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Center"
                               FontSize="16"
                               Opacity="0.5" />
                </Grid>
            </Grid>
        </Border>

        <!-- 底部区域：分页控制 -->
        <Grid Grid.Row="2" Margin="0,15,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!-- 页码信息 -->
            <TextBlock x:Name="PageInfoText"
                       Grid.Column="0"
                       VerticalAlignment="Center"
                       Text="第1页/共1页" />

            <!-- 分页导航 -->
            <StackPanel Grid.Column="2"
                        Orientation="Horizontal"
                        HorizontalAlignment="Right">
                <Button x:Name="PrevButton"
                        Content="上一页"
                        Width="80"
                        Height="32"
                        Margin="0,0,10,0"
                        Click="PrevButton_Click" />
                <Button x:Name="NextButton"
                        Content="下一页"
                        Width="80"
                        Height="32"
                        Click="NextButton_Click" />
            </StackPanel>
        </Grid>
    </Grid>
</Window>