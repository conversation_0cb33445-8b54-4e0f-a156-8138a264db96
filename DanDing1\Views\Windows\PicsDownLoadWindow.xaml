﻿<Window x:Class="DanDing1.Views.Windows.PicsDownLoadWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        Title="图库下载至本地"
        Height="486"
        Width="451"
        d:DataContext="{d:DesignInstance local:PicsDownLoadWindow,
        IsDesignTimeCreatable=True}"
        ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
        ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        ScrollViewer.CanContentScroll="False"
        WindowStartupLocation="CenterScreen"
        mc:Ignorable="d">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部版本选择区域 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="选择图库版本：" 
                     FontSize="16" 
                     Margin="0,0,0,10"
                     Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
            <ComboBox x:Name="VersionComboBox" 
                     Width="200" 
                     HorizontalAlignment="Left"
                     Background="{DynamicResource ControlFillColorDefaultBrush}"
                     Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                     BorderBrush="{DynamicResource ControlElevationBorderBrush}"/>
        </StackPanel>

        <!-- 中间下载状态显示区域 -->
        <Border Grid.Row="1"
                BorderBrush="{DynamicResource ControlElevationBorderBrush}"
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Padding="15">
            <StackPanel>
                <TextBlock Text="下载进度" 
                         FontSize="14" 
                         Margin="0,0,0,10"
                         Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                <ListView x:Name="ProgressListView" 
                         Height="300"
                         Background="Transparent"
                         BorderThickness="0">
                    <ListView.ItemTemplate>
                        <DataTemplate>
                            <StackPanel Width="250" Margin="0,5">
                                <TextBlock Text="{Binding FileName}" 
                                         FontWeight="SemiBold"
                                         Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                <ProgressBar Value="{Binding Progress}"
                                           Maximum="100"
                                           Height="4"
                                           Margin="0,5,0,0"
                                           Foreground="{DynamicResource SystemAccentColorSecondaryBrush}"
                                           Background="{DynamicResource ControlFillColorTertiaryBrush}"/>
                                <TextBlock Text="{Binding Status}"
                                         Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                         FontSize="12"/>
                            </StackPanel>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                    <ListView.ItemContainerStyle>
                        <Style TargetType="ListViewItem">
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Padding" Value="0"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="ListViewItem">
                                        <ContentPresenter/>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </ListView.ItemContainerStyle>
                </ListView>
            </StackPanel>
        </Border>

        <!-- 底部按钮区域 -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    Margin="0,20,0,0">
            <ui:Button x:Name="DownloadButton"
                      Content="开始下载"
                      Click="DownloadButton_Click"
                      Appearance="Primary"
                      Margin="0,0,10,0"/>
            <ui:Button x:Name="CloseButton"
                      Content="关闭"
                      Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>