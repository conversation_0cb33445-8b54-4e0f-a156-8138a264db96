﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace DanDing1.Models
{
    /// <summary>
    /// 组队队友选择配置
    /// </summary>
    internal class TeamKeysModel
    {
        public Dictionary<string, byte[]> TeamKeys { get; set; } = new();
        public Dictionary<string, string> TeamNames { get; set; } = new();

        public TeamKeysModel()
        {
            TeamKeys = XConfig.LoadValueFromFile<Dictionary<string, byte[]>>("TeamKeys") ?? [];
            TeamNames = XConfig.LoadValueFromFile<Dictionary<string, string>>("TeamNames") ?? [];
        }

        public void AddKey(string teamName, byte[] key)
        {
            TeamKeys[teamName] = key;
            XConfig.SaveValueToFile("TeamKeys", TeamKeys);
        }

        public void AddKey(string teamName, byte[] key, string name)
        {
            TeamKeys[teamName] = key;
            TeamNames[teamName] = name;
            XConfig.SaveValueToFile("TeamKeys", TeamKeys);
            XConfig.SaveValueToFile("TeamNames", TeamNames);
        }

        public void RemoveKey(string teamName)
        {
            TeamKeys.Remove(teamName);
            TeamNames.Remove(teamName);
            XConfig.SaveValueToFile("TeamKeys", TeamKeys);
            XConfig.SaveValueToFile("TeamNames", TeamNames);
        }

        public byte[] GetKey_Bytes(string teamName)
        {
            return TeamKeys[teamName];
        }

        public string GetKey_Name(string teamName)
        {
            return TeamNames[teamName];
        }

        /// <summary>
        /// 更新指定队伍的键值。
        /// 使用旧键名查找并更新为新的键名，同时保持字节数组的值不变。
        /// </summary>
        /// <param name="oldKey">旧的键名</param>
        /// <param name="newKey">新的键名</param>
        /// <param name="keyBytes">新的字节数组</param>
        public void UpdateKey(string oldKey, string newKey, byte[] keyBytes)
        {
            if (TeamKeys.ContainsKey(oldKey))
            {
                // 取出旧键的字节数组和名称
                var value = TeamKeys[oldKey];
                TeamKeys.Remove(oldKey); // 移除旧键

                // 添加新键
                TeamKeys[newKey] = keyBytes ?? value; // 如果新值为空，保持旧值不变

                // 更新 TeamNames 对应的名称（如果存在）
                if (TeamNames.ContainsKey(oldKey))
                {
                    var name = TeamNames[oldKey];
                    TeamNames.Remove(oldKey);
                    TeamNames[newKey] = name;
                }

                // 保存更新后的集合
                XConfig.SaveValueToFile("TeamKeys", TeamKeys);
                XConfig.SaveValueToFile("TeamNames", TeamNames);
            }
            else
            {
                throw new KeyNotFoundException($"The key '{oldKey}' was not found in the collection.");
            }
        }
    }
}