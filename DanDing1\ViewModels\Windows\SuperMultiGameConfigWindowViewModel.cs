﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DanDing1.Services;
using DanDing1.Views.Windows;
using XHelper;

namespace DanDing1.ViewModels.Windows
{
    public partial class SuperMultiGameConfigWindowViewModel : ObservableObject, ICloneable
    {
        [ObservableProperty]
        private string _applicationTitle;

        /// <summary>
        /// 任务列表循环次数
        /// </summary>
        [ObservableProperty]
        private string _loopCount = "1";

        /// <summary>
        /// 变速开关
        /// </summary>
        [ObservableProperty]
        private bool _speedSwitch = false;

        /// <summary>
        /// 是否开启通知
        /// </summary>
        [ObservableProperty]
        private bool _notice_IsChecked;

        /// <summary>
        /// 通知方式
        /// </summary>
        [ObservableProperty]
        private List<string> _Notice_Lists = ["邮件", "自定义"];

        /// <summary>
        /// 通知方式选中项目
        /// </summary>
        [ObservableProperty]
        private string _Notice_SelectItem = "邮件";

        /// <summary>
        /// 是否录制本次运行记录
        /// </summary>
        [ObservableProperty]
        private bool _isRecord = false;

        /// <summary>
        /// 是否需要适配体服执行任务
        /// </summary>
        [ObservableProperty]
        private bool _isTifu = false;

        /// <summary>
        /// 结束后关闭模拟器
        /// </summary>
        [ObservableProperty]
        private bool _endCloseGame = false;

        /// <summary>
        /// 对话框结果
        /// </summary>
        [ObservableProperty]
        private bool _dialogResult = false;

        /// <summary>
        /// 请求关闭窗口事件
        /// </summary>
        public event EventHandler<bool> RequestClose;

        public SuperMultiGameConfigWindowViewModel()
        {
            ApplicationTitle = "游戏独立配置";
        }

        [RelayCommand]
        private void Save()
        {
            // 保存配置并设置对话框结果为true
            DialogResult = true;
            // 触发请求关闭窗口事件
            RequestClose?.Invoke(this, true);
        }

        [RelayCommand]
        private void Cancel()
        {
            // 取消操作，设置对话框结果为false
            DialogResult = false;
            // 触发请求关闭窗口事件
            RequestClose?.Invoke(this, false);
        }

        /// <summary>
        /// 打开预设管理窗口
        /// </summary>
        [RelayCommand]
        private void OpenPreset()
        {
            try
            {
                // 创建预设窗口
                var presetWindow = new PresetWindow();
                //置顶窗口
                presetWindow.Topmost = true;
                // 以模态窗口方式显示
                presetWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                XLogger.Error($"打开预设窗口时出错: {ex.Message}");
                // 这里没有InfoBar，只记录错误日志
            }
        }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}