﻿using DamoControlKit.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace DamoControlKit.Model
{
    /// <summary>
    /// 多点找色类
    /// </summary>
    public class MultiColor(Position? pos, string first, string offset, double sim, int dir) : IFind
    {
        public new string ToString => GetIdentifier();

        /// <summary>
        /// 扫描方向
        /// </summary>
        public int Dir { get; set; } = dir;

        /// <summary>
        /// 首偏色点颜色
        /// </summary>
        public string First { get; set; } = first;

        /// <summary>
        /// 是否准备好
        /// </summary>
        public bool Isready => First != null;

        /// <summary>
        /// 偏移颜色描述
        /// </summary>
        public string Offset { get; set; } = offset;

        /// <summary>
        /// 识别位置
        /// </summary>
        public Position Pos { get; set; } = pos ?? new(0, 0, 2000, 2000);

        /// <summary>
        /// 相似度
        /// </summary>
        public double Sim { get; set; } = sim;

        public dmsoft? dmsoft { get; set; }

        /// <summary>
        /// 唯一标识符 队列用
        /// </summary>
        private string Identifier { get; set; } = "";

        public bool Await(dmsoft? x, int period)
        {
            dmsoft? xtmp = (x ?? dmsoft) ?? throw new Exception("插件似乎未工作，错误！");
            int count = period / 100;
            int new_Count = 0;
            while (new_Count < count)
            {
                if (Find(null))
                    return true;
                new_Count++;
                xtmp.delay(100);
            }
            return false;
        }

        public bool Find(dmsoft? x)
        {
            dmsoft? xtmp = (x ?? dmsoft) ?? throw new Exception("插件似乎未工作，错误！");
            return xtmp.FindMultiColor(Pos.X, Pos.Y, Pos.X1, Pos.Y1, First, Offset, Sim, Dir, out _, out _) == 1;
        }

        public Point? FindPoint(dmsoft? x)
        {
            dmsoft? xtmp = (x ?? dmsoft) ?? throw new Exception("插件似乎未工作，错误！");
            if (xtmp.FindMultiColor(Pos.X, Pos.Y, Pos.X1, Pos.Y1, First, Offset, Sim, Dir, out int pointx, out int pointy) == 1)
                return new(pointx, pointy);
            return null;
        }

        /// <summary>
        /// 获取唯一标识符
        /// </summary>
        /// <returns></returns>
        public string GetIdentifier() => Identifier == "" ? First : Identifier;

        /// <summary>
        /// 设置唯一标识符
        /// </summary>
        public void SetIdentifier(string identifier)
        {
            Identifier = identifier;
        }

        /// <summary>
        /// 设置唯一标识符
        /// </summary>
        /// <param name="identifier">唯一标识符</param>
        /// <param name="identifiers">已有的标识符列表</param>
        /// <returns>设置失败返回False</returns>
        public bool SetIdentifier(string identifier, List<string> identifiers)
        {
            if (identifiers.Contains(identifier))
                return false;
            Identifier = identifier;
            return true;
        }

        public bool SetXsoft()
        {
            throw new NotImplementedException();
        }

        public bool SetXsoft(dmsoft x)
        {
            dmsoft = x; return true;
        }
    }

    /// <summary>
    /// 多点找色类集合
    /// </summary>
    public class MultiColors
    {
        public int Count => MultiColorList.Count;

        public MultiColors()
        { }

        public MultiColors(MultiColors multiColors)
        { Add(multiColors); }

        public MultiColors(List<MultiColor> multiColors)
        { Add(multiColors); }

        public List<MultiColor> MultiColorList { get; set; } = [];

        public void Add(MultiColor multiColor) => MultiColorList.Add(multiColor);

        public void Add(List<MultiColor> multiColors) => MultiColorList.AddRange(multiColors);

        public void Add(MultiColors multiColors) => MultiColorList.AddRange(multiColors.MultiColorList);

        public void SetXsoft(dmsoft x) => MultiColorList.ForEach(t => t.SetXsoft(x));

        public bool Remove(MultiColor multiColor)
        {
            if (MultiColorList.IndexOf(multiColor) == -1)
                return false;
            MultiColorList.Remove(multiColor);
            return true;
        }

        public List<string>? GetAllIdentifier()
        {
            List<string> allIdentifier = [];
            MultiColorList.ForEach(t => allIdentifier.Add(t.GetIdentifier()));
            if (allIdentifier.Count == 0) return null;
            return allIdentifier;
        }

        /// <summary>
        /// 查找所有多点找色 遇到成功即返回True
        /// </summary>
        /// <returns></returns>
        public bool FindAll()
        {
            foreach (var item in MultiColorList)
                if (item.Find(null))
                    return true;
            return false;
        }

        /// <summary>
        /// 查找所有多点找色 遇到成功即返回True
        /// </summary>
        /// <returns></returns>
        public bool FindAll(out int x, out int y)
        {
            x = y = -1;
            Point? p = null;
            foreach (var item in MultiColorList)
                if ((p = item.FindPoint(null)) is not null)
                {
                    x = p.X;
                    y = p.Y;
                    return true;
                }
            return false;
        }
    }
}