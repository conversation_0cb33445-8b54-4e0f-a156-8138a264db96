﻿using DanDing1.Commands;
using DanDing1.Helpers;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;
using Wpf.Ui.Abstractions.Controls;
using Wpf.Ui.Controls;
using XHelper;
using XHelper.Models;

namespace DanDing1.ViewModels.Pages
{
    public partial class LogViewModel : ObservableObject, INavigationAware
    {
        [ObservableProperty]
        private string _SelectShowLog = "全部";

        private ObservableCollection<LogModel> _log = [];

        public ObservableCollection<LogModel> Log
        {
            get
            {
                return XLogger.GetLogObj(SelectShowLog);
            }
            set { SetProperty(ref _log, value); }
        }

        /// <summary>
        /// 更新日志源
        /// </summary>
        public void UpLogSource()
        {
            Log = XLogger.GetLogObj(SelectShowLog);
        }

        private Commands.Commands? commands;

        /// <summary>
        /// 命令控制
        /// </summary>
        public async Task<bool> CommandControl(string commandStr)
        {
            if (string.IsNullOrEmpty(commandStr))
                return false;
            commands ??= new();
            if (!await commands.Do(commandStr))
            {
                XLogger.Warn($"无法响应命令[{commandStr}]，它不在命令清单中！");
                return false;
            }
            return true;
        }

        public Task OnNavigatedToAsync()
        {
            return Task.CompletedTask;
        }

        public Task OnNavigatedFromAsync()
        {
            return Task.CompletedTask;
        }
    }
}