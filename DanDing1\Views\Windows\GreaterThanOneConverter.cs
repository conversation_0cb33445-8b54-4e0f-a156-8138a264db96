using System;
using System.Globalization;
using System.Windows.Data;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// 将数值转换为布尔值的转换器，用于判断数值是否大于1
    /// </summary>
    public class GreaterThanOneConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int intValue)
            {
                return intValue > 1;
            }

            if (value is double doubleValue)
            {
                return doubleValue > 1;
            }

            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}