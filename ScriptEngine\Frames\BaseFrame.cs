﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace ScriptEngine.Frames
{
    public class BaseFrame
    {
        /// <summary>
        /// 控制任务结束
        /// </summary>
        internal CancellationTokenSource ct;

        /// <summary>
        /// 用户参数
        /// </summary>
        public DDBuilder DB { get; }

        public BaseFrame(CancellationTokenSource ct, DDBuilder dB)
        {
            this.ct = ct;
            DB = dB;
        }

        internal void Delay(int ms)
        {
            int delayStep = 1000; // 检查间隔，以毫秒为单位
            int totalTicks = (int)(ms * TimeSpan.TicksPerMillisecond); // 总延迟时间的刻度值

            for (long ticks = 0; ticks < totalTicks; ticks += delayStep * TimeSpan.TicksPerMillisecond)
            {
                if (ct.Token.IsCancellationRequested)
                {
                    DB.Log.Info("用户结束了任务..");
                    throw new TaskCanceledException();
                }
                Task.Delay(delayStep).Wait();
            }
        }
    }
}