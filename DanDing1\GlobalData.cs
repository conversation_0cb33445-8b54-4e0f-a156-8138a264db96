﻿using DanDing1.Models;
using DanDing1.ViewModels.Base;

namespace DanDing1
{
    /// <summary>
    /// 全局数据集合
    /// </summary>
    internal class GlobalData
    {
        /// <summary>
        /// 活动结束时间
        /// </summary>
        public static DateTime ActiveEndTime = new(2025, 6, 10, 23, 59, 59);

        /// <summary>
        /// 是否为内测版本
        /// </summary>
        public static bool IsBetaVersion = false;

        /// <summary>
        /// 程序版本
        /// </summary>
        public static string Ver = "1.4.19";

        /// <summary>
        /// ws服务连接地址
        /// wss://ws.danding.vip
        /// ws://127.0.0.1:8080
        /// </summary>
        public static string WS_HOST = "wss://ws.danding.vip";

        /// <summary>
        /// 设置主页超级多开的进行时开关
        /// </summary>
        public SetRunningUICallBack? SetRunningUI;

        private static readonly GlobalData _instance = new GlobalData();
        private string _picServerVer;

#pragma warning disable CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑声明为可以为 null。

        private GlobalData()
        {
            UserProfile = UserProfileModel.Load();
        }

#pragma warning restore CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑声明为可以为 null。

        ~GlobalData()
        {
            CheckTimer?.Dispose();
            LogUploadTimer?.Dispose();
        }

        public static GlobalData Instance => _instance;

        /// <summary>
        /// App设置
        /// </summary>
        public AppConfig appConfig { get; set; } = new();

        /// <summary>
        /// 账号状态检查定时器
        /// </summary>
        public Timer CheckTimer { get; set; }

        public GameViewBaseModel? Game1RunningConfig { get; set; }

        public GameViewBaseModel? Game2RunningConfig { get; set; }

        public GameViewBaseModel? Game3RunningConfig { get; set; }

        public GameViewBaseModel? Game4RunningConfig { get; set; }

        /// <summary>
        /// 全局数据字典
        /// </summary>
        public Dictionary<string, object> Global_Data { get; set; } = new();

        /// <summary>
        /// MuMu配置初始化定时器
        /// </summary>
        public Timer InitMuMuConfigsTimer { get; set; }

        /// <summary>
        /// 日志上传定时器
        /// </summary>
        public Timer LogUploadTimer { get; set; }

        /// <summary>
        /// 日志窗口控制对象
        /// </summary>
        public LogWindowsControl logWinControl { get; set; } = new();

        /// <summary>
        /// 主窗口句柄
        /// </summary>
        public nint MainWindowsHwnd { get; set; }

        /// <summary>
        /// 图库版本
        /// </summary>
        public string PicServerVer
        {
            get
            {
                string s = Instance.appConfig.Info.Now_Ver;
                if (!string.IsNullOrEmpty(_picServerVer))
                    s = _picServerVer;
                return s;
            }
            set { _picServerVer = value; }
        }

        /// <summary>
        /// 用户偏好配置
        /// </summary>
        public UserConfigModel UserConfig { get; set; } = new();

        /// <summary>
        /// 用户行为统计数据
        /// </summary>
        public UserProfileModel UserProfile { get; private set; }

        /// <summary>
        /// 游戏视图模型集合
        /// </summary>
        /// <param name="gameIndex"></param>
        /// <returns></returns>
        internal GameViewBaseModel? GetGameViewModel(int gameIndex)
        {
            switch (gameIndex)
            {
                case 1:
                    return Game1RunningConfig;

                case 2:
                    return Game2RunningConfig;

                case 3:
                    return Game3RunningConfig;

                case 4:
                    return Game4RunningConfig;

                default:
                    return null;
            }
        }
    }
}