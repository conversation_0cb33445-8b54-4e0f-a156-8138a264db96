﻿using DanDing1.Views.Windows;
using ShareX.MediaLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DanDing1.Models
{
    /// <summary>
    /// 日志小窗控制类
    /// </summary>
    internal class LogWindowsControl
    {
        private List<string> GameNameList = ["全部", "游戏1", "游戏2", "游戏3", "游戏4"];

        private bool DontContinue(string name) => !GameNameList.Contains(name);

        private Dictionary<string, bool> WinShowStatus = new()
        {
            {"全部",false },
            {"游戏1",false },
            {"游戏2",false },
            {"游戏3",false },
            {"游戏4",false },
        };

        private Dictionary<string, LogWindow> LogWins = new()
        {
            {"全部",new(new("全部")) },
            {"游戏1",new(new("游戏1")) },
            {"游戏2",new(new("游戏2")) },
            {"游戏3",new(new("游戏3")) },
            {"游戏4",new(new("游戏4")) },
        };

        public Action ClosedCallBack;

        public LogWindowsControl()
        {
            foreach (var item in LogWins)
                item.Value.Closing += Value_Closing;
        }

        private void Value_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            LogWindow? window = sender as LogWindow;
            if (window is null)
                return;

            // 取消关闭操作
            e.Cancel = true;

            // 隐藏窗口
            window.Hide();
            WinShowStatus[window.ViewModel.GameName] = false;
            ClosedCallBack?.Invoke();
        }

        /// <summary>
        /// 展示窗口
        /// </summary>
        /// <param name="gameName"></param>
        public void ShowLogWin(string gameName)
        {
            if (DontContinue(gameName)) return;
            if (!WinShowStatus[gameName])
            {
                LogWins[gameName].Show();
                WinShowStatus[gameName] = true;
            }
        }

        /// <summary>
        /// 关闭窗口
        /// </summary>
        /// <param name="gameName"></param>
        public void CloseLogWin(string gameName)
        {
            if (DontContinue(gameName)) return;
            if (WinShowStatus[gameName])
            {
                LogWins[gameName].Close();
                WinShowStatus[gameName] = false;
            }
        }

        /// <summary>
        /// 返回日志窗口开启状态
        /// </summary>
        /// <param name="gameName"></param>
        /// <returns></returns>
        public bool GetLogWinStatus(string gameName)
        {
            if (DontContinue(gameName)) return false;
            return WinShowStatus[gameName];
        }
    }
}