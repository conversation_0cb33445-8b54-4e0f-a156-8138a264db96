﻿#pragma checksum "..\..\..\..\..\..\Views\Windows\TaskConfigWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "436E42929BDF1E8CF1998C1F6DC886EC18BA9F2F"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Views.Windows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Windows {
    
    
    /// <summary>
    /// TaskConfigWindow
    /// </summary>
    public partial class TaskConfigWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 37 "..\..\..\..\..\..\Views\Windows\TaskConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox TaskListBox;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\..\..\Views\Windows\TaskConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PlanComboBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/windows/taskconfigwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\Views\Windows\TaskConfigWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TaskListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 38 "..\..\..\..\..\..\Views\Windows\TaskConfigWindow.xaml"
            this.TaskListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TaskListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.PlanComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 263 "..\..\..\..\..\..\Views\Windows\TaskConfigWindow.xaml"
            this.PlanComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PlanComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 272 "..\..\..\..\..\..\Views\Windows\TaskConfigWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ImportButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 277 "..\..\..\..\..\..\Views\Windows\TaskConfigWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 286 "..\..\..\..\..\..\Views\Windows\TaskConfigWindow.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DelButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 295 "..\..\..\..\..\..\Views\Windows\TaskConfigWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ImportCodeButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 300 "..\..\..\..\..\..\Views\Windows\TaskConfigWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.FromCloudButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 305 "..\..\..\..\..\..\Views\Windows\TaskConfigWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShareConfigButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 310 "..\..\..\..\..\..\Views\Windows\TaskConfigWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MySharesButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

