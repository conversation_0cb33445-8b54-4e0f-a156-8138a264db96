﻿using DamoControlKit.Model;
using ScriptEngine.Factorys;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using XHelper;

namespace ScriptEngine.Tasks
{
    internal class YuHun : BaseTask
    {
        /// <summary>
        /// 是否开启标记功能
        /// </summary>
        private bool Biaoji = false;

        private bool BiaoJi_Status = false;

        private List<string> DontSendLog = ["标记", "喂食"];

        /// <summary>
        /// 关卡层数
        /// </summary>
        private int Level = -1;

        /// <summary>
        /// 任务次数
        /// </summary>
        private int Ncount = 0;

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "御魂");
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Ncount = GetConfig.Count;
            GetConfig.Others.TryGetValue("Level", out string? s);
            try { Level = s is null ? -1 : int.Parse(s); } catch (Exception) { Level = -1; }
            GetConfig.Others.TryGetValue("Biaoji", out s);
            try { Biaoji = s is not null && bool.Parse(s); } catch (Exception) { Biaoji = false; }

            if (Level == -1) log.Warn("当前御魂任务并未设置层数！按最后一次战斗的层数战斗...");

            //场景判断
            string nows = Scene.NowScene;
            Sleep(500);

            if (nows != "御魂")
            {
                if (Scene.NowScene == "御魂")
                    goto Start;

                //先去探索，获取突破卷数量
                if (!Scene.TO.TanSuo())
                {
                    log.Warn("御魂任务无法继续，当前游戏所在场景未知，请调整到庭院或探索主界面开始脚本！");
                    return;
                }
                var tupo_Count = Fast.Scence.TanSuo_GetTuPoCount();
                log.Debug("本地Ocr识别突破卷结果：" + tupo_Count);
                if (tupo_Count == 30)
                    Tmp.Do_Tupo(); //执行临时执行突破

                //再去御魂
                Scene.TO.YuHun();
            }
        Start:

            //选择层数
            if (Level != -1) SelectLevel();
            main();
            UserNotificationMessage = $"共{Ncount}次战斗，战斗成功{count}次。";
            if (Db.Data.Get("AutoBuff") is bool autoBuff && autoBuff)
                RunTaskFactory.Run<BuffTask>(Db, Dm, Ct, 1, "自动Buff", new() { { "所有", "0" } });
        }

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            //点击开始
            Fast.Click("1115,600,1199,676");
            log.Info("战斗点击开始");
            var pics = Mp.Filter("御魂.");
            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                FindOkFun(p.Name, p);
                if (!DontSendLog.Any(p.Name.Contains)) log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("胜利") || p.Name.Contains("达摩"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败"))
                {
                    ret_bol = false;
                    isbreak = true;
                }
            }
            if (ret_bol)
                Combat_End();//等待Yuhun界面

            return ret_bol;
        }

        /// <summary>
        /// 御魂胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("御魂");
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                if (p.Name.Contains("挑战"))
                {
                    isbreak = true;
                    continue;
                }
                log.Info($"执行点击：{p._Name}");
                p.Click();
            }
        }

        /// <summary>
        /// 结束任务方法
        /// </summary>
        private void EndCallBack()
        {
            log.Info("执行御魂任务收尾方法,等待返回后,退出到探索。");
            var mps = new MemPics()
                .Add(Mp.Filter("御魂.准备"))
                .Add(Mp.Filter("御魂.达摩"));

            while (!Mp.Filter("御魂.挑战").FindAll())
            {
                mps.FindAllAndClick();
                Sleep(1000);
            }
            log.Info("退出到探索..");
            Sleep(500);
            Fast.Click("32,22,76,62");
            Sleep(1200);
        }

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            //判断喂食
            if (Db.UserConfigs.TryGetValue("YuhunFood", out object? _T) && _T is bool T && T && name.Contains("喂食"))
            {
                log.Info("宠物饿了，开始自动喂食");
                Sleep(1000);
                pic.FindAndClick(Dm);
                Sleep(500);
                Fast.Click(925, 528, 973, 566);
                Sleep(1500);
                Fast.Click(695, 105, 846, 151);
                if (Random.Shared.Next(0, 100) < 50)
                    Fast.Click(695, 105, 846, 151);
                return false;
            }

            if (Biaoji && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.2秒，标记位置：5号位");
                Sleep(200);
                Fast.Click(1013, 454, 1082, 551);
                return false;
            }
            return true;
        }

        private int count = 0;

        /// <summary>
        /// 主流程
        /// </summary>
        private void main()
        {
            if (UserConfig_Preset != null)
            {
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入式神录，开始应用预设{UserConfig_Preset}");
                Fast.Click(830, 649, 864, 681);
                Sleep(1500);
                Tmp.Do_Preset(preset);
            }
        Re:
            while (count < Ncount)
            {
                if (!WaitYuhun()) goto Re;

                Anti.RandomDelay();//防封等待
                if (Anti.ShouldTriggerRandomYysAuto())//判断是否需要穿插纸人
                {
                    Fast.Click(825, 562, 859, 595); //打开小纸人
                    Sleep(500);
                    int do_Count = Random.Shared.Next(1, 5); // 1-4次随机次数
                    if (Tmp.Do_YysAuto(do_Count))
                    {
                        count += do_Count;
                        log.Info($"触发随机穿插纸人战斗结束..脚本继续接管..");
                        Anti.ResetRandomYysAuto();
                        goto Re;
                    }
                    else Sleep(1000);
                }
                if (Db.PendingTimerTask) //执行定时任务
                {
                    Db.PendingTimerTask = false;
                    log.Info("暂停当前任务，执行定时任务，退出到探索..");
                    EndCallBack();
                    Db?.TimerTask?.DoAllTask();
                    Sleep(1000);
                    throw new Exception("定时任务执行结束，重新执行当前的主任务..");
                }
                Tmp.Do_ClearYuHun(); //判断是否需要执行清除御魂

                if (Combat())
                {
                    count++;
                    log.Info($"御魂战斗胜利，战斗次数：{count}/{Ncount}");
                }
                else
                {
                    log.Warn($"御魂战斗失败，请检查您的队伍配置是否正常！战斗次数：{count}/{Ncount}");
                    Defeated();
                }
            }
            EndCallBack();
        }

        /// <summary>
        /// 层数选择
        /// </summary>
        private void SelectLevel()
        {
            //识别层数
            log.Info($"开始识别层数，选择层数 {Level}");
            if (Level < 5)
                Operational.Slide_Pos(new DamoControlKit.Model.Point(316, 170), new(333, 585));
            Sleep(500);

            string color = DynamicData.DictsColor[0];
            int it = Dm.FindStrFast(278, 134, 321, 624, Level.ToString(), color, 0.9, out int x, out int y);
            log.Debug($"识别层数 {Level} 结果：" + it);
            if (it < 0)
            {
                //滑动，尝试
                if (Level < 5)
                    Operational.Slide_Pos(new DamoControlKit.Model.Point(316, 170), new(333, 585));
                else if (Level > 8)
                    Operational.Slide_Pos(new DamoControlKit.Model.Point(333, 585), new(316, 170));

                Sleep(1000);
                log.Info($"重新识别层数 {Level}");
                it = Dm.FindStrFast(278, 134, 321, 624, Level.ToString(), color, 0.9, out x, out y);
                //没识别到就直接开打
                if (it < 0)
                {
                    XLogger.Warn($"识别层数 {Level} 失败，可能已经选中！继续使用当前层数战斗!");
                    return;
                }
            }
            //选择
            log.Info($"选择层数 {Level}.");
            Operational.Click(x, y);
            Sleep(500);
        }

        /// <summary>
        /// 等待御魂开始界面
        /// </summary>
        /// <returns></returns>
        private bool WaitYuhun()
        {
            var pics = Mp.Filter("挑战");
            if (pics.Wait()) return true;
            return false;
        }
    }
}