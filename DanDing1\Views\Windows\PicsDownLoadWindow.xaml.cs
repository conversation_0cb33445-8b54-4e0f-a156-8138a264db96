﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using XHelper.DanDingNet;
using Path = System.IO.Path;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// PicsDownLoadWindow.xaml 的交互逻辑
    /// </summary>
    public partial class PicsDownLoadWindow : Window
    {
        private ObservableCollection<DownloadItem> _downloadItems;
        private const string BASE_PATH = @"D:\DD_Core";
        private readonly string[] FILE_NAMES = new[] { "Dicts.json", "Pics.json", "Pos.json" };

        public PicsDownLoadWindow()
        {
            // 检查D盘是否存在
            if (!CheckDriveExists())
            {
                MessageBox.Show("未检测到D盘，无法继续操作！\n请确保D盘可用后再试。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Close();
                return;
            }

            InitializeComponent();
            _downloadItems = new ObservableCollection<DownloadItem>();
            ProgressListView.ItemsSource = _downloadItems;
            LoadVersionsAsync();

            // 添加版本选择变更事件
            VersionComboBox.SelectionChanged += VersionComboBox_SelectionChanged;
        }

        private bool CheckDriveExists()
        {
            try
            {
                DriveInfo[] drives = DriveInfo.GetDrives();
                return drives.Any(drive =>
                    drive.IsReady &&
                    drive.Name.StartsWith("D:", StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception)
            {
                return false;
            }
        }

        private void VersionComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (VersionComboBox.SelectedItem != null)
            {
                string selectedVersion = VersionComboBox.SelectedItem.ToString()!;
                string versionPath = Path.Combine(BASE_PATH, selectedVersion);

                // 检查目录是否存在
                if (Directory.Exists(versionPath))
                {
                    DownloadButton.Content = "覆盖下载";
                }
                else
                {
                    DownloadButton.Content = "开始下载";
                }
            }
        }

        private async void LoadVersionsAsync()
        {
            var versions = await GlobalData.Instance.appConfig.dNet.System.GetPicVersionsAsync();
            if (versions != null && versions.Any())
            {
                VersionComboBox.ItemsSource = versions;
                VersionComboBox.SelectedIndex = 0;
            }
            else
            {
                MessageBox.Show("获取版本列表失败，请检查网络连接！", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Close();
            }
        }

        private async void DownloadButton_Click(object sender, RoutedEventArgs e)
        {
            if (VersionComboBox.SelectedItem == null)
            {
                MessageBox.Show("请先选择版本！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            DownloadButton.IsEnabled = false;
            string selectedVersion = VersionComboBox.SelectedItem.ToString()!;
            string versionPath = Path.Combine(BASE_PATH, selectedVersion);
            bool isOverwriting = Directory.Exists(versionPath);

            try
            {
                // 创建目录（如果不存在）
                Directory.CreateDirectory(versionPath);

                // 如果是覆盖下载，先删除已存在的文件
                if (isOverwriting)
                {
                    foreach (var fileName in FILE_NAMES)
                    {
                        string filePath = Path.Combine(versionPath, fileName);
                        if (File.Exists(filePath))
                        {
                            File.Delete(filePath);
                        }
                    }
                }

                // 清空并初始化下载列表
                _downloadItems.Clear();
                foreach (var fileName in FILE_NAMES)
                {
                    _downloadItems.Add(new DownloadItem { FileName = fileName });
                }

                // 开始下载
                for (int i = 0; i < FILE_NAMES.Length; i++)
                {
                    var item = _downloadItems[i];
                    item.Status = "正在下载...";
                    item.Progress = 30;

                    var content = await GlobalData.Instance.appConfig.dNet.System.GetPicFileAsync(selectedVersion, FILE_NAMES[i]);
                    if (content != null)
                    {
                        item.Progress = 60;
                        string filePath = Path.Combine(versionPath, FILE_NAMES[i]);

                        // 处理内容：去除首尾引号并转换换行符
                        content = content.Trim('"').Replace("\\n", "\n");

                        await File.WriteAllTextAsync(filePath, content, Encoding.UTF8);

                        item.Progress = 100;
                        item.Status = "下载完成";
                    }
                    else
                    {
                        item.Status = "下载失败";
                        MessageBox.Show($"下载 {FILE_NAMES[i]} 失败！", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }

                MessageBox.Show($"{(isOverwriting ? "覆盖" : "下载")}完成！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"下载过程中发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                DownloadButton.IsEnabled = true;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }

    public class DownloadItem : INotifyPropertyChanged
    {
        private string _fileName = "";
        private string _status = "等待下载";
        private double _progress = 0;

        public string FileName
        {
            get => _fileName;
            set
            {
                _fileName = value;
                OnPropertyChanged(nameof(FileName));
            }
        }

        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        public double Progress
        {
            get => _progress;
            set
            {
                _progress = value;
                OnPropertyChanged(nameof(Progress));
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
