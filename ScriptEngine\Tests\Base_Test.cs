﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace ScriptEngine.Tests
{
    internal class Base_Test
    {
        /// <summary>
        /// 测试函数的启停
        /// </summary>
        public static Dictionary<string, bool> OnOrOff = new()
        {
            { "ScriptFrame_Run_Test",false}
        };

        public Base_Test(string TestName)
        {
            XLogger.Debug(TestName + " 测试方法被调用！(生产模式禁止使用)");
        }

        /// <summary>
        /// 测试
        /// </summary>
        public virtual void Test()
        {
        }
    }
}