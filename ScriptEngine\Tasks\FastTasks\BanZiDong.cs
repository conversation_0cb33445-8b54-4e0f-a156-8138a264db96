﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;

namespace ScriptEngine.Tasks.FastTasks
{
    internal class BanZiDong : BaseTask
    {
        private Dictionary<string, string> _Resources = new Dictionary<string, string>();

        // 添加对资源字典的锁对象
        private readonly object _resourceLock = new object();

        /// <summary>
        /// 是否开启标记功能
        /// </summary>
        private bool Biaoji = false;

        private bool BiaoJi_Status = false;

        private List<string> DontSendLog = ["标记", "喂食"];

        // 文件系统监视器
        private FileSystemWatcher? _fileWatcher;

        // 任务自动结束时间（分钟）
        private double _autoEndTimeMinutes = 0;

        // 任务开始时间
        private DateTime _startTime;

        // 用于跟踪警告是否已显示
        private bool _warningDisplayed = false;

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "半自动");
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            GetConfig.Others.TryGetValue("Biaoji", out string? s);
            try { Biaoji = s is not null && bool.Parse(s); } catch (Exception) { Biaoji = false; }

            // 读取自动结束时间设置
            GetConfig.Others.TryGetValue("AutoEndTime", out string? autoEndTimeStr);
            if (!string.IsNullOrEmpty(autoEndTimeStr) && double.TryParse(autoEndTimeStr, out double autoEndTime) && autoEndTime > 0)
            {
                _autoEndTimeMinutes = autoEndTime;
                log.Info_Green($"您当前正在执行的任务是：半自动任务；将在{_autoEndTimeMinutes}分钟后自动结束");
            }
            else
            {
                log.Info_Green("您当前正在执行的任务是：半自动任务；未设置自动结束时间");
            }

            // 记录开始时间
            _startTime = DateTime.Now;

            // 重置警告标志
            _warningDisplayed = false;

            log.Info_Green("开始检查本地图片资源···");
            InitDirPics();
            Main();
        }

        /// <summary>
        /// 任务结束或销毁时清理资源
        /// </summary>
        public void Destroy()
        {
            // 关闭并释放文件监视器
            if (_fileWatcher != null)
            {
                try
                {
                    // 移除事件处理程序，防止内存泄漏
                    _fileWatcher.Created -= OnResourceChanged;
                    _fileWatcher.Deleted -= OnResourceChanged;
                    _fileWatcher.Renamed -= OnResourceRenamed;

                    // 停止监视
                    _fileWatcher.EnableRaisingEvents = false;

                    // 释放资源
                    _fileWatcher.Dispose();
                    _fileWatcher = null;

                    log.Info("资源监控已停止，文件监视器已释放");
                }
                catch (Exception ex)
                {
                    log.Debug($"释放文件监视器时出错: {ex.Message}");
                }
            }

            // 清理资源字典
            lock (_resourceLock)
            {
                _Resources.Clear();
            }
        }

        private void InitDirPics()
        {
            string dir = AppDomain.CurrentDomain.BaseDirectory + "Resources";
            if (!Directory.Exists(dir))
            {
                log.Debug("Resources目录不存在，无法加载图片资源");
                return;
            }

            // 初始加载所有资源
            LoadResourceFiles(dir);

            // 设置文件系统监视器
            if (_fileWatcher == null)
            {
                _fileWatcher = new FileSystemWatcher(dir)
                {
                    NotifyFilter = NotifyFilters.FileName | NotifyFilters.DirectoryName | NotifyFilters.LastWrite,
                    Filter = "*.bmp",
                    IncludeSubdirectories = true,
                    EnableRaisingEvents = true
                };

                // 添加事件处理程序
                _fileWatcher.Created += OnResourceChanged;
                _fileWatcher.Deleted += OnResourceChanged;
                _fileWatcher.Renamed += OnResourceRenamed;

                log.Info("已启动资源目录监控，将实时更新资源字典");
            }
        }

        /// <summary>
        /// 加载资源文件到字典
        /// </summary>
        private void LoadResourceFiles(string dir)
        {
            // 获取所有.bmp文件
            var bmpFiles = Directory.GetFiles(dir, "*.bmp", SearchOption.AllDirectories);
            int bmpCount = bmpFiles.Length;

            lock (_resourceLock)
            {
                // 清空资源字典并重新加载
                _Resources.Clear();

                // 遍历所有bmp文件，将名称和路径加入到_Resources字典中
                foreach (var bmpFile in bmpFiles)
                {
                    string fileName = Path.GetFileNameWithoutExtension(bmpFile);
                    _Resources[fileName] = bmpFile;
                }
            }

            log.Info($"已加载{bmpCount}张图片资源到字典中，将一起查找并点击");
        }

        /// <summary>
        /// 处理资源文件创建或删除事件
        /// </summary>
        private void OnResourceChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                if (e.ChangeType == WatcherChangeTypes.Created)
                {
                    // 添加新资源
                    string fileName = Path.GetFileNameWithoutExtension(e.FullPath);
                    lock (_resourceLock)
                    {
                        _Resources[fileName] = e.FullPath;
                    }
                    log.Info($"检测到新增资源：{fileName}");
                }
                else if (e.ChangeType == WatcherChangeTypes.Deleted)
                {
                    // 移除已删除资源
                    string fileName = Path.GetFileNameWithoutExtension(e.FullPath);
                    lock (_resourceLock)
                    {
                        if (_Resources.ContainsKey(fileName))
                        {
                            _Resources.Remove(fileName);
                        }
                    }
                    log.Info($"检测到资源删除：{fileName}");
                }
            }
            catch (Exception ex)
            {
                log.Debug($"处理资源变更时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理资源文件重命名事件
        /// </summary>
        private void OnResourceRenamed(object sender, RenamedEventArgs e)
        {
            try
            {
                string oldFileName = Path.GetFileNameWithoutExtension(e.OldFullPath);
                string newFileName = Path.GetFileNameWithoutExtension(e.FullPath);

                lock (_resourceLock)
                {
                    // 移除旧名称资源
                    if (_Resources.ContainsKey(oldFileName))
                    {
                        _Resources.Remove(oldFileName);
                    }

                    // 添加新名称资源
                    _Resources[newFileName] = e.FullPath;
                }

                log.Info($"检测到资源重命名：{oldFileName} -> {newFileName}");
            }
            catch (Exception ex)
            {
                log.Debug($"处理资源重命名时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            if (Biaoji && (name.Contains("达摩") || name.Contains("胜利") || name.Contains("失败") || name.Contains("战斗")))
            {
                log.Info("标记位已解锁；");
                BiaoJi_Status = false;
                return false;
            }
            if (Biaoji && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.2秒，标记位置：5号位，标记位已上锁；");
                Sleep(200);
                Fast.Click(985, 481, 1034, 543);
                return false;
            }
            return true;
        }

        private void Main()
        {
            BiaoJi_Status = false;
            try
            {
                while (!Ct.IsCancellationRequested)
                {
                    Sleep(500);

                    // 创建资源字典的安全副本，避免遍历时修改异常
                    Dictionary<string, string> resourcesCopy;
                    lock (_resourceLock)
                    {
                        resourcesCopy = new Dictionary<string, string>(_Resources);
                    }

                    //检查本地资源（使用副本进行遍历）
                    foreach (var resource in resourcesCopy)
                    {
                        if (Dm.FindPic(0, 0, 2000, 2000, resource.Value, "202020", 0.9, 0, out int x, out int y) != -1)
                        {
                            log.Info("【用户资源】已找到：" + resource.Key + " 执行点击..");
                            Fast.Click(x, y);
                            Sleep(500);
                        }
                    }

                    //检查系统资源
                    foreach (var pic in Mp.PicList)
                    {
                        if (pic.Find())
                        {
                            FindOkFun(pic.Name, pic);
                            if (!DontSendLog.Any(pic.Name.Contains))
                                log.Info("【系统资源】已找到：" + pic._Rename + " 执行点击..");
                            pic.Click();
                            if (pic.Name.Contains("默认邀请"))
                            {
                                log.Info("点击 默认邀请-\"确定\"");
                                Fast.Click(712, 414, 814, 451);
                            }
                            Sleep(500);
                        }
                    }

                    // 检查任务是否自动结束
                    if (_autoEndTimeMinutes > 0)
                    {
                        DateTime currentTime = DateTime.Now;
                        TimeSpan elapsedTime = currentTime - _startTime;

                        // 计算剩余时间并在特定时间点提示用户
                        double remainingMinutes = _autoEndTimeMinutes - elapsedTime.TotalMinutes;
                        if (remainingMinutes <= 1 && remainingMinutes > 0.9 && !_warningDisplayed)
                        {
                            log.Warn($"半自动任务将在1分钟后自动结束");
                            _warningDisplayed = true; // 标记警告已显示
                        }

                        // 达到设定时间后自动结束任务
                        if (elapsedTime.TotalMinutes >= _autoEndTimeMinutes)
                        {
                            log.Info_Green($"已达到设定的{_autoEndTimeMinutes}分钟，半自动任务自动结束");
                            // 通知取消任务
                            Ct.Cancel();
                            break;
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                log.Info("任务已取消");
            }
            catch (Exception ex)
            {
                log.Error($"执行过程中出错: {ex.Message}");
            }
            finally
            {
                // 确保在循环结束时清理资源
                Destroy();
            }
        }
    }
}