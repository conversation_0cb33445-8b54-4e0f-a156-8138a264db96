﻿using DamoControlKit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace CmdTester
{
    internal class D
    {
        public D()
        {
            Console.WriteLine("TestD COTR Init...");
        }

        /// <summary>
        /// 绑定测试
        /// </summary>
        /// <exception cref="Exception"></exception>
        public dmsoft? b(int _hwnd = 0)
        {
            int hwnd;
            if (_hwnd == 0)
            {
                Console.Write("输入需要绑定窗口的句柄：");
                hwnd = int.Parse(Console.ReadLine() ?? "");
            }
            else
                hwnd = _hwnd;

            dmsoft x;

            //注册大漠插进
            if (!DamoKit.Reg("xsllovemlj83e580fa0fd8214b5b9a70864794b53a", out string errorStr))
                throw new Exception("注册失败：" + errorStr);

            try
            {
                x = DamoKit.BindHwnd(new() { Hwnd = hwnd }, out errorStr);//绑定窗口 最简化版本
            }
            catch (Exception e)
            {
                Console.WriteLine("绑定失败" + e.Message);
                return null;
            }

            Console.WriteLine("注册成功，当前插件版本：" + x.Ver());
            Console.WriteLine("当前对象绑定的窗口为：" + x.GetBindWindow());

            //x.UnBindWindow();
            //Console.WriteLine("解绑完毕..");
            return x;
        }

        private const int HWND = 460002;

        public void a()
        {
            dmsoft x = b(HWND) ?? throw new Exception("X对象没有正确返回！");//绑定
            XYoloV8.LoadModels(YoloModels.Detect);

            x.GetScreenDataBmp(0, 0, 2000, 2000, out int data, out int size);
            byte[] genePic2 = new byte[(int)size];
            IntPtr ptr = new((int)data);
            for (int i = 0; i < (int)size; i++)
                genePic2[i] = Marshal.ReadByte(ptr + 1 * i);
            //XYoloV8.TestModels(genePic2);
        }

        public void aa()
        {
            dmsoft x = b(HWND) ?? throw new Exception("X对象没有正确返回！");//绑定
            XYoloV8.LoadModels(YoloModels.Classify);

            x.GetScreenDataBmp(0, 0, 2000, 2000, out int data, out int size);
            byte[] genePic2 = new byte[(int)size];
            IntPtr ptr = new((int)data);
            for (int i = 0; i < (int)size; i++)
                genePic2[i] = Marshal.ReadByte(ptr + 1 * i);

            var s = XYoloV8.Classify(genePic2);
            Console.WriteLine(s);
        }
    }
}