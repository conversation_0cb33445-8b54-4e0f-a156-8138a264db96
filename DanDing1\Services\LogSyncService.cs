using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XHelper;
using XHelper.Models;

namespace DanDing1.Services
{
    /// <summary>
    /// 日志同步服务，用于处理来自服务器的日志同步请求
    /// </summary>
    public class LogSyncService
    {
        private readonly ILogStorage _logStorage;
        private const int MaxLogEntries = 100; // 最多返回100条日志

        // 用于日志标记，便于识别
        private const string LOG_SYNC_TAG = "LogSyncService:";

        public LogSyncService(ILogStorage logStorage)
        {
            _logStorage = logStorage ?? throw new ArgumentNullException(nameof(logStorage));

            // 注册WebSocket消息处理器
            RegisterMessageHandlers();

            XLogger.OnlyWrite($"{LOG_SYNC_TAG} 已初始化，已注册GET_LOGS_COMMAND消息处理器");
        }

        /// <summary>
        /// 注册WebSocket消息处理器
        /// </summary>
        public void RegisterMessageHandlers()
        {
            // 先取消注册，避免重复
            XWebsocket.UnregisterMessageHandler("GET_LOGS_COMMAND");

            // 重新注册处理器
            XWebsocket.RegisterMessageHandler("GET_LOGS_COMMAND", HandleGetLogsCommand);

            XLogger.OnlyWrite($"{LOG_SYNC_TAG} 已重新注册GET_LOGS_COMMAND消息处理器");
        }

        /// <summary>
        /// 处理GET_LOGS_COMMAND消息
        /// </summary>
        /// <param name="message">WebSocket消息内容</param>
        private void HandleGetLogsCommand(string message)
        {
            try
            {
                XLogger.OnlyWrite($"{LOG_SYNC_TAG} 收到日志同步请求: {message}");

                // 解析请求消息
                var request = JsonConvert.DeserializeObject<GetLogsCommand>(message);
                if (request == null)
                {
                    XLogger.Error($"{LOG_SYNC_TAG} 无法解析日志同步请求");
                    return;
                }

                // 获取最新的日志条目
                var logEntries = _logStorage.GetRecentLogs(MaxLogEntries);

                List<RemoteLogEntry>? list2 = [];
                // 移除 logEntries中的DEBUG日志
                foreach (var item in logEntries)
                    if (item.Level != "DEBUG")
                        list2.Add(item);

                // 构造响应消息
                var response = new LogEntriesResponse
                {
                    type = "LOG_ENTRIES_RESPONSE",
                    requestId = request.requestId,
                    timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    payload = new LogEntriesPayload
                    {
                        entries = list2
                    }
                };

                // 异步发送响应
                _ = SendLogEntriesResponseAsync(response);
            }
            catch (Exception ex)
            {
                XLogger.Error($"{LOG_SYNC_TAG} 处理日志同步请求时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送日志条目响应
        /// </summary>
        /// <param name="response">响应对象</param>
        private async Task SendLogEntriesResponseAsync(LogEntriesResponse response)
        {
            try
            {
                bool success = await XWebsocket.SendObjectAsync(response);
                if (success)
                {
                    XLogger.OnlyWrite($"{LOG_SYNC_TAG} 已成功发送日志同步响应: requestId={response.requestId}, 条目数={response.payload.entries.Count}");
                }
                else
                {
                    XLogger.Error($"{LOG_SYNC_TAG} 发送日志同步响应失败: requestId={response.requestId}");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"{LOG_SYNC_TAG} 发送日志同步响应时出错: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 获取日志命令
    /// </summary>
    public class GetLogsCommand
    {
        public string type { get; set; }
        public string requestId { get; set; }
        public long timestamp { get; set; }
    }

    /// <summary>
    /// 日志条目响应
    /// </summary>
    public class LogEntriesResponse
    {
        public string type { get; set; }
        public string requestId { get; set; }
        public long timestamp { get; set; }
        public LogEntriesPayload payload { get; set; }
    }

    /// <summary>
    /// 日志条目响应载荷
    /// </summary>
    public class LogEntriesPayload
    {
        public List<RemoteLogEntry> entries { get; set; }
    }

    /// <summary>
    /// 日志存储接口
    /// </summary>
    public interface ILogStorage
    {
        /// <summary>
        /// 获取最近的日志条目
        /// </summary>
        /// <param name="count">要获取的日志条目数量</param>
        /// <returns>日志条目列表</returns>
        List<RemoteLogEntry> GetRecentLogs(int count);
    }
}