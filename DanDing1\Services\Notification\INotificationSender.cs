using System.Threading.Tasks;

namespace DanDing1.Services.Notification
{
    /// <summary>
    /// 通知发送器接口，定义所有通知发送方式必须实现的方法
    /// </summary>
    public interface INotificationSender
    {
        /// <summary>
        /// 通知类型标识符
        /// </summary>
        string NoticeType { get; }

        /// <summary>
        /// 通知类型显示名称
        /// </summary>
        string DisplayName { get; }

        /// <summary>
        /// 发送通知
        /// </summary>
        /// <param name="title">通知标题</param>
        /// <param name="content">通知内容</param>
        /// <param name="extraParams">额外参数，如有特定需求的通知方式可使用</param>
        /// <returns>发送是否成功</returns>
        Task<bool> SendAsync(string title, string content, object extraParams = null);

        /// <summary>
        /// 格式化通知内容
        /// </summary>
        /// <param name="content">原始内容</param>
        /// <returns>格式化后的内容</returns>
        string FormatContent(string content);
    }
}