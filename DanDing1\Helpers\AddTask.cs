﻿using ScriptEngine.Model;

namespace DanDing1.Helpers
{
    internal class AddTask
    {
        /// <summary>
        /// 任务限制字典，存储各任务的上下限
        /// 键：任务名称
        /// 值：[最小限制, 最大限制]
        /// </summary>
        private static readonly Dictionary<string, int[]> TaskLimits = new()
        {
            // 基础任务限制
            {"探索", new[] {1, 80}},
            {"永生", new[] {1, 120}},
            {"日轮", new[] {1, 120}},
            {"业原火", new[] {1, 120}},
            {"活动", new[] {1, 120}},
            {"定时任务", new[] {1, 120}},
            {"日常任务", new[] {1, 120}},
            {"英杰", new[] {1, 50}},
            {"百鬼", new[] {1, 50}},
            // 特殊任务限制
            {"六道", new[] {1, 10}},
            {"契灵", new[] {1, 100}},
            {"御魂", new[] {1, 120}},
            {"时长", new[] {1, 60}},
            {"突破", new[] {1, 30}},
            {"寮突", new[] {1, 20}},
            {"御灵", new[] {1, 50}},
            {"觉醒", new[] {1, 50}},
            {"斗技", new[] {1, 50}}
        };

        public static TaskConfigsModel.Configs? Base(string name, int count, out string errorStr)
        {
            List<string> taskname = ["探索", "永生", "日轮", "业原火", "活动", "定时任务", "日常任务", "英杰", "百鬼"];
            if (!taskname.Contains(name))
            {
                errorStr = "Base添加任务暂时不支持" + name + "任务！";
                return null;
            }
            TaskConfigsModel.Configs configs = new();
            errorStr = "";

            // 使用简化的检查方法
            if (!CheckTaskCount(count, name, out errorStr))
            {
                return null;
            }

            configs.Name = name;
            configs.ShowName = name;
            configs.Count = count;
            configs.Others = [];
            return configs;
        }

        /// <summary>
        /// 添加活动任务
        /// </summary>
        /// <param name="name"></param>
        /// <param name="count"></param>
        /// <param name="errorStr"></param>
        /// <returns></returns>
        public static TaskConfigsModel.Configs? Hdong(string name, int count, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";

            // 使用简化的检查方法
            if (!CheckTaskCount(count, name, out errorStr))
            {
                return null;
            }

            configs.Name = name;
            configs.ShowName = name;
            configs.Count = count;
            configs.Others = [];
            return configs;
        }

        public static TaskConfigsModel.Configs Buff(string[] vs, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";
            configs.Name = "Buff";
            configs.ShowName = "Buff";
            if (vs[1] == "1")
            {
                int i = 0;
                List<string> buffName = ["金币", "经验", "觉醒", "御魂"];
                configs.ShowName += "开";
                //开Buff
                foreach (char ch in vs[2])
                {
                    if (ch == '1')
                    {
                        configs.Others.Add(buffName[i], "1");
                        configs.ShowName += buffName[i];
                    }
                    i++;
                }
                if (configs.Others.Count == 0)
                {
                    errorStr = "您没有选择任何Buff！";
                    return null;
                }
                configs.Count = 1;
            }
            else
            {
                configs.ShowName += "关所有";
                configs.Count = 0;
                configs.Others.Add("所有", "0");
            }

            if (vs[4] == "1")
                configs.Others.Add("仅下一次使用", "1");
            else
                configs.Others.Add("仅下一次使用", "0");
            return configs;
        }

        public static TaskConfigsModel.Configs? Delay(double time, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";

            // 使用简化的检查方法
            if (!CheckTaskCount((int)time, "时长", out errorStr))
            {
                return null;
            }

            configs.Name = "等待";
            configs.ShowName = "等待 " + time + "分钟";
            int ms = (int)(time * 60 * 1000);
            configs.Count = ms;
            configs.Others = [];
            return configs;
        }

        /// <summary>
        /// 添加快速-半自动任务
        /// </summary>
        /// <param name="name">任务名</param>
        /// <param name="biaoJi">标记</param>
        /// <param name="autoEndTime">定时结束时间(分钟)</param>
        /// <returns></returns>
        public static TaskConfigsModel.Configs FastTask(string name, bool biaoJi = false, string autoEndTime = "0")
        {
            TaskConfigsModel.Configs configs = new();
            configs.Name = name;
            configs.ShowName = name;
            configs.Count = 1;
            configs.Others = new()
            {
                {"Biaoji", biaoJi.ToString()}, //标记
                {"AutoEndTime", autoEndTime}   //定时结束时间
            };
            return configs;
        }

        /// <summary>
        /// 添加觉醒任务
        /// </summary>
        /// <param name="count"></param>
        /// <param name="pairs"></param>
        /// <param name="errorStr"></param>
        /// <returns></returns>
        public static TaskConfigsModel.Configs? Jxing(int count, Dictionary<string, string> pairs, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";

            // 使用简化的检查方法
            if (!CheckTaskCount(count, "觉醒", out errorStr))
            {
                return null;
            }

            configs.Name = "觉醒";
            configs.ShowName = "觉醒-" + pairs["Class"];
            configs.Count = count;
            configs.Others = pairs;
            return configs;
        }

        /// <summary>
        /// 六道
        /// </summary>
        /// <param name="count"></param>
        /// <param name="pairs"></param>
        /// <param name="errorStr"></param>
        /// <returns></returns>
        public static TaskConfigsModel.Configs? Liudao(int count, Dictionary<string, string> pairs, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";

            // 使用简化的检查方法
            if (!CheckTaskCount(count, "六道", out errorStr))
            {
                return null;
            }

            configs.Name = "六道";
            configs.ShowName = "六道";
            configs.Count = count;
            configs.Others = pairs;
            return configs;
        }

        /// <summary>
        /// 添加寮突任务
        /// </summary>
        /// <param name="count"></param>
        /// <param name="pairs"></param>
        /// <param name="errorStr"></param>
        /// <returns></returns>
        public static TaskConfigsModel.Configs? LTpo(int count, Dictionary<string, string> pairs, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";

            // 使用简化的检查方法
            if (!CheckTaskCount(count, "寮突", out errorStr))
            {
                return null;
            }

            configs.Name = "寮突";
            configs.ShowName = "寮突";
            configs.Count = count;
            configs.Others = pairs;
            return configs;
        }

        /// <summary>
        /// 契灵
        /// </summary>
        /// <param name="count"></param>
        /// <param name="pairs"></param>
        /// <param name="errorStr"></param>
        /// <returns></returns>
        public static TaskConfigsModel.Configs? Qiling(int count, Dictionary<string, string> pairs, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";

            // 使用简化的检查方法
            if (!CheckTaskCount(count, "契灵", out errorStr))
            {
                return null;
            }

            configs.Name = "契灵";
            configs.ShowName = "契灵";
            configs.Count = count;
            configs.Others = pairs;
            return configs;
        }

        /// <summary>
        /// 添加突破任务
        /// </summary>
        /// <param name="count"></param>
        /// <param name="pairs"></param>
        /// <param name="errorStr"></param>
        /// <returns></returns>
        public static TaskConfigsModel.Configs? Tpo(int count, Dictionary<string, string> pairs, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";

            // 使用简化的检查方法
            if (!CheckTaskCount(count, "突破", out errorStr))
            {
                return null;
            }

            if (pairs["EndStartLtu"] == "True" && pairs["LtuCount"] == "0")
            {
                errorStr = "您启用了突破后寮突，但寮突次数为0！";
                return null;
            }
            configs.Name = "突破";
            configs.ShowName = "突破";
            configs.Count = count;
            configs.Others = pairs;
            return configs;
        }

        /// <summary>
        /// 探索组队任务
        /// </summary>
        /// <param name="count">次数</param>
        /// <param name="combatstr">只打对象</param>
        /// <param name="counting_Mode">结算模式</param>
        /// <param name="location">组队身份</param>
        /// <param name="laTeam">邀请队友区域</param>
        /// <param name="team_name">队友名字</param>
        /// <param name="bytes">队友ID图片</param>
        /// <param name="errorStr">错误信息</param>
        /// <returns></returns>
        public static TaskConfigsModel.Configs? TSun_ZuDui(int count, string combatstr, bool counting_Mode, string location, string laTeam, string team_name, byte[] bytes, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";
            configs.Name = "组队探索";
            var str = team_name == "" ? location : team_name;
            configs.ShowName = $"组队探索-{str}" + "-" + combatstr;
            combatstr = combatstr switch
            {
                "全都打" => "",
                "达摩" => "damo",
                "经验" => "jingyan",
                "金币" => "jinbi",
                _ => ""
            };
            configs.Count = count;
            configs.Others = new()
            {
                {"CombatStr", combatstr },//指定战斗Buff
                {"Counting_Mode", counting_Mode.ToString() },//结算模式
                {"Location",location },//组队身份
                {"Team",laTeam },//邀请队友区域
                {"Name",team_name },//队友名字
            };
            if (team_name != null)
                configs.Others_Obj = new()
                {
                    { team_name,bytes}
                };
            else
                configs.Others_Obj = [];

            return configs;
        }

        /// <summary>
        /// 探索任务
        /// </summary>
        /// <param name="count">次数</param>
        /// <param name="combatstr">只打对象</param>
        /// <param name="counting_Mode"></param>
        /// <param name="errorStr"></param>
        /// <returns></returns>
        public static TaskConfigsModel.Configs? Tsuo(int count, string combatstr, bool counting_Mode, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";

            // 使用简化的检查方法
            if (!CheckTaskCount(count, "探索", out errorStr))
            {
                return null;
            }

            configs.Name = "探索";
            configs.ShowName = "探索" + "-" + combatstr;
            combatstr = combatstr switch
            {
                "全都打" => "",
                "达摩" => "damo",
                "经验" => "jingyan",
                "金币" => "jinbi",
                _ => ""
            };
            configs.Count = count;
            configs.Others = new()
            {
                {"CombatStr", combatstr },
                {"Counting_Mode", counting_Mode.ToString() }
            };
            return configs;
        }

        public static TaskConfigsModel.Configs? Yhun(int count, Dictionary<string, string> pairs, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";

            // 使用简化的检查方法
            if (!CheckTaskCount(count, "御魂", out errorStr))
            {
                return null;
            }

            configs.Name = "御魂";
            configs.ShowName = "御魂";
            configs.Count = count;
            configs.Others = pairs;
            return configs;
        }

        /// <summary>
        /// 御魂组队任务
        /// </summary>
        /// <param name="count">次数</param>
        /// <param name="level">层数</param>
        /// <param name="biaoji">标记</param>
        /// <param name="location">组队身份</param>
        /// <param name="team_name">队友名字</param>
        /// <param name="bytes">队友ID图片</param>
        /// <param name="errorStr">错误信息</param>
        /// <returns></returns>
        public static TaskConfigsModel.Configs? Yhun_ZuDui(int count, string level, bool biaoji, string location, string laTeam, string team_name, byte[] bytes, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";
            configs.Name = "组队御魂";
            var str = team_name == "" ? location : team_name;
            configs.ShowName = $"组队御魂-{str}";
            configs.Count = count;
            configs.Others = new()
            {
                {"Level",level },//层数
                {"Biaoji",biaoji.ToString() },//标记
                {"Location",location },//组队身份
                {"Team",laTeam },//邀请队友区域
                {"Name",team_name },//队友名字
            };
            if (team_name != null)
                configs.Others_Obj = new()
                {
                    { team_name,bytes}
                };
            else
                configs.Others_Obj = [];

            return configs;
        }

        /// <summary>
        /// 添加御灵任务
        /// </summary>
        /// <param name="count"></param>
        /// <param name="pairs"></param>
        /// <param name="errorStr"></param>
        /// <returns></returns>
        public static TaskConfigsModel.Configs? Yling(int count, Dictionary<string, string> pairs, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";

            // 使用简化的检查方法
            if (!CheckTaskCount(count, "御灵", out errorStr))
            {
                return null;
            }

            configs.Name = "御灵";
            configs.ShowName = "御灵-" + pairs["Class"];
            configs.Count = count;
            configs.Others = pairs;
            return configs;
        }

        internal static TaskConfigsModel.Configs? Dji(int dji_Count, string dji_Biaoji, bool dji_ManTing, out string errorStr)
        {
            TaskConfigsModel.Configs configs = new();
            errorStr = "";

            // 使用简化的检查方法
            if (!CheckTaskCount(dji_Count, "斗技", out errorStr))
            {
                return null;
            }

            configs.Name = "斗技";
            configs.ShowName = "斗技";
            configs.Count = dji_Count;
            configs.Others = new()
            {
                {"Biaoji",dji_Biaoji},
                {"ManTing",dji_ManTing.ToString() }
            };
            return configs;
        }

        /// <summary>
        /// 检查任务次数是否合法，如果用户积分≥30则忽略上限限制
        /// </summary>
        /// <param name="count">要检查的次数</param>
        /// <param name="taskName">任务名称</param>
        /// <param name="errorStr">错误信息输出</param>
        /// <returns>是否合法</returns>
        private static bool CheckTaskCount(int count, string taskName, out string errorStr)
        {
            // 检查任务是否在限制字典中
            if (!TaskLimits.TryGetValue(taskName, out var limits))
            {
                errorStr = $"未知任务类型：{taskName}，无法验证次数限制！";
                return false;
            }

            int minLimit = limits[0];
            int maxLimit = limits[1];

            // 检查用户积分是否≥30，如果是则忽略上限限制
            if (GlobalData.Instance.appConfig.User_Points >= 30)
            {
                // 仅检查下限
                if (count < minLimit)
                {
                    errorStr = $"{taskName}次数不合法！请重新添加，次数必须≥{minLimit}！";
                    return false;
                }
                errorStr = "";
                return true;
            }
            else
            {
                // 正常检查上下限
                if (!Utils.CheckNumber(count, minLimit, maxLimit))
                {
                    errorStr = $"{taskName}次数不合法！请重新添加范围再在[{minLimit}-{maxLimit}]次之间！";
                    return false;
                }
                errorStr = "";
                return true;
            }
        }
    }
}