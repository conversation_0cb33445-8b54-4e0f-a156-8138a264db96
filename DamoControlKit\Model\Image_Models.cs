﻿using DamoControlKit.Interface;
using DamoControlKit.runtimes;
using XHelper;

namespace DamoControlKit.Model
{
    /// <summary>
    /// 内存地址图片类
    /// </summary>
    public class MemPic : IFind, ICloneable
    {
        private const string DEFAULT_DELTA_COLOR = "202020";
        private const double DEFAULT_SIM = 0.9;
        private const string DEFAULT_POSITION = "0,0,2000,2000";

        /// <summary>
        /// 初始化图片资源路径
        /// </summary>
        /// <param name="name">图片名称</param>
        /// <param name="meminfo">内存信息</param>
        public MemPic(string name, string meminfo)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Meminfo = meminfo ?? throw new ArgumentNullException(nameof(meminfo));
            FindPosition = new(DEFAULT_POSITION);
        }

        /// <summary>
        /// Log用图片名
        /// </summary>
        public string _Name => Name.Replace(".bmp", string.Empty);

        /// <summary>
        /// 点击范围
        /// </summary>
        public List<Position>? ClickPositions { get; private set; }

        /// <summary>
        /// 图片识别时偏色
        /// </summary>
        public string Delta_Color { get; set; } = DEFAULT_DELTA_COLOR;

        /// <summary>
        /// 图片识别时扫描查找方向
        /// 0左到右、上到下
        /// 1左到右、下到上
        /// 2右到左、上到下
        /// 3右到左、下到上
        /// </summary>
        public int Dir { get; set; }

        public dmsoft? dmsoft { get; set; }

        /// <summary>
        /// 查找范围
        /// </summary>
        public Position FindPosition { get; set; }

        /// <summary>
        /// 内存地址 ptr,size
        /// </summary>
        public string Meminfo { get; }

        /// <summary>
        /// 图片名字
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 图片识别时相似度
        /// </summary>
        public double Sim { get; set; } = DEFAULT_SIM;

        /// <summary>
        /// 真实的图片名
        /// </summary>
        public string _Rename => _Name.Split('.')[1];

        /// <summary>
        /// 补偿查找的图片
        /// </summary>
        public MemPics? Remedy_Pics { get; set; }

        /// <summary>
        /// 替换前的旧范围
        /// </summary>
        private Position? PositionOld { get; set; }

        /// <summary>
        /// 添加点击位置
        /// </summary>
        /// <param name="pos"></param>
        /// <returns></returns>
        public MemPic AddClickPosition(Position pos)
        {
            ArgumentNullException.ThrowIfNull(pos);
            ClickPositions ??= new();
            ClickPositions.Add(pos);
            return this;
        }

        /// <summary>
        /// 点击操作
        /// </summary>
        /// <param name="isFindPos">是否使用查找位置进行点击</param>
        public void Click(bool isFindPos = false)
        {
            if (dmsoft is null)
                throw new InvalidOperationException("Click Error: dmsoft对象不存在");

            if (isFindPos)
            {
                FindPosition.SetXsoft(dmsoft);
                FindPosition.Click();
                return;
            }

            if (ClickPositions?.Count > 0)
            {
                var random = new Random();
                var clickPosition = ClickPositions[random.Next(ClickPositions.Count)];
                clickPosition.SetXsoft(dmsoft);
                clickPosition.Click();
            }
        }

        /// <summary>
        /// 深拷贝
        /// </summary>
        /// <returns></returns>
        public object Clone()
        {
            var clonedMemPic = new MemPic(Name, Meminfo)
            {
                Delta_Color = Delta_Color,
                Dir = Dir,
                dmsoft = dmsoft,
                FindPosition = (Position)FindPosition.Clone(),
                Sim = Sim,
                Remedy_Pics = (MemPics?)Remedy_Pics?.Clone(),
                PositionOld = (Position?)PositionOld?.Clone(),
                ClickPositions = ClickPositions is null ? null : new List<Position>(ClickPositions)
            };

            return clonedMemPic;
        }

        /// <summary>
        /// 在指定范围里找图
        /// </summary>
        /// <param name="pos">查找范围</param>
        /// <returns>是否找到图片</returns>
        public bool Find(Position pos)
        {
            ArgumentNullException.ThrowIfNull(pos);
            var xtmp = dmsoft ?? throw new InvalidOperationException("插件未初始化或未工作");

            if (FindPosition.X == 0 && FindPosition.Y == 0 && FindPosition.X1 == 0 && FindPosition.Y1 == 0)
                return false;

            return FindInPosition(xtmp, pos.X, pos.Y, pos.X1, pos.Y1);
        }

        public bool Find(dmsoft? x = null)
        {
            var xtmp = x ?? dmsoft ?? throw new InvalidOperationException("插件未初始化或未工作");

            if (FindPosition.X == 0 && FindPosition.Y == 0 && FindPosition.X1 == 0 && FindPosition.Y1 == 0)
                return false;

            return FindInPosition(xtmp, FindPosition.X, FindPosition.Y, FindPosition.X1, FindPosition.Y1);
        }

        private bool FindInPosition(dmsoft dm, int x, int y, int x1, int y1)
        {
            if (dm.FindPicMem(x, y, x1, y1, Meminfo, Delta_Color, Sim, Dir, out _, out _) > -1)
                return true;

            // 补偿查找
            if (Remedy_Pics?.PicList is not null)
            {
                foreach (var pic in Remedy_Pics.PicList)
                {
                    if (dm.FindPicMem(x, y, x1, y1, pic.Meminfo, Delta_Color, Sim, Dir, out _, out _) > -1)
                    {
                        //XLogger.Debug($"补偿查找成功：[{pic._Name}]");
                        return true;
                    }
                }
            }
            return false;
        }

        public bool FindAndClick(dmsoft? x)
        {
            var xtmp = x ?? dmsoft ?? throw new InvalidOperationException("插件未初始化或未工作");

            if (xtmp.FindPicMem(FindPosition.X, FindPosition.Y, FindPosition.X1, FindPosition.Y1,
                Meminfo, Delta_Color, Sim, Dir, out int xx, out int yy) <= -1)
                return false;

            //if (ClickPositions?.Count > 0)
            //{
            //    Click();
            //}
            //else
            //{
            var random = new Random();
            int x1 = xx + random.Next(-2, 3);
            int y1 = yy + random.Next(-2, 3);

            dmsoft.MoveTo(x1, y1);
            Record.SaveClick(dmsoft.GetID(), x1, y1);
            dmsoft.delay(10);
            dmsoft.LeftClick();
            dmsoft.Delays(10, 30);
            //}
            return true;
        }

        public Point? FindPoint(dmsoft? x)
        {
            var xtmp = x ?? dmsoft ?? throw new InvalidOperationException("插件未初始化或未工作");

            var result = FindPointInPosition(xtmp, FindPosition.X, FindPosition.Y, FindPosition.X1, FindPosition.Y1);
            if (result is not null)
                return result;

            // 补偿查找
            if (Remedy_Pics?.PicList is not null)
            {
                foreach (var pic in Remedy_Pics.PicList)
                {
                    result = FindPointInPosition(xtmp, FindPosition.X, FindPosition.Y, FindPosition.X1, FindPosition.Y1, pic.Meminfo);
                    if (result is not null)
                        return result;
                }
            }
            return null;
        }

        private Point? FindPointInPosition(dmsoft dm, int x, int y, int x1, int y1, string? meminfo = null)
        {
            var ret = dm.FindPicMem(x, y, x1, y1, meminfo ?? Meminfo, Delta_Color, Sim, Dir, out int point_x, out int point_y);
            return ret != -1 ? new Point(point_x, point_y) : null;
        }

        /// <summary>
        /// 恢复原始范围
        /// </summary>
        /// <returns></returns>
        public bool RecoverPosition()
        {
            if (PositionOld is null)
                return false;

            FindPosition = new(PositionOld.P1, PositionOld.P2);
            return true;
        }

        /// <summary>
        /// 设置图片查找范围
        /// </summary>
        /// <param name="pos"></param>
        /// <returns></returns>
        public MemPic SetFindPosition(Position pos)
        {
            ArgumentNullException.ThrowIfNull(pos);
            FindPosition = pos;
            return this;
        }

        /// <summary>
        /// 设置其它参数
        /// delta_color|delta
        /// sim
        /// dir
        /// pos
        /// </summary>
        /// <returns></returns>
        public MemPic SetParameter(Dictionary<string, string> pairs)
        {
            ArgumentNullException.ThrowIfNull(pairs);

            foreach (var (key, value) in pairs)
            {
                switch (key.ToLower())
                {
                    case "delta_color":
                    case "delta":
                        Delta_Color = value;
                        break;

                    case "sim":
                        if (!double.TryParse(value, out double simValue))
                            throw new ArgumentException($"无效的sim值: {value}");
                        Sim = simValue;
                        break;

                    case "dir":
                        if (!int.TryParse(value, out int dirValue))
                            throw new ArgumentException($"无效的dir值: {value}");
                        Dir = dirValue;
                        break;

                    case "pos":
                        FindPosition = new(value);
                        break;

                    default:
                        throw new ArgumentException($"无效的参数名: {key}");
                }
            }
            return this;
        }

        /// <summary>
        /// 设置临时范围
        /// </summary>
        /// <returns></returns>
        public MemPic SetPosition(Position pos)
        {
            ArgumentNullException.ThrowIfNull(pos);
            PositionOld = new(FindPosition.P1, FindPosition.P2);
            FindPosition = new(pos.P1, pos.P2);
            return this;
        }

        public bool SetXsoft(dmsoft x)
        {
            ArgumentNullException.ThrowIfNull(x);
            dmsoft = x;
            return true;
        }

        public bool Await(dmsoft? x, int period)
        {
            var xtmp = x ?? dmsoft ?? throw new InvalidOperationException("插件未初始化或未工作");

            int count = period / 100;
            for (int i = 0; i < count; i++)
            {
                if (Find())
                    return true;
                xtmp.delay(100);
            }
            return false;
        }
    }

    /// <summary>
    /// 内存图片集合
    /// </summary>
    public class MemPics : ICloneable
    {
        private readonly List<MemPic> _picList = [];

        public MemPics()
        { }

        public MemPics(List<MemPic> list)
        {
            ArgumentNullException.ThrowIfNull(list);
            Add(list);
        }

        public MemPics(MemPics pics)
        {
            ArgumentNullException.ThrowIfNull(pics);
            Add(pics);
        }

        public int Count => _picList.Count;

        public List<MemPic> PicList
        {
            get => _picList;
            init => _picList.AddRange(value);
        }

        public MemPic this[string name] =>
            GetMemPic(name) ?? throw new KeyNotFoundException($"MemPics 无法索引到{name}");

        /// <summary>
        /// 添加图片
        /// </summary>
        public MemPics Add(MemPic pic)
        {
            ArgumentNullException.ThrowIfNull(pic);
            _picList.Add(pic);
            return this;
        }

        /// <summary>
        /// 添加图片组
        /// </summary>
        public MemPics Add(List<MemPic> pics)
        {
            ArgumentNullException.ThrowIfNull(pics);
            _picList.AddRange(pics);
            return this;
        }

        /// <summary>
        /// 添加图片组
        /// </summary>
        public MemPics Add(MemPics pics)
        {
            ArgumentNullException.ThrowIfNull(pics);
            _picList.AddRange(pics.PicList);
            return this;
        }

        /// <summary>
        /// 筛选返回合集
        /// </summary>
        public MemPics Filter(string name)
        {
            ArgumentException.ThrowIfNullOrEmpty(name);

            var pics = new MemPics();
            foreach (var pic in _picList.Where(t => t.Name.Contains(name)))
            {
                pics.Add(pic);
            }
            return pics;
        }

        /// <summary>
        /// 查找所有图片，找到任意一个返回True
        /// </summary>
        public bool FindAll()
        {
            return _picList.Any(pic => pic.Find());
        }

        /// <summary>
        /// 在指定范围内查找图片
        /// </summary>
        public bool FindAsPosition(Position pos)
        {
            ArgumentNullException.ThrowIfNull(pos);
            return _picList.Any(pic => pic.Find(pos));
        }

        /// <summary>
        /// 查找所有图片并返回找到的第一个图片的坐标
        /// </summary>
        public bool FindAll(out int x, out int y)
        {
            foreach (var pic in _picList)
            {
                var point = pic.FindPoint(null);
                if (point is not null)
                {
                    x = point.X;
                    y = point.Y;
                    return true;
                }
            }

            x = y = -1;
            return false;
        }

        /// <summary>
        /// 查找并点击所有找到的图片
        /// </summary>
        public bool FindAllAndClick()
        {
            foreach (var pic in _picList)
                if (pic.FindAndClick(null)) return true;
            return false;
        }

        /// <summary>
        /// 查找所有图片并返回找到的图片名称列表
        /// </summary>
        public bool FindAllE(out List<string> names)
        {
            names = _picList.Where(pic => pic.Find())
                           .Select(pic => pic.Name)
                           .ToList();
            return names.Count > 0;
        }

        /// <summary>
        /// 查找所有图片并返回第一个找到的图片
        /// </summary>
        public MemPic? FindAllEa()
        {
            return _picList.FirstOrDefault(pic => pic.Find());
        }

        /// <summary>
        /// 查找所有图片并返回找到的图片集合
        /// </summary>
        public MemPics? FindAllEx()
        {
            var pics = new MemPics();
            foreach (var pic in _picList.Where(pic => pic.Find()))
            {
                pics.Add(pic);
            }
            return pics.Count > 0 ? pics : null;
        }

        /// <summary>
        /// 获取指定名称的图片
        /// </summary>
        public MemPic? GetMemPic(string name, bool full = false)
        {
            ArgumentException.ThrowIfNullOrEmpty(name);

            return full
                ? _picList.FirstOrDefault(t =>
                {
                    int lastDotIndex = t.Name.LastIndexOf('.');
                    string result = t.Name[..lastDotIndex];
                    return result == name;
                })
                : _picList.FirstOrDefault(t => t.Name.Contains(name));
        }

        /// <summary>
        /// 设置所有图片的大漠对象
        /// </summary>
        public void SetAllXsoft(dmsoft x)
        {
            ArgumentNullException.ThrowIfNull(x);
            foreach (var pic in _picList)
            {
                pic.SetXsoft(x);
            }
        }

        /// <summary>
        /// 等待图片集合中的任意一张图片出现
        /// </summary>
        /// <param name="seconds">等待时长（秒），默认3秒</param>
        /// <returns>在指定时间内找到图片返回True，否则返回False</returns>
        public bool Wait(int seconds = 3)
        {
            int totalChecks = seconds * 10; // 每100毫秒检查一次
            for (int i = 0; i < totalChecks; i++)
            {
                if (FindAll())
                    return true;

                if (_picList[0].dmsoft is null)
                    throw new InvalidOperationException("插件未初始化或未工作");

                _picList[0]?.dmsoft?.delay(100);
            }
            return false;
        }

        /// <summary>
        /// 深拷贝
        /// </summary>
        public object Clone()
        {
            var clonedMemPics = new MemPics();
            foreach (var pic in _picList)
            {
                clonedMemPics.Add((MemPic)pic.Clone());
            }
            return clonedMemPics;
        }
    }

    /// <summary>
    /// 基本图片类型
    /// </summary>
    public class Pic : IFind
    {
        /// <summary>
        /// 初始化图片资源路径
        /// </summary>
        /// <param name="pathName"></param>
        /// <exception cref="Exception"></exception>
        public Pic(string pathName)
        {
            if (!SetPath(pathName)) throw new Exception($"Pic初始化失败，路径无法解析[{pathName}]");
            if (Path is null || Name is null) throw new Exception($"Pic设置路径失败，无法获取路径[{pathName}]");
        }

        /// <summary>
        /// 图片识别时偏色
        /// </summary>
        public string Delta_Color { get; set; } = "202020";

        /// <summary>
        /// 图片识别时扫描查找方向
        /// 0左到右、上到下
        /// 1左到右、下到上
        /// 2右到左、上到下
        /// 3右到左、下到上
        /// </summary>
        public int Dir { get; set; } = 0;

        public dmsoft? dmsoft { get; set; }

        /// <summary>
        /// 图片名字
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 图片路径
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 查找范围
        /// </summary>
        public Position Position { get; set; } = new("0,0,2000,2000");

        /// <summary>
        /// 图片识别时相似度
        /// </summary>
        public double Sim { get; set; } = 0.9;

        /// <summary>
        /// 当查找失败时 另外查找的图片资源
        /// </summary>
        private List<Pic> ErrorPicList { get; set; } = [];

        /// <summary>
        /// 获取全部路径 用于识别
        /// </summary>
        private string GetPathName => Path + "\\" + Name;

        /// <summary>
        /// 是否设置了路径
        /// 只能设置一次路径
        /// </summary>
        private bool IsSetPath { set; get; } = false;

        /// <summary>
        /// 替换前的旧范围
        /// </summary>
        private Position? PositionOld { get; set; }

        /// <summary>
        /// 添加 查找失败时 另外查找的图片资源
        /// </summary>
        /// <param name="pic"></param>
        /// <returns></returns>
        public Pic AddErrorPic(Pic pic)
        {
            pic.SetXsoft(dmsoft ?? throw new Exception("主Pic图片未设置dmsoft对象！无法添加补充Pic识别！"));
            ErrorPicList.Add(pic);
            return this;
        }

        public bool Await(dmsoft? x, int period)
        {
            dmsoft? xtmp = (x ?? dmsoft) ?? throw new Exception("插件似乎未工作，错误！");
            int count = period / 100;
            int new_Count = 0;
            while (new_Count < count)
            {
                if (Find(null))
                    return true;
                new_Count++;
                xtmp.delay(100);
            }
            return false;
        }

        public bool Find(dmsoft? x)
        {
            dmsoft? xtmp = (x ?? dmsoft) ?? throw new Exception("插件似乎未工作，错误！");
            if (xtmp.FindPic(Position.X, Position.Y, Position.X1, Position.Y1, GetPathName, Delta_Color, Sim, Dir, out _, out _) <= -1)
                return ErrorFind();
            return true;
        }

        public Point? FindPoint(dmsoft? x)
        {
            dmsoft? xtmp = (x ?? dmsoft) ?? throw new Exception("插件似乎未工作，错误！");
            var ret_tmp = xtmp.FindPic(Position.X, Position.Y, Position.X1, Position.Y1, GetPathName, Delta_Color, Sim, Dir, out int point_x, out int point_y);
            if (ret_tmp == -1) return null;
            return new(point_x, point_y);
        }

        /// <summary>
        /// 恢复原始范围
        /// </summary>
        /// <returns></returns>
        public bool RecoverPosition()
        {
            if (PositionOld == null) return false;
            Position = new(PositionOld.P1, PositionOld.P2);
            return true;
        }

        /// <summary>
        /// 设置图片查找范围
        /// </summary>
        /// <param name="pos"></param>
        /// <returns></returns>
        public Pic SetFindPosition(Position pos)
        {
            Position = pos;
            return this;
        }

        /// <summary>
        /// 设置其它参数
        /// delta_color|delta
        /// sim
        /// dir
        /// pos
        /// </summary>
        /// <returns></returns>
        public Pic SetParameter(Dictionary<string, string> pairs)
        {
            string key;
            foreach (var item in pairs)
            {
                key = item.Key.ToLower();
                switch (key)
                {
                    case "delta_color":
                    case "delta": Delta_Color = item.Value; break;
                    case "sim": Sim = double.Parse(item.Value); break;
                    case "dir": Sim = int.Parse(item.Value); break;
                    case "pos": Position = new(item.Value); break;
                    default:
                        throw new Exception($"SetParameter Error!{item.Key}解析失败！");
                }
            }
            return this;
        }

        /// <summary>
        /// 设置图片路径
        /// </summary>
        /// <param name="pathName"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool SetPath(string pathName)
        {
            if (IsSetPath)
                throw new Exception("多次重复的设置错误，当前图片资源对象只能设置一次路径！");
            if (!File.Exists(pathName)) return false;
            Path = System.IO.Path.GetDirectoryName(pathName) ?? throw new Exception($"Pic设置路径失败，无法获取路径[{pathName}]");// 获取目录路径
            Name = System.IO.Path.GetFileName(pathName);// 获取文件名
            IsSetPath = true;
            return true;
        }

        /// <summary>
        /// 设置临时范围
        /// </summary>
        /// <returns></returns>
        public Pic SetPosition(Position pos)
        {
            PositionOld = new(Position.P1, Position.P2);
            Position = new(pos.P1, pos.P2);
            return this;
        }

        public bool SetXsoft()
        {
            throw new NotImplementedException();
        }

        public bool SetXsoft(dmsoft x)
        {
            dmsoft = x; return true;
        }

        /// <summary>
        /// 找图失败补充查找
        /// </summary>
        private bool ErrorFind()
        {
            foreach (var t in ErrorPicList)
                if (t.Find(null)) return true;
            return false;
        }
    }

    /// <summary>
    /// 图片类型集合
    /// </summary>
    public class Pics
    {
        public Pics(List<Pic> list)
        { Add(list); }

        public Pics(Pics pics)
        { Add(pics); }

        public Pics()
        { }

        public int Count => PicList.Count;
        public List<Pic> PicList { get; set; } = [];

        /// <summary>
        /// 添加图片
        /// </summary>
        /// <param name="pic"></param>
        public void Add(Pic pic) => PicList.Add(pic);

        /// <summary>
        /// 添加图片组
        /// </summary>
        /// <param name="pic"></param>
        public void Add(List<Pic> pics) => PicList.AddRange(pics);

        /// <summary>
        /// 添加图片组
        /// </summary>
        /// <param name="pic"></param>
        public void Add(Pics pics) => PicList.AddRange(pics.PicList);

        /// <summary>
        /// 筛选返回合集
        /// </summary>
        /// <param name="name"></param>
        /// <param name="isPath"></param>
        /// <returns></returns>
        public Pics Filter(string name, bool isPath = false)
        {
            Pics pics = new();
            if (isPath)
            {
                PicList.ForEach(t =>
                {
                    if (t.Path.Contains(name))
                        pics.Add(t);
                });
                return pics;
            }

            PicList.ForEach(t =>
            {
                if (t.Name.Contains(name))
                    pics.Add(t);
            });
            return pics;
        }

        /// <summary>
        /// 查找所有 找到任意一个返回 True
        /// </summary>
        /// <returns></returns>
        public bool FindAll()
        {
            foreach (var pic in PicList)
                if (pic.Find(null)) return true;
            return false;
        }

        /// <summary>
        /// 查找所有 找到返回一个Name_Lists
        /// </summary>
        /// <returns>一个没找到返回False</returns>
        public bool FindAllE(out List<string> names)
        {
            List<string> names_tmp = [];
            foreach (var pic in PicList)
                if (pic.Find(null))
                    names_tmp.Add(pic.Name);

            if (names_tmp.Count == 0)
            {
                names = names_tmp;
                return false;
            }

            names = names_tmp;
            return true;
        }

        /// <summary>
        /// 查找所有 找到返回一个Name_Lists
        /// </summary>
        /// <returns>一个没找到返回False</returns>
        public Pics? FindAllEx()
        {
            Pics pics = new();
            foreach (var pic in PicList)
                if (pic.Find(null))
                    pics.Add(pic);
            if (pics.Count == 0) return null;
            return pics;
        }

        /// <summary>
        /// 删除图片 筛选名字
        /// </summary>
        /// <param name="name"></param>
        public void Remove(string name)
        {
            Pic? pTmp = null;
            PicList.ForEach(t =>
            {
                if (t.Name.Contains(name))
                    pTmp = t;
            });

            if (pTmp is not null) PicList.Remove(pTmp);
        }

        /// <summary>
        /// 设置所有大漠对象
        /// </summary>
        public void SetAllXsoft(dmsoft x) => PicList.ForEach(t => t.SetXsoft(x));
    }
}