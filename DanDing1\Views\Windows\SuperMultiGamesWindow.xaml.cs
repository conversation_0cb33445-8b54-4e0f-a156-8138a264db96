﻿using DanDing1.Models.Super;
using DanDing1.Services;
using DanDing1.ViewModels.Windows;
using DanDing1.ViewModels.Windows.SuperMultiGamesWindowViewModels;
using DanDing1.Views.UserControls;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.Windows.Controls;
using XHelper;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// SuperMultiGamesWindow.xaml 的交互逻辑
    /// </summary>
    public partial class SuperMultiGamesWindow : Window
    {
        private readonly SuperMultiGamesWindowViewModel viewModel;
        private MultiGamePreviewControl _gamePreview;
        private bool _isContextMenuOpen = false;  // 添加字段跟踪菜单状态
        private bool _tabControlSelectionChangedEventAttached = false; // 跟踪是否已添加选项卡切换事件
                                                                       // 修改为MultiGamePreviewControl

        public SuperMultiGamesWindow(SuperMultiGamesWindowViewModel viewModel)
        {
            InitializeComponent();

            this.Loaded += (s, e) =>
            {
                if (GlobalData.Instance.appConfig.User_Points < 7)
                {
                    XLogger.Warn("用户权限不足..");
                    this.Close();
                    return;
                }
            };

            this.viewModel = viewModel;
            DataContext = this;

            // 设置窗口引用
            viewModel.SetWindowReference(this);

            // 监听游戏日志集合变化
            viewModel.GameLogs.CollectionChanged += GameLogs_CollectionChanged;

            // 监听游戏列表集合变化，用于同步预览控件
            viewModel.SuperMultiGame_DataModelCollection.CollectionChanged += SuperMultiGame_DataModelCollection_CollectionChanged;

            // 订阅AddTaskControl的TasksChanged事件
            AddTask.TasksChanged += AddTask_TasksChanged;
            AddTask.SetSuperMode();

            // 订阅DataGrid的ContextMenu事件
            var dataGrid = this.FindName("DataGrid") as Wpf.Ui.Controls.DataGrid;
            if (dataGrid != null && dataGrid.ContextMenu != null)
            {
                dataGrid.ContextMenu.Opened += (s, e) => _isContextMenuOpen = true;
                dataGrid.ContextMenu.Closed += (s, e) => _isContextMenuOpen = false;
            }

            // 订阅日志更新事件，实现自动滚动
            viewModel.OnMainLogUpdated += () => ScrollLogToBottom(0); // 主日志选项卡索引为0
            viewModel.OnGameLogUpdated += () =>
            {
                // 窗口加载完成后，确保日志滚动到底部
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    // 获取当前选中的游戏日志选项卡索引，如果存在则滚动
                    if (LogTabControl.SelectedIndex > 0)
                    {
                        ScrollLogToBottom(LogTabControl.SelectedIndex);
                    }
                }), DispatcherPriority.Loaded);
            };

            // 获取GamePreview控件引用
            _gamePreview = this.FindName("GamePreview") as MultiGamePreviewControl;

            // 订阅模拟器数据刷新完成事件
            viewModel.MuMuDataRefreshed += ViewModel_MuMuDataRefreshed;
        }

        public SuperMultiGamesWindowViewModel ViewModel => viewModel;

        /// <summary>
        /// 查找指定类型的父元素
        /// </summary>
        private static T FindParent<T>(DependencyObject child) where T : DependencyObject
        {
            if (child == null) return null;

            var parent = VisualTreeHelper.GetParent(child);

            if (parent == null) return null;

            if (parent is T) return (T)parent;

            return FindParent<T>(parent);
        }

        /// <summary>
        /// 查找指定类型的所有可视化子元素
        /// </summary>
        private static IEnumerable<T> FindVisualChild<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj == null) yield break;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
            {
                var child = VisualTreeHelper.GetChild(depObj, i);

                if (child is T t)
                    yield return t;

                foreach (var childOfChild in FindVisualChild<T>(child))
                    yield return childOfChild;
            }
        }

        /// <summary>
        /// 添加日志选项卡
        /// </summary>
        private void AddLogTab(TabControl tabControl, GameLogItem gameLog)
        {
            // 创建新的选项卡
            var newTab = new TabItem
            {
                Header = gameLog.GameName
            };

            // 创建TextBox作为选项卡内容
            var textBox = new Wpf.Ui.Controls.TextBox
            {
                IsReadOnly = true,
                TextWrapping = TextWrapping.Wrap,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                BorderThickness = new Thickness(0),
                Padding = new Thickness(5)
            };

            // 绑定到游戏日志的LogContent属性
            var binding = new Binding("LogContent")
            {
                Source = gameLog,
                Mode = BindingMode.OneWay
            };
            textBox.SetBinding(Wpf.Ui.Controls.TextBox.TextProperty, binding);

            // 设置选项卡的内容
            newTab.Content = textBox;

            // 添加到TabControl
            tabControl.Items.Add(newTab);
        }

        private void AddTask_TasksChanged(object sender, EventArgs e)
        {
            // 当任务列表变化时，自动更新当前任务显示
            if (ViewModel.SelectedGameModel != null && ViewModel.SelectedGameModel.TaskConfiguration != null)
            {
                // 检查游戏状态，如果是未运行状态，不更改CurrentTask值
                if (ViewModel.SelectedGameModel.RunningStatus == "未运行")
                {
                    ViewModel.SelectedGameModel.CurrentTask = "未运行";
                }
                // 如果游戏正在运行但没有任务，则显示"无任务"
                else if (ViewModel.SelectedGameModel.TaskConfiguration.GameTaskLists.Count == 0)
                {
                    ViewModel.SelectedGameModel.CurrentTask = "无任务";
                }
            }
        }

        /// <summary>
        /// 处理DataGrid的右键菜单事件，阻止系统默认右键菜单
        /// </summary>
        private void DataGrid_PreviewMouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 如果菜单已经打开，完全阻止右键事件
            if (_isContextMenuOpen)
            {
                e.Handled = true;
                return;
            }

            // 标记事件为已处理，防止系统自带菜单显示
            e.Handled = true;

            // 获取DataGrid和上下文菜单
            var dataGrid = (Wpf.Ui.Controls.DataGrid)sender;
            ContextMenu menu = dataGrid.ContextMenu;

            // 获取点击位置下的行
            var hitTestResult = VisualTreeHelper.HitTest(dataGrid, e.GetPosition(dataGrid));

            if (hitTestResult != null)
            {
                // 查找点击位置所在的DataGridRow
                var row = FindParent<DataGridRow>(hitTestResult.VisualHit);
                if (row != null)
                {
                    // 选中该行
                    dataGrid.SelectedItem = row.Item;

                    // 显示上下文菜单
                    if (menu != null)
                    {
                        // 设置菜单的放置目标和位置
                        menu.PlacementTarget = dataGrid;
                        menu.Placement = System.Windows.Controls.Primitives.PlacementMode.MousePoint;
                        menu.HorizontalOffset = 0;
                        menu.VerticalOffset = 0;
                        menu.IsOpen = true;
                    }
                }
            }
        }

        private void DataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ViewModel.SelectedGameModel != null)
            {
                // 确保选中的游戏模型有任务配置
                if (ViewModel.SelectedGameModel.TaskConfiguration == null)
                {
                    ViewModel.SelectedGameModel.TaskConfiguration = new ViewModels.Pages.AddTaskPropertyViewModel();
                }

                // 将AddTaskControl的ViewModel设置为当前选中游戏的任务配置
                AddTask.ViewModel = ViewModel.SelectedGameModel.TaskConfiguration;

                // 刷新AddTaskControl的任务列表显示
                AddTask.RefreshTaskList();

                // 更新游戏预览 (使用MultiGamePreviewControl的API)
                if (_gamePreview != null)
                {
                    _gamePreview.UpdateGame(ViewModel.SelectedGameModel);
                }
            }
        }

        /// <summary>
        /// 处理GameLogs集合变化，动态创建或删除选项卡
        /// </summary>
        private void GameLogs_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            // 获取对应的TabControl
            var tabControl = this.LogTabControl;

            if (tabControl == null) return;

            // 处理游戏日志集合重置
            if (e.Action == NotifyCollectionChangedAction.Reset)
            {
                // 保留第一个选项卡（主日志），移除所有其他选项卡
                while (tabControl.Items.Count > 1)
                {
                    tabControl.Items.RemoveAt(1);
                }

                // 如果有游戏日志项，重新创建所有选项卡
                ObservableCollection<GameLogItem> logs = sender as ObservableCollection<GameLogItem>;
                if (logs != null && logs.Count > 0)
                {
                    foreach (GameLogItem gameLog in logs)
                    {
                        AddLogTab(tabControl, gameLog);
                    }
                }

                // 选中第一个选项卡
                tabControl.SelectedIndex = 0;
                return;
            }

            // 处理新增的游戏日志
            if (e.Action == NotifyCollectionChangedAction.Add && e.NewItems != null)
            {
                foreach (GameLogItem gameLog in e.NewItems)
                {
                    AddLogTab(tabControl, gameLog);

                    // 自动选中新添加的选项卡
                    tabControl.SelectedIndex = tabControl.Items.Count - 1;
                }
            }

            // 处理删除的游戏日志
            if (e.Action == NotifyCollectionChangedAction.Remove && e.OldItems != null)
            {
                foreach (GameLogItem gameLog in e.OldItems)
                {
                    // 查找要删除的选项卡
                    // 跳过索引0（主日志选项卡）
                    for (int i = 1; i < tabControl.Items.Count; i++)
                    {
                        var tabItem = tabControl.Items[i] as TabItem;
                        if (tabItem != null && tabItem.Header?.ToString() == gameLog.GameName)
                        {
                            // 找到对应的选项卡，删除它
                            tabControl.Items.RemoveAt(i);
                            // 如果删除的是当前选中的选项卡，切换到主日志选项卡
                            if (tabControl.SelectedIndex >= tabControl.Items.Count)
                            {
                                tabControl.SelectedIndex = 0; // 主日志标签页
                            }
                            break;
                        }
                    }
                }
            }

            // 为TabControl添加选项卡切换事件，确保切换到选项卡时滚动到底部
            if (tabControl.Items.Count > 0 && !_tabControlSelectionChangedEventAttached)
            {
                tabControl.SelectionChanged += (s, ev) =>
                {
                    if (tabControl.SelectedIndex >= 0)
                    {
                        ScrollLogToBottom(tabControl.SelectedIndex);
                    }
                };
                _tabControlSelectionChangedEventAttached = true;
            }
        }

        /// <summary>
        /// 将指定选项卡中的日志滚动到底部
        /// </summary>
        /// <param name="tabIndex">选项卡索引</param>
        private void ScrollLogToBottom(int tabIndex)
        {
            if (LogTabControl == null || tabIndex < 0 || tabIndex >= LogTabControl.Items.Count)
                return;

            // 使用较高优先级确保UI已经渲染完成
            Dispatcher.BeginInvoke(new Action(() =>
            {
                // 获取选项卡内容
                var tabItem = LogTabControl.Items[tabIndex] as TabItem;
                if (tabItem == null) return;

                // 获取TextBox
                var textBox = tabItem.Content as Wpf.Ui.Controls.TextBox;
                if (textBox == null) return;

                try
                {
                    // 首先尝试使用TextBox的ScrollToEnd方法
                    textBox.ScrollToEnd();

                    // 然后尝试找到ScrollViewer并滚动它
                    var scrollViewer = FindVisualChild<ScrollViewer>(textBox).FirstOrDefault();
                    if (scrollViewer != null)
                    {
                        scrollViewer.ScrollToVerticalOffset(scrollViewer.ScrollableHeight);
                    }
                }
                catch (Exception ex)
                {
                    // 记录异常但不中断UI
                    Console.WriteLine($"日志滚动异常: {ex.Message}");
                }
            }), DispatcherPriority.Render);
        }

        // 添加错误通知示例
        private void ShowErrorNotification(string errorMessage)
        {
            NotificationService.Instance.Error(errorMessage, "错误");
        }

        // 在游戏启动成功时显示通知（根据实际代码位置进行添加）
        // 这可能在SuperMultiGamesWindowViewModel中，此处仅作示例
        private void ShowGameStartNotification(string gameName)
        {
            NotificationService.Instance.Success($"游戏 {gameName} 已成功启动", "启动成功");
        }

        // 添加警告通知示例
        private void ShowWarningNotification(string warningMessage)
        {
            NotificationService.Instance.Warning(warningMessage, "警告");
        }

        // 添加游戏集合变化处理方法
        private void SuperMultiGame_DataModelCollection_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            if (_gamePreview == null) return;

            // 处理集合重置
            if (e.Action == NotifyCollectionChangedAction.Reset)
            {
                // 集合完全重置，重新设置所有游戏
                _gamePreview.SetGames(ViewModel.SuperMultiGame_DataModelCollection);
                return;
            }

            // 处理新增游戏
            if (e.Action == NotifyCollectionChangedAction.Add && e.NewItems != null)
            {
                foreach (SuperMultiGame_DataModel game in e.NewItems)
                {
                    _gamePreview.AddGame(game);
                }
            }

            // 处理移除游戏
            if (e.Action == NotifyCollectionChangedAction.Remove && e.OldItems != null)
            {
                foreach (SuperMultiGame_DataModel game in e.OldItems)
                {
                    _gamePreview.RemoveGame(game.GameId);
                }
            }

            // 处理游戏替换
            if (e.Action == NotifyCollectionChangedAction.Replace && e.NewItems != null)
            {
                foreach (SuperMultiGame_DataModel game in e.NewItems)
                {
                    _gamePreview.UpdateGame(game);
                }
            }
        }

        // 处理模拟器数据刷新完成事件
        private void ViewModel_MuMuDataRefreshed(object sender, EventArgs e)
        {
            // 当模拟器数据刷新后，更新所有预览控件的句柄
            if (_gamePreview != null)
            {
                _gamePreview.RefreshAllHandles();
            }

            // 添加刷新完成通知
            NotificationService.Instance.Info("模拟器数据已刷新", "刷新完成");
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            e.Cancel = true; // 阻止关闭
            this.Visibility = Visibility.Hidden; // 隐藏窗口
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // 如果有游戏数据，默认选中第一条
            if (ViewModel.SuperMultiGame_DataModelCollection.Count > 0)
            {
                ViewModel.SelectedGameModel = ViewModel.SuperMultiGame_DataModelCollection[0];

                // 确保选中的游戏模型有任务配置
                if (ViewModel.SelectedGameModel.TaskConfiguration == null)
                {
                    ViewModel.SelectedGameModel.TaskConfiguration = new ViewModels.Pages.AddTaskPropertyViewModel();
                }

                // 设置AddTaskControl的ViewModel并刷新显示
                AddTask.ViewModel = ViewModel.SelectedGameModel.TaskConfiguration;
                AddTask.RefreshTaskList();
            }

            // 确保游戏日志选项卡已正确创建
            // 由于在某些情况下CollectionChanged事件可能未正确触发，所以手动检查并创建
            if (ViewModel.GameLogs.Count > 0)
            {
                // 检查是否已经创建了相应的选项卡
                if (LogTabControl.Items.Count < ViewModel.GameLogs.Count + 1) // +1是因为有一个主日志选项卡
                {
                    // 保留第一个选项卡（主日志），移除所有其他选项卡
                    while (LogTabControl.Items.Count > 1)
                    {
                        LogTabControl.Items.RemoveAt(1);
                    }

                    // 为每个游戏日志创建选项卡
                    foreach (GameLogItem gameLog in ViewModel.GameLogs)
                    {
                        AddLogTab(LogTabControl, gameLog);
                    }
                }
            }

            // 窗口加载完成后，确保日志滚动到底部
            Dispatcher.BeginInvoke(new Action(() =>
            {
                // 滚动主日志选项卡到底部
                ScrollLogToBottom(0);

                // 如果有其他选项卡且当前选中了其他选项卡，也滚动到底部
                if (LogTabControl.SelectedIndex > 0)
                {
                    ScrollLogToBottom(LogTabControl.SelectedIndex);
                }
            }), DispatcherPriority.Loaded);

            // 初始化游戏预览
            if (_gamePreview != null && ViewModel.SuperMultiGame_DataModelCollection.Count > 0)
            {
                // 设置所有游戏到预览控件
                _gamePreview.SetGames(ViewModel.SuperMultiGame_DataModelCollection);
            }

            // 显示欢迎通知
            NotificationService.Instance.Success("超级多开窗口已成功加载", "欢迎", 5000);
        }
    }
}