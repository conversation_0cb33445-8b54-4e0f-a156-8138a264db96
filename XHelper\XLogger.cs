﻿using Microsoft.VisualBasic;
using NLog;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using XHelper.Interface;
using XHelper.Models;
using static System.Net.Mime.MediaTypeNames;

namespace XHelper
{
    public static class XLogger
    {
        private static readonly object _busABcDatasLock = new object();
        private static ObservableCollection<LogModel> log = new();

        private static SynchronizationContext? SyncContext = null;

        public static ObservableCollection<LogModel> Log
        {
            get { return log; }
            set { log = value; }
        }

        public static Dictionary<string, ObservableCollection<LogModel>> OneLogPairs = [];

        private static string _allLog = "";

        // 这需要在应用程序启动时初始化
        // 使用接口而非具体实现类型
        public static ILogForwarder? LogForwarder { get; set; }

        // 辅助方法，将XLogger的"Tip"转换为标准级别字符串
        private static string GetStandardLogLevel(string xLoggerTip)
        {
            // XLogger的LogModel将Tip设置为"[提示]"，"[错误]"等
            // 它也有"Green"，相当于"Info"
            if (xLoggerTip != null)
            {
                if (xLoggerTip.Contains("错误") || xLoggerTip.Equals("Error", StringComparison.OrdinalIgnoreCase)) return "ERROR";
                if (xLoggerTip.Contains("警告") || xLoggerTip.Equals("Warn", StringComparison.OrdinalIgnoreCase)) return "WARN";
                if (xLoggerTip.Contains("调试") || xLoggerTip.Equals("Debug", StringComparison.OrdinalIgnoreCase)) return "DEBUG";
            }
            return "INFO"; // 默认为"[提示]"，"Green"或未知
        }

        /// <summary>
        /// 添加日志到日志列表
        /// </summary>
        /// <param name="log"></param>
        private static void _addAllLog(string log)
        {
            // 获取当前时间并格式化为 longdate 格式（包含四位毫秒数）
            string longdate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.ffff");

            // 在日志前面加上 longdate
            _allLog += $"[{longdate}]{log}\n";
        }

        /// <summary>
        /// 获取当前全部日志
        /// </summary>
        /// <returns></returns>
        public static string GetAllLog() => _allLog;

        /// <summary>
        /// 获取最近的日志条目
        /// </summary>
        /// <param name="count">要获取的日志条目数量上限</param>
        /// <returns>最近的日志条目数组</returns>
        public static string[] GetRecentLogs(int count)
        {
            lock (_busABcDatasLock)
            {
                // 从日志列表中获取最近的日志，排除debug类型的日志
                var recentLogs = Log.Where(log => log.Tip != "[调试]")
                    .Where(log => !log.Text.Contains("安全审计"))
                    .Take(Math.Min(count, Log.Count))
                    .Select(log => $"{log.Tip} {log.Text}")
                    .ToArray();

                return recentLogs;
            }
        }

        /// <summary>
        /// 保存未知错误
        /// </summary>
        /// <param name="e"></param>
        public static void SaveException(Exception e)
        {
            XLogger.Write($"发生未知异常！({e.Message})");
            XLogger.Write(e?.StackTrace?.ToString() ?? "没有相应的堆栈信息！");
            XLogger.Write(e?.InnerException?.Message ?? "没有相应的异常描述信息！");
        }

        /// <summary>
        /// 检查并限制日志集合大小
        /// </summary>
        /// <param name="logCollection">要检查的日志集合</param>
        private static void CheckAndLimitLogSize(ObservableCollection<LogModel> logCollection)
        {
            if (logCollection != null && logCollection.Count > 1000)
            {
                // 移除最老的日志直到数量为1000
                while (logCollection.Count > 1000)
                {
                    logCollection.RemoveAt(logCollection.Count - 1);
                }
            }
        }

        public static ObservableCollection<LogModel> GetLogObj(string name)
        {
            switch (name)
            {
                case "全部":
                    return Log;

                case "游戏1":
                    ObservableCollection<LogModel> obj11 = new();
                    OneLogPairs.TryGetValue("游戏1", out var obj1);
                    if (obj1 is null)
                    {
                        OneLogPairs.TryAdd("游戏1", obj11);
                        return obj11;
                    }
                    CheckAndLimitLogSize(obj1);
                    return obj1;

                case "游戏2":
                    ObservableCollection<LogModel> obj22 = new();
                    OneLogPairs.TryGetValue("游戏2", out var obj2);
                    if (obj2 is null)
                    {
                        OneLogPairs.TryAdd("游戏2", obj22);
                        return obj22;
                    }
                    CheckAndLimitLogSize(obj2);
                    return obj2;

                case "游戏3":
                    ObservableCollection<LogModel> obj33 = new();
                    OneLogPairs.TryGetValue("游戏3", out var obj3);
                    if (obj3 is null)
                    {
                        OneLogPairs.TryAdd("游戏3", obj33);
                        return obj33;
                    }
                    CheckAndLimitLogSize(obj3);
                    return obj3;

                case "游戏4":
                    ObservableCollection<LogModel> obj44 = new();
                    OneLogPairs.TryGetValue("游戏4", out var obj4);
                    if (obj4 is null)
                    {
                        OneLogPairs.TryAdd("游戏4", obj44);
                        return obj44;
                    }
                    CheckAndLimitLogSize(obj4);
                    return obj4;
            }
            ;
            return [];
        }

        /// <summary>
        /// 过滤游戏日志到对应容器
        /// </summary>
        private static void FiltterGameLog(LogModel item)
        {
            Dictionary<string, string> userNames = new()
            {
                {$" [{UserName1}] ", "游戏1"},
                {$" [{UserName2}] ", "游戏2"},
                {$" [{UserName3}] ", "游戏3"},
                {$" [{UserName4}] ", "游戏4"}
            };

            foreach (var pair in userNames)
            {
                if (item.Text.Contains(pair.Key))
                {
                    //添加到OneLogPairs中
                    OneLogPairs.TryAdd(pair.Value, []);
                    if (!OneLogPairs.TryGetValue(pair.Value, out ObservableCollection<LogModel>? lists)) return;
                    lists.Insert(0, item);

                    // 检查并限制游戏特定日志集合大小
                    if (lists.Count > 1000)
                    {
                        lists.RemoveAt(lists.Count - 1);
                    }

                    return;
                }
            }

            // 如果没有找到用户名,再检查游戏名称
            foreach (var value in userNames.Values)
            {
                if (item.Text.Contains(value))
                {
                    OneLogPairs.TryAdd(value, []);
                    if (!OneLogPairs.TryGetValue(value, out ObservableCollection<LogModel>? lists)) return;
                    lists.Insert(0, item);

                    // 检查并限制游戏特定日志集合大小
                    if (lists.Count > 1000)
                    {
                        lists.RemoveAt(lists.Count - 1);
                    }

                    return;
                }
            }
        }

        private static Logger _logger { get; set; } = LogManager.GetCurrentClassLogger();

        private static bool isShowDebug = false;

        private static string UserName1 = "游戏1";
        private static string UserName2 = "游戏2";
        private static string UserName3 = "游戏3";
        private static string UserName4 = "游戏4";

        public static void Init(bool isDebug)
        {
            Debug("初始化.. 删除三天前的日志文件夹..");
            DelLogFile(DateTime.Now.AddDays(-3));
            _logger?.Info("");
            _logger?.Info("=========DanDing-Started==========");
            _logger?.Info("");
            UserName1 = XConfig.LoadValueFromFile<string>("游戏1", "GameName") ?? "游戏1";
            UserName2 = XConfig.LoadValueFromFile<string>("游戏2", "GameName") ?? "游戏2";
            UserName3 = XConfig.LoadValueFromFile<string>("游戏3", "GameName") ?? "游戏3";
            UserName4 = XConfig.LoadValueFromFile<string>("游戏4", "GameName") ?? "游戏4";
            Info("日志模块：初始化完成.. 用户自定义名称载入成功..");
            isShowDebug = isDebug;
            Log.CollectionChanged += Log_CollectionChanged;
        }

        private static void Log_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            ObservableCollection<LogModel>? items = sender as ObservableCollection<LogModel>;
            if (Log.Count > 1000)
            {
                Log.CollectionChanged -= Log_CollectionChanged;
                Log.RemoveAt(Log.Count - 1);
                Log.CollectionChanged += Log_CollectionChanged;
            }
        }

        public static void InitThread(SynchronizationContext syncContext)
        {
            SyncContext = syncContext;
        }

        /// <summary>
        /// 删除指定日期前的日志
        /// </summary>
        /// <param name="time"></param>
        public static void DelLogFile(DateTime time)
        {
            try
            {
                string directorypath = AppDomain.CurrentDomain.BaseDirectory + "Logs";
                if (System.IO.Directory.Exists(directorypath))
                {
                    DirectoryInfo thisOne = new DirectoryInfo(directorypath);
                    DirectoryInfo[] subDirectories = thisOne.GetDirectories(); //获得目录
                                                                               //删除错误日志
                    if (subDirectories != null)
                    {
                        foreach (DirectoryInfo item in subDirectories)
                        {
                            DateTime filedate = DateTime.Now;
                            DateTime.TryParse(item.Name, out filedate);
                            if (time > filedate)
                            {
                                System.IO.Directory.Delete(item.FullName, true);
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        // --- 日志记录方法，带有LogForwarder支持 ---

        public static void Info(string msg)
        {
            _logger?.Info(msg); // NLog
            _addAllLog(msg);    // 内部字符串日志

            // 排队等待WebSocket转发
            // 注意：msg是原始消息。XHelper.Models.LogModel的Text属性会附加时间戳。
            // 我们将使用DateTime.UtcNow作为时间戳。
            LogForwarder?.EnqueueLog(DateTime.UtcNow, "INFO", msg);

            // 现有UI更新逻辑
            lock (_busABcDatasLock)
            {
                SyncContext?.Post(new SendOrPostCallback(delegate
                {
                    Log.Insert(0, new LogModel { Text = msg, Tip = "Info" });
                    FiltterGameLog(new LogModel { Text = msg, Tip = "Info" });
                }), null);
            }
        }

        public static void Info(string msg, params object[] args)
        {
            string formattedMsg = string.Format(msg, args);
            _logger?.Info(formattedMsg);
            _addAllLog(formattedMsg);
            LogForwarder?.EnqueueLog(DateTime.UtcNow, "INFO", formattedMsg);
            lock (_busABcDatasLock)
            {
                SyncContext?.Post(new SendOrPostCallback(delegate
                {
                    Log.Insert(0, new LogModel { Text = formattedMsg, Tip = "Info" });
                    FiltterGameLog(new LogModel { Text = formattedMsg, Tip = "Info" });
                }), null);
            }
        }

        public static void Info_Green(string msg)
        {
            _logger?.Info(msg);
            _addAllLog(msg);
            LogForwarder?.EnqueueLog(DateTime.UtcNow, "INFO", msg); // 或自定义"SUCCESS"级别
            lock (_busABcDatasLock)
            {
                SyncContext?.Post(new SendOrPostCallback(delegate
                {
                    Log.Insert(0, new LogModel { Text = msg, Tip = "Green" });
                    FiltterGameLog(new LogModel { Text = msg, Tip = "Green" });
                }), null);
            }
        }

        public static void Debug(string msg) // 假设Debug日志也应该被转发
        {
            _addAllLog(msg);
            if (!isShowDebug)
            {
                _logger?.Debug(msg);
                // 可选择决定是否转发未显示的调试日志
                // LogForwarder?.EnqueueLog(DateTime.UtcNow, "DEBUG", msg);
                return;
            }
            _logger?.Debug(msg);
            LogForwarder?.EnqueueLog(DateTime.UtcNow, "DEBUG", msg);
            lock (_busABcDatasLock)
            {
                SyncContext?.Post(new SendOrPostCallback(delegate
                {
                    Log.Insert(0, new LogModel { Text = msg, Tip = "Debug" });
                    FiltterGameLog(new LogModel { Text = msg, Tip = "Debug" });
                }), null);
            }
        }

        public static void Error(string msg, params object[] args)
        {
            string formattedMsg = string.Format(msg, args);
            _logger?.Error(formattedMsg);
            _addAllLog(formattedMsg);
            LogForwarder?.EnqueueLog(DateTime.UtcNow, "ERROR", formattedMsg);
            lock (_busABcDatasLock)
            {
                SyncContext?.Post(new SendOrPostCallback(delegate
                {
                    Log.Insert(0, new LogModel { Text = formattedMsg, Tip = "Error" });
                    FiltterGameLog(new LogModel { Text = formattedMsg, Tip = "Error" });
                }), null);
            }
        }

        public static void Warn(string msg, params object[] args)
        {
            string formattedMsg = string.Format(msg, args);
            _logger?.Warn(formattedMsg);
            _addAllLog(formattedMsg);
            LogForwarder?.EnqueueLog(DateTime.UtcNow, "WARN", formattedMsg);
            lock (_busABcDatasLock)
            {
                SyncContext?.Post(new SendOrPostCallback(delegate
                {
                    Log.Insert(0, new LogModel { Text = formattedMsg, Tip = "Warn" });
                    FiltterGameLog(new LogModel { Text = formattedMsg, Tip = "Warn" });
                }), null);
            }
        }

        // 如果需要转发，修改Write和OnlyWrite
        public static void Write(string msg) // 通常是内部错误
        {
            _logger?.Error(msg); // 作为ERROR记录到NLog
            _addAllLog(msg);
            LogForwarder?.EnqueueLog(DateTime.UtcNow, "ERROR", msg); // 作为ERROR转发
        }

        public static void OnlyWrite(string msg) // 仅对NLog的调试日志
        {
            _logger?.Debug(msg);
            // 决定这些是否应该被转发。如果它们纯粹用于本地NLog调试，可能不需要。
            // LogForwarder?.EnqueueLog(DateTime.UtcNow, "DEBUG", msg);
        }

        /// <summary>
        /// 打包日志文件到指定目录
        /// </summary>
        /// <param name="targetDirectory">目标目录路径</param>
        /// <param name="additionalFiles">额外需要打包的文件列表</param>
        /// <returns>打包后的ZIP文件完整路径</returns>
        public static string PackLogs(string targetDirectory, List<string>? additionalFiles = null)
        {
            try
            {
                // 创建目标目录（如果不存在）
                if (!Directory.Exists(targetDirectory))
                {
                    Directory.CreateDirectory(targetDirectory);
                }

                // 获取日志根目录
                string logsRootDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
                if (!Directory.Exists(logsRootDirectory))
                {
                    Info($"日志目录不存在: {logsRootDirectory}");
                    return string.Empty;
                }

                // 创建ZIP文件路径
                string zipFileName = $"logs_{DateTime.Now:yyyyMMdd_HHmmss}.zip";
                string zipFilePath = Path.Combine(targetDirectory, zipFileName);

                // 如果文件已存在，则删除
                if (File.Exists(zipFilePath))
                {
                    File.Delete(zipFilePath);
                }

                // 创建临时目录用于存放要打包的文件
                string tempDirectory = Path.Combine(Path.GetTempPath(), $"LogsPackage_{Guid.NewGuid()}");
                if (Directory.Exists(tempDirectory))
                {
                    Directory.Delete(tempDirectory, true);
                }
                Directory.CreateDirectory(tempDirectory);

                try
                {
                    // 用于跟踪已添加的文件，避免重复
                    HashSet<string> addedFiles = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

                    // 复制当前日期的日志文件到临时目录
                    string todayLogsDirectory = Path.Combine(logsRootDirectory, DateTime.Now.ToString("yyyy-MM-dd"));
                    if (Directory.Exists(todayLogsDirectory))
                    {
                        string destTodayDir = Path.Combine(tempDirectory, DateTime.Now.ToString("yyyy-MM-dd"));
                        Directory.CreateDirectory(destTodayDir);

                        // 获取今日日志目录中的所有文件
                        var logFiles = Directory.GetFiles(todayLogsDirectory);

                        // 先处理标准日志文件（按图片中显示的文件名）
                        string[] standardLogTypes = { "debug.txt", "error.txt", "info.txt", "log_all.txt", "warn.txt" };
                        foreach (string logType in standardLogTypes)
                        {
                            string sourceFile = Path.Combine(todayLogsDirectory, logType);
                            if (File.Exists(sourceFile))
                            {
                                string destFile = Path.Combine(destTodayDir, logType);
                                File.Copy(sourceFile, destFile);
                                addedFiles.Add(sourceFile.ToLower());
                                Debug($"添加标准日志文件: {logType}");
                            }
                        }

                        // 处理今日目录中的其他非标准日志文件
                        foreach (string file in logFiles)
                        {
                            // 如果文件已经添加过，则跳过
                            if (addedFiles.Contains(file.ToLower()))
                                continue;

                            string destFile = Path.Combine(destTodayDir, Path.GetFileName(file));
                            File.Copy(file, destFile);
                            addedFiles.Add(file.ToLower());
                            Debug($"添加其他日志文件: {Path.GetFileName(file)}");
                        }
                    }

                    // 创建一个包含当前内存中日志的文件
                    string memoryLogPath = Path.Combine(tempDirectory, "memory_log.txt");
                    File.WriteAllText(memoryLogPath, GetAllLog());
                    Debug("添加内存日志到日志包");

                    // 处理额外的文件
                    if (additionalFiles != null && additionalFiles.Count > 0)
                    {
                        string additionalFilesDir = Path.Combine(tempDirectory, "additional_files");
                        Directory.CreateDirectory(additionalFilesDir);

                        foreach (string filePath in additionalFiles)
                        {
                            // 检查文件是否存在，并且之前没有被添加过
                            if (File.Exists(filePath) && !addedFiles.Contains(filePath.ToLower()))
                            {
                                string fileName = Path.GetFileName(filePath);
                                string destFile = Path.Combine(additionalFilesDir, fileName);

                                // 如果目标文件名已存在，添加序号后缀
                                if (File.Exists(destFile))
                                {
                                    string fileNameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
                                    string fileExt = Path.GetExtension(fileName);
                                    int counter = 1;

                                    while (File.Exists(destFile))
                                    {
                                        destFile = Path.Combine(additionalFilesDir, $"{fileNameWithoutExt}_{counter}{fileExt}");
                                        counter++;
                                    }
                                }

                                File.Copy(filePath, destFile);
                                addedFiles.Add(filePath.ToLower());
                                Debug($"添加额外文件到日志包: {filePath}");
                            }
                            else if (!File.Exists(filePath))
                            {
                                Warn($"要打包的额外文件不存在: {filePath}");
                            }
                            else
                            {
                                Warn($"文件已经添加过，跳过重复添加: {filePath}");
                            }
                        }
                    }

                    // 创建压缩文件
                    ZipFile.CreateFromDirectory(tempDirectory, zipFilePath);

                    Info($"日志已成功打包到: {zipFilePath}，共包含 {addedFiles.Count + 1} 个文件"); // +1 是因为内存日志
                    return zipFilePath;
                }
                finally
                {
                    // 清理临时目录
                    if (Directory.Exists(tempDirectory))
                    {
                        Directory.Delete(tempDirectory, true);
                    }
                }
            }
            catch (Exception ex)
            {
                Error($"打包日志时发生错误: {ex.Message}");
                return string.Empty;
            }
        }
    }
}