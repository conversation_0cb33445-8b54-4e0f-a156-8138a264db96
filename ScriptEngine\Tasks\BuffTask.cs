﻿using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace ScriptEngine.Tasks
{
    internal class BuffTask : BaseTask
    {
        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "Buff");
        }

        private bool CloseAll = false;

        private bool OpenJC_Status = false;

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            if (Scene.NowIsScene("庭院"))
            {
            }
            else
            {
                if (!Scene.TO.TanSuo(true))//去探索
                {
                    log.Warn("Buff任务无法继续，当前游戏所在场景未知，请调整到庭院或探索主界面开始脚本！");
                    return;
                }
                var tupo_Count = Fast.Scence.TanSuo_GetTuPoCount();
                log.Debug("本地Ocr识别突破卷结果：" + tupo_Count);
                if (tupo_Count == 30)
                    Tmp.Do_Tupo(); //执行临时执行突破
            }

            List<string> buff_List = new List<string>();

            if (GetConfig.Others.TryGetValue("仅下一次使用", out string? onlyNext) && onlyNext == "1")
            {
                log.Info("仅下一次使用Buff，将在下一个任务完成后关闭所有Buff");
                Db.AddData("关Buff", 1);
            }
            bool open = false;
            foreach (var item in GetConfig.Others)
            {
                if (item.Value == "0")
                {
                    log.Info($"关所有Buff");
                    Off();
                }
                else
                {
                    if (!CloseAll)
                    {
                        log.Info($"开Buff前关闭所有Buff");
                        CloseAll = true;
                        Off();
                    }
                    //log.Info($"开Buff[{item.Key}]");
                    buff_List.Add(item.Key);
                    open = true;
                    //On(item.Key);
                }
            }

            if (open && !On_For_OCR(buff_List))
            {
                log.Warn("未通过OCR找到指定Buff，尝试使用原图色的方式开启Buff");
                foreach (var item in GetConfig.Others)
                {
                    log.Info($"开Buff[{item.Key}]");
                    On(item.Key);
                }
            }

            UserNotificationMessage = "OK.";
            log.Info("退出加成界面，结束加成任务..");
            Operational.Click(953, 234, 1025, 448);
            Sleep(1000);
        }

        /// <summary>
        /// 用OCR识别Buff并开启
        /// </summary>
        /// <param name="buff_List"></param>
        /// <returns></returns>
        private bool On_For_OCR(List<string> buff_List)
        {
            if (buff_List.Count == 0) return false;
            bool ret = false;
            string txts = Fast.Ocr_String(423, 133, 761, 505, t =>
            {
                foreach (var txt in t)
                {
                    foreach (var item in buff_List)
                    {
                        if (txt.Text.Contains(item))
                        {
                            log.Info("开启Buff：" + item);
                            Fast.Click(423 + txt.Center.X + 200, 133 + txt.Center.Y - 20);
                            ret = true;
                            Sleep(500);
                        }
                    }
                }
            });
            return ret;
        }

        /// <summary>
        /// 打开加成
        /// </summary>
        private bool OpenJC()
        {
            if (OpenJC_Status)
            {
                Sleep(600);
                return true;
            }

            if (Scene.NowIsScene("庭院"))
            {
                Operational.Click(369, 39, 390, 66);
                Sleep(1000);
            }
            else if (Scene.NowIsScene("探索"))
            {
                Operational.Click(429, 29, 452, 57);
                Sleep(1000);
            }
            else
            {
                XLogger.Info("Buff加成没有找到，无法继续操作Buff开关任务..");
                return false;
            }
            OpenJC_Status = true;
            return true;
        }

        private void On(string BuffName)
        {
            if (!OpenJC()) return;
            List<string> str_Pos = [];
            var pics = Mp.Filter(BuffName);
            foreach (var pic in pics.PicList)
            {
                string str = Dm.FindPicMemEx(370, 126, 428, 506, pic.Meminfo, pic.Delta_Color, 0.9, 0);
                str_Pos.Add(str);
                log.Debug($"Buff|{BuffName}|Ret:{str}");
            }

            foreach (var item in str_Pos)
            {
                if (string.IsNullOrEmpty(item)) continue;

                var Points = item.Split('|');
                foreach (var point in Points)
                {
                    if (string.IsNullOrEmpty(point)) continue;

                    var coordinates = point.Split(',');
                    if (coordinates.Length < 3) continue;

                    if (!int.TryParse(coordinates[1], out int x) ||
                        !int.TryParse(coordinates[2], out int y)) continue;

                    x += 409;
                    Operational.Click(x, y);
                    Sleep(1000);
                }
            }
        }

        /// <summary>
        /// 关闭所有Buff
        /// </summary>
        private void Off(bool open = true)
        {
            if (open && !OpenJC()) return;
            var pics = Mp.Filter("Buff开");
            bool ret = true;
            while (ret)
            {
                ret = pics.FindAll(out int x, out int y);
                if (ret) Operational.Click(x, y);
                Sleep(1000);
            }

            log.Info($"所有Buff已经被关闭..");
        }
    }
}