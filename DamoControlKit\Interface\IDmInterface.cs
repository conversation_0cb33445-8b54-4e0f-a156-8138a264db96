﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DamoControlKit.Interface
{
    interface IDmInterface
    {
        /// <summary>
        /// 大漠对象
        /// </summary>
        dmsoft? dmsoft { get; set; }

        /// <summary>
        /// 设置新的大漠对象
        /// </summary>
        /// <returns></returns>
        bool SetXsoft() => false;

        /// <summary>
        /// 设置存储一个新的大漠对象
        /// </summary>
        /// <param name="x">大漠对象</param>
        /// <returns></returns>
        bool SetXsoft(dmsoft x);
    }
}
