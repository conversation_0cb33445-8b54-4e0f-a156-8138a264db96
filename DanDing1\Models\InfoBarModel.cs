﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using Wpf.Ui.Controls;

namespace DanDing1.Models
{
    /// <summary>
    /// 提示消息类
    /// </summary>
    public partial class InfoBarModel : ObservableObject, INotifyPropertyChanged
    {
        [ObservableProperty]
        public string _title;

        [ObservableProperty]
        public string _message;

        [ObservableProperty]
        public InfoBarSeverity _severity = InfoBarSeverity.Informational;

        [ObservableProperty]
        public bool _isOpen = false;

        private bool que = false;

        /// <summary>
        /// 展示提示
        /// </summary>
        public void Show(string title, string message, InfoBarSeverity severity)
        {
            IsOpen = false;
            Title = title;
            Message = message;
            Severity = severity;
            que = true;
            IsOpen = true;
        }

        /// <summary>
        /// 展示提示 使用字符串表示严重性
        /// </summary>
        public void Show(string title, string message, string severityString)
        {
            IsOpen = false;
            Title = title;
            Message = message;
            Severity = ParseSeverity(severityString);
            que = true;
            IsOpen = true;
        }

        /// <summary>
        /// 展示提示
        /// </summary>
        /// <param name="title"></param>
        /// <param name="message"></param>
        /// <param name="severity">Success、Warning、Error</param>
        /// <param name="ms"></param>
        public async void Show(string title, string message, string severityString, int ms)
        {
            IsOpen = false;
            Title = title;
            Message = message;
            Severity = ParseSeverity(severityString);
            IsOpen = true;
            await Task.Delay(ms);
            if (que) { que = false; return; }
            IsOpen = false;
        }

        public void Hidden()
        {
            Title = "";
            Message = "";
            Severity = InfoBarSeverity.Informational;
            IsOpen = false;
        }

        /// <summary>
        /// 将字符串转换为InfoBarSeverity枚举
        /// </summary>
        /// <param name="severityString">严重性字符串</param>
        /// <returns>InfoBarSeverity枚举值</returns>
        private InfoBarSeverity ParseSeverity(string severityString)
        {
            if (string.IsNullOrEmpty(severityString))
                return InfoBarSeverity.Informational;

            return severityString.ToLower() switch
            {
                "success" => InfoBarSeverity.Success,
                "warning" => InfoBarSeverity.Warning,
                "error" => InfoBarSeverity.Error,
                _ => InfoBarSeverity.Informational
            };
        }
    }
}