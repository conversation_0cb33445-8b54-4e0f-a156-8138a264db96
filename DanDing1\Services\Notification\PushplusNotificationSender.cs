using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using XHelper;

namespace DanDing1.Services.Notification
{
    /// <summary>
    /// Pushplus推送通知发送器
    /// 基于Pushplus API实现：https://www.pushplus.plus/doc/function/one.html
    /// </summary>
    public class PushplusNotificationSender : BaseNotificationSender
    {
        private readonly HttpClient _httpClient;
        private const string API_URL = "https://www.pushplus.plus/send"; // Pushplus官方API地址

        /// <summary>
        /// 构造函数
        /// </summary>
        public PushplusNotificationSender()
        {
            _httpClient = new HttpClient();
        }

        /// <summary>
        /// 通知类型标识符
        /// </summary>
        public override string NoticeType => "pushplus";

        /// <summary>
        /// 通知类型显示名称
        /// </summary>
        public override string DisplayName => "Pushplus推送";

        /// <summary>
        /// 格式化Pushplus推送内容
        /// </summary>
        /// <param name="content">原始内容</param>
        /// <returns>格式化后的内容</returns>
        public override string FormatContent(string content)
        {
            // Pushplus支持HTML格式，可以使用HTML标签替换换行符
            return content.Replace("#换行", "\n");
        }

        /// <summary>
        /// 发送Pushplus推送的核心实现
        /// 基于Pushplus官方API：https://www.pushplus.plus/doc/function/one.html
        /// </summary>
        /// <param name="title">通知标题</param>
        /// <param name="content">通知内容</param>
        /// <param name="extraParams">额外参数，可包含token(string)和template(string)等</param>
        /// <returns>发送是否成功</returns>
        protected override async Task<bool> SendNotificationCoreAsync(string title, string content, object extraParams)
        {
            try
            {
                // 获取Pushplus token
                string token = GetToken(extraParams);
                if (string.IsNullOrEmpty(token))
                {
                    XLogger.Error("Pushplus推送失败：未配置Token");
                    return false;
                }

                // 获取模板类型（可选）
                string template = GetTemplate(extraParams);
                
                // 构建请求数据
                var requestData = new
                {
                    token = token,             // 用户令牌，必填
                    title = title,             // 消息标题，必填
                    content = content,         // 消息内容，必填
                    template = template,       // 模板类型，可选，默认为html
                    channel = "wechat",        // 发送渠道，可选，微信公众号
                    webhook = string.Empty,    // webhook编码，可选
                    callbackUrl = string.Empty // 回调地址，可选
                };

                var json = JsonSerializer.Serialize(requestData);
                var requestContent = new StringContent(json, Encoding.UTF8, "application/json");

                // 发送请求
                var response = await _httpClient.PostAsync(API_URL, requestContent);

                // 解析响应
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<PushplusResponse>(responseContent);

                    if (result?.Success == true)
                    {
                        //XLogger.Info($"Pushplus推送成功，消息ID: {result.Data}");
                        return true;
                    }
                    else
                    {
                        XLogger.Error($"Pushplus推送API返回错误: {result?.Msg}, 代码: {result?.Code}");
                        return false;
                    }
                }

                XLogger.Error($"Pushplus推送请求失败，HTTP状态码: {response.StatusCode}");
                return false;
            }
            catch (Exception ex)
            {
                XLogger.Error($"发送Pushplus推送异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从额外参数或配置中获取Token
        /// </summary>
        /// <param name="extraParams">额外参数</param>
        /// <returns>Token字符串</returns>
        private string GetToken(object extraParams)
        {
            // 尝试从extraParams中获取token
            if (extraParams is System.Collections.Generic.Dictionary<string, object> dict && 
                dict.TryGetValue("token", out var tokenObj) && 
                tokenObj is string token && 
                !string.IsNullOrEmpty(token))
            {
                return token;
            }

            // 从全局配置获取token
            return GlobalData.Instance.UserConfig.Notice_Pushplus_Token ?? string.Empty;
        }

        /// <summary>
        /// 从额外参数或配置中获取模板类型
        /// </summary>
        /// <param name="extraParams">额外参数</param>
        /// <returns>模板类型字符串</returns>
        private string GetTemplate(object extraParams)
        {
            // 尝试从extraParams中获取template
            if (extraParams is System.Collections.Generic.Dictionary<string, object> dict && 
                dict.TryGetValue("template", out var templateObj) && 
                templateObj is string template && 
                !string.IsNullOrEmpty(template))
            {
                return template;
            }

            // 默认使用html模板
            return "html";
        }

        /// <summary>
        /// Pushplus推送响应数据结构
        /// </summary>
        private class PushplusResponse
        {
            /// <summary>
            /// 响应代码，200或0表示成功
            /// </summary>
            public int Code { get; set; }

            /// <summary>
            /// 响应消息
            /// </summary>
            public string Msg { get; set; }

            /// <summary>
            /// 响应数据（消息ID）
            /// </summary>
            public string Data { get; set; }
            
            /// <summary>
            /// 是否成功
            /// </summary>
            public bool Success => Code == 200 || Code == 0;
        }
    }
} 