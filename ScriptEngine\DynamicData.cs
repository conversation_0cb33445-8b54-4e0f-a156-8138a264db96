﻿using DamoControlKit.Model;
using ScriptEngine.Helpers;
using ScriptEngine.Model;
using System.Diagnostics;
using System.Text.RegularExpressions;
using XHelper;
using XHelper.DanDingNet;

namespace ScriptEngine
{
    /// <summary>
    /// 脚本动态数据信息类
    /// </summary>
    public static class DynamicData
    {
        /// <summary>
        /// 云字库列表
        /// </summary>
        public static List<string> DictList;

        public static Dictionary<int, string> DictsColor = new()
        {
            {0,"2e2b27-303030" }
        };

        private const string DictsName = "Dicts.json";

        private const string PicsName = "Pics.json";

        private const string PossName = "Pos.json";

        private static string Base_Url = "null";

        private static string pattern = @"\.(\D+)\d+";

        private static MemPics? pics = null;

        private static string Pics_txt;

        private static string Poss_txt;

        private static string Ver = "";

        /// <summary>
        /// 用于线程同步的静态锁对象
        /// </summary>
        private static readonly object _lockObj = new object();

        /// <summary>
        /// 标记图库是否已初始化的volatile变量，确保跨线程可见性
        /// </summary>
        private static volatile bool _isPicInit = false;

        /// <summary>
        /// MainPics 图库
        /// </summary>
        public static MemPics? Pics
        {
            get { return pics; }
            set
            {
                XLogger.Debug($"Main_Pics 被更新！版本号：{Ver}");
                pics = value;
            }
        }

        private static bool IsPicInit
        {
            get { return _isPicInit; }
            set { _isPicInit = value; }
        }

        private static bool IsInit => !String.IsNullOrEmpty(Base_Url) && !String.IsNullOrEmpty(Ver);

        /// <summary>
        /// 筛选类名 返回组合
        /// </summary>
        /// <param name="className"></param>
        /// <returns></returns>
        public static MemPics FilterPicsClass(string className, bool Rem = true)
        {
            MemPics Mp = new();
            foreach (var item in Pics?.PicList ?? [])
            {
                string classN = item.Name.Split('.')[0];
                if (classN.Contains(className))
                    Mp.Add((MemPic)item.Clone());
            }

            if (Rem)
            {
                //设置补偿图库资源
                var repics = FilterPicsClass("Remedy", false);
                foreach (var item in Mp.PicList)
                {
                    var name = GetReName(item._Name);
                    item.Remedy_Pics = FilterPicsNameByLists(repics, name, false);
                }
            }

            return Mp;
        }

        /// <summary>
        /// 筛选类名 返回组合
        /// </summary>
        /// <param name="className"></param>
        /// <returns></returns>
        public static MemPics FilterPicsName(string picName, bool Rem = true)
        {
            MemPics Mp = new();
            foreach (var item in Pics?.PicList ?? [])
            {
                string picN = item.Name.Split('.')[1];
                if (picN.Contains(picName))
                    Mp.Add((MemPic)item.Clone());
            }

            if (Rem)
                //设置补偿图库资源
                foreach (var item in Mp.PicList)
                {
                    var name = GetReName(item._Name);
                    item.Remedy_Pics = FilterPicsClass(name);
                }

            return Mp;
        }

        /// <summary>
        /// 筛选类名 返回组合
        /// </summary>
        /// <param name="className"></param>
        /// <returns></returns>
        public static MemPics FilterPicsNameByLists(MemPics Pics, string picName, bool Rem = true)
        {
            MemPics Mp = new();
            foreach (var item in Pics?.PicList ?? [])
            {
                string picN = item.Name.Split('.')[1];
                if (picN.Contains(picName))
                    Mp.Add((MemPic)item.Clone());
            }

            if (Rem)//设置补偿图库资源
                foreach (var item in Mp.PicList)
                {
                    var name = GetReName(item._Name);
                    item.Remedy_Pics = FilterPicsClass(name);
                }

            return Mp;
        }

        /// <summary>
        /// 初始化-同步
        /// </summary>
        /// <param name="ver"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static bool Init(string ver)
        {
            Ver = ver;
            if (String.IsNullOrEmpty(Base_Url) || String.IsNullOrEmpty(Ver))
                throw new Exception("Net Init Error...");
            try
            {
                // 双重检查锁定模式
                if (!IsPicInit)
                {
                    lock (_lockObj)
                    {
                        if (!IsPicInit)
                        {
                            Updata();
                        }
                    }
                }
            }
            catch (Exception e)
            {
                XLogger.Error("初始化图库发生错误：" + e.Message);
                return false;
            }
            return true;
        }

        /// <summary>
        /// 初始化-异步
        /// </summary>
        /// <param name="ver"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static async Task<bool> InitAsync(string server, string ver)
        {
            Ver = ver;
            if (String.IsNullOrEmpty(Base_Url) || String.IsNullOrEmpty(Ver))
                throw new Exception("Net Init Error...");
            if (server == "临时")
            {
                string host = DDApi.ServerHost[XConfig.LoadValueFromFile<string>("ServerHost") ?? "港式接口1"];
                Base_Url = await XNet.GetStringAsync(host + DDApi.Api["获取临时图库源地址"]) + "/";
                Base_Url = Base_Url.Replace("\"", null);
                if (Base_Url == "/")
                {
                    XLogger.Warn("图库获取临时地址获取失败！（使用默认地址）");
                    Base_Url = "null";
                }
                XLogger.Debug("用户选择了临时图库节点..设置成功：" + Base_Url);
            }

            try
            {
                if (server == "本地")
                {
                    XLogger.Debug("用户选择了本地图库节点..调用路径：D://DD_Core/，版本号：" + ver);
                    // 双重检查锁定模式
                    if (!IsPicInit)
                    {
                        lock (_lockObj)
                        {
                            if (!IsPicInit)
                            {
                                Updata_Local(ver);
                            }
                        }
                    }
                    return true;
                }

                // 双重检查锁定模式
                if (!IsPicInit)
                {
                    // 对于异步初始化，先获取锁再检查状态
                    bool lockTaken = false;
                    try
                    {
                        Monitor.Enter(_lockObj, ref lockTaken);
                        if (!IsPicInit)
                        {
                            // 释放锁后执行异步操作
                            if (lockTaken)
                            {
                                Monitor.Exit(_lockObj);
                                lockTaken = false;
                            }

                            await UpdataAsync();

                            // 异步操作完成后重新加锁设置标志
                            Monitor.Enter(_lockObj, ref lockTaken);
                            IsPicInit = true;
                        }
                    }
                    finally
                    {
                        // 确保锁被释放
                        if (lockTaken)
                        {
                            Monitor.Exit(_lockObj);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                XLogger.Error("初始化图库发生错误：" + e.Message);
                throw;
            }
            return true;
        }

        /// <summary>
        /// 重置初始化标志
        /// </summary>
        public static void ReSetInitStatus()
        {
            lock (_lockObj)
            {
                XLogger.Debug($"图库重置了初始化标志{IsPicInit} -> False");
                IsPicInit = false;
            }
        }

        /// <summary>
        /// 设置图库Url
        /// </summary>
        /// <param name="host"></param>
        public static void SetHost(string? host)
        {
            Base_Url = host ?? "null";
        }

        /// <summary>
        /// 手动更新数据
        /// </summary>
        public static void Updata()
        {
            if (Base_Url == "null")
                throw new Exception("图库地址未设置！无法继续执行！");
            DictList = new();

            Pics_txt = XNet.GetStringAsync(Base_Url + Ver + "/" + PicsName).Result;
            Poss_txt = XNet.GetStringAsync(Base_Url + Ver + "/" + PossName).Result;
            string Dicts_txt = XNet.GetStringAsync(Base_Url + Ver + "/" + DictsName).Result;

            if (Debugger.IsAttached)
            {
                XLogger.Debug("Pics_txt获取成功：" + Pics_txt);
                XLogger.Debug("Poss_txt获取成功：" + Poss_txt);
                XLogger.Debug("Dicts_txt获取成功：" + Dicts_txt);
            }

            Data_Pics pics = XSerializer.DeserializeJsonTxtToObject<Data_Pics>(Pics_txt, true);//解析json文件
            Data_Poss poss = XSerializer.DeserializeJsonTxtToObject<Data_Poss>(Poss_txt, true);//解析json文件
            using StringReader sr = new StringReader(Dicts_txt);
            string? line;
            while ((line = sr.ReadLine()) != null)
                DictList.Add(line);

            MergeDataJson merge = new(pics, poss);
            Pics = merge.GetMemPics();
        }

        /// <summary>
        /// 更新图库—本地
        /// </summary>
        public static void Updata_Local(string ver)
        {
            DictList = new();
            string _Base_Url = "D://DD_Core/";
            if (!Directory.Exists(_Base_Url + ver))
                throw new Exception("本地图库位置不存在！");
            if (!File.Exists(_Base_Url + ver + "/" + DictsName))
                throw new Exception($"本地图库相关版本配置不存在！[{ver}|{DictsName}]");
            if (!File.Exists(_Base_Url + ver + "/" + PicsName))
                throw new Exception($"本地图库相关版本配置不存在！[{ver}|{PicsName}]");
            if (!File.Exists(_Base_Url + ver + "/" + PossName))
                throw new Exception($"本地图库相关版本配置不存在！[{ver}|{PossName}]");

            Pics_txt = File.ReadAllText(_Base_Url + ver + "/" + PicsName);
            Poss_txt = File.ReadAllText(_Base_Url + ver + "/" + PossName);
            string Dicts_txt = File.ReadAllText(_Base_Url + ver + "/" + DictsName);

            if (Debugger.IsAttached)
            {
                XLogger.Debug($"Pics_txt获取成功：{Pics_txt.Length}长度");
                XLogger.Debug($"Poss_txt获取成功：{Poss_txt.Length}长度");
                XLogger.Debug($"Dicts_txt获取成功：{Dicts_txt.Length}长度");
            }

            Data_Pics pics = XSerializer.DeserializeJsonTxtToObject<Data_Pics>(Pics_txt, true);//解析json文件
            Data_Poss poss = XSerializer.DeserializeJsonTxtToObject<Data_Poss>(Poss_txt, true);//解析json文件
            using StringReader sr = new StringReader(Dicts_txt);
            string? line;
            while ((line = sr.ReadLine()) != null)
                DictList.Add(line);

            MergeDataJson merge = new(pics, poss);
            Pics = merge.GetMemPics();
        }

        /// <summary>
        /// 手动更新数据
        /// </summary>
        public static async Task UpdataAsync()
        {
            if (Base_Url == "null")
                throw new Exception("图库地址未设置！无法继续执行！");

            DictList = new();

            Pics_txt = await XNet.GetStringAsync(Base_Url + Ver + "/" + PicsName);
            Poss_txt = await XNet.GetStringAsync(Base_Url + Ver + "/" + PossName);
            string Dicts_txt = await XNet.GetStringAsync(Base_Url + Ver + "/" + DictsName);
            Pics_txt = Pics_txt.Replace("\"", null);
            Poss_txt = Poss_txt.Replace("\"", null);
            Dicts_txt = Dicts_txt.Replace("\"", null).Replace("\\n", "\r\n");
            if (Debugger.IsAttached)
            {
                XLogger.Debug("Pics_txt获取成功：" + Pics_txt);
                XLogger.Debug("Poss_txt获取成功：" + Poss_txt);
                XLogger.Debug("Dicts_txt获取成功：" + Dicts_txt);
            }

            Data_Pics pics = XSerializer.DeserializeJsonTxtToObject<Data_Pics>(Pics_txt, true);//解析json文件
            Data_Poss poss = XSerializer.DeserializeJsonTxtToObject<Data_Poss>(Poss_txt, true);//解析json文件
            using StringReader sr = new StringReader(Dicts_txt);
            string? line;
            while ((line = sr.ReadLine()) != null)
                DictList.Add(line);

            MergeDataJson merge = new(pics, poss);
            Pics = merge.GetMemPics();
        }

        private static string GetReName(string name)
        {
            // 查找匹配项
            Match match = Regex.Match(name, pattern);

            if (match.Success)
            {
                // 提取捕获的组（"标记"）
                string result = match.Groups[1].Value;
                return result;
            }
            return name.Split('.')[1];
        }

        /// <summary>
        /// 输出基础信息
        /// </summary>
        /// <returns></returns>
        public new static string ToString()
        {
            return $"""图库版本：{Ver} 载入状态：{IsPicInit} 图库数量：{pics?.Count ?? 0} 字库数量：{DictList?.Count ?? 0}""";
        }

        /// <summary>
        /// 达摩图片的预定义点击位置字典
        /// </summary>
        private static readonly Dictionary<string, string> DamoPositions = new()
        {
            { "位置1", "403,127,839,152" },
            { "位置2", "360,702,921,715" },
            { "位置3", "59,197,111,467" },
            { "位置4", "1143,211,1220,497" }
        };

        /// <summary>
        /// 胜利/失败图片的预定义点击位置字典
        /// </summary>
        private static readonly Dictionary<string, string> VictoryFailurePositions = new()
        {
            { "位置1", "423,140,542,246" },
            { "位置2", "697,127,990,242" },
            { "位置3", "360,702,921,715" },
            { "位置4", "1145,179,1220,508" },
            { "位置5", "64,203,126,417" }
        };

        /// <summary>
        /// 更新指定图片的点击位置
        /// </summary>
        /// <param name="victoryFailurePattern">控制图片名字中存在"胜利、失败"的点击位置变更（如"位置1"、"位置2"等，传入"关闭"则不进行操作）</param>
        /// <param name="damoPattern">控制图片名字中存在"达摩"的点击位置变更（如"位置1"、"位置2"等，传入"关闭"则不进行操作）</param>
        public static void UpdateClickPositions(string victoryFailurePattern, string damoPattern)
        {
            if (Pics == null || Pics.PicList.Count == 0)
            {
                XLogger.Warn("图库未初始化或为空，无法更新点击位置");
                return;
            }

            foreach (var pic in Pics.PicList)
            {
                string picName = pic.Name.Split('.')[1];

                // 处理"胜利、失败"图片的点击位置
                if (!string.IsNullOrEmpty(victoryFailurePattern) &&
                    victoryFailurePattern != "关闭" &&
                    (picName.Contains("胜利") || picName.Contains("失败")))
                {
                    // 检查位置是否在预定义字典中
                    if (VictoryFailurePositions.TryGetValue(victoryFailurePattern, out string? positionStr))
                    {
                        // 清空现有位置集合（通过将空列表赋值给新的位置）
                        if (pic.ClickPositions != null)
                        {
                            while (pic.ClickPositions.Count > 0)
                            {
                                pic.ClickPositions.RemoveAt(0);
                            }
                        }

                        // 使用AddClickPosition方法添加新位置
                        pic.AddClickPosition(new Position(positionStr));

                        //XLogger.Debug($"已更新胜利/失败图片点击位置: {pic._Name}, 位置: {victoryFailurePattern}");
                    }
                    else
                    {
                        //XLogger.Warn($"未找到胜利/失败图片的预定义位置: {victoryFailurePattern}");
                    }
                }

                // 处理"达摩"图片的点击位置
                if (!string.IsNullOrEmpty(damoPattern) &&
                    damoPattern != "关闭" &&
                    picName.Contains("达摩"))
                {
                    // 检查位置是否在预定义字典中
                    if (DamoPositions.TryGetValue(damoPattern, out string? positionStr))
                    {
                        // 清空现有位置集合（通过将空列表赋值给新的位置）
                        if (pic.ClickPositions != null)
                        {
                            while (pic.ClickPositions.Count > 0)
                            {
                                pic.ClickPositions.RemoveAt(0);
                            }
                        }

                        // 使用AddClickPosition方法添加新位置
                        pic.AddClickPosition(new Position(positionStr));

                        //XLogger.Debug($"已更新达摩图片点击位置: {pic._Name}, 位置: {damoPattern}");
                    }
                    else
                    {
                        //XLogger.Warn($"未找到达摩图片的预定义位置: {damoPattern}");
                    }
                }
            }

            XLogger.Info($"点击位置更新完成，更新模式: 胜利/失败[{victoryFailurePattern}], 达摩[{damoPattern}]");
        }
    }
}