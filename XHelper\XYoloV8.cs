﻿using Compunet.YoloSharp;
using Compunet.YoloSharp.Data;
using System.Diagnostics;

namespace XHelper
{
    public enum YoloModels
    {
        Detect, Classify, DetectBg
    };

    /// <summary>
    /// YoloV8自定义帮助类
    /// </summary>
    public static class XYoloV8
    {
        private static readonly object _busABcDatasLock = new object();
        private static YoloPredictor? _Predictor;

        /// <summary>
        /// 缓存yolo对象
        /// </summary>
        private static Dictionary<YoloModels, YoloPredictor> LastVoloV8 = new();

        private static Dictionary<YoloModels, string> modelsPath = new()
        {
            { YoloModels.Detect, @".\runtimes\models\Ts.onnx" },
            { YoloModels.Classify, @".\runtimes\models\Cs.onnx" },
            { YoloModels.DetectBg, @".\runtimes\models\Bg.onnx" }
        };

        /// <summary>
        /// 控制是否启用YOLO功能
        /// </summary>
        private static bool _isEnabled = true;

        /// <summary>
        /// 模型文件是否存在的缓存
        /// </summary>
        private static Dictionary<YoloModels, bool> _modelFileExists = new();

        /// <summary>
        /// 获取或设置YOLO功能是否启用
        /// </summary>
        public static bool IsEnabled
        {
            get { return _isEnabled; }
            set { _isEnabled = value; }
        }

        /// <summary>
        /// 重置YOLO功能状态，清除错误缓存
        /// </summary>
        public static void Reset()
        {
            _isEnabled = true;
            _modelFileExists.Clear();
        }

        public static YoloResult<Classification>? Classify(byte[] imgdata)
        {
            if (!_isEnabled)
            {
                XLogger.Debug("YOLO识别功能已禁用");
                return null;
            }

            if (_Predictor is null)
            {
                XLogger.Warn("模型没有预载入！无法预测图像！");
                return null;
            }

            try
            {
                lock (_busABcDatasLock)
                {
                    return _Predictor.Classify(imgdata, new() { Confidence = 0.45f });
                }
            }
            catch (Exception e)
            {
                XLogger.SaveException(e);
                XLogger.Error("图像分类过程发生错误，暂时禁用YOLO功能");
                _isEnabled = false;
                return null;
            }
        }

        /// <summary>
        /// 大模型图像预测解析
        /// </summary>
        public static YoloResult<Detection>? Detect(byte[] imgdata)
        {
            if (!_isEnabled)
            {
                XLogger.Debug("YOLO识别功能已禁用");
                return null;
            }

            if (_Predictor is null)
            {
                XLogger.Warn("模型没有预载入！无法预测图像！");
                return null;
            }

            try
            {
                lock (_busABcDatasLock)
                {
                    return _Predictor.Detect(imgdata, new() { Confidence = 0.45f });
                }
            }
            catch (Exception e)
            {
                XLogger.SaveException(e);
                XLogger.Error("目标检测过程发生错误，暂时禁用YOLO功能");
                _isEnabled = false;
                return null;
            }
        }

        public static bool LoadModels(YoloModels models)
        {
            if (!_isEnabled)
            {
                XLogger.Debug("YOLO识别功能已禁用，跳过模型加载");
                return false;
            }

            // 检查缓存中是否已知模型文件不存在
            if (_modelFileExists.TryGetValue(models, out bool exists) && !exists)
            {
                XLogger.Debug($"模型文件之前已确认不存在，跳过加载 [{models}]");
                return false;
            }

            try
            {
                // 检查模型文件是否存在
                string modelPath = modelsPath[models];
                if (!System.IO.File.Exists(modelPath))
                {
                    XLogger.Warn($"模型文件不存在: {modelPath}");
                    _modelFileExists[models] = false;
                    return false;
                }

                //查看缓存有没有这个类型
                if (LastVoloV8.TryGetValue(models, out _Predictor))
                {
                    _Predictor = LastVoloV8[models];
                    Debug.WriteLine($"Ai预测模型加载完成！[缓存：{models}]");
                    return true;
                }

                LastVoloV8.TryAdd(models, new YoloPredictor(modelsPath[models]));
                _Predictor = LastVoloV8[models];
                _modelFileExists[models] = true;
            }
            catch (Exception e)
            {
                XLogger.SaveException(e);
                XLogger.Error($"Ai预测模型 [{models}] 加载失败！");
                return false;
            }

            XLogger.Debug($"Ai预测模型加载完成！[{models}]");
            return true;
        }

        /// <summary>
        /// 加载模型&识别模型
        /// </summary>
        /// <param name="models"></param>
        /// <param name="imgdata"></param>
        /// <returns></returns>
        public static object? yoloV8(YoloModels models, byte[] imgdata)
        {
            if (!_isEnabled)
            {
                XLogger.Debug("YOLO识别功能已禁用");
                return null;
            }

            try
            {
                if (!LoadModels(models))
                {
                    return null;
                }

                if (models == YoloModels.Classify)
                    return Classify(imgdata);
                else if (models == YoloModels.Detect || models == YoloModels.DetectBg)
                    return Detect(imgdata);
                else
                {
                    XLogger.Warn($"未知的模型类型: {models}");
                    return null;
                }
            }
            catch (Exception e)
            {
                XLogger.SaveException(e);
                XLogger.Error("YOLO处理过程中发生错误，暂时禁用YOLO功能");
                _isEnabled = false;
                return null;
            }
        }

        /// <summary>
        /// 尝试启用YOLO功能
        /// </summary>
        /// <returns>是否成功启用</returns>
        public static bool TryEnable()
        {
            _isEnabled = true;
            _modelFileExists.Clear();

            try
            {
                // 尝试加载所有模型检查是否可用
                foreach (YoloModels model in Enum.GetValues(typeof(YoloModels)))
                {
                    if (!System.IO.File.Exists(modelsPath[model]))
                    {
                        XLogger.Warn($"模型文件不存在: {modelsPath[model]}");
                        return false;
                    }
                }

                return true;
            }
            catch (Exception e)
            {
                XLogger.SaveException(e);
                XLogger.Error("尝试启用YOLO功能失败");
                _isEnabled = false;
                return false;
            }
        }
    }
}