﻿using System.Runtime.Intrinsics.Arm;
using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;

namespace ScriptEngine.Tasks.LiuDao
{
    internal class LiuDaoTask : BaseTask
    {
#pragma warning disable CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑添加 "required" 修饰符或声明为可为 null。

        /// <summary>
        /// 场景识别图片
        /// </summary>
        internal MemPics ChangJingShiBie_Pics;

        internal LiuDaoTask_Config Config;

        /// <summary>
        /// 六道所有岛屿
        /// </summary>
        internal List<string> DaoYu_List = ["鏖战", "混沌", "神秘", "星之屿", "宁息", "Boss", "选择"];

        /// <summary>
        /// 岛屿识别图片
        /// </summary>
        internal MemPics DaoYuShiBie_Pics;

        /// <summary>
        /// 使用双倍次数
        /// </summary>
        internal int Double_Count = 0;

        /// <summary>
        /// 战斗图片
        /// </summary>
        internal MemPics ZhanDou_Pics;

        /// <summary>
        /// Boss次数
        /// </summary>
        private int Boss_Count = 0;

        private Dictionary<string, Pixel> Colors = new Dictionary<string, Pixel>()
        {
            {"处于选择岛屿界面",new Pixel(1195,249,"d4d0c6-101010",0.96) },
            {"处于选择岛屿界面1",new Pixel(1201,248,"c5c1b3-202020",0.96) },
            {"不再提醒",new Pixel(759,431,"f3b25e-101010",0.96) },
        };

        private Dictionary<string, Position> Find_Pos = new()
        {
            {"六道中",new(1071,226,1244,257) }
        };

#pragma warning restore CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑添加 "required" 修饰符或声明为可为 null。

        private bool ret = false;

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "六道");
            ZhanDou_Pics = Mp.Filter("六道.战斗_")
                .Add(Mp.Filter("六道.事件_获得奖励"))
                .Add(Mp.Filter("六道.事件_选Buff"));
            DaoYuShiBie_Pics = Mp.Filter("六道.岛屿识别_");
            ChangJingShiBie_Pics = Mp.Filter("六道.场景识别_");
            //初始化组件
            foreach (var item in Colors)
                item.Value.SetXsoft(Dm);
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Config = new LiuDaoTask_Config(configs);
            //FastTest();
            log.Warn("开始全自动六道任务，请注意以上几点：");
            log.Warn("1.新模拟器自己手动先打一把完整的六道(跳过一些动画)；");
            log.Warn("2.购买双倍只会再系统提示后进行购买，不主动购买！");
            log.Warn("3.极时使用双倍同样也是系统提示后执行的操作！");
            log.Warn("4.六道任务的预设更换任务只能在探索场景中更换！");
            Sleep(3000);

            string nowDaoYu = NowInDaoYu();
            if (nowDaoYu != "" && nowDaoYu != "选择")
            {
                log.Info($"当前处于{nowDaoYu}岛屿，直接从中途开始！.");
                ReScan();
                return;
            }

            //场景判断 + 场景 移动
            string nows = Scene.NowScene;
            if (nows != "六道")
            {
                //先去探索，获取突破卷数量
                if (!Scene.TO.TanSuo())
                {
                    log.Warn("六道任务无法继续，当前游戏所在场景未知，请调整到庭院或探索主界面开始脚本！");
                    return;
                }

                var tupo_Count = Fast.Scence.TanSuo_GetTuPoCount();
                log.Debug("本地Ocr识别突破卷结果：" + tupo_Count);
                if (tupo_Count == 30)
                    Tmp.Do_Tupo(); //执行临时执行突破

                if (UserConfig_Preset != null)
                {
                    //使用预设
                    List<string> preset = [.. UserConfig_Preset.Split('|')];
                    log.Info($"进入突破的式神录，开始应用预设{UserConfig_Preset}");
                    Fast.Click("254,640,305,687");
                    Sleep(1500);
                    Fast.Click("1217,624,1247,649");
                    Sleep(1500);
                    Tmp.Do_Preset(preset);
                    Sleep(500);
                    log.Info($"退出突破界面，继续探索任务...");
                    Fast.Click("1191,117,1229,151");
                    Sleep(1500);
                }

                //再去六道
                Scene.TO.LiuDao();
            }
            main();
            UserNotificationMessage = $"共战斗{Boss_Count}/{Config.Ncount}次. 使用{Double_Count}次双倍.";
        }

        private void FastTest()
        {
            //new LiuDao_DaoYuTask(this, "测试").Start();

            // ReScan:
            //     Sleep(1000);
            //     string nowDaoYu = NowInDaoYu();
            //     if (nowDaoYu != "" && nowDaoYu != "选择")
            //         new LiuDao_DaoYuTask(this, nowDaoYu).Start();
            //     var dict = ScanNowDaoYu();
            //     if (dict.Count != 0)
            //     {
            //         log.Info($"识别到岛屿：[{GetDaoYuRealName(dict[0].Key)}]，进入该岛屿子逻辑！");
            //         //点击坐标
            //         Fast.Click(dict[0].Value.X, dict[0].Value.Y);
            //         Sleep(1500);
            //         if (NowInDaoYu() == "")
            //             goto ReScan;
            //         new LiuDao_DaoYuTask(this, NowInDaoYu()).Start();
            //     }
            //     else
            //         goto ReScan;
            //     FastTest();
        }

        /// <summary>
        /// 从图片名称获取岛屿真实名字
        /// </summary>
        /// <returns></returns>
        private string GetDaoYuRealName(string picname)
        {
            foreach (var item in DaoYu_List)
            {
                if (picname.Contains(item))
                    return item;
            }
            return "";
        }

        private void HandleDaoYu(string nowDaoYu)
        {
            bool isBoss = nowDaoYu == "Boss";
            new LiuDao_DaoYuTask(this, nowDaoYu).Start();
            if (isBoss)
            {
                Boss_Count++;
                log.Info($"Boss战斗次数：{Boss_Count}/{Config.Ncount}");
                if (Boss_Count >= Config.Ncount)
                {
                    log.Info($"Boss战斗总次数达到{Config.Ncount}次，退出六道任务..");
                    ret = true;
                    return;
                }
                Config.Reset();
                StartLiuDao();
            }
        }

        /// <summary>
        /// 主流程
        /// </summary>
        private void main()
        {
            //检查是否在选择岛屿界面
            if (Colors["处于选择岛屿界面"].Find(null) || Colors["处于选择岛屿界面1"].Find(null))
            {
                log.Info_Green("当前处于选择岛屿界面，直接从中途开始！.");
                ReScan();
                return;
            }
            //检测手法2
            if (Fast.Ocr_String(Find_Pos["六道中"]).Contains("迎战月读"))
            {
                log.Info_Green("当前处于六道中，直接从中途开始！.");
                ReScan();
                return;
            }

            StartLiuDao();
            ReScan();
        }

        /// <summary>
        /// 返回当前在哪个岛屿内
        /// </summary>
        /// <returns></returns>
        private string NowInDaoYu()
        {
            if (ChangJingShiBie_Pics.FindAllE(out var lists))
            {
                foreach (var item in DaoYu_List)
                {
                    if (lists[0].Contains(item))
                        return item;
                }
            }
            return "";
        }

        private void ReScan()
        {
            while (!ret)
            {
                Sleep(1000);
                string nowDaoYu = NowInDaoYu();
                if (nowDaoYu != "" && nowDaoYu != "选择")
                {
                    HandleDaoYu(nowDaoYu);
                }
                else
                {
                    var dict = ScanNowDaoYu();
                    if (dict.Count != 0)
                    {
                        log.Info($"识别到岛屿：[{GetDaoYuRealName(dict[0].Key)}]，进入该岛屿子逻辑！");
                        //点击坐标
                        Fast.Click(dict[0].Value.X, dict[0].Value.Y);
                        Sleep(500);
                        if (NowInDaoYu() == "")
                            continue;
                        HandleDaoYu(NowInDaoYu());
                    }
                }
            }
            log.Info("六道任务结束..退出六道..");
            Fast.Click(24, 27, 53, 54);
            Sleep(1000);
            Fast.Click(24, 27, 53, 54);
            Sleep(1000);
        }

        /// <summary>
        /// 扫描当前存在的岛屿
        /// </summary>
        /// <returns></returns>
        private List<KeyValuePair<string, Point>> ScanNowDaoYu()
        {
            var dict = new Dictionary<string, Point>();
            foreach (var t in DaoYuShiBie_Pics.PicList)
            {
                Point? point = t.FindPoint(Dm);
                if (point is not null)
                    dict.Add(t._Name, point);
            }
            //根据岛屿选择优先级排序
            var list = dict.OrderBy(p =>
            {
                var index = Config.DaoYu_Priority.FindIndex(x => p.Key.Contains(x));
                return index == -1 ? int.MaxValue : index;
            }).ToList();
            return list;
        }

        /// <summary>
        /// 开始六道任务的初始化流程
        /// </summary>
        private void StartLiuDao()
        {
            int clickInterval = 0;
            if (Db.PendingTimerTask) //执行定时任务
            {
                Db.PendingTimerTask = false;
                log.Info("暂停当前任务，执行定时任务，退出到探索..");
                Fast.Click(20, 24, 55, 57);
                Sleep(1000);
                Fast.Click(20, 24, 55, 57);
                Sleep(1000);
                Db?.TimerTask?.DoAllTask();
                Sleep(1000);
                throw new Exception("定时任务执行结束，重新执行当前的主任务..");
            }

            log.Info("等待月之海-椒图本的开启界面..");
            //判断是否能开始
            while (!Mp.Filter("椒图本_开始").FindAll())
            {
                Sleep(1000);
                clickInterval++;

                //每3秒点击一次范围
                if (clickInterval >= 3)
                {
                    Fast.Click(603, 20, 714, 45);
                    clickInterval = 0;
                }
            }
            //点击开始
            log.Info("点击开启1按钮.");
            Fast.Click(1136, 595, 1214, 664);
            Sleep(5000);
            Fast.Click(1151, 596, 1228, 659);
            log.Info("点击确定按钮.");
            Sleep(4000);
            while (!Fast.Ocr_String(487, 17, 846, 70).Contains("拖动式神"))
            {
                Sleep(1000);
                if (!Fast.Ocr_String(487, 17, 846, 70).Contains("拖动式神"))
                    Fast.Click(1151, 596, 1228, 659);
            }

            Fast.Click(1136, 595, 1214, 664);
            log.Info("点击开启2按钮.");
            Sleep(600);
            if (Colors["不再提醒"].Find(null))
            {
                log.Info("点击不再提醒..");
                Fast.Click(575, 348, 597, 370);
                Sleep(600);
                log.Info("点击确定..");
                Fast.Click(709, 415, 814, 449);
                Sleep(600);
            }
            Sleep(4500);

            //等待选择初始Buff
            while (Mp.Filter("事件_初始Buff").FindAll())
            {
                log.Info("选择初始Buff[柔风抱暖].");
                Fast.Click(278, 579, 375, 610);
                Sleep(2000);
            }
        }
    }
}