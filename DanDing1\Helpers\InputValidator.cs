using System;
using System.Text.RegularExpressions;
using System.Security;
using System.Linq;

namespace DanDing1.Helpers
{
    /// <summary>
    /// 用户输入验证工具类
    /// 用于验证和清理用户输入，防止安全风险
    /// </summary>
    public static class InputValidator
    {
        // 用户名正则表达式：字母、数字、下划线和汉字，长度9-30
        private static readonly Regex UsernameRegex = new Regex(@"^[a-zA-Z0-9_\u4e00-\u9fa5]{9,30}$", RegexOptions.Compiled);

        // 密码正则表达式：至少包含一个大写字母、一个小写字母和一个数字，长度9-30
        private static readonly Regex PasswordRegex = new Regex(@"^[\w\W]{9,30}$", RegexOptions.Compiled);

        // 邮箱正则表达式
        private static readonly Regex EmailRegex = new Regex(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", RegexOptions.Compiled);

        // 卡密正则表达式：仅允许字母和数字，长度为16或32
        private static readonly Regex KamiRegex = new Regex(@"^[a-zA-Z0-9]+$", RegexOptions.Compiled);

        // 验证码正则表达式：6位数字
        private static readonly Regex CodeRegex = new Regex(@"^[a-zA-Z0-9]{5}$", RegexOptions.Compiled);

        // 检查危险字符，如SQL注入和XSS攻击的常见字符
        private static readonly Regex DangerousCharsRegex = new Regex(@"[;'""<>]|--|\bOR\b|\bAND\b|\bUNION\b|\bSELECT\b|\bFROM\b|\bWHERE\b|\bDROP\b|\bALTER\b|\bDELETE\b|\bINSERT\b|\bUPDATE\b|\bCREATE\b|\bSCRIPT\b|\bALERT\b|\bCONFIRM\b|\bPROMPT\b|\bEVAL\b", RegexOptions.IgnoreCase | RegexOptions.Compiled);

        /// <summary>
        /// 验证用户名格式是否正确
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>验证结果，包含是否有效和错误消息</returns>
        public static (bool IsValid, string ErrorMessage) ValidateUsername(string username)
        {
            if (string.IsNullOrEmpty(username))
                return (false, "用户名不能为空");

            if (username.Length < 9)
                return (false, "用户名长度不能少于9个字符");

            if (username.Length > 30)
                return (false, "用户名长度不能超过30个字符");

            if (!UsernameRegex.IsMatch(username))
                return (false, "用户名只能包含字母、数字、下划线和汉字");

            if (ContainsDangerousCharacters(username))
                return (false, "用户名包含非法字符");

            return (true, string.Empty);
        }

        /// <summary>
        /// 验证密码格式是否正确
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns>验证结果，包含是否有效和错误消息</returns>
        public static (bool IsValid, string ErrorMessage) ValidatePassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                return (false, "密码不能为空");

            if (password.Length < 9)
                return (false, "密码长度不能少于9个字符");

            if (password.Length > 30)
                return (false, "密码长度不能超过30个字符");

            if (!PasswordRegex.IsMatch(password))
                return (false, "密码长度必须在9-30个字符之间");

            return (true, string.Empty);
        }

        /// <summary>
        /// 验证邮箱格式是否正确
        /// </summary>
        /// <param name="email">邮箱地址</param>
        /// <returns>验证结果，包含是否有效和错误消息</returns>
        public static (bool IsValid, string ErrorMessage) ValidateEmail(string email)
        {
            if (string.IsNullOrEmpty(email))
                return (false, "邮箱不能为空");

            if (!EmailRegex.IsMatch(email))
                return (false, "邮箱格式不正确");

            if (ContainsDangerousCharacters(email))
                return (false, "邮箱包含非法字符");

            return (true, string.Empty);
        }

        /// <summary>
        /// 验证卡密格式是否正确
        /// </summary>
        /// <param name="kami">卡密</param>
        /// <returns>验证结果，包含是否有效和错误消息</returns>
        public static (bool IsValid, string ErrorMessage) ValidateKami(string kami)
        {
            if (string.IsNullOrEmpty(kami))
                return (false, "卡密不能为空");

            if (!KamiRegex.IsMatch(kami))
                return (false, "卡密格式不正确，只能包含字母和数字");

            if (ContainsDangerousCharacters(kami))
                return (false, "卡密包含非法字符");

            return (true, string.Empty);
        }

        /// <summary>
        /// 验证验证码格式是否正确
        /// </summary>
        /// <param name="code">验证码</param>
        /// <returns>验证结果，包含是否有效和错误消息</returns>
        public static (bool IsValid, string ErrorMessage) ValidateCode(string code)
        {
            if (string.IsNullOrEmpty(code))
                return (false, "验证码不能为空");

            if (!CodeRegex.IsMatch(code))
                return (false, "验证码格式不正确，应为5位字母或数字");

            return (true, string.Empty);
        }

        /// <summary>
        /// 检查输入是否包含危险字符
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>是否包含危险字符</returns>
        public static bool ContainsDangerousCharacters(string input)
        {
            if (string.IsNullOrEmpty(input))
                return false;

            return DangerousCharsRegex.IsMatch(input);
        }

        /// <summary>
        /// 清理输入，移除或转义危险字符
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>清理后的字符串</returns>
        public static string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            // 替换常见的危险字符
            string sanitized = input
                .Replace("'", "''")
                .Replace("<", "&lt;")
                .Replace(">", "&gt;")
                .Replace("\"", "&quot;")
                .Replace(";", "&#59;");

            return sanitized;
        }
    }
}