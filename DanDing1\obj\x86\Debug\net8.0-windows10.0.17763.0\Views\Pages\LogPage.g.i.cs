﻿#pragma checksum "..\..\..\..\..\..\Views\Pages\LogPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D1CB1818C215128E3D30C0D62ED0885C43961977"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Models;
using DanDing1.Views.Pages;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Pages {
    
    
    /// <summary>
    /// LogPage
    /// </summary>
    public partial class LogPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 42 "..\..\..\..\..\..\Views\Pages\LogPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LogFiltter;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\..\Views\Pages\LogPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button ShowLogWindow;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\..\..\Views\Pages\LogPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.SymbolIcon ShowLogWindow_Icon;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\..\..\Views\Pages\LogPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button OpenLogsFolderButton;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\..\..\Views\Pages\LogPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button SaveLogsButton;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\..\Views\Pages\LogPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl itemsControl;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\..\..\Views\Pages\LogPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CommandBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/pages/logpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\Views\Pages\LogPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.LogFiltter = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 2:
            this.ShowLogWindow = ((Wpf.Ui.Controls.Button)(target));
            
            #line 53 "..\..\..\..\..\..\Views\Pages\LogPage.xaml"
            this.ShowLogWindow.Click += new System.Windows.RoutedEventHandler(this.Button_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ShowLogWindow_Icon = ((Wpf.Ui.Controls.SymbolIcon)(target));
            return;
            case 4:
            this.OpenLogsFolderButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 61 "..\..\..\..\..\..\Views\Pages\LogPage.xaml"
            this.OpenLogsFolderButton.Click += new System.Windows.RoutedEventHandler(this.OpenLogsFolderButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SaveLogsButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 67 "..\..\..\..\..\..\Views\Pages\LogPage.xaml"
            this.SaveLogsButton.Click += new System.Windows.RoutedEventHandler(this.SaveLogsButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.itemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 7:
            this.CommandBox = ((System.Windows.Controls.TextBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

