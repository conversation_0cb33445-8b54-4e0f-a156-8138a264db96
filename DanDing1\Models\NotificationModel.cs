using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;

namespace DanDing1.Models
{
    /// <summary>
    /// 通知类型
    /// </summary>
    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error
    }

    /// <summary>
    /// 通知模型
    /// </summary>
    public class NotificationModel : INotifyPropertyChanged
    {
        private string _message;
        private string _title;
        private NotificationType _type;

        public string Message
        {
            get => _message;
            set
            {
                _message = value;
                OnPropertyChanged();
            }
        }

        public string Title
        {
            get => _title;
            set
            {
                _title = value;
                OnPropertyChanged();
            }
        }

        public NotificationType Type
        {
            get => _type;
            set
            {
                _type = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(IconColor));
                OnPropertyChanged(nameof(IconSymbol));
            }
        }

        public DateTime CreatedAt { get; set; }

        public ICommand CloseCommand { get; set; }

        public Brush IconColor => Type switch
        {
            NotificationType.Success => new SolidColorBrush(Color.FromRgb(82, 196, 26)),
            NotificationType.Warning => new SolidColorBrush(Color.FromRgb(235, 173, 23)),
            NotificationType.Error => new SolidColorBrush(Color.FromRgb(245, 108, 108)),
            _ => new SolidColorBrush(Color.FromRgb(64, 158, 255))
        };

        public string IconSymbol => Type switch
        {
            NotificationType.Success => "CheckmarkCircle24",
            NotificationType.Warning => "Warning24",
            NotificationType.Error => "ErrorCircle24",
            _ => "Info24"
        };

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}