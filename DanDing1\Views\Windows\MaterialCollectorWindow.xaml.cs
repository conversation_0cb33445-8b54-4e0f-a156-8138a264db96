using ShareX.HelpersLib;
using ShareX.ScreenCaptureLib;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.IO.Compression;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Interop;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using Wpf.Ui.Controls;

namespace DanDing1.Views.Windows
{
    public partial class MaterialCollectorWindow : Window
    {
        private IntPtr targetWindowHandle;
        private string tempFolder;
        private List<string> capturedImages;
        private DispatcherTimer autoTimer;

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        public MaterialCollectorWindow()
        {
            InitializeComponent();

            // 创建临时文件夹
            tempFolder = Path.Combine(Path.GetTempPath(), "DanDing_MaterialCollector");
            if (!Directory.Exists(tempFolder))
            {
                Directory.CreateDirectory(tempFolder);
            }

            capturedImages = new List<string>();
            LoadExistingImages();

            // 初始化自动截图定时器
            autoTimer = new DispatcherTimer();
            autoTimer.Interval = TimeSpan.FromSeconds(1);
            autoTimer.Tick += AutoTimer_Tick;
        }

        private void AutoTimer_Tick(object? sender, EventArgs e)
        {
            CaptureButton_Click(null, null);
        }

        private void AutoCapture_CheckedChanged(object sender, RoutedEventArgs e)
        {
            if (sender is CheckBox checkBox)
            {
                if (checkBox.IsChecked == true)
                {
                    autoTimer.Start();
                }
                else
                {
                    autoTimer.Stop();
                }
            }
        }

        private void LoadExistingImages()
        {
            if (Directory.Exists(tempFolder))
            {
                string[] files = Directory.GetFiles(tempFolder, "*.bmp");
                foreach (string file in files)
                {
                    capturedImages.Add(file);
                    ImageList.Items.Add(Path.GetFileName(file));
                }
                UpdateButtonStates();
            }
        }

        /// <summary>
        /// 选中的窗口信息
        /// bind用
        /// </summary>
        public WindowInfo? SelectedWindow { get; private set; }

        [DllImport("user32.dll")]
        private static extern IntPtr WindowFromPoint(int x, int y);

        private void BindWindow_Click(object sender, RoutedEventArgs e)
        {
            SelectedWindow = null;
            RegionCaptureOptions Options = new();
            Options.DetectControls = false;
            SimpleWindowInfo simpleWindowInfo = RegionCaptureTasks.GetWindowInfo(Options, out int x, out int y);
            if (simpleWindowInfo != null)
            {
                IntPtr windowHandle = WindowFromPoint(x, y);
                SelectedWindow = new WindowInfo(windowHandle);
                targetWindowHandle = SelectedWindow.Handle;
                WindowInfo.Text = $"已绑定窗口 - 分辨率: {SelectedWindow.ClientRectangle.Width}x{SelectedWindow.ClientRectangle.Height}";

                // 启用截图按钮和自动截图选项
                CaptureButton.IsEnabled = true;
                AutoCapture.IsEnabled = true;
                UpdateButtonStates();
            }
        }

        private void CaptureButton_Click(object sender, RoutedEventArgs e)
        {
            if (targetWindowHandle == IntPtr.Zero) return;

            RECT rect;
            GetWindowRect(targetWindowHandle, out rect);
            int width = rect.Right - rect.Left;
            int height = rect.Bottom - rect.Top;

            using (Bitmap bitmap = new Bitmap(width, height))
            {
                using (Graphics graphics = Graphics.FromImage(bitmap))
                {
                    graphics.CopyFromScreen(rect.Left, rect.Top, 0, 0, new System.Drawing.Size(width, height));
                }

                string fileName = $"{DateTime.Now:yyyyMMdd_HHmmss}.bmp";
                string filePath = Path.Combine(tempFolder, fileName);
                bitmap.Save(filePath, ImageFormat.Bmp);

                capturedImages.Add(filePath);
                ImageList.Items.Add(fileName);
                ImageList.SelectedIndex = ImageList.Items.Count - 1;
            }

            UpdateButtonStates();
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (capturedImages.Count == 0) return;

            string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            string zipFileName = $"素材收集_{DateTime.Now:yyyyMMdd_HHmmss}.zip";
            string zipPath = Path.Combine(desktopPath, zipFileName);

            using (ZipArchive archive = ZipFile.Open(zipPath, ZipArchiveMode.Create))
            {
                foreach (string imagePath in capturedImages)
                {
                    archive.CreateEntryFromFile(imagePath, Path.GetFileName(imagePath));
                }
            }

            // 打开资源管理器并选中文件
            System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{zipPath}\"");
        }

        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            int selectedIndex = ImageList.SelectedIndex;
            if (selectedIndex == -1) return;

            string filePath = capturedImages[selectedIndex];
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }

            capturedImages.RemoveAt(selectedIndex);
            ImageList.Items.RemoveAt(selectedIndex);

            PreviewImage.Source = null;
            UpdateButtonStates();
        }

        private void ImageList_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            int selectedIndex = ImageList.SelectedIndex;
            if (selectedIndex != -1 && selectedIndex < capturedImages.Count)
            {
                string imagePath = capturedImages[selectedIndex];
                if (File.Exists(imagePath))
                {
                    BitmapImage bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.UriSource = new Uri(imagePath);
                    bitmap.EndInit();
                    PreviewImage.Source = bitmap;
                }
            }

            UpdateButtonStates();
        }

        private void UpdateButtonStates()
        {
            if (targetWindowHandle != IntPtr.Zero)
            {
                CaptureButton.IsEnabled = true;
                AutoCapture.IsEnabled = true;
                SaveButton.IsEnabled = capturedImages.Count > 0;
                DeleteButton.IsEnabled = ImageList.SelectedIndex != -1;
            }
            else
            {
                CaptureButton.IsEnabled = false;
                AutoCapture.IsEnabled = false;
                SaveButton.IsEnabled = false;
                DeleteButton.IsEnabled = false;
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            // 停止自动截图
            autoTimer.Stop();

            base.OnClosed(e);

            // 清理临时文件夹中的文件
            if (Directory.Exists(tempFolder))
            {
                try
                {
                    Directory.Delete(tempFolder, true);
                }
                catch { }
            }
        }
    }
}