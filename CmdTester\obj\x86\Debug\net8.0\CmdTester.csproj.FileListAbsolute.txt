C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\leptonica-1.82.0.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\tesseract50.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x64\leptonica-1.82.0.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x64\tesseract50.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\CmdTester.exe
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\NLog.Config
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\onnxruntime.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\angle_net.onnx
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\crnn_lite_lstm.onnx
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\dbnet.onnx
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\keys.txt
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\models\Cs.onnx
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\models\Ts.onnx
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\num.traineddata
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\concrt140.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\cvextern.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\msvcp140.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\msvcp140_1.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\msvcp140_2.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\msvcp140_codecvt_ids.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\opencv_videoio_ffmpeg440.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\vcruntime140.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\dm.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\DmReg.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\CmdTester.deps.json
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\CmdTester.runtimeconfig.json
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\CmdTester.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\CmdTester.pdb
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Clipper2Lib.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Emgu.CV.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Emgu.CV.Bitmap.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Lazy.Captcha.Core.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.ML.OnnxRuntime.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\NLog.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\OpenCvSharp.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\SixLabors.Fonts.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\SixLabors.ImageSharp.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\SixLabors.ImageSharp.Drawing.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\SkiaSharp.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.CodeDom.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.Drawing.Common.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.Management.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Tesseract.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\YoloV8.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\android\native\onnxruntime.aar
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework.zip
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\linux-arm64\native\libonnxruntime.so
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\linux-arm64\native\libonnxruntime_providers_shared.so
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime.so
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime_providers_shared.so
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\osx-arm64\native\libonnxruntime.dylib
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\osx-x64\native\libonnxruntime.dylib
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-arm64\native\onnxruntime.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-arm64\native\onnxruntime.lib
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-arm64\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-arm64\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\onnxruntime.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\onnxruntime.lib
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\onnxruntime.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\onnxruntime.lib
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win\lib\net8.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\OpenCvSharpExtern.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\opencv_videoio_ffmpeg4100_64.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\OpenCvSharpExtern.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\opencv_videoio_ffmpeg4100.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\osx\native\libSkiaSharp.dylib
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-arm64\native\libSkiaSharp.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\libSkiaSharp.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\libSkiaSharp.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win\lib\net8.0\System.Management.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\DamoControlKit.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\OcrLiteLib.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\ScriptEngine.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\XHelper.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Emgu.CV.Platform.NetStandard.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\clipper_library.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\DamoControlKit.pdb
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\ScriptEngine.pdb
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\XHelper.pdb
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\OcrLiteLib.pdb
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Emgu.CV.Platform.NetStandard.xml
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.csproj.AssemblyReference.cache
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.AssemblyInfoInputs.cache
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.AssemblyInfo.cs
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.csproj.CoreCompileInputs.cache
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.csproj.Up2Date
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\refint\CmdTester.dll
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.pdb
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.genruntimeconfig.cache
C:\Users\<USER>\ansel\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\ref\CmdTester.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\onnxruntime.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\leptonica-1.82.0.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\tesseract50.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x64\leptonica-1.82.0.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x64\tesseract50.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\CmdTester.exe
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\NLog.Config
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\cls.onnx
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\det.onnx
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\keys.txt
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\models\Bg.onnx
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\models\Cs.onnx
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\models\Ts.onnx
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\num.traineddata
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\PaddleOCR_cpp.exe
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\rec.onnx
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\concrt140.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\cvextern.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\msvcp140.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\msvcp140_1.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\msvcp140_2.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\msvcp140_codecvt_ids.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\opencv_videoio_ffmpeg440.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\x86\vcruntime140.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\dm.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\DmReg.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\CmdTester.deps.json
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\CmdTester.runtimeconfig.json
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\CmdTester.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\CmdTester.pdb
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Clipper2Lib.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\CommunityToolkit.HighPerformance.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Emgu.CV.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Emgu.CV.Bitmap.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Lazy.Captcha.Core.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.ML.OnnxRuntime.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Minio.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\NLog.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\OpenCvSharp.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\SixLabors.Fonts.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\SixLabors.ImageSharp.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\SixLabors.ImageSharp.Drawing.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\SkiaSharp.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.CodeDom.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.Drawing.Common.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.Private.Windows.Core.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.IO.Hashing.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.IO.Pipelines.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.Management.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.Numerics.Tensors.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.Reactive.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\System.Text.Json.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\Tesseract.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\YoloSharp.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\android\native\onnxruntime.aar
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework.zip
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\linux-arm64\native\libonnxruntime.so
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\linux-arm64\native\libonnxruntime_providers_shared.so
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime.so
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime_providers_shared.so
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\osx-arm64\native\libonnxruntime.dylib
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\osx-x64\native\libonnxruntime.dylib
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-arm64\native\onnxruntime.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-arm64\native\onnxruntime.lib
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-arm64\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-arm64\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\onnxruntime.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\onnxruntime.lib
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\onnxruntime.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\onnxruntime.lib
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win\lib\net8.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\OpenCvSharpExtern.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\opencv_videoio_ffmpeg4100_64.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\OpenCvSharpExtern.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\opencv_videoio_ffmpeg4100.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\linux-arm\native\libSkiaSharp.so
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\linux-arm64\native\libSkiaSharp.so
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\linux-musl-x64\native\libSkiaSharp.so
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\linux-x64\native\libSkiaSharp.so
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\osx\native\libSkiaSharp.dylib
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-arm64\native\libSkiaSharp.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x64\native\libSkiaSharp.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win-x86\native\libSkiaSharp.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\win\lib\net8.0\System.Management.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\DamoControlKit.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\OcrLiteLib.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\ScriptEngine.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\XHelper.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\clipper_library.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\DamoControlKit.pdb
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\DamoControlKit.xml
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\ScriptEngine.pdb
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\XHelper.pdb
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.AssemblyInfo.cs
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.csproj.Up2Date
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\refint\CmdTester.dll
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.pdb
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\CmdTester.genruntimeconfig.cache
C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\obj\x86\Debug\net8.0\ref\CmdTester.dll
