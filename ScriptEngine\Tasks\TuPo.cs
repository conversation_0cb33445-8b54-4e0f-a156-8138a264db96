﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System.Runtime.InteropServices;

namespace ScriptEngine.Tasks
{
    internal class TuPo : BaseTask
    {
        /// <summary>
        /// 使用Windows API调用MessageBox显示对话框
        /// </summary>
        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        private static extern int MessageBox(IntPtr hWnd, string text, string caption, uint type);

        /// <summary>
        /// 设置窗口位置
        /// </summary>
        [DllImport("user32.dll")]
        private static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);

        /// <summary>
        /// 查找窗口
        /// </summary>
        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        private static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

        /// <summary>
        /// 获取前台窗口句柄
        /// </summary>
        [DllImport("user32.dll")]
        private static extern IntPtr GetForegroundWindow();

        /// <summary>
        /// 设置前台窗口
        /// </summary>
        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        // MessageBox 类型常量
        private const uint MB_YESNO = 0x00000004;

        private const uint MB_ICONQUESTION = 0x00000020;
        private const uint MB_TOPMOST = 0x00040000;  // 设置顶置
        private const uint MB_SETFOREGROUND = 0x00010000;  // 获取焦点

        // MessageBox 返回值常量
        private const int IDYES = 6;

        private const int IDNO = 7;

        // SetWindowPos 常量
        private static readonly IntPtr HWND_TOPMOST = new IntPtr(-1);

        private const uint SWP_NOSIZE = 0x0001;
        private const uint SWP_NOMOVE = 0x0002;
        private const uint SWP_SHOWWINDOW = 0x0040;

        /// <summary>
        /// 是否开启标记功能
        /// </summary>
        private bool Biaoji = false;

        private bool BiaoJi_Status = false;

        private Dictionary<string, Position> biaoJiPoss = new()
        {
            {"位置1",new Position("239,515,268,544") },
            {"位置2",new Position("439,461,465,490") },
            {"位置3",new Position("617,410,639,438") },
            {"位置4",new Position("793,461,825,500") },
            {"位置5",new Position("1001,498,1033,549") },
        };

        public string BiaoJi_Str { get; private set; }

        /// <summary>
        /// 检查可进攻战斗位置
        /// </summary>
        private Dictionary<int, Point> CheckCombatPairs = new()
        {
            {1,new(450,160) },
            {2,new(781,160) },
            {3,new(1112,160) },
            {4,new(450,294) },
            {5,new(781,294) },
            {6,new(1112,294) },
            {7,new(450,431) },
            {8,new(781,431) },
            {9,new(1112,431) },
        };

        /// <summary>
        /// 战斗再次确认区域
        /// </summary>
        private Dictionary<int, Position> CombatPairs = new()
        {
            {1,new(346,367,419,403) },
            {2,new(673,368,755,402) },
            {3,new(1012,369,1094,406) },
            {4,new(348,503,428,535) },
            {5,new(679,507,762,538) },
            {6,new(1007,502,1097,542) },
            {7,new(348,640,429,674) },
            {8,new(677,639,760,676) },
            {9,new(1013,642,1083,674) },
        };

        private int count = 0;

        /// <summary>
        /// 日志不输出的突破名
        /// </summary>
        private List<string> DontSendLog = ["标记"];

        /// <summary>
        /// 任务次数
        /// </summary>
        private int Ncount = 0;

        /// <summary>
        /// 卡级功能
        /// </summary>
        private bool Tui4 = false;

        /// <summary>
        /// 刷新前是否需要用户确认
        /// </summary>
        private bool User_Affirm = false;

        /// <summary>
        /// 突破卷数量
        /// </summary>
        private int tupo_Count = -1;

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "突破");
            //初始化大漠插件到组件中
            foreach (var item in CheckCombatPairs)
                item.Value.SetXsoft(dm);
            foreach (var item in CombatPairs)
                item.Value.SetXsoft(dm);
            foreach (var item in biaoJiPoss)
                item.Value.SetXsoft(Dm);
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            try
            {
                base.Start(configs);
                Ncount = GetConfig.Count;

                // 安全解析Biaoji配置
                GetConfig.Others.TryGetValue("Biaoji", out string? s);
                if (!new List<string>() { "位置1", "位置2", "位置3", "位置4", "位置5" }.Contains(s ?? ""))
                {
                    BiaoJi_Str = "不标记";
                    Biaoji = false;
                }
                else
                {
                    BiaoJi_Str = s ?? "位置5";
                    Biaoji = true;
                }

                // 安全解析Tui4配置
                GetConfig.Others.TryGetValue("Tui4", out s);
                try { Tui4 = s is null ? false : bool.Parse(s); }
                catch { Tui4 = false; log.Debug("Tui4参数解析失败，使用默认值false"); }

                GetConfig.Others.TryGetValue("Affirm", out s);
                try { User_Affirm = s is null ? false : bool.Parse(s); }
                catch { User_Affirm = false; log.Debug("Affirm参数解析失败，使用默认值false"); }

                //场景判断
                string nows = Scene.NowScene;
                if (nows != "突破")
                {
                    //先去探索，获取突破卷数量
                    if (!Scene.TO.TanSuo())
                    {
                        log.Warn("突破任务无法继续，当前游戏所在场景未知，请调整到庭院或探索主界面开始脚本！");
                        return;
                    }
                    tupo_Count = Fast.Scence.TanSuo_GetTuPoCount();
                    log.Debug("本地Ocr识别突破卷结果：" + tupo_Count);
                    //再去探索
                    Scene.TO.TuPo();
                }

                //重新识别突破卷
                if (tupo_Count == -1)
                {
                    //重新识别识别突破卷
                    tupo_Count = Fast.Scence.TuPo_GetTuPoCount();
                    log.Debug("本地Ocr识别突破卷结果：" + tupo_Count);
                    if (tupo_Count == -1)
                    {
                        log.Error("突破任务无法继续，未识别到您的突破卷数量！");
                        Fast.Click("1181,114,1227,153");
                        Sleep(1500);
                        return;
                    }
                }

                if (tupo_Count < Ncount)
                {
                    log.Warn($"请注意：您当前设置{Ncount}次突破任务，但突破卷只有{tupo_Count}张！");
                    Ncount = tupo_Count;
                }
                main();
                UserNotificationMessage = $"共战斗{count}/{Ncount}次.";
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("A task was canceled")) throw;
                log.Error($"突破任务执行出错: {ex.Message}");
                if (ex.InnerException != null)
                {
                    log.Error($"内部异常: {ex.InnerException.Message}");
                }
                throw;
            }
        }

        /// <summary>
        /// 返回列表中任意一个数
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private static int GetRandomElement(List<int> list)
        {
            if (list == null || list.Count == 0)
                return -1;

            Random random = new();
            int randomIndex = random.Next(list.Count);
            return list[randomIndex];
        }

        /// <summary>
        /// 发起进攻
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        private bool Combat(int id)
        {
            int retrycount = 0;
        Re:
            if (id != -1)//点击进入战斗
            {
                CheckCombatPairs[id].Click();
                Sleep(1500);
                CombatPairs[id].Click_Double();
                Sleep(333);
                log.Info("突破战斗开始");
                Fast.Click(588, 5, 608, 11);
                Sleep(333);
                //回溯判断是不是在突破场景
                if (Scene.NowIsScene("突破"))
                {
                    if (retrycount >= 3)
                    {
                        log.Error("战斗失败，请检查您的队伍配置是否正常！");
                        return false;
                    }
                    retrycount++;
                    log.Info("当前还处于突破场景，重新执行进入战斗！" + retrycount);
                    goto Re;
                }
            }
            var pics = Mp.Filter("突破");
            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置
            while (!isbreak)
            {
                Sleep(500);
                if (Scene.NowIsScene("突破"))
                {
                    log.Info("当前处于突破场景，退出战斗！");
                    ret_bol = false;
                    isbreak = true;
                    break;
                }
                var p = pics.FindAllEa();
                if (p is null) continue;
                FindOkFun(p.Name, p);
                if (p.Name.Contains("胜利"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败") || p.Name.Contains("再次挑战"))
                {
                    // 第一次识别到失败，等待1秒后再次确认
                    Sleep(1000);
                    var p2 = pics.FindAllEa();
                    if (p2 != null && (p2.Name.Contains("失败") || p2.Name.Contains("再次挑战")))
                    {
                        ret_bol = false;
                        isbreak = true;
                    }
                }
                if (!DontSendLog.Any(p.Name.Contains))
                    log.Info($"执行点击：{p._Name}");
                p.Click();
            }
            if (ret_bol)
                Combat_End();//战斗收尾

            return ret_bol;
        }

        /// <summary>
        /// 突破胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("突破");
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(199);
                if (Scene.NowScene == "突破")
                {
                    isbreak = true;
                    continue;
                }
                var p = pics.FindAllEa();
                if (p is null) continue;
                if (!DontSendLog.Any(p.Name.Contains)) log.Info($"执行点击：{p._Name}");
                p.Click();
            }
        }

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private void FindOkFun(string name, MemPic? pic = null)
        {
            int countt = 0;
            var bj_pics = DynamicData.FilterPicsClass("Sub").Filter("绿标");
            bj_pics.SetAllXsoft(Dm);
            if (Biaoji && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.1秒，标记位置：" + BiaoJi_Str);
                Sleep(100, true);
            ReBiao:
                biaoJiPoss[BiaoJi_Str].Click();
                Sleep(200, true);
                if (!bj_pics.FindAll())
                {
                    //重新识别一次
                    Sleep(100, true);
                    if (!bj_pics.FindAll())
                    {
                        Sleep(150, true);
                        if (countt >= 1)
                        {
                            log.Warn("始终没有找到标记，本次标记失败...");
                            return;
                        }
                        countt++;
                        goto ReBiao;
                    }
                }
                else
                {
                    log.Info("标记完成！");
                }
            }
        }

        /// <summary>
        /// 返回可进攻ID列表
        /// </summary>
        /// <returns></returns>
        private List<int> GetCombatID()
        {
            List<int> list = [];
            foreach (var item in CheckCombatPairs)
            {
                if (item.Value.CmpColor("daccbd-101010", 0.9))
                    list.Add(item.Key);
            }
            log.Debug("突破识别可进攻位置ID:" + string.Join("|", list.ToArray()));
            return list;
        }

        /// <summary>
        /// 返回可进攻ID-随机
        /// </summary>
        /// <param name="count"></param>
        /// <returns></returns>
        private int GetCombatID_Random(out int count)
        {
            var list = GetCombatID();
            if (list == null)
            {
                count = 0;
                return -1;
            }
            count = list.Count;
            log.Info($"识别剩余可进攻个数：{count}");
            return GetRandomElement(list);
        }

        /// <summary>
        /// 卡等级
        /// </summary>
        /// <param name="id"></param>
        private bool Ka57(int id)
        {
            int tui_count = 0;
            log.Info("您开启了卡级功能，最后一个目标为您失败4次！");
            //点击进入战斗
            CheckCombatPairs[id].Click();
            Sleep(1000);
            CombatPairs[id].Click();
            Sleep(333);
            while (tui_count < 4)
            {
                log.Info($"卡等级，开始失败第{tui_count + 1}次");
                tui1ci(id);
                tui_count++;
            }
            //战斗
            log.Info($"正式进入战斗，等待战斗结果");
            Sleep(1500);
            return Combat(-1);
        }

        /// <summary>
        /// 突破结束时 开始执行寮突
        /// </summary>
        private void EndStartLiaoTu()
        {
            bool endStartLiaoTu = false;
            GetConfig.Others.TryGetValue("EndStartLtu", out string? s);
            try { endStartLiaoTu = s is null ? false : bool.Parse(s); } catch (Exception) { endStartLiaoTu = false; }
            if (endStartLiaoTu)
            {
                log.Info("突破结束，开始执行寮突...默认：6次");
                LiaoTu t = new();
                t.Init(Db, Dm, Ct, "");
                t.Start(new TaskConfigsModel.Configs()
                {
                    Count = 6,
                    Name = "寮突",
                    ShowName = "寮突 6次",
                    Others = new() { { "Biaoji", Biaoji.ToString() }, { "LtuCount", "6" } }
                });
            }
        }

        /// <summary>
        /// 是否执行卡57
        /// </summary>
        /// <returns></returns>
        private bool NowDoKa57()
        {
            List<Pixel> pixels = [
                new(307,159,"6c655d-101010",0.95),
                new(646,159,"6c655d-101010",0.95),
                new(986,159,"6c655d-101010",0.95),
                new(307,293,"6c655d-101010",0.95),
                new(646,293,"6c655d-101010",0.95),
                new(986,293,"6c655d-101010",0.95),
                new(307,430,"6c655d-101010",0.95),
                new(646,430,"6c655d-101010",0.95),
                new(986,430,"6c655d-101010",0.95),
                ];

            int count = 0;
            foreach (var pix in pixels)
            {
                if (pix.Find(Dm))
                    count++;
            }
            if (count != 8)
                log.Warn("不继续执行卡级操作，因为已攻破结界不足8个...");

            return count == 8;
        }

        /// <summary>
        /// 主流程
        /// </summary>
        private void main()
        {
            try
            {
                if (UserConfig_Preset != null)
                {
                    Sleep(1000);
                    //使用预设
                    List<string> preset = [.. UserConfig_Preset.Split('|')];
                    log.Info($"进入式神录，开始应用预设{UserConfig_Preset}");
                    Fast.Click("1213,620,1250,654");
                    Sleep(1500);
                    Tmp.Do_Preset(preset);
                }

            Re:
                tupo_Count = Waitmain();//等待场景
                if (tupo_Count <= 0)
                {
                    log.Info("突破卷数量不足以继续挑战，突破任务提前结束！");
                    Fast.Click(1181, 114, 1227, 153);
                    Sleep(1500);
                    EndStartLiaoTu();
                    return;
                }
                if (count >= Ncount)
                {
                    log.Info("次数已到设置次数，突破任务结束！");
                    Fast.Click(1181, 114, 1227, 153);
                    Sleep(1500);
                    EndStartLiaoTu();
                    return;
                }
                //防封等待
                Anti.RandomDelay();
                if (Db.PendingTimerTask) //执行定时任务
                {
                    Db.PendingTimerTask = false;
                    log.Info("暂停当前任务，执行定时任务，退出到探索..");
                    Fast.Click(1181, 114, 1227, 153);
                    Sleep(1500);
                    Db?.TimerTask?.DoAllTask();
                    Sleep(1000);
                    throw new Exception("定时任务执行结束，重新执行当前的主任务..");
                }

                int id = GetCombatID_Random(out int id_count);//获取目标
                bool Res = false;
                if (id_count == 0)
                {
                    log.Info("没有找到可以战斗的目标，开始执行刷新操作！");
                    Refresh_All();
                    goto Re;
                }
                else if (Tui4 && id_count == 1 && NowDoKa57())
                {
                    Res = Ka57(id);//卡级战斗
                    if (!Res)
                        goto Re;
                }
                else
                    Res = Combat(id);//普通战斗

                if (Res)//判断结果
                {
                    count++;
                    log.Info($"突破战斗胜利，战斗次数进度：{count}/{Ncount}");
                }
                else
                {
                    log.Warn($"突破战斗失败，请检查您的队伍配置是否正常！剩余战斗次数：{count}/{Ncount}");
                    Defeated();
                }
                Sleep(200);
                goto Re;
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("A task was canceled")) throw;
                log.Error($"突破任务执行出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 刷新操作
        /// </summary>
        private void Refresh_All()
        {
            if (User_Affirm)
            {
                log.Info("当前需要执行刷新的操作...等待用户确认后继续...");

                // 使用Windows API的MessageBox显示确认对话框，设置顶置和获取焦点
                int result = MessageBox(IntPtr.Zero,
                    $"【{log.LogClassName}】突破刷新确认\r\n您可以检查当前突破失败的对局，并手动战斗胜利后，是否需要脚本执行突破刷新操作？",
                    "突破刷新前用户确认",
                    MB_YESNO | MB_ICONQUESTION | MB_TOPMOST | MB_SETFOREGROUND);

                // 查找消息框并设置为顶置窗口（额外保证）
                IntPtr hwnd = FindWindow("#32770", "突破刷新前用户确认");
                if (hwnd != IntPtr.Zero)
                {
                    SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW);
                    SetForegroundWindow(hwnd);
                }

                if (result != IDYES)
                    return; // 用户没有确认，取消刷新操作
            }

            log.Info("等待刷新按钮亮起（若有）...");
            Fast.WaitColor(1092, 592, "f3b25e-101010");
            log.Info("点击刷新按钮..");
            Operational.Click(992, 580, 1109, 618);
            Sleep(1500);
            log.Info("点击确定..");
            Operational.Click(703, 416, 827, 451);
            Sleep(1500);
            log.Info("刷新操作完成..");
        }

        /// <summary>
        /// 退出一次
        /// </summary>
        /// <returns></returns>
        private void tui1ci(int id)
        {
            var pics = Mp.Filter("突破.ZD_胜利")
                .Add(Mp.Filter("突破.ZD_失败"))
                .Add(Mp.Filter("突破.标记"))
                .Add(Mp.Filter("突破.达摩"))
                .Add(Mp.Filter("突破.胜利"))
                .Add(Mp.Filter("突破.失败"))
                .Add(Mp.Filter("突破.准备"));

            var pics_再次挑战 = Mp.Filter("再次挑战");

            bool isbreak = false;
            int ccount = 0;
            while (!isbreak)
            {
                if (pics_再次挑战.FindAll())
                {
                    Fast.Click(861, 518);
                    Sleep(666);
                    if (Dm.CmpColor(799, 426, "f3b25e-101010", 0.95) == 0)
                    {
                        log.Info("点击：进入不再提示！");
                        Fast.Click(542, 348, 567, 370);
                        Sleep(300);
                        Fast.Click(705, 414, 812, 448);
                    }
                    return;
                }

                Sleep(100, true);
                if (pics.FindAllE(out var list))
                    foreach (var item in list)
                        if (item.Contains("标记") || item.Contains("准备"))
                        {
                            isbreak = true;
                            ccount = 0;
                            continue;
                        }
                Sleep(100, true);
                ccount++;
                if (ccount > 5)
                {
                    Operational.Click(1218, 181, 1263, 217);
                    Sleep(100);
                    // 检查当前场景
                    if (Scene.NowIsScene("突破"))
                    {
                        log.Warn("突破卡等级回到了突破界面，开始重新调整1...");
                        //点击进入战斗
                        CheckCombatPairs[id].Click();
                        Sleep(1000);
                        CombatPairs[id].Click();
                        Sleep(333);
                        ccount = 0;
                        continue;
                    }
                    ccount = 0;
                }
            }
            Sleep(100);
            //执行退出
            Fast.Click(22, 15, 46, 40);
            Sleep(888);
            Fast.Click(699, 402, 786, 445);
            Sleep(333);
            //等待再次挑战
            Mp.Filter("再次挑战").Wait();
            Sleep(333);
            //加场景判断
            if (!Scene.NowIsScene("突破"))
            {
                Fast.Click(861, 518);
                Sleep(666);
                if (Dm.CmpColor(799, 426, "f3b25e-101010", 0.95) == 0)
                {
                    log.Info("点击：进入不再提示！");
                    Fast.Click(542, 348, 567, 370);
                    Sleep(300);
                    Fast.Click(705, 414, 812, 448);
                }
            }
            else
            {
                log.Warn("突破卡等级回到了突破界面，开始重新调整2...");
                //点击进入战斗
                CheckCombatPairs[id].Click();
                Sleep(1000);
                CombatPairs[id].Click();
            }
            Sleep(244);
        }

        /// <summary>
        /// 等待主场景
        /// </summary>
        /// <returns>突破卷 数量</returns>
        private int Waitmain()
        {
            //等待场景
            while (!Scene.NowIsScene("突破"))
            {
                Sleep(3000);
                log.Info("等待突破任务主场景...");
                // 点击之前再判断一下场景
                if (!Scene.NowIsScene("突破"))
                    Operational.Click(245, 656, 465, 702);
            }
            tupo_Count = Fast.Scence.TuPo_GetTuPoCount();
            return tupo_Count;
        }
    }
}