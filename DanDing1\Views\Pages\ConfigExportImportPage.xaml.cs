using DanDing1.ViewModels.Pages;
using System;
using System.Collections.Generic;
using System.Windows.Controls;
using DanDing1.ViewModels.Windows;

namespace DanDing1.Views.Pages
{
    /// <summary>
    /// ConfigExportImportPage.xaml 的交互逻辑
    /// </summary>
    public partial class ConfigExportImportPage : Page
    {
        private readonly SchedulerWindowViewModel _schedulerViewModel;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ConfigExportImportPage(SchedulerWindowViewModel schedulerViewModel)
        {
            InitializeComponent();

            _schedulerViewModel = schedulerViewModel;

            // 创建并设置ViewModel
            var viewModel = new ConfigExportImportViewModel(
                new List<EmulatorItem>(_schedulerViewModel.Emulators),
                new List<ScheduledTask>(_schedulerViewModel.ScheduledTasks));

            // 订阅刷新事件
            viewModel.RefreshParentUI += ViewModel_RefreshParentUI;

            // 设置数据上下文
            DataContext = viewModel;
        }

        /// <summary>
        /// 处理刷新父级ViewModel的事件
        /// </summary>
        private void ViewModel_RefreshParentUI(object sender, EventArgs e)
        {
            // 使用公开的全面刷新方法更新所有数据
            _schedulerViewModel.RefreshAll();
        }
    }
}