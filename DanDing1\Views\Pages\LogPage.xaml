﻿<Page
    x:Class="DanDing1.Views.Pages.LogPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:DanDing1.Views.Pages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:DanDing1.Models"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="LogPage"
    d:DataContext="{d:DesignInstance local:LogPage,
    IsDesignTimeCreatable=False}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
    ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    ScrollViewer.CanContentScroll="False"
    mc:Ignorable="d">

    <Grid Margin="16,-16,16,16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 顶部工具栏 -->
        <Grid Grid.Row="0"
              Margin="0,0,0,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="1"
                        Orientation="Horizontal">
                <ui:TextBlock Text="筛选："
                              VerticalAlignment="Center"
                              FontSize="14"
                              FontWeight="Black" />
                <ComboBox x:Name="LogFiltter"
                          Width="87"
                          Height="35"
                          FontWeight="Black"
                          Margin="0,0,8,0"
                          SelectedValue="{Binding ViewModel.SelectShowLog}" />
                <ui:Button x:Name="ShowLogWindow"
                           Width="42"
                           Height="32"
                           Margin="0,0,8,0"
                           ToolTip="弹出或收回 独立日志窗口"
                           Click="Button_Click">
                    <ui:SymbolIcon x:Name="ShowLogWindow_Icon"
                                   Symbol="ArrowUpRight16" />
                </ui:Button>
                <ui:Button x:Name="OpenLogsFolderButton"
                           FontWeight="Black"
                           Height="32"
                           Content="打开日志文件夹"
                           Click="OpenLogsFolderButton_Click" />
                <ui:Button x:Name="SaveLogsButton"
                           Height="32"
                           FontWeight="Black"
                           Margin="5 0 0 0"
                           Content="打包并提交日志❗"
                           Click="SaveLogsButton_Click" />
            </StackPanel>
        </Grid>

        <!-- 日志显示区域 -->
        <Border Grid.Row="1"
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                CornerRadius="8"
                Margin="0,0,0,12">
            <ScrollViewer VerticalScrollBarVisibility="Auto"
                          Padding="12">
                <ItemsControl x:Name="itemsControl"
                              ItemsSource="{Binding ViewModel.Log}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Margin="0,4"
                                    Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                    CornerRadius="6"
                                    Padding="12,2">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0"
                                               Text="{Binding Tip}"
                                               FontSize="15"
                                               Margin="0,0,12,0"
                                               VerticalAlignment="Top"
                                               FontWeight="SemiBold"
                                               Foreground="{Binding Color}" />

                                    <TextBlock Grid.Column="1"
                                               Text="{Binding Text}"
                                               FontSize="15"
                                               FontWeight="Medium"
                                               Foreground="{Binding Color}"
                                               TextWrapping="Wrap" />
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Border>

        <!-- 底部命令输入区域 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0"
                       Text="命令："
                       FontSize="14"
                       FontWeight="SemiBold"
                       VerticalAlignment="Center"
                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                       Margin="0,0,12,0" />

            <TextBox x:Name="CommandBox"
                     Grid.Column="1"
                     Height="32"
                     VerticalContentAlignment="Center"
                     FontWeight="Medium"
                     Padding="8,0" />
        </Grid>
    </Grid>
</Page>