using ScriptEngine.Model;
using System.Collections.ObjectModel;
using System.Text.Json;
using XHelper;
using XHelper.Models;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// CloudConfigWindow.xaml 的交互逻辑
    /// </summary>
    public partial class CloudConfigWindow : Window
    {
        // 配置列表数据源
        private List<_BrowseConfigItem> _configItems = new();

        // 分页相关属性
        private int _currentPage = 1;

        private int _itemsPerPage = 10;

        // 任务更新回调
        private Action<ObservableCollection<TaskConfigsModel.Configs>> _onTaskChanged;

        // 搜索关键词
        private string _searchTerm = "";

        private int _totalPages = 1;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="onTaskChanged">任务导入后的回调函数</param>
        public CloudConfigWindow(Action<ObservableCollection<TaskConfigsModel.Configs>> onTaskChanged)
        {
            InitializeComponent();
            _onTaskChanged = onTaskChanged;

            // 初始化窗口
            Loaded += CloudConfigWindow_Loaded;
        }

        /// <summary>
        /// 窗口加载事件
        /// </summary>
        private async void CloudConfigWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // 检查网络服务是否初始化
            if (GlobalData.Instance.appConfig.dNet == null)
            {
                System.Windows.MessageBox.Show("网络服务未初始化，请先登录", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                Close();
                return;
            }

            // 加载第一页数据
            await LoadConfigsAsync(_currentPage);
        }

        /// <summary>
        /// 配置列表双击事件 - 导入选中的配置
        /// </summary>
        private async void ConfigsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            // 检查是否有选中的配置
            if (ConfigsDataGrid.SelectedItem is _BrowseConfigItem selectedConfig)
            {
                // 询问用户是否确认导入
                var result = MessageBox.Show(
                    $"确定要导入配置\"{selectedConfig.ConfigName}\"吗？\n\n" +
                    $"版本: {selectedConfig.Version}\n" +
                    $"分享者: {selectedConfig.SharedByUsername}",
                    "导入确认",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes)
                    return;

                try
                {
                    // 显示等待光标
                    var cursor = Mouse.OverrideCursor;
                    Mouse.OverrideCursor = Cursors.Wait;

                    // 获取配置详情
                    var configService = GlobalData.Instance.appConfig.dNet.Config;
                    var configResult = await configService.GetConfigAsync(selectedConfig.ShareCode);

                    // 恢复光标
                    Mouse.OverrideCursor = cursor;

                    // 检查结果
                    if (configResult == null || !configResult.IsSuccess || configResult.Data == null)
                    {
                        string errorMsg = configResult?.Message ?? "获取配置失败";
                        MessageBox.Show($"获取配置失败: {errorMsg}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // 尝试将配置 JSON 反序列化为任务列表
                    try
                    {
                        if (configResult.Data.ConfigJson == null)
                        {
                            MessageBox.Show("配置内容为空", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                            return;
                        }

                        string configJson = configResult.Data.ConfigJson.ToString();
                        var tasklist = JsonSerializer.Deserialize<ObservableCollection<TaskConfigsModel.Configs>>(configJson);

                        if (tasklist == null || tasklist.Count == 0)
                        {
                            MessageBox.Show("配置格式错误或任务列表为空", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                            return;
                        }

                        // 更新任务列表
                        _onTaskChanged.Invoke(tasklist);
                        string taskName = configResult.Data.ConfigName;

                        // 将网络配置同时保存到本地
                        string localConfigName = "";
                        try
                        {
                            // 生成配置名并保存
                            string baseConfigName = $"{taskName}_{configResult.Data.Version}";
                            localConfigName = SaveImportedConfig(tasklist, baseConfigName);

                            XLogger.Info($"成功将云端配置导入的任务保存到本地: {localConfigName}");
                        }
                        catch (Exception saveEx)
                        {
                            XLogger.Warn($"保存云端配置导入的任务到本地失败: {saveEx.Message}");
                        }

                        // 显示导入成功的消息，包含分享者信息
                        MessageBox.Show($"成功导入配置 \"{configResult.Data.ConfigName}\"\n" +
                            $"版本: {configResult.Data.Version}\n" +
                            $"分享者: {configResult.Data.SharedByUsername}\n" +
                            $"分享时间: {configResult.Data.SharedAt}\n" +
                            $"配置已同时保存到本地" + (!string.IsNullOrEmpty(localConfigName) ? $"，保存为：{localConfigName}" : "") +
                            "，可在下拉列表中选择使用",
                            "导入成功", MessageBoxButton.OK, MessageBoxImage.Information);

                        // 设置对话框结果为true，表示成功导入
                        DialogResult = true;
                        Close();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"解析配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导入过程中发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 生成合法的配置文件名（复用TaskConfigWindow中的方法逻辑）
        /// </summary>
        private string GenerateSafeConfigName(string baseName)
        {
            // 替换非法字符
            string safeName = baseName;
            foreach (char c in System.IO.Path.GetInvalidFileNameChars())
            {
                safeName = safeName.Replace(c, '_');
            }

            // 替换可能引起问题的其他字符
            safeName = safeName.Replace('|', '_')
                               .Replace(' ', '_')
                               .Replace('.', '_');

            // 限制文件名长度，避免过长
            if (safeName.Length > 50)
            {
                safeName = safeName.Substring(0, 50);
            }

            // 检查文件是否已存在，如果存在则添加数字后缀
            string baseFileName = safeName;
            int counter = 1;
            string filePath = System.IO.Path.Combine(".\\runtimes\\AppConfig\\UserTaskList\\", safeName);

            while (System.IO.File.Exists(filePath))
            {
                safeName = $"{baseFileName}_{counter}";
                filePath = System.IO.Path.Combine(".\\runtimes\\AppConfig\\UserTaskList\\", safeName);
                counter++;
            }

            return safeName;
        }

        /// <summary>
        /// 加载配置数据
        /// </summary>
        private async Task LoadConfigsAsync(int page, string searchTerm = null)
        {
            // 显示加载中状态
            LoadingGrid.Visibility = Visibility.Visible;
            ConfigsDataGrid.Visibility = Visibility.Collapsed;
            NoDataGrid.Visibility = Visibility.Collapsed;

            try
            {
                // 获取配置服务
                var configService = GlobalData.Instance.appConfig.dNet.Config;

                // 调用API获取配置列表
                var result = await configService.BrowseConfigsAsync(page, _itemsPerPage, searchTerm);

                // 处理返回结果
                if (result != null && result.IsSuccess && result.Data != null)
                {
                    // 更新页码信息
                    _currentPage = result.Data.Page;
                    _totalPages = result.Data.Pages;
                    _configItems = result.Data.Items;

                    // 更新UI
                    PageInfoText.Text = $"第{_currentPage}页/共{_totalPages}页";
                    PrevButton.IsEnabled = _currentPage > 1;
                    NextButton.IsEnabled = _currentPage < _totalPages;

                    // 更新数据源
                    ConfigsDataGrid.ItemsSource = _configItems;

                    // 显示数据网格或空数据提示
                    if (_configItems.Count > 0)
                    {
                        ConfigsDataGrid.Visibility = Visibility.Visible;
                        NoDataGrid.Visibility = Visibility.Collapsed;
                    }
                    else
                    {
                        ConfigsDataGrid.Visibility = Visibility.Collapsed;
                        NoDataGrid.Visibility = Visibility.Visible;
                    }
                }
                else
                {
                    // 处理API错误
                    string errorMsg = result?.Message ?? "获取配置列表失败";
                    XLogger.Warn($"浏览配置失败: {errorMsg}");

                    // 显示空数据提示
                    ConfigsDataGrid.Visibility = Visibility.Collapsed;
                    NoDataGrid.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                // 处理异常
                XLogger.Error($"加载配置列表异常: {ex.Message}");
                System.Windows.MessageBox.Show($"加载配置失败: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);

                // 显示空数据提示
                ConfigsDataGrid.Visibility = Visibility.Collapsed;
                NoDataGrid.Visibility = Visibility.Visible;
            }
            finally
            {
                // 隐藏加载中状态
                LoadingGrid.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 下一页按钮点击事件
        /// </summary>
        private async void NextButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage < _totalPages)
            {
                _currentPage++;
                await LoadConfigsAsync(_currentPage, _searchTerm);
            }
        }

        /// <summary>
        /// 上一页按钮点击事件
        /// </summary>
        private async void PrevButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                await LoadConfigsAsync(_currentPage, _searchTerm);
            }
        }

        /// <summary>
        /// 保存导入的配置到本地（复用TaskConfigWindow中的方法逻辑）
        /// </summary>
        private string SaveImportedConfig(ObservableCollection<TaskConfigsModel.Configs> taskList, string baseConfigName)
        {
            // 生成安全的配置名称
            string configName = GenerateSafeConfigName(baseConfigName);

            // 保存配置
            if (XConfig.SaveValueToFile("UserTaskList", configName, taskList))
            {
                return configName;
            }
            else
            {
                throw new Exception("保存配置文件失败");
            }
        }

        /// <summary>
        /// 搜索框回车事件
        /// </summary>
        private async void SearchBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                // 获取搜索关键词
                _searchTerm = SearchBox.Text?.Trim() ?? "";
                _currentPage = 1;

                // 重新加载配置列表
                await LoadConfigsAsync(_currentPage, _searchTerm);
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private async void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            // 获取搜索关键词
            _searchTerm = SearchBox.Text?.Trim() ?? "";
            _currentPage = 1;

            // 重新加载配置列表
            await LoadConfigsAsync(_currentPage, _searchTerm);
        }
    }
}