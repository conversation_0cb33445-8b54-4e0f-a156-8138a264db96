﻿using ScriptEngine;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace DanDing1.Commands
{
    internal class Test_InitPics
    {
        internal async void Start(string Base_Url)
        {
            DDBuilder dBuilder = new();
            dBuilder.AddData("Base_Url", Base_Url);
            await dBuilder.SetPicsVerAsync(XConfig.LoadValueFromFile<string>("PicServer") ?? "默认", GlobalData.Instance.PicServerVer);
        }
    }
}