using DanDing1.Models.Super;
using DanDing1.ViewModels.Pages;
using DanDing1.ViewModels.Windows;
using System;
using System.Collections.Generic;

namespace DanDing1.Models
{
    /// <summary>
    /// 系统配置导出导入模型
    /// </summary>
    public class ConfigExportModel
    {
        /// <summary>
        /// 导出版本
        /// </summary>
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// 导出时间
        /// </summary>
        public DateTime ExportTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 模拟器列表
        /// </summary>
        public List<EmulatorExportItem> Emulators { get; set; } = new List<EmulatorExportItem>();

        /// <summary>
        /// 定时任务列表
        /// </summary>
        public List<ScheduledTaskExport> ScheduledTasks { get; set; } = new List<ScheduledTaskExport>();

        /// <summary>
        /// 定时系统配置
        /// </summary>
        public SchedulerConfig SystemConfig { get; set; } = new SchedulerConfig();
    }

    /// <summary>
    /// 模拟器导出项
    /// </summary>
    public class EmulatorExportItem
    {
        /// <summary>
        /// 模拟器名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 模拟器索引
        /// </summary>
        public string SimulatorIndex { get; set; }

        /// <summary>
        /// 模拟器配置
        /// </summary>
        public SimulatorConfig SimulatorConfig { get; set; }

        /// <summary>
        /// 用户配置
        /// </summary>
        public SuperMultiGameConfigWindowViewModel Config { get; set; }
    }

    /// <summary>
    /// 定时任务导出项
    /// </summary>
    public class ScheduledTaskExport
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 任务描述 (用于兼容性)
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool Enabled { get; set; }

        /// <summary>
        /// 关联的模拟器名称
        /// </summary>
        public string EmulatorName { get; set; }

        /// <summary>
        /// Cron表达式 (用于兼容性)
        /// </summary>
        public string CronExpression { get; set; }

        /// <summary>
        /// 任务参数（JSON格式）
        /// </summary>
        public string TaskParameters { get; set; }

        // 为了兼容现有代码，保留这些属性，但不会实际使用
        /// <summary>
        /// 游戏任务列表 (用于兼容性)
        /// </summary>
        public object GameTaskLists { get; set; }

        /// <summary>
        /// 任务属性 (用于兼容性)
        /// </summary>
        public AddTaskPropertyViewModel TaskProperty { get; set; }
    }

    /// <summary>
    /// 定时系统配置
    /// </summary>
    public class SchedulerConfig
    {
        /// <summary>
        /// 最大并发模拟器数
        /// </summary>
        public int MaxConcurrentEmulators { get; set; } = 4;

        /// <summary>
        /// 默认任务超时时间(秒)
        /// </summary>
        public int DefaultTaskTimeout { get; set; } = 3600;

        /// <summary>
        /// 自动关机空闲时间(秒)
        /// </summary>
        public int AutoShutdownIdleTime { get; set; } = 300;

        /// <summary>
        /// 是否启用任务重试
        /// </summary>
        public bool EnableTaskRetry { get; set; } = true;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;
    }

    /// <summary>
    /// 导入报告
    /// </summary>
    public class ImportReport
    {
        /// <summary>
        /// 导入结果
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 总模拟器数量
        /// </summary>
        public int TotalEmulators { get; set; }

        /// <summary>
        /// 成功导入的模拟器数量
        /// </summary>
        public int ImportedEmulators { get; set; }

        /// <summary>
        /// 匹配失败的模拟器名称列表
        /// </summary>
        public List<string> FailedEmulatorNames { get; set; } = new List<string>();

        /// <summary>
        /// 导入的任务数量
        /// </summary>
        public int ImportedTasks { get; set; }

        /// <summary>
        /// 新增的任务数量
        /// </summary>
        public int NewTasks { get; set; }

        /// <summary>
        /// 更新的任务数量
        /// </summary>
        public int UpdatedTasks { get; set; }

        /// <summary>
        /// 由于模拟器不匹配而跳过的任务数量
        /// </summary>
        public int SkippedTasks { get; set; }

        /// <summary>
        /// 详细信息
        /// </summary>
        public string Details { get; set; }

        /// <summary>
        /// 是否需要刷新UI界面
        /// </summary>
        public bool RequiresUIRefresh { get; set; } = false;
    }
}