﻿using DamoControlKit.Model;
using DamoControlKit.runtimes;

namespace DamoControlKit.Control
{
    /// <summary>
    /// 操作类
    /// </summary>
    public class Operational(dmsoft x)
    {
        public dmsoft X { get; } = x;

        /// <summary>
        /// 点击精准坐标
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <exception cref="Exception"></exception>
        public void Click(Point point)
        {
            Click(point.X, point.Y);
        }

        /// <summary>
        /// 点击精准坐标
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <exception cref="Exception"></exception>
        public void Click(int x, int y)
        {
            Random r = new();
            if (X is null)
                throw new Exception($"坐标 {this.ToString}没有指定可用的插件对象，导致错误！");
            int x1 = x + r.Next(-2, 3);
            int y1 = y + r.Next(-2, 3);
            X.MoveTo(x1, y1);
            X.delay(10);
            Record.SaveClick(X.GetID(), x1, y1);
            X.LeftClick();
            X.Delays(10, 30);
        }

        /// <summary>
        /// 点击范围
        /// </summary>
        /// <param name="pos"></param>
        /// <exception cref="Exception"></exception>
        public Position Click(Position pos)
        {
            if (X is null)
                throw new Exception($"范围 {pos.ToString}没有指定可用的插件对象，导致错误！");
            string ret = X.MoveToEx(pos.X, pos.Y, pos.X1 - pos.X, pos.Y1 - pos.Y);
            Record.SaveClick(X.GetID(), ret);
            var vs = ret.Split(',');
            int x = int.Parse(vs[0]);
            int y = int.Parse(vs[1]);
            Position RetPos = new($"{x - 10},{y - 10},{x + 10},{y + 10}");
            X.LeftClick();
            X.Delays(10, 30);
            return RetPos;
        }

        /// <summary>
        /// 点击范围
        /// </summary>
        /// <param name="pos"></param>
        /// <exception cref="Exception"></exception>
        public Position Click(int xx, int yy, int x1, int y1)
        {
            Position pos = new($"{xx},{yy},{x1},{y1}");
            if (X is null)
                throw new Exception($"范围 {pos.ToString}没有指定可用的插件对象，导致错误！");
            string ret = X.MoveToEx(pos.X, pos.Y, pos.X1 - pos.X, pos.Y1 - pos.Y);
            Record.SaveClick(X.GetID(), ret);
            var vs = ret.Split(',');
            int x = int.Parse(vs[0]);
            int y = int.Parse(vs[1]);
            Position RetPos = new($"{x - 10},{y - 10},{x + 10},{y + 10}");
            X.LeftClick();
            X.Delays(10, 30);
            return RetPos;
        }

        /// <summary>
        /// 多次点击
        /// </summary>
        /// <param name="pos"></param>
        /// <param name="count"></param>
        /// <param name="delay"></param>
        /// <exception cref="Exception"></exception>
        public void Click(Position pos, int count, int delay = 25)
        {
            if (X is null)
                throw new Exception($"范围 {pos.ToString}没有指定可用的插件对象，导致错误！");
            Position? NextPos = null;
            for (int i = 0; i < count; i++)
            {
                NextPos = Click(NextPos ?? pos);
                X.delay(delay);
            }
        }

        /// <summary>
        /// 多次点击 概率减少点击次数 至少1次
        /// </summary>
        /// <param name="pos"></param>
        /// <param name="count"></param>
        /// <param name="delay"></param>
        /// <exception cref="Exception"></exception>
        public void ClickEx(Position pos, int count, int delay = 25)
        {
            if (X is null)
                throw new Exception($"范围 {pos.ToString}没有指定可用的插件对象，导致错误！");
            Position? NextPos = null;
            int ii = new Random().Next(0, 8);
            if (ii == 1) count--;
            if (count < 1) count = 1;
            for (int i = 0; i < count; i++)
            {
                NextPos = Click(NextPos ?? pos);
                X.delay(delay);
            }
        }

        /// <summary>
        /// 范围滑动屏幕
        /// </summary>
        /// <param name="pos1"></param>
        /// <param name="pos2"></param>
        public void Slide_Pos(Position pos1, Position pos2)
        {
            int x, y, x1, y1;
            x = pos1.X + (pos1.X1 - pos1.X) / 2;
            y = pos1.Y + (pos1.Y1 - pos1.Y) / 2;
            x1 = pos2.X + (pos2.X1 - pos2.X) / 2;
            y1 = pos2.Y + (pos2.Y1 - pos2.Y) / 2;

            X.MoveTo(x, y);
            X.delay(50);
            X.LeftDown();
            X.delay(100);
            X.MoveTo(x1, y1);
            X.delay(100);
            X.LeftUp();
            X.delay(50);
        }

        /// <summary>
        /// 范围滑动屏幕
        /// </summary>
        /// <param name="pos1"></param>
        /// <param name="pos2"></param>
        public void Slide_Pos(Point point1, Point point2)
        {
            X.MoveTo(point1.X, point1.Y);
            X.delay(50);
            X.LeftDown();
            X.delay(100);
            X.MoveTo(point2.X, point2.Y);
            X.delay(100);
            X.LeftUp();
            X.delay(50);
        }
    }
}