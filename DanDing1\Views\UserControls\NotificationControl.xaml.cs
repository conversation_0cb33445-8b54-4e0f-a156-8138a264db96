using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;

namespace DanDing1.Views.UserControls
{
    /// <summary>
    /// NotificationControl.xaml 的交互逻辑
    /// </summary>
    public partial class NotificationControl : UserControl
    {
        private Storyboard _fadeInStoryboard;
        private Storyboard _fadeOutStoryboard;
        private bool _isClosing = false;

        // 添加关闭完成事件
        public event EventHandler CloseCompleted;

        public NotificationControl()
        {
            InitializeComponent();

            // 获取动画
            _fadeInStoryboard = (Storyboard)FindResource("FadeInStoryboard");
            _fadeOutStoryboard = (Storyboard)FindResource("FadeOutStoryboard");

            // 注册动画完成事件
            _fadeOutStoryboard.Completed += FadeOutStoryboard_Completed;

            // 加载后播放淡入动画
            Loaded += (s, e) =>
            {
                System.Diagnostics.Debug.WriteLine("通知已加载，播放淡入动画");
                _fadeInStoryboard.Begin(this);
            };

            // 鼠标进入时暂停自动关闭
            MouseEnter += NotificationControl_MouseEnter;
            MouseLeave += NotificationControl_MouseLeave;
        }

        /// <summary>
        /// 开始关闭动画
        /// </summary>
        public void BeginClose()
        {
            try
            {
                if (!_isClosing)
                {
                    _isClosing = true;
                    System.Diagnostics.Debug.WriteLine("开始关闭通知：" + (DataContext as DanDing1.Models.NotificationModel)?.Title);
                    _fadeOutStoryboard.Begin(this);
                }
            }
            catch (Exception ex)
            {
                // 如果动画失败，直接移除通知
                System.Diagnostics.Debug.WriteLine($"通知关闭失败: {ex.Message}");
                RemoveFromParent();
            }
        }

        private void FadeOutStoryboard_Completed(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("淡出动画完成");
                // 触发关闭完成事件
                CloseCompleted?.Invoke(this, EventArgs.Empty);
                // 移除自身
                RemoveFromParent();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"移除通知元素失败: {ex.Message}");
            }
        }

        private void RemoveFromParent()
        {
            try
            {
                var parent = Parent as Panel;
                if (parent != null && parent.Children.Contains(this))
                {
                    parent.Children.Remove(this);
                    System.Diagnostics.Debug.WriteLine("通知已从面板移除");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"移除通知出错: {ex.Message}");
            }
        }

        private void NotificationControl_MouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
        {
            // 鼠标进入时，为了保证用户可以看清内容，可以考虑暂停自动关闭计时器
            // 这需要与NotificationService协调
        }

        private void NotificationControl_MouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
        {
            // 鼠标离开时，可以恢复自动关闭计时
        }
    }
}