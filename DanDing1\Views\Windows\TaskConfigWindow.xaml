﻿<Window x:Class="DanDing1.Views.Windows.TaskConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        Title="{Binding ApplicationTitle}"
        Height="700"
        Width="1100"
        d:DataContext="{d:DesignInstance local:TaskConfigWindow,
        IsDesignTimeCreatable=True}"
        ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
        ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        ScrollViewer.CanContentScroll="False"
        WindowStartupLocation="CenterScreen"
        mc:Ignorable="d">
    <Grid Margin="15">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="250"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧任务列表 -->
        <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                CornerRadius="8"
                Padding="10">
            <DockPanel>
                <TextBlock Text="当前任务列表"
                           FontSize="18"
                           FontWeight="Bold"
                           Margin="5,0,0,10"
                           DockPanel.Dock="Top"/>
                <ListBox x:Name="TaskListBox"
                         SelectionChanged="TaskListBox_SelectionChanged"
                         ItemsSource="{Binding Tasks}"
                         Background="Transparent"
                         BorderThickness="0">
                    <ListBox.Resources>
                        <Style TargetType="ListBoxItem">
                            <Setter Property="Margin"
                                    Value="0,2"/>
                            <Setter Property="Padding"
                                    Value="0"/>
                            <Setter Property="Background"
                                    Value="Transparent"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="ListBoxItem">
                                        <Border x:Name="Border"
                                                Background="{TemplateBinding Background}"
                                                CornerRadius="4"
                                                Margin="{TemplateBinding Margin}">
                                            <ContentPresenter/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <MultiTrigger>
                                                <MultiTrigger.Conditions>
                                                    <Condition Property="IsMouseOver"
                                                               Value="True"/>
                                                    <Condition Property="IsSelected"
                                                               Value="False"/>
                                                </MultiTrigger.Conditions>
                                                <Setter Property="Background"
                                                        Value="{DynamicResource ControlFillColorTertiaryBrush}"/>
                                            </MultiTrigger>
                                            <Trigger Property="IsSelected"
                                                     Value="True">
                                                <Setter Property="Background"
                                                        Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </ListBox.Resources>
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Border Padding="12,8"
                                    Background="Transparent">
                                <TextBlock Text="{Binding ShowName}"
                                           FontSize="14">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Foreground"
                                                    Value="{DynamicResource TextFillColorPrimaryBrush}"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=ListBoxItem}, Path=IsSelected}"
                                                             Value="True">
                                                    <Setter Property="Foreground"
                                                            Value="White"/>
                                                    <Setter Property="FontWeight"
                                                            Value="SemiBold"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </Border>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </DockPanel>
        </Border>

        <!-- 任务编辑区 -->
        <Border Grid.Column="1"
                Margin="15,0"
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                CornerRadius="8"
                Padding="20">
            <ScrollViewer>
                <StackPanel>
                    <TextBlock Text="任务设置"
                               FontWeight="Bold"
                               FontSize="20"
                               Margin="0,0,0,20"/>

                    <!-- 基本信息卡片 -->
                    <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                            CornerRadius="6"
                            Padding="15"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <Grid Margin="0,0,0,10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"
                                                      MinWidth="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="任务名："
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           Margin="0,0,10,0"/>
                                <TextBox Grid.Column="1"
                                         Text="{Binding SelectedTask.Name}"
                                         IsReadOnly="True"
                                         Background="Transparent"
                                         BorderThickness="0,0,0,1"/>
                            </Grid>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"
                                                      MinWidth="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="次数："
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           Margin="0,0,10,0"/>
                                <TextBox Grid.Column="1"
                                         Text="{Binding SelectedTask.Count}"
                                         IsReadOnly="True"
                                         Background="Transparent"
                                         BorderThickness="0,0,0,1"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 其它参数卡片 -->
                    <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                            CornerRadius="6"
                            Padding="15"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="其它参数"
                                       FontWeight="Bold"
                                       FontSize="16"
                                       Margin="0,0,0,10"/>
                            <ItemsControl ItemsSource="{Binding SelectedTaskOthers}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Margin="0,4"
                                                Padding="8"
                                                Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                CornerRadius="4">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"
                                                                      MinWidth="100"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Text="{Binding Key}"
                                                           FontWeight="SemiBold"
                                                           Margin="0,0,10,0"/>
                                                <TextBox Grid.Column="1"
                                                         Text="{Binding Value}"
                                                         IsReadOnly="True"
                                                         Background="Transparent"
                                                         BorderThickness="0"
                                                         TextWrapping="Wrap"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </Border>

                    <!-- 其它对象参数卡片 -->
                    <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                            CornerRadius="6"
                            Padding="15">
                        <StackPanel>
                            <TextBlock Text="其它对象参数"
                                       FontWeight="Bold"
                                       FontSize="16"
                                       Margin="0,0,0,10"/>
                            <ItemsControl ItemsSource="{Binding SelectedTaskOthers_Obj}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Margin="0,4"
                                                Padding="8"
                                                Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                CornerRadius="4">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"
                                                                      MinWidth="100"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Text="{Binding Key}"
                                                           FontWeight="SemiBold"
                                                           Margin="0,0,10,0"/>
                                                <TextBox Grid.Column="1"
                                                         Text="{Binding Value}"
                                                         IsReadOnly="True"
                                                         Background="Transparent"
                                                         BorderThickness="0"
                                                         TextWrapping="Wrap"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- 控制中心 -->
        <Border Grid.Column="2"
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                CornerRadius="8"
                Padding="15"
                VerticalAlignment="Top">
            <StackPanel>
                <TextBlock Text="方案管理"
                           FontWeight="Bold"
                           FontSize="18"
                           Margin="0,0,0,15"/>

                <ComboBox x:Name="PlanComboBox"
                          Margin="0,0,0,5"
                          Width="182"
                          Height="40"
                          SelectionChanged="PlanComboBox_SelectionChanged"/>

                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            Margin="0,0,0,5">
                    <Button Content="刷新"
                            Height="32"
                            Width="85"
                            Margin="0,0,10,0"
                            Click="ImportButton_Click"
                            ToolTip="刷新方案列表，选择新方案将自动导入"/>
                    <Button Content="保存"
                            Height="32"
                            Width="85"
                            Click="SaveButton_Click"/>
                </StackPanel>

                <ui:Button Content="删除选中方案"
                           Margin="19,0,0,5"
                           Background="#E53935"
                           Height="32"
                           FontWeight="Black"
                           Width="182"
                           Click="DelButton_Click"/>

                <Separator Margin="0,5,0,5"
                           Width="200"/>

                <Button Content="使用方案码导入"
                        Margin="19,0,0,5"
                        Height="32"
                        Width="182"
                        Click="ImportCodeButton_Click"/>
                <Button Content="从云端导入"
                        Margin="19,0,0,5"
                        Height="32"
                        Width="182"
                        Click="FromCloudButton_Click"/>
                <Button Content="共享当前配置"
                        Margin="19,0,0,5"
                        Height="32"
                        Width="182"
                        Click="ShareConfigButton_Click"/>
                <Button Content="我分享的配置"
                        Margin="19,0,0,5"
                        Height="32"
                        Width="182"
                        Click="MySharesButton_Click"/>

            </StackPanel>
        </Border>
    </Grid>
</Window>