﻿using DanDing1.Helpers;
using DanDing1.Models.Super;
using ScriptEngine.MuMu;
using System.Diagnostics;
using XHelper;

namespace DanDing1.ViewModels.Windows
{
    public partial class SuperMultiGamesWindowViewModel : ObservableObject
    {
        /// <summary>
        /// 是否正在执行自动刷新（用于控制日志输出）
        /// </summary>
        private bool IsAutoRefreshing { get; set; } = false;

        public IServiceProvider ServiceProvider { get; }

        public SuperMultiGamesWindowViewModel(IServiceProvider serviceProvider)
        {
            ApplicationTitle = "超级多开Beta";
            InitializeSuperMultiGame_DataModelCollection();
            GameLogs = [];
            IsWindowTopMost = false;
            SelectedLogTabIndex = 0; // 默认选中操作日志
            RunningTasksCount = 0; // 初始化正在运行的任务数量

            // 初始化日志更新委托，防止空引用异常
            OnMainLogUpdated = delegate { };
            OnGameLogUpdated = delegate { };

            // 初始化委托方法
            InitializeDelegates();

            // 加载保存的配置
            LoadSavedConfiguration();
            ServiceProvider = serviceProvider;

            // 初始化时更新正在运行的任务数量
            UpdateRunningTasksCount();
        }

        public void SetWindowReference(Window window)
        {
            _windowReference = new WeakReference<Window>(window);
        }

        /// <summary>
        /// 添加日志记录到指定游戏的日志
        /// </summary>
        /// <param name="gameId">游戏ID</param>
        /// <param name="message">日志消息</param>
        private void AddGameLog(int gameId, string message)
        {
            var gameLog = GameLogs.FirstOrDefault(g => g.GameId == gameId);
            if (gameLog != null)
            {
                gameLog.AddLog(message);

                // 更新游戏的最新日志信息
                var game = SuperMultiGame_DataModelCollection.FirstOrDefault(g => g.GameId == gameId);
                if (game != null)
                {
                    game.LastLogMessage = message;
                }

                // 通知UI滚动到底部
                OnGameLogUpdated?.Invoke();
            }
        }

        /// <summary>
        /// 添加日志记录到主日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void AddLog(string message)
        {
            LogOutput += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}\n";
            // 如果日志太长，则只保留最新的50条记录
            string[] logLines = LogOutput.Split('\n');
            if (logLines.Length > 50)
            {
                LogOutput = string.Join("\n", logLines.Skip(logLines.Length - 50));
            }

            // 使用Debug.WriteLine输出到控制台而非写入日志文件
            Debug.WriteLine($"[超级多开] {message}");

            // 通知UI滚动到底部
            OnMainLogUpdated?.Invoke();
        }

        /// <summary>
        /// 初始化游戏数据模型集合
        /// </summary>
        private void InitializeSuperMultiGame_DataModelCollection()
        {
            SuperMultiGame_DataModelCollection = new();
        }

        /// <summary>
        /// 刷新所有游戏的模拟器数据
        /// </summary>
        /// <param name="game"></param>
        /// <returns></returns>
        private async Task<bool> RefreshSelectedGameWithResult(SuperMultiGame_DataModel game)
        {
            if (game == null) return false;

            // 获取MuMu模拟器路径
            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (string.IsNullOrEmpty(path))
            {
                AddLog("MuMu模拟器路径未设置，无法刷新数据");
                return false;
            }

            // 初始化MuMu实例
            MuMu mumu = new();
            if (!mumu.Init(path))
            {
                AddLog("MuMu模拟器初始化失败，无法刷新数据");
                return false;
            }

            // 获取最新的模拟器实例信息
            var mumuInstances = await mumu._GetInstancesAsync();
            if (mumuInstances == null || mumuInstances.Instances.Count == 0)
            {
                AddLog("未找到任何MuMu模拟器实例，请确保模拟器已启动");
                return false;
            }

            // 使用MumuUtils查找匹配的模拟器实例
            var matchResult = MumuUtils.FindMatchingMumuInstance(
                mumuInstances.Instances,
                game,
                // 在自动刷新模式下不输出日志
                message => { if (!IsAutoRefreshing) AddLog(message); });

            if (matchResult.HasValue)
            {
                // 更新游戏模型的句柄信息
                MumuUtils.UpdateGameModelHandles(game, matchResult.Value.Value);
                return true;
            }

            return false;
        }

        /// <summary>
        /// 临时取消窗口置顶，并在指定操作完成后恢复
        /// </summary>
        /// <param name="action">需要执行的操作</param>
        private async Task TemporarilyUnsetTopMost(Func<Task> action)
        {
            bool wasTopMost = IsWindowTopMost;
            if (wasTopMost)
            {
                // 临时取消置顶
                IsWindowTopMost = false;
                if (_windowReference != null && _windowReference.TryGetTarget(out Window window))
                {
                    window.Topmost = false;
                }
            }

            try
            {
                // 执行操作
                await action();
            }
            finally
            {
                // 恢复原来的置顶状态
                if (wasTopMost)
                {
                    IsWindowTopMost = true;
                    if (_windowReference != null && _windowReference.TryGetTarget(out Window window))
                    {
                        window.Topmost = true;
                    }
                }
            }
        }
    }
}