using System;
using System.ComponentModel;

namespace DanDing1.Models
{
    /// <summary>
    /// 定时任务历史记录数据模型
    /// </summary>
    public class TaskHistoryRecord : INotifyPropertyChanged
    {
        private string _id = Guid.NewGuid().ToString();
        private string _taskId;
        private string _taskName;
        private string _emulatorName;
        private DateTime _startTime;
        private DateTime _endTime;
        private string _status;
        private string _errorMessage;
        private string _taskType;
        private string _executionDetails;
        private int _retryCount;
        private string _scheduleType;

        /// <summary>
        /// 记录ID
        /// </summary>
        public string Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged(nameof(Id));
            }
        }

        /// <summary>
        /// 关联的任务ID
        /// </summary>
        public string TaskId
        {
            get => _taskId;
            set
            {
                _taskId = value;
                OnPropertyChanged(nameof(TaskId));
            }
        }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName
        {
            get => _taskName;
            set
            {
                _taskName = value;
                OnPropertyChanged(nameof(TaskName));
            }
        }

        /// <summary>
        /// 模拟器名称
        /// </summary>
        public string EmulatorName
        {
            get => _emulatorName;
            set
            {
                _emulatorName = value;
                OnPropertyChanged(nameof(EmulatorName));
            }
        }

        /// <summary>
        /// 开始执行时间
        /// </summary>
        public DateTime StartTime
        {
            get => _startTime;
            set
            {
                _startTime = value;
                OnPropertyChanged(nameof(StartTime));
                OnPropertyChanged(nameof(Duration));
            }
        }

        /// <summary>
        /// 结束执行时间
        /// </summary>
        public DateTime EndTime
        {
            get => _endTime;
            set
            {
                _endTime = value;
                OnPropertyChanged(nameof(EndTime));
                OnPropertyChanged(nameof(Duration));
            }
        }

        /// <summary>
        /// 执行时长
        /// </summary>
        public TimeSpan Duration => EndTime > StartTime ? EndTime - StartTime : TimeSpan.Zero;

        /// <summary>
        /// 执行状态(成功/失败/中断/执行中)
        /// </summary>
        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        /// <summary>
        /// 错误信息(如果有)
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                _errorMessage = value;
                OnPropertyChanged(nameof(ErrorMessage));
            }
        }

        /// <summary>
        /// 任务类型
        /// </summary>
        public string TaskType
        {
            get => _taskType;
            set
            {
                _taskType = value;
                OnPropertyChanged(nameof(TaskType));
            }
        }

        /// <summary>
        /// 执行详情(JSON格式)
        /// </summary>
        public string ExecutionDetails
        {
            get => _executionDetails;
            set
            {
                _executionDetails = value;
                OnPropertyChanged(nameof(ExecutionDetails));
            }
        }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount
        {
            get => _retryCount;
            set
            {
                _retryCount = value;
                OnPropertyChanged(nameof(RetryCount));
            }
        }

        /// <summary>
        /// 调度类型(一次性/循环/间隔)
        /// </summary>
        public string ScheduleType
        {
            get => _scheduleType;
            set
            {
                _scheduleType = value;
                OnPropertyChanged(nameof(ScheduleType));
            }
        }

        /// <summary>
        /// 格式化的执行时长
        /// </summary>
        public string FormattedDuration => FormatTimeSpan(Duration);

        /// <summary>
        /// 格式化时间间隔
        /// </summary>
        private string FormatTimeSpan(TimeSpan timeSpan)
        {
            if (timeSpan.TotalDays >= 1)
            {
                return $"{(int)timeSpan.TotalDays}天 {timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
            }
            else
            {
                return $"{timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}