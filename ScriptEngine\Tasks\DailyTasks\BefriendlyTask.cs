﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Tasks.DailyTasks
{
    internal class BefriendlyTask : BaseTask
    {
        private Dictionary<string, Position> ClickPos_Pairs = new Dictionary<string, Position>()
        {
            {"打开好友",new(888,626,930,669) },
            {"切换友情点",new(108,651,163,685) },
            {"领取友情点",new(82,551,181,580) },
            {"关闭好友",new(1160,100,1197,135) },
            {"随机范围",new(440,9,585,50) },
        };

        private Dictionary<string, Position> Find_Pairs = new()
        {
            {"是否可以领取",new(55,545,187,584) }, // OCR会返回一键收取时可以领取
            {"是否在好友界面",new(536,47,697,88) }, // OCR会返回好友时代表在好友界面
        };

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, className);
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            log.Info("开始执行任务：" + configs.Name);
        Retry:
            // 判断庭院位置
            if (Scene.NowScene != "庭院")
            {
                if (!Scene.TO.TingYuan())
                {
                    if (!Scene.TO.ResetScene(out var s))
                    {
                        log.Error("无法进入庭院，结束任务");
                        return;
                    }
                    else
                        goto Retry;
                }
            }
            Sleep(1000); // 等待1秒
            CollectFriendshipPoints();
            Sleep(1000);
            CloseFriendInterface();
        }

        /// <summary>
        /// 检查是否在好友界面
        /// </summary>
        /// <returns>如果在好友界面返回 true，否则返回 false</returns>
        private bool IsInFriendInterface()
        {
            Sleep(500);
            string findStr = Fast.Ocr_String(Find_Pairs["是否在好友界面"]);
            return findStr.Contains("好友");
        }

        /// <summary>
        /// 打开好友界面
        /// </summary>
        private bool OpenFriendInterface()
        {
            Fast.Click(ClickPos_Pairs["打开好友"]);
            // 可以添加延迟或检查确保界面已打开
            Sleep(1500); // 等待1.5秒
            if (!IsInFriendInterface())
            {
                log.Error("打开好友界面失败，结束任务");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 关闭好友界面
        /// </summary>
        private void CloseFriendInterface()
        {
            // 判断是否在好友界面
            if (!IsInFriendInterface())
            {
                // 如果不在好友界面，每隔3秒循环点击随机范围，直到回到好友界面
                log.Info("未检测到好友界面，尝试点击随机范围返回");
                while (!IsInFriendInterface())
                {
                    Fast.Click(ClickPos_Pairs["随机范围"]);
                    Sleep(3000); // 等待3秒
                }
            }

            // 确认回到好友界面后再关闭
            Fast.Click(ClickPos_Pairs["关闭好友"]);
            log.Info("已关闭好友界面，结束任务..");
        }

        /// <summary>
        /// 领取友情点
        /// </summary>
        /// <returns>是否成功领取</returns>
        private bool CollectFriendshipPoints()
        {
            if (!IsInFriendInterface())
            {
                log.Info("未在好友界面，尝试打开好友界面");
                if (!OpenFriendInterface())
                {
                    return false;
                }
            }

            // 切换到友情点标签
            log.Info("切换到友情点标签");
            Fast.Click(ClickPos_Pairs["切换友情点"]);
            Sleep(1000);

            // 检查是否可以领取
            string result = Fast.Ocr_String(Find_Pairs["是否可以领取"]);
            if (result.Contains("收取"))
            {
                log.Info("检测到可领取的友情点，开始领取");
                Fast.Click(ClickPos_Pairs["领取友情点"]);
                Sleep(1500);
                log.Info("友情点领取完成");
                Fast.Click(ClickPos_Pairs["随机范围"]);
                Fast.Click(ClickPos_Pairs["随机范围"]);
                return true;
            }
            else
            {
                log.Info("暂无可领取的友情点");
                return false;
            }
        }
    }
}