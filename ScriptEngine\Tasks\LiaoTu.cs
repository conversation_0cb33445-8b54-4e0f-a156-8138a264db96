﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Tasks
{
    internal class LiaoTu : BaseTask
    {
        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "寮突");
            foreach (var item in biaoJiPoss)
                item.Value.SetXsoft(Dm);
        }

        /// <summary>
        /// 剩余战斗次数
        /// </summary>
        private int Last_DZ_Count = 0;

        private Dictionary<string, Position> biaoJiPoss = new()
        {
            {"位置1",new Position("239,515,268,544") },
            {"位置2",new Position("439,461,465,490") },
            {"位置3",new Position("617,410,639,438") },
            {"位置4",new Position("793,461,825,500") },
            {"位置5",new Position("1001,498,1033,549") },
        };

        public string BiaoJi_Str { get; private set; }

        public bool BiaoJi_Status { get; private set; }
        public bool Biaoji { get; private set; }
        public int Ncount { get; private set; }
        public int ZDCount { get; private set; } = 0;

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Ncount = GetConfig.Count;
            GetConfig.Others.TryGetValue("Biaoji", out var s);
            if (!new List<string>() { "位置1", "位置2", "位置3", "位置4", "位置5" }.Contains(s ?? ""))
            {
                BiaoJi_Str = "不标记";
                Biaoji = false;
            }
            else
            {
                BiaoJi_Str = s ?? "位置5";
                Biaoji = true;
            }

            string nows = Scene.NowScene;
            if (nows == "探索" || nows == "庭院")
                Scene.TO.LiaoTu();
            WaitMain();//刷新剩余次数
            if (EndFlag)
                return;

            //判断剩余次数
            if (Last_DZ_Count <= 0)
            {
                log.Warn("当前寮突剩余次数为：" + Last_DZ_Count);
                log.Warn("不可以继续执行当前任务，结束任务！");
                Fast.Click(1180, 109, 1223, 147);
                Sleep(1000);
                return;
            }
            mian();
            UserNotificationMessage = $"共战斗{ZDCount}/{Ncount}次.";
        }

        /// <summary>
        /// 主线任务执行
        /// </summary>
        private void mian()
        {
            if (UserConfig_Preset != null)
            {
                Sleep(1000);
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入式神录，开始应用预设{UserConfig_Preset}");
                Fast.Click(1218, 618, 1245, 647);
                Sleep(1500);
                Tmp.Do_Preset(preset);
            }

            int QueNumber = 0;
        ReStart:
            int jg_x, jg_y, ReFindCount = 3;
            int FindCount = 0;

            while (true)
            {
                //防封等待
                Anti.RandomDelay();
                if (Db.PendingTimerTask) //执行定时任务
                {
                    Db.PendingTimerTask = false;
                    log.Info("暂停当前任务，执行定时任务，退出到探索..");
                    Fast.Click(1180, 109, 1223, 147);
                    Sleep(1000);
                    Db?.TimerTask?.DoAllTask();
                    Sleep(1000);
                    throw new Exception("定时任务执行结束，重新执行当前的主任务..");
                }

                var mp = Mp.Filter("可战斗").FindAllEa();
                if (mp is not null)
                {
                    mp.Click();
                    Sleep(2000);
                    if (Mp.Filter("进攻").FindAll(out jg_x, out jg_y))
                        break;
                }
                Sleep(600);
                Operational.Click(245, 656, 465, 702);
                if (FindCount >= ReFindCount)
                {
                    if (QueNumber >= 5)
                    {
                        log.Warn("滑动了5次，没有发现可以战斗的目标，结束寮突任务！");
                        Fast.Click(1180, 109, 1223, 147);
                        Sleep(1000);
                        return;
                    }
                    log.Warn("滑动一次！当前已滑动：" + QueNumber + "次..");
                    //滑动
                    Dm.MoveTo(1000, 395);
                    Dm.LeftDown();
                    Sleep(1000);
                    Dm.MoveTo(1000, 261);
                    Sleep(700);
                    Dm.LeftUp();
                    FindCount = 0;
                    QueNumber++;
                }
                FindCount++;
            }

            //点击进攻
            Fast.Click(jg_x, jg_y);
            while (Scene.NowIsScene("寮突")) Sleep(500);
            //进入战斗
            if (Combat())
            {
                ZDCount++;
                log.Info_Green("当前寮突已经战斗次数：" + ZDCount);
            }
            WaitMain();//刷新剩余次数
            if (EndFlag)
                return;

            //判断设置次数
            if (ZDCount >= Ncount)
            {
                log.Info_Green("当前寮突战斗次数达到设置次数！结束任务!");
                Fast.Click(1180, 109, 1223, 147);
                Sleep(1000);
                return;
            }

            //判断剩余次数
            if (Last_DZ_Count <= 0)
            {
                log.Warn("当前寮突剩余次数为：" + Last_DZ_Count);
                log.Warn("不可以继续执行当前任务，结束任务！");
                Fast.Click(1180, 109, 1223, 147);
                Sleep(1000);
                return;
            }
            goto ReStart;
        }

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            int countt = 0;
            var bj_pics = DynamicData.FilterPicsClass("Sub").Filter("绿标");
            bj_pics.SetAllXsoft(Dm);
            if (Biaoji && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.1秒，标记位置：" + BiaoJi_Str);
                Sleep(100, true);
            ReBiao:
                biaoJiPoss[BiaoJi_Str].Click();
                Sleep(200, true);
                if (!bj_pics.FindAll())
                {
                    //重新识别一次
                    Sleep(100, true);
                    if (!bj_pics.FindAll())
                    {
                        Sleep(150, true);
                        if (countt >= 1)
                        {
                            log.Warn("始终没有找到标记，本次标记失败...");
                            return false;
                        }
                        countt++;
                        goto ReBiao;
                    }
                }
                else
                {
                    log.Info("标记完成！");
                }
            }
            return true;
        }

        private List<string> DontSendLog = ["标记"];

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            log.Info("开始寮突战斗.");
            var pics = Mp.Filter("寮突.ZD");
            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                FindOkFun(p.Name, p);
                if (!DontSendLog.Any(p.Name.Contains)) log.Info($"执行点击：{p._Rename}");
                if (p.Name.Contains("胜利") || p.Name.Contains("达摩"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败"))
                {
                    // 识别到失败时等待1秒，再识别一次
                    Sleep(1000);
                    var confirmFailure = pics.FindAllEa();
                    if (confirmFailure != null && confirmFailure.Name.Contains("失败"))
                    {
                        ret_bol = false;
                        isbreak = true;
                    }
                }
                p.Click();
                Sleep(500);
            }
            if (ret_bol)
                Combat_End();//等待Yuhun界面

            return ret_bol;
        }

        /// <summary>
        /// 胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("寮突.ZD");
            bool isbreak = false;
            while (!isbreak)
            {
                if (Scene.NowIsScene("寮突"))
                {
                    isbreak = true;
                    continue;
                }
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                log.Info($"执行点击：{p._Rename}");
                p.Click();
            }
        }

        private bool EndFlag = false;

        /// <summary>
        /// 等待主任务场景
        /// </summary>
        private void WaitMain()
        {
            //等待场景
            while (!Scene.NowIsScene("寮突"))
            {
                Sleep(3000);
                log.Info("等待寮突任务主场景...");
                if (!Scene.NowIsScene("寮突")) Operational.Click(245, 656, 465, 702);
            }
            Sleep(1000);
            if (isGongPo())
            {
                log.Warn("寮突任务已被攻破，结束任务！");
                Fast.Click(1180, 109, 1223, 147);
                Sleep(1000);
                EndFlag = true;
                return;
            }
            GetLastDZCount();
        }

        /// <summary>
        /// 寮突是否已被攻破
        /// </summary>
        /// <returns></returns>
        private bool isGongPo()
        {
            return Mp.Filter("已攻破").FindAll();
        }

        private void GetLastDZCount()
        {
            string count_str = Fast.Ocr_Local(264, 562, 329, 587);
            if (count_str != "")
            {
                int.TryParse(count_str.Split('/')[0], out Last_DZ_Count);
                log.Info("剩余寮突战斗次数：" + Last_DZ_Count);
            }
            else
            {
                count_str = Fast.Ocr_String(264, 562, 329, 587);
                if (count_str != "")
                {
                    int.TryParse(count_str.Substring(0, 1), out Last_DZ_Count);
                    log.Info("剩余寮突战斗次数：" + Last_DZ_Count);
                }
            }
        }
    }
}