using DanDing1.ViewModels.Windows;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using XHelper;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// SchedulerWindow.xaml 的交互逻辑
    /// </summary>
    public partial class SchedulerWindow : Window
    {
        private readonly SchedulerWindowViewModel viewModel;
        private bool _isTaskContextMenuOpen = false;
        private bool _isEmulatorContextMenuOpen = false;

        /// <summary>
        /// 默认构造函数，使用单例ViewModel
        /// </summary>
        public SchedulerWindow()
        {
            InitializeComponent();

            // 使用单例ViewModel
            this.viewModel = SchedulerWindowViewModel.Instance;
            DataContext = viewModel;

            // 设置窗口引用
            viewModel.SetWindowReference(this);

            // 订阅日志更新事件，实现自动滚动
            viewModel.OnMainLogUpdated += () => ScrollLogToBottom();
        }

        /// <summary>
        /// 构造函数，接受ViewModel参数（用于向后兼容）
        /// </summary>
        /// <param name="viewModelParam">视图模型实例</param>
        public SchedulerWindow(SchedulerWindowViewModel viewModelParam)
        {
            InitializeComponent();

            // 始终使用单例实例，忽略传入的参数
            // 这样即使代码中使用了这个构造函数，也不会创建多个实例
            this.viewModel = SchedulerWindowViewModel.Instance;
            DataContext = this.viewModel;

            // 设置窗口引用
            this.viewModel.SetWindowReference(this);

            // 订阅日志更新事件，实现自动滚动
            this.viewModel.OnMainLogUpdated += () => ScrollLogToBottom();
        }

        public SchedulerWindowViewModel ViewModel => viewModel;

        /// <summary>
        /// 查找指定类型的父元素
        /// </summary>
        private static T FindParent<T>(DependencyObject child) where T : DependencyObject
        {
            if (child == null) return null;

            var parent = VisualTreeHelper.GetParent(child);

            if (parent == null) return null;

            if (parent is T) return (T)parent;

            return FindParent<T>(parent);
        }

        /// <summary>
        /// 处理任务DataGrid的右键菜单事件，阻止系统默认右键菜单
        /// </summary>
        private void TasksDataGrid_PreviewMouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 如果菜单已经打开，完全阻止右键事件
            if (_isTaskContextMenuOpen)
            {
                e.Handled = true;
                return;
            }

            // 标记事件为已处理，防止系统自带菜单显示
            e.Handled = true;

            // 获取DataGrid和上下文菜单
            var dataGrid = (Wpf.Ui.Controls.DataGrid)sender;
            ContextMenu menu = dataGrid.ContextMenu;

            // 获取点击位置下的行
            var hitTestResult = VisualTreeHelper.HitTest(dataGrid, e.GetPosition(dataGrid));

            if (hitTestResult != null)
            {
                // 查找点击位置所在的DataGridRow
                var row = FindParent<DataGridRow>(hitTestResult.VisualHit);

                if (row != null)
                {
                    // 设置选中行
                    dataGrid.SelectedItem = row.DataContext;

                    // 在点击位置显示上下文菜单
                    if (menu != null)
                    {
                        menu.PlacementTarget = dataGrid;
                        menu.IsOpen = true;
                    }
                }
            }
        }

        /// <summary>
        /// 处理任务选中变化事件
        /// </summary>
        private void TasksDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var dataGrid = (Wpf.Ui.Controls.DataGrid)sender;
            if (dataGrid.SelectedItem != null)
            {
                viewModel.SelectedTask = dataGrid.SelectedItem;
            }
        }

        /// <summary>
        /// 处理模拟器DataGrid的选中变化事件
        /// </summary>
        private void EmulatorsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var dataGrid = (Wpf.Ui.Controls.DataGrid)sender;
            if (dataGrid.SelectedItem != null)
            {
                viewModel.SelectedEmulator = dataGrid.SelectedItem;
            }
        }

        /// <summary>
        /// 将日志滚动到底部
        /// </summary>
        private void ScrollLogToBottom()
        {
            // 确保UI线程访问
            Dispatcher.BeginInvoke(new Action(() =>
            {
                if (MainLogTextBox != null)
                {
                    MainLogTextBox.CaretIndex = MainLogTextBox.Text.Length;
                    MainLogTextBox.ScrollToEnd();
                }
            }), DispatcherPriority.Loaded);
        }

        /// <summary>
        /// 处理窗口关闭事件
        /// </summary>
        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // 这里不需要额外处理，ViewModel中已经处理了窗口关闭事件
        }

        /// <summary>
        /// 窗口加载完成后注册ContextMenu事件
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // 异步初始化视图模型
            _ = viewModel.InitializeAsync();

            // 订阅DataGrid的ContextMenu事件
            if (TasksDataGrid != null && TasksDataGrid.ContextMenu != null)
            {
                TasksDataGrid.ContextMenu.Opened += (sender, args) => _isTaskContextMenuOpen = true;
                TasksDataGrid.ContextMenu.Closed += (sender, args) => _isTaskContextMenuOpen = false;
            }

            if (EmulatorsDataGrid != null && EmulatorsDataGrid.ContextMenu != null)
            {
                EmulatorsDataGrid.ContextMenu.Opened += (sender, args) => _isEmulatorContextMenuOpen = true;
                EmulatorsDataGrid.ContextMenu.Closed += (sender, args) => _isEmulatorContextMenuOpen = false;
            }
        }
    }
}