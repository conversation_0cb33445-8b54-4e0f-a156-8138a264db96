﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using DanDing1.ViewModels.Pages;
using DanDing1.ViewModels.Windows;
using ScriptEngine.MuMu;

namespace DanDing1.Models.Super
{
    /// <summary>
    /// 用于SuperMultiGamesWindow中的DataGrid显示的数据模型
    /// </summary>
    public partial class SuperMultiGame_DataModel : ObservableObject
    {
        [ObservableProperty]
        private int _gameId;

        [ObservableProperty]
        private string _simulatorType = "MuMu";

        [ObservableProperty]
        private string _gameName = string.Empty;

        [ObservableProperty]
        private int _mumuHandle;

        [ObservableProperty]
        private int _gameHandle;

        [ObservableProperty]
        private Team_Identity_Unit _identity;

        [ObservableProperty]
        private string _currentTask = string.Empty;

        [ObservableProperty]
        private TimeSpan _runningDuration = TimeSpan.Zero;

        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(CurrentTask))]
        private string _runningStatus = "未运行";

        [ObservableProperty]
        private bool _isButtonEnabled = true;

        /// <summary>
        /// 游戏最新的一条日志信息
        /// </summary>
        [ObservableProperty]
        private string _lastLogMessage = string.Empty;

        /// <summary>
        /// 添加部分属性更改通知
        /// </summary>
        /// <param name="value"></param>
        partial void OnRunningStatusChanged(string value)
        {
            // 当状态变为"未运行"时，自动设置当前任务为"未运行"
            if (value == "未运行")
            {
                CurrentTask = "未运行";
                IsButtonEnabled = true;
            }
            else if (value == "启动中")
            {
                IsButtonEnabled = false;
            }
            else if (value == "运行中")
            {
                IsButtonEnabled = true;
            }
        }

        /// <summary>
        /// MuMu模拟器实例信息
        /// </summary>
        [ObservableProperty]
        private MuMuInstance _mumuInstance;

        /// <summary>
        /// MuMu模拟器的真实名称，用于在模拟器重启后通过名称匹配
        /// </summary>
        [ObservableProperty]
        private string _mumuRealName = string.Empty;

        /// <summary>
        /// 关联的任务配置
        /// </summary>
        private AddTaskPropertyViewModel? _taskConfiguration;

        /// <summary>
        /// 配置业务模型
        /// </summary>
        private SuperMultiGameConfigWindowViewModel? _config;

        /// <summary>
        /// 手动实现TaskConfiguration属性，使其可访问性为internal
        /// </summary>
        internal AddTaskPropertyViewModel? TaskConfiguration
        {
            get => _taskConfiguration;
            set
            {
                if (_taskConfiguration != value)
                {
                    _taskConfiguration = value;
                    OnPropertyChanged(nameof(TaskConfiguration));
                }
            }
        }

        /// <summary>
        /// 手动实现Config属性，使其可访问性为internal
        /// </summary>
        internal SuperMultiGameConfigWindowViewModel? Config
        {
            get => _config;
            set
            {
                if (_config != value)
                {
                    _config = value;
                    OnPropertyChanged(nameof(Config));
                }
            }
        }


        /// <summary>
        /// 游戏启动时临时数据
        /// </summary>
        internal Dictionary<string, object> GameData = new Dictionary<string, object>();
    }
}