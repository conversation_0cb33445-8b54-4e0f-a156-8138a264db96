﻿using Newtonsoft.Json;

namespace XHelper.Models
{
    internal class PaddleOcr_Model
    {
        public PaddleOcr_Model(string jsonString)
        {
            if (string.IsNullOrEmpty(jsonString))
            {
                Results = new List<OcrResult>();
                return;
            }

            try
            {
                Results = JsonConvert.DeserializeObject<List<OcrResult>>(jsonString);
            }
            catch (Exception ex)
            {
                Results = new List<OcrResult>();
                throw new ArgumentException($"JSON反序列化失败: {ex.Message}", nameof(jsonString));
            }
        }

        public List<OcrResult> Results { get; set; }

        // 一些辅助方法
        public string GetAllText()
        {
            return string.Join(" ", Results?.Select(r => r.Text) ?? Enumerable.Empty<string>());
        }

        public IEnumerable<OcrResult> GetResultsByPosition(int x, int y, int tolerance = 5)
        {
            return Results?.Where(r =>
                Math.Abs(r.X - x) <= tolerance &&
                Math.Abs(r.Y - y) <= tolerance) ??
                Enumerable.Empty<OcrResult>();
        }

        public class OcrResult
        {
            public int Height => Points != null && Points.Count >= 2 ?
                Math.Abs(Points[2][1] - Points[0][1]) : 0;

            [JsonProperty("points")]
            public List<List<int>> Points { get; set; }

            [JsonProperty("text")]
            public string Text { get; set; }

            public int Width => Points != null && Points.Count >= 2 ?
                            Math.Abs(Points[2][0] - Points[0][0]) : 0;

            // 为了方便使用，添加一些计算属性
            public int X => Points?.FirstOrDefault()?.FirstOrDefault() ?? 0;

            public int Y => Points?.FirstOrDefault()?.LastOrDefault() ?? 0;
        }
    }
}