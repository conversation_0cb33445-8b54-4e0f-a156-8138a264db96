﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DanDing1.Models
{
    public class AppInfo
    {
        /// <summary>
        /// 本地版本号
        /// </summary>
        public string Now_Ver { get; set; } = GlobalData.Ver;

        /// <summary>
        /// 是否需要提示更新
        /// </summary>
        public bool IsNeedUpData => Now_Ver != Ver;

        /// <summary>
        /// 云端初始化版本号
        /// </summary>
        public string Ver { get; set; }

        /// <summary>
        /// 公告
        /// </summary>
        public string Notice { get; set; } = "暂无版本公告信息";

        /// <summary>
        /// 更新日志
        /// </summary>
        public string UpdataLog { get; set; } = "暂无更新日志";

        /// <summary>
        /// 最新版本下载地址
        /// </summary>
        public string NewVerUrl { get; set; } = "";
    }
}