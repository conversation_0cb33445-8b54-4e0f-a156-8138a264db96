using DanDing1.ViewModels.Pages;
using DanDing1.ViewModels.Windows;
using System.Collections.ObjectModel;
using System.Text.Json;
using XHelper;

namespace DanDing1.Services
{
    /// <summary>
    /// 调度器配置
    /// </summary>
    public class SchedulerConfig
    {
        public int AutoShutdownIdleTime { get; set; } = 300;
        public int DefaultTaskTimeout { get; set; } = 3600;
        public bool EnableTaskRetry { get; set; } = true;
        public int MaxConcurrentEmulators { get; set; } = 4;
        public int MaxRetryCount { get; set; } = 3;
    }

    /// <summary>
    /// 定时调度服务 - 负责管理和执行定时任务
    /// </summary>
    public class SchedulerService
    {
        #region 私有字段

        // 检查任务的时间间隔
        private readonly TimeSpan _checkInterval = TimeSpan.FromSeconds(30);

        // 记录模拟器忙碌状态的字典
        private readonly Dictionary<string, bool> _emulatorBusyStatus = new();

        // 日志记录函数
        private readonly Action<string> _logAction;

        // 当前正在执行的任务列表
        private readonly List<string> _runningTaskIds = new();

        // 用于同步的锁对象
        private readonly object _syncLock = new();

        // 视图模型引用，用于处理特殊错误情况
        private readonly SchedulerWindowViewModel _viewModel;

        // 取消标记，用于停止服务
        private CancellationTokenSource _cancellationTokenSource;

        // 定时检查任务的计时器
        private Timer _checkTimer;

        // 默认任务超时时间（秒）
        private int _defaultTaskTimeout = 3600;

        // 模拟器集合
        private ObservableCollection<EmulatorItem> _emulators;

        // 服务运行状态
        private bool _isRunning = false;

        // 上一次活跃（在线）的模拟器数量，用于记录变化
        private int _lastActiveEmulatorCount = 0;

        // 任务集合
        private ObservableCollection<ScheduledTask> _tasks;

        #endregion 私有字段

        #region 构造函数

        /// <summary>
        /// 创建调度服务实例
        /// </summary>
        /// <param name="tasks">调度任务集合</param>
        /// <param name="emulators">模拟器集合</param>
        /// <param name="logAction">日志记录函数</param>
        /// <param name="viewModel">视图模型引用，用于处理特殊错误情况</param>
        public SchedulerService(ObservableCollection<ScheduledTask> tasks, ObservableCollection<EmulatorItem> emulators,
            Action<string> logAction, SchedulerWindowViewModel viewModel = null)
        {
            _tasks = tasks;
            _emulators = emulators;
            _logAction = logAction ?? (msg => XLogger.Info($"[调度服务] {msg}"));
            _viewModel = viewModel;
        }

        #endregion 构造函数

        #region 公共方法

        /// <summary>
        /// 启动调度服务
        /// </summary>
        public void Start()
        {
            if (_isRunning)
            {
                _logAction("调度服务已经在运行中");
                return;
            }

            // 初始化取消标记
            if (_cancellationTokenSource != null)
            {
                _cancellationTokenSource.Dispose();
            }
            _cancellationTokenSource = new CancellationTokenSource();

            // 设置GameControlService的取消令牌源
            GameControlService.SetCancellationTokenSource(_cancellationTokenSource);

            // 初始化模拟器状态
            InitializeEmulatorStatus().Wait();

            // 从配置文件直接读取超时设置
            try
            {
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                string configDir = Path.Combine(baseDir, "runtimes", "AppConfig", "Scheduler");
                string configPath = Path.Combine(configDir, "SchedulerConfig.json");

                if (File.Exists(configPath))
                {
                    string json = File.ReadAllText(configPath);
                    var config = JsonSerializer.Deserialize<SchedulerConfig>(json);
                    if (config != null)
                    {
                        _defaultTaskTimeout = config.DefaultTaskTimeout;
                        _logAction($"已从配置文件获取默认任务超时时间: {_defaultTaskTimeout}秒");
                    }
                }
                else
                {
                    _logAction($"未找到配置文件，使用默认任务超时时间: {_defaultTaskTimeout}秒");
                }
            }
            catch (Exception ex)
            {
                _logAction($"读取任务超时配置失败，使用默认值({_defaultTaskTimeout}秒): {ex.Message}");
            }

            // 初始化在线模拟器计数
            int activeEmulatorCount;
            lock (_syncLock)
            {
                activeEmulatorCount = _emulators.Count(e => e.Status == "在线");
                _lastActiveEmulatorCount = activeEmulatorCount;
            }

            // 读取最大并发模拟器数量配置
            int maxConcurrentEmulators = 4; // 默认值
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SchedulerConfig.json");
                if (File.Exists(configPath))
                {
                    string json = File.ReadAllText(configPath);
                    var config = JsonSerializer.Deserialize<SchedulerConfig>(json);
                    if (config != null)
                    {
                        maxConcurrentEmulators = config.MaxConcurrentEmulators;
                    }
                }
            }
            catch (Exception ex)
            {
                _logAction($"读取最大并发模拟器配置失败，使用默认值(4): {ex.Message}");
            }

            // 输出初始模拟器状态
            _logAction($"调度器已启动，最大并发模拟器数量: {maxConcurrentEmulators}");
            int totalEmulators;
            lock (_syncLock)
            {
                totalEmulators = _emulators.Count;
            }
            _logAction($"模拟器状态检测完成: {activeEmulatorCount} 个在线, {totalEmulators - activeEmulatorCount} 个离线");
            _logAction($"当前在线模拟器: {activeEmulatorCount}/{maxConcurrentEmulators}");

            // 创建并启动定时器
            _checkTimer = new Timer(CheckScheduledTasks, null, TimeSpan.Zero, _checkInterval);

            _isRunning = true;
            _logAction("调度服务已启动");
        }

        /// <summary>
        /// 异步启动调度服务
        /// </summary>
        public Task StartAsync()
        {
            return Task.Run(() =>
            {
                Start();
            });
        }

        /// <summary>
        /// 停止调度服务
        /// </summary>
        public async Task Stop()
        {
            if (!_isRunning)
            {
                _logAction("调度服务未在运行");
                return;
            }

            _logAction("正在停止调度服务...");

            // 先设置运行状态为false，防止新的任务被调度
            _isRunning = false;

            // 停止定时器
            _logAction("正在停止调度定时器...");
            _checkTimer?.Dispose();
            _checkTimer = null;

            // 发送取消信号并等待一小段时间让任务有机会响应
            try
            {
                _logAction("正在发送取消信号给所有任务...");

                // 先通知GameControlService停止接受新任务
                GameControlService.ClearCancellationTokenSource();
                _logAction("已清除GameControlService的取消令牌源，不再接受新任务");

                // 然后取消当前任务
                if (_cancellationTokenSource != null && !_cancellationTokenSource.IsCancellationRequested)
                {
                    _cancellationTokenSource.Cancel();
                    _logAction("已发送取消信号给所有正在执行的任务");
                }

                // 等待一小段时间让任务有机会响应取消信号
                _logAction("等待任务响应取消信号...");
                await Task.Delay(1000); // 增加等待时间，确保任务有足够时间响应

                // 释放取消令牌源
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
                _logAction("取消令牌已释放");
            }
            catch (Exception ex)
            {
                _logAction($"取消任务时出错: {ex.Message}");
            }

            // 清空运行中的任务列表
            int runningTaskCount;
            lock (_syncLock)
            {
                runningTaskCount = _runningTaskIds.Count;
                _logAction($"清空运行中的任务列表，共 {runningTaskCount} 个任务");
                _runningTaskIds.Clear();

                // 重置所有模拟器的忙碌状态
                foreach (var key in _emulatorBusyStatus.Keys.ToList())
                {
                    _emulatorBusyStatus[key] = false;
                }
                _logAction("已重置所有模拟器的忙碌状态");
            }

            // 停止所有正在运行的脚本任务，但不关闭模拟器
            _logAction("正在停止所有正在运行的脚本任务...");
            bool stopResult = await GameControlService.StopAllTasks();
            _logAction($"停止所有脚本任务{(stopResult ? "成功" : "完成，但有部分任务可能未正常停止")}");

            // 更新任务状态，但保留模拟器状态
            int interruptedTaskCount = 0;
            List<ScheduledTask> tasksSnapshot;
            lock (_syncLock)
            {
                tasksSnapshot = _tasks.ToList();
            }

            foreach (var task in tasksSnapshot)
            {
                if (task.Status == "执行中" || task.Status == "重试中" || task.Status == "重试执行中")
                {
                    task.Status = "已中断";
                    interruptedTaskCount++;
                }
            }
            _logAction($"已更新 {interruptedTaskCount} 个任务状态为'已中断'");

            // 更新模拟器的当前任务状态
            int onlineEmulatorCount = 0;
            List<EmulatorItem> emulatorsSnapshot;
            lock (_syncLock)
            {
                emulatorsSnapshot = _emulators.ToList();
            }

            foreach (var emulator in emulatorsSnapshot)
            {
                if (emulator.Status == "在线")
                {
                    emulator.CurrentTask = "空闲"; // 模拟器保持在线，但当前无任务
                    onlineEmulatorCount++;
                }
            }
            _logAction($"已将 {onlineEmulatorCount} 个在线模拟器的当前任务状态设为'空闲'");

            _logAction("调度服务已完全停止，模拟器保持运行状态");
        }

        #endregion 公共方法

        #region 私有方法

        /// <summary>
        /// 测试Cron表达式
        /// </summary>
        /// <param name="cronExpression">要测试的Cron表达式</param>
        /// <param name="count">要获取的未来执行时间数量</param>
        /// <returns>未来的执行时间列表</returns>
        public List<DateTime> TestCronExpression(string cronExpression, int count = 5)
        {
            List<DateTime> occurrences = new List<DateTime>();
            try
            {
                // 处理输入的Cron表达式
                // 如果表达式以数字和空格开头，可能是6段格式，需要移除第一段
                string normalizedCron = cronExpression;
                string[] parts = cronExpression.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 6)
                {
                    // 移除第一段(秒)，转换为5段标准格式
                    normalizedCron = string.Join(" ", parts.Skip(1));
                    _logAction($"测试Cron表达式：检测到6段Cron格式，已转换为5段标准格式: {normalizedCron}");
                }
                else if (parts.Length != 5)
                {
                    _logAction($"测试Cron表达式：无效的Cron表达式格式: {cronExpression}，应为5段格式");
                    return occurrences;
                }

                var expression = Cronos.CronExpression.Parse(normalizedCron);

                DateTime? currentTime = DateTime.UtcNow;
                for (int i = 0; i < count; i++)
                {
                    currentTime = expression.GetNextOccurrence(currentTime.Value, TimeZoneInfo.Local);
                    if (currentTime.HasValue)
                    {
                        occurrences.Add(currentTime.Value.ToLocalTime());
                        currentTime = currentTime.Value.AddSeconds(1); // 加1秒避免获取相同时间
                    }
                    else
                        break;
                }

                return occurrences;
            }
            catch (Exception ex)
            {
                _logAction($"测试Cron表达式失败: {ex.Message}");
                return occurrences;
            }
        }

        /// <summary>
        /// 检查模拟器是否正在运行
        /// </summary>
        /// <param name="mumu">MuMu实例</param>
        /// <param name="index">模拟器索引</param>
        /// <returns>是否正在运行</returns>
        private bool CheckEmulatorRunning(ScriptEngine.MuMu.MuMu mumu, int index)
        {
            try
            {
                // 调用IsRunningByIndex方法检查模拟器状态
                return mumu.IsRunningByIndex(index);
            }
            catch (Exception ex)
            {
                _logAction($"调用IsRunningByIndex检查模拟器状态时出错: {ex.Message}");

                // 替代方法：通过检查在线实例来判断
                try
                {
                    // 获取所有在线实例
                    var onlineInstances = mumu.GetOnlineInstance();
                    if (onlineInstances != null && onlineInstances.Count > 0)
                    {
                        // 查找是否有匹配的索引
                        foreach (var instance in onlineInstances)
                        {
                            if (instance.Index == index.ToString())
                            {
                                return true; // 找到匹配的在线模拟器
                            }
                        }
                    }
                }
                catch (Exception getInstanceEx)
                {
                    _logAction($"获取在线模拟器实例时出错: {getInstanceEx.Message}");
                }

                return false; // 未找到模拟器或发生错误
            }
        }

        /// <summary>
        /// 检查所有计划任务并执行满足条件的任务
        /// </summary>
        private async void CheckScheduledTasks(object state)
        {
            try
            {
                // 如果服务已停止，直接返回
                if (!_isRunning || _cancellationTokenSource == null || _cancellationTokenSource.IsCancellationRequested)
                {
                    _logAction("调度服务已停止，不再检查任务");
                    return;
                }

                // 创建需要检查的任务列表的副本，避免集合被修改导致异常
                List<ScheduledTask> tasksToCheck;
                lock (_syncLock)
                {
                    tasksToCheck = _tasks.Where(t => t.Enabled).ToList();
                }

                // 更新模拟器状态
                await UpdateEmulatorStatus();

                // 获取当前时间
                DateTime now = DateTime.Now;

                // 计算当前活跃模拟器数量（在线的模拟器数量）
                int activeEmulatorCount = 0;
                lock (_syncLock)
                {
                    // 修改计算方式：使用在线状态而非忙碌状态作为活跃标准，确保与SchedulerWindowViewModel一致
                    activeEmulatorCount = _emulators.Count(e => e.Status == "在线");
                }

                // 读取最大并发模拟器数量配置
                int maxConcurrentEmulators = 4; // 默认值
                try
                {
                    string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SchedulerConfig.json");
                    if (File.Exists(configPath))
                    {
                        string json = File.ReadAllText(configPath);
                        var config = JsonSerializer.Deserialize<SchedulerConfig>(json);
                        if (config != null)
                        {
                            maxConcurrentEmulators = config.MaxConcurrentEmulators;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logAction($"读取最大并发模拟器配置失败，使用默认值(4): {ex.Message}");
                }

                // 仅在模拟器数量发生变化时记录日志
                if (activeEmulatorCount != _lastActiveEmulatorCount)
                {
                    _logAction($"当前在线模拟器: {activeEmulatorCount}/{maxConcurrentEmulators}");
                    _lastActiveEmulatorCount = activeEmulatorCount;
                }

                // 获取正在执行任务的模拟器数量
                int busyEmulatorCount = 0;
                lock (_syncLock)
                {
                    busyEmulatorCount = _emulatorBusyStatus.Count(kvp => kvp.Value);
                }

                // 检查是否已达到最大并发数
                bool reachedMaxConcurrency = busyEmulatorCount >= maxConcurrentEmulators;
                if (reachedMaxConcurrency)
                {
                    _logAction($"已达到最大并发模拟器数量限制({maxConcurrentEmulators})，等待其他任务完成...");
                }

                // 遍历所有启用的任务
                foreach (var task in tasksToCheck)
                {
                    try
                    {
                        // 跳过已经在执行的任务
                        if (_runningTaskIds.Contains(task.Id))
                            continue;

                        // 检查任务的下次执行时间
                        if (!string.IsNullOrEmpty(task.NextExecutionTime) &&
                            DateTime.TryParse(task.NextExecutionTime, out DateTime nextTime))
                        {
                            // 检查是否过期超过30分钟
                            TimeSpan overdue = now - nextTime;
                            if (nextTime < now && overdue.TotalMinutes > 30)
                            {
                                // 检查当天是否已经完成过该任务
                                bool hasCompletedToday = await CheckTaskCompletedToday(task, nextTime.Date);

                                if (hasCompletedToday)
                                {
                                    task.Status = "已完成";
                                    // 当天已完成，不标记为过期，直接更新下次执行时间
                                    _logAction($"任务 [{task.Name}] 在 {nextTime.Date:yyyy-MM-dd} 已完成，跳过过期标记");
                                    UpdateNextExecutionTime(task);
                                    continue;
                                }

                                // 任务过期超过30分钟且当天未完成，标记为过期
                                task.Status = "已过期";
                                _logAction($"任务 [{task.Name}] 已过期超过30分钟，不执行并标记为已过期");

                                // 更新下次执行时间
                                UpdateNextExecutionTime(task);
                                continue;
                            }

                            // 检查是否将在45分钟内执行
                            TimeSpan timeUntilExecution = nextTime - now;
                            if (nextTime > now && timeUntilExecution.TotalMinutes <= 45)
                            {
                                // 将在45分钟内执行，标记为待执行
                                if (task.Status != "待执行")
                                {
                                    task.Status = "待执行";
                                    _logAction($"任务 [{task.Name}] 将在{timeUntilExecution.TotalMinutes:F0}分钟后执行，标记为待执行");
                                }
                            }
                        }

                        // 检查任务是否应该执行
                        if (!await ShouldExecuteTaskAsync(task, now))
                            continue;

                        // 获取任务关联的模拟器
                        EmulatorItem emulator;
                        lock (_syncLock)
                        {
                            emulator = _emulators.FirstOrDefault(e => e.Name == task.EmulatorName && e.Enabled);
                        }

                        if (emulator == null)
                        {
                            _logAction($"任务 [{task.Name}] 关联的模拟器 [{task.EmulatorName}] 不存在或已禁用");
                            continue;
                        }

                        // 检查模拟器是否忙碌
                        bool isBusy;
                        lock (_syncLock)
                        {
                            isBusy = _emulatorBusyStatus.TryGetValue(emulator.Name, out bool busy) && busy;
                        }

                        if (isBusy)
                        {
                            _logAction($"模拟器 [{emulator.Name}] 正忙，任务 [{task.Name}] 将稍后执行");
                            continue;
                        }

                        // 检查是否已达到最大并发数且当前模拟器未在运行
                        // 如果模拟器已在运行（但不忙碌），允许分配新任务，不计入并发限制
                        if (reachedMaxConcurrency && emulator.Status != "在线")
                        {
                            _logAction($"已达到最大并发限制，任务 [{task.Name}] 将等待其他任务完成后执行");
                            continue;
                        }

                        // 异步执行任务
                        Task.Run(() => ExecuteTaskAsync(task, emulator));
                    }
                    catch (Exception ex)
                    {
                        _logAction($"处理任务 [{task.Name}] 时出错: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logAction($"检查调度任务时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 异步执行任务
        /// </summary>
        private async Task ExecuteTaskAsync(ScheduledTask task, EmulatorItem emulator)
        {
            // 添加历史记录ID变量
            string historyRecordId = null;

            try
            {
                // 如果服务已停止，不执行任务
                if (!_isRunning || _cancellationTokenSource == null || _cancellationTokenSource.IsCancellationRequested)
                {
                    _logAction($"调度服务已停止，任务 [{task.Name}] 不会执行");

                    // 确保清理任务状态
                    lock (_syncLock)
                    {
                        _runningTaskIds.Remove(task.Id);
                        if (_emulatorBusyStatus.ContainsKey(emulator.Name))
                        {
                            _emulatorBusyStatus[emulator.Name] = false;
                        }
                    }

                    return;
                }

                // 将任务ID添加到运行列表，防止重复执行
                lock (_syncLock)
                {
                    _runningTaskIds.Add(task.Id);
                    _emulatorBusyStatus[emulator.Name] = true;
                }

                // 记录任务开始执行历史
                if (_viewModel != null)
                {
                    historyRecordId = _viewModel.RecordTaskStarted(task, emulator);
                    _logAction($"已记录任务 [{task.Name}] 开始执行历史，记录ID: {historyRecordId}");
                }

                // 从配置文件获取重试设置
                bool enableTaskRetry = true; // 默认启用重试
                int maxRetryCount = 3;       // 默认最大重试次数
                int currentRetryCount = 0;   // 当前重试次数

                try
                {
                    string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SchedulerConfig.json");
                    if (File.Exists(configPath))
                    {
                        string json = File.ReadAllText(configPath);
                        var config = JsonSerializer.Deserialize<SchedulerConfig>(json);
                        if (config != null)
                        {
                            enableTaskRetry = config.EnableTaskRetry;
                            maxRetryCount = config.MaxRetryCount;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logAction($"读取任务重试配置失败，使用默认值: {ex.Message}");
                }

                bool taskSuccess = false;
                bool needRetry = false;

                do
                {
                    try
                    {
                        // 如果是重试，记录相关信息
                        if (currentRetryCount > 0)
                        {
                            _logAction($"开始第 {currentRetryCount}/{maxRetryCount} 次重试任务: [{task.Name}] 在模拟器: [{emulator.Name}]");
                            // 重置任务状态
                            task.Status = "重试中";
                        }

                        // 检查模拟器是否已经在线，如果是则需要重启
                        if (emulator.Status == "在线")
                        {
                            _logAction($"模拟器 [{emulator.Name}] 已处于在线状态，准备重启...");

                            // 关闭模拟器
                            bool closeSuccess = await GameControlService.CloseEmulator(emulator);
                            if (!closeSuccess)
                            {
                                _logAction($"模拟器 [{emulator.Name}] 关闭失败，任务 [{task.Name}] 可能无法正常执行");
                            }
                            else
                            {
                                _logAction($"模拟器 [{emulator.Name}] 已关闭，等待关闭完成...");
                            }

                            // 等待一段时间确保模拟器完全关闭
                            await Task.Delay(5000);

                            // 获取对应的MuMu实例类型
                            ScriptEngine.MuMu.MuMu mumu = new();
                            string mumuPath = XHelper.XConfig.LoadValueFromFile<string>("MuMuPath");

                            if (string.IsNullOrEmpty(mumuPath))
                            {
                                _logAction($"MuMu模拟器路径未配置，无法启动模拟器 [{emulator.Name}]");

                                // 记录任务失败历史
                                if (_viewModel != null && historyRecordId != null)
                                {
                                    _viewModel.RecordTaskCompleted(historyRecordId, false, "MuMu模拟器路径未配置，无法启动模拟器");
                                }

                                return;
                            }

                            if (!mumu.Init(mumuPath))
                            {
                                _logAction($"MuMu模拟器初始化失败，无法启动模拟器 [{emulator.Name}]");

                                // 记录任务失败历史
                                if (_viewModel != null && historyRecordId != null)
                                {
                                    _viewModel.RecordTaskCompleted(historyRecordId, false, "MuMu模拟器初始化失败，无法启动模拟器");
                                }

                                return;
                            }

                            _logAction($"MuMu模拟器初始化成功，准备启动模拟器 [{emulator.Name}]");

                            // 解析模拟器索引
                            if (!int.TryParse(emulator.SimulatorIndex, out int index))
                            {
                                _logAction($"无法解析模拟器索引: {emulator.SimulatorIndex}");

                                // 记录任务失败历史
                                if (_viewModel != null && historyRecordId != null)
                                {
                                    _viewModel.RecordTaskCompleted(historyRecordId, false, $"无法解析模拟器索引: {emulator.SimulatorIndex}");
                                }

                                return;
                            }

                            // 启动模拟器
                            var result = await mumu.OpenByIndexAsync(index);
                            if (!result.success || result.mainWndHandle == 0 || result.renderWndHandle == 0)
                            {
                                _logAction($"模拟器 [{emulator.Name}] 启动失败，无法执行任务");

                                // 确定是否需要重试
                                needRetry = enableTaskRetry && currentRetryCount < maxRetryCount;
                                if (needRetry)
                                {
                                    currentRetryCount++;
                                    _logAction($"模拟器启动失败，{maxRetryCount - currentRetryCount + 1}秒后重试 ({currentRetryCount}/{maxRetryCount})");
                                    await Task.Delay((maxRetryCount - currentRetryCount + 1) * 1000);
                                    continue;
                                }

                                // 记录任务失败历史
                                if (_viewModel != null && historyRecordId != null)
                                {
                                    _viewModel.RecordTaskCompleted(historyRecordId, false, $"模拟器 [{emulator.Name}] 启动失败，无法执行任务");
                                }

                                return;
                            }

                            _logAction($"模拟器 [{emulator.Name}] 启动成功，等待10秒后启动游戏...");

                            // 等待10秒，确保模拟器完全启动
                            await Task.Delay(10000);

                            // 启动阴阳师应用
                            int adbPort = mumu.GetAdbPortByIndex(index).port;
                            try
                            {
                                mumu.Start_App("阴阳师", adbPort);
                                _logAction($"阴阳师已在模拟器 [{emulator.Name}] 上启动成功");
                            }
                            catch (Exception ex)
                            {
                                _logAction($"阴阳师启动失败，无法执行任务 [{task.Name}]: {ex.Message}");

                                // 确定是否需要重试
                                needRetry = enableTaskRetry && currentRetryCount < maxRetryCount;
                                if (needRetry)
                                {
                                    currentRetryCount++;
                                    _logAction($"游戏启动失败，{maxRetryCount - currentRetryCount + 1}秒后重试 ({currentRetryCount}/{maxRetryCount})");
                                    await Task.Delay((maxRetryCount - currentRetryCount + 1) * 1000);
                                    continue;
                                }

                                // 记录任务失败历史
                                if (_viewModel != null && historyRecordId != null)
                                {
                                    _viewModel.RecordTaskCompleted(historyRecordId, false, $"阴阳师启动失败: {ex.Message}");
                                }

                                return;
                            }
                        }

                        // 更新任务和模拟器状态
                        task.Status = currentRetryCount > 0 ? "重试执行中" : "执行中";
                        emulator.Status = "在线";
                        emulator.CurrentTask = task.Name;

                        if (currentRetryCount == 0)
                        {
                            _logAction($"开始执行任务: [{task.Name}] 在模拟器: [{emulator.Name}]");
                        }

                        DateTime startTime = DateTime.Now;

                        // 创建一个CancellationTokenSource用于控制时间更新
                        using var durationUpdateCts = new CancellationTokenSource();

                        // 启动运行时长更新任务
                        var updateTask = UpdateRunningDuration(emulator, durationUpdateCts.Token);

                        // 执行任务
                        taskSuccess = await LaunchTaskAsync(task, emulator);

                        // 停止运行时长更新
                        durationUpdateCts.Cancel();
                        try
                        {
                            // 等待更新任务完成
                            await updateTask;
                        }
                        catch (OperationCanceledException)
                        {
                            // 忽略取消异常
                        }

                        // 不再在这里重置运行时长，保留累加的时间

                        // 如果任务执行失败且启用了重试
                        if (!taskSuccess && enableTaskRetry && currentRetryCount < maxRetryCount)
                        {
                            // 检查服务是否已停止
                            if (!_isRunning || _cancellationTokenSource == null || _cancellationTokenSource.IsCancellationRequested)
                            {
                                _logAction($"调度服务已停止，任务 [{task.Name}] 不会重试");
                                task.Status = "已中断";
                                needRetry = false;

                                // 记录任务中断历史
                                if (_viewModel != null && historyRecordId != null)
                                {
                                    _viewModel.RecordTaskCancelled(historyRecordId, false, "重试过程中调度服务已停止，任务被中断");
                                }
                            }
                            else
                            {
                                currentRetryCount++;
                                _logAction($"任务 [{task.Name}] 执行失败，{maxRetryCount - currentRetryCount + 1}秒后进行第 {currentRetryCount}/{maxRetryCount} 次重试");
                                task.Status = "准备重试";

                                // 使用带取消标记的延迟
                                try
                                {
                                    await Task.Delay((maxRetryCount - currentRetryCount + 1) * 1000, _cancellationTokenSource.Token);
                                    needRetry = true;
                                }
                                catch (OperationCanceledException)
                                {
                                    _logAction($"任务 [{task.Name}] 重试已取消，调度服务已停止");
                                    task.Status = "已中断";
                                    needRetry = false;

                                    // 记录任务中断历史
                                    if (_viewModel != null && historyRecordId != null)
                                    {
                                        _viewModel.RecordTaskCancelled(historyRecordId, false, "重试过程中调度服务已停止，任务被中断");
                                    }
                                }
                            }
                        }
                        else
                        {
                            // 更新任务状态
                            if (taskSuccess)
                            {
                                task.Status = currentRetryCount > 0 ? $"重试成功({currentRetryCount}次)" : "已完成";
                                _logAction($"任务 [{task.Name}] {(currentRetryCount > 0 ? $"在第 {currentRetryCount} 次重试后" : "")}执行成功");
                                needRetry = false;

                                // 记录任务成功历史
                                if (_viewModel != null && historyRecordId != null)
                                {
                                    _viewModel.RecordTaskCompleted(historyRecordId, true);
                                }
                            }
                            else
                            {
                                task.Status = currentRetryCount > 0 ? $"重试失败(达到{maxRetryCount}次)" : "执行失败";
                                _logAction($"任务 [{task.Name}] {(currentRetryCount > 0 ? $"经过 {currentRetryCount} 次重试后依然" : "")}执行失败");
                                needRetry = false;

                                // 记录任务失败历史
                                if (_viewModel != null && historyRecordId != null)
                                {
                                    _viewModel.RecordTaskCompleted(historyRecordId, false,
                                        currentRetryCount > 0
                                            ? $"经过 {currentRetryCount} 次重试后依然执行失败"
                                            : "任务执行失败");
                                }
                            }
                        }

                        // 如果任务成功完成，更新下次执行时间
                        if (taskSuccess)
                        {
                            UpdateNextExecutionTime(task);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logAction($"执行任务 [{task.Name}] 时出错: {ex.Message}");

                        // 记录任务失败历史
                        if (_viewModel != null && historyRecordId != null)
                        {
                            _viewModel.RecordTaskCompleted(historyRecordId, false, $"执行任务时出错: {ex.Message}");
                        }

                        // 检查是否是大漠验证失败错误
                        if (_viewModel != null && (ex.Message.Contains("账号未登录或登录已过期") ||
                            ex.ToString().Contains("账号未登录或登录已过期")))
                        {
                            _logAction("检测到大漠插件验证失败错误，尝试特殊处理...");
                            try
                            {
                                // 调用ViewModel中的特殊处理方法
                                bool shouldRetry = await _viewModel.HandleTaskExecutionError(ex.Message);

                                if (!shouldRetry)
                                {
                                    _logAction("大漠验证失败，重新登录处理未成功，停止重试");
                                    needRetry = false;
                                    return;
                                }
                                else
                                {
                                    _logAction("大漠验证失败已处理，将继续重试任务");
                                    currentRetryCount++;
                                    needRetry = true;
                                    await Task.Delay(3000); // 等待3秒后重试
                                    continue;
                                }
                            }
                            catch (Exception handlerEx)
                            {
                                _logAction($"处理大漠验证失败时出错: {handlerEx.Message}");
                            }
                        }

                        // 标准重试逻辑
                        // 确定是否需要重试
                        if (enableTaskRetry && currentRetryCount < maxRetryCount)
                        {
                            currentRetryCount++;
                            _logAction($"任务 [{task.Name}] 执行出错，{maxRetryCount - currentRetryCount + 1}秒后进行第 {currentRetryCount}/{maxRetryCount} 次重试");
                            await Task.Delay((maxRetryCount - currentRetryCount + 1) * 1000);
                            needRetry = true;
                        }
                        else
                        {
                            needRetry = false;
                        }

                        // 确保模拟器状态在出错时也被重置为离线
                        emulator.Status = "离线";
                    }
                } while (needRetry);

                // 重置模拟器状态
                emulator.Status = "离线";
                emulator.CurrentTask = "无";
            }
            catch (Exception ex)
            {
                _logAction($"执行任务 [{task.Name}] 主流程出错: {ex.Message}");
                task.Status = "执行错误";

                // 确保模拟器状态在出错时也被重置为离线
                emulator.Status = "离线";
            }
            finally
            {
                // 从运行列表中移除任务ID
                lock (_syncLock)
                {
                    _runningTaskIds.Remove(task.Id);
                    _emulatorBusyStatus[emulator.Name] = false;
                }
            }
        }

        /// <summary>
        /// 从Cron表达式计算下次执行时间
        /// </summary>
        private DateTime GetNextCronTime(string cronExpression)
        {
            try
            {
                // 处理输入的Cron表达式
                // 如果表达式以数字和空格开头，可能是6段格式，需要移除第一段
                string normalizedCron = cronExpression;
                string[] parts = cronExpression.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 6)
                {
                    // 移除第一段(秒)，转换为5段标准格式
                    normalizedCron = string.Join(" ", parts.Skip(1));
                    _logAction($"检测到6段Cron格式，已转换为5段标准格式: {normalizedCron}");
                }
                else if (parts.Length != 5)
                {
                    _logAction($"无效的Cron表达式格式: {cronExpression}，应为5段格式");
                    return DateTime.MaxValue;
                }

                // 解析Cron表达式
                var expression = Cronos.CronExpression.Parse(normalizedCron);

                // 获取下次执行时间（从当前UTC时间开始，转换到本地时区）
                var nextTime = expression.GetNextOccurrence(DateTime.UtcNow, TimeZoneInfo.Local);

                // 如果无法计算下次执行时间，返回最大日期值
                return nextTime?.ToLocalTime() ?? DateTime.MaxValue;
            }
            catch (Exception ex)
            {
                _logAction($"Cron表达式解析错误: {ex.Message}");
                return DateTime.MaxValue; // 返回最大日期值表示错误
            }
        }

        /// <summary>
        /// 初始化所有模拟器的状态
        /// </summary>
        private async Task InitializeEmulatorStatus()
        {
            lock (_syncLock)
            {
                // 清空状态字典
                _emulatorBusyStatus.Clear();
            }

            // 初始化MuMu模拟器
            var mumu = new ScriptEngine.MuMu.MuMu();
            string path = XHelper.XConfig.LoadValueFromFile<string>("MuMuPath");
            bool mumuInitialized = !string.IsNullOrEmpty(path) && mumu.Init(path);

            if (!mumuInitialized)
            {
                _logAction("MuMu模拟器初始化失败，无法检测模拟器状态");
            }

            // 创建模拟器集合的快照，避免枚举过程中集合被修改
            List<EmulatorItem> emulatorsSnapshot;
            lock (_syncLock)
            {
                emulatorsSnapshot = _emulators.ToList();
            }

            // 初始化所有模拟器的状态
            foreach (var emulator in emulatorsSnapshot)
            {
                lock (_syncLock)
                {
                    _emulatorBusyStatus[emulator.Name] = false;
                }

                bool isRunning = false;

                // 实时检测模拟器运行状态
                if (mumuInitialized && !string.IsNullOrEmpty(emulator.SimulatorIndex))
                {
                    if (int.TryParse(emulator.SimulatorIndex, out int index))
                    {
                        try
                        {
                            // 检查模拟器是否正在运行
                            isRunning = CheckEmulatorRunning(mumu, index);

                            // 同步更新模拟器的内部状态
                            emulator.SimulatorConfig.IsRunning = isRunning;
                        }
                        catch (Exception ex)
                        {
                            _logAction($"检测模拟器 [{emulator.Name}] 状态时出错: {ex.Message}");
                        }
                    }
                }
                else
                {
                    // 如果无法实时检测，则使用当前记录的状态
                    isRunning = emulator.SimulatorConfig?.IsRunning ?? false;
                }

                // 根据运行状态设置在线/离线状态
                emulator.Status = isRunning ? "在线" : "离线";

                // 如果在线但没有任务，显示为"空闲"
                emulator.CurrentTask = isRunning ? "空闲" : "无";

                // 软件启动时始终将运行时长重置为0
                emulator.RunningDuration = "0:00:00";

                _logAction($"模拟器 [{emulator.Name}] 初始状态: {emulator.Status}");
            }
        }

        /// <summary>
        /// 启动任务执行
        /// </summary>
        private async Task<bool> LaunchTaskAsync(ScheduledTask task, EmulatorItem emulator)
        {
            try
            {
                // 如果服务已停止，不执行任务
                if (!_isRunning || _cancellationTokenSource == null || _cancellationTokenSource.IsCancellationRequested)
                {
                    _logAction($"调度服务已停止，任务 [{task.Name}] 不会启动");
                    return false;
                }

                _logAction($"准备启动任务: [{task.Name}] 参数解析中...");

                // 解析任务参数
                if (string.IsNullOrEmpty(task.TaskParameters))
                {
                    _logAction($"任务 [{task.Name}] 参数为空，无法执行");
                    return false;
                }

                // 解析任务配置
                AddTaskPropertyViewModel model = new();
                model.SetConfigs(task.TaskParameters);
                var configs = model.GameTaskLists;
                if (configs == null || configs.Count == 0)
                {
                    _logAction($"任务 [{task.Name}] 参数解析失败或为空，无法执行");
                    return false;
                }

                _logAction($"任务集合： [{task.Name}] 参数解析成功，准备启动脚本");

                // 创建一个回调函数来接收脚本输出并将其写入日志
                GameControlService.LogCallback scriptLogCallback = (message) =>
                {
                    _logAction(message);

                    // 检测是否是大漠验证失败的错误日志
                    if (message.Contains("账号未登录或登录已过期") && _viewModel != null)
                    {
                        // 抛出异常以触发重试机制
                        throw new InvalidOperationException(
                            "检测到大漠插件验证失败: " + message);
                    }
                };

                // 启动脚本，并传递日志回调函数
                if (await GameControlService.Start(model, emulator, task, scriptLogCallback) is false)
                    return false;

                // 添加脚本执行完成的检测
                _logAction($"脚本已启动，等待脚本执行完成...");
                bool isCompleted = false;
                int checkIntervalMs = 1000; // 每秒检查一次

                // 使用服务类中存储的默认超时时间
                int maxWaitTime = _defaultTaskTimeout * 1000; // 转换为毫秒

                _logAction($"设置任务超时时间为：{_defaultTaskTimeout}秒");
                int waitedTime = 0;

                while (!isCompleted && waitedTime < maxWaitTime)
                {
                    // 检查脚本是否完成
                    isCompleted = await GameControlService.IsScriptCompleted(emulator);

                    if (!isCompleted)
                    {
                        await Task.Delay(checkIntervalMs);
                        waitedTime += checkIntervalMs;
                    }
                }

                if (isCompleted)
                {
                    _logAction($"脚本执行完成: [{task.Name}]");
                    return true;
                }
                else
                {
                    _logAction($"脚本执行超时: [{task.Name}]，已等待{waitedTime / 1000}秒");
                    // 尝试停止脚本执行
                    await GameControlService.Stop(emulator);
                    return false;
                }
            }
            catch (Exception ex)
            {
                // 检查是否是大漠验证失败异常
                if ((ex.Message.Contains("账号未登录或登录已过期") ||
                     ex.ToString().Contains("账号未登录或登录已过期")) &&
                    _viewModel != null)
                {
                    _logAction($"任务执行中检测到大漠插件验证失败: {ex.Message}");
                    // 重新抛出异常，让上层ExecuteTaskAsync方法处理
                    throw;
                }

                _logAction($"启动任务 [{task.Name}] 执行时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 判断任务是否应该执行
        /// </summary>
        private async Task<bool> ShouldExecuteTaskAsync(ScheduledTask task, DateTime now)
        {
            if (string.IsNullOrEmpty(task.NextExecutionTime))
                return false;

            if (!DateTime.TryParse(task.NextExecutionTime, out DateTime nextTime))
                return false;

            // 如果下次执行时间已过
            if (nextTime <= now)
            {
                // 检查是否过期超过30分钟
                TimeSpan overdue = now - nextTime;
                if (overdue.TotalMinutes > 30)
                {
                    // 检查当天是否已经完成过该任务
                    bool hasCompletedToday = await CheckTaskCompletedToday(task, nextTime.Date);
                    if (hasCompletedToday)
                    {
                        task.Status = "已完成";
                        // 当天已完成，不执行
                        return false;
                    }

                    // 过期超过30分钟且当天未完成，不执行
                    return false;
                }
                // 未过期30分钟，可以执行
                return true;
            }

            return false;
        }

        /// <summary>
        /// 更新所有模拟器的状态
        /// </summary>
        private async Task UpdateEmulatorStatus()
        {
            // 初始化MuMu模拟器
            var mumu = new ScriptEngine.MuMu.MuMu();
            string path = XHelper.XConfig.LoadValueFromFile<string>("MuMuPath");
            bool mumuInitialized = !string.IsNullOrEmpty(path) && mumu.Init(path);

            if (!mumuInitialized)
            {
                return; // 无法初始化模拟器，保持当前状态
            }

            // 创建模拟器集合的快照，避免枚举过程中集合被修改
            List<EmulatorItem> emulatorsSnapshot;
            lock (_syncLock)
            {
                emulatorsSnapshot = _emulators.ToList();
            }

            // 更新所有模拟器的状态
            foreach (var emulator in emulatorsSnapshot)
            {
                // 跳过正在执行任务的模拟器
                bool isBusy;
                lock (_syncLock)
                {
                    isBusy = _emulatorBusyStatus.TryGetValue(emulator.Name, out bool busy) && busy;
                }

                if (isBusy)
                {
                    // 如果模拟器正在执行任务，则不更新其状态
                    continue;
                }

                if (!string.IsNullOrEmpty(emulator.SimulatorIndex) && int.TryParse(emulator.SimulatorIndex, out int index))
                {
                    try
                    {
                        // 检查模拟器是否正在运行
                        bool isRunning = CheckEmulatorRunning(mumu, index);

                        // 更新模拟器状态
                        emulator.Status = isRunning ? "在线" : "离线";

                        // 更新显示的任务
                        if (isRunning)
                        {
                            emulator.CurrentTask = emulator.CurrentTask == "无" ? "空闲" : emulator.CurrentTask;
                        }
                        else
                        {
                            emulator.CurrentTask = "无";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logAction($"更新模拟器 [{emulator.Name}] 状态时出错: {ex.Message}");
                    }
                }
            }

            // 计算并通知活跃模拟器数量
            try
            {
                int activeCount;
                lock (_syncLock)
                {
                    activeCount = _emulators.Count(e => e.Status == "在线");

                    // 更新上次活跃模拟器计数（但不输出日志，由CheckScheduledTasks负责）
                    _lastActiveEmulatorCount = activeCount;
                }

                // 使用Application.Current.Dispatcher调用UI线程安全地更新ViewModel
                Application.Current.Dispatcher.Invoke(() =>
                {
                    // 查找当前的SchedulerWindowViewModel实例
                    var viewModel = App.GetService<SchedulerWindowViewModel>();
                    if (viewModel != null)
                    {
                        // 更新活跃模拟器数量显示
                        viewModel.ActiveEmulators = activeCount.ToString();
                    }
                });
            }
            catch (Exception ex)
            {
                _logAction($"更新活跃模拟器计数时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新任务的下次执行时间
        /// </summary>
        private void UpdateNextExecutionTime(ScheduledTask task)
        {
            DateTime now = DateTime.Now;
            DateTime nextTime;

            switch (task.ScheduleType)
            {
                case "一次性":
                    // 一次性任务执行后禁用
                    task.Enabled = false;
                    _logAction($"一次性任务 [{task.Name}] 已执行完成，已禁用");
                    return;

                case "每日":
                    // 如果能解析当前的下次执行时间，则保持时分秒不变，日期改为明天
                    if (DateTime.TryParse(task.NextExecutionTime, out DateTime dailyTime))
                    {
                        var timeOfDay = dailyTime.TimeOfDay;
                        nextTime = DateTime.Today.AddDays(1).Add(timeOfDay);
                    }
                    else
                    {
                        // 默认设置为明天同一时间
                        nextTime = now.AddDays(1);
                    }
                    break;

                case "每周":
                    // 如果能解析当前的下次执行时间，则保持星期几和时分秒不变，日期改为下周
                    if (DateTime.TryParse(task.NextExecutionTime, out DateTime weeklyTime))
                    {
                        // 获取星期几和时间
                        DayOfWeek dayOfWeek = weeklyTime.DayOfWeek;
                        var timeOfDay = weeklyTime.TimeOfDay;

                        // 计算本周这个星期几的日期
                        int daysUntilNextWeekday = ((int)dayOfWeek - (int)now.DayOfWeek + 7) % 7;
                        if (daysUntilNextWeekday == 0)
                            daysUntilNextWeekday = 7; // 如果是同一天，则是下周的这一天

                        nextTime = now.Date.AddDays(daysUntilNextWeekday).Add(timeOfDay);
                    }
                    else
                    {
                        // 默认设置为下周同一时间
                        nextTime = now.AddDays(7);
                    }
                    break;

                case "每月":
                    // 如果能解析当前的下次执行时间，则保持日和时分秒不变，月份改为下月
                    if (DateTime.TryParse(task.NextExecutionTime, out DateTime monthlyTime))
                    {
                        int day = monthlyTime.Day;
                        var timeOfDay = monthlyTime.TimeOfDay;

                        // 计算下个月的日期
                        DateTime nextMonth = new DateTime(now.Year, now.Month, 1).AddMonths(1);

                        // 确保日期有效（处理2月等问题）
                        int daysInNextMonth = DateTime.DaysInMonth(nextMonth.Year, nextMonth.Month);
                        day = Math.Min(day, daysInNextMonth);

                        nextTime = new DateTime(nextMonth.Year, nextMonth.Month, day).Add(timeOfDay);
                    }
                    else
                    {
                        // 默认设置为下个月同一时间
                        nextTime = now.AddMonths(1);
                    }
                    break;

                case "间隔执行":
                    // 简单地在当前时间上加上间隔分钟数
                    nextTime = now.AddMinutes(task.IntervalMinutes);
                    _logAction($"间隔执行任务 [{task.Name}] 下次将在 {task.IntervalMinutes} 分钟后执行");
                    break;

                case "循环执行":
                    try
                    {
                        // 使用Cron表达式计算下次执行时间
                        nextTime = GetNextCronTime(task.CronExpression);
                        if (nextTime == DateTime.MaxValue)
                        {
                            _logAction($"警告：任务 [{task.Name}] 的Cron表达式 '{task.CronExpression}' 无法计算有效的下次执行时间");
                            // 设置为24小时后，避免无限循环
                            nextTime = now.AddDays(1);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logAction($"计算Cron表达式时间失败: {ex.Message}, 任务 [{task.Name}] 将在24小时后重试");
                        // 出错则24小时后重试
                        nextTime = now.AddDays(1);
                    }
                    break;

                default:
                    // 未知调度类型，设为一周后
                    nextTime = now.AddDays(7);
                    break;
            }

            // 更新任务的下次执行时间
            task.NextExecutionTime = nextTime.ToString("yyyy-MM-dd HH:mm:ss");
            _logAction($"任务 [{task.Name}] 下次执行时间已更新为: {task.NextExecutionTime}");
        }

        /// <summary>
        /// 检查任务在指定日期是否已完成
        /// </summary>
        /// <param name="task">要检查的任务</param>
        /// <param name="date">检查的日期</param>
        /// <returns>是否已完成</returns>
        private async Task<bool> CheckTaskCompletedToday(ScheduledTask task, DateTime date)
        {
            try
            {
                // 创建临时的历史记录服务实例来检查
                var historyService = new TaskHistoryService();

                // 获取指定日期的历史记录
                var records = historyService.LoadRecordsByDate(date);

                // 检查是否有该任务在指定日期成功完成的记录
                bool hasCompleted = records.Any(r =>
                    r.TaskId == task.Id &&
                    r.TaskName == task.Name &&
                    r.EmulatorName == task.EmulatorName &&
                    (r.Status == "成功" || r.Status.Contains("成功")) &&
                    r.StartTime.Date == date.Date);

                if (hasCompleted)
                {
                    _logAction($"发现任务 [{task.Name}] 在 {date:yyyy-MM-dd} 已有成功完成记录");
                }

                return hasCompleted;
            }
            catch (Exception ex)
            {
                _logAction($"检查任务 [{task.Name}] 完成状态时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 定期更新模拟器运行时长
        /// </summary>
        /// <param name="emulator">模拟器对象</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        private async Task UpdateRunningDuration(EmulatorItem emulator, CancellationToken cancellationToken)
        {
            try
            {
                // 如果服务已停止，直接返回
                if (!_isRunning)
                {
                    return;
                }

                // 解析当前运行时间字符串为TimeSpan
                TimeSpan currentDuration;
                if (!TimeSpan.TryParseExact(emulator.RunningDuration, @"h\:mm\:ss", null, out currentDuration))
                {
                    // 如果解析失败，默认为0
                    currentDuration = TimeSpan.Zero;
                }

                // 记录开始计时的时间点
                DateTime startTime = DateTime.Now;

                // 每秒更新一次模拟器运行时长
                while (!cancellationToken.IsCancellationRequested && _isRunning)
                {
                    // 计算新增的运行时长
                    TimeSpan elapsed = DateTime.Now - startTime;

                    // 累加到已有时间上
                    TimeSpan totalDuration = currentDuration.Add(elapsed);

                    // 更新UI
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        // 格式化时间为时:分:秒
                        emulator.RunningDuration = totalDuration.ToString(@"h\:mm\:ss");
                    });

                    // 使用带取消标记的延迟
                    try
                    {
                        await Task.Delay(1000, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        // 取消操作，退出循环
                        break;
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 忽略取消异常
            }
            catch (Exception ex)
            {
                _logAction($"更新模拟器 [{emulator.Name}] 运行时长时出错: {ex.Message}");
            }
        }

        #endregion 私有方法
    }
}