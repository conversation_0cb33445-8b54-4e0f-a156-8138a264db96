﻿using DamoControlKit.Interface;

namespace DamoControlKit.Model
{
    /// <summary>
    /// 像素点颜色 适用于 CmpColor
    /// </summary>
    public class Pixel(int x, int y, string color, double sim) : IFind
    {
        public string Color { get; set; } = color ?? "";
        public double Sim { get; set; } = sim;
        public int X { get; set; } = x;
        public dmsoft? dmsoft { get; set; }
        public int Y { get; set; } = y;

        public bool Await(dmsoft? x, int period)
        {
            if (Color == "") return false;
            dmsoft? xtmp = (x ?? dmsoft) ?? throw new Exception($"颜色 {color}没有指定可用的插件对象，导致错误！");
            int count = period / 100;
            int new_Count = 0;
            while (new_Count < count)
            {
                if (xtmp.CmpColor(X, Y, Color, Sim) == 0)
                    return true;
                new_Count++;
                xtmp.delay(100);
            }
            return false;
        }

        public bool Find(dmsoft? x)
        {
            if (Color == "") return false;
            dmsoft? xtmp = x ?? dmsoft;
            return xtmp is null ? throw new Exception($"颜色 {color}没有指定可用的插件对象，导致错误！") : xtmp.CmpColor(X, Y, Color, Sim) == 0;
        }

        public Point? FindPoint(dmsoft? x)
        {
            throw new NotImplementedException();
        }

        public bool SetXsoft()
        {
            throw new NotImplementedException();
        }

        public bool SetXsoft(dmsoft x)
        {
            dmsoft = x; return true;
        }
    }

    /// <summary>
    /// 像素颜色集合
    /// </summary>
    public class Pixels
    {
        public int Count => PixelList.Count;
        public Pixels() { }

        public Pixels(List<Pixel> lists) { Add(lists); }

        public Pixels(Pixels pixels) { Add(pixels); }

        /// <summary>
        /// 像素颜色集合
        /// </summary>
        public List<Pixel> PixelList { get; set; } = [];

        public bool Add(Pixel pix)
        {
            PixelList.Add(pix);
            return true;
        }

        public bool Add(List<Pixel> lists)
        {
            PixelList.AddRange(lists);
            return true;
        }

        public bool Add(Pixels pix)
        {
            PixelList.AddRange(pix.PixelList);
            return true;
        }

        public bool Remove(Pixel pix)
        {
            if (PixelList.IndexOf(pix) == -1)
                return false;
            PixelList.Remove(pix);
            return true;
        }

        public void SetXsoft(dmsoft x) => PixelList.ForEach(t => t.SetXsoft(x));

        public bool FindAll()
        {
            foreach (var item in PixelList)
                if (item.Find(null)) return true;
            return false;
        }
    }
}