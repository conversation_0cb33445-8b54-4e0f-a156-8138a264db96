﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System.Diagnostics;

namespace ScriptEngine.Tasks
{
    internal class DouJi : BaseTask
    {
        private Dictionary<string, Position> biaoJiPoss = new()
        {
            {"位置1",new Position("172,416,264,588") },
            {"位置2",new Position("409,381,488,546") },
            {"位置3",new Position("612,370,676,462") },
            {"位置4",new Position("796,300,859,402") },
            {"位置5",new Position("989,375,1072,494") },
        };

        /// <summary>
        /// 任务配置
        /// </summary>
        private DouJiConfig _config;

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "斗技");
            foreach (var item in biaoJiPoss)
                item.Value.SetXsoft(Dm);
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            _config = new(configs);

            //场景判断
            string nows = Scene.NowScene;
            if (nows != "斗技")
            {
                //先去探索，获取突破卷数量
                if (!Scene.TO.DouJi())
                {
                    log.Warn("斗技任务无法继续，当前游戏所在场景未知，请调整到庭院或探索主界面开始脚本！");
                    return;
                }
            }
            main();
            UserNotificationMessage = $"共战斗胜利{count_su}次，失败{count_fa}次，目标胜利次数为：{_config.Ncount}，胜率为{count_su / (count_fa + count_su) * 100:0.00}%.";
        }

        private double count_su = 0;//胜利次数
        private double count_fa = 0;//失败次数
        private int maxScore = 0;

        private void main()
        {
            if (UserConfig_Preset != null)
            {
                Sleep(1500);
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入阵容中的式神录，开始应用预设{UserConfig_Preset}");
                Fast.Click("827,304,840,338");
                Sleep(1500);
                Fast.Click("397,95,418,114");
                Sleep(1500);
                Tmp.Do_Preset(preset);
                log.Info("退出阵容中的式神录，继续斗技...");
                Sleep(1000);
                Fast.Click("29,18,65,48");
                Sleep(1500);
            }

        Re:
            WaitMainScene();
            if (maxScore == 0) maxScore = GetDouji_MaxScore();//获取最大积分
            log.Info("点击战斗..");
            Fast.Click(1164, 583, 1230, 641);//点击战斗
            Sleep(1000);
            //防封等待
            Anti.RandomDelay();
            if (Db.PendingTimerTask) //执行定时任务
            {
                Db.PendingTimerTask = false;
                log.Info("暂停当前任务，执行定时任务，退出到探索..");
                Exit();
                Db?.TimerTask?.DoAllTask();
                Sleep(1000);
                throw new Exception("定时任务执行结束，重新执行当前的主任务..");
            }

            Stopwatch sw = new();
            sw.Start();
            if (Combat())
            {//胜利
                count_su++;
                log.Info_Green("战斗胜利，战斗用时：" + sw.Elapsed.TotalSeconds.ToString("0.00") + "秒，已胜利次数：" + count_su);
            }
            else
            {//失败
                count_fa++;
                log.Warn("战斗失败，战斗用时：" + sw.Elapsed.TotalSeconds.ToString("0.00") + "秒，已胜利次数：" + count_su + "，斗技任务通用设置中超过失败次数暂停的功能因任务设定无法使用！");
            }

            sw.Stop();

            //汇报当前胜率
            double sl = count_su / (count_fa + count_su);
            int score = WaitMainScene();
            int douji_score = GetDouji_Score(true);
            log.Info($"当前斗技胜率：{sl * 100.0:0.00}%，斗技积分：{score}/{maxScore}，段位积分：{douji_score}");

            if (count_su >= _config.Ncount)
            {
                log.Info_Green($"胜利次数达到设定次数 {count_su}次，结束任务.");
                Exit();
                return;
            }
            if (_config.ManTing && score == maxScore)
            {
                log.Info_Green($"检测到斗技积分{score}已经打满，结束任务.");
                Exit();
                return;
            }
            if (_config.MingShi && douji_score >= 3000)
            {
                log.Info_Green($"检测到段位积分{douji_score}≥3000分，结束任务.");
                Exit();
                return;
            }
            goto Re;
        }

        /// <summary>
        /// 任务退出
        /// </summary>
        private void Exit()
        {
            log.Info("斗技任务结束，退出场景..点击返回町中");
            Operational.Click(30, 14, 62, 45);
            Sleep(1500);
            log.Info("点击返回庭院");
            Operational.Click(1027, 233, 1080, 299);
            Sleep(1500);
        }

        private bool BiaoJi_Status = false;

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            int countt = 0;
            var bj_pics = DynamicData.FilterPicsClass("Sub").Filter("绿标");
            bj_pics.SetAllXsoft(Dm);
            if (_config.BiaoJi && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.1秒，标记位置：" + _config.BiaoJi_Str);
                Sleep(100, true);
            ReBiao:
                biaoJiPoss[_config.BiaoJi_Str].Click();
                Sleep(200, true);
                if (!bj_pics.FindAll())
                {
                    //重新识别一次
                    Sleep(100, true);
                    if (!bj_pics.FindAll())
                    {
                        Sleep(150, true);
                        if (countt >= 1)
                        {
                            log.Warn("始终没有找到标记，本次标记失败...");
                            return false;
                        }
                        countt++;
                        goto ReBiao;
                    }
                }
                else
                {
                    log.Info("标记完成！");
                }
            }
            //if (_config.BiaoJi && !BiaoJi_Status && name.Contains("标记"))
            //{
            //    //点击标记位置
            //    BiaoJi_Status = true;
            //    log.Info("等待0.2秒，标记位置：" + _config.BiaoJi_Str);
            //    Sleep(200);
            //    biaoJiPoss[_config.BiaoJi_Str].Click();
            //    //Fast.Click("984,434,1022,500");
            //    return false;
            //}
            return true;
        }

        private List<string> DontSendLog = ["标记"];

        /// <summary>
        /// 斗技
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            //点击开始
            var pics = Mp.Filter("斗技");
            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                FindOkFun(p.Name, p);
                if (!DontSendLog.Any(p.Name.Contains)) log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("胜利"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败"))
                {
                    ret_bol = false;
                    isbreak = true;
                }
            }
            //if (ret_bol)
            //    Combat_End();

            return ret_bol;
        }

        /// <summary>
        /// 获取斗技积分
        /// </summary>
        private int GetDouji_Score(bool real = false)
        {
            string res = "";
            if (real)
                res = Fast.Ocr_Local(704, 500, 809, 542);
            else
                res = Fast.Ocr_Local(701, 558, 829, 589);

            int ret;
            try
            {
                ret = int.Parse(res.Replace(" ", null).Split('/')[0]);
            }
            catch (Exception)
            {
                return 0;
            }
            return ret;
        }

        /// <summary>
        /// 获取斗技积分上限
        /// </summary>
        private int GetDouji_MaxScore()
        {
            //WaitMainScene();
            var res = Fast.Ocr_Local(701, 558, 829, 589);
            //770/3200
            int ret;
            try
            {
                ret = int.Parse(res.Replace(" ", null).Split('/')[1]);
            }
            catch (Exception)
            {
                return 0;
            }
            return ret;
        }

        private string GetDouji_DuanWei()
        {
            var res = Fast.Ocr_String(579, 412, 690, 467);
            return res;
        }

        /// <summary>
        /// 等待主场景
        /// </summary>
        private int WaitMainScene()
        {
            log.Info("等待主场景..");
            while (Scene.NowScene != "斗技")
            {
                Sleep(3000);
                if (Scene.NowIsScene("斗技"))
                {
                    Sleep(500);
                    return GetDouji_Score();
                }
                Fast.Click(414, 687, 561, 709);
            }
            return GetDouji_Score();
        }
    }

    /// <summary>
    /// 斗技任务参数
    /// </summary>
    internal class DouJiConfig : BaseTaskConfig
    {
        public DouJiConfig(TaskConfigsModel.Configs configs)
        {
            Ncount = configs.Count;
            BiaoJi_Str = configs.Others.TryGetValue("Biaoji", out string? value) ? value : "不标记";
            if (!new List<string>() { "位置1", "位置2", "位置3", "位置4", "位置5" }.Contains(BiaoJi_Str))
            {
                BiaoJi_Str = "不标记";
                BiaoJi = false;
            }
            else
                BiaoJi = true;

            ManTing = bool.Parse(configs.Others.TryGetValue("ManTing", out value) ? value : "False");
            MingShi = bool.Parse(configs.Others.TryGetValue("MingShi", out value) ? value : "False");
        }

        public string BiaoJi_Str { get; set; }

        public bool ManTing { get; set; }
        public bool MingShi { get; set; }
    }
}