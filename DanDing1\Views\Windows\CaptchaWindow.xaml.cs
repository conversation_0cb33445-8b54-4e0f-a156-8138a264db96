using System;
using System.IO;
using System.Windows;
using System.Windows.Media.Imaging;
using XHelper;

namespace DanDing1.Views
{
    public partial class CaptchaWindow : Window
    {
        private readonly EasyCaptcha _captcha;
        private bool _isVerified;
        private int _retryCount;
        private const int MaxRetries = 3;

        public bool IsVerified => _isVerified;

        public CaptchaWindow()
        {
            InitializeComponent();
            _captcha = new EasyCaptcha();
            _retryCount = 0;
            RefreshCaptcha();
        }

        private void RefreshCaptcha()
        {
            _captcha.RenderCaptcha(); // 重新生成验证码

            if (!_captcha.IsReady())
            {
                MessageBox.Show("验证码生成失败，请重试", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            var stream = _captcha.GetImgMemoryStream();
            var bitmap = new BitmapImage();
            bitmap.BeginInit();
            bitmap.CacheOption = BitmapCacheOption.OnLoad;

            stream.Seek(0, SeekOrigin.Begin);
            bitmap.StreamSource = stream;

            bitmap.EndInit();

            CaptchaImage.Source = bitmap;
            CaptchaInput.Text = string.Empty;
        }

        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(CaptchaInput.Text))
            {
                MessageBox.Show("请输入验证码", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (_captcha.CheckCaptcha(CaptchaInput.Text.ToLower()))
            {
                _isVerified = true;
                DialogResult = true;
                Close();
            }
            else
            {
                _retryCount++;
                if (_retryCount >= MaxRetries)
                {
                    MessageBox.Show("验证失败次数过多，请稍后重试", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    DialogResult = false;
                    Close();
                }
                else
                {
                    MessageBox.Show($"验证码错误，还有{MaxRetries - _retryCount}次机会", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    RefreshCaptcha();
                }
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            RefreshCaptcha();
        }
    }
}