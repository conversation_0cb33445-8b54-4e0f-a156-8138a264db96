﻿using Lazy.Captcha.Core;
using SkiaSharp;

namespace XHelper
{
    /// <summary>
    /// 随机验证码
    /// </summary>
    public class EasyCaptcha
    {
        private CaptchaService captchaService { get; set; }
        private string captchaId = string.Empty;
        private CaptchaData captchaData { get; set; }
        private MemoryStream ImgStream;
        private byte[] currentCaptchaBytes;
        private string Code { get; set; }

        /// <summary>
        /// 验证码默认高度35 宽度98
        /// </summary>
#pragma warning disable CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑添加 "required" 修饰符或声明为可为 null。

        public EasyCaptcha()
#pragma warning restore CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑添加 "required" 修饰符或声明为可为 null。
        {
            captchaService = GenerateCaptchaService();
            RenderCaptcha();
        }

        public void RenderCaptcha()
        {
            // 释放之前的流
            ImgStream?.Dispose();

            // 第一次比较慢，之后会很快
            captchaId = Guid.NewGuid().ToString();
            captchaData = captchaService.Generate(captchaId, 10);

            // 使用字节数组创建新的MemoryStream
            currentCaptchaBytes = captchaData.Bytes;
            ImgStream = new MemoryStream(currentCaptchaBytes);
            Code = captchaData.Code;
        }

        /// <summary>
        /// 获取验证码图片流
        /// </summary>
        /// <returns></returns>
        public MemoryStream GetImgMemoryStream() => ImgStream;

        /// <summary>
        /// 获取当前验证码图片的字节数组
        /// </summary>
        /// <returns></returns>
        public byte[] GetCurrentCaptchaBytes() => currentCaptchaBytes;

        /// <summary>
        /// 是否准备就绪
        /// </summary>
        /// <returns></returns>
        public bool IsReady()
        {
            if (string.IsNullOrEmpty(Code) || currentCaptchaBytes is null || ImgStream is null || captchaData is null)
                return false;
            return true;
        }

        /// <summary>
        /// 验证验证码
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public bool CheckCaptcha(string code)
        {
            if (!IsReady())
            {
                XLogger.Error("验证码验证失败，验证码模块发生异常！");
                throw new Exception("验证码验证失败，验证码模块发生异常！");
            }

            if (string.IsNullOrEmpty(captchaId))
                return false;

            return Code.ToLower() == code;
        }

        private CaptchaService GenerateCaptchaService()
        {
            return CaptchaServiceBuilder
               .New()
               .CodeLength(4)
               .CaptchaType(0)
               .FontFamily(SKTypeface.Default)
               .FontSize(26)
               .BubbleCount(3)
               .BubbleThickness(1)
               .BubbleMinRadius(3)
               .BubbleMaxRadius(8)
               .InterferenceLineCount(4)
               .Animation(false)
               .FrameDelay(300)
               .Width(98)
               .Height(35)
               .Quality(90)
               .TextBold(true)
               .Build();
        }
    }
}