using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DanDing1.Models.Super;
using System.Windows.Threading;
using Wpf.Ui.Controls;

namespace DanDing1.Views.UserControls
{
    public partial class MultiGamePreviewControl : UserControl
    {
        private Dictionary<int, SingleGamePreviewItem> _previewItems = new Dictionary<int, SingleGamePreviewItem>();
        private bool _initializing = false;

        // 默认预览项目基准尺寸
        private const double DEFAULT_ASPECT_RATIO = 1288.0 / 900;

        private const double MAX_WIDTH_PERCENT = 0.8; // 最大宽度为窗口右侧区域的80%
        private double _currentScaleFactor = 0.75; // 默认为75%

        public MultiGamePreviewControl()
        {
            InitializeComponent();
            this.Loaded += MultiGamePreviewControl_Loaded;
            this.Unloaded += MultiGamePreviewControl_Unloaded;
            this.SizeChanged += MultiGamePreviewControl_SizeChanged;
        }

        private void MultiGamePreviewControl_Loaded(object sender, RoutedEventArgs e)
        {
            // 移除对ToggleSwitch事件的处理

            // 设置默认滑动条值
            SizeSlider.Value = _currentScaleFactor * 100;

            // 设置默认布局方向
            LayoutModeToggle.IsChecked = PreviewPanel.Orientation == Orientation.Horizontal;

            // 首次加载时初始化大小
            Dispatcher.BeginInvoke(new Action(() =>
            {
                UpdateAllPreviewSizes();
            }), DispatcherPriority.Loaded);
        }

        private void MultiGamePreviewControl_Unloaded(object sender, RoutedEventArgs e)
        {
            StopAllPreviews();
        }

        private void MultiGamePreviewControl_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            // 当控件大小变化时，更新所有预览
            if (!_initializing && _previewItems.Count > 0)
            {
                // 使用延迟执行确保布局已更新
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    UpdateAllPreviewSizes(); // 重新计算大小

                    foreach (var item in _previewItems.Values)
                    {
                        if (item.IsPreviewActive)
                        {
                            // 重新启动预览以更新位置
                            item.RestartPreview();
                        }
                    }
                }), DispatcherPriority.Render);
            }
        }

        private void PreviewScrollViewer_ScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            // 当滚动发生时，更新所有预览位置
            if (!_initializing && _previewItems.Count > 0)
            {
                // 使用延迟执行确保布局已更新
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    foreach (var item in _previewItems.Values)
                    {
                        if (item.IsPreviewActive)
                        {
                            // 只更新位置而不重启预览，提高性能
                            item.UpdatePosition();
                        }
                    }
                }), DispatcherPriority.Render);
            }
        }

        public void SetGames(IEnumerable<SuperMultiGame_DataModel> games)
        {
            if (games == null) return;

            _initializing = true;

            // 清除当前所有预览
            StopAllPreviews();
            _previewItems.Clear();
            PreviewPanel.Children.Clear();

            foreach (var game in games)
            {
                if (game != null)
                {
                    AddGame(game);
                }
            }

            _initializing = false;

            // 更新SyncAllButton的状态
            UpdateSyncAllButtonState();
        }

        public void AddGame(SuperMultiGame_DataModel game)
        {
            if (game == null || _previewItems.ContainsKey(game.GameId)) return;

            var item = new SingleGamePreviewItem
            {
                Game = game,
                UseSimulatorHandle = true // 默认使用模拟器句柄
            };

            // 设置新添加项目的大小
            UpdatePreviewItemSize(item);

            item.PreviewStatusChanged += Item_PreviewStatusChanged;
            _previewItems.Add(game.GameId, item);
            PreviewPanel.Children.Add(item);

            // 更新SyncAllButton的状态
            UpdateSyncAllButtonState();
        }

        public void RemoveGame(int gameId)
        {
            if (_previewItems.TryGetValue(gameId, out var item))
            {
                item.StopPreview();
                item.PreviewStatusChanged -= Item_PreviewStatusChanged;
                PreviewPanel.Children.Remove(item);
                _previewItems.Remove(gameId);

                // 更新SyncAllButton的状态
                UpdateSyncAllButtonState();
            }
        }

        private void Item_PreviewStatusChanged(object sender, bool isActive)
        {
            // 当任何一个预览项状态改变时，更新全局按钮状态
            UpdateSyncAllButtonText();
        }

        public void StartAllPreviews()
        {
            foreach (var item in _previewItems.Values)
            {
                item.StartPreview();
            }

            // 更新全局按钮文本
            UpdateSyncAllButtonText();
        }

        public void StopAllPreviews()
        {
            foreach (var item in _previewItems.Values)
            {
                item.StopPreview();
            }

            // 更新全局按钮文本
            UpdateSyncAllButtonText();
        }

        // 更新SyncAllButton的启用状态
        public void UpdateSyncAllButtonState()
        {
            SyncAllButton.IsEnabled = _previewItems.Count > 0;
            UpdateSyncAllButtonText();
        }

        // 更新SyncAllButton的文本
        private void UpdateSyncAllButtonText()
        {
            if (_previewItems.Count == 0)
            {
                SyncAllButton.Content = "同步全部预览";
                return;
            }

            // 检查是否所有模拟器都处于同步状态
            bool allSynced = _previewItems.Values.All(item => item.IsPreviewActive);

            // 如果所有模拟器都同步，显示"停止全部预览"；否则显示"同步全部预览"
            SyncAllButton.Content = allSynced ? "停止全部预览" : "同步全部预览";
        }

        // 修改后的"同步右侧游戏列表"按钮点击事件，仅添加新的模拟器
        private void SyncGameListButton_Click(object sender, RoutedEventArgs e)
        {
            // 从SuperMultiGamesWindow获取游戏列表
            var parentWindow = Window.GetWindow(this) as DanDing1.Views.Windows.SuperMultiGamesWindow;
            if (parentWindow == null || parentWindow.ViewModel == null) return;

            // 获取游戏列表
            var gamesList = parentWindow.ViewModel.SuperMultiGame_DataModelCollection;
            if (gamesList == null) return;

            // 仅添加不在当前预览区域的模拟器
            foreach (var game in gamesList)
            {
                if (game != null && !_previewItems.ContainsKey(game.GameId))
                {
                    AddGame(game);
                }
            }
        }

        private void SyncAllButton_Click(object sender, RoutedEventArgs e)
        {
            // 根据按钮内容决定行为
            if (SyncAllButton.Content.ToString() == "停止全部预览")
            {
                // 停止所有预览
                StopAllPreviews();
            }
            else
            {
                // 当按钮显示"同步全部预览"时，只启动当前未同步的模拟器
                foreach (var item in _previewItems.Values)
                {
                    if (!item.IsPreviewActive)
                    {
                        item.StartPreview();
                    }
                }

                // 更新按钮文本
                UpdateSyncAllButtonText();
            }
        }

        // 更新某个游戏信息
        public void UpdateGame(SuperMultiGame_DataModel game)
        {
            if (game == null) return;

            if (_previewItems.TryGetValue(game.GameId, out var item))
            {
                // 重新设置Game对象，这将触发LastLogMessage的更新
                item.Game = game;

                // 如果模拟器句柄或游戏句柄变化，且正在预览，则需要重启预览
                bool shouldRestart = item.IsPreviewActive && (
                    (item.UseSimulatorHandle && item.Game.MumuHandle != game.MumuHandle) ||
                    (!item.UseSimulatorHandle && item.Game.GameHandle != game.GameHandle));

                if (shouldRestart)
                {
                    item.RestartPreview();
                }
            }
            else
            {
                // 如果不存在，添加新的
                AddGame(game);
            }
        }

        // 刷新所有预览项的句柄
        public void RefreshAllHandles()
        {
            foreach (var item in _previewItems.Values)
            {
                if (item.Game != null)
                {
                    // 确保使用最新的句柄
                    bool wasActive = item.IsPreviewActive;

                    // 更新游戏信息，这会更新句柄
                    item.Game = item.Game;

                    // 如果预览处于活跃状态，则重新启动预览以使用新句柄
                    if (wasActive)
                    {
                        item.RestartPreview();
                    }
                }
            }
        }

        // 处理预览大小滑动条值变化
        private void SizeSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (_initializing) return;

            _currentScaleFactor = e.NewValue / 100.0;
            UpdateAllPreviewSizes();
        }

        // 更新所有预览项的大小
        private void UpdateAllPreviewSizes()
        {
            foreach (var item in _previewItems.Values)
            {
                UpdatePreviewItemSize(item);
            }

            // 使用延迟执行确保布局已更新后重新启动预览
            Dispatcher.BeginInvoke(new Action(() =>
            {
                foreach (var item in _previewItems.Values)
                {
                    if (item.IsPreviewActive)
                    {
                        item.RestartPreview();
                    }
                }
            }), DispatcherPriority.Render);
        }

        // 更新单个预览项的大小
        private void UpdatePreviewItemSize(SingleGamePreviewItem item)
        {
            // 获取当前预览区域宽度
            double availableWidth = PreviewScrollViewer.ActualWidth;

            // 计算基于窗口宽度的最大宽度（80%限制）
            double maxWidth = availableWidth * MAX_WIDTH_PERCENT;

            // 确保最小宽度不小于150
            double minWidth = Math.Min(150, maxWidth * 0.5);

            // 根据滑动条比例计算宽度
            double newWidth = Math.Max(minWidth, Math.Min(maxWidth, maxWidth * _currentScaleFactor));

            // 根据16:9比例计算高度
            double newHeight = newWidth / DEFAULT_ASPECT_RATIO;

            item.Width = newWidth;
            item.Height = newHeight;
        }

        // 处理布局模式切换
        private void LayoutModeToggle_Changed(object sender, RoutedEventArgs e)
        {
            bool isHorizontal = LayoutModeToggle.IsChecked ?? true;
            PreviewPanel.Orientation = isHorizontal ? Orientation.Horizontal : Orientation.Vertical;

            // 布局更改后更新预览
            Dispatcher.BeginInvoke(new Action(() =>
            {
                foreach (var item in _previewItems.Values)
                {
                    if (item.IsPreviewActive)
                    {
                        // 仅更新位置而不重启预览
                        item.UpdatePosition();
                    }
                }
            }), DispatcherPriority.Render);
        }
    }
}