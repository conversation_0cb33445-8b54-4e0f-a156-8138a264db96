{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DanDing1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{9BC56462-F4C1-4975-B98C-ACCBA287C5B3}|DanDing1.csproj|c:\\users\\<USER>\\source\\repos\\danding1subproject\\danding1\\services\\schedulerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9BC56462-F4C1-4975-B98C-ACCBA287C5B3}|DanDing1.csproj|solutionrelative:services\\schedulerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9BC56462-F4C1-4975-B98C-ACCBA287C5B3}|DanDing1.csproj|c:\\users\\<USER>\\source\\repos\\danding1subproject\\danding1\\views\\windows\\taskhistorywindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{9BC56462-F4C1-4975-B98C-ACCBA287C5B3}|DanDing1.csproj|solutionrelative:views\\windows\\taskhistorywindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{5a4e9529-b6a0-46b5-be4f-0f0b239bc0eb}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "SchedulerService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DanDing1\\Services\\SchedulerService.cs", "RelativeDocumentMoniker": "Services\\SchedulerService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DanDing1\\Services\\SchedulerService.cs", "RelativeToolTip": "Services\\SchedulerService.cs", "ViewState": "AgIAAOIBAAAAAAAAAAAAwPsBAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-05T12:45:36.162Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "TaskHistoryWindow.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DanDing1\\Views\\Windows\\TaskHistoryWindow.xaml", "RelativeDocumentMoniker": "Views\\Windows\\TaskHistoryWindow.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\DanDing1SubProject\\DanDing1\\Views\\Windows\\TaskHistoryWindow.xaml", "RelativeToolTip": "Views\\Windows\\TaskHistoryWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-04T14:03:01.19Z", "EditorCaption": ""}]}]}]}