﻿#pragma checksum "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8CF06ADAF7488055D9BB10ACED4FE9733F7A324E"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Views.Windows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Windows {
    
    
    /// <summary>
    /// CloudConfigWindow
    /// </summary>
    public partial class CloudConfigWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 46 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchBox;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ConfigsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingGrid;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid NoDataGrid;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PageInfoText;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrevButton;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/windows/cloudconfig/cloudconfigwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SearchBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 51 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
            this.SearchBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.SearchBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 55 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ConfigsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 75 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
            this.ConfigsDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.ConfigsDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 4:
            this.LoadingGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 5:
            this.NoDataGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 6:
            this.PageInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.PrevButton = ((System.Windows.Controls.Button)(target));
            
            #line 189 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
            this.PrevButton.Click += new System.Windows.RoutedEventHandler(this.PrevButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.NextButton = ((System.Windows.Controls.Button)(target));
            
            #line 194 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\CloudConfigWindow.xaml"
            this.NextButton.Click += new System.Windows.RoutedEventHandler(this.NextButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

