<Window x:Class="DanDing1.Views.Windows.MySharesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        Title="我分享的配置"
        Height="650"
        Width="1255"
        d:DataContext="{d:DesignInstance local:MySharesWindow,
        IsDesignTimeCreatable=True}"
        ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
        ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        WindowStartupLocation="CenterScreen"
        mc:Ignorable="d">
    <Window.Resources>
        <local:StatusToVisibilityConverter x:Key="StatusToVisibilityConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 头部区域：标题和筛选选项 -->
        <Grid Grid.Row="0" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <StackPanel>
                <TextBlock Text="我分享的配置"
                           FontSize="24"
                           FontWeight="Bold"
                           Margin="0,0,0,5" />
                <TextBlock Text="查看和管理您分享的所有配置方案"
                           Opacity="0.7"
                           FontSize="14" />
            </StackPanel>

            <StackPanel Grid.Column="1"
                        Orientation="Horizontal"
                        VerticalAlignment="Center">
                <ComboBox x:Name="StatusFilterComboBox"
                          Width="120"
                          Height="35"
                          Margin="0,0,10,0"
                          SelectionChanged="StatusFilterComboBox_SelectionChanged">
                    <ComboBoxItem Content="全部状态" Tag="" IsSelected="True"/>
                    <ComboBoxItem Content="活跃" Tag="active"/>
                    <ComboBoxItem Content="已过期" Tag="expired"/>
                    <ComboBoxItem Content="已删除" Tag="deleted"/>
                </ComboBox>
                <ComboBox x:Name="SortByComboBox"
                          Width="120"
                          Height="35"
                          Margin="0,0,10,0"
                          SelectionChanged="SortByComboBox_SelectionChanged">
                    <ComboBoxItem Content="创建时间排序" Tag="create_time" IsSelected="True"/>
                    <ComboBoxItem Content="过期时间排序" Tag="expires_at"/>
                </ComboBox>
                <ComboBox x:Name="OrderComboBox"
                          Width="120"
                          Height="35"
                          Margin="0,0,10,0"
                          SelectionChanged="OrderComboBox_SelectionChanged">
                    <ComboBoxItem Content="降序" Tag="desc" IsSelected="True"/>
                    <ComboBoxItem Content="升序" Tag="asc"/>
                </ComboBox>
                <Button x:Name="RefreshButton"
                        Content="刷新"
                        Height="35"
                        Width="80"
                        Click="RefreshButton_Click" />
            </StackPanel>
        </Grid>

        <!-- 内容区域：配置列表 -->
        <Border Grid.Row="1"
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                CornerRadius="8"
                Padding="0">
            <Grid>
                <DataGrid x:Name="SharesDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="None"
                          HeadersVisibility="Column"
                          BorderThickness="0"
                          IsReadOnly="True"
                          Background="Transparent"
                          RowHeight="50">
                    <DataGrid.Resources>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="Padding" Value="15,10" />
                            <Setter Property="FontWeight" Value="SemiBold" />
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="共享码"
                                            Binding="{Binding ShareCode}"
                                            Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Margin" Value="15,0" />
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="版本"
                                            Binding="{Binding Version}"
                                            Width="75">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="配置的名字"
                                            Binding="{Binding ConfigName}"
                                            Width="*">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="TextTrimming" Value="CharacterEllipsis" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="状态"
                                            Binding="{Binding Status}"
                                            Width="65">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Status}" Value="active">
                                            <Setter Property="Foreground" Value="#4CAF50" />
                                            <Setter Property="Text" Value="活跃" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Status}" Value="expired">
                                            <Setter Property="Foreground" Value="#FF9800" />
                                            <Setter Property="Text" Value="已过期" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Status}" Value="deleted">
                                            <Setter Property="Foreground" Value="#F44336" />
                                            <Setter Property="Text" Value="已删除" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="创建时间"
                                            Binding="{Binding CreatedAt}"
                                            Width="200">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="过期时间"
                                            Binding="{Binding ExpiresAt}"
                                            Width="200">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding ExpiresAt}" Value="{x:Null}">
                                            <Setter Property="Text" Value="永不过期" />
                                            <Setter Property="Foreground" Value="#2196F3" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="使用次数"
                                            Binding="{Binding GetCount}"
                                            Width="90">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTemplateColumn Header="操作" Width="235">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" Margin="5"
                                                Visibility="{Binding Status, Converter={StaticResource StatusToVisibilityConverter}, ConverterParameter=active}">
                                        <!-- 复制按钮 -->
                                        <Button Content="复制码"
                                                Width="68"
                                                Margin="0,0,5,0"
                                                Click="CopyCodeButton_Click"
                                                Tag="{Binding ShareCode}"/>

                                        <!-- 设置过期按钮 -->
                                        <Button Content="设为过期"
                                                Width="80"
                                                Click="ExpireNowButton_Click"
                                                Tag="{Binding ShareCode}"/>

                                        <!-- 删除按钮 -->
                                        <Button Content="删除"
                                                Width="55"
                                                Margin="5,0,0,0"
                                                Background="#E53935"
                                                Foreground="White"
                                                Click="DeleteButton_Click"
                                                Tag="{Binding ShareCode}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 加载指示器 -->
                <Grid x:Name="LoadingGrid"
                      Visibility="Collapsed"
                      Background="{DynamicResource ControlFillColorDefaultBrush}">
                    <StackPanel VerticalAlignment="Center"
                                HorizontalAlignment="Center">
                        <ui:ProgressRing IsIndeterminate="True"
                                         Width="40"
                                         Height="40"
                                         Margin="0,0,0,10" />
                        <TextBlock Text="加载中..."
                                   HorizontalAlignment="Center" />
                    </StackPanel>
                </Grid>

                <!-- 无数据提示 -->
                <Grid x:Name="NoDataGrid"
                      Visibility="Collapsed"
                      Background="{DynamicResource ControlFillColorDefaultBrush}">
                    <TextBlock Text="您还没有分享过任何配置"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Center"
                               FontSize="16"
                               Opacity="0.5" />
                </Grid>
            </Grid>
        </Border>

        <!-- 底部区域：分页控制 -->
        <Grid Grid.Row="2" Margin="0,15,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!-- 页码信息 -->
            <TextBlock x:Name="PageInfoText"
                       Grid.Column="0"
                       VerticalAlignment="Center"
                       Text="第1页/共1页" />

            <!-- 分页导航 -->
            <StackPanel Grid.Column="2"
                        Orientation="Horizontal"
                        HorizontalAlignment="Right">
                <Button x:Name="PrevButton"
                        Content="上一页"
                        Width="80"
                        Height="32"
                        Margin="0,0,10,0"
                        Click="PrevButton_Click" />
                <Button x:Name="NextButton"
                        Content="下一页"
                        Width="80"
                        Height="32"
                        Click="NextButton_Click" />
            </StackPanel>
        </Grid>
    </Grid>
</Window>