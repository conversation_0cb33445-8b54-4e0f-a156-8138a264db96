﻿using DanDing1.Models;
using DanDing1.Helpers;
using DanDing1.Services.Notification;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Wpf.Ui.Controls;
using XHelper;
using Wpf.Ui.Abstractions.Controls;
using Microsoft.Extensions.DependencyInjection;

namespace DanDing1.ViewModels.Pages
{
    public partial class UserConfigViewModel : ObservableObject, INavigationAware
    {
        [ObservableProperty]
        private UserConfigModel _userConfig;

        [ObservableProperty]
        private string _enabledNoticeTypes = "无";

        [ObservableProperty]
        private List<string> _Tpo_Biaojis = ["不标记", "位置1", "位置2", "位置3", "位置4", "位置5"];

        [ObservableProperty]
        private List<string> _custClick_Damos = ["关闭", "位置1", "位置2", "位置3", "位置4"];

        [ObservableProperty]
        private List<string> _custClick_Results = ["关闭", "位置1", "位置2", "位置3", "位置4", "位置5"];

        [ObservableProperty]
        private List<string> _kaPing_DoLists = ["结束任务", "重启游戏"];

        /// <summary>
        /// 御魂满后的操作列表
        /// </summary>
        [ObservableProperty]
        private List<string> _fullYuHun_DoLists = ["结束任务", "重启清理御魂"];

        [ObservableProperty]
        private string _Notice_Ntfy_Key;

        // 标记是否已经从云端加载过配置
        private bool _hasLoadedFromCloud = false;

        // 记录上次检查时的登录状态，用于检测状态变化
        private bool _lastLoginState = false;

        public async Task OnNavigatedFromAsync()
        {
            // 保存用户配置
            bool dontSave = UserConfig.Equals(GlobalData.Instance.UserConfig);
            if (!dontSave)
            {
                // 更新本地配置
                GlobalData.Instance.UserConfig = (UserConfigModel)UserConfig.Clone();

                // 在尝试上传到云端之前，明确检查用户是否已登录且不是试用用户
                if (GlobalData.Instance.appConfig.IsLogin && !GlobalData.Instance.appConfig.IsFree && _hasLoadedFromCloud)
                {
                    // 上传保存配置
                    string? v = await GlobalData.Instance.appConfig.dNet.User.SaveUserConfigAsync(JsonSerializer.Serialize(UserConfig));
                    if (!string.IsNullOrEmpty(v))
                        XLogger.Info($"用户云端配置保存成功，{v}");
                    else
                        XLogger.Info($"保存云端配置失败！请检查网络！");
                }
                else if (!GlobalData.Instance.appConfig.IsLogin)
                {
                    XLogger.Info("用户未登录，配置未上传到云端。");
                }
                else if (GlobalData.Instance.appConfig.IsFree)
                {
                    XLogger.Warn("试用状态的用户无法进行云端配置的保存！");
                }
                else if (!_hasLoadedFromCloud)
                {
                    XLogger.Warn("未从云端加载过配置，为防止覆盖云端数据，不上传配置！");
                }
            }
        }

        public async Task OnNavigatedToAsync()
        {
            // 检查登录状态是否发生变化
            bool currentLoginState = GlobalData.Instance.appConfig.IsLogin;
            bool loginStateChanged = currentLoginState != _lastLoginState;
            _lastLoginState = currentLoginState;

            // 如果用户登录状态发生变化且当前已登录，尝试重新加载云端配置
            if (loginStateChanged && currentLoginState)
            {
                XLogger.Info("检测到用户登录状态变化，尝试重新加载云端配置。");
                await LoadConfigFromCloud();
            }
            // 如果用户已登录但尚未从云端加载过配置，尝试加载
            else if (currentLoginState && !_hasLoadedFromCloud)
            {
                XLogger.Info("用户已登录但尚未从云端加载配置，尝试加载。");
                await LoadConfigFromCloud();
            }

            // 无论如何，都从 GlobalData 加载当前配置
            LoadUserConfigFromGlobal();
            return;
        }

        /// <summary>
        /// 尝试从云端加载用户配置
        /// </summary>
        private async Task LoadConfigFromCloud()
        {
            // 如果用户已登录且不是试用用户，尝试从云端加载配置
            if (GlobalData.Instance.appConfig.IsLogin && !GlobalData.Instance.appConfig.IsFree)
            {
                XLogger.Info("尝试从云端加载配置...");
                try
                {
                    // 从GetUserInfoAsync获取用户配置
                    var user = await GlobalData.Instance.appConfig.dNet.User.GetUserInfoAsync();
                    if (user != null && user.IsSuccess && user.Data?.config != null)
                    {
                        var cloudConfig = XSerializer.DeserializeJsonTxtToObject<UserConfigModel>(user.Data.config.ToString());
                        if (cloudConfig != null)
                        {
                            GlobalData.Instance.UserConfig = cloudConfig; // 更新全局配置
                            _hasLoadedFromCloud = true;
                            XLogger.Info("云端配置加载成功。");

                            // 获取Ntfy接口
                            var rep = await GlobalData.Instance.appConfig.dNet.User.GetNtfyKeyAsync();
                            if (rep?.IsSuccess ?? false)
                            {
                                GlobalData.Instance.appConfig.Ntfy_Key = rep.Data.app_key;
                            }
                        }
                        else
                        {
                            XLogger.Warn("从云端加载的配置数据无效，使用本地配置。");
                        }
                    }
                    else
                    {
                        XLogger.Info("云端没有找到用户配置，使用本地配置。");
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Error($"从云端加载配置失败: {ex.Message}，使用本地配置。");
                }
            }
            else
            {
                XLogger.Info("用户未登录或为试用用户，使用本地配置。");
            }
        }

        private void LoadUserConfigFromGlobal()
        {
            // 从 GlobalData.Instance.UserConfig 克隆最新的配置
            UserConfig = (UserConfigModel)GlobalData.Instance.UserConfig.Clone();
            Notice_Ntfy_Key = GlobalData.Instance.appConfig.Ntfy_Key;

            // 更新通知类型显示
            UpdateEnabledNoticeTypes();
        }

        partial void OnUserConfigChanged(UserConfigModel value)
        {
            if (value != null)
            {
                UpdateEnabledNoticeTypes();

                // 移除旧的事件处理器（如果有）
                if (value != null)
                {
                    value.PropertyChanged -= UserConfig_PropertyChanged;
                    value.PropertyChanged += UserConfig_PropertyChanged;
                }
            }
        }

        private void UserConfig_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName?.StartsWith("Notice_") == true)
            {
                UpdateEnabledNoticeTypes();
            }
        }

        private void UpdateEnabledNoticeTypes()
        {
            if (UserConfig == null)
                return;

            List<string> enabledTypes = new List<string>();

            if (UserConfig.Notice_WxPush)
                enabledTypes.Add("微信推送");

            if (UserConfig.Notice_Ntfy)
                enabledTypes.Add("App通知");

            if (UserConfig.Notice_Pushplus)
                enabledTypes.Add("Pushplus推送");

            if (UserConfig.Notice_Miaotixing)
                enabledTypes.Add("喵提醒");

            if (enabledTypes.Count > 0)
            {
                EnabledNoticeTypes = "已选自定义通知方式：" + string.Join(", ", enabledTypes);
            }
            else
            {
                EnabledNoticeTypes = "当前没有选择任何自定义通知方式";
            }
        }

        /// <summary>
        /// 测试通知功能
        /// </summary>
        /// <param name="noticeType">要测试的通知类型</param>
        /// <returns>发送是否成功</returns>
        public async Task<bool> TestNotificationAsync(string noticeType)
        {
            try
            {
                // 直接创建通知发送器工厂
                var senderFactory = new NotificationSenderFactory(GlobalData.Instance.appConfig.dNet.User);

                // 创建通知服务
                var notificationService = new TaskNotificationService(senderFactory);

                // 构建测试标题和内容
                string title = "蛋定助手 - 通知测试";
                string content = $"这是一条测试通知，发送时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                // 调用简化的通知发送方法
                bool result = await notificationService.SendSimpleNotificationAsync(noticeType, title, content);

                if (result)
                {
                    XLogger.Info($"[{noticeType}] 测试通知发送成功");
                }
                else
                {
                    XLogger.Warn($"[{noticeType}] 测试通知发送失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                XLogger.Error($"发送测试通知时出错: {ex.Message}");
                return false;
            }
        }
    }
}