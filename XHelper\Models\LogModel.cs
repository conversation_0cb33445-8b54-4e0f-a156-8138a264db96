﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XHelper.Models
{
    public class LogModel
    {
        private string color = "";

        private string text = "";

        private string tip = "";

        public string Color
        {
            get { return color; }
            set { color = value; }
        }

        public string Text
        {
            get { return text; }
            set
            {
                text = DateTime.Now.ToString("HH:mm:ss:fff") + "| " + value;
            }
        }

        public string Tip
        {
            get { return tip; }
            set
            {
                string str = value switch
                {
                    "Info" => "#2E8B57",
                    "Green" => "#008B00",
                    "Warn" => "#ed792c",
                    "Error" => "#e74856",
                    "Debug" => "#284d9e",
                    _ => "#373737",
                };
                Color = str;
                string ttp = value switch
                {
                    "Info" => "提示",
                    "Green" => "提示",
                    "Warn" => "警告",
                    "Error" => "错误",
                    "Debug" => "调试",
                    _ => "普通",
                };
                tip = "[" + ttp + "]";
            }
        }
    }
}