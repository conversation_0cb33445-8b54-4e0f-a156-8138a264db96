using System;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace DanDing1.Helpers
{
    /// <summary>
    /// API请求验证助手
    /// 用于增强API请求的安全性，防止CSRF和其他安全威胁
    /// </summary>
    public static class RequestValidator
    {
        // 用于生成随机值的实例
        private static readonly RandomNumberGenerator RandomGenerator = RandomNumberGenerator.Create();

        /// <summary>
        /// 生成一个安全的随机令牌，用于CSRF保护
        /// </summary>
        /// <param name="length">令牌长度</param>
        /// <returns>随机令牌</returns>
        public static string GenerateCSRFToken(int length = 32)
        {
            byte[] tokenData = new byte[length];
            RandomGenerator.GetBytes(tokenData);
            return Convert.ToBase64String(tokenData);
        }

        /// <summary>
        /// 对API请求添加安全头
        /// </summary>
        /// <param name="request">HTTP请求消息</param>
        /// <param name="token">CSRF令牌</param>
        public static void AddSecurityHeaders(HttpRequestMessage request, string token)
        {
            // 添加CSRF令牌
            request.Headers.Add("X-CSRF-Token", token);

            // 添加其他安全头
            request.Headers.Add("X-Content-Type-Options", "nosniff");
            request.Headers.Add("X-Frame-Options", "DENY");
            request.Headers.Add("X-XSS-Protection", "1; mode=block");
        }

        /// <summary>
        /// 生成请求签名，用于验证请求完整性
        /// </summary>
        /// <param name="payload">请求负载</param>
        /// <param name="secret">密钥</param>
        /// <returns>请求签名</returns>
        public static string GenerateRequestSignature(string payload, string secret)
        {
            using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secret)))
            {
                byte[] hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(payload));
                return Convert.ToBase64String(hash);
            }
        }

        /// <summary>
        /// 验证请求签名是否有效
        /// </summary>
        /// <param name="payload">请求负载</param>
        /// <param name="signature">请求签名</param>
        /// <param name="secret">密钥</param>
        /// <returns>签名是否有效</returns>
        public static bool ValidateRequestSignature(string payload, string signature, string secret)
        {
            string expectedSignature = GenerateRequestSignature(payload, secret);
            return string.Equals(expectedSignature, signature, StringComparison.Ordinal);
        }

        /// <summary>
        /// 验证请求内容是否包含可疑内容，防止注入攻击
        /// </summary>
        /// <param name="content">请求内容</param>
        /// <returns>是否包含可疑内容</returns>
        public static bool ContainsSuspiciousContent(string content)
        {
            if (string.IsNullOrEmpty(content))
                return false;

            return InputValidator.ContainsDangerousCharacters(content);
        }

        /// <summary>
        /// 审计请求，记录敏感操作
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="action">操作类型</param>
        /// <param name="details">操作详情</param>
        /// <param name="ip">IP地址</param>
        public static void AuditRequest(string userId, string action, string details, string ip)
        {
            // 记录敏感操作，例如登录、修改密码等
            // 此处可以接入日志系统，记录关键信息
            XLogger.Info($"安全审计: 用户[{userId}] 操作[{action}] IP[{ip}] 详情[{details}]");
        }

        /// <summary>
        /// 验证请求速率，防止暴力攻击
        /// </summary>
        /// <param name="key">请求标识，如用户ID或IP地址</param>
        /// <param name="maxAttempts">最大尝试次数</param>
        /// <param name="timeWindowSeconds">时间窗口（秒）</param>
        /// <returns>是否允许此请求</returns>
        public static bool IsRequestRateLimited(string key, int maxAttempts, int timeWindowSeconds)
        {
            // 实际应用中，这里应该使用分布式缓存或数据库来存储计数器
            // 为简化示例，此处仅返回false，表示未限制
            // 实际实现应跟踪每个键在时间窗口内的请求次数
            return false;
        }
    }
}
