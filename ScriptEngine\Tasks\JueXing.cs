﻿using DamoControlKit.Model;
using ScriptEngine.Factorys;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;

namespace ScriptEngine.Tasks
{
    internal class JueXing : BaseTask
    {
        private bool Biaoji; //是否标记
        private bool BiaoJi_Status; //标记状态
        private List<string> DontSendLog = ["标记"]; //不发送日志的指令
        private int Ncount; //任务总执行次数
        private string TaskClass; //觉醒类型

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "觉醒");
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Ncount = GetConfig.Count;
            GetConfig.Others.TryGetValue("Biaoji", out string? s);
            try { Biaoji = s is null ? false : bool.Parse(s); } catch (Exception) { Biaoji = false; }
            GetConfig.Others.TryGetValue("Class", out s);
            TaskClass = s is null ? "雷" : s;
            log.Info("开始执行觉醒任务，当前执行次数：" + Ncount + "，是否标记：" + Biaoji + "，觉醒类型：" + TaskClass);
            //场景判断
            string nows = Scene.NowScene;
            if (nows != "觉醒")
            {
                //先去探索，获取突破卷数量
                if (!Scene.TO.TanSuo())
                {
                    log.Warn("觉醒任务无法继续，当前游戏所在场景未知，请调整到庭院或探索主界面开始脚本！");
                    return;
                }
                var tupo_Count = Fast.Scence.TanSuo_GetTuPoCount();
                log.Debug("本地Ocr识别突破卷结果：" + tupo_Count);
                if (tupo_Count == 30)
                    Tmp.Do_Tupo(); //执行临时执行突破

                //再去觉醒
                Scene.TO.JueXing(TaskClass);
            }

            main();
            UserNotificationMessage = $"共战斗{count}/{Ncount}次.";
            if (Db.Data.Get("AutoBuff") is bool autoBuff && autoBuff)
                RunTaskFactory.Run<BuffTask>(Db, Dm, Ct, 1, "自动Buff", new() { { "所有", "0" } });
        }

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            //点击开始
            Fast.Click("1115,600,1199,676");
            log.Info("战斗点击开始");
            var pics = Mp.Filter("觉醒");
            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                FindOkFun(p.Name, p);
                if (!DontSendLog.Any(p.Name.Contains)) log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("胜利") || p.Name.Contains("达摩"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败"))
                {
                    ret_bol = false;
                    isbreak = true;
                }
            }
            if (ret_bol)
                Combat_End();//等待Yuhun界面

            return ret_bol;
        }

        /// <summary>
        /// 觉醒胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("觉醒");
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                if (p.Name.Contains("挑战"))
                {
                    isbreak = true;
                    continue;
                }
                log.Info($"执行点击：{p._Name}");
                p.Click();
            }
        }

        /// <summary>
        /// 结束任务方法
        /// </summary>
        private void EndCallBack()
        {
            log.Info("执行觉醒任务收尾方法,等待返回后,退出到探索。");
            var mps = new MemPics()
                .Add(Mp.Filter("觉醒.准备"))
                .Add(Mp.Filter("觉醒.达摩"));

            while (!Mp.Filter("觉醒.挑战").FindAll())
            {
                mps.FindAllAndClick();
                Sleep(1000);
            }
            log.Info("退出到探索..");
            Sleep(500);
            Fast.Click("32,22,76,62");
            Sleep(1200);
        }

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            if (Biaoji && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.2秒，标记位置：最右");
                Sleep(200);
                Fast.Click("489,574,575,673");
                return false;
            }
            return true;
        }

        private int count = 0;

        private void main()
        {
            if (UserConfig_Preset != null)
            {
                Sleep(1500);
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入式神录，开始应用预设{UserConfig_Preset}");
                Fast.Click("828,649,857,674");
                Sleep(1500);
                Tmp.Do_Preset(preset);
            }
            int clickcount = 0;
        Re:
            while (count < Ncount)
            {
                if (!WaitJueXing())
                {
                    clickcount++;
                    if (clickcount > 5)
                    {
                        Fast.Click(449, 664, 627, 698);
                        Sleep(200);
                        clickcount = 0;
                    }
                    goto Re;
                }

                Anti.RandomDelay(); //防封等待
                if (Anti.ShouldTriggerRandomYysAuto())//判断是否需要穿插纸人
                {
                    Fast.Click(825, 562, 859, 595); //打开小纸人
                    Sleep(500);
                    int do_Count = Random.Shared.Next(1, 5); // 1-4次随机次数
                    if (Tmp.Do_YysAuto(do_Count))
                    {
                        count += do_Count;
                        log.Info($"触发随机穿插纸人战斗结束..脚本继续接管..");
                        Anti.ResetRandomYysAuto();
                        goto Re;
                    }
                    else Sleep(1000);
                }
                if (Db.PendingTimerTask) //执行定时任务
                {
                    Db.PendingTimerTask = false;
                    log.Info("暂停当前任务，执行定时任务，退出到探索..");
                    EndCallBack();
                    Db?.TimerTask?.DoAllTask();
                    Sleep(1000);
                    throw new Exception("定时任务执行结束，重新执行当前的主任务..");
                }
                Tmp.Do_ClearYuHun(); //执行临时执行御魂

                if (Combat())
                {
                    count++;
                    log.Info($"觉醒战斗胜利，战斗次数：{count}/{Ncount}");
                }
                else
                {
                    log.Warn($"觉醒战斗失败，请检查您的队伍配置是否正常！战斗次数：{count}/{Ncount}");
                    Defeated();
                }
            }
            EndCallBack();
        }

        /// <summary>
        /// 等待觉醒开始界面
        /// </summary>
        /// <returns></returns>
        private bool WaitJueXing()
        {
            //string nows = Scene.NowScene;
            //if (nows == "觉醒")
            //{
            var pics = Mp.Filter("挑战");
            if (pics.Wait()) return true;
            //}
            return false;
        }
    }
}