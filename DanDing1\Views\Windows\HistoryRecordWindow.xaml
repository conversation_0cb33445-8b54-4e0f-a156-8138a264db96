﻿<Window x:Class="DanDing1.Views.Windows.HistoryRecordWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        Title="录制解析器"
        Height="761"
        Width="929"
        d:DataContext="{d:DesignInstance local:HistoryRecordWindow,
                                     IsDesignTimeCreatable=True}"
        ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
        ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        ScrollViewer.CanContentScroll="False"
        WindowStartupLocation="CenterScreen"
        mc:Ignorable="d">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!-- 左侧：日志列表 -->
        <ui:TextBlock HorizontalAlignment="Center" FontWeight="Black" Foreground="Red" Text="---秒级时间戳列表---" Margin="5" FontSize="14" />
        <ListBox x:Name="LogListBox" Grid.Column="0" SelectionChanged="LogListBox_SelectionChanged" Margin="0 22 0 0">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <TextBlock Text="{Binding DisplayText}" />
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>

        <!-- 右侧：控制台和功能区域 -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <!-- 控制台区域 -->
                <RowDefinition Height="*" />
                <!-- 图片和日志区域 -->
            </Grid.RowDefinitions>

            <!-- 控制台区域 -->
            <Border Grid.Row="0"
                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    Margin="10"
                    Padding="10">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <!-- 导出所有图片按钮 -->
                    <Button x:Name="ExportAllImagesButton"
                            Content="导出所有图片"
                            Margin="0,0,10,0"
                            Padding="10,5"
                            Click="ExportAllImagesButton_Click" />
                    <!-- 导出当前图片按钮 -->
                    <Button x:Name="ExportCurrentImageButton"
                            Content="导出当前图片"
                            Padding="10,5"
                            Click="ExportCurrentImageButton_Click" />
                </StackPanel>
            </Border>

            <!-- 图片和日志区域 -->
            <StackPanel Grid.Row="1" Margin="10">
                <Image x:Name="ScreenshotImage"
                       Height="460"
                       Stretch="Uniform" />
                <TextBox x:Name="LogTextBox"
                         FontSize="18"
                         IsReadOnly="True"
                         TextWrapping="Wrap"
                         VerticalScrollBarVisibility="Auto" />
            </StackPanel>
        </Grid>
    </Grid>
</Window>