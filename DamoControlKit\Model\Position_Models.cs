﻿using DamoControlKit.Interface;
using DamoControlKit.runtimes;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DamoControlKit.Model
{
    /// <summary>
    /// 坐标_点
    /// </summary>
    public class Point : IClick
    {
        public Point(int x, int y)
        {
            X = x;
            Y = y;
        }

        public Point(string Str)
        {
            var vs = Str.Split(',');
            if (vs.Length != 2)
                throw new Exception("点_Point初始化失败，Str解析失败..");
            X = int.Parse(vs[0]);
            Y = int.Parse(vs[1]);
        }

        public new string ToString => X + "，" + Y;
        public int X { get; }
        public dmsoft? dmsoft { get; set; }
        public int Y { get; }

        /// <summary>
        /// 比色
        /// </summary>
        /// <param name="color_str"></param>
        /// <returns></returns>
        public bool CmpColor(string color_str, double sim = 0.95)
        {
            if (dmsoft is null)
                throw new Exception($"坐标 {this.ToString}没有指定可用的插件对象，导致错误！");
            return dmsoft.CmpColor(X, Y, color_str, sim) == 0;
        }

        public void Click(int x, int y)
        {
            Random r = new();
            if (dmsoft is null)
                throw new Exception($"坐标 {this.ToString}没有指定可用的插件对象，导致错误！");
            int x1 = x + r.Next(-2, 3);
            int y1 = y + r.Next(-2, 3);
            dmsoft.MoveTo(x1, y1);
            dmsoft.delay(10);

            Record.SaveClick(dmsoft.GetID(), x1, y1);
            dmsoft.LeftClick();
            dmsoft.Delays(10, 30);
        }

        public void Click(Position pos)
        {
            throw new("未实现的方法！");
        }

        public void Click(Position pos, int count, int delay = 25)
        {
            throw new("未实现的方法！");
        }

        public void Click()
        {
            Click(X, Y);
        }

        public void ClickEx(Position pos, int count, int delay = 25)
        {
            throw new("未实现的方法！");
        }

        public bool SetXsoft()
        {
            throw new NotImplementedException();
        }

        public bool SetXsoft(dmsoft x)
        {
            dmsoft = x; return true;
        }
    }

    /// <summary>
    /// 范围
    /// </summary>
    public class Position : IClick, ICloneable
    {
        /// <summary>
        /// 是否可以点击
        /// </summary>
        private bool isReady = true;

        /// <summary>
        /// 初始化1
        /// </summary>
        /// <param name="x">坐标1_x</param>
        /// <param name="y">坐标1_y</param>
        /// <param name="x1">坐标2_x</param>
        /// <param name="y1">坐标2_y</param>
        public Position(int x, int y, int x1, int y1)
        {
            X = x;
            Y = y;
            X1 = x1;
            Y1 = y1;
            P1 = new(X, Y);
            P2 = new(X1, Y1);
        }

        /// <summary>
        /// 初始化2
        /// </summary>
        /// <param name="Str">字符串格式"x,y,x1,y1"</param>
        /// <exception cref="Exception">vs.Length != 4</exception>
        public Position(string Str)
        {
            if (Str is "")
            {
                isReady = false;
                return;
            }
            else if (Str is "GS")
            {
                X = 1;
                Y = 1;
                X1 = 1;
                Y1 = 1;
                P1 = new(X, Y);
                P2 = new(X1, Y1);
                return;
            }

            var vs = Str.Split(",");
            if (vs.Length != 4)
                throw new Exception("范围_Position初始化失败，Str解析失败..");
            X = int.Parse(vs[0]);
            Y = int.Parse(vs[1]);
            X1 = int.Parse(vs[2]);
            Y1 = int.Parse(vs[3]);
            P1 = new(X, Y);
            P2 = new(X1, Y1);
        }

        /// <summary>
        /// 是否为跟随模式
        /// </summary>
        public bool isGS => X == 1 && Y == 1 && X1 == 1 && Y1 == 1;

        /// <summary>
        /// 初始化3
        /// </summary>
        /// <param name="p1">xy点1</param>
        /// <param name="p2">xy点2</param>
        public Position(Point p1, Point p2)
        {
            X = p1.X;
            Y = p1.Y;
            X1 = p2.X;
            Y1 = p2.Y;
            P1 = p1;
            P2 = p2;
        }

        public Point P1 { get; }

        public Point P2 { get; }

        public new string ToString => X + "," + Y + "," + X1 + "," + Y1;

        public int X { get; }

        public int X1 { get; }

        public dmsoft? dmsoft { get; set; }

        public int Y { get; }

        public int Y1 { get; }

        /// <summary>
        /// 唯一标识符 队列用
        /// </summary>
        private string Identifier { get; set; } = "";

        public void Click(int x, int y)
        {
            throw new("未实现的方法！");
        }

        public void Click(Position pos)
        {
            if (!isReady)
                return;

            if (dmsoft is null)
                throw new Exception($"范围 {pos.ToString}没有指定可用的插件对象，导致错误！");
            string ret = dmsoft.MoveToEx(pos.X, pos.Y, pos.X1 - pos.X, pos.Y1 - pos.Y);
            Record.SaveClick(dmsoft.GetID(), ret);
            //var vs = ret.Split(',');
            //int x = int.Parse(vs[0]);
            //int y = int.Parse(vs[1]);
            //RetPos.Pos_str = $"{x - 10},{y - 10},{x + 10},{y + 10}";
            dmsoft.LeftClick();
            dmsoft.Delays(10, 30);
        }

        public void Click(Position pos, int count, int delay = 25)
        {
            throw new("未实现的方法！");
        }

        public void Click_Double()
        {
            this.Click();
            this.Click();
        }

        public void Click() => Click(this);

        public void ClickEx(Position pos, int count, int delay = 25)
        {
            throw new("未实现的方法！");
        }

        /// <summary>
        /// 获取唯一标识符
        /// </summary>
        /// <returns></returns>
        public string GetIdentifier() => Identifier == "" ? ToString : Identifier;

        /// <summary>
        /// 设置唯一标识符
        /// </summary>
        public void SetIdentifier(string identifier)
        {
            Identifier = identifier;
        }

        /// <summary>
        /// 设置唯一标识符
        /// </summary>
        /// <param name="identifier">唯一标识符</param>
        /// <param name="identifiers">已有的标识符列表</param>
        /// <returns>设置失败返回False</returns>
        public bool SetIdentifier(string identifier, List<string> identifiers)
        {
            if (identifiers.Contains(identifier))
                return false;
            Identifier = identifier;
            return true;
        }

        public bool SetXsoft()
        {
            throw new NotImplementedException();
        }

        public bool SetXsoft(dmsoft x)
        {
            dmsoft = x; return true;
        }

        /// <summary>
        /// 深拷贝
        /// </summary>
        /// <returns></returns>
        public object Clone()
        {
            return new Position(X, Y, X1, Y1)
            {
                dmsoft = this.dmsoft,
                Identifier = this.Identifier
            };
        }
    }

    /// <summary>
    /// 范围集合
    /// </summary>
    public class Positions
    {
        public Positions()
        { }

        public Positions(Positions poss)
        { }

        public Positions(List<Position> poss)
        { }

        public List<Position> PositionsList { get; set; } = [];

        public void Add(Position pos) => PositionsList.Add(pos);

        public void Add(Positions poss) => PositionsList.AddRange(poss.PositionsList);

        public void Add(List<Position> poss) => PositionsList.AddRange(poss);

        /// <summary>
        /// 点击 根据唯一标识符
        /// </summary>
        /// <param name="identifier"></param>
        public void Click(string identifier)
        {
            PositionsList.ForEach(t =>
            {
                if (t.GetIdentifier() == identifier) t.Click();
            });
        }

        public List<string>? GetAllIdentifier()
        {
            List<string> allIdentifier = [];
            PositionsList.ForEach(t => allIdentifier.Add(t.GetIdentifier()));
            if (allIdentifier.Count == 0) return null;
            return allIdentifier;
        }

        /// <summary>
        /// 顺序点击队列
        /// </summary>
        /// <param name="delay">间隔时间</param>
        public void Queue_Click(int delay)
        {
            PositionsList.ForEach(async t =>
            {
                t.Click();
                await Task.Delay(delay);
            });
        }

        public bool Remove(Position pos)
        {
            if (PositionsList.IndexOf(pos) == -1)
                return false;
            PositionsList.Remove(pos);
            return true;
        }

        public void SetAllXsoft(dmsoft x) => PositionsList.ForEach(t => t.SetXsoft(x));
    }
}