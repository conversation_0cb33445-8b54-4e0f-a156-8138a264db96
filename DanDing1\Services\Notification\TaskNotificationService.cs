using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XHelper;

namespace DanDing1.Services.Notification
{
    /// <summary>
    /// 任务通知服务，统一处理任务完成通知
    /// </summary>
    public class TaskNotificationService
    {
        private readonly NotificationSenderFactory _senderFactory;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="senderFactory">通知发送器工厂</param>
        public TaskNotificationService(NotificationSenderFactory senderFactory)
        {
            _senderFactory = senderFactory;
        }

        /// <summary>
        /// 发送任务完成通知
        /// </summary>
        /// <param name="gameName">游戏名称</param>
        /// <param name="runningTime">运行时间</param>
        /// <param name="mainClickCount">主点击次数</param>
        /// <param name="subClickCount">副点击次数</param>
        /// <param name="taskNames">执行的任务名称列表</param>
        /// <param name="taskMessages">任务执行详情消息</param>
        /// <param name="noticeType">通知类型显示名称</param>
        /// <returns>发送是否成功</returns>
        public async Task<bool> SendTaskCompletionNoticeAsync(
            string gameName,
            string runningTime,
            int mainClickCount,
            int subClickCount,
            IEnumerable<string> taskNames,
            Dictionary<string, string> taskMessages,
            string noticeType)
        {
            return await SendTaskCompletionNoticeAsync(gameName, runningTime, mainClickCount, subClickCount,
                taskNames, taskMessages, noticeType, null);
        }

        /// <summary>
        /// 发送任务完成通知
        /// </summary>
        /// <param name="gameName">游戏名称</param>
        /// <param name="runningTime">运行时间</param>
        /// <param name="mainClickCount">主点击次数</param>
        /// <param name="subClickCount">副点击次数</param>
        /// <param name="taskNames">执行的任务名称列表</param>
        /// <param name="taskMessages">任务执行详情消息</param>
        /// <param name="noticeType">通知类型显示名称</param>
        /// <param name="extraParams">额外参数</param>
        /// <returns>发送是否成功</returns>
        public async Task<bool> SendTaskCompletionNoticeAsync(
            string gameName,
            string runningTime,
            int mainClickCount,
            int subClickCount,
            IEnumerable<string> taskNames,
            Dictionary<string, string> taskMessages,
            string noticeType,
            Dictionary<string, object> extraParams)
        {
            try
            {
                // 记录当前配置
                //XLogger.Info($"自定义通知配置：App通知={GlobalData.Instance.UserConfig.Notice_Ntfy}, 微信推送={GlobalData.Instance.UserConfig.Notice_WxPush}, Pushplus={GlobalData.Instance.UserConfig.Notice_Pushplus}, 喵提醒={GlobalData.Instance.UserConfig.Notice_Miaotixing}");

                // 构建通知标题
                string title = $"蛋定助手-{gameName}-任务结束通知";

                // 构建通知内容
                string content = $"您当前执行的{gameName}-任务已结束！(用时：{runningTime})#换行" +
                    $"本次任务共执行{mainClickCount}次主点击；#换行" +
                    $"执行{subClickCount}次副点击(受悬赏任务、卡屏重启等操作影响)；#换行" +
                    $"执行任务列表：{string.Join(",", taskNames)}；#换行";

                // 添加任务执行详情
                if (taskMessages != null && taskMessages.Count > 0)
                {
                    content += $"任务运行详情：#换行{string.Join("#换行", taskMessages.Select(x => x.Key + "：" + x.Value))}#换行";
                }

                // 检查是否为自定义通知类型
                if (noticeType == "自定义")
                {
                    // 获取用户配置中的通知开关
                    bool useNtfy = GlobalData.Instance.UserConfig.Notice_Ntfy;
                    bool useWxPush = GlobalData.Instance.UserConfig.Notice_WxPush;
                    bool usePushplus = GlobalData.Instance.UserConfig.Notice_Pushplus;
                    bool useMiaotixing = GlobalData.Instance.UserConfig.Notice_Miaotixing;

                    // 记录当前配置
                    //XLogger.Info($"自定义通知配置：App通知={useNtfy}, 微信推送={useWxPush}, Pushplus={usePushplus}, 喵提醒={useMiaotixing}");

                    // 如果没有启用任何通知方式，默认使用邮件
                    if (!useNtfy && !useWxPush && !usePushplus && !useMiaotixing)
                    {
                        XLogger.Info("自定义通知模式下未启用任何额外通知方式，将只发送邮件通知");
                        return await SendSingleNotification("邮件", title, content, extraParams);
                    }

                    // 用于保存发送结果
                    List<bool> results = new List<bool>();
                    List<string> successTypes = new List<string>();
                    List<string> failedTypes = new List<string>();

                    // 始终发送邮件通知（作为基础通知方式）
                    //bool emailResult = await SendSingleNotification("邮件", title, content, extraParams);
                    //results.Add(emailResult);
                    //if (emailResult)
                    //    successTypes.Add("邮件");
                    //else
                    //    failedTypes.Add("邮件");

                    // 根据配置发送App通知
                    if (useNtfy)
                    {
                        bool ntfyResult = await SendSingleNotification("App", title, content, extraParams);
                        results.Add(ntfyResult);
                        if (ntfyResult)
                            successTypes.Add("App");
                        else
                            failedTypes.Add("App");
                    }

                    // 根据配置发送微信推送通知
                    if (useWxPush)
                    {
                        // 获取微信推送用户UID配置（从用户配置中获取正确的UID）
                        string wxPushUid = GlobalData.Instance.UserConfig.Notice_WxPush_UID;
                        // 获取主题ID配置（多个主题ID用逗号分隔）
                        string topicIdsStr = "97707";

                        // 准备额外参数
                        Dictionary<string, object> wxPushParams = new Dictionary<string, object>();

                        // 添加用户UID列表
                        if (!string.IsNullOrEmpty(wxPushUid))
                        {
                            wxPushParams["uids"] = new List<string> { wxPushUid };
                        }

                        // 添加主题ID列表
                        if (!string.IsNullOrEmpty(topicIdsStr))
                        {
                            List<int> topicIds = new List<int>();
                            foreach (var idStr in topicIdsStr.Split(',', StringSplitOptions.RemoveEmptyEntries))
                            {
                                if (int.TryParse(idStr.Trim(), out int topicId))
                                {
                                    topicIds.Add(topicId);
                                }
                            }

                            if (topicIds.Count > 0)
                            {
                                wxPushParams["topicIds"] = topicIds;
                            }
                        }

                        // 如果UID为空，记录日志
                        if (string.IsNullOrEmpty(wxPushUid))
                        {
                            XLogger.Warn("未配置WxPushUid，将尝试使用主题ID发送");
                        }

                        bool wxPushResult = await SendSingleNotification("微信推送", title, content,
                            wxPushParams.Count > 0 ? wxPushParams : null);
                        results.Add(wxPushResult);
                        if (wxPushResult)
                            successTypes.Add("微信推送");
                        else
                            failedTypes.Add("微信推送");
                    }

                    // 根据配置发送Pushplus推送通知
                    if (usePushplus)
                    {
                        // 获取Pushplus的Token配置
                        string pushplusToken = GlobalData.Instance.UserConfig.Notice_Pushplus_Token;

                        if (string.IsNullOrEmpty(pushplusToken))
                        {
                            XLogger.Warn("未配置Pushplus Token，将跳过Pushplus通知");
                        }
                        else
                        {
                            // 准备额外参数
                            Dictionary<string, object> pushplusParams = new Dictionary<string, object>
                            {
                                ["token"] = pushplusToken,
                                ["template"] = "html" // 默认使用html模板
                            };

                            bool pushplusResult = await SendSingleNotification("Pushplus推送", title, content, pushplusParams);
                            results.Add(pushplusResult);
                            if (pushplusResult)
                                successTypes.Add("Pushplus推送");
                            else
                                failedTypes.Add("Pushplus推送");
                        }
                    }

                    // 根据配置发送喵提醒通知
                    if (useMiaotixing)
                    {
                        // 获取喵提醒的ID配置
                        string miaotixingId = GlobalData.Instance.UserConfig.Notice_Miaotixing_ID;

                        if (string.IsNullOrEmpty(miaotixingId))
                        {
                            XLogger.Warn("未配置喵提醒ID，将跳过喵提醒通知");
                        }
                        else
                        {
                            // 准备额外参数
                            Dictionary<string, object> miaotixingParams = new Dictionary<string, object>
                            {
                                ["miaoid"] = miaotixingId
                            };

                            bool miaotixingResult = await SendSingleNotification("喵提醒", title, content, miaotixingParams);
                            results.Add(miaotixingResult);
                            if (miaotixingResult)
                                successTypes.Add("喵提醒");
                            else
                                failedTypes.Add("喵提醒");
                        }
                    }

                    // 记录发送结果
                    if (successTypes.Count > 0)
                    {
                        XLogger.Info($"成功发送通知类型: {string.Join(", ", successTypes)}");
                    }
                    if (failedTypes.Count > 0)
                    {
                        XLogger.Warn($"发送失败的通知类型: {string.Join(", ", failedTypes)}");
                    }

                    // 如果至少有一种通知方式成功，则认为发送成功
                    return results.Any(r => r);
                }
                else
                {
                    // 非自定义模式，使用单一通知方式
                    return await SendSingleNotification(noticeType, title, content, extraParams);
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"发送任务完成通知失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 发送单一类型的通知
        /// </summary>
        /// <param name="noticeType">通知类型显示名称</param>
        /// <param name="title">通知标题</param>
        /// <param name="content">通知内容</param>
        /// <param name="extraParams">额外参数</param>
        /// <returns>发送是否成功</returns>
        private async Task<bool> SendSingleNotification(string noticeType, string title, string content, Dictionary<string, object> extraParams = null)
        {
            // 获取通知发送器
            var sender = _senderFactory.GetSenderByDisplayName(noticeType);
            if (sender == null)
            {
                XLogger.Warn($"未找到通知类型 '{noticeType}' 对应的发送器，使用默认发送器");
                sender = _senderFactory.GetSender("email");

                if (sender == null)
                {
                    XLogger.Error("没有可用的通知发送器");
                    return false;
                }
            }

            try
            {
                // 发送通知
                bool result = await sender.SendAsync(title, content, extraParams);
                if (result)
                {
                    XLogger.Info($"[{noticeType}] 通知发送成功");
                }
                else
                {
                    XLogger.Warn($"[{noticeType}] 通知发送失败");
                }
                return result;
            }
            catch (Exception ex)
            {
                XLogger.Error($"[{noticeType}] 发送通知时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有支持的通知类型显示名称
        /// </summary>
        /// <returns>通知类型显示名称列表</returns>
        public IEnumerable<string> GetSupportedNoticeTypes()
        {
            return _senderFactory.GetAllDisplayNames();
        }

        /// <summary>
        /// 发送简化的通知消息
        /// 该方法只需要标题和内容作为参数，使用用户当前配置的通知方式
        /// </summary>
        /// <param name="type">通知类型，传入"自定义"时将使用用户配置的组合通知方式</param>
        /// <param name="title">通知标题</param>
        /// <param name="content">通知内容</param>
        /// <returns>发送是否成功</returns>
        public async Task<bool> SendSimpleNotificationAsync(string type, string title, string content)
        {
            try
            {
                // 使用传入的通知方式
                string noticeType = type;

                if (noticeType == "自定义")
                {
                    // 检查用户是否启用了任何通知方式
                    bool useNtfy = GlobalData.Instance.UserConfig.Notice_Ntfy;
                    bool useWxPush = GlobalData.Instance.UserConfig.Notice_WxPush;
                    bool usePushplus = GlobalData.Instance.UserConfig.Notice_Pushplus;
                    bool useMiaotixing = GlobalData.Instance.UserConfig.Notice_Miaotixing;

                    //XLogger.Info($"自定义通知配置：App通知={useNtfy}, 微信推送={useWxPush}, Pushplus={usePushplus}, 喵提醒={useMiaotixing}");

                    if (!useNtfy && !useWxPush && !usePushplus && !useMiaotixing)
                    {
                        XLogger.Warn("未启用任何额外通知方式，将只发送邮件通知");
                        return await SendSingleNotification("邮件", title, content);
                    }
                }

                // 使用发送通知的核心方法
                XLogger.Info($"正在发送测试通知：{noticeType}...");

                // 构建简单任务信息
                List<string> taskNames = new List<string> { "测试通知" };
                Dictionary<string, string> taskMessages = new Dictionary<string, string> { { "消息", content } };

                // 如果是自定义模式，调用完整通知方法以支持多渠道发送
                if (noticeType == "自定义")
                {
                    return await SendTaskCompletionNoticeAsync(
                        "测试", // 游戏名称
                        DateTime.Now.ToString("HH:mm:ss"), // 当前时间
                        0, // 主点击次数
                        0, // 副点击次数
                        taskNames, // 任务名称列表
                        taskMessages, // 任务消息
                        noticeType // 自定义通知类型
                    );
                }
                else
                {
                    // 非自定义模式，使用单一通知发送
                    return await SendSingleNotification(noticeType, title, content);
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"发送简化通知失败: {ex.Message}");
                return false;
            }
        }
    }
}