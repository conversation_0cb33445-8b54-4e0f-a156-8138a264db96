using System;
using System.Globalization;
using System.Windows.Data;

namespace DanDing1.Helpers
{
    /// <summary>
    /// 将TimeSpan格式化为包含天数的字符串的转换器
    /// 当TimeSpan超过1天时，会在前面添加天数标识
    /// </summary>
    public class TimeSpanWithDaysConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is TimeSpan timeSpan)
            {
                // 如果超过1天，在前面加天数标识
                if (timeSpan.Days > 0)
                {
                    return $"{timeSpan.Days}天{timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
                }
                
                // 否则使用常规时:分:秒格式
                return $"{timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
            }
            
            return string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 