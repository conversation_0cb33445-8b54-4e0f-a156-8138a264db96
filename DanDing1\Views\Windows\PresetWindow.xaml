<ui:FluentWindow
    x:Class="DanDing1.Views.Windows.PresetWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:local="clr-namespace:DanDing1.Views.Windows"
    xmlns:userControls="clr-namespace:DanDing1.Views.UserControls"
    Title="预设管理"
    Width="800"
    Height="550"
    d:DesignHeight="550"
    d:DesignWidth="800"
    ExtendsContentIntoTitleBar="True"
    WindowBackdropType="Mica"
    WindowCornerPreference="Round"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <ui:TitleBar
            Grid.Row="0"
            Title="预设管理"
            ShowMaximize="False"
            CanMaximize="False"
            Icon="pack://application:,,,/Assets/egg-icon-256.png" />

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1" Margin="12">
            <userControls:PresetControl x:Name="presetControl" />
        </Grid>

        <!-- 对话框宿主 -->
        <ContentPresenter x:Name="dialogHost" Grid.RowSpan="2" />
    </Grid>
</ui:FluentWindow>