﻿using DamoControlKit;
using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System;
using System.Text.RegularExpressions;
using XHelper;
using Point = DamoControlKit.Model.Point;

namespace ScriptEngine.Tasks
{
    /// <summary>
    /// 百鬼任务
    /// </summary>
    internal class BaiGuiTask : BaseTask
    {
        // 冻结状态下最大点击次数
        private const int MaxFrozenAreaClicks = 3;

        // 冻结状态下前后范围点击次数
        private Dictionary<string, int> FrozenAreaClickCount = new()
        {
            {"前", 0},
            {"后", 0},
        };

        /// <summary>
        /// 是否邀请队友
        /// </summary>
        private bool Inv = false;

        // 追击砸豆状态
        private bool IsZhuiJiMode = false;

        private Point LastTargetPoint;

        private Dictionary<string, Position> NanZaPos = new()
        {
            {"前", new(740,345,1237,538)},
            {"后", new(15,374,626,568)},
        };

        private Dictionary<string, int> TiQianLiang_X = new()
        {
            {"减速", 50},
            {"", 150},
        };

        private Dictionary<string, int> ZaDouJianGe = new()
        {
            {"加速", 0},
            {"冻结", 2000},
            {"", 700},
        };

        // 追击砸豆配置
        private Dictionary<string, (int Count, int Interval)> ZhuiJiConfig = new()
        {
            {"SSR", (5, 300)},
            {"SP", (5, 300)},
            {"G", (2, 500)},
            {"SR", (1, 600)}
        };

        private int ZhuiJiInterval = 0;
        private int ZhuiJiRemainCount = 0;

        /// <summary>
        /// 总百鬼次数
        /// </summary>
        public int Maxcount { get; private set; }

        /// <summary>
        /// 当前执行百鬼次数
        /// </summary>
        public int Nowcount { get; private set; }

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "百鬼");
            XYoloV8.Reset();
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Nowcount = 0;
            Maxcount = GetConfig.Count;
            GetConfig.Others.TryGetValue("Inv", out string? s);
            try { Inv = s is not null && bool.Parse(s); } catch (Exception) { Inv = false; }

            log.Info($"百鬼任务开始，计划执行{Maxcount}次...");

            if (!ToBaiGuiScence())
            {
                log.Warn("百鬼任务无法继续，当前游戏所在场景未知，请调整到庭院或探索主界面开始脚本！");
                return;
            }

            // 任务循环执行
            while (Nowcount < Maxcount && !Ct.IsCancellationRequested)
            {
                Nowcount++;
                log.Info($"开始执行第 {Nowcount}/{Maxcount} 次百鬼任务...");
                if (!TaskRun())
                {
                    log.Warn($"第 {Nowcount} 次百鬼任务执行失败，任务中止");
                    break;
                }

                Anti.RandomDelay();

                // 如果还有下一次任务，等待一段时间后继续
                if (Nowcount < Maxcount)
                {
                    log.Info($"等待5秒后开始下一次百鬼任务...");
                    Sleep(5000);
                    // 重新进入百鬼界面
                    if (!ToBaiGuiScence())
                    {
                        log.Warn("无法进入百鬼界面，任务中止");
                        break;
                    }
                }
            }

            // 输出任务完成统计
            if (Ct.IsCancellationRequested)
                log.Warn($"百鬼任务被用户取消，已完成 {Nowcount - 1}/{Maxcount} 次");
            else
                log.Info($"百鬼任务全部完成，共执行 {Nowcount}/{Maxcount} 次");

            //返回庭院
            Fast.Click(1083, 203);
            Sleep(1500);
            Fast.Click(1060, 270);
            Sleep(2000);
        }

        /// <summary>
        /// 执行追击砸豆
        /// </summary>
        private bool ExecuteZhuiJiZaDou(List<string> buffs)
        {
            if (!IsZhuiJiMode || ZhuiJiRemainCount <= 0)
                return false;

            bool isFrozen = buffs.Contains("冻结");

            // 生成X轴偏移值，冻结状态下不偏移
            int xOffset = isFrozen ? 0 : new Random().Next(80, 151);

            // 计算新的点击坐标
            int newX = Math.Max(1, LastTargetPoint.X - xOffset);

            if (newX <= 0 && !isFrozen) // 只有非冻结状态才检查X轴边界
            {
                log.Debug("X轴已达到边界，追击砸豆结束");
                ResetZhuiJiStatus();
                return false;
            }

            // 随机Y轴偏移±30像素
            int randomYOffset = new Random().Next(-30, 31);
            Point clickPoint = new Point(newX, LastTargetPoint.Y + randomYOffset);

            // 更新最后目标点
            LastTargetPoint = clickPoint;

            // 执行点击
            log.Info($"执行追击砸豆 (剩余:{ZhuiJiRemainCount}){(isFrozen ? "[冻结状态]" : "")}...");
            Operational.Click(clickPoint);
            Sleep(ZhuiJiInterval);

            // 减少剩余追击次数
            ZhuiJiRemainCount--;

            // 检查是否结束追击
            if (ZhuiJiRemainCount <= 0)
            {
                log.Debug("追击砸豆次数已用完，结束追击");
                ResetZhuiJiStatus();
            }

            return true;
        }

        /// <summary>
        /// 根据时间获取当前砸豆延迟时间
        /// </summary>
        private int GetCurrentDelayTime(double elapsedSeconds, int defaultDelayTime, ref bool is25)
        {
            if (elapsedSeconds > 27)
            {
                if (!is25)
                {
                    TiaoDou(7, 10);
                    is25 = true;
                    log.Debug("已超过27秒，砸豆间隔调整为300ms");
                }
                return 300;
            }
            return defaultDelayTime;
        }

        /// <summary>
        /// 根据buff获取砸豆延迟时间
        /// </summary>
        private int GetDelayTimeBasedOnBuff(List<string> buffs)
        {
            if (buffs.Contains("冻结"))
                return ZaDouJianGe["冻结"];
            else if (buffs.Contains("加速"))
                return ZaDouJianGe["加速"];
            return ZaDouJianGe[""];
        }

        /// <summary>
        /// 根据buff获取X轴偏移值
        /// </summary>
        private int GetDownXBasedOnBuff(List<string> buffs)
        {
            if (buffs.Contains("减速"))
                return TiQianLiang_X["减速"];
            return TiQianLiang_X[""];
        }

        /// <summary>
        /// 在冻结状态下选择合适的砸豆位置
        /// </summary>
        /// <returns>返回砸豆位置，如果为null则表示两个区域都达到点击上限</returns>
        private Position? GetFrozenZaDouPosition()
        {
            // 检查前区域是否未达到上限
            if (FrozenAreaClickCount["前"] < MaxFrozenAreaClicks)
            {
                FrozenAreaClickCount["前"]++;
                log.Debug($"冻结状态下点击前区域 ({FrozenAreaClickCount["前"]}/{MaxFrozenAreaClicks})");
                return NanZaPos["前"];
            }

            // 检查后区域是否未达到上限
            if (FrozenAreaClickCount["后"] < MaxFrozenAreaClicks)
            {
                FrozenAreaClickCount["后"]++;
                log.Debug($"冻结状态下点击后区域 ({FrozenAreaClickCount["后"]}/{MaxFrozenAreaClicks})");
                return NanZaPos["后"];
            }

            // 两个区域都达到上限
            log.Debug("冻结状态下两个区域都达到点击上限，等待冻结状态结束");
            return null;
        }

        private List<string> GetNowBuff()
        {
            List<string> buffs_list = new();
            Dictionary<string, string> tobuffname = new()
            {
                {"百鬼.buffs_冻结.bmp","冻结" },
                {"百鬼.buffs_冻结1.bmp","冻结" },
                {"百鬼.buffs_冻结2.bmp","冻结" },
                {"百鬼.buffs_冻结3.bmp","冻结" },
                {"百鬼.buffs_冻结4.bmp","冻结" },
                {"百鬼.buffs_概率.bmp","概率" },
                {"百鬼.buffs_概率1.bmp","概率" },
                {"百鬼.buffs_概率2.bmp","概率" },
                {"百鬼.buffs_概率3.bmp","概率" },
                {"百鬼.buffs_好友概率.bmp","好友概率" },
                {"百鬼.buffs_好友概率1.bmp","好友概率" },
                {"百鬼.buffs_好友概率2.bmp","好友概率" },
                {"百鬼.buffs_好友概率3.bmp","好友概率" },
                {"百鬼.buffs_加速撒豆.bmp","加速撒豆" },
                {"百鬼.buffs_加速撒豆1.bmp","加速撒豆" },
                {"百鬼.buffs_加速撒豆2.bmp","加速撒豆" },
                {"百鬼.buffs_加速撒豆3.bmp","加速撒豆" },
                {"百鬼.buffs_减速.bmp","减速" },
                {"百鬼.buffs_减速1.bmp","减速" },
                {"百鬼.buffs_减速2.bmp","减速" },
                {"百鬼.buffs_减速3.bmp","减速" },
            };
            var buffs = Mp.Filter("buffs_");
            buffs.FindAllE(out var lists);
            foreach (var item in lists)
            {
                if (tobuffname.ContainsKey(item))
                    buffs_list.Add(tobuffname[item]);
            }
            buffs_list = buffs_list.Distinct().ToList();
            return buffs_list;
        }

        /// <summary>
        /// 根据时间获取砸豆位置
        /// </summary>
        private Position GetZaDouPositionBasedOnTime(double elapsedSeconds)
        {
            if (elapsedSeconds > 30)
                return NanZaPos["后"];
            return NanZaPos["前"];
        }

        /// <summary>
        /// 处理冻结和减速状态下的计时器控制
        /// </summary>
        private bool HandleBuffStateTimer(List<string> buffs, System.Diagnostics.Stopwatch stopwatch, bool wasPaused)
        {
            // 检查是否存在冻结或减速buff
            bool shouldPause = buffs.Contains("冻结") || buffs.Contains("减速");
            bool isFrozen = buffs.Contains("冻结");

            // 如果不再是冻结状态但之前有冻结状态，重置冻结区域点击计数
            if (!isFrozen && FrozenAreaClickCount["前"] > 0 || FrozenAreaClickCount["后"] > 0)
            {
                ResetFrozenAreaClickCount();
            }

            if (shouldPause && !wasPaused)
            {
                // 当检测到冻结或减速状态且之前不是暂停状态时，暂停计时器
                stopwatch.Stop();
                string buffType = buffs.Contains("冻结") ? "冻结" : "减速";
                log.Debug($"检测到{buffType}状态，暂停计时器");
                return true;
            }
            else if (!shouldPause && wasPaused)
            {
                // 当检测到不再是冻结或减速状态且之前是暂停状态时，继续计时器
                stopwatch.Start();
                log.Debug("暂停状态解除，继续计时器");
                return false;
            }
            return wasPaused;
        }

        /// <summary>
        /// 初始化砸豆计时器
        /// </summary>
        private System.Diagnostics.Stopwatch InitializeZaDouTimer()
        {
            var stopwatch = new System.Diagnostics.Stopwatch();
            stopwatch.Start();
            return stopwatch;
        }

        /// <summary>
        /// 检查砸豆是否完成
        /// </summary>
        private bool IsZaDouFinished(MemPics endpics)
        {
            if (NowIsBaiGuiScence(false))
            {
                log.Info("当前位于百鬼界面，结束砸豆操作，并重新恢复流程...");
                return true;
            }
            if (endpics.FindAll())
            {
                log.Info("当前剩余豆子数量为0，结束本次砸豆...");
                return true;
            }
            return false;
        }

        private MemPics? Main_pics = null;

        /// <summary>
        /// 判断当前是否在百鬼界面
        /// </summary>
        private bool NowIsBaiGuiScence(bool useocr = true)
        {
            Main_pics ??= Mp.Filter("Sc_Main");
            if (Main_pics.FindAll())
            {
                string num = Fast.Ocr_Local(921, 589, 1030, 621);
                log.Info_Green("当前在百鬼场景中，百鬼门票剩余：" + num);
                return true;
            }
            if (useocr)
            {
                string ocrstr = Fast.Ocr_String(101, 628, 405, 669);
                if (ocrstr.Contains("同行"))
                {
                    string num = Fast.Ocr_Local(921, 589, 1030, 621);
                    log.Info_Green("当前在百鬼场景中，百鬼门票剩余：" + num);
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 随机砸豆
        /// </summary>
        private void RandomZaDou(Position position, int delayTime)
        {
            log.Info("随机砸豆...");
            Fast.Click(position);
            Sleep(delayTime);
        }

        /// <summary>
        /// 重置冻结区域点击计数
        /// </summary>
        private void ResetFrozenAreaClickCount()
        {
            FrozenAreaClickCount["前"] = 0;
            FrozenAreaClickCount["后"] = 0;
            log.Debug("冻结区域点击计数已重置");
        }

        /// <summary>
        /// 重置追击砸豆状态
        /// </summary>
        private void ResetZhuiJiStatus()
        {
            IsZhuiJiMode = false;
            ZhuiJiRemainCount = 0;
            ZhuiJiInterval = 0;
            log.Debug("追击砸豆状态已重置");
        }

        /// <summary>
        /// 保存砸豆结果截图
        /// </summary>
        private void SaveZaDouResultScreenshot()
        {
            if (NowIsBaiGuiScence())
            {
                Sleep(2000);
                return;
            }

            log.Info("等待3s后截图..");
            Sleep(3000);
            while (!Mp.Filter("截图").FindAll()) Sleep(1000);
            Sleep(2500);
            //截图 保存到.\\百鬼 文件夹
            var path = Path.Combine(Environment.CurrentDirectory, "百鬼");
            if (!Directory.Exists(path))
                Directory.CreateDirectory(path);
            Dm.CaptureJpg(178, 96, 1207, 621, path + $"\\[{log.LogClassName}] {DateTime.Now:yyyyMMddHHmmss}.jpg", 100);
            Fast.Click(554, 656, 762, 692);
            Sleep(2000);
        }

        /// <summary>
        /// 执行单次百鬼任务
        /// </summary>
        /// <returns>是否成功完成</returns>
        private bool TaskRun()
        {
            try
            {
                WaitMain();
                if (Inv)
                {
                    log.Info("检查能否邀请好友...");
                    if (Mp.Filter("可邀请").FindAll())
                    {
                        log.Info("邀请好友...默认第二个");
                        Fast.Click(168, 597);
                        Sleep(2500);
                        Fast.Click(575, 261);
                        Sleep(2500);
                        Fast.Click(725, 621);
                        Sleep(2000);
                        while (!NowIsBaiGuiScence(false))
                        {
                            Fast.Click(725, 621);
                            Sleep(2000);
                        }
                    }
                }

                log.Info("点击进入...");
                Fast.Click(1072, 574, 1140, 629);
                Sleep(2500);
                log.Info("等待选择式神...");
                while (Fast.Ocr_String(398, 626, 879, 686).Contains("碎片"))
                {
                    log.Info("选择三倍碎片式神..");
                    // 随机选择一个范围点击
                    int randomChoice = new Random().Next(1, 4); // 生成1-3的随机数
                    switch (randomChoice)
                    {
                        case 1:
                            log.Info("随机选择第一个式神...");
                            Fast.Click(234, 416, 302, 544);
                            break;

                        case 2:
                            log.Info("随机选择第二个式神...");
                            Fast.Click(589, 426, 673, 531);
                            break;

                        case 3:
                            log.Info("随机选择第三个式神...");
                            Fast.Click(990, 455, 1036, 532);
                            break;
                    }
                    Sleep(1000);
                    log.Info("点击开始..");
                    Fast.Click(1122, 573, 1221, 642);
                    Sleep(2000);
                }
                ZaDou();
                log.Info($"第 {Nowcount} 次百鬼任务完成");
                return true;
            }
            catch (Exception ex)
            {
                log.Error($"百鬼任务执行出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 调整单次砸豆数量
        /// </summary>
        private void TiaoDou(int startnum, int endnum)
        {
            Dictionary<int, Point> _dic = new()
            {
                {1,new(230,657) },
                {2,new(309,657) },
                {3,new(345,658) },
                {4,new(379,658) },
                {5,new(415,656) },
                {6,new(464,660) },
                {7,new(484,658) },
                {8,new(518,657) },
                {9,new(548,658) },
                {10,new(580,658) },
            };
            Operational.Slide_Pos(_dic[startnum], _dic[endnum]);
            Sleep(1000);
        }

        /// <summary>
        /// 调整单次砸豆数量
        /// </summary>
        private void TiaoDou(int num)
        {
            Dictionary<int, Point> _dic = new()
            {
                {1,new(230,657) },
                {2,new(309,657) },
                {3,new(345,658) },
                {4,new(379,658) },
                {5,new(415,656) },
                {6,new(464,660) },
                {7,new(484,658) },
                {8,new(518,657) },
                {9,new(548,658) },
                {10,new(576,658) },
            };
            Operational.Slide_Pos(new Point(404, 655), _dic[num]);
            Sleep(300, true);
        }

        /// <summary>
        /// 去百鬼
        /// </summary>
        private bool ToBaiGuiScence()
        {
            bool try1 = false;
        Retry:
            if (NowIsBaiGuiScence()) return true;
            else if (!try1)
            {
                try1 = true;
                Sleep(2000);
                goto Retry;
            }

            string nowsc = Scene.NowScene;
            log.Info("当前所在场景：" + nowsc);
            if (nowsc == "庭院")
            {
                Scene.TO.TingZhong();
                Sleep(1000);
                Fast.Click(903, 192);
                Sleep(2000);
                goto Retry;
            }
            if (nowsc == "探索")
            {
                Scene.TO.TingYuan();
                Sleep(2000);
                goto Retry;
            }
            return false;
        }

        /// <summary>
        /// 从目标名称中提取类型前缀
        /// </summary>
        private string ExtractTargetType(string targetName)
        {
            // 使用正则表达式提取目标类型前缀
            var match = Regex.Match(targetName, @"'([A-Z]+)");
            if (match.Success && match.Groups.Count > 1)
            {
                string prefix = match.Groups[1].Value;
                log.Debug($"从'{targetName}'中提取到目标类型前缀: {prefix}");
                return prefix;
            }

            // 备用方法：直接检查是否包含关键前缀
            if (targetName.Contains("'SSR")) return "SSR";
            if (targetName.Contains("'SP")) return "SP";
            if (targetName.Contains("'G")) return "G";
            if (targetName.Contains("'SR")) return "SR";
            if (targetName.Contains("'B")) return "B";

            log.Debug($"无法从'{targetName}'中提取目标类型前缀");
            return "";
        }

        /// <summary>
        /// 尝试智能砸豆（识别高优先级目标）
        /// </summary>
        private bool TrySmartZaDou(int down_x, int delayTime, List<string> buffs)
        {
            // 如果当前处于追击模式，则执行追击砸豆
            if (IsZhuiJiMode)
            {
                return ExecuteZhuiJiZaDou(buffs);
            }

            var boxs = Fast.YoloDet_Baigui_All();
            if (boxs == null || boxs.Count == 0) return false;

            // 按照优先级规则排序：B -> SSR -> SP -> G -> SR
            var sortedBoxs = boxs.OrderBy(box =>
            {
                string name = box.Name.ToString();
                if (name.Contains("'B")) return 0;
                if (name.Contains("'SSR")) return 1;
                if (name.Contains("'SP")) return 2;
                if (name.Contains("'G")) return 3;
                if (name.Contains("'SR")) return 4;
                return 5; // 其他情况的最低优先级
            }).ToList();

            // 只处理优先级最高的一个box
            var highestPriorityBox = sortedBoxs[0];
            string targetName = highestPriorityBox.Name.ToString();
            log.Debug($"选择点击最高优先级目标: {targetName}");

            var point = new Position(highestPriorityBox.Bounds.GetPosStr());
            var _point = point.GetCenterPoint();

            var randomYOffset = new Random().Next(-30, 31); // Y轴随机偏移±30
            // 确保X坐标不小于1
            var adjustedX = Math.Max(1, _point.X - down_x);
            var click_point = new Point(adjustedX, _point.Y + randomYOffset);

            // 保存最后点击的位置（用于后续追击）
            LastTargetPoint = click_point;

            log.Info($"智能砸豆... {targetName}");
            Operational.Click(click_point);
            Sleep(delayTime);

            // 检查是否需要启动追击模式（只对非B型目标启动追击）
            // 提取目标类型
            string targetType = ExtractTargetType(targetName);

            // 如果不是B类型，并且是配置中的目标类型，启动追击模式
            if (targetType != "B" && targetType != "")
            {
                log.Debug($"检查目标类型 {targetType} 是否需要启动追击模式");

                // 如果是配置中的目标类型，启动追击模式
                if (ZhuiJiConfig.ContainsKey(targetType))
                {
                    IsZhuiJiMode = true;
                    ZhuiJiRemainCount = ZhuiJiConfig[targetType].Count;
                    ZhuiJiInterval = ZhuiJiConfig[targetType].Interval;
                    log.Info($"启动追击模式: {targetType}, 次数: {ZhuiJiRemainCount}, 间隔: {ZhuiJiInterval}ms");
                }
                else
                {
                    log.Debug($"目标类型 {targetType} 不在追击配置中");
                }
            }
            else if (targetType == "B")
            {
                log.Debug("B类型目标不启动追击模式");
            }

            return true;
        }

        /// <summary>
        /// 等待百鬼场景
        /// </summary>
        private void WaitMain()
        {
            while (!NowIsBaiGuiScence())
            {
                Sleep(1000);
            }
        }

        /// <summary>
        /// 开始砸豆
        /// </summary>
        private void ZaDou()
        {
            log.Info("开始砸豆...调整单次砸豆数量为：7");
            TiaoDou(7);

            // 初始化砸豆计时器
            var stopwatch = InitializeZaDouTimer();
            bool wasPaused = false;
            bool is25 = false;

            // 初始化砸豆参数
            int down_x = TiQianLiang_X[""];
            int delay_time = ZaDouJianGe[""];
            var endpics = Mp.Filter("结束").Add(Mp.Filter("截图")).Add(Mp.Filter("时间0"));

            // 重置冻结区域点击计数
            ResetFrozenAreaClickCount();

            // 重置追击砸豆状态
            ResetZhuiJiStatus();

            while (true)
            {
                // 检查游戏是否结束
                if (IsZaDouFinished(endpics)) break;

                // 计算已经过去的时间（秒）
                double elapsedSeconds = stopwatch.Elapsed.TotalSeconds;

                // 获取当前buff状态
                var buffs = GetNowBuff();

                // 处理冻结和减速状态对计时器的影响
                wasPaused = HandleBuffStateTimer(buffs, stopwatch, wasPaused);

                // 根据buff状态调整参数
                down_x = GetDownXBasedOnBuff(buffs);
                delay_time = GetDelayTimeBasedOnBuff(buffs);

                // 检查是否处于冻结状态
                bool isFrozen = buffs.Contains("冻结");

                if (isFrozen && !IsZhuiJiMode)
                {
                    // 冻结状态下且不处于追击模式时的特殊处理
                    Position? frozenPosition = GetFrozenZaDouPosition();
                    if (frozenPosition != null)
                    {
                        // 在指定位置随机砸豆
                        RandomZaDou(frozenPosition, delay_time);
                    }
                    else
                    {
                        // 两个区域都达到上限，等待冻结状态结束
                        log.Info("冻结状态下两个区域都已达到点击上限，等待冻结状态结束...");
                        Sleep(500);
                    }
                    continue;
                }

                // 非冻结状态下的常规处理或冻结状态下的追击处理
                // 根据时间调整砸豆策略
                Position nanZaPosition = GetZaDouPositionBasedOnTime(elapsedSeconds);

                // 根据时间调整砸豆间隔
                int currentDelayTime = GetCurrentDelayTime(elapsedSeconds, delay_time, ref is25);

                // 检查游戏是否结束
                if (IsZaDouFinished(endpics)) break;

                // 非冻结状态下智能砸豆或追击砸豆
                if (TrySmartZaDou(down_x, currentDelayTime, buffs))
                {
                    continue; // 成功智能砸豆或追击砸豆后继续下一轮
                }

                // 随机砸豆（当智能砸豆和追击砸豆都失败时）
                RandomZaDou(nanZaPosition, currentDelayTime);
            }

            // 重置追击模式
            ResetZhuiJiStatus();

            // 任务结束后截图保存
            SaveZaDouResultScreenshot();
        }
    }
}