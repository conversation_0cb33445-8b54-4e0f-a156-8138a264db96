﻿using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System.Reflection;
using static ScriptEngine.Model.TaskConfigsModel;

namespace ScriptEngine.Factorys
{
    /// <summary>
    /// 任务执行工厂类，用于快速启动Tasks文件夹下的任务
    /// </summary>
    internal static class RunTaskFactory
    {
        /// <summary>
        /// 快速启动任务（泛型方式）
        /// </summary>
        /// <typeparam name="T">任务类型，必须继承自BaseTask</typeparam>
        /// <param name="db">DDBuilder实例</param>
        /// <param name="dm">dmsoft实例</param>
        /// <param name="ct">CancellationTokenSource实例</param>
        /// <param name="count">执行次数</param>
        /// <param name="showName">显示名称，为空则使用类名</param>
        /// <param name="others">其他参数</param>
        /// <returns>启动的任务实例</returns>
        public static T Run<T>(DDBuilder db, dmsoft dm, CancellationTokenSource ct, int count = 1, string? showName = null, Dictionary<string, string>? others = null) where T : BaseTask, new()
        {
            Dictionary<string, string> toNamedic = new()
            {
                {"BuffTask","Buff" },
                {"TuPo","突破" },
                {"TanSuo","探索" }
            };
            T task = new();
            string className = typeof(T).Name;

            var config = new Configs()
            {
                Count = count,
                Name = className,
                ShowName = showName ?? $"{className} {count}次",
                Others = others ?? []
            };

            ReloadPreset(db, toNamedic[className], config);

            task.Init(db, dm, ct, className);

            task.Start(config);

            return task;
        }

        /// <summary>
        /// 快速启动任务（非泛型方式，通过任务名称）
        /// </summary>
        /// <param name="taskName">任务名称（类名）</param>
        /// <param name="db">DDBuilder实例</param>
        /// <param name="dm">dmsoft实例</param>
        /// <param name="ct">CancellationTokenSource实例</param>
        /// <param name="count">执行次数</param>
        /// <param name="showName">显示名称，为空则使用类名</param>
        /// <param name="others">其他参数</param>
        /// <returns>启动的任务实例，未找到任务则返回null</returns>
        public static BaseTask? Run(string taskName, DDBuilder db, dmsoft dm, CancellationTokenSource ct, int count = 1, string? showName = null, Dictionary<string, string>? others = null)
        {
            // 获取任务类型
            Type? taskType = Assembly.GetExecutingAssembly()
                .GetTypes()
                .FirstOrDefault(t =>
                    t.IsClass &&
                    !t.IsAbstract &&
                    t.IsSubclassOf(typeof(BaseTask)) &&
                    t.Name.Equals(taskName, StringComparison.OrdinalIgnoreCase));

            if (taskType == null) return null;

            // 创建任务实例
            if (Activator.CreateInstance(taskType) is not BaseTask task) return null;

            // 初始化并启动任务
            task.Init(db, dm, ct, taskName);
            task.Start(new TaskConfigsModel.Configs()
            {
                Count = count,
                Name = taskName,
                ShowName = showName ?? $"{taskName} {count}次",
                Others = others ?? new Dictionary<string, string>()
            });

            return task;
        }

        /// <summary>
        /// 重新判断加载预设
        /// </summary>
        private static void ReloadPreset(DDBuilder DB, string str, Configs config)
        {
            Dictionary<string, string> topairs = new()
            {
                {"御魂", "Gal_YuShe_Yuhun"},
                {"觉醒", "Gal_YuShe_Juexing"},
                {"探索", "Gal_YuShe_Tansuo"},
                {"日轮", "Gal_YuShe_Rilun"},
                {"永生", "Gal_YuShe_Yongsheng"},
                {"御灵", "Gal_YuShe_Yuling"},
                {"业原火", "Gal_YuShe_Yeyuanhuo"},
                {"突破", "Gal_YuShe_Tupo"},
                {"悬赏", "Gal_YuShe_Xuanshang"},
                {"六道", "Gal_YuShe_Liudao"},
                {"契灵", "Gal_YuShe_Qiling"},
                {"斗技", "Gal_YuShe_Douji"},
                {"英杰", "Gal_YuShe_Yingjie"}
            };

            if (DB.UserConfigs.TryGetValue("Gal_YuShe", out object? _T) && _T is bool && (bool)_T)
                if (topairs.TryGetValue(str, out string presetKey))
                    if (DB.UserConfigs.TryGetValue(presetKey, out object? _T1) && _T1 is string && !string.IsNullOrEmpty((string)_T1))
                        config.Others["Preset"] = _T1.ToString();
        }
    }
}