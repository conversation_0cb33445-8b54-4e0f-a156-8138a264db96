﻿using DanDing1.ViewModels.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Wpf.Ui.Controls;
using Wpf.Ui.Abstractions.Controls;
using XHelper;
using DanDing1.Views.UserControls;

namespace DanDing1.Views.Pages
{
    /// <summary>
    /// Game1Page.xaml 的交互逻辑
    /// </summary>
    public partial class Game1Page : INavigableView<Game1ViewModel>
    {
        private PageBaseEvent baseEvent { get; set; }

        public Game1Page(Game1ViewModel viewModel)
        {
            ViewModel = viewModel;
            DataContext = this;
            InitializeComponent();

            ViewModel.Init("游戏1", GameView.AddTask);

            baseEvent = new(ViewModel);

            GameView.GameModelName.Text = "游戏1";

            //悬赏接受状态
            GameView.AddTask.XShang_Status.IsChecked = XConfig.LoadValueFromFile<bool>(ViewModel.GameName, "XShang");
            GameView.AddTask.ViewModel.XShangChecked = XConfig.LoadValueFromFile<bool>(ViewModel.GameName, "XShang");
            GameView.AddTask.XShang_Status.Checked += baseEvent.XShang_Status_Checked;
            GameView.AddTask.XShang_Status.Unchecked += baseEvent.XShang_Status_Checked;

            //庭院皮肤场景
            string GameScene_ComboBox_SelectedItem = XConfig.LoadValueFromFile<string>(ViewModel.GameName, "TYscene") ?? "";
            if (GameScene_ComboBox_SelectedItem != "")
            {
                GameView.AddTask.GameScene_ComboBox.SelectedValue = GameScene_ComboBox_SelectedItem;
                GameView.AddTask.ViewModel.Game_Scene = GameScene_ComboBox_SelectedItem;
            }
            else
                GameView.AddTask.GameScene_ComboBox.SelectedValue = "默认";

            //寄养队友名字：
            string Timer_JiYang_DesignatedName = XConfig.LoadValueFromFile<string>(ViewModel.GameName, "JiYang_DesignatedName") ?? "";
            if (Timer_JiYang_DesignatedName != "")
                GameView.AddTask.ViewModel.Timer_JiYang_DesignatedName = Timer_JiYang_DesignatedName;

            // 使用AddTaskPropertyViewModel的动态加载方法加载配置到当前实例
            GameView.AddTask.ViewModel.LoadConfig(ViewModel.GameName);
            GameView.AddTask.ViewModel.ResetAppStutas();

            GameView.AddTask.GameScene_ComboBox.SelectionChanged += baseEvent.GameScene_ComboBox_SelectionChanged;
        }

        public Game1ViewModel ViewModel { get; }
    }
}