using DanDing1.Models.Ws_Models;
using DanDing1.Views.Windows;
using DanDing1.ViewModels.Windows;
using Hardcodet.Wpf.TaskbarNotification;
using Microsoft.Extensions.DependencyInjection;
using System.Text;
using System.Windows.Controls;
using System.Windows.Threading;
using Wpf.Ui;
using XHelper;

namespace DanDing1.Services
{
    /// <summary>
    /// 托盘图标服务类，负责创建和管理托盘图标及菜单
    /// </summary>
    public class TrayIconService : IDisposable
    {
        // 单例模式实现
        private static readonly Lazy<TrayIconService> _instance = new Lazy<TrayIconService>(() =>
            new TrayIconService(App.GetService<IServiceProvider>()));

        // 服务提供者
        private readonly IServiceProvider _serviceProvider;

        // 标记是否已经释放资源
        private bool _disposed = false;

        // 标记是否已经显示过最小化提示
        private bool _hasShownMinimizeNotification = false;

        // 主窗口引用
        private MainWindow _mainWindow;

        // 超级多开窗口引用
        private SuperMultiGamesWindow _superMultiGamesWindow;

        // 定时调度窗口引用
        private SchedulerWindow _schedulerWindow;

        // 定时调度窗口视图模型引用
        private SchedulerWindowViewModel _schedulerViewModel;

        /// <summary>
        /// 获取调度器视图模型
        /// </summary>
        public SchedulerWindowViewModel SchedulerViewModel => GetSchedulerViewModel();

        /// <summary>
        /// 设置调度器视图模型实例
        /// </summary>
        /// <param name="viewModel">调度器视图模型</param>
        public void SetSchedulerViewModel(SchedulerWindowViewModel viewModel)
        {
            _schedulerViewModel = viewModel;
            XLogger.Info("已设置调度器视图模型实例");
        }

        // 托盘图标对象
        private TaskbarIcon _taskbarIcon;

        // 托盘提示更新定时器
        private DispatcherTimer _tooltipUpdateTimer;

        // 脚本状态上报服务
        private ScriptStatusReporterService _scriptStatusReporter;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        public TrayIconService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;

            // 在应用程序退出时确保释放资源
            Application.Current.Exit += (s, e) => Dispose();

            // 初始化托盘图标
            InitializeTaskbarIcon();

            // 初始化托盘提示更新定时器
            InitializeTooltipUpdateTimer();

            // 等待主窗口加载完成后再添加事件
            Application.Current.Dispatcher.BeginInvoke(new Action(() =>
            {
                // 获取主窗口实例
                _mainWindow = _serviceProvider.GetService<INavigationWindow>() as MainWindow;

                if (_mainWindow != null)
                {
                    // 监听主窗口状态变化
                    _mainWindow.StateChanged += MainWindow_StateChanged;
                }
                else
                {
                    XLogger.Warn("无法获取主窗口实例，最小化隐藏功能可能不可用");
                }
            }));
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~TrayIconService()
        {
            Dispose(false);
        }

        /// <summary>
        /// 获取托盘图标服务的单例实例
        /// </summary>
        public static TrayIconService Instance => _instance.Value;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        if (Application.Current != null && Application.Current.Dispatcher != null)
                        {
                            if (Application.Current.Dispatcher.CheckAccess())
                            {
                                // 清理定时调度窗口资源
                                if (_schedulerWindow != null)
                                {
                                    var viewModel = _schedulerWindow.ViewModel;
                                    viewModel?.Cleanup();
                                    _schedulerWindow = null;
                                }

                                StopTooltipUpdateTimer();
                                DisposeTaskbarIcon();
                            }
                            else
                            {
                                Application.Current.Dispatcher.Invoke(() =>
                                {
                                    // 清理定时调度窗口资源
                                    if (_schedulerWindow != null)
                                    {
                                        var viewModel = _schedulerWindow.ViewModel;
                                        viewModel?.Cleanup();
                                        _schedulerWindow = null;
                                    }

                                    StopTooltipUpdateTimer();
                                    DisposeTaskbarIcon();
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"释放托盘图标资源时发生错误: {ex.Message}");
                    }
                }

                _disposed = true;
            }
        }

        /// <summary>
        /// 释放托盘图标资源
        /// </summary>
        private void DisposeTaskbarIcon()
        {
            if (_taskbarIcon != null)
            {
                _taskbarIcon.Visibility = Visibility.Collapsed;
                _taskbarIcon.Dispose();
                _taskbarIcon = null;
                XLogger.Info("托盘图标资源已释放");
            }
        }

        /// <summary>
        /// 退出应用程序
        /// </summary>
        private async void ExitApplication()
        {
            try
            {
                XLogger.Info("正在退出应用程序...");

                // 停止所有运行中的脚本任务，但不关闭模拟器
                StopAllScripts();

                // 如果调度器视图模型存在，调用其清理方法
                var schedulerViewModel = GetSchedulerViewModel();
                if (schedulerViewModel != null)
                {
                    XLogger.Info("正在清理调度器资源...");
                    await schedulerViewModel.CleanupAsync();
                    XLogger.Info("调度器资源已清理完成，模拟器保持运行状态");
                }

                // 退出应用程序
                XLogger.Info("应用程序即将关闭，模拟器将继续运行");
                Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                XLogger.Error($"退出应用程序时发生错误: {ex.Message}");
                // 即使发生错误也尝试关闭应用程序
                Application.Current.Shutdown();
            }
        }

        /// <summary>
        /// 获取脚本状态上报服务
        /// </summary>
        private ScriptStatusReporterService GetScriptStatusReporter()
        {
            if (_scriptStatusReporter == null)
            {
                _scriptStatusReporter = _serviceProvider.GetService<ScriptStatusReporterService>();
            }
            return _scriptStatusReporter;
        }

        /// <summary>
        /// 获取调度器窗口视图模型
        /// </summary>
        private SchedulerWindowViewModel GetSchedulerViewModel()
        {
            if (_schedulerViewModel == null)
            {
                // 尝试从现有窗口获取
                if (_schedulerWindow != null)
                {
                    _schedulerViewModel = _schedulerWindow.ViewModel;
                }
                else
                {
                    // 直接使用单例实例而不是通过依赖注入
                    _schedulerViewModel = DanDing1.ViewModels.Windows.SchedulerWindowViewModel.Instance;
                }
            }
            return _schedulerViewModel;
        }

        /// <summary>
        /// 初始化托盘图标
        /// </summary>
        private void InitializeTaskbarIcon()
        {
            try
            {
                // 确保在UI线程上创建托盘图标
                if (!Application.Current.Dispatcher.CheckAccess())
                {
                    Application.Current.Dispatcher.Invoke(InitializeTaskbarIcon);
                    return;
                }

                // 创建托盘图标
                _taskbarIcon = new TaskbarIcon();

                // 加载图标
                try
                {
                    // 使用应用程序关联图标作为托盘图标
                    var appIcon = System.Drawing.Icon.ExtractAssociatedIcon(System.Reflection.Assembly.GetExecutingAssembly().Location);
                    if (appIcon != null)
                    {
                        _taskbarIcon.Icon = appIcon;
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Error($"加载托盘图标失败: {ex.Message}");
                }

                // 设置初始提示文本
                _taskbarIcon.ToolTipText = "蛋定助手 - 未执行脚本任务";

                // 创建上下文菜单
                var contextMenu = new ContextMenu();

                // 添加菜单项
                var openMenuItem = new MenuItem { Header = "打开" };
                openMenuItem.Click += (s, e) => ShowMainWindow();

                var multiGameMenuItem = new MenuItem { Header = "超级多开" };
                multiGameMenuItem.Click += (s, e) => ShowSuperMultiWindow();

                var schedulerMenuItem = new MenuItem { Header = "定时调度" };
                schedulerMenuItem.Click += (s, e) => ShowSchedulerWindow();

                var preferencesMenuItem = new MenuItem { Header = "我的偏好" };
                preferencesMenuItem.Click += (s, e) => ShowPreferencesPage();

                var settingsMenuItem = new MenuItem { Header = "常规设置" };
                settingsMenuItem.Click += (s, e) => ShowSettingsPage();

                var exitMenuItem = new MenuItem { Header = "退出" };
                exitMenuItem.Click += (s, e) => ExitApplication();

                // 添加菜单项到上下文菜单
                contextMenu.Items.Add(openMenuItem);
                contextMenu.Items.Add(multiGameMenuItem);
                contextMenu.Items.Add(schedulerMenuItem);
                contextMenu.Items.Add(new Separator());
                contextMenu.Items.Add(preferencesMenuItem);
                contextMenu.Items.Add(settingsMenuItem);
                contextMenu.Items.Add(new Separator());
                contextMenu.Items.Add(exitMenuItem);

                // 设置上下文菜单
                _taskbarIcon.ContextMenu = contextMenu;

                // 设置双击事件
                _taskbarIcon.TrayMouseDoubleClick += (s, e) => ShowMainWindow();

                // 确保托盘图标可见
                _taskbarIcon.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                XLogger.Error($"托盘图标服务初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化托盘提示更新定时器
        /// </summary>
        private void InitializeTooltipUpdateTimer()
        {
            try
            {
                // 确保在UI线程上创建定时器
                if (!Application.Current.Dispatcher.CheckAccess())
                {
                    Application.Current.Dispatcher.Invoke(InitializeTooltipUpdateTimer);
                    return;
                }

                _tooltipUpdateTimer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(2) // 每2秒更新一次
                };

                _tooltipUpdateTimer.Tick += (s, e) => UpdateTooltipText();
                _tooltipUpdateTimer.Start();

                //XLogger.Info("托盘提示更新定时器已启动");
            }
            catch (Exception ex)
            {
                XLogger.Error($"初始化托盘提示更新定时器时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理主窗口状态变化事件
        /// </summary>
        private void MainWindow_StateChanged(object sender, EventArgs e)
        {
            try
            {
                if (sender is Window window && window.WindowState == WindowState.Minimized)
                {
                    // 隐藏主窗口而不是最小化
                    window.Hide();

                    // 如果是首次最小化，显示提示
                    if (!_hasShownMinimizeNotification)
                    {
                        ShowMinimizeNotification();
                        _hasShownMinimizeNotification = true;
                    }
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"处理窗口状态变化时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示主窗口
        /// </summary>
        private void ShowMainWindow()
        {
            try
            {
                if (_mainWindow == null)
                {
                    _mainWindow = _serviceProvider.GetService<INavigationWindow>() as MainWindow;
                }

                if (_mainWindow != null)
                {
                    // 确保窗口可见
                    _mainWindow.Show();

                    // 如果窗口是最小化状态，恢复为正常状态
                    if (_mainWindow.WindowState == WindowState.Minimized)
                    {
                        _mainWindow.WindowState = WindowState.Normal;
                    }

                    _mainWindow.Activate();
                    _mainWindow.Focus();
                }
                else
                {
                    XLogger.Warn("无法获取主窗口实例");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"显示主窗口时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示最小化提示
        /// </summary>
        private void ShowMinimizeNotification()
        {
            _taskbarIcon?.ShowBalloonTip("蛋定助手已最小化", "您可以通过点击托盘图标打开蛋定助手哦~", BalloonIcon.Info);
        }

        /// <summary>
        /// 显示偏好页面
        /// </summary>
        private void ShowPreferencesPage()
        {
            try
            {
                ShowMainWindow();

                if (_mainWindow != null)
                {
                    // 导航到偏好页面
                    _mainWindow.Navigate(typeof(DanDing1.Views.Pages.UserConfigPage));
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"导航到偏好页面时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示设置页面
        /// </summary>
        private void ShowSettingsPage()
        {
            try
            {
                ShowMainWindow();

                if (_mainWindow != null)
                {
                    // 导航到设置页面
                    _mainWindow.Navigate(typeof(DanDing1.Views.Pages.SettingsPage));
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"导航到设置页面时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示超级多开窗口
        /// </summary>
        private void ShowSuperMultiWindow()
        {
            try
            {
                if (_superMultiGamesWindow == null)
                {
                    _superMultiGamesWindow = _serviceProvider.GetService<SuperMultiGamesWindow>();
                }

                if (_superMultiGamesWindow != null)
                {
                    if (_superMultiGamesWindow.WindowState == WindowState.Minimized)
                    {
                        _superMultiGamesWindow.WindowState = WindowState.Normal;
                    }

                    _superMultiGamesWindow.Show();
                    _superMultiGamesWindow.Activate();
                    _superMultiGamesWindow.Focus();
                }
                else
                {
                    XLogger.Warn("无法获取超级多开窗口实例");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"显示超级多开窗口时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示定时调度窗口
        /// </summary>
        public void ShowSchedulerWindow()
        {
            try
            {
                // 如果窗口不存在，则创建
                if (_schedulerWindow == null || !_schedulerWindow.IsLoaded)
                {
                    // 先获取调度器窗口视图模型
                    var viewModel = _serviceProvider.GetService<SchedulerWindowViewModel>();
                    if (viewModel == null)
                    {
                        // 如果从主要服务提供者获取失败，则尝试从 Application.Current.Resources["ServiceProvider"] 获取
                        if (Application.Current.Resources.Contains("ServiceProvider"))
                        {
                            var appServiceProvider = Application.Current.Resources["ServiceProvider"] as IServiceProvider;
                            if (appServiceProvider != null)
                            {
                                viewModel = appServiceProvider.GetService(typeof(SchedulerWindowViewModel)) as SchedulerWindowViewModel;
                            }
                        }
                    }

                    if (viewModel == null)
                    {
                        XLogger.Warn("无法获取定时调度窗口视图模型");
                        return;
                    }

                    // 创建调度器窗口
                    _schedulerWindow = new SchedulerWindow(viewModel);

                    // 记录视图模型引用
                    _schedulerViewModel = viewModel;

                    // 窗口关闭时处理
                    _schedulerWindow.Closed += (s, e) =>
                    {
                        _schedulerWindow = null;
                        _schedulerViewModel = null;
                    };

                    // 显示窗口
                    _schedulerWindow.Show();
                }
                else
                {
                    if (_schedulerWindow.WindowState == WindowState.Minimized)
                    {
                        _schedulerWindow.WindowState = WindowState.Normal;
                    }

                    _schedulerWindow.Show();
                    _schedulerWindow.Activate();
                    _schedulerWindow.Focus();

                    // 确保我们有视图模型引用
                    if (_schedulerViewModel == null)
                    {
                        _schedulerViewModel = _schedulerWindow.ViewModel;
                    }
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"显示定时调度窗口时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止所有运行中的脚本任务，但不关闭模拟器
        /// </summary>
        private void StopAllScripts()
        {
            try
            {
                // 停止游戏1的脚本
                if (GlobalData.Instance?.Game1RunningConfig?.isRunning ?? false)
                {
                    Scripts.Stop(0, out _);
                }

                // 停止游戏2的脚本
                if (GlobalData.Instance?.Game2RunningConfig?.isRunning ?? false)
                {
                    Scripts.Stop(1, out _);
                }

                // 停止游戏3的脚本
                if (GlobalData.Instance?.Game3RunningConfig?.isRunning ?? false)
                {
                    Scripts.Stop(2, out _);
                }

                // 停止游戏4的脚本
                if (GlobalData.Instance?.Game4RunningConfig?.isRunning ?? false)
                {
                    Scripts.Stop(3, out _);
                }

                // 停止超级多开中的所有脚本
                var superMultiVM = _serviceProvider.GetService<ViewModels.Windows.SuperMultiGamesWindowViewModel>();
                if (superMultiVM != null)
                {
                    foreach (var game in superMultiVM.SuperMultiGame_DataModelCollection)
                    {
                        if (game.RunningStatus == "运行中")
                        {
                            // 使用脚本ID停止脚本
                            int scriptId = superMultiVM.GetType().GetMethod("GetScriptId", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance).Invoke(superMultiVM, new object[] { game.GameId }) as int? ?? -1;
                            if (scriptId >= 0)
                            {
                                Scripts.Stop(scriptId, out _);
                            }
                        }
                    }
                }

                XLogger.Info("已停止所有运行中的脚本任务，模拟器保持运行状态");
            }
            catch (Exception ex)
            {
                XLogger.Error($"停止脚本时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止托盘提示更新定时器
        /// </summary>
        private void StopTooltipUpdateTimer()
        {
            if (_tooltipUpdateTimer != null && _tooltipUpdateTimer.IsEnabled)
            {
                _tooltipUpdateTimer.Stop();
                XLogger.Info("托盘提示更新定时器已停止");
            }
        }

        /// <summary>
        /// 强制更新托盘提示文本
        /// </summary>
        public void ForceUpdateTooltipText()
        {
            try
            {
                if (Application.Current.Dispatcher.CheckAccess())
                {
                    UpdateTooltipText();
                }
                else
                {
                    Application.Current.Dispatcher.Invoke(UpdateTooltipText);
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"强制更新托盘提示文本时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新托盘图标提示文本
        /// </summary>
        private void UpdateTooltipText()
        {
            try
            {
                if (_taskbarIcon == null) return;

                // 获取调度器状态
                var schedulerViewModel = GetSchedulerViewModel();
                bool hasSchedulerInfo = false;
                string schedulerStatus = "未启动";
                string activeEmulatorsInfo = "";

                if (schedulerViewModel != null)
                {
                    schedulerStatus = schedulerViewModel.SchedulerStatus;
                    activeEmulatorsInfo = schedulerViewModel.ActiveEmulators;
                    hasSchedulerInfo = true;
                }

                // 获取脚本状态上报服务
                var statusReporter = GetScriptStatusReporter();
                if (statusReporter == null && !hasSchedulerInfo)
                {
                    _taskbarIcon.ToolTipText = "蛋定助手 - 未执行脚本任务";
                    return;
                }

                // 获取所有脚本状态
                var scriptStatuses = statusReporter?.GetAllScriptStatuses();
                bool hasRunningScripts = scriptStatuses != null && scriptStatuses.Count > 0 &&
                                         scriptStatuses.Any(s =>
                                             s.Status == ScriptRuntimeStatus.Running ||
                                             s.Status == ScriptRuntimeStatus.Starting ||
                                             s.Status == ScriptRuntimeStatus.Waiting ||
                                             s.Status == ScriptRuntimeStatus.Paused);

                // 构建提示文本
                var tooltipBuilder = new StringBuilder();

                // 添加基本标题
                tooltipBuilder.AppendLine("蛋定助手");

                // 添加调度器状态信息
                if (hasSchedulerInfo)
                {
                    tooltipBuilder.AppendLine($"调度器状态: {schedulerStatus}");
                    if (!string.IsNullOrEmpty(activeEmulatorsInfo))
                    {
                        tooltipBuilder.AppendLine($"在线模拟器: {activeEmulatorsInfo}");
                    }

                    // 如果有下一个任务信息且不是"无待执行任务"，添加该信息
                    if (schedulerViewModel != null &&
                        !string.IsNullOrEmpty(schedulerViewModel.NextTaskInfo) &&
                        schedulerViewModel.NextTaskInfo != "无待执行任务")
                    {
                        tooltipBuilder.AppendLine($"下一个任务: {schedulerViewModel.NextTaskInfo}");
                    }
                }

                // 如果没有运行的脚本，也没有调度器信息
                if (!hasRunningScripts && !hasSchedulerInfo)
                {
                    _taskbarIcon.ToolTipText = "蛋定助手 - 未执行脚本任务";
                    return;
                }

                // 如果有运行的脚本，添加脚本信息
                if (hasRunningScripts)
                {
                    // 筛选出正在运行的脚本
                    var runningScripts = scriptStatuses.Where(s =>
                        s.Status == ScriptRuntimeStatus.Running ||
                        s.Status == ScriptRuntimeStatus.Starting ||
                        s.Status == ScriptRuntimeStatus.Waiting ||
                        s.Status == ScriptRuntimeStatus.Paused).ToList();

                    tooltipBuilder.AppendLine("运行中的脚本:");

                    foreach (var script in runningScripts)
                    {
                        string taskInfo = !string.IsNullOrEmpty(script.CurrentTask)
                            ? script.CurrentTask
                            : "等待中";

                        string statusInfo = string.Empty;
                        switch (script.Status)
                        {
                            case ScriptRuntimeStatus.Paused:
                                statusInfo = "已暂停";
                                break;

                            case ScriptRuntimeStatus.Waiting:
                                statusInfo = "等待中";
                                break;

                            case ScriptRuntimeStatus.Starting:
                                statusInfo = "启动中";
                                break;
                        }

                        if (!string.IsNullOrEmpty(statusInfo))
                        {
                            taskInfo = $"{taskInfo} ({statusInfo})";
                        }

                        if (!string.IsNullOrEmpty(script.Message))
                        {
                            taskInfo = $"{taskInfo} - {script.Message}";
                        }

                        // 添加运行时间显示
                        string runTimeInfo = string.Empty;
                        if (script.RunningDurationSeconds.HasValue && script.RunningDurationSeconds.Value > 0)
                        {
                            TimeSpan runTime = TimeSpan.FromSeconds(script.RunningDurationSeconds.Value);
                            if (runTime.TotalHours >= 1)
                            {
                                runTimeInfo = $"[{runTime.Hours}时{runTime.Minutes}分{runTime.Seconds}秒]";
                            }
                            else if (runTime.TotalMinutes >= 1)
                            {
                                runTimeInfo = $"[{runTime.Minutes}分{runTime.Seconds}秒]";
                            }
                            else
                            {
                                runTimeInfo = $"[{runTime.Seconds}秒]";
                            }
                        }

                        tooltipBuilder.AppendLine($"{script.FriendlyName} - 正在执行：{taskInfo} {runTimeInfo}".TrimEnd());
                    }
                }

                // 更新提示文本
                _taskbarIcon.ToolTipText = tooltipBuilder.ToString().TrimEnd();
            }
            catch (Exception ex)
            {
                XLogger.Error($"更新托盘图标提示文本时发生错误: {ex.Message}");

                // 出错时设置默认提示文本
                if (_taskbarIcon != null)
                {
                    _taskbarIcon.ToolTipText = "蛋定助手";
                }
            }
        }
    }
}