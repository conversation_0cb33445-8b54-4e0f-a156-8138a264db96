﻿#pragma checksum "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "94DDE5CDA700A1B798A8B4E014410FC45EDD3A9E"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Views.Windows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Windows {
    
    
    /// <summary>
    /// MySharesWindow
    /// </summary>
    public partial class MySharesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 51 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SortByComboBox;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox OrderComboBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SharesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingGrid;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid NoDataGrid;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PageInfoText;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrevButton;
        
        #line default
        #line hidden
        
        
        #line 284 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/windows/cloudconfig/myshareswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 55 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SortByComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 65 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
            this.SortByComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SortByComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.OrderComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 73 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
            this.OrderComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.OrderComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 81 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SharesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 9:
            this.LoadingGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 10:
            this.NoDataGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 11:
            this.PageInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.PrevButton = ((System.Windows.Controls.Button)(target));
            
            #line 283 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
            this.PrevButton.Click += new System.Windows.RoutedEventHandler(this.PrevButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.NextButton = ((System.Windows.Controls.Button)(target));
            
            #line 288 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
            this.NextButton.Click += new System.Windows.RoutedEventHandler(this.NextButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 6:
            
            #line 208 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyCodeButton_Click);
            
            #line default
            #line hidden
            break;
            case 7:
            
            #line 214 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExpireNowButton_Click);
            
            #line default
            #line hidden
            break;
            case 8:
            
            #line 223 "..\..\..\..\..\..\..\Views\Windows\CloudConfig\MySharesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

