﻿using DanDing1.Models.Ws_Models;
using ScriptEngine;
using ScriptEngine.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace DanDing1
{
    /// <summary>
    /// 蛋定脚本管理
    /// </summary>
    internal class Scripts
    {
        /// <summary>
        /// 线程同步锁
        /// </summary>
        private static readonly object _lock = new object();

        /// <summary>
        /// 脚本信息封装类
        /// </summary>
        private class ScriptInfo
        {
            public int Id { get; set; }
            public DDScript Script { get; } = new DDScript();
            public DDBuilder Builder { get; set; }
            public TaskConfigsModel TaskConfig { get; set; }
            public bool IsRunning { get; set; }
        }

        /// <summary>
        /// 脚本信息集合
        /// </summary>
        private static readonly Dictionary<string, ScriptInfo> _scriptInfos = new Dictionary<string, ScriptInfo>();

        /// <summary>
        /// 现存脚本名
        /// </summary>
        private static readonly List<string> _gameScriptNames = new List<string>();

        /// <summary>
        /// 存储脚本任务结束回调函数
        /// </summary>
        private static Action<object, ScriptCallBackDataModel> _scriptTaskEndedCallback;

        /// <summary>
        /// 添加任务名
        /// </summary>
        /// <param name="name">任务名称</param>
        /// <returns>返回id，-1表示已存在</returns>
        public static int AddScriptName(string name)
        {
            lock (_lock)
            {
                if (_gameScriptNames.Contains(name))
                    return -1;

                _gameScriptNames.Add(name);
                _scriptInfos.Add(name, new ScriptInfo());
                return _gameScriptNames.IndexOf(name);
            }
        }

        /// <summary>
        /// 根据标识符获取脚本名称
        /// </summary>
        /// <param name="identifier">字符串名称或整数ID</param>
        /// <returns>脚本名称</returns>
        private static string GetScriptName(object identifier)
        {
            lock (_lock)
            {
                if (identifier is string)
                    return (string)identifier;

                int index = (int)identifier;
                if (index < 0 || index >= _gameScriptNames.Count)
                {
                    XLogger.Error($"脚本ID索引越界: {index}, 当前脚本列表大小: {_gameScriptNames.Count}");
                    return string.Empty;
                }

                return _gameScriptNames[index];
            }
        }

        /// <summary>
        /// 设置任务参数
        /// </summary>
        /// <param name="identifier">任务标识符（名称或ID）</param>
        /// <param name="dDBuilder">任务参数</param>
        public static void SetBuilder(object identifier, DDBuilder dDBuilder)
        {
            string name = GetScriptName(identifier);

            // 如果名称为空则提前返回
            if (string.IsNullOrEmpty(name))
            {
                XLogger.Error($"无法设置任务参数，脚本名称为空，标识符：{identifier}");
                return;
            }

            lock (_lock)
            {
                if (!CheckHaveScript(name)) return;

                _scriptInfos[name].Builder = dDBuilder;
            }
        }

        /// <summary>
        /// 设置任务参数
        /// </summary>
        /// <param name="identifier">任务标识符（名称或ID）</param>
        /// <param name="taskConfigsModel">任务配置</param>
        public static void SetTaskConfig(object identifier, TaskConfigsModel taskConfigsModel)
        {
            string name = GetScriptName(identifier);

            lock (_lock)
            {
                if (!CheckHaveScript(name)) return;

                _scriptInfos[name].TaskConfig = taskConfigsModel;
            }
        }

        /// <summary>
        /// 设置任务完成回调
        /// </summary>
        /// <param name="name">任务名称</param>
        /// <param name="action">回调方法</param>
        public static void SetScrpitCallBack(string name, Action action)
        {
            lock (_lock)
            {
                if (!CheckHaveScript(name)) return;

                _scriptInfos[name].Script.TaskEnded = () => { action.Invoke(); };
            }
        }

        /// <summary>
        /// 设置任务完成回调
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="action">回调方法</param>
        public static void SetScrpitCallBack(int id, Action action)
        {
            lock (_lock)
            {
                if (!CheckHaveScript(id)) return;

                string name = _gameScriptNames[id];
                _scriptInfos[name].Script.TaskEnded = () => { action.Invoke(); };
            }
        }

        /// <summary>
        /// 设置任务完成回调
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="action">带数据的回调方法</param>
        public static void SetScrpitCallBack_Data(int id, TaskEnded_CallBackData action)
        {
            lock (_lock)
            {
                if (!CheckHaveScript(id)) return;

                string name = _gameScriptNames[id];
                _scriptInfos[name].Script.TaskEnded_Data = action;
            }
        }

        /// <summary>
        /// 任务通知获取回调
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="action">通知回调</param>
        public static void SetTaskUserNotificationMessage(int id, TaskUserNotificationMessage action)
        {
            lock (_lock)
            {
                if (!CheckHaveScript(id)) return;

                string name = _gameScriptNames[id];
                _scriptInfos[name].Script.TaskUserNotificationMessage = action;
            }
        }

        /// <summary>
        /// 启动任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="errorstr">错误信息</param>
        /// <returns>是否成功启动</returns>
        public static bool Start(int id, out string errorstr)
        {
            errorstr = "";

            lock (_lock)
            {
                if (!CheckHaveScript(id))
                {
                    errorstr = "不存在此任务！无法开始任务..";
                    return false;
                }

                string name = _gameScriptNames[id];
                var scriptInfo = _scriptInfos[name];

                if (scriptInfo.IsRunning)
                {
                    errorstr = "任务正在执行中！";
                    return false;
                }

                var manager = scriptInfo.Script;
                TaskEnded_CallBack tmpcall = manager.TaskEnded;

                if (tmpcall is null)
                    manager.TaskEnded = () =>
                    {
                        scriptInfo.IsRunning = false;
                        TriggerTaskEndedCallback(id);
                    };
                else
                    manager.TaskEnded = () =>
                    {
                        tmpcall.Invoke();
                        scriptInfo.IsRunning = false;
                        TriggerTaskEndedCallback(id);
                    };

                if (scriptInfo.Builder == null)
                {
                    errorstr = $"无法继续！没有此任务的参数！[id:{id}]";
                    return false;
                }

                manager.Start(scriptInfo.Builder);
                scriptInfo.IsRunning = true;
                return true;
            }
        }

        /// <summary>
        /// 停止任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="errorstr">错误信息</param>
        /// <returns>是否成功停止</returns>
        public static bool Stop(int id, out string errorstr)
        {
            errorstr = "";

            lock (_lock)
            {
                if (!CheckHaveScript(id))
                {
                    errorstr = "不存在此任务！无法停止任务..";
                    return false;
                }

                string name = _gameScriptNames[id];
                var scriptInfo = _scriptInfos[name];

                if (!scriptInfo.IsRunning)
                {
                    errorstr = "任务未开始！";
                    return false;
                }

                // 用户取消
                TriggerTaskEndedCallback(id, isCancel: true);

                scriptInfo.Script?.Stop();
                scriptInfo.IsRunning = false;
                return true;
            }
        }

        /// <summary>
        /// 异步停止任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>是否成功停止</returns>
        public static async Task<bool> StopAsync(int id)
        {
            string name;
            ScriptInfo scriptInfo;

            lock (_lock)
            {
                if (!CheckHaveScript(id))
                    return false;

                name = _gameScriptNames[id];
                scriptInfo = _scriptInfos[name];

                if (!scriptInfo.IsRunning)
                    return false;
            }

            try
            {
                await scriptInfo.Script.Stop().ConfigureAwait(false);

                // 用户取消
                TriggerTaskEndedCallback(id, isCancel: true);

                lock (_lock)
                {
                    scriptInfo.IsRunning = false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 当前是否有此脚本控制类
        /// </summary>
        /// <param name="name">脚本名称</param>
        /// <returns>是否存在</returns>
        private static bool CheckHaveScript(string name)
        {
            return _scriptInfos.ContainsKey(name) && _gameScriptNames.Contains(name);
        }

        /// <summary>
        /// 当前是否有此脚本控制类
        /// </summary>
        /// <param name="id">脚本ID</param>
        /// <returns>是否存在</returns>
        private static bool CheckHaveScript(int id)
        {
            return id >= 0 && id < _gameScriptNames.Count && _gameScriptNames[id] != null;
        }

        /// <summary>
        /// id任务是否在执行中
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="errorstr">错误信息</param>
        /// <returns>是否正在运行</returns>
        public static bool IsRunning(int id, out string errorstr)
        {
            errorstr = "";

            lock (_lock)
            {
                if (!CheckHaveScript(id))
                {
                    errorstr = "不存在此任务！无法停止任务..";
                    return false;
                }

                string name = _gameScriptNames[id];
                var scriptInfo = _scriptInfos[name];

                if (!scriptInfo.IsRunning)
                {
                    errorstr = $"没有正在运行的任务！[id:{id}]";
                }

                return scriptInfo.IsRunning;
            }
        }

        /// <summary>
        /// 停止所有正在运行的脚本
        /// </summary>
        /// <returns>停止成功的脚本数量</returns>
        public static int StopAllScripts()
        {
            int stoppedCount = 0;
            lock (_lock)
            {
                foreach (var scriptName in _gameScriptNames)
                {
                    if (_scriptInfos.TryGetValue(scriptName, out var scriptInfo) && scriptInfo.IsRunning)
                    {
                        try
                        {
                            // 取消脚本任务
                            scriptInfo.Script.Stop();

                            // 用户取消
                            TriggerTaskEndedCallback(scriptInfo.Id, isCancel: true);

                            scriptInfo.IsRunning = false;
                            stoppedCount++;
                            XLogger.Info($"已停止脚本: {scriptName}");
                        }
                        catch (Exception ex)
                        {
                            XLogger.Error($"停止脚本 {scriptName} 时出错: {ex.Message}");
                        }
                    }
                }
            }

            return stoppedCount;
        }

        /// <summary>
        /// 设置脚本任务结束回调函数
        /// </summary>
        /// <param name="scriptTaskEndedCallback">回调函数，参数为发送者和脚本回调数据模型</param>
        internal static void SetScriptCallBack_Data(Action<object, ScriptCallBackDataModel> scriptTaskEndedCallback)
        {
            _scriptTaskEndedCallback = scriptTaskEndedCallback;
        }

        /// <summary>
        /// 触发脚本任务结束回调
        /// </summary>
        /// <param name="scriptId">脚本ID</param>
        /// <param name="isCancel">是否取消</param>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="endReason">结束原因</param>
        internal static void TriggerTaskEndedCallback(int scriptId, bool isCancel = false, string errorMessage = null, ScriptTaskEndReason endReason = ScriptTaskEndReason.Normal)
        {
            try
            {
                if (_scriptTaskEndedCallback != null)
                {
                    var callbackData = new ScriptCallBackDataModel
                    {
                        ScriptId = scriptId,
                        IsCancel = isCancel,
                        ErrorMessage = errorMessage,
                        EndReason = endReason
                    };

                    _scriptTaskEndedCallback.Invoke(null, callbackData);
                }
            }
            catch (Exception ex)
            {
                // 记录异常但不抛出，避免影响主流程
                System.Diagnostics.Debug.WriteLine($"触发脚本任务结束回调异常: {ex.Message}");
            }
        }
    }
}