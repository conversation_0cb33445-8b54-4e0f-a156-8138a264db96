using System;

namespace XHelper.Models
{
    /// <summary>
    /// 定义日志转发器的接口，允许将日志转发到外部目标
    /// </summary>
    public interface ILogForwarder
    {
        /// <summary>
        /// 将日志条目加入队列等待发送
        /// </summary>
        /// <param name="timestamp">日志时间戳</param>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="taskId">可选的任务ID</param>
        void EnqueueLog(DateTime timestamp, string level, string message, string? taskId = null);
    }
} 