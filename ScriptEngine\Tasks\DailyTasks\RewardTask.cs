﻿using DamoControlKit.Model;
using ScriptEngine.Factorys;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using XHelper.Models;

namespace ScriptEngine.Tasks.DailyTasks
{
    internal class RewardTask : BaseTask
    {
        private Dictionary<string, Position> ClickPos_Pairs = new Dictionary<string, Position>()
        {
            {"开启一键追踪",new(1114,595,1179,643) },//如果使用图片查找到了一键追踪，则点击该位置
            {"关闭悬赏封印",new(1162,121,1200,153) },
        };

        private Dictionary<string, Position> Find_Pairs = new()
        {
            {"是否打开了悬赏封印",new(522,57,771,101) }, //如果OCR出现了悬赏封印字样，则表示悬赏已打开
            {"是否存在悬赏封印怪物",new(8,141,134,529) }, //需要在探索界面OCR识别这个范围
            {"悬赏返回_挑战",new(1043,566,1160,654) },
        };

        private bool goTY = false;

        private string TaskType = "";

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, className);
        }

        /// <summary>
        /// 点击前往的坐标位置
        /// </summary>
        private readonly Dictionary<(int, int), Point> pointDict = new Dictionary<(int, int), Point>()
        {
            {(225,289),new(1022,255) },
            {(290,360),new(1019,330) },
            {(361,430),new(1021,397) },
            {(431,505),new(1019,470) },
        };

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            bool retry1 = false;
        ReDo:
            if (Scene.NowScene == "庭院")
            {
                if (!Mp.Filter("封印入口").FindAllAndClick())
                {
                    if (retry1)
                    {
                        log.Error("当前在庭院，但是没有找到悬赏封印的点击位置，结束任务..");
                        return;
                    }
                    log.Warn("当前在庭院，但是没有找到悬赏封印的点击位置，尝试重新进入一次庭院..");
                    Scene.TO.TanSuo();
                    Sleep(1500);
                    Scene.TO.TingYuan();
                    Sleep(1500);
                    retry1 = true;
                    goto ReDo;
                }
                Sleep(1000);
                log.Info("判断是否打开了悬赏封印界面...");
                if (!NowisXuanShang())
                {
                    log.Warn("当前在庭院，刚刚已经点击了悬赏封印入口，但是当前位置不是在悬赏封印界面，结束任务..");
                    return;
                }
                log.Info("点击一键追踪...");
                if (Mp.Filter("一键追踪").FindAll())
                {
                    Fast.Click(ClickPos_Pairs["开启一键追踪"]);
                    Sleep(1000);
                }
                else
                {
                    Fast.Click(ClickPos_Pairs["开启一键追踪"]);
                    Sleep(1000);
                    if (Mp.Filter("一键追踪").FindAll())
                    {
                        Fast.Click(ClickPos_Pairs["开启一键追踪"]);
                        Sleep(1000);
                    }
                    else
                    {
                        log.Warn("没有找到 一键追踪 按钮，可能是任务清完了,结束任务..");
                        Fast.Click(ClickPos_Pairs["关闭悬赏封印"]);
                        Sleep(1000);
                        return;
                    }
                }
                log.Info("关闭悬赏封印...并去探索场景..");
                Fast.Click(ClickPos_Pairs["关闭悬赏封印"]);
                Sleep(1000);
                if (!Scene.TO.TanSuo())
                {
                    log.Warn("无法前往探索界面，结束任务..");
                    return;
                }
            }
        ReFind:

            if (Scene.NowScene == "探索")
            {
                PickJL();
                if (!Fast.Ocr_String(Find_Pairs["是否存在悬赏封印怪物"]).Contains("悬赏"))
                    Operational.Slide_Pos(new Position(28, 501, 119, 524), new(40, 165, 108, 181));

                List<XOcr_TextBlock>? _t = null;
                Fast.Ocr_String(Find_Pairs["是否存在悬赏封印怪物"], t => _t = t);

                bool nowisXie = false;
                int i = 0;
                foreach (var item in _t ?? [])
                {
                    if (item.Text.Contains("悬赏"))
                    {
                        log.Info("判断悬赏任务是否为协同任务..");
                        if (!nowisXie && i > 0 && (_t?[i - 1].Text.Contains("协") ?? false))
                        {
                            log.Info("目标任务是协同任务..请自行解决..");
                            nowisXie = true;
                        }
                        if (!nowisXie && i + 1 < (_t?.Count ?? 0) && (_t?[i + 1].Text.Contains('/') ?? false))
                        {
                            var vs = _t[i + 1].Text.Split('/');
                            if (vs.Length == 2 && int.TryParse(vs[0], out int num) && int.TryParse(vs[1], out int den))
                                if (num == den)
                                {
                                    nowisXie = true;
                                    log.Info("目标任务是协同任务..请自行解决..");
                                }
                        }
                        goTY = true;
                        log.Info("打开一个悬赏任务..");

                        Fast.Click(Find_Pairs["是否存在悬赏封印怪物"].X + item.Center.X, Find_Pairs["是否存在悬赏封印怪物"].Y + item.Center.Y);
                        Sleep(1000);
                        if (nowisXie)
                        {
                            log.Warn("未知的任务类型，取消追踪..");
                            Fast.Click(186, 201);
                            Sleep(1000);
                            Fast.Click(1171, 129);
                            Sleep(1000);
                            nowisXie = false;
                            break;
                        }
                        log.Info("判断任务类型..");
                        List<XOcr_TextBlock>? ft = null;
                        string s1 = Fast.Ocr_String(519, 225, 948, 512, t => ft = t);

                        // 按优先级排序任务类型：挑战 > 秘闻 > 探索
                        if (ft != null && ft.Count > 0)
                        {
                            // 创建任务类型优先级字典
                            Dictionary<string, int> priorityMap = new Dictionary<string, int>
                            {
                                { "挑战", 3 },
                                { "秘闻", 1 },
                                { "探索", 2 }
                            };

                            // 按优先级排序
                            ft = ft.OrderByDescending(item =>
                            {
                                foreach (var priority in priorityMap)
                                {
                                    if (item.Text.Contains(priority.Key))
                                        return priority.Value;
                                }
                                return 0;
                            }).ToList();

                            foreach (var it in ft)
                                it.SetBasePoint(519, 225);
                        }
                        else
                        {
                            log.Warn("未知的任务类型，取消追踪..");
                            Fast.Click(186, 201);
                            Sleep(1000);
                            Fast.Click(1171, 129);
                            Sleep(1000);
                            break;
                        }

                        log.Info($"判断最优的任务类型为：{ft?[0].Text} ???");
                        string s = ft?[0].Text ?? "";

                        if (s.Contains("秘闻"))
                            TaskType = "秘闻";
                        else if (s.Contains("挑战"))
                            TaskType = "挑战";
                        else if (s.Contains("探索"))
                            TaskType = "探索";
                        else if (s.Contains("御魂"))
                            TaskType = "御魂";
                        else
                            TaskType = "未知";

                        if (TaskType == "未知")
                        {
                            log.Warn("未知的任务类型，取消追踪..");
                            Fast.Click(186, 201);
                            Sleep(1000);
                            Fast.Click(1171, 129);
                            Sleep(1000);
                            break;
                        }
                        else
                        {
                            log.Info($"任务类型：{TaskType}，点击前往..");
                            foreach (var dc in pointDict)
                            {
                                if (ft?[0].Center.Y > dc.Key.Item1 && ft?[0].Center.Y < dc.Key.Item2)
                                {
                                    Fast.Click(dc.Value.X, dc.Value.Y);
                                    break;
                                }
                            }
                            Sleep(1500);
                            break;
                        }
                    }
                    i++;
                }

                if (!goTY && TaskType == "")
                {
                    goTY = true;
                    Scene.TO.TingYuan();
                    goto ReDo;
                }
                DoTask();
                if (!Fast.Ocr_String(Find_Pairs["是否存在悬赏封印怪物"]).Contains("悬赏"))
                    Operational.Slide_Pos(new Position(28, 501, 119, 524), new(40, 165, 108, 181));
                Sleep(500);
                if (goTY && !Fast.Ocr_String(Find_Pairs["是否存在悬赏封印怪物"]).Contains("悬赏"))
                {
                    log.Info_Green("悬赏任务清理完毕，结束任务..");
                    return;
                }
                else
                    goto ReFind;
            }
        }

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            //点击开始
            log.Info("战斗开始");
            var pics = Mp.Filter("ZD_");
            bool ret_bol = false;
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                p.Click();
                if (p.Name.Contains("胜利") || p.Name.Contains("达摩"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败"))
                {
                    ret_bol = false;
                    isbreak = true;
                }
            }
            WaitBack();
            return ret_bol;
        }

        /// <summary>
        /// 执行任务
        /// </summary>
        private void DoTask()
        {
            Sleep(4000);
            if (TaskType == "未知")
            {
                log.Warn("未知的任务类型，无法继续..");
                return;
            }
            if (TaskType == "秘闻")
            {
                log.Info("点击挑战");
                Fast.Click(1097, 598);
                Fast.Click(1097, 598);
                Sleep(1500);
                log.Info("等待战斗结果..");
                Combat();
                Sleep(1000);
                Fast.Click(1164, 105, 1199, 136);
                Sleep(1000);
                while (Fast.Ocr_String(90, 21, 240, 73).Contains("秘闻"))
                {
                    Sleep(1000);
                    Fast.Click(49, 47);
                    Sleep(1000);
                }
                TaskType = "";
                PickJL();
            }
            if (TaskType == "挑战")
            {
                log.Info("点击挑战");
                Fast.Click(950, 533);
                Fast.Click(950, 533);
                Sleep(1500);
                log.Info("等待战斗结果..");
                Combat();
                Fast.Click(1047, 152);
                Sleep(1000);
                TaskType = "";
                PickJL();
            }
            if (TaskType == "御魂")
            {
                log.Info("点击挑战");
                Fast.Click(1121, 612);
                Fast.Click(1121, 612);
                Sleep(1500);
                log.Info("等待战斗结果..");
                Combat();
                Sleep(1000);
                Fast.Click(39, 32, 74, 61);
                Sleep(1000);
                TaskType = "";
                PickJL();
            }
            if (TaskType == "探索")
            {
                log.Info("点击探索");
                Fast.Click(947, 538);
                Fast.Click(947, 538);
                Sleep(1500);
                var Config = new Dictionary<string, string>()
                {
                    {"Counting_Mode","True" }
                };
                RunTaskFactory.Run<TanSuo>(Db, Dm, Scene.Ct, 1, "探索", Config);
                Sleep(1000);
                TaskType = "";
                PickJL();
            }
        }

        /// <summary>
        /// 当前是否在悬赏界面
        /// </summary>
        /// <returns></returns>
        private bool NowisXuanShang() => Fast.Ocr_String(Find_Pairs["是否打开了悬赏封印"]).Contains("悬赏封印");

        /// <summary>
        /// 领取奖励
        /// </summary>
        private void PickJL()
        {
            log.Info("尝试领取奖励..");
            while (Mp.Filter("奖励领取").FindAllAndClick())
            {
                Sleep(1500);
                Fast.Click(1157, 145);
                Sleep(1500);
            }
            while (Mp.Filter("宝箱领取").FindAllAndClick())
            {
                Sleep(1500);
                Fast.Click(1157, 145);
                Sleep(1500);
            }
        }

        private void WaitBack()
        {
            log.Info("等待返回...");
            if (TaskType == "秘闻")
            {
                while (!Fast.Ocr_String(Find_Pairs["悬赏返回_挑战"]).Contains("挑战"))
                {
                    Fast.Click(784, 58, 955, 89);
                    Sleep(1000);
                    if (Scene.NowScene == "探索")
                        break;
                }
            }
            if (TaskType == "挑战")
            {
                while (!Fast.Ocr_String(883, 512, 1005, 566).Contains("挑战"))
                {
                    Fast.Click(1157, 145);
                    Sleep(1000);
                    if (Scene.NowScene == "探索")
                        break;
                }
            }
            if (TaskType == "御魂")
            {
                while (!Fast.Ocr_String(93, 17, 248, 69).Contains("御魂"))
                {
                    Fast.Click(1157, 145);
                    Sleep(1000);
                    if (Scene.NowScene == "探索")
                        break;
                }
            }
        }
    }
}