﻿using Newtonsoft.Json.Linq;
using XHelper;
using XHelper.DanDingNet;

namespace DanDing1.Models
{
    /// <summary>
    /// 程序运行配置
    /// </summary>
    public class AppConfig
    {
        /// <summary>
        /// 是否启用WebSocket通信
        /// </summary>
        public bool EnableWebSocketCommunication { get; set; } = XConfig.LoadValueFromFile<bool?>("AppConfig", "EnableWS") ?? true;

        /// <summary>
        /// 是否启用Http日志上报
        /// </summary>
        public bool EnableHttpLogReporting { get; set; } = XConfig.LoadValueFromFile<bool?>("AppConfig", "EnableHttpLogReporting") ?? true;

        /// <summary>
        /// 用户积分
        /// </summary>
        public int User_Points { get; set; } = 0;

        /// <summary>
        /// 用户登录状态
        /// </summary>
        public bool IsLogin { get; set; } = false;

        /// <summary>
        /// 用户是否为试用用户
        /// </summary>
        public bool IsFree { get; set; } = false;

        public AppInfo Info { get; set; } = new();

        /// <summary>
        /// app通知key
        /// </summary>
        public string Ntfy_Key { get; set; } = "";

        public DanDingNet dNet { get; set; }

        /// <summary>
        /// 是否已经初始化
        /// </summary>
        private bool isInit { get; set; } = false;

        public async Task<bool> InitAsync()
        {
            if (isInit) return true;
            XLogger.Init(XConfig.LoadValueFromFile<bool>("ShowDebug"));
            dNet = new(Info.Now_Ver);
            await dNet.InitializeAsync(XConfig.LoadValueFromFile<string>("ServerHost") ?? "中转线路2");
            if (dNet.Auth != null) isInit = true;
            else return false;

            var info = await dNet.System.GetInfoAsync();
            Info.Ver = info.NewVer;
            Info.Notice = info.AppInfo;
            Info.UpdataLog = info.UpData;
            Info.NewVerUrl = info.AppNewUrl;
            return true;
        }

        public async Task UpNowHost()
        {
            await dNet.InitializeAsync(XConfig.LoadValueFromFile<string>("ServerHost") ?? "中转线路2");
            var info = await dNet.System.GetInfoAsync();
            Info.Ver = info.NewVer;
            Info.Notice = info.AppInfo;
            Info.UpdataLog = info.UpData;
            Info.NewVerUrl = info.AppNewUrl;
            if (GlobalData.Instance.appConfig.IsLogin)
                MessageBox.Show("切换节点成功，由于您当前处于登录状态，请重启应用以避免潜在的闪退风险。", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);// 显示重启提示
        }
    }
}