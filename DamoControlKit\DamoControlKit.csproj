﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Platforms>x86</Platforms>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>


  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
    <Optimize>False</Optimize>
    <DebugType>full</DebugType>
  </PropertyGroup>


  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x86'">
    <Optimize>False</Optimize>
    <DebugType>full</DebugType>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="runtimes\dm.dll" />
    <None Remove="runtimes\DmReg.dll" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="runtimes\dm.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="runtimes\DmReg.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="YoloSharp" Version="6.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\XHelper\XHelper.csproj" />
  </ItemGroup>

</Project>
