﻿/*
 * 文件名: SuperMultiGamesWindowViewModel.Property.cs
 * 职责描述: 定义SuperMultiGamesWindowViewModel的所有属性和字段
 * 该文件包含了ViewModel的所有私有字段和公共属性定义，用于数据绑定和状态管理
 */

using DanDing1.Models;
using DanDing1.Models.Super;
using DanDing1.ViewModels.Windows.SuperMultiGamesWindowViewModels;
using System.Collections.ObjectModel;

namespace DanDing1.ViewModels.Windows
{
    public partial class SuperMultiGamesWindowViewModel : ObservableObject
    {
        private ObservableCollection<GameLogItem> _gameLogs;

        /// <summary>
        /// 任务运行记录通知
        /// </summary>
        private Dictionary<string, string> TaskNotificationMessages = new Dictionary<string, string>();

        // 每个游戏的运行时间任务
        private Dictionary<int, Task> _gameRuntimeTasks = new Dictionary<int, Task>();

        // 每个游戏的脚本ID
        private Dictionary<int, int> _gameScriptIds = new Dictionary<int, int>();


        private bool _isTaskControlVisible = true;

        private bool _isWindowTopMost;

        private string _logOutput = "系统启动完成，等待操作...\n";

        private SuperMultiGame_DataModel _selectedGameModel;

        private int _selectedLogTabIndex;

        private ObservableCollection<SuperMultiGame_DataModel> _superMultiGame_DataModelCollection;

        // 保存对窗口的弱引用，避免内存泄漏
        private WeakReference<Window> _windowReference;

        // 模拟器数据刷新完成事件
        public event EventHandler MuMuDataRefreshed;

        private string _applicationTitle;

        private int _runningTasksCount;

        public string ApplicationTitle
        {
            get => _applicationTitle;
            set => SetProperty(ref _applicationTitle, value);
        }

        /// <summary>
        /// 当前正在执行的任务数量
        /// </summary>
        public int RunningTasksCount
        {
            get => _runningTasksCount;
            set => SetProperty(ref _runningTasksCount, value);
        }

        public ObservableCollection<GameLogItem> GameLogs
        {
            get => _gameLogs;
            set => SetProperty(ref _gameLogs, value);
        }

        public bool IsTaskControlVisible
        {
            get => _isTaskControlVisible;
            set
            {
                if (_isTaskControlVisible != value)
                {
                    _isTaskControlVisible = value;
                    OnPropertyChanged(nameof(IsTaskControlVisible));
                }
            }
        }

        public bool IsWindowTopMost
        {
            get => _isWindowTopMost;
            set => SetProperty(ref _isWindowTopMost, value);
        }

        public string LogOutput
        {
            get => _logOutput;
            set => SetProperty(ref _logOutput, value);
        }

        public SuperMultiGame_DataModel SelectedGameModel
        {
            get => _selectedGameModel;
            set => SetProperty(ref _selectedGameModel, value);
        }

        public int SelectedLogTabIndex
        {
            get => _selectedLogTabIndex;
            set => SetProperty(ref _selectedLogTabIndex, value);
        }

        public ObservableCollection<SuperMultiGame_DataModel> SuperMultiGame_DataModelCollection
        {
            get => _superMultiGame_DataModelCollection;
            set => SetProperty(ref _superMultiGame_DataModelCollection, value);
        }

        /// <summary>
        /// 配置 简
        /// </summary>
        private AppConfig _config => GlobalData.Instance.appConfig;
    }
}