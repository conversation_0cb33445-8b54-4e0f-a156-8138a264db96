﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;

namespace ScriptEngine.Tasks.DailyTasks
{
    /// <summary>
    /// 每日签到
    /// </summary>
    internal class CheckinTask : BaseTask
    {
        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, className);
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            log.Info("开始执行任务：" + configs.Name);

            // 判断场景是否为庭院
            GoToTingYuan();

            // 执行逻辑
            CollectCheckin();
        }

        /// <summary>
        /// 领取免费礼包
        /// </summary>
        private void CollectCheckin()
        {
            if (Mp.Filter("签到").FindAllAndClick())
            {
                log.Info("点击签到完成..开奖..");
                Fast.Click(567, 150, 702, 338);
                Sleep(4000);
                log.Info("截图->退出..");
                //截图 保存到.\\签到 文件夹
                var path = Path.Combine(Environment.CurrentDirectory, "签到");
                if (!Directory.Exists(path))
                    Directory.CreateDirectory(path);
                Dm.CaptureJpg(0, 0, 2000, 2000, path + $"\\[{log.LogClassName}] {DateTime.Now:yyyyMMddHHmmss}.jpg", 100);
                while (Scene.NowScene != "庭院")
                {
                    Fast.Click(854, 90, 892, 129);
                    Sleep(2000);
                }
            }
            else
            {
                log.Warn("未找到签到图标，结束任务..");
            }
            log.Info("每日签到任务完成");
        }

        /// <summary>
        /// 确保角色在庭院场景
        /// </summary>
        private bool GoToTingYuan()
        {
        Retry:
            Scene.TO.TingYuan();
            // 判断庭院位置
            if (Scene.NowScene != "庭院")
            {
                log.Info("当前不在庭院，尝试前往庭院");
                if (!Scene.TO.TingYuan())
                {
                    if (!Scene.TO.ResetScene(out var s))
                    {
                        log.Error("无法进入庭院，结束任务");
                        return false;
                    }
                    else
                        goto Retry;
                }
                Sleep(1500); // 等待1.5秒

                // 再次确认是否在庭院
                if (Scene.NowScene != "庭院")
                {
                    if (!Scene.TO.ResetScene(out var s))
                    {
                        log.Error("无法进入庭院，结束任务");
                        return false;
                    }
                    else
                        goto Retry;
                }
            }
            log.Info("已确认在庭院场景");
            Sleep(3000);
            return true;
        }
    }
}