﻿using DanDing1.ViewModels.Windows;
using ShareX.ScreenCaptureLib;
using System.Runtime.InteropServices;
using System.Windows.Interop;
using Wpf.Ui.Controls;
using MessageBox = System.Windows.MessageBox;
using MessageBoxButton = System.Windows.MessageBoxButton;
using MessageBoxImage = System.Windows.MessageBoxImage;
using Button = System.Windows.Controls.Button;
using DanDing1.ViewModels.Base;
using System.Collections.ObjectModel;
using System.Windows.Controls.Primitives;
using XHelper;
using ScriptEngine.Model;
using System.IO;
using System.Windows.Controls;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// LogWindow.xaml 的交互逻辑
    /// </summary>
    public partial class LogWindow : Window
    {
        public LogWindowViewModel ViewModel { get; }

        // 目标窗口的句柄
        private IntPtr _targetWindowHandle;

        // 定时器用于定期检查目标窗口的位置和大小
        private System.Timers.Timer _timer;

        // 是否启用吸附
        private bool _isAdhesionEnabled = false;

        // 上次窗口位置
        private double _lastLeft;

        private double _lastTop;
        private double _lastHeight;

        // 位置更新阈值（像素）
        private const double POSITION_THRESHOLD = 2.0;

        // 性能优化：记录上次更新时间
        private DateTime _lastUpdateTime = DateTime.MinValue;

        private const int MIN_UPDATE_INTERVAL_MS = 16; // 约60fps

        #region Windows API

        [DllImport("user32.dll")]
        private static extern IntPtr WindowFromPoint(int x, int y);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        #endregion Windows API

        public LogWindow(
            LogWindowViewModel viewModel)
        {
            ViewModel = viewModel;
            DataContext = this;
            InitializeComponent();

            // 初始化定时器 - 使用50ms作为平衡值
            _timer = new System.Timers.Timer(50);
            _timer.Elapsed += OnTimerElapsed;
            _timer.AutoReset = true;

            //获取GameName
            GameName = viewModel.GameName;

            // 窗口加载完成后尝试自动吸附
            this.Loaded += LogWindow_Loaded;
        }

        public string GameName { get; set; }

        private GameViewBaseModel? gameView
        {
            get { return ViewModel.gameView; }
            set
            {
                ViewModel.gameView = value;
                ViewModel.gameView?.LogGameControlModel.SetStartedCallBack(() =>
                {
                    Application.Current?.Dispatcher?.Invoke(() =>
                    {
                        GameControl_Icon.Symbol = SymbolRegular.RecordStop16;
                        GameControl_Icon.Foreground = Brushes.Red;
                        GameControl_Button.IsEnabled = true;
                    });
                });
                ViewModel.gameView?.LogGameControlModel.SetEndedCallBack(() =>
                {
                    Application.Current?.Dispatcher?.Invoke(() =>
                    {
                        GameControl_Icon.Symbol = SymbolRegular.Play24;
                        GameControl_Icon.Foreground = Brushes.Green;
                        GameControl_Button.IsEnabled = true;
                    });
                });
            }
        }

        /// <summary>
        /// 窗口加载完成后尝试自动吸附
        /// </summary>
        private void LogWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // 仅在GameName不是"全部"时尝试自动吸附
            if (GameName != "全部")
            {
                try
                {
                    nint gameHandle = GetGameHandle(GameName);
                    if (gameHandle != 0)
                    {
                        // 获取到有效句柄，启用吸附
                        _targetWindowHandle = gameHandle;
                        _isAdhesionEnabled = true;

                        // 查找AdhesionButton并更新内容
                        if (this.FindName("AdhesionButton") is Button adhesionButton)
                        {
                            adhesionButton.Content = "取消吸附";
                        }

                        // 初始化位置记录
                        UpdateLastPosition();
                        _timer.Start();
                    }
                }
                catch (Exception ex)
                {
                    // 记录错误但不显示消息框，避免干扰用户体验
                    Debug.WriteLine($"自动吸附时发生错误: {ex.Message}");
                }
            }
        }

        private void AdhesionButton_Click(object sender, RoutedEventArgs e)
        {
            if (!_isAdhesionEnabled)
            {
                try
                {
                    if (GameName != "全部")
                    {
                        nint _gameHandle = GetGameHandle(GameName);
                        if (_gameHandle != 0)
                        {
                            _targetWindowHandle = _gameHandle;
                            _isAdhesionEnabled = true;
                            if (sender is Button btn) btn.Content = "取消吸附";
                            // 初始化位置记录
                            UpdateLastPosition();
                            _timer.Start();
                            return;
                        }
                    }

                    // 获取当前窗口句柄
                    IntPtr selfHandle = new WindowInteropHelper(this).Handle;

                    // 获取目标窗口
                    RegionCaptureOptions options = new();
                    options.DetectControls = false;
                    SimpleWindowInfo simpleWindowInfo = RegionCaptureTasks.GetWindowInfo(options, out int x, out int y);

                    if (simpleWindowInfo != null)
                    {
                        _targetWindowHandle = WindowFromPoint(x, y);

                        // 检查是否选择了自己
                        if (_targetWindowHandle == selfHandle)
                        {
                            MessageBox.Show("不能选择当前窗口作为吸附目标", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }

                        if (_targetWindowHandle != IntPtr.Zero)
                        {
                            _isAdhesionEnabled = true;
                            if (sender is Button btn) btn.Content = "取消吸附";
                            // 初始化位置记录
                            UpdateLastPosition();
                            _timer.Start();
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 重置所有状态
                    ResetAdhesionState(sender);
                    MessageBox.Show($"吸附窗口时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                // 取消吸附
                ResetAdhesionState(sender);
            }
        }

        private nint GetGameHandle(string gameName)
        {
            // 使用字典映射游戏名称到其配置
            var gameHandles = new Dictionary<string, Func<nint>>()
            {
                { "游戏1", () => nint.Parse(GlobalData.Instance?.Game1RunningConfig?.SelectHwnd ?? "0") },
                { "游戏2", () => nint.Parse(GlobalData.Instance?.Game2RunningConfig?.SelectHwnd ?? "0") },
                { "游戏3", () => nint.Parse(GlobalData.Instance?.Game3RunningConfig?.SelectHwnd ?? "0") },
                { "游戏4", () => nint.Parse(GlobalData.Instance?.Game4RunningConfig?.SelectHwnd ?? "0") }
            };

            gameView ??= gameName switch
            {
                "游戏1" => GlobalData.Instance?.Game1RunningConfig,
                "游戏2" => GlobalData.Instance?.Game2RunningConfig,
                "游戏3" => GlobalData.Instance?.Game3RunningConfig,
                "游戏4" => GlobalData.Instance?.Game4RunningConfig,
                _ => null
            };
            nint ret = new nint();

            try
            {
                ret = gameHandles.TryGetValue(gameName, out var getHandle) ? getHandle() : 0;
            }
            catch (Exception)
            {
                return new nint();
            }

            return ret;
        }

        /// <summary>
        /// 重置吸附状态的辅助方法
        /// </summary>
        /// <param name="sender"></param>
        private void ResetAdhesionState(object sender)
        {
            _isAdhesionEnabled = false;
            _timer.Stop();
            _targetWindowHandle = IntPtr.Zero;
            if (sender is null)
            {
                var adhesionButton = this.FindName("AdhesionButton") as Button;
                if (adhesionButton != null)
                {
                    adhesionButton.Content = "吸附窗口";
                }
            }
            else if (sender is Button btn) btn.Content = "吸附窗口";
        }

        /// <summary>
        /// 更新上次位置记录
        /// </summary>
        private void UpdateLastPosition()
        {
            _lastLeft = this.Left;
            _lastTop = this.Top;
            _lastHeight = this.Height;
        }

        /// <summary>
        /// 检查位置是否有显著变化
        /// </summary>
        /// <param name="newLeft"></param>
        /// <param name="newTop"></param>
        /// <param name="newHeight"></param>
        /// <returns></returns>
        private bool HasSignificantChange(double newLeft, double newTop, double newHeight)
        {
            return Math.Abs(newLeft - _lastLeft) > POSITION_THRESHOLD ||
                   Math.Abs(newTop - _lastTop) > POSITION_THRESHOLD ||
                   Math.Abs(newHeight - _lastHeight) > POSITION_THRESHOLD;
        }

        private void Window_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                this.DragMove();
            }
        }

        private void close_Click(object sender, RoutedEventArgs e)
        {
            if (_isAdhesionEnabled)
                ResetAdhesionState(null);
            this.Close();
        }

        /// <summary>
        /// 窗口置顶开关
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Zd_Checked(object sender, RoutedEventArgs e)
        {
            //判断
            var topMostToggle = this.FindName("Zd") as System.Windows.Controls.Primitives.ToggleButton;
            if (topMostToggle != null && topMostToggle.IsChecked == true)
                this.Topmost = true;
            else
                this.Topmost = false;
        }

        /// <summary>
        /// 定时器事件：定期检查目标窗口的位置和大小
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void OnTimerElapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            try
            {
                if (_targetWindowHandle == IntPtr.Zero || !_isAdhesionEnabled) return;
                if (this.Dispatcher.HasShutdownStarted || this.Dispatcher.HasShutdownFinished) return;

                // 性能优化：控制更新频率
                var now = DateTime.Now;
                if ((now - _lastUpdateTime).TotalMilliseconds < MIN_UPDATE_INTERVAL_MS) return;

                // 获取目标窗口的矩形区域
                RECT rect;
                if (GetWindowRect(_targetWindowHandle, out rect))
                {
                    double newLeft = rect.Right;
                    double newTop = rect.Top;
                    double newHeight = rect.Bottom - rect.Top;

                    // 检查位置是否有显著变化
                    if (HasSignificantChange(newLeft, newTop, newHeight))
                    {
                        this.Dispatcher.BeginInvoke(new Action(() =>
                        {
                            this.Left = newLeft;
                            this.Top = newTop;
                            this.Height = newHeight;
                            UpdateLastPosition();
                        }), System.Windows.Threading.DispatcherPriority.Background);

                        _lastUpdateTime = now;
                    }
                }
            }
            catch (Exception ex)
            {
                // 避免在定时器中抛出未处理异常
                this.Dispatcher.BeginInvoke(new Action(() =>
                {
                    _isAdhesionEnabled = false;
                    _timer.Stop();
                    // 查找并更新按钮文本
                    var adhesionButton = this.FindName("AdhesionButton") as Button;
                    if (adhesionButton != null) adhesionButton.Content = "吸附窗口";
                    MessageBox.Show($"窗口同步时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }));
            }
        }

        /// <summary>
        /// 窗口关闭时停止定时器
        /// </summary>
        /// <param name="e"></param>
        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            _timer?.Dispose();
            base.OnClosed(e);
        }

        private void Title_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (TitleEdit != null && Title != null)
            {
                TitleEdit.Visibility = Visibility.Visible;
                Title.Visibility = Visibility.Collapsed;
                TitleEdit.Focus();
                TitleEdit.SelectAll();
            }
        }

        private void TitleEdit_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                CommitTitleEdit();
            }
            else if (e.Key == Key.Escape)
            {
                CancelTitleEdit();
            }
        }

        private void TitleEdit_LostFocus(object sender, RoutedEventArgs e)
        {
            CommitTitleEdit();
        }

        private void CommitTitleEdit()
        {
            if (TitleEdit != null && Title != null)
            {
                // 更新 ViewModel 的标题
                ViewModel.Title_Str = TitleEdit.Text;

                TitleEdit.Visibility = Visibility.Collapsed;
                Title.Visibility = Visibility.Visible;
            }
        }

        private void CancelTitleEdit()
        {
            if (TitleEdit != null && Title != null)
            {
                // 恢复为 ViewModel 中的原始值
                TitleEdit.Text = ViewModel.Title_Str;
                TitleEdit.Visibility = Visibility.Collapsed;
                Title.Visibility = Visibility.Visible;
            }
        }

        /// <summary>
        /// 任务快速启动
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void GameControl_Button_Click(object sender, RoutedEventArgs e)
        {
            gameView ??= GameName switch
            {
                "游戏1" => GlobalData.Instance?.Game1RunningConfig,
                "游戏2" => GlobalData.Instance?.Game2RunningConfig,
                "游戏3" => GlobalData.Instance?.Game3RunningConfig,
                "游戏4" => GlobalData.Instance?.Game4RunningConfig,
                _ => null
            };

            if (GameControl_Icon.Symbol == SymbolRegular.Play24)
                await Start();
            else if (GameControl_Icon.Symbol == SymbolRegular.RecordStop16)
                await Stop();
        }

        private async Task Stop()
        {
            if (gameView != null)
            {
                GameControl_Button.IsEnabled = false;
                GameControl_Icon.Symbol = SymbolRegular.Play24;
                GameControl_Icon.Foreground = Brushes.Green;
                try
                {
                    await gameView.LogGameControlModel.StopGame();
                }
                catch (Exception)
                {
                    GameControl_Icon.Symbol = SymbolRegular.RecordStop16;
                    GameControl_Icon.Foreground = Brushes.Red;
                }
                GameControl_Button.IsEnabled = true;
            }
            else
            {
                MessageBox.Show("请先选择游戏，或激活对应的游戏布局后开始！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async Task Start()
        {
            if (gameView != null)
            {
                GameControl_Button.IsEnabled = false;
                try
                {
                    gameView.LogGameControlModel.SetStartedCallBack(() =>
                    {
                        GameControl_Icon.Symbol = SymbolRegular.RecordStop16;
                        GameControl_Icon.Foreground = Brushes.Red;
                        GameControl_Button.IsEnabled = true;
                    });
                    await gameView.LogGameControlModel.StartGame();
                }
                catch (Exception)
                {
                    GameControl_Icon.Symbol = SymbolRegular.Play24;
                    GameControl_Icon.Foreground = Brushes.Green;
                }
                GameControl_Button.IsEnabled = true;
            }
            else
            {
                MessageBox.Show("请先选择游戏，或激活对应的游戏布局后开始！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void TasksManager_Click(object sender, RoutedEventArgs e)
        {
            gameView?.LogGameControlModel.OpenTaskPage();
        }

        private void TasksManager_MouseRightButtonUp(object sender, MouseButtonEventArgs e)
        {
            // 获取任务列表
            var lists = GetPlan_List();
            if (lists.Count == 0)
            {
                ContextMenu contextMenu_noTask = new ContextMenu();
                contextMenu_noTask.Items.Add(new Wpf.Ui.Controls.MenuItem()
                {
                    Header = "没有保存的任务列表,无法快速导入！",
                    Foreground = Brushes.Red
                });
                // 设置ContextMenu的显示位置为鼠标点击的位置
                contextMenu_noTask.Placement = PlacementMode.MousePoint;

                // 将ContextMenu与按钮关联
                ((Wpf.Ui.Controls.Button)sender).ContextMenu = contextMenu_noTask;

                // 显示ContextMenu
                contextMenu_noTask.IsOpen = true;
                return;
            }

            // 创建一个新的ContextMenu
            ContextMenu contextMenu = new ContextMenu();
            foreach (var taskname in lists)
            {
                Wpf.Ui.Controls.MenuItem menuItem = new();
                menuItem.Header = taskname;
                menuItem.Click += (s, e) =>
                {
                    if (s is not Wpf.Ui.Controls.MenuItem ss) return;

                    switch (GameName)
                    {
                        case "游戏1":
                            GlobalData.Instance.Game1RunningConfig?.SetNowTaskPlan(ss.Header?.ToString() ?? "");
                            break;

                        case "游戏2":
                            GlobalData.Instance.Game2RunningConfig?.SetNowTaskPlan(ss.Header?.ToString() ?? "");
                            break;

                        case "游戏3":
                            GlobalData.Instance.Game3RunningConfig?.SetNowTaskPlan(ss.Header?.ToString() ?? "");
                            break;

                        case "游戏4":
                            GlobalData.Instance.Game4RunningConfig?.SetNowTaskPlan(ss.Header?.ToString() ?? "");
                            break;
                    }

                    XLogger.Info_Green($" [{GameName}] " + $"快速载入任务配置完成: {ss.Header?.ToString() ?? ""}");
                };

                contextMenu.Items.Add(menuItem);
            }

            // 设置ContextMenu的显示位置为鼠标点击的位置
            contextMenu.Placement = PlacementMode.MousePoint;

            // 将ContextMenu与按钮关联
            ((Wpf.Ui.Controls.Button)sender).ContextMenu = contextMenu;

            // 显示ContextMenu
            contextMenu.IsOpen = true;
        }

        private List<string> GetPlan_List()
        {
            List<string> Plan = new();
            for (int i = 1; i < 5; i++)
            {
                var tasklist = XConfig.LoadValueFromFile<ObservableCollection<TaskConfigsModel.Configs>>("TaskList", $"游戏{i}");
                if (tasklist is not [] and not null)
                    Plan.Add($"游戏{i}|上次运行");
            }
            //读取用户自定义任务列表
            //遍历文件夹 \runtimes\AppConfig\UserTaskList 下的所有文件，添加到 PlanComboBox 的 ItemsSource 中
            DirectoryInfo dir = new DirectoryInfo(".\\runtimes\\AppConfig\\UserTaskList\\");
            if (!dir.Exists)
                dir.Create();//不存在则创建文件夹
            foreach (var file in dir.GetFiles())
                Plan.Add(file.Name);

            return Plan;
        }
    }

    public class MaxWidthConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is double actualWidth)
            {
                return actualWidth - 100;
            }
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class InverseBooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value == null || parameter == null)
                return Visibility.Visible;

            bool isEqual = value.ToString() == parameter.ToString();
            return isEqual ? Visibility.Collapsed : Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class StringNotEqualToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value == null || parameter == null)
                return Visibility.Visible;

            bool isEqual = value.ToString() == parameter.ToString();
            return isEqual ? Visibility.Collapsed : Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}