﻿using DamoControlKit;
using DamoControlKit.Model;
using DanDing1.Helpers;
using DanDing1.Resources;
using DanDing1.Views.Windows;
using Microsoft.Toolkit.Uwp.Notifications;
using ScriptEngine;
using ScriptEngine.Model;
using ScriptEngine.MuMu;
using ShareX.HelpersLib;
using ShareX.ScreenCaptureLib;
using System.Collections.ObjectModel;
using System.Text.RegularExpressions;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Extensions;
using XHelper;
using CommunityToolkit.Mvvm.Input;
using static ScriptEngine.DDBuilder;
using DanDing1.Services.Notification;

namespace DanDing1.ViewModels.Base
{
    /// <summary>
    /// event 事件
    /// </summary>
    public partial class GameViewBaseModel
    {
        internal LogGameControl_TaskEndedCallBack TaskEndedCallBack { get; set; }
        internal LogGameControl_TaskStartedCallBack TaskStartedCallBack { get; set; }

        /// <summary>
        /// 打开任务列表详情窗口
        /// </summary>
        [RelayCommand]
        internal void OnOpenTaskPage()
        {
            var window = ServiceProvider.GetService(typeof(TaskConfigWindow)) as TaskConfigWindow;
            window?.InitConfig(GameTaskLists, (t) =>
            {
                GameTaskLists = t;
            });
            window?.ShowDialog();
        }

        /// <summary>
        /// 开始任务
        /// </summary>
        /// <param name="Fastconfigs">快速任务配置列表,如果为null则使用GameTaskLists</param>
        /// <returns>异步任务</returns>
        [RelayCommand]
        internal async Task OnStart(ObservableCollection<TaskConfigsModel.Configs>? Fastconfigs)
        {
            try
            {
                // 验证任务是否正在进行
                if (isRunning)
                {
                    InfoBar?.Show("警告", "当前任务正在执行，无法继续执行新的任务！", "Warning", 3000);
                    return;
                }
                RunningTaskName = "启动中";

                // 验证启动条件(任务列表是否为空等)
                if (!await ValidateStartConditions(Fastconfigs))
                    return;

                // 初始化大漠构建器,传入任务配置
                var dBuilder = await InitializeDDBuilder(Fastconfigs ?? GameTaskLists);
                if (dBuilder == null)
                    return;

                // 验证大漠插件是否正常
                if (!await ValidateDamoPlugin(dBuilder))
                    return;

                // 开始执行脚本
                if (!await StartScriptExecution(dBuilder))
                    return;

                // 完成启动任务(更新运行时间、自动保存配置等)
                await CompleteStartupTasks(Fastconfigs);

                // 启动成功通知
                TaskStartedCallBack?.Invoke();
            }
            catch (Exception e)
            {
                // 处理启动过程中的错误
                await HandleStartupError(e);
            }
        }

        /// <summary>
        /// 停止任务
        /// </summary>
        [RelayCommand]
        internal async Task OnStop()
        {
            if (!Scripts.IsRunning(ScriptId, out string errorstr))
            {
                InfoBar?.Show("无法暂停任务", errorstr, "Warning", 3000);
                return;
            }

            await Scripts.StopAsync(ScriptId);
            TaskEndedCallBack?.Invoke();
            StartButtonEnabled = true;
            isRunning = false;
        }

        /// <summary>
        /// 添加任务
        /// </summary>
        /// <param name="name"></param>
        [RelayCommand]
        private async Task OnAddask(string name)
        {
            // 检查六道任务是否需要 PaddleOCR
            if (name == "六道" && !await CheckPaddleOCR())
            {
                return;
            }

            await _taskService.OnAddTask(name, this);
        }

        /// <summary>
        /// 绑定方法
        /// </summary>
        [RelayCommand]
        private void OnBind()
        {
            SelectedWindow = null;
            RegionCaptureOptions Options = new();
            Options.DetectControls = false;
            SimpleWindowInfo simpleWindowInfo = RegionCaptureTasks.GetWindowInfo(Options, out int x, out int y);
            if (simpleWindowInfo != null)
            {
                IntPtr windowHandle = WindowFromPoint(x, y);
                SelectedWindow = new WindowInfo(windowHandle);
                int hwnd = (int)SelectedWindow.Handle;
                AddTaskControl.ViewModel.SelectHwnd = hwnd;
                SelectHwnd = hwnd.ToString();
                SelectDpi = $"{SelectedWindow.ClientRectangle.Width}*{SelectedWindow.ClientRectangle.Height}";
                SendTipAboutHwnd();
            }
        }

        /// <summary>
        /// 删除全部任务
        /// </summary>
        /// <param name="index"></param>
        [RelayCommand]
        private void OnDelAllTask()
        {
            GameTaskLists = [];
        }

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="index"></param>
        [RelayCommand]
        private void OnDelTask(int index)
        {
            if (index == -1)
                return;
            TaskConfigsModel.Configs? configs = GameTaskLists?[index];
            if (configs is null) return;
            GameTaskLists?.RemoveAt(index);
        }

        /// <summary>
        /// 快速执行任务
        /// </summary>
        [RelayCommand]
        private async Task OnFastStart(string taskname)
        {
            StartButtonEnabled = false;
            if (Scripts.IsRunning(ScriptId, out string errorstr))
            {
                InfoBar?.Show("无法快速开始！", "您当前正在执行任务，无法继续，请暂停后重试！", "Warning", 3000);
                StartButtonEnabled = true;
                return;
            }
            if (string.IsNullOrEmpty(SelectHwnd))
            {
                InfoBar?.Show("警告", $"无法开始任务，您没有绑定{GameName}的句柄！", "Warning", 3000);
                StartButtonEnabled = true;
                return;
            }

            // 检查六道任务是否需要 PaddleOCR
            if (taskname == "六道" && !await CheckPaddleOCR())
            {
                StartButtonEnabled = true;
                return;
            }

            // 使用任务服务获取配置
            var config = _taskService.GetConfig(taskname, AddTaskControl?.ViewModel);
            if (config is null)
            {
                StartButtonEnabled = true;
                return;
            }
            if (taskname == "活动")
                config.Others.Add("Automated", true.ToString());
            ObservableCollection<TaskConfigsModel.Configs> fastGameTaskLists = [config];
            await OnStart(fastGameTaskLists);
        }

        [RelayCommand]
        private void OnMoveDown(int index) => MoveTask(index, 1);

        [RelayCommand]
        private void OnMoveUp(int index) => MoveTask(index, -1);

        /// <summary>
        /// 打开方案管理
        /// </summary>
        [RelayCommand]
        private void OnOpenTaskList() => OnOpenTaskPage();

        /// <summary>
        /// 保存任务列表
        /// </summary>
        [RelayCommand]
        private void OnSaveTaskList()
        {
            if (GameTaskLists.Count == 0)
            {
                InfoBar?.Show("警告", "无法保存任务列表，您没有添加任何任务！", "Warning", 2000);
                return;
            }
            if (XConfig.SaveValueToFile("TaskList", GameName, GameTaskLists))
                InfoBar?.Show($"保存成功[{GameTaskLists.Count}个任务]", "您已成功保存当前任务列表，此后打开程序后将为您自动载入保存的任务列表！", "Success", 4000);
            else
                InfoBar?.Show("警告", "无法保存任务列表，发生了未知错误！请检查软件权限是否正常！", "Warning", 3000);
        }

        private void ResetStartState()
        {
            isRunning = false;
            StartButtonEnabled = true;
        }

        /// <summary>
        /// 打开预设管理窗口
        /// </summary>
        [RelayCommand]
        private void OpenPreset()
        {
            try
            {
                // 创建预设窗口
                var presetWindow = new Views.Windows.PresetWindow();

                // 以模态窗口方式显示
                presetWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                XLogger.Error($"打开预设窗口时出错: {ex.Message}");
                InfoBar?.Show("错误", $"打开预设窗口时出错: {ex.Message}", "Error");
            }
        }

        #region OnStart Helper Methods

        /// <summary>
        /// 任务运行记录通知
        /// </summary>
        private Dictionary<string, string> TaskNotificationMessages = new Dictionary<string, string>();

        /// <summary>
        /// 完成启动任务
        /// </summary>
        /// <param name="fastconfigs">快速配置列表</param>
        private async Task CompleteStartupTasks(ObservableCollection<TaskConfigsModel.Configs>? fastconfigs)
        {
            runtime_task = Task.Run(UpRunningTime);
            AutoSaveTaskConfigs(fastconfigs);
            AutoMoveLogView();
            await Task.Run(() => GlobalData.Instance.UserProfile.IncrementTaskStartCount());
            XConfig.SaveValueToFile(GameName, "NoticeConfigs", new Dictionary<string, object>
                {
                    { "Notice_IsChecked", Notice_IsChecked },
                    { "Notice_SelectItem", Notice_SelectItem },
                });

            StartButtonEnabled = false;
            isRunning = true;
        }

        /// <summary>
        /// 创建游戏设置模型
        /// </summary>
        /// <param name="loopCount">循环次数</param>
        /// <returns>游戏设置模型实例</returns>
        private async Task<GameSettingsModel> CreateGameSettingsModel(int loopCount)
        {
            return new GameSettingsModel
            {
                AdbPort = await Task.Run(() => Utils.FilterMuMuAdbPort(int.Parse(SelectHwnd), new MuMu()._GetInstances())),
                XuanShang = AddTaskControl.ViewModel.XShangChecked,
                CourtyardSkin = AddTaskControl.ViewModel.Game_Scene,
                LoopCount = loopCount,
                IsRecord = IsRecord,
                IsTifu = IsTifu,
                RecordQuality = await Task.Run(() => XConfig.LoadValueFromFile<string>("RecordQuality") ?? "原生质量（不压缩）"),
                SpeedSwitch = SpeedSwitch,
                EndCloseGame = EndCloseGame
            };
        }

        /// <summary>
        /// 任务结束回调处理
        /// </summary>
        /// <param name="t">记录数据</param>
        private async void EndCallBack_Data(RecordData t)
        {
            RunningTaskName = "未运行";

            InfoBar?.Show("通知", "任务已顺利完成！", "Success", 5000);
            GlobalData.Instance.UserProfile.IncrementScriptClickCount(t.MainClick + t.SubClick); //点击次数累加
            GlobalData.Instance.UserProfile.UpdateScriptRunningTime(RunTimed / 60);
            TaskEndedCallBack?.Invoke();
            await Task.Delay(1000);
            if (Notice_IsChecked)
            {
                if ((RunTimed / 60) >= 3)
                    await SendNotice(t);
                else
                    XLogger.Warn($"[{GameName}] 您当前执行的任务总时长不超过3分钟，无法触发发送通知的条件！");
            }
            XLogger.Info($"任务运行详情：\r\n{string.Join("\r\n", TaskNotificationMessages.Select(x =>
            {
                if (x.Key == "检测线程" || x.Key == "Sub")
                {
                    //获取Value中 "当前清理御魂次数：0，当前处理悬赏次数：0，当前卡屏次数：0  "
                    var value = x.Value;
                    var match = Regex.Match(value, @"清理御魂次数：(\d+)，处理悬赏次数：(\d+)，卡屏次数：(\d+)");
                    if (match.Success)
                        GlobalData.Instance.UserProfile.IncrementBountyCount(int.Parse(match.Groups[2].Value));
                }
                return x.Key + "：" + x.Value;
            }
            ))}");

            //Windows通知
            new ToastContentBuilder()
                .AddArgument("conversationId", 9813)
                .AddText($"[{GameName}]您执行的任务已经结束了！")
                .AddText($"用时：{LastShowTime.Replace("Last：", null)}")
                .AddText("更多详情请前往蛋定助手查看...")
                .Show();
            isRunning = false;
            //录制回调操作
            if (IsRecord)
            {
                //打开文件夹.\runtimes\Debug
                Process.Start(new ProcessStartInfo()
                {
                    FileName = "explorer.exe",
                    Arguments = ".\\runtimes\\Debug"
                });
            }
            IsRecord = false;

            // 如果设置了任务结束后关闭模拟器
            if (EndCloseGame && !string.IsNullOrEmpty(SelectHwnd) && SelectHwnd != "0")
            {
                try
                {
                    // 获取模拟器索引
                    int mumuIndex = await Task.Run(() => Utils.FilterMuMuIndex(int.Parse(SelectHwnd), new MuMu()._GetInstances()));
                    if (mumuIndex >= 0)
                    {
                        XLogger.Info($"[{GameName}] 任务结束，正在关闭模拟器...");
                        InfoBar?.Show("通知", "任务结束，正在关闭模拟器...", "Information", 3000);

                        // 初始化MuMu实例
                        var mumu = new MuMu();
                        string path = XConfig.LoadValueFromFile<string>("MuMuPath");
                        if (!string.IsNullOrEmpty(path) && mumu.Init(path))
                        {
                            // 关闭模拟器
                            mumu.CloseByIndex(mumuIndex);

                            // 重置句柄和分辨率
                            SelectHwnd = "0";
                            SelectDpi = "0*0";

                            XLogger.Info($"[{GameName}] 模拟器已关闭");
                            InfoBar?.Show("通知", "模拟器已关闭", "Success", 3000);
                        }
                        else
                        {
                            XLogger.Warn($"[{GameName}] 无法初始化MuMu模拟器，无法关闭模拟器");
                            InfoBar?.Show("警告", "无法初始化MuMu模拟器，无法关闭模拟器", "Warning", 3000);
                        }
                    }
                    else
                    {
                        XLogger.Warn($"[{GameName}] 无法获取模拟器索引，无法关闭模拟器");
                        InfoBar?.Show("警告", "无法获取模拟器索引，无法关闭模拟器", "Warning", 3000);
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Error($"[{GameName}] 关闭模拟器时出错: {ex.Message}");
                    InfoBar?.Show("错误", $"关闭模拟器时出错: {ex.Message}", "Error", 3000);
                }
            }
        }

        /// <summary>
        /// 处理大漠插件注册错误
        /// </summary>
        /// <param name="dBuilder">DDBuilder实例</param>
        /// <param name="dmsoftCode">大漠注册码</param>
        /// <returns>处理是否成功</returns>
        private async Task<bool> HandleDamoRegistrationError(DDBuilder dBuilder, string dmsoftCode)
        {
            XLogger.Warn($" [{GameName}] " + $"正在尝试修复 检测到大漠插件未注册，正在尝试手动注册...");
            InfoBar?.Show("正在尝试修复", "检测到大漠插件未注册，正在尝试手动注册...", "Warning", 3000);
            if (await DamoKit.TryManualRegisterAndRegAsync(dmsoftCode))
            {
                if (!await dBuilder.CheckAsync(dmsoftCode))
                {
                    XLogger.Warn($" [{GameName}] " + $"无法开始任务，句柄已经失效，请重新绑定！");
                    InfoBar?.Show("警告", "无法开始任务，句柄已经失效，请重新绑定！", "Warning", 3000);
                    ResetStartState();
                    return false;
                }
                return true;
            }

            XLogger.Error($" [{GameName}] " + $"大漠插件注册失败，请尝试以管理员身份运行程序或手动注册dm.dll");
            InfoBar?.Show("错误", "大漠插件注册失败，请尝试以管理员身份运行程序或手动注册dm.dll", "Error", 5000);
            ResetStartState();
            return false;
        }

        /// <summary>
        /// 处理启动错误
        /// </summary>
        /// <param name="e">异常信息</param>
        private async Task HandleStartupError(Exception e)
        {
            XLogger.SaveException(e);
            XLogger.Error("Start开始任务出现致命错误！");
            XLogger.Error("相关错误信息已保存到error.txt中！");

            if (e.Message.Contains("REGDB_E_CLASSNOTREG") || e.Message.Contains("没有注册类"))
            {
                try
                {
                    var dmsoftCode = await _config.dNet.User.GetDmsoftCodeAsync();
                    InfoBar?.Show("正在尝试修复", "检测到大漠插件未注册，正在尝试手动注册...", "Warning", 3000);

                    if (await DamoKit.TryManualRegisterAndRegAsync(dmsoftCode))
                    {
                        XLogger.Info("大漠插件手动注册成功，请重新启动任务");
                        InfoBar?.Show("修复成功", "大漠插件已成功注册，请重新启动任务", "Success", 5000);
                    }
                    else
                    {
                        InfoBar?.Show("错误", "大漠插件注册失败，请尝试以管理员身份运行程序或手动注册dm.dll", "Error", 5000);
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Error($"尝试修复大漠插件注册失败: {ex.Message}");
                    InfoBar?.Show("错误", "修复大漠插件注册失败，请尝试以管理员身份运行程序", "Error", 5000);
                }
            }

            ResetStartState();
        }

        /// <summary>
        /// 初始化大漠构建器
        /// </summary>
        /// <param name="configs">任务配置列表</param>
        /// <returns>初始化后的DDBuilder实例</returns>
        private async Task<DDBuilder?> InitializeDDBuilder(ObservableCollection<TaskConfigsModel.Configs> configs)
        {
            int loopCount = int.TryParse(LoopCount, out int count) ? count : 1;
            var tcm = await Task.Run(() => Utils.GetTaskConfigs(configs));

            // 初始化更新游戏当前任务委托
            UpdateGameTaskDelegate OnUpdateGameTask = (taskName) =>
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    RunningTaskName = taskName;
                });
            };

            // 初始化更新游戏模拟器句柄的委托
            UpdateGameHwndDelegate OnUpdateGameHwnd = async (mumuhwnd, gamehwnd) =>
            {
                SelectHwnd = gamehwnd.ToString();
                if (SelectHwnd == "0") SelectDpi = "0*0";
                else
                {
                    var window = await Task.Run(() => new WindowInfo(int.Parse(SelectHwnd)));
                    SelectDpi = $"{window.ClientRectangle.Width}*{window.ClientRectangle.Height}";
                }
            };

            // 在脚本引擎初始化时 创建通知发送器工厂
            var notificationSenderFactory = new NotificationSenderFactory(_config.dNet.User);
            var notificationService = new TaskNotificationService(notificationSenderFactory);
            SendNotificationDelegate sendNotificationDelegate = async (title, content) =>
            {
                if (!Notice_IsChecked) return false;
                var type = Notice_SelectItem ?? "邮件";
                if (Notice_SelectItem == "自定义" && GlobalData.Instance.UserConfig.Notice_Ntfy)
                    await notificationService.SendSimpleNotificationAsync("App", title, content);
                if (Notice_SelectItem == "自定义" && GlobalData.Instance.UserConfig.Notice_WxPush)
                    await notificationService.SendSimpleNotificationAsync("微信推送", title, content);
                if (Notice_SelectItem == "自定义" && GlobalData.Instance.UserConfig.Notice_Pushplus)
                    await notificationService.SendSimpleNotificationAsync("Pushplus推送", title, content);
                if (Notice_SelectItem == "自定义" && GlobalData.Instance.UserConfig.Notice_Miaotixing)
                    await notificationService.SendSimpleNotificationAsync("喵提醒", title, content);
                return true;
            };

            // 创建并配置DDBuilder
            var dBuilder = new DDBuilder();
            dBuilder.SetSimulator("mumu", int.Parse(SelectHwnd))
                .SetDelegates(OnUpdateGameTask, OnUpdateGameHwnd, sendNotificationDelegate)
                .SetBindSetting(BindModels.GetBindModel("mumu", 1))
                .SetTaskList(tcm)
                .InitLog(GameName)
                .SetMuMuIndex(await Task.Run(() => Utils.FilterMuMuIndex(int.Parse(SelectHwnd), new MuMu()._GetInstances())))
                .SetGameSettings(await CreateGameSettingsModel(loopCount))
                .SetUserConfigs(GlobalData.Instance.UserConfig.Pairs)
                .AddData("Base_Url", GlobalData.Instance.appConfig.dNet.System.GetCurrentHost());

            // 初始化图片服务器
            if (!await InitializePicServer(dBuilder))
                return null;

            return dBuilder;
        }

        /// <summary>
        /// 初始化图片服务器
        /// </summary>
        /// <param name="dBuilder">DDBuilder实例</param>
        /// <returns>初始化是否成功</returns>
        private async Task<bool> InitializePicServer(DDBuilder dBuilder)
        {
            var picServer = await Task.Run(() => XConfig.LoadValueFromFile<string>("PicServer") ?? "默认");
            var picVer = GlobalData.Instance.PicServerVer ?? _config.Info.Now_Ver;

            InfoBar?.Show("图库初始化中..", $"图库源：{picServer}，图库版本：{picVer}", "Success");

            if (!await dBuilder.SetPicsVerAsync(picServer, picVer))
            {
                InfoBar?.Show("无法开始任务", "云端图库初始化失败！请检查设置图库版本是否正确！", "Error", 3000);
                ResetStartState();
                return false;
            }

            XLogger.Info($"[{GameName}] 图库初始完成，" + DynamicData.ToString());
            return true;
        }

        /// <summary>
        /// 发送脚本运行汇总给用户
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private async Task SendNotice(RecordData data)
        {
            await Task.Delay(2000);//等待2秒发通知

            try
            {
                // 创建通知发送器工厂
                var senderFactory = new NotificationSenderFactory(_config.dNet.User);
                var notificationService = new TaskNotificationService(senderFactory);

                // 发送任务完成通知
                bool success = await notificationService.SendTaskCompletionNoticeAsync(
                    GameName,
                    LastShowTime.Replace("Last：", null),
                    data.MainClick,
                    data.SubClick,
                    data.TaskNames,
                    TaskNotificationMessages,
                    Notice_SelectItem);

                if (success)
                {
                    XLogger.Info($"[{GameName}] 任务结束通知发送成功！");
                }
                else
                {
                    XLogger.Warn($"[{GameName}] 任务结束通知发送失败！");
                }
            }
            catch (Exception e)
            {
                XLogger.Warn($"通知发送失败: {e.Message}");
                XLogger.SaveException(e);
            }
        }

        /// <summary>
        /// 启动脚本执行
        /// </summary>
        /// <param name="dBuilder">DDBuilder实例</param>
        /// <returns>启动是否成功</returns>
        private async Task<bool> StartScriptExecution(DDBuilder dBuilder)
        {
            // 添加ScriptId有效性检查
            if (ScriptId < 0)
            {
                string errorMsg = $"无法启动任务，脚本ID无效：{ScriptId}";
                XLogger.Error($" [{GameName}] " + errorMsg);
                InfoBar?.Show("启动失败", errorMsg, "Error", 3000);
                ResetStartState();
                return false;
            }

            Scripts.SetBuilder(ScriptId, dBuilder);
            Scripts.SetTaskUserNotificationMessage(ScriptId, (t) => TaskNotificationMessages = t);
            Scripts.SetScrpitCallBack_Data(ScriptId, EndCallBack_Data);

            string errorstr = "";
            bool startSuccess = await Task.Run(() => Scripts.Start(ScriptId, out errorstr));
            if (!startSuccess)
            {
                XLogger.Warn($" [{GameName}] " + $"启动任务失败..{errorstr}");
                InfoBar?.Show("启动任务失败", errorstr, "Warning", 3000);
                ResetStartState();
                return false;
            }
            XLogger.Info_Green($" [{GameName}] " + $"任务启动成功..");
            return true;
        }

        /// <summary>
        /// 验证大漠插件状态
        /// </summary>
        /// <param name="dBuilder">DDBuilder实例</param>
        /// <returns>验证是否通过</returns>
        private async Task<bool> ValidateDamoPlugin(DDBuilder dBuilder)
        {
            if (!DamoKit.IsReg)
            {
                XLogger.Info($" [{GameName}] " + $"插件初始化中，首次执行请耐心等待3s左右的时间..");
                InfoBar?.Show("插件初始化中..", "首次执行请耐心等待3s左右的时间..", "Success", 1500);
                var dmsoftCode = await _config.dNet.User.GetDmsoftCodeAsync();

                try
                {
                    if (!await dBuilder.CheckAsync(dmsoftCode))
                    {
                        XLogger.Warn($" [{GameName}] " + $"无法开始任务，句柄已经失效，请重新绑定！");
                        InfoBar?.Show("警告", "无法开始任务，句柄已经失效，请重新绑定！", "Warning", 3000);
                        ResetStartState();
                        return false;
                    }
                }
                catch (Exception ex) when (ex.Message.Contains("REGDB_E_CLASSNOTREG") || ex.Message.Contains("没有注册类"))
                {
                    return await HandleDamoRegistrationError(dBuilder, dmsoftCode);
                }
            }
            else if (!await dBuilder.CheckAsync(""))
            {
                XLogger.Warn($" [{GameName}] " + $"无法开始任务，句柄已经失效，请重新绑定！");
                InfoBar?.Show("警告", "无法开始任务，句柄已经失效，请重新绑定！", "Warning", 3000);
                ResetStartState();
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证启动条件
        /// </summary>
        /// <param name="configs">任务配置列表</param>
        /// <returns>验证是否通过</returns>
        private async Task<bool> ValidateStartConditions(ObservableCollection<TaskConfigsModel.Configs>? configs)
        {
            var useConfigs = configs ?? GameTaskLists;

            // 检查是否开启录制
            if (IsRecord)
            {
                var res = await ContentDialogService.ShowSimpleDialogAsync(
                    new SimpleContentDialogCreateOptions()
                    {
                        Title = "录制提醒！",
                        Content = MyString.Record_Tip,
                        CloseButtonText = "不开启录制并继续",
                        PrimaryButtonText = "我了解"
                    });
                if (res != ContentDialogResult.Primary)
                {
                    IsRecord = false;
                    XLogger.Info($"[{GameName}] 任务录制已关闭！");
                }
            }

            // 检查任务列表是否为空
            if (useConfigs.Count == 0)
            {
                XLogger.Warn($" [{GameName}] " + "无法开始任务，您没有添加任何任务！");
                InfoBar?.Show("警告", "无法开始任务，您没有添加任何任务！", "Warning", 3000);
                return false;
            }

            // 检查是否已绑定句柄
            if (string.IsNullOrEmpty(SelectHwnd))
            {
                XLogger.Warn($" [{GameName}] " + $"无法开始任务，您没有绑定{GameName}的句柄！");
                InfoBar?.Show("警告", $"无法开始任务，您没有绑定{GameName}的句柄！", "Warning", 3000);
                return false;
            }

            return true;
        }

        #endregion OnStart Helper Methods
    }
}