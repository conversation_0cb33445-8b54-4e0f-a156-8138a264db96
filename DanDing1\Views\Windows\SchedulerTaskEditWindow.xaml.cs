using DanDing1.Models;
using DanDing1.ViewModels.Windows;
using ScriptEngine.Model;
using System;
using System.Collections.ObjectModel;
using System.Windows;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// SchedulerTaskEditWindow.xaml 的交互逻辑
    /// </summary>
    public partial class SchedulerTaskEditWindow : Window
    {
        private SchedulerTaskEditViewModel _viewModel;

        /// <summary>
        /// 是否为编辑模式
        /// </summary>
        public bool IsEditMode
        {
            get => _viewModel.IsEditMode;
            set => _viewModel.IsEditMode = value;
        }

        public SchedulerTaskEditWindow()
        {
            InitializeComponent();
            _viewModel = new SchedulerTaskEditViewModel();
            this.DataContext = _viewModel;

            // 监听IsEditMode属性变化以更新窗口标题
            _viewModel.PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(SchedulerTaskEditViewModel.IsEditMode))
                {
                    UpdateWindowTitle();
                }
                else if (e.PropertyName == nameof(SchedulerTaskEditViewModel.SelectedScheduleType))
                {
                    // 在选择调度类型时，自动更新Cron预览
                    if (_viewModel.IsCron)
                    {
                        _viewModel.UpdateCronPreview();
                    }
                    else if (_viewModel.IsInterval)
                    {
                        _viewModel.UpdateIntervalPreview();
                    }
                }
            };

            TaskControl.SetSchedulerMode();

            // 初始化时设置窗口标题和按钮文本
            UpdateWindowTitle();
        }

        /// <summary>
        /// 根据编辑模式更新窗口标题
        /// </summary>
        private void UpdateWindowTitle()
        {
            this.Title = _viewModel.IsEditMode ? "编辑定时任务" : "添加定时任务";
            ConfirmButton.Content = _viewModel.IsEditMode ? "保存修改" : "确定添加";
        }

        /// <summary>
        /// 获取当前选中的任务类型
        /// </summary>
        /// <returns>任务类型名称</returns>
        public string GetTaskTypeName()
        {
            return TaskControl.GetSelectedTaskType();
        }

        /// <summary>
        /// 获取任务参数
        /// </summary>
        /// <returns>任务参数JSON字符串</returns>
        public string GetTaskParameters()
        {
            return TaskControl.ViewModel.GetConfigs();
        }

        /// <summary>
        /// 设置任务参数
        /// </summary>
        /// <param name="parameters">任务参数JSON字符串</param>
        public void SetTaskParameters(string parameters)
        {
            if (!string.IsNullOrEmpty(parameters) && TaskControl != null && TaskControl.ViewModel != null)
            {
                TaskControl.ViewModel.SetConfigs(parameters);
            }
        }

        /// <summary>
        /// 获取创建的调度任务
        /// </summary>
        /// <returns>调度任务对象，如果无法创建则返回null</returns>
        public ScheduledTask GetScheduledTask()
        {
            string taskNames = GetTaskTypeName();

            // 检查taskNames是否为空
            if (string.IsNullOrEmpty(taskNames) || taskNames == "未知任务")
            {
                MessageBox.Show("未选择任务类型，请先选择一个任务类型！", "无法创建任务", MessageBoxButton.OK, MessageBoxImage.Warning);
                return null;
            }

            return _viewModel.GetScheduledTask(taskNames, GetTaskParameters());
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }

        /// <summary>
        /// 确认按钮点击事件
        /// </summary>
        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            if (_viewModel.ValidateTask())
            {
                this.DialogResult = true;
                this.Close();
            }
            else
            {
                MessageBox.Show("请检查任务设置是否正确！", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }


    }
}