using OpenCvSharp;
using System;
using System.IO;

namespace XHelper.OCR
{
    /// <summary>
    /// 使用OpenCV进行图像处理的实现类
    /// </summary>
    internal class OpenCvImageProcessor : IImageProcessor
    {
        private readonly OcrConfiguration _config;
        private bool _isSaveTmp;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">OCR配置</param>
        public OpenCvImageProcessor(OcrConfiguration config)
        {
            _config = config;
            _isSaveTmp = config.SaveTempImages;
        }

        /// <summary>
        /// 设置是否保存临时优化图像
        /// </summary>
        /// <param name="save">是否保存</param>
        public void SetSaveTempImages(bool save)
        {
            _isSaveTmp = save;
        }

        /// <summary>
        /// 从文件路径优化图像
        /// </summary>
        /// <param name="imagePath">图像文件路径</param>
        /// <returns>优化后的图像字节数组</returns>
        public byte[] OptimizeImage(string imagePath)
        {
            if (string.IsNullOrEmpty(imagePath) || !File.Exists(imagePath))
            {
                XLogger.Error($"图像文件不存在: {imagePath}");
                return new byte[0];
            }

            try
            {
                // 加载图片
                using Mat image = Cv2.ImRead(imagePath, ImreadModes.Color);
                if (image == null || image.Empty())
                {
                    XLogger.Error($"无法读取图像文件: {imagePath}");
                    return new byte[0];
                }

                return ProcessImage(image);
            }
            catch (OpenCVException ex)
            {
                XLogger.Error($"OpenCV处理图像异常: {ex.Message}");
                return new byte[0];
            }
            catch (Exception ex)
            {
                XLogger.Error($"图像优化异常: {ex.Message}");
                return new byte[0];
            }
        }

        /// <summary>
        /// 从字节数组优化图像
        /// </summary>
        /// <param name="imageData">图像字节数组</param>
        /// <returns>优化后的图像字节数组</returns>
        public byte[] OptimizeImage(byte[] imageData)
        {
            if (imageData == null || imageData.Length == 0)
            {
                XLogger.Error("输入图像数据为空");
                return new byte[0];
            }

            try
            {
                // 加载图片 bytes
                using Mat image = Cv2.ImDecode(imageData, ImreadModes.Color);
                if (image == null || image.Empty())
                {
                    XLogger.Error("无法解码图像数据");
                    return new byte[0];
                }

                return ProcessImage(image);
            }
            catch (OpenCVException ex)
            {
                XLogger.Error($"OpenCV处理图像异常: {ex.Message}");
                return new byte[0];
            }
            catch (Exception ex)
            {
                XLogger.Error($"图像优化异常: {ex.Message}");
                return new byte[0];
            }
        }

        /// <summary>
        /// 图像处理共享逻辑
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <returns>处理后的图像字节数组</returns>
        private byte[] ProcessImage(Mat image)
        {
            if (image == null || image.Empty())
                return new byte[0];

            Mat? grayImage = null;
            Mat? binaryImage = null;
            Mat? dilateImage = null;
            Mat? kernel = null;
            Mat? invertedImage = null;
            Mat? borderedImage = null;

            try
            {
                // 转换为灰度图
                grayImage = new Mat();
                Cv2.CvtColor(image, grayImage, ColorConversionCodes.BGR2GRAY);

                // 二值化
                binaryImage = new Mat();
                Cv2.Threshold(grayImage, binaryImage, 0, 255, ThresholdTypes.Binary | ThresholdTypes.Otsu);

                // 膨胀
                dilateImage = new Mat();
                kernel = Cv2.GetStructuringElement(MorphShapes.Rect, new Size(1, 1));
                Cv2.Dilate(binaryImage, dilateImage, kernel);

                invertedImage = new Mat();
                if (IsWhiteBackground(dilateImage))
                {
                    // 转换为黑底白字
                    Cv2.BitwiseNot(dilateImage, invertedImage);
                }
                else
                {
                    invertedImage = dilateImage.Clone();
                }

                // 添加50像素的黑色边框
                borderedImage = new Mat();
                Cv2.CopyMakeBorder(invertedImage, borderedImage, 25, 25, 15, 15, BorderTypes.Constant, new Scalar(0));

                // 检查边框是否添加成功
                if (borderedImage.Empty())
                {
                    //XLogger.Error("边框添加失败，borderedImage为空");
                    borderedImage = invertedImage.Clone(); // 防止后续操作出错
                }
                else
                {
                    //XLogger.Debug($"原图尺寸: {invertedImage.Width}x{invertedImage.Height}, 添加边框后尺寸: {borderedImage.Width}x{borderedImage.Height}");
                }

                // 保存图片
                if (_isSaveTmp)
                {
                    try
                    {
                        string debugDir = Path.GetDirectoryName(_config.TempImagePath);
                        if (!string.IsNullOrEmpty(debugDir) && !Directory.Exists(debugDir))
                        {
                            Directory.CreateDirectory(debugDir);
                        }
                        //XLogger.Debug($"OptimizeImage SaveImagePath: {_config.TempImagePath}");

                        // 确保使用带边框的图像保存
                        Cv2.ImWrite(_config.TempImagePath, borderedImage);

                        // 为调试添加一个原图的保存
                        if (!string.IsNullOrEmpty(_config.TempImagePath))
                        {
                            string origPath = Path.Combine(
                                Path.GetDirectoryName(_config.TempImagePath) ?? string.Empty,
                                Path.GetFileNameWithoutExtension(_config.TempImagePath) + "_original" + Path.GetExtension(_config.TempImagePath));
                            Cv2.ImWrite(origPath, invertedImage);
                            //XLogger.Debug($"原图保存路径: {origPath}");
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"保存优化图像失败: {ex.Message}");
                    }
                }

                // 返回带边框图片的字节
                return borderedImage.ToBytes(".png");
            }
            catch (Exception ex)
            {
                XLogger.Error($"图像处理异常: {ex.Message}");
                return new byte[0];
            }
            finally
            {
                // 显式释放资源
                grayImage?.Dispose();
                binaryImage?.Dispose();
                kernel?.Dispose();

                // 注意释放资源的顺序
                borderedImage?.Dispose();

                if (invertedImage != null && invertedImage != dilateImage)
                {
                    dilateImage?.Dispose();
                    invertedImage?.Dispose();
                }
                else
                {
                    invertedImage?.Dispose();
                    dilateImage?.Dispose();
                }
            }
        }

        /// <summary>
        /// 判断图像是否为白色背景
        /// </summary>
        /// <param name="image">输入图像</param>
        /// <returns>是否为白色背景</returns>
        private static bool IsWhiteBackground(Mat image)
        {
            if (image == null || image.Empty())
                return false;

            try
            {
                // 计算图像的平均亮度
                Scalar meanScalar = Cv2.Mean(image);
                double meanBrightness = meanScalar.Val0;

                // 如果平均亮度高于阈值（如128），认为是白底黑字
                return meanBrightness > 128;
            }
            catch (Exception ex)
            {
                XLogger.Error($"判断图像背景异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取上边框尺寸
        /// </summary>
        /// <returns>像素值</returns>
        public virtual int GetTopPadding()
        {
            return 25; // 上边框为25像素
        }
        
        /// <summary>
        /// 获取下边框尺寸
        /// </summary>
        /// <returns>像素值</returns>
        public virtual int GetBottomPadding()
        {
            return 25; // 下边框为25像素
        }
        
        /// <summary>
        /// 获取左边框尺寸
        /// </summary>
        /// <returns>像素值</returns>
        public virtual int GetLeftPadding()
        {
            return 15; // 左边框为15像素
        }
        
        /// <summary>
        /// 获取右边框尺寸
        /// </summary>
        /// <returns>像素值</returns>
        public virtual int GetRightPadding()
        {
            return 15; // 右边框为15像素
        }
    }
}