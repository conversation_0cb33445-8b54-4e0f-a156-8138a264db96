using System;

namespace XHelper.OCR
{
    /// <summary>
    /// OCR引擎类型枚举
    /// </summary>
    internal enum OcrEngineType
    {
        /// <summary>
        /// OcrLite引擎，用于一般文本识别
        /// </summary>
        OcrLite,

        /// <summary>
        /// Tesseract引擎，专用于数字识别
        /// </summary>
        Tesseract,

        /// <summary>
        /// PaddleOCR引擎，高级文本识别
        /// </summary>
        PaddleOcr,
        
        /// <summary>
        /// PpOcr引擎，替代OcrLite的改进引擎
        /// </summary>
        PpOcr
    }

    /// <summary>
    /// OCR引擎工厂类，负责创建不同类型的OCR引擎
    /// </summary>
    internal class OcrEngineFactory
    {
        private readonly OcrConfiguration _config;
        private readonly IImageProcessor _imageProcessor;

        // 用于缓存已创建的引擎实例
        private IOcrEngine? _ocrLiteEngine;
        private IOcrEngine? _tesseractEngine;
        private IOcrEngine? _paddleOcrEngine;
        private IOcrEngine? _ppOcrEngine;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">OCR配置</param>
        /// <param name="imageProcessor">图像处理器</param>
        public OcrEngineFactory(OcrConfiguration config, IImageProcessor imageProcessor)
        {
            _config = config;
            _imageProcessor = imageProcessor;
        }

        /// <summary>
        /// 创建OCR引擎实例
        /// </summary>
        /// <param name="engineType">引擎类型</param>
        /// <returns>OCR引擎实例</returns>
        public IOcrEngine CreateEngine(OcrEngineType engineType)
        {
            return engineType switch
            {
                OcrEngineType.OcrLite => GetPpOcrEngine(), // 将OcrLite替换为PpOcr
                OcrEngineType.Tesseract => GetTesseractEngine(),
                OcrEngineType.PaddleOcr => GetPaddleOcrEngine(),
                OcrEngineType.PpOcr => GetPpOcrEngine(),
                _ => throw new ArgumentException($"不支持的OCR引擎类型: {engineType}")
            };
        }

        /// <summary>
        /// 获取OcrLite引擎实例
        /// </summary>
        /// <returns>OcrLite引擎实例</returns>
        private IOcrEngine GetOcrLiteEngine()
        {
            if (_ocrLiteEngine == null)
            {
                _ocrLiteEngine = new OcrLiteEngine(_config, _imageProcessor);
            }
            return _ocrLiteEngine;
        }

        /// <summary>
        /// 获取Tesseract引擎实例
        /// </summary>
        /// <returns>Tesseract引擎实例</returns>
        private IOcrEngine GetTesseractEngine()
        {
            if (_tesseractEngine == null)
            {
                _tesseractEngine = new TesseractOcrEngine(_config, _imageProcessor);
            }
            return _tesseractEngine;
        }

        /// <summary>
        /// 获取PaddleOCR引擎实例
        /// </summary>
        /// <returns>PaddleOCR引擎实例</returns>
        private IOcrEngine GetPaddleOcrEngine()
        {
            if (_paddleOcrEngine == null)
            {
                _paddleOcrEngine = new PaddleOcrEngine(_config, _imageProcessor);
            }
            return _paddleOcrEngine;
        }
        
        /// <summary>
        /// 获取PpOcr引擎实例
        /// </summary>
        /// <returns>PpOcr引擎实例</returns>
        private IOcrEngine GetPpOcrEngine()
        {
            if (_ppOcrEngine == null)
            {
                _ppOcrEngine = new PpOcrEngine(_config, _imageProcessor);
            }
            return _ppOcrEngine;
        }
    }
}