﻿using DamoControlKit;
using DamoControlKit.Model;
using Emgu.CV.Bioinspired;
using Emgu.CV.Ocl;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System.ComponentModel;
using XHelper;
using static ScriptEngine.Model.TaskConfigsModel;

namespace ScriptEngine.Tasks
{
    internal class TanSuo_ZuDui : BaseTask
    {
        private Dictionary<string, Pixel> Colors = new Dictionary<string, Pixel>()
        {
            {"探索开始",new Pixel(945,526,"f3b25e-101010",0.97) },
            {"退出探索",new Pixel(737,400,"f3b25e-101010",0.97) },
            {"创建组队房间",new Pixel(737,496,"f3b25e-101010",0.97) },
            {"继续邀请",new Pixel(835,451,"f6b65f-101010",0.97) },
            {"轮换关闭",new Pixel(130,671,"1e245c-101010",0.98) },
        };

        private int count = 0;

        private bool DaGuoBossLe = false;
        private int FindNowCount = 0;

        /// <summary>
        /// 跨区选择
        /// </summary>
        private Dictionary<string, Position> InvFriendPoss = new()
        {
            {"好友",new Position(486,96,569,135) },
            {"寮友",new Position(374,97,436,131) },
            {"跨区",new Position(601,99,679,135) }
        };

        private bool isBoss = false;

        private MemPics pics_Boss;

        private int Slide_Count = 0;

        /// <summary>
        /// 突破卷数量
        /// </summary>
        private int tupo_Count = -1;

        private TanSuo_ZuDui_Config Config { get; set; }

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "探索组队");
            //初始化组件
            foreach (var item in Colors)
                item.Value.SetXsoft(dm);
            foreach (var item in InvFriendPoss)
                item.Value.SetXsoft(dm);
            pics_Boss = Mp.Filter("找怪Boss");
            XYoloV8.Reset();
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Config = new(configs);
            log.Info(Config.ToString());
            log.Warn("请注意，组队探索任务会使检测突破卷的设置失效！若需要，请单刷探索！3秒后继续！");
            Sleep(3000);

            if (Config.Location == "队长")
            {
                Main_DuiZhang();
            }
            else if (Config.Location == "队长-手动邀请")
            {
                Main_DuiZhang_Hand();
            }
            else if (Config.Location == "队员")
            {
                Main_DuiYuan();
            }
            else
                log.Error("未知的组队身份！无法继续执行组队探索任务！");
            UserNotificationMessage = $"共战斗{count}/{Config.Ncount}次.";
            return;
        }

        #region 战斗流程

        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("战斗_")
                .Add(Mp.Filter("活动"));
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(500);
                if (Mp.Filter("探索界面").FindAll())
                {
                    isbreak = true;
                    continue;
                }
                var p = pics.FindAllEa();
                if (p is null) continue;
                log.Info($"执行点击：{p._Name}");
                p.Click();
            }
        }

        #endregion 战斗流程

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            //点击开始
            log.Info("战斗开始");
            var pics = Mp.Filter("战斗_")
                .Add(Mp.Filter("活动"));

            bool ret_bol = false;
            bool isbreak = false;
            while (!isbreak)
            {
                //场景判断
                if (Mp.Filter("探索界面").FindAll())
                {
                    log.Warn("当前场景在探索任务主界面，退出战斗！");
                    ret_bol = true;
                    isbreak = true;
                    break;
                }
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("胜利") || p.Name.Contains("达摩"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(120);
                }
                if (p.Name.Contains("失败"))
                {
                    ret_bol = false;
                    isbreak = true;
                }
            }
            if (ret_bol)
                Combat_End();

            return ret_bol;
        }

        /// <summary>
        /// 退出任务到探索
        /// </summary>
        private void Exit()
        {
        Re:
            //在组队场景中的话执行退出组队房间
            if (NowISInvScene())
            {
                Sleep(500);
                log.Info("退出组队界面..");
                Fast.Click(36, 25, 70, 58);
                Sleep(500);
                Fast.Click(696, 415, 826, 456);
                Sleep(500);
                return;
            }

            //在探索场景中的话执行退出到探索，并取消继续邀请
            if (Mp.Filter("探索界面").FindAll())
            {
                Fast.Click(35, 48, 72, 80);
                while (!Colors["退出探索"].Await(null, 3000))//等待探索退出按钮
                    Fast.Click(35, 48, 72, 80);
                Fast.Click(728, 388, 826, 420);//点击确定
                Sleep(1000);
                if (Config.Location != "队员")
                {
                    log.Warn("等待默认邀请队友弹窗后继续··");
                    while (!Colors["继续邀请"].Await(null, 3000))
                    {
                        log.Warn("等待默认邀请队友弹窗后继续··");
                        Sleep(50);
                    }
                    ;
                    Fast.Click(464, 415, 589, 452);//点击取消
                    Sleep(500);
                }
                return;
            }
            log.Warn("退出流程，没有找到符合要求的场景（探索界面、组队界面），重试···");
            goto Re;
        }

        /// <summary>
        /// 领取宝箱
        /// </summary>
        private void PickBaoXiang()
        {
            log.Info("尝试查找宝箱并领取..");
            while (Mp.Filter("宝箱领取").FindAllAndClick())
            {
                Sleep(1500);
                Fast.Click(1157, 145);
                Sleep(1500);
            }

            // 等待返回探索主界面
            int waitCount = 0;
            while (!Scene.NowIsScene("探索") && waitCount < 5)
            {
                log.Debug("等待返回探索主界面...");
                Fast.Click(1157, 145);
                Sleep(1000);
                waitCount++;
            }
        }

        /// <summary>
        /// 查找战斗对象
        /// </summary>
        /// <returns>True:进入了战斗，False:需要滑动</returns>
        private bool Find_CombatObj()
        {
            FindNowCount++;
            bool findOK = false;
            //先扫一下Boss
            if (pics_Boss.FindAll(out int x, out int y))
            {
                log.Info("找到Boss，准备战斗..");
                Operational.Click(x, y);
                isBoss = true;
                findOK = true;
                Sleep(200);
            }
            else//Ai找图
            {
                var boxs = Fast.YoloV8Det_All(YoloModels.Detect);
                if (boxs != null && Config.Yolo_ObjCombatStr == "")
                    foreach (var box in boxs)
                    {
                        if (box.Name.ToString().Contains("combat"))
                        {
                            var point = new Position(box.Bounds.GetPosStr());
                            //判断是否为Boss
                            if (pics_Boss.FindAsPosition(point))
                            {
                                log.Info("找到Boss，准备战斗..");
                                isBoss = true;
                            }
                            else log.Info("找到小怪，准备战斗..");
                            Operational.Click(point.GetCenterPoint());
                            findOK = true;
                            Sleep(200);
                            break;
                        }
                    }
                else if (boxs != null && Config.Yolo_ObjCombatStr != "")
                {
                    List<Position> combatLists = [];
                    boxs.ToList().ForEach(t =>
                    {
                        if (t.Name.ToString().Contains("combat"))
                            combatLists.Add(new(t.Bounds.GetPosStr()));
                    });

                    foreach (var box in boxs)
                    {
                        if (box.Name.ToString().Contains(Config.Yolo_ObjCombatStr))
                        {
                            var pos = new Position(box.Bounds.GetPosStr());
                            //输出对象置信度
                            log.Debug(Config.Yolo_ObjCombatStr + "目标置信度：" + box.Confidence);
#if DEBUG
                            //打标
                            //Dm.CaptureJpg(0, 0, 2000, 2000, @"E:\A-jpg\TasnSuo[" + box.Confidence.ToString("F2") + "]-" + DateTime.Now.Ticks + ".jpg", 95);
#endif
                            //找到最近的战斗坐标
                            var combat_pos = Algorithm.FindClosestBoxAbove(pos, combatLists, 250);
                            if (combat_pos == null)
                                continue;

                            //判断是否为Boss
                            if (pics_Boss.FindAsPosition(combat_pos))
                            {
                                log.Info("找到Boss，准备战斗..");
                                isBoss = true;
                            }
                            else log.Info("找到小怪，准备战斗..");
                            Operational.Click(combat_pos.GetCenterPoint());
                            findOK = true;
                            Sleep(200);
                            break;
                        }
                    }
                }
            }

            if (FindNowCount >= 3)
            {
                FindNowCount = 0;
                if (!findOK) return false;
            }
            if (!findOK)//重新查找
                return Find_CombatObj();
            else if (Mp.Filter("探索界面").FindAll())//检查当前是否进入了战斗
                return Find_CombatObj();

            FindNowCount = 0;
            return true;
        }

        /// <summary>
        /// 邀请成员
        /// </summary>
        private bool InviteName()
        {
            string Team1_Orig = Config.Team1;
            if (!NowISInvScene())
            {
                log.Warn("不在组队界面内，无法邀请成员！");
                return false;
            }
            log.Info("打开邀请界面");
            Fast.Click(1059, 227, 1107, 275);
            Sleep(1000);
            List<string> Team1 = ["好友", "寮友", "跨区"];
            Team1.Remove(Team1_Orig);
            int i = -1;
        ReTry1:
            if (Config.Name1 != "")
            {
                string team_show = i != -1 ? Team1[i - 1] : Team1_Orig;
                log.Info("开始邀请队友1，" + Config.Name1 + "|" + team_show);
                if (i == -1)
                {
                    InvFriendPoss[Team1_Orig].Click();
                    Sleep(1000);
                }
                var point = Config.Name1_Pic.FindPoint(Dm);
                if (point is null)
                {
                    //不存在
                    log.Warn("队友没有找到，或不在线？可能在其它区域，更换队友所在区域再试一下！");
                    if (i == -1) i = 0;
                    if (i >= Team1.Count)
                    {
                        log.Warn("尝试邀请队友失败，队友可能真的不在线！或请检查队友名称是否正确！点击 取消");
                        Fast.Click(469, 557, 535, 592);
                        return false;
                    }
                    InvFriendPoss[Team1[i++]].Click();
                    Sleep(1000);
                    goto ReTry1;
                }
                if (i != -1)
                    Config.Team1 = Team1[i - 1];
                Operational.Click(point);
                log.Info("点击 邀请");
                Fast.Click(741, 559, 810, 592);
                Sleep(1200);
            }
            Sleep(1000);
            return true;
        }

        /// <summary>
        /// 主流程
        /// </summary>
        /// <returns>True:任务结束 False:重进</returns>
        private bool FakeOneCount(out bool isFakeBoss)
        {
        ReStart:
            isFakeBoss = false;
            //if (!Mp.Filter("探索界面").Wait(5)) JoinMainSecen();//进入主场景
            if (!WaitMain()) goto ReStart;

            Check_GouLiang();

            if (!Find_CombatObj())
            {
                if (!Slide())
                {
                    log.Info("滑动到顶，执行重进操作..");
                    ReGotoMainSecen();
                    return false;
                }
            }
            if (Combat())
            {
                if (!Config.Counting_Mode)
                {
                    count++;
                    log.Info_Green($"组队探索战斗胜利，战斗次数：{count}/{Config.Ncount}");
                    if (count >= Config.Ncount)
                        return true;
                }
                else
                    log.Info_Green($"组队探索战斗胜利");
            }
            else
            {
                log.Warn($"组队探索战斗失败，请检查您的队伍配置是否正常！战斗次数：{count}/{Config.Ncount}");
                Defeated();
            }

            if (isBoss)
            {
                if (Config.Counting_Mode)
                {
                    count++;
                    log.Info_Green($"当前Boss战斗次数：{count}/{Config.Ncount}");
                }
                isBoss = false;
                isFakeBoss = true;
                Sleep(500);
                if (count >= Config.Ncount)
                    return true;
                ReGotoMainSecen();
                Sleep(400);
                return false;
            }
            Sleep(50);
            goto ReStart;
        }

        /// <summary>
        /// 队员主流程
        /// </summary>
        private void Main_DuiYuan()
        {
            log.Info_Green("开启探索组队-队员流程，你可以在探索，以及组队房间中开始！");
            var pics = Mp.Filter("战斗_");
            var _Ts = Mp.Filter("探索界面");
            var _Zding = Mp.Filter("在组队中");
        ReAccept:
            if (!NowISInvScene())
            {
                log.Info("等待接受邀请...");
                int x, y;
                while (!Mp.Filter("组队接受").FindAll(out x, out y))
                    Sleep(100);
                log.Info("接受邀请...");
                Fast.Click(x, y);
            }
            Sleep(1000);
            if (!NowISInvScene())
            {
                log.Warn("没有进入到组队房间，重新接受邀请...");
                goto ReAccept;
            }
            log.Info("当前在组队房间中，开始队员任务...");

            while (true)
            {
                bool isbreak = false;
                while (!isbreak)
                {
                    if (_Ts.FindAll())
                        if (!_Zding.FindAll())
                        {
                            log.Info("在探索主界面，但是没有队友，退出探索主界面...");
                            Fast.Click(35, 51, 71, 80);
                            Sleep(200);
                            while (!Colors["退出探索"].Await(null, 3000))//等待探索退出按钮
                            {
                                Sleep(50);
                                Fast.Click(35, 51, 71, 80);
                                Sleep(50);
                            }
                            Sleep(800);
                            Fast.Click(707, 392, 735, 421);
                            Sleep(400);
                            goto ReAccept;
                        }
                    if (Scene.NowIsScene("探索"))
                    {
                        PickBaoXiang();
                        log.Info("在探索界面，重新等待队友邀请...");
                        goto ReAccept;
                    }
                    Sleep(400);
                    var p = pics.FindAllEa();
                    if (p is null) continue;
                    log.Info($"执行点击：{p._Name}");
                    p.Click();
                    if (p.Name.Contains("胜利"))
                    {
                        count++;
                        log.Info_Green($"组队探索战斗胜利，战斗次数：{count}/{Config.Ncount}");
                        Combat_End();
                        isbreak = true;
                        Sleep(120);
                    }
                    if (p.Name.Contains("失败"))
                    {
                        log.Warn($"组队探索战斗失败了，战斗次数：{count}/{Config.Ncount}");
                        isbreak = true;
                    }
                }
                if (count >= Config.Ncount)
                {
                    log.Info_Green("队员任务完成，结束！");
                    goto Exit;
                }
            }

        Exit:
            Exit();
        }

        /// <summary>
        ///队长主流程
        /// </summary>
        /// <param name="isHand">手动邀请模式？</param>
        private void Main_DuiZhang(bool isHand = false)
        {
            if (isHand) goto StartFake;

            if (NowISInvScene())
            {
                log.Info_Green("当前在组队房间，直接开始邀请队友...");
                goto ReInv;
            }

            if (Scene.NowScene != "探索")
                if (!Scene.TO.TanSuo())//先去探索
                {
                    log.Warn("组队探索任务无法继续，当前游戏所在场景未知，请调整到庭院或探索主界面开始脚本！");
                    return;
                }
            PickBaoXiang();

            if (!Colors["探索开始"].Find(null))
            {
                Fast.Click(1075, 503, 1224, 547);//点击K28
                Sleep(200);
            }
            Sleep(100);
            Mp.Filter("K28_组队按钮").FindAllAndClick();
            Sleep(100);
            if (Colors["创建组队房间"].Find(null))
            {
                Fast.Click(408, 417, 430, 439);//点击不公开
                log.Info("不公开..");
                Sleep(200);
                Fast.Click(566, 496, 709, 533);//点击创建按钮
                log.Info("创建..");
                Sleep(200);
            }

        //邀请队友
        ReInv:
            if (Mp.Filter("组队挑战按钮").FindAll())
            {
                log.Info("房间已有队友...可以直接开始，切换为手动邀请模式继续");
                Config.Location = "队长-手动邀请";
                Main_DuiZhang_Hand();
                return;
            }

            if (!InviteName())
            {
                Sleep(2000);
                goto ReInv;
            }
        ReDelay:
            log.Info("已邀请队友，等待15秒！");
            //等待可以战斗
            int i = 15;
            while (i > 0)
            {
                if (Mp.Filter("组队挑战按钮").FindAll())
                {
                    log.Info_Green("等待到队友，开始挑战！");
                    Sleep(200);
                    Fast.Click(1183, 616, 1249, 676);
                    break;
                }
                Sleep(1000);
                i--;
            }
            if (i <= 0) { log.Warn("重新邀请队友..."); goto ReInv; }
        StartFake:
            //开始挑战
            Sleep(50);
            if (count >= Config.Ncount)
            {
                Exit();
                return;
            }

            if (FakeOneCount(out bool isFakeBoss))
            {
                Exit();
                log.Info_Green("组队任务完成，结束！");
                return;
            }
            goto ReDelay;
        }

        /// <summary>
        /// 手动 队长主流程
        /// </summary>
        private void Main_DuiZhang_Hand()
        {
            if (!NowISInvScene())
            {
                log.Error("您选择的手动邀请模式，当前不在组队房间，无法继续组队探索...");
                return;
            }
            log.Info_Green("开启成功，当前在房间中，请手动邀请队友到房间内，脚本将自动开始！");
        ReInv:
            //等待可以战斗
            int i = 3;
            while (i > 0)
            {
                if (Mp.Filter("组队挑战按钮").FindAll())
                {
                    log.Info_Green("等待到队友，开始挑战！");
                    Sleep(200);
                    Fast.Click(1183, 616, 1249, 676);
                    break;
                }
                Sleep(1000);
                i--;
            }
            if (i <= 0) { log.Info("等待用户手动邀请队友..."); goto ReInv; }
            Main_DuiZhang(true);
        }

        /// <summary>
        /// 是否在组队房间界面
        /// </summary>
        /// <returns></returns>
        private bool NowISInvScene()
        {
            return Mp.Filter("组队场景").FindAll();
        }

        /// <summary>
        /// 重新进入探索主界面
        /// </summary>
        private void ReGotoMainSecen()
        {
            Sleep(500);
            if (Mp.Filter("探索界面").FindAll())
            {
                Fast.Click(35, 48, 72, 80);
                Sleep(200);
                while (!Colors["退出探索"].Await(null, 3000))//等待探索退出按钮
                {
                    Sleep(50);
                    Fast.Click(35, 48, 72, 80);
                    Sleep(50);
                }

                //Sleep(1000);
                while (Colors["退出探索"].Find(null))//点击退出探索的确定
                {
                    Sleep(400);
                    //场景判断
                    if (Scene.NowIsScene("探索"))
                        break;
                    Fast.Click(707, 392, 735, 421);
                    Sleep(400);
                }

                Sleep(1200);
            }
            log.Warn("等待默认邀请队友弹窗后继续··");
            while (!Colors["继续邀请"].Await(null, 3000))
            {
                log.Warn("等待默认邀请队友弹窗后继续··");
                Sleep(50);
            }
            ;
            DaGuoBossLe = false;
            Sleep(100);
            log.Warn("点击继续邀请队友··");
            Fast.Click(703, 410, 825, 452);//点击邀请
            Slide_Count = 0;
        }

        private void Check_GouLiang()
        {
            bool retry = false;
            log.Debug("检查自动轮换是否开启..仅支持队长..");
        Re:
            if (Colors["轮换关闭"].Find(null) && retry == false)
            {
                log.Info("检测到狗粮轮换处于关闭状态，尝试开启..");
                Fast.Click(130, 671);
                Sleep(1000);
                retry = true;
                goto Re;
            }
            if (Colors["轮换关闭"].Find(null) && retry)
            {
                log.Info("开始添加轮换狗粮..");
                Fast.Click(65, 675);
                Sleep(1000);
                log.Info("点击一键清空并选中候补出战..");
                Fast.Click(1027, 430);
                Sleep(1000);
                Fast.Click(809, 281);
                Sleep(1000);
                log.Info("切换到素材..");
                Fast.Click(63, 649);
                Sleep(1000);
                Fast.Click(68, 308);
                Sleep(1000);
                for (int i = 0; i < 6; i++)
                {
                    log.Info($"开始添加狗粮{i + 1}..");
                    Fast.LongClick(182, 584, 1500);
                    Sleep(1000);
                    if (i != 5)
                        Fast.Click(182, 584);
                    Sleep(1000);
                }
                log.Info("点击确定..");
                Fast.Click(1159, 442);
                Sleep(1000);
                retry = false;
                goto Re;
            }
        }

        /// <summary>
        ///滑动界面
        /// </summary>
        /// <returns>true:OK false:需要重进</returns>
        private bool Slide()
        {
            if (Slide_Count >= 2)
            {
                if (Config.Yolo_ObjCombatStr == "" && !DaGuoBossLe)//如果没打过Boss 多等一会再扫描
                {
                    log.Info("要重进了，但是还没看到boss，多等一会再扫怪~");
                    DaGuoBossLe = true;
                    Sleep(1500);
                    return true;
                }
                Slide_Count = 0;
                return false;
            }
            Operational.Slide_Pos(new Position("1271,665,1289,691"), new Position("115,385,219,468"));
            Sleep(500);
            Slide_Count++;
            return true;
        }

        /// <summary>
        /// 等待探索战斗主界面
        /// </summary>
        private bool WaitMain()
        {
            int ReTry = 3;
            int count = 0;
            while (!Mp.Filter("探索界面").Wait())
            {
                if (count >= ReTry)
                {
                    log.Warn("移动至探索场景主界面超时！");
                    return false;
                }
                count++;
            }
            return true;
        }
    }

    internal class TanSuo_ZuDui_Config : BaseTaskConfig
    {
#pragma warning disable CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑添加 "required" 修饰符或声明为可为 null。

        public TanSuo_ZuDui_Config(Configs configs)
#pragma warning restore CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑添加 "required" 修饰符或声明为可为 null。
        {
            Configs = configs;
            Ncount = configs.Count;
            Yolo_ObjCombatStr = configs.Others.TryGetValue("CombatStr", out var yolo_objcombatstr) ? yolo_objcombatstr : "";
            Counting_Mode = configs.Others.TryGetValue("Counting_Mode", out var counting_mode) ? counting_mode == "True" : false;
            Location = configs.Others.TryGetValue("Location", out var location) ? location : "";
            if (Location == "") throw new Exception("御魂组队任务无法继续，未查询到组队身份！(队长？队员？)");
            if (Location == "队长") InitName_Pic1();
        }

        public Configs Configs { get; }

        /// <summary>
        /// 计数模式
        /// True：仅已Boss计数
        /// False：Boss+小怪计数
        /// </summary>
        public bool Counting_Mode { get; }

        /// <summary>
        /// 组队任务身份
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// 队友1号
        /// </summary>
        public string Name1 { get; set; }

        /// <summary>
        /// 成员1名字截图
        /// </summary>
        public MemPic Name1_Pic { get; private set; }

        /// <summary>
        /// 队友1 邀请队友所在区域
        /// </summary>
        public string Team1 { get; set; }

        /// <summary>
        /// 战斗种类
        /// damo jingyan jinbi 空
        /// </summary>
        public string Yolo_ObjCombatStr { get; }

        public override string ToString()
        {
            return $"探索组队配置-总次数:{Ncount}-身份:{Location}-成员1名:{Name1} 所在:{Team1}";
        }

        /// <summary>
        /// 初始化成员名字截图
        /// </summary>
        /// <exception cref="Exception"></exception>
        /// <exception cref="InvalidCastException"></exception>
        private void InitName_Pic1()
        {
            Team1 = Configs.Others.TryGetValue("Team", out string? value) ? value : "";
            if (Team1 == "") throw new Exception("探索组队任务无法继续，队长必须设置邀请队友所在区域！");
            Name1 = Configs.Others.TryGetValue("Name", out value) ? value : "";
            if (Name1 == "") throw new Exception("探索组队任务无法继续，未查询到其它成员名字！");
            Configs.Others_Obj.TryGetValue(Name1, out object? obj);
            byte[]? bytes = null;
            if (obj is System.Text.Json.JsonElement jsonElement)
                if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.String)
                {
                    string base64String = jsonElement.GetString() ?? throw new Exception("探索组队任务无法继续，JsonElement 中数据为空！");
                    bytes = Convert.FromBase64String(base64String);
                }
                else throw new InvalidCastException("JsonElement 不包含 Base64 编码的字符串。");
            else
                bytes = obj as byte[];
            if (bytes is null) throw new Exception("探索组队任务无法继续，成员没有对应截图数据！");
            int ptr, size;
            ptr = new Data_Pic("成员1", "探索组队", bytes).GetMemPtr(out size);
            Name1_Pic = new MemPic("探索组队.成员1.bmp", $"{ptr},{size}").SetFindPosition(new(0, 0, 2000, 2000));
        }
    }
}