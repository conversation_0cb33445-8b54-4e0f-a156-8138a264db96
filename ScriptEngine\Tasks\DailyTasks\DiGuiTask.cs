﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Tasks.DailyTasks
{
    internal class DiGuiTask : BaseTask
    {
        private Dictionary<string, Position> ClickPos_Pairs = new()
        {
            {"地鬼入口",new(649,640,692,683) },
            {"筛选",new(1114,37,1152,75) },
            {"收藏",new(1195,597,1227,681) },
            {"热门",new(1190,245,1226,322) },
            {"挑战",new(1095,505,1213,590) },
            {"结束",new(52,39,93,79) },
            {"退回到地鬼Main",new(1194,26,1231,61) },
        };

        private Dictionary<string, Position> FindPos_Pairs = new()
        {
            {"声望获取",new(1164, 59, 1278, 95) },
            {"挑战1",new(1091,235,1133,275) },
            {"挑战2",new(1089,390,1134,436) },
            {"挑战3",new(1088,545,1132,587) },
        };

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, className);
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            ToDiguiScence();
            var points = Get_DiGui_Points();
            NCount = PointsToNCount(points);
            log.Info_Green($"当前声望：{points},支持战斗{NCount}个地域鬼王");
            log.Info_Green("现在开始选择鬼王并战斗..优先选择收藏中的鬼王..");
            Main();
        }

        /// <summary>
        /// 战斗次数
        /// </summary>
        private int NCount { get; set; }

        private void Main()
        {
            string str = "收藏";
            // 选择收藏中的鬼王
            while (Fast.Ocr_String(1151, 673, 1266, 719).Contains("阴阳"))
            {
                Fast.Click(ClickPos_Pairs["筛选"]);
                Sleep(1000);
            }

            // 添加标志防止无限循环
            bool hasCheckedHot = false;

        ReDo:
            Fast.Click(ClickPos_Pairs[str]);
            Sleep(1000);
            var ShouCang_Count = Find_ShouCang_DiGuiCount();
            if (ShouCang_Count < NCount)
            {
                // 如果已经检查过热门类别，不再重复检查
                if (hasCheckedHot || str == "热门")
                {
                    log.Warn($"可用的地鬼数量不足{NCount}个，将只进行{ShouCang_Count}次战斗");
                    NCount = Math.Max(ShouCang_Count, 1); // 确保至少进行一次战斗
                }
                else
                {
                    log.Warn($"收藏的地鬼数量不足{NCount}个，尝试选择热门中的鬼王战斗..");
                    str = "热门";
                    hasCheckedHot = true;
                    goto ReDo;
                }
            }

            for (int i = 0; i < NCount; i++)
            {
                SelectDiGuiCombat(i + 1);
                if (i + 1 < NCount)
                {
                    Sleep(1000);
                    Fast.Click(ClickPos_Pairs["筛选"]);
                    Sleep(1000);
                    Fast.Click(ClickPos_Pairs[str]);
                    Sleep(1000);
                }
            }
            log.Info_Green($"地鬼任务结束，退回到探索..");
            Fast.Click(ClickPos_Pairs["结束"]);
            Sleep(1500);
        }

        /// <summary>
        /// 选择地鬼并战斗
        /// </summary>
        private void SelectDiGuiCombat(int number)
        {
            Fast.Click(FindPos_Pairs[$"挑战{number}"]);
            Sleep(1000);
            log.Info($"开始第{number}个鬼王战斗..点击挑战..");
            Fast.Click(ClickPos_Pairs["挑战"]);
            if (!Combat())
            {
                log.Warn($"地鬼战斗失败，请检查您的队伍配置是否正常！任务结束..");
                throw new TaskCanceledException();
            }
            Sleep(1000);
            Fast.Click(ClickPos_Pairs["退回到地鬼Main"]);
            Sleep(1000);
        }

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            //点击开始
            Sleep(800);
            log.Info("战斗开始");
            var pics = Mp.Filter("地鬼.ZD_");
            bool ret_bol = false;
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("胜利") || p.Name.Contains("达摩")
                    || p.Name.Contains("获得奖励"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败"))
                {
                    ret_bol = false;
                    isbreak = true;
                }
            }
            if (ret_bol)
                Combat_End();

            return ret_bol;
        }

        /// <summary>
        /// 胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("地鬼.ZD_");
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                if (p.Name.Contains("挑战"))
                {
                    isbreak = true;
                    continue;
                }
                log.Info($"执行点击：{p._Name}");
                p.Click();
            }
        }

        private int Find_ShouCang_DiGuiCount()
        {
            int count = 0;
            var shaixuanpic = Mp.Filter("筛选_挑战");
            bool tiaozhan1 = shaixuanpic.FindAsPosition(FindPos_Pairs["挑战1"]);
            bool tiaozhan2 = shaixuanpic.FindAsPosition(FindPos_Pairs["挑战2"]);
            bool tiaozhan3 = shaixuanpic.FindAsPosition(FindPos_Pairs["挑战3"]);
            if (tiaozhan1) count++;
            if (tiaozhan2) count++;
            if (tiaozhan3) count++;
            return count;
        }

        /// <summary>
        /// 根据声望返回可挑战的鬼王次数
        /// </summary>
        /// <param name="points"></param>
        /// <returns></returns>
        private int PointsToNCount(int points)
        {
            if (points < 2000)
                return 1; // 声望小于2000点时，每天只能挑战1个地域鬼王
            else if (points < 10000)
                return 2; // 声望达到2000点时，每天可以挑战2个地域鬼王
            else
                return 3; // 声望达到10000点时，每天可以挑战3个地域鬼王
        }

        /// <summary>
        /// 获取地鬼声望点数
        /// </summary>
        /// <returns></returns>
        private int Get_DiGui_Points()
        {
            log.Info("尝试获取地鬼声望点数..");
            int maxRetries = 5;
            int currentRetry = 0;

            while (currentRetry < maxRetries)
            {
                string diGui_points = Fast.Ocr_String_V2(FindPos_Pairs["声望获取"]);

                if (int.TryParse(diGui_points, out int result))
                {
                    return result;
                }
                else
                {
                    // OCR解析失败时，继续循环尝试
                    currentRetry++;
                    if (currentRetry >= maxRetries)
                    {
                        log.Warn($"OCR识别地鬼声望点数失败，已尝试{maxRetries}次");
                        return 0; // 返回默认值
                    }

                    // 短暂延迟后再次尝试
                    Sleep(500);
                }
            }

            return 0; // 默认返回值
        }

        /// <summary>
        /// 进入地鬼场景 有体服适配
        /// </summary>
        /// <returns></returns>
        private bool ToDiguiScence()
        {
            if (NowIsDiguiScence()) return true;
            log.Info("前往地鬼任务场景..");
            Scene.TO.TanSuo();
            Sleep(1000);
            if (Db.GameSetting.IsTifu)
                Fast.Click(751, 644, 794, 687);
            else
                Fast.Click(ClickPos_Pairs["地鬼入口"]);
            Sleep(1000);

            int maxRetries = 10;
            int currentRetry = 0;

            while (!NowIsDiguiScence() && currentRetry < maxRetries)
            {
                Sleep(1000);
                if (!NowIsDiguiScence())
                    Fast.Click(ClickPos_Pairs["地鬼入口"]);

                currentRetry++;
            }

            if (currentRetry >= maxRetries)
            {
                log.Warn("尝试进入地鬼场景失败，已达到最大尝试次数");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 判断当前是否在地鬼场景
        /// </summary>
        /// <returns></returns>
        private bool NowIsDiguiScence()
        {
            var pics = Mp.Filter("主场景");
            return pics.FindAll();
        }

        /// <summary>
        /// 等待前往地鬼场景
        /// </summary>
        /// <returns></returns>
        private bool WaitDiguiScence()
        {
            var pics = Mp.Filter("主场景");
            if (pics.Wait()) return true;
            return false;
        }
    }
}