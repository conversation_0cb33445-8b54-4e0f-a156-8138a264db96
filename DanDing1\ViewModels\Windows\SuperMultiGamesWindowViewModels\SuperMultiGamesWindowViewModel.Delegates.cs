﻿/*
 * 文件名: SuperMultiGamesWindowViewModel.Delegates.cs
 * 职责描述: 定义和初始化所有委托及相关功能
 * 该文件包含了用于跨线程通信的委托定义、UI更新委托和其初始化方法
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DanDing1.ViewModels.Windows
{
    public partial class SuperMultiGamesWindowViewModel : ObservableObject
    {
        #region 委托定义

        /// <summary>
        /// 主线程游戏日志输出委托，用于任务线程调用
        /// </summary>
        public UIGameLogDelegate GameLogOutputDelegate;

        /// <summary>
        /// 主线程日志输出委托，用于任务线程调用
        /// </summary>
        public LogDelegate LogOutputDelegate;

        /// <summary>
        /// 游戏日志更新后通知UI滚动到底部
        /// </summary>
        public LogUpdatedDelegate OnGameLogUpdated;

        /// <summary>
        /// 主日志更新后通知UI滚动到底部
        /// </summary>
        public LogUpdatedDelegate OnMainLogUpdated;

        /// <summary>
        /// 更新游戏运行状态委托，用于任务线程调用
        /// </summary>
        public UpdateGameStatusDelegate OnUpdateGameStatus;

        /// <summary>
        /// 日志委托类型，用于在UI线程更新日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public delegate void LogDelegate(string message);

        /// <summary>
        /// 日志更新后通知UI滚动到底部的委托
        /// </summary>
        public delegate void LogUpdatedDelegate();

        /// <summary>
        /// 游戏日志委托类型，用于在UI线程更新游戏日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public delegate void UIGameLogDelegate(string message);

        /// <summary>
        /// 更新游戏运行状态委托类型
        /// </summary>
        /// <param name="gameId">游戏ID</param>
        /// <param name="status">运行状态</param>
        public delegate void UpdateGameStatusDelegate(int gameId, string status);

        #endregion 委托定义

        /// <summary>
        /// 初始化委托方法
        /// </summary>
        private void InitializeDelegates()
        {
            // 初始化日志输出委托
            LogOutputDelegate = new LogDelegate(message =>
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    AddLog(message);
                });
            });

            // 初始化更新游戏运行状态委托
            OnUpdateGameStatus = new UpdateGameStatusDelegate((gameId, status) =>
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var game = SuperMultiGame_DataModelCollection.FirstOrDefault(g => g.GameId == gameId);
                    if (game != null)
                    {
                        game.RunningStatus = status;
                    }
                });
            });
        }
    }
}