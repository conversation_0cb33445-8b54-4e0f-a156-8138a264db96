﻿using DamoControlKit.Model;
using DamoControlKit.runtimes;
using NLog;
using OpenCvSharp;
using ScriptEngine.Frames;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace ScriptEngine
{
    /// <summary>
    /// 主脚本流程类
    /// </summary>
    public class DDScript
    {
        private CancellationTokenSource? ct { get; set; }
        private Task? task { get; set; }

        /// <summary>
        /// 任务结束回调函数
        /// </summary>
        public TaskEnded_CallBack? TaskEnded { get; set; }

        /// <summary>
        /// 任务结束回调记录数据函数
        /// </summary>
        public TaskEnded_CallBackData? TaskEnded_Data { get; set; }

        /// <summary>
        /// 任务用户通知信息回调函数
        /// </summary>
        public TaskUserNotificationMessage? TaskUserNotificationMessage { get; set; }

        public Log? log { get; set; } // 修改为可为 null

        private List<string> task_names = new List<string>();

        public void Start(DDBuilder dB)
        {
            log = dB.Log;
            //记录 task_names
            task_names = dB.TaskLists.TaskLists.Values.Select(x => $"{x.Name}-{x.Count}次").ToList();

            if (log is null) // 检查 log 是否为 null
                throw new InvalidOperationException("Log 未初始化");

            ct = new CancellationTokenSource();
            task = Task.Run(() =>
            {
                log.Debug("后台线程开启！");
                ScriptFrame scriptFrame = new(ct, dB);
                scriptFrame.GetDmID += SetDmID;
                try
                {
                    scriptFrame.Run();
                }
                catch (TaskCanceledException)
                {
                    log.Info("任务流程被响应，并且已经取消了！");
                }
                catch (Exception e)
                {
                    if (e.Message.Contains("A task was canceled") || (e.InnerException?.Message?.Contains("A task was canceled") ?? false))
                    {
                        log.Info("任务已被取消");
                    }
                    else
                    {
                        log.Write("发生未知异常！");
                        log.Write(e?.StackTrace?.ToString() ?? "没有相应的堆栈信息！");
                        log.Write(e?.InnerException?.Message ?? "没有相应的异常描述信息！");
                        log.Error("任务出现异常，现已结束执行：" + e?.Message);
                    }
                }

                TaskUserNotificationMessage?.Invoke(scriptFrame.Task_UserNotificationMessage);
                TaskEnded?.Invoke();
                TaskEnded_Data?.Invoke(GetRecordData());
                scriptFrame.Free();
            }, ct.Token);
            log.Debug("任务线程已经在后台执行，UI控制权转移给用户！");
        }

        private int Dm_Mian_Id = -1;
        private int Dm_Sub_Id = -1;


        public void SetDmID(int main_id, int sub_id)
        {
            Dm_Mian_Id = main_id;
            Dm_Sub_Id = sub_id;
            log?.Debug($"dm_id已捕获：main[{main_id}] sub[{sub_id}]"); // 使用空条件运算符
        }

        /// <summary>
        /// 获取点击记录数据
        /// </summary>
        /// <returns></returns>
        private RecordData GetRecordData()
        {
            RecordData data = new RecordData();
            Record.GetData(Dm_Mian_Id, out data.MainClick, out data.MainPoints);
            Record.GetData(Dm_Sub_Id, out data.SubClick, out data.SubPoints);
            data.TaskNames = task_names;
            return data;
        }

        public async Task Stop()
        {
            if (log is null) // 检查 log 是否为 null
                throw new InvalidOperationException("Log 未初始化");

            if (ct is null)
            {
                log.Write("您当前还没有开始任务流程！");
                return;
            }
            if (ct.IsCancellationRequested)
            {
                log.Write("结束命令已经发送！请等待任务流程结束！");
                return;
            }
            log.Debug("向后台发送取消任务的命令..");
            ct.Cancel();
            if (task is not null) await task;
        }

        /// <summary>
        /// 导出点击数据集合
        /// </summary>
        /// <param name="path"></param>
        public void SaveClickDataBase(string path)
        {
            if (log is null) // 检查 log 是否为 null
                throw new InvalidOperationException("Log 未初始化");

            Record.SaveTo(path);
        }
    }
}