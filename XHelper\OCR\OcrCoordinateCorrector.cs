using System;
using System.Collections.Generic;
using System.Drawing;
using XHelper.Models;

namespace XHelper.OCR
{
    /// <summary>
    /// OCR坐标矫正工具类，用于调整边框引起的坐标偏移
    /// </summary>
    internal static class OcrCoordinateCorrector
    {
        /// <summary>
        /// 使用图像处理器信息和OCR配置进行OcrLite引擎坐标矫正
        /// 同时考虑图像处理器添加的边框和OCR引擎的Padding参数
        /// </summary>
        /// <param name="blocks">原始TextBlock列表</param>
        /// <param name="imageProcessor">图像处理器</param>
        /// <param name="config">OCR配置</param>
        /// <returns>矫正后的TextBlock列表</returns>
        public static List<XOcr_TextBlock> CorrectOcrLiteCoordinates(
            List<XOcr_TextBlock> blocks,
            IImageProcessor imageProcessor, 
            OcrConfiguration config)
        {
            if (blocks == null || blocks.Count == 0 || imageProcessor == null)
                return new List<XOcr_TextBlock>();

            // 总的左边距 = 图像处理器添加的左边框 + OcrLite的Padding参数/2
            int totalLeftPadding = imageProcessor.GetLeftPadding() + config.Padding / 2;
            
            // 总的上边距 = 图像处理器添加的上边框 + OcrLite的Padding参数/2
            int totalTopPadding = imageProcessor.GetTopPadding() + config.Padding / 2;

            return CorrectCoordinates(blocks, totalTopPadding, totalLeftPadding);
        }

        /// <summary>
        /// 使用图像处理器信息进行坐标矫正
        /// </summary>
        /// <param name="blocks">原始TextBlock列表</param>
        /// <param name="imageProcessor">图像处理器</param>
        /// <returns>矫正后的TextBlock列表</returns>
        public static List<XOcr_TextBlock> CorrectCoordinates(
            List<XOcr_TextBlock> blocks, 
            IImageProcessor imageProcessor)
        {
            if (blocks == null || blocks.Count == 0 || imageProcessor == null)
                return new List<XOcr_TextBlock>();

            return CorrectCoordinates(
                blocks, 
                imageProcessor.GetTopPadding(), 
                imageProcessor.GetLeftPadding());
        }

        /// <summary>
        /// OcrLite引擎坐标矫正
        /// 减去上下各25像素，左右各15像素的边框
        /// </summary>
        /// <param name="blocks">原始TextBlock列表</param>
        /// <returns>矫正后的TextBlock列表</returns>
        public static List<XOcr_TextBlock> CorrectOcrLiteCoordinates(List<XOcr_TextBlock> blocks)
        {
            if (blocks == null || blocks.Count == 0)
                return new List<XOcr_TextBlock>();

            List<XOcr_TextBlock> correctedBlocks = new List<XOcr_TextBlock>();
            
            foreach (var block in blocks)
            {
                // 矫正矩形
                Rectangle correctedRect = new Rectangle(
                    Math.Max(0, block.Rect.X - 15),  // 减去左边框宽度
                    Math.Max(0, block.Rect.Y - 25),  // 减去上边框高度
                    block.Rect.Width,
                    block.Rect.Height
                );
                
                correctedBlocks.Add(new XOcr_TextBlock(block.Text, correctedRect));
            }

            return correctedBlocks;
        }

        /// <summary>
        /// PaddleOCR引擎坐标矫正
        /// 减去四周各25像素的边框
        /// </summary>
        /// <param name="blocks">原始TextBlock列表</param>
        /// <returns>矫正后的TextBlock列表</returns>
        public static List<XOcr_TextBlock> CorrectPaddleOcrCoordinates(List<XOcr_TextBlock> blocks)
        {
            if (blocks == null || blocks.Count == 0)
                return new List<XOcr_TextBlock>();

            List<XOcr_TextBlock> correctedBlocks = new List<XOcr_TextBlock>();
            
            foreach (var block in blocks)
            {
                // 矫正矩形
                Rectangle correctedRect = new Rectangle(
                    Math.Max(0, block.Rect.X - 25),  // 减去左边框宽度
                    Math.Max(0, block.Rect.Y - 25),  // 减去上边框高度
                    block.Rect.Width,
                    block.Rect.Height
                );
                
                correctedBlocks.Add(new XOcr_TextBlock(block.Text, correctedRect));
            }

            return correctedBlocks;
        }

        /// <summary>
        /// 直接根据指定的边框尺寸进行坐标矫正
        /// </summary>
        /// <param name="blocks">原始TextBlock列表</param>
        /// <param name="topPadding">上边框尺寸</param>
        /// <param name="leftPadding">左边框尺寸</param>
        /// <returns>矫正后的TextBlock列表</returns>
        public static List<XOcr_TextBlock> CorrectCoordinates(
            List<XOcr_TextBlock> blocks, 
            int topPadding, 
            int leftPadding)
        {
            if (blocks == null || blocks.Count == 0)
                return new List<XOcr_TextBlock>();

            List<XOcr_TextBlock> correctedBlocks = new List<XOcr_TextBlock>();
            
            foreach (var block in blocks)
            {
                // 矫正矩形
                Rectangle correctedRect = new Rectangle(
                    Math.Max(0, block.Rect.X - leftPadding),
                    Math.Max(0, block.Rect.Y - topPadding),
                    block.Rect.Width,
                    block.Rect.Height
                );
                
                correctedBlocks.Add(new XOcr_TextBlock(block.Text, correctedRect));
            }

            return correctedBlocks;
        }
    }
} 