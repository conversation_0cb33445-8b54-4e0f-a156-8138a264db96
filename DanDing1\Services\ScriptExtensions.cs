using DanDing1.Models.Super;
using DanDing1.ViewModels.Base;
using DanDing1.ViewModels.Windows;
using ScriptEngine.Frames;
using ScriptEngine.Model;
using System;
using System.Collections.ObjectModel;

namespace DanDing1.Services
{
    /// <summary>
    /// 脚本相关扩展方法集合
    /// </summary>
    public static class ScriptExtensions
    {
        /// <summary>
        /// 获取任务列表转换List<string>
        /// </summary>
        /// <param name="configs"></param>
        /// <returns></returns>
        public static List<string> GetTasks(this ObservableCollection<TaskConfigsModel.Configs> configs)
        {
            var tasks = new List<string>();
            foreach (var item in configs)
                tasks.Add(item.ShowName);
            return tasks;
        }

        /// <summary>
        /// SuperMultiGamesWindowViewModel的扩展方法，尝试获取游戏对应的脚本ID
        /// </summary>
        public static bool TryGetGameScriptId(this SuperMultiGamesWindowViewModel vm, int gameId, out int scriptId)
        {
            scriptId = -1;

            try
            {
                // 这里需要根据实际项目结构调整
                // 获取游戏对应的脚本ID，实际实现可能需要访问vm中的字典或调用相应方法
                var gameScriptIds = vm.GetType().GetField("_gameScriptIds",
                    System.Reflection.BindingFlags.NonPublic |
                    System.Reflection.BindingFlags.Instance);

                if (gameScriptIds != null)
                {
                    var dictionary = gameScriptIds.GetValue(vm) as System.Collections.Generic.Dictionary<int, int>;
                    if (dictionary != null && dictionary.ContainsKey(gameId))
                    {
                        scriptId = dictionary[gameId];
                        return true;
                    }
                }
            }
            catch
            {
                // 忽略异常
            }

            return false;
        }

        /// <summary>
        /// 获取GameViewBaseModel关联的DDBuilder
        /// </summary>
        public static ScriptFrame GetDDBuilder(this GameViewBaseModel viewModel)
        {
            try
            {
                // 这里需要根据实际项目结构调整
                // 实际实现可能需要调用viewModel的某个方法或访问特定属性
                // 使用反射访问私有字段或方法示例：
                var builderProperty = viewModel.GetType().GetProperty("DDBuilder",
                    System.Reflection.BindingFlags.NonPublic |
                    System.Reflection.BindingFlags.Instance |
                    System.Reflection.BindingFlags.Public);

                if (builderProperty != null)
                {
                    return builderProperty.GetValue(viewModel) as ScriptFrame;
                }

                // 如果没有直接属性，可能需要通过其他方式获取
                // 这里仅作示例，实际实现需要根据真实代码结构调整
            }
            catch
            {
                // 忽略异常
            }

            return null;
        }

        /// <summary>
        /// 获取SuperMultiGame_DataModel关联的DDBuilder
        /// </summary>
        public static ScriptFrame GetDDBuilder(this SuperMultiGame_DataModel game)
        {
            try
            {
                // 这里需要根据实际项目结构调整
                // 尝试获取game中的DDBuilder引用
                var builderProperty = game.GetType().GetProperty("DDBuilder",
                    System.Reflection.BindingFlags.NonPublic |
                    System.Reflection.BindingFlags.Instance |
                    System.Reflection.BindingFlags.Public);

                if (builderProperty != null)
                {
                    return builderProperty.GetValue(game) as ScriptFrame;
                }

                // 实际实现需要根据项目结构调整
            }
            catch
            {
                // 忽略异常
            }

            return null;
        }
    }
}