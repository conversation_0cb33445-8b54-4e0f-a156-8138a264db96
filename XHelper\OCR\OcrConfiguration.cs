using System;
using System.IO;

namespace XHelper.OCR
{
    /// <summary>
    /// OCR配置类，用于集中管理OCR相关配置
    /// </summary>
    internal class OcrConfiguration
    {
        #region 共享配置

        /// <summary>
        /// 运行时目录路径
        /// </summary>
        public string RuntimesPath { get; set; } = @".\runtimes";

        /// <summary>
        /// 是否保存优化后的临时图片
        /// </summary>
        public bool SaveTempImages { get; set; } = false;

        /// <summary>
        /// 临时图片保存路径
        /// </summary>
        public string TempImagePath { get; set; } = @".\runtimes\Debug\Image_Tmp.bmp";

        #endregion 共享配置

        #region OcrLite配置

        /// <summary>
        /// 图像边距
        /// </summary>
        public int Padding { get; set; } = 50;

        /// <summary>
        /// 图像缩放大小
        /// </summary>
        public int ImgResize { get; set; } = 1024;

        /// <summary>
        /// 检测框分数阈值
        /// </summary>
        public float BoxScoreThresh { get; set; } = 0.618F;

        /// <summary>
        /// 检测框阈值
        /// </summary>
        public float BoxThresh { get; set; } = 0.300F;

        /// <summary>
        /// 裁剪比例
        /// </summary>
        public float UnClipRatio { get; set; } = 2.0F;

        /// <summary>
        /// 是否执行角度检测
        /// </summary>
        public bool DoAngle { get; set; } = true;

        /// <summary>
        /// 是否执行最大角度检测
        /// </summary>
        public bool MostAngle { get; set; } = true;

        /// <summary>
        /// 检测模型路径
        /// </summary>
        public string DetModelPath => Path.Combine(RuntimesPath, "det.onnx");

        /// <summary>
        /// 分类模型路径
        /// </summary>
        public string ClsModelPath => Path.Combine(RuntimesPath, "cls.onnx");

        /// <summary>
        /// 识别模型路径
        /// </summary>
        public string RecModelPath => Path.Combine(RuntimesPath, "rec.onnx");

        /// <summary>
        /// 字典文件路径
        /// </summary>
        public string KeysPath => Path.Combine(RuntimesPath, "keys.txt");

        #endregion OcrLite配置

        #region Tesseract配置

        /// <summary>
        /// Tesseract使用的语言
        /// </summary>
        public string TesseractLanguage { get; set; } = "num";

        /// <summary>
        /// 字符白名单，限制识别范围
        /// </summary>
        public string CharWhitelist { get; set; } = "0123456789/";

        /// <summary>
        /// 页面分割模式
        /// </summary>
        public string PageSegMode { get; set; } = "7"; // PSM_SINGLE_LINE

        #endregion Tesseract配置

        #region PaddleOCR配置

        /// <summary>
        /// PaddleOCR可执行文件名
        /// </summary>
        public string PaddleExeName { get; set; } = "PaddleOCR_cpp.exe";

        /// <summary>
        /// PaddleOCR可执行文件路径
        /// </summary>
        public string PaddleExePath => Path.Combine(RuntimesPath, PaddleExeName);

        /// <summary>
        /// 进程超时时间(毫秒)
        /// </summary>
        public int ProcessTimeoutMs { get; set; } = 30000;

        /// <summary>
        /// 文件最小有效大小(字节)
        /// </summary>
        public long MinFileSize { get; set; } = 1000;

        #endregion PaddleOCR配置

        /// <summary>
        /// 检查模型文件是否存在
        /// </summary>
        /// <param name="modelType">模型类型</param>
        /// <returns>是否存在</returns>
        public bool CheckModelFilesExist(string modelType)
        {
            if (!Directory.Exists(RuntimesPath))
            {
                XLogger.Error($"OCR模型目录不存在: {Path.GetFullPath(RuntimesPath)}");
                return false;
            }

            switch (modelType.ToLower())
            {
                case "ocrlite":
                    return CheckOcrLiteModels();

                case "tesseract":
                    return CheckTesseractData();

                case "paddle":
                    return CheckPaddleOcrFile();

                default:
                    return false;
            }
        }

        /// <summary>
        /// 检查OcrLite模型文件是否存在
        /// </summary>
        private bool CheckOcrLiteModels()
        {
            bool filesExist = true;
            Dictionary<string, string> modelFiles = new Dictionary<string, string>
            {
                { "检测模型", DetModelPath },
                { "分类模型", ClsModelPath },
                { "识别模型", RecModelPath },
                { "字典文件", KeysPath }
            };

            foreach (var file in modelFiles)
            {
                if (!File.Exists(file.Value))
                {
                    XLogger.Error($"OCR{file.Key}文件不存在: {Path.GetFullPath(file.Value)}");
                    filesExist = false;
                }
            }

            return filesExist;
        }

        /// <summary>
        /// 检查Tesseract数据是否存在
        /// </summary>
        private bool CheckTesseractData()
        {
            if (!Directory.Exists(RuntimesPath))
            {
                XLogger.Error($"Tesseract数据目录不存在: {Path.GetFullPath(RuntimesPath)}");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 检查PaddleOCR文件是否存在
        /// </summary>
        private bool CheckPaddleOcrFile()
        {
            string exePath = PaddleExePath;

            // 检查文件是否存在
            if (!File.Exists(exePath))
            {
                XLogger.Warn($"未找到Pro版本OCR文件: {exePath}");
                return false;
            }

            // 检查路径是否包含空格
            if (exePath.Contains(' '))
            {
                XLogger.Warn($"{PaddleExeName}的路径中包含空格，这可能导致程序无法正常工作!");
                XLogger.Warn("建议将程序移动到不包含空格的路径下，例如：C:\\OCR\\");
                return false;
            }

            // 检查文件大小
            try
            {
                FileInfo fileInfo = new FileInfo(exePath);
                //XLogger.Debug($"Pro版本OCR文件大小: {fileInfo.Length} 字节");
                if (fileInfo.Length < MinFileSize)
                {
                    XLogger.Warn($"{PaddleExeName}文件异常，文件大小过小!");
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                XLogger.Error($"检查Pro版本OCR文件信息时发生异常: {ex.Message}");
                return false;
            }
        }
    }
}