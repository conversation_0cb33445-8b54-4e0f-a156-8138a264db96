﻿using DanDing1.Views.Pages;
using DanDing1.Views.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Wpf.Ui;
using XHelper;
using XHelper.Models;
using Newtonsoft.Json;

namespace DanDing1.Services
{
    /// <summary>
    /// Managed host of the application.
    /// </summary>
    public class ApplicationHostService : IHostedService
    {
        private readonly IServiceProvider _serviceProvider;
        private INavigationWindow _navigationWindow;
        private WebSocketLogForwarderService _logForwarderService;
        private LogStorageService _logStorageService;
        private LogSyncService _logSyncService;
        private ScriptSyncService _scriptSyncService;

        public ApplicationHostService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            // 使用依赖注入获取LogStorageService，而不是创建新实例
            _logStorageService = serviceProvider.GetRequiredService<ILogStorage>() as LogStorageService;
            _logForwarderService = new WebSocketLogForwarderService();
            _logSyncService = new LogSyncService(_logStorageService);

            // 使用依赖注入获取ScriptStatusReporterService和ScriptSyncService
            var statusReporter = serviceProvider.GetService<ScriptStatusReporterService>();
            if (statusReporter != null)
            {
                _scriptSyncService = new ScriptSyncService(statusReporter);
            }
        }

        /// <summary>
        /// Triggered when the application host is ready to start the service.
        /// </summary>
        /// <param name="cancellationToken">Indicates that the start process has been aborted.</param>
        public async Task StartAsync(CancellationToken cancellationToken)
        {
            // 确保LogSyncService已初始化，它会注册WebSocket消息处理器
            // 在构造函数中已经创建了LogSyncService实例，这里只是确保它被引用
            // XLogger.Debug("LogSyncService已初始化，WebSocket消息处理器已注册");

            // 确保脚本状态同步服务已初始化
            if (_scriptSyncService != null)
            {
                // 确保消息处理器已注册
                _scriptSyncService.RegisterMessageHandlers();
                XLogger.Debug("ScriptSyncService已初始化，WebSocket消息处理器已注册");
            }

            // 设置日志转发器
            XLogger.LogForwarder = new CombinedLogForwarder(_logForwarderService, _logStorageService);
            //// 启动日志转发服务
            //_logForwarderService.Start();
            //// 直接注册WebSocket消息处理器，确保能处理GET_LOGS_COMMAND消息
            //RegisterWebSocketHandlers();

            if (GlobalData.Instance.appConfig.EnableWebSocketCommunication)
            {
                // 如果是试用用户，禁用WebSocket功能
                if (GlobalData.Instance.appConfig.IsFree)
                {
                    XWebsocket.IsDisabled = true;
                    XLogger.Debug("试用用户不支持WebSocket通信，已禁用WebSocket功能");
                    await XWebsocket.DisconnectAsync();
                }
                else
                {
                    _logForwarderService.Start();
                    RegisterWebSocketHandlers();
                }
            }
            else
            {
                await XWebsocket.DisconnectAsync();
            }

            await HandleActivationAsync();
        }

        /// <summary>
        /// Triggered when the application host is performing a graceful shutdown.
        /// </summary>
        /// <param name="cancellationToken">Indicates that the shutdown process should no longer be graceful.</param>
        public async Task StopAsync(CancellationToken cancellationToken)
        {
            if (_logForwarderService != null)
            {
                await _logForwarderService.StopAsync();
                _logForwarderService.Dispose();
            }

            // 取消注册WebSocket消息处理器
            XWebsocket.UnregisterMessageHandler("GET_LOGS_COMMAND");
            XWebsocket.UnregisterMessageHandler("TRIGGER_SCRIPT_STATUS_SYNC_COMMAND");

            // 断开WebSocket连接
            await XWebsocket.DisconnectAsync();

            await Task.CompletedTask;
        }

        /// <summary>
        /// 创建主窗口并进行初始化
        /// </summary>
        private async Task HandleActivationAsync()
        {
            await Task.CompletedTask;

            if (!Application.Current.Windows.OfType<MainWindow>().Any())
            {
                _navigationWindow = (_serviceProvider.GetService(typeof(INavigationWindow)) as INavigationWindow)!;
                _navigationWindow!.ShowWindow();

                _navigationWindow.Navigate(typeof(LoadPage));
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 注册WebSocket消息处理器
        /// </summary>
        private void RegisterWebSocketHandlers()
        {
            // 先取消注册，避免重复
            XWebsocket.UnregisterMessageHandler("GET_LOGS_COMMAND");

            // 注册日志同步消息处理器
            XWebsocket.RegisterMessageHandler("GET_LOGS_COMMAND", HandleGetLogsCommand);

            XLogger.OnlyWrite("ApplicationHostService: 已直接注册GET_LOGS_COMMAND消息处理器");
        }

        /// <summary>
        /// 处理GET_LOGS_COMMAND消息
        /// </summary>
        /// <param name="message">WebSocket消息内容</param>
        private void HandleGetLogsCommand(string message)
        {
            try
            {
                XLogger.OnlyWrite($"ApplicationHostService: 收到日志同步请求: {message}");

                // 解析请求消息
                var request = JsonConvert.DeserializeObject<GetLogsCommand>(message);
                if (request == null)
                {
                    XLogger.Error("ApplicationHostService: 无法解析日志同步请求");
                    return;
                }

                // 获取最新的日志条目
                var logEntries = _logStorageService.GetRecentLogs(100); // 最多返回100条日志

                // 构造响应消息
                var response = new LogEntriesResponse
                {
                    type = "LOG_ENTRIES_RESPONSE",
                    requestId = request.requestId,
                    timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    payload = new LogEntriesPayload
                    {
                        entries = logEntries
                    }
                };

                // 异步发送响应
                _ = SendLogEntriesResponseAsync(response);
            }
            catch (Exception ex)
            {
                XLogger.Error($"ApplicationHostService: 处理日志同步请求时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送日志条目响应
        /// </summary>
        /// <param name="response">响应对象</param>
        private async Task SendLogEntriesResponseAsync(LogEntriesResponse response)
        {
            try
            {
                bool success = await XWebsocket.SendObjectAsync(response);
                if (success)
                {
                    XLogger.OnlyWrite($"ApplicationHostService: 已成功发送日志同步响应: requestId={response.requestId}, 条目数={response.payload.entries.Count}");
                }
                else
                {
                    XLogger.Error($"ApplicationHostService: 发送日志同步响应失败: requestId={response.requestId}");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"ApplicationHostService: 发送日志同步响应时出错: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 组合日志转发器，将日志同时发送到多个目标
    /// </summary>
    public class CombinedLogForwarder : ILogForwarder
    {
        private readonly ILogForwarder[] _forwarders;

        public CombinedLogForwarder(params ILogForwarder[] forwarders)
        {
            _forwarders = forwarders ?? Array.Empty<ILogForwarder>();
        }

        public void EnqueueLog(DateTime timestamp, string level, string message, string? taskId = null)
        {
            foreach (var forwarder in _forwarders)
            {
                forwarder.EnqueueLog(timestamp, level, message, taskId);
            }
        }
    }
}