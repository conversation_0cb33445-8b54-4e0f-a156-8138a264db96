﻿using ScriptEngine.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Tasks.QiLing
{
    /// <summary>
    /// 契灵任务配置类
    /// </summary>
    internal class QiLingTask_Config : BaseTaskConfig
    {
        /// <summary>
        /// 构造函数,初始化配置
        /// </summary>
        /// <param name="configs">任务配置参数</param>
        public QiLingTask_Config(TaskConfigsModel.Configs configs)
        {
            // 获取任务类型,默认为"镇墓兽"
            TaskType = configs.Others.TryGetValue("TaskType", out string? value) ? value : "镇墓兽";
            // 获取是否自动购买召唤次数
            AutoBuySummon = configs.Others.TryGetValue("AutoBuySummon", out string? autoBuy) && bool.Parse(autoBuy);
            // 初始化胜利次数为0
            VictoryCount = 0;
            // 获取最大胜利次数
            Ncount = MaxVictoryCount = configs.Count;
            // 是否标记
            BiaoJi = bool.Parse(configs.Others.TryGetValue("Biaoji", out string? value1) ? value1 : "False");
            // 初始化剩余召唤次数为0
            RemainingSummonCount = 0;
            // 初始化捕捉成功次数为0
            CaptureSuccessCount = 0;
        }

        /// <summary>
        /// 是否自动购买召唤次数
        /// </summary>
        public bool AutoBuySummon { get; set; }

        /// <summary>
        /// 捕捉成功次数
        /// </summary>
        public int CaptureSuccessCount { get; set; }

        /// <summary>
        /// 最大胜利次数
        /// </summary>
        public int MaxVictoryCount { get; set; }

        /// <summary>
        /// 剩余召唤次数
        /// </summary>
        public int RemainingSummonCount { get; set; }

        /// <summary>
        /// 任务类型:"镇墓兽"或"探查"
        /// </summary>
        public string TaskType { get; set; }

        /// <summary>
        /// 当前胜利次数
        /// </summary>
        public int VictoryCount { get; set; }
    }
}