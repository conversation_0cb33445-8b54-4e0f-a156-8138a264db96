﻿using ScriptEngine.Model;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Xml.Linq;

namespace DanDing1.ViewModels.Windows
{
    public class KeyValueItem
    {
        public KeyValueItem(string k, string v)
        {
            Key = k;
            Value = v;
        }

        private string _value;

        /// <summary>
        /// 值
        /// </summary>
        public string Value
        {
            get { return _value; }
            set { _value = value; }
        }

        private string key;

        private Dictionary<string, string> CN_dict = new()
        {
            {"Level","层数" },
            {"Biaoji","标记" },
            {"Location","组队位置" },
            {"Team","邀请队友区域" },
            {"Name","队友名字" },
            {"CombatStr","指定Buff" },
            {"Counting_Mode", "结算模式" },
            {"Tui4", "突破卡级" },
            {"EndStartLtu", "结束打寮突" },
            {"LtuCount", "寮突次数" },
            {"Class", "类别" },
            {"ManTing", "积分满结束" },
            {"Mode", "模式" },
            {"XingZhiDao_Damo", "打达摩"},
            {"ShuangBeiJiaCheng", "用双倍加成"},
            {"ShuangBeiJiaCheng_Buy", "购买加成"},
            {"Extra_Skill", "额外技能"},
            {"FirstEnd", "提前结束"},
            {"AutoBuff", "自动加成"},
            {"MingShi", "名士"},
            {"AutoBuySummon", "自动购买召唤"},
            {"TaskType", "任务类型"},
            {"HunDunBuKaiXiang", "混沌不开箱"},
            {"金币", "金币加成"},
            {"经验", "经验加成"},
            {"觉醒", "觉醒加成"},
            {"御魂", "御魂加成"},
            {"所有", "所有加成"}
        };

        /// <summary>
        /// 键
        /// </summary>
        public string Key
        {
            get { return key; }
            set
            {
                CN_dict.TryGetValue(value, out string? k);
                if (k != null) k += "\r\n";
                key = k + value;
            }
        }
    }

    /// <summary>
    /// 任务详情
    /// </summary>
    public partial class TaskConfigWindowViewModel : ObservableObject
    {
        [ObservableProperty]
        private string _applicationTitle;

        public TaskConfigWindowViewModel()
        {
            ApplicationTitle = "任务列表详情";
        }

        [ObservableProperty]
        public ObservableCollection<TaskConfigsModel.Configs> _tasks;

        [ObservableProperty]
        public TaskConfigsModel.Configs _selectedTask;

        [ObservableProperty]
        public ObservableCollection<KeyValueItem> _selectedTaskOthers;

        [ObservableProperty]
        public ObservableCollection<KeyValueItem> _selectedTaskOthers_Obj;
    }
}