﻿#pragma checksum "..\..\..\..\..\..\Views\Windows\HistoryRecordWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "938014D3D0B647679EE22B7033B8BD93BCE6E3A6"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Views.Windows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Windows {
    
    
    /// <summary>
    /// HistoryRecordWindow
    /// </summary>
    public partial class HistoryRecordWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 28 "..\..\..\..\..\..\Views\Windows\HistoryRecordWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox LogListBox;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\..\..\Views\Windows\HistoryRecordWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportAllImagesButton;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\..\Views\Windows\HistoryRecordWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportCurrentImageButton;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\..\..\Views\Windows\HistoryRecordWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image ScreenshotImage;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\..\Views\Windows\HistoryRecordWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LogTextBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/windows/historyrecordwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\Views\Windows\HistoryRecordWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.LogListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 28 "..\..\..\..\..\..\Views\Windows\HistoryRecordWindow.xaml"
            this.LogListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.LogListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ExportAllImagesButton = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\..\..\Views\Windows\HistoryRecordWindow.xaml"
            this.ExportAllImagesButton.Click += new System.Windows.RoutedEventHandler(this.ExportAllImagesButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ExportCurrentImageButton = ((System.Windows.Controls.Button)(target));
            
            #line 62 "..\..\..\..\..\..\Views\Windows\HistoryRecordWindow.xaml"
            this.ExportCurrentImageButton.Click += new System.Windows.RoutedEventHandler(this.ExportCurrentImageButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ScreenshotImage = ((System.Windows.Controls.Image)(target));
            return;
            case 5:
            this.LogTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

