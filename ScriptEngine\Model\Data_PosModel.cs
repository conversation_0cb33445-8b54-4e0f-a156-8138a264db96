﻿using ScriptEngine.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Model
{
    /// <summary>
    /// 范围数据信息
    /// 用于序列化存储
    /// </summary>
    public class Data_Pos : IData
    {
        public string Name { get; set; }
        public string Class { get; set; }
        /// <summary>
        /// 查找范围-字符串
        /// x,y,x1,y1
        /// </summary>
        public string FindPos { get; set; }
        /// <summary>
        /// 点击范围-字符串
        /// x,y,x1,y1
        /// </summary>
        public string ClickPos { get; set; }
        public Data_Pos() { }
        public Data_Pos(string name, string _class, string findPos, string clickPos)
        {
            Name = name;
            Class = _class;
            FindPos = findPos;
            ClickPos = clickPos;
        }
    }

    public class Data_Poss
    {
        public int Count => DataPoss.Count;
        public List<Data_Pos> DataPoss { get; set; } = new();
        public Data_Poss() { }
        public Data_Poss Add(Data_Pos pos) { DataPoss.Add(pos); return this; }
        public Data_Poss Add(List<Data_Pos> poss) { DataPoss.AddRange(poss); return this; }
    }
}
