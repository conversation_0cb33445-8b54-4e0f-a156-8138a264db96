﻿#pragma checksum "..\..\..\..\..\..\Views\Windows\SchedulerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "40BC502A0FFC3802499B52F0E0253C518311CB6E"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.ViewModels.Windows;
using DanDing1.Views.UserControls;
using DanDing1.Views.Windows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Windows {
    
    
    /// <summary>
    /// SchedulerWindow
    /// </summary>
    public partial class SchedulerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 11 "..\..\..\..\..\..\Views\Windows\SchedulerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DanDing1.Views.Windows.SchedulerWindow SchedulerWindowInstance;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\..\..\Views\Windows\SchedulerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainGrid;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\..\..\Views\Windows\SchedulerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.DataGrid TasksDataGrid;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\..\..\Views\Windows\SchedulerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.DataGrid EmulatorsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 391 "..\..\..\..\..\..\Views\Windows\SchedulerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.TextBox MainLogTextBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/windows/schedulerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\Views\Windows\SchedulerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SchedulerWindowInstance = ((DanDing1.Views.Windows.SchedulerWindow)(target));
            
            #line 22 "..\..\..\..\..\..\Views\Windows\SchedulerWindow.xaml"
            this.SchedulerWindowInstance.Closing += new System.ComponentModel.CancelEventHandler(this.Window_Closing);
            
            #line default
            #line hidden
            
            #line 23 "..\..\..\..\..\..\Views\Windows\SchedulerWindow.xaml"
            this.SchedulerWindowInstance.Loaded += new System.Windows.RoutedEventHandler(this.Window_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MainGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.TasksDataGrid = ((Wpf.Ui.Controls.DataGrid)(target));
            
            #line 165 "..\..\..\..\..\..\Views\Windows\SchedulerWindow.xaml"
            this.TasksDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TasksDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 169 "..\..\..\..\..\..\Views\Windows\SchedulerWindow.xaml"
            this.TasksDataGrid.PreviewMouseRightButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TasksDataGrid_PreviewMouseRightButtonDown);
            
            #line default
            #line hidden
            return;
            case 4:
            this.EmulatorsDataGrid = ((Wpf.Ui.Controls.DataGrid)(target));
            
            #line 303 "..\..\..\..\..\..\Views\Windows\SchedulerWindow.xaml"
            this.EmulatorsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.EmulatorsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.MainLogTextBox = ((Wpf.Ui.Controls.TextBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

