﻿using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Collections.ObjectModel;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Model
{
    /// <summary>
    /// 用户任务清单
    /// </summary>
    public class TaskConfigsModel
    {
        /// <summary>
        /// 任务列表名
        /// </summary>
        public Dictionary<string, Configs> TaskLists = new();

        /// <summary>
        /// 用于维护任务顺序的列表
        /// </summary>
        private readonly List<string> _taskOrder = new();

        /// <summary>
        /// 用于同步的对象锁
        /// </summary>
        private readonly object _lockObject = new();

        #region

        /// <summary>
        /// 任务参数
        /// </summary>
        public class Configs
        {
            /// <summary>
            /// 任务名
            /// </summary>
            public string Name { get; set; }

            /// <summary>
            /// 次数
            /// </summary>
            public int Count { get; set; }

            /// <summary>
            /// xaml展示用
            /// </summary>
            public string ShowName { get; set; }

            public Dictionary<string, string> Others { get; set; } = new();

            /// <summary>
            /// 其它参数-对象
            /// </summary>
            public Dictionary<string, object> Others_Obj { get; set; } = new();
        }

        #endregion

        /// <summary>
        /// 快速添加任务&次数
        /// </summary>
        /// <param name="taskName"></param>
        /// <param name="Count"></param>
        /// <returns></returns>
        public TaskConfigsModel Add(string taskName, int Count)
        {
            lock (_lockObject)
            {
                if (taskName == "Buff")
                    throw new Exception("Buff任务不可以使用快速添加任务方法！");
                else
                {
                    string key = taskName + ' ' + Count + '次';
                    if (TaskLists.TryAdd(key, new() { Name = taskName, Count = Count }))
                    {
                        _taskOrder.Add(key);
                    }
                }
                return this;
            }
        }

        /// <summary>
        /// 快速添加任务&其它配置
        /// </summary>
        /// <param name="taskName"></param>
        /// <param name="Count"></param>
        /// <returns></returns>
        public TaskConfigsModel Add(string taskName, string k, string v)
        {
            GetConfigs(taskName).Others.Add(k, v);
            return this;
        }

        /// <summary>
        /// 添加任务配置
        /// </summary>
        /// <param name="taskName"></param>
        /// <param name="Config"></param>
        /// <returns></returns>
        public TaskConfigsModel AddConfig(string taskName, Configs Config)
        {
            lock (_lockObject)
            {
                if (taskName == "Buff")
                {
                    string OnStr = "";
                    string kaiguan = "关";
                    foreach (var item in Config.Others)
                    {
                        if (item.Value == "0")
                            kaiguan = "关";
                        else
                            kaiguan = "开";
                        OnStr = OnStr + item.Key + kaiguan + "|";
                    }

                    string taskKey;
                    if (kaiguan == "关")
                    {
                        //检查是否有多个关所有
                        int suoyou_OCunt = 1;
                        foreach (var item in TaskLists)
                        {
                            if (item.Key.Contains("所有关"))
                                suoyou_OCunt++;
                        }
                        taskKey = taskName + ' ' + OnStr[0..^1] + suoyou_OCunt;
                    }
                    else
                    {
                        taskKey = taskName + ' ' + OnStr[0..^1];
                    }

                    // 检查键是否已存在，如果存在则添加唯一标识
                    string uniqueKey = taskKey;
                    int counter = 1;
                    while (TaskLists.ContainsKey(uniqueKey))
                    {
                        uniqueKey = $"{taskKey} ({counter})";
                        counter++;
                    }

                    if (TaskLists.TryAdd(uniqueKey, Config))
                    {
                        _taskOrder.Add(uniqueKey);
                    }
                }
                else
                {
                    string CountNumber = "";
                    int i = 0;
                    string key = taskName + ' ' + Config.Count + '次' + CountNumber;
                    while (!TaskLists.TryAdd(key, Config))
                    {
                        i++;
                        CountNumber = " (" + i.ToString() + ")";
                        key = taskName + ' ' + Config.Count + '次' + CountNumber;
                    }
                    _taskOrder.Add(key);
                }
                return this;
            }
        }

        /// <summary>
        /// 获取任务类
        /// </summary>
        /// <param name="configs">任务配置集合</param>
        /// <returns>按原始顺序初始化的任务配置模型</returns>
        public static TaskConfigsModel GetTaskConfigs(ObservableCollection<Configs> configs)
        {
            // 创建新的任务配置模型
            var taskConfigs = new TaskConfigsModel();

            // 使用List临时存储所有配置，以保持顺序
            var orderedConfigs = configs.ToList();

            // 按顺序添加每个配置
            foreach (var config in orderedConfigs)
            {
                taskConfigs.AddConfig(config.Name, config);
            }

            return taskConfigs;
        }

        /// <summary>
        /// 遍历任务
        /// </summary>
        /// <param name="callBack"></param>
        public void ForEach(Func<string, Configs, int> callBack)
        {
            lock (_lockObject)
            {
                // 按照添加顺序遍历任务
                foreach (var key in _taskOrder.ToList())
                {
                    if (TaskLists.TryGetValue(key, out var config))
                    {
                        callBack.Invoke(key, config);
                    }
                }
            }
        }

        public Configs GetConfigs(string name)
        {
            lock (_lockObject)
            {
                foreach (var key in _taskOrder)
                {
                    if (TaskLists.TryGetValue(key, out var config) && config.Name == name)
                    {
                        return config;
                    }
                }
                throw new Exception($"当前任务名[{name}]，没有任何参数...no have configs!");
            }
        }

        /// <summary>
        /// 删除指定名称的任务
        /// </summary>
        /// <param name="taskName">要删除的任务名称</param>
        /// <returns>当前对象，支持链式调用</returns>
        public TaskConfigsModel Delete(string taskName)
        {
            lock (_lockObject)
            {
                // 找出所有匹配该任务名称的键
                var keysToRemove = _taskOrder
                    .Where(key => TaskLists.TryGetValue(key, out var config) && config.Name == taskName)
                    .ToList();

                // 从字典和顺序列表中删除这些任务
                foreach (var key in keysToRemove)
                {
                    TaskLists.Remove(key, out _);
                    _taskOrder.Remove(key);
                }

                return this;
            }
        }
    }
}