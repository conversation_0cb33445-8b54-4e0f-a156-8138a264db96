using System;
using System.Collections.Generic;
using System.IO;
using Tesseract;
using XHelper.Models;

namespace XHelper.OCR
{
    /// <summary>
    /// Tesseract引擎实现类，专用于数字识别
    /// </summary>
    internal class TesseractOcrEngine : IOcrEngine
    {
        private readonly OcrConfiguration _config;
        private readonly IImageProcessor _imageProcessor;
        private bool _initialized = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">OCR配置</param>
        /// <param name="imageProcessor">图像处理器</param>
        public TesseractOcrEngine(OcrConfiguration config, IImageProcessor imageProcessor)
        {
            _config = config;
            _imageProcessor = imageProcessor;
        }

        /// <summary>
        /// 初始化OCR引擎
        /// </summary>
        /// <returns>是否初始化成功</returns>
        public bool Initialize()
        {
            // 已初始化则直接返回
            if (_initialized)
                return true;

            try
            {
                // 检查Tesseract数据是否存在
                if (!_config.CheckModelFilesExist("tesseract"))
                {
                    return false;
                }

                _initialized = true;
                return true;
            }
            catch (Exception ex)
            {
                XLogger.Error($"Tesseract引擎初始化异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从图像路径识别文本
        /// </summary>
        /// <param name="imagePath">图像路径</param>
        /// <param name="callback">可选的回调函数，用于接收文本块信息</param>
        /// <returns>识别结果文本</returns>
        public string RecognizeText(string imagePath, Action<List<XOcr_TextBlock>>? callback = null)
        {
            if (string.IsNullOrEmpty(imagePath) || !File.Exists(imagePath))
            {
                XLogger.Warn($"图像文件不存在: {imagePath}");
                return string.Empty;
            }

            try
            {
                List<XOcr_TextBlock> blocks = new();

                // 优化图像
                byte[] optimizedImageBytes = _imageProcessor.OptimizeImage(imagePath);
                if (optimizedImageBytes == null || optimizedImageBytes.Length == 0)
                {
                    XLogger.Error("图像优化失败");
                    return string.Empty;
                }

                return ProcessImage(optimizedImageBytes, blocks, callback);
            }
            catch (TesseractException tex)
            {
                XLogger.Error($"Tesseract引擎异常: {tex.Message}");
                return string.Empty;
            }
            catch (Exception ex)
            {
                XLogger.Error($"数字识别异常: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 从图像字节数组识别文本
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <param name="callback">可选的回调函数，用于接收文本块信息</param>
        /// <returns>识别结果文本</returns>
        public string RecognizeText(byte[] imageData, Action<List<XOcr_TextBlock>>? callback = null)
        {
            if (imageData == null || imageData.Length == 0)
            {
                XLogger.Warn("输入图像数据为空");
                return string.Empty;
            }

            try
            {
                List<XOcr_TextBlock> blocks = new();

                // 优化图像
                byte[] optimizedImageBytes = _imageProcessor.OptimizeImage(imageData);
                if (optimizedImageBytes == null || optimizedImageBytes.Length == 0)
                {
                    XLogger.Error("图像优化失败");
                    return string.Empty;
                }

                return ProcessImage(optimizedImageBytes, blocks, callback);
            }
            catch (TesseractException tex)
            {
                XLogger.Error($"Tesseract引擎异常: {tex.Message}");
                return string.Empty;
            }
            catch (Exception ex)
            {
                XLogger.Error($"数字识别异常: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 使用Tesseract处理图像数据
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <param name="blocks">文本块列表</param>
        /// <param name="callback">回调函数</param>
        /// <returns>识别结果文本</returns>
        private string ProcessImage(byte[] imageData, List<XOcr_TextBlock> blocks, Action<List<XOcr_TextBlock>>? callback)
        {
            // 初始化Tesseract引擎
            if (!Initialize())
            {
                return string.Empty;
            }

            using var engine = new Tesseract.TesseractEngine(_config.RuntimesPath, _config.TesseractLanguage, EngineMode.Default);

            // 设置字符白名单，限制识别范围为数字和斜杠，提高识别准确性
            //engine.SetVariable("tessedit_char_whitelist", _config.CharWhitelist);

            // 设置页面分割模式为单行文本，适合数字识别
            //engine.SetVariable("tessedit_pageseg_mode", _config.PageSegMode); // PSM_SINGLE_LINE

            using var img = Pix.LoadFromMemory(imageData);
            if (img == null)
            {
                XLogger.Error("无法加载图像数据");
                return string.Empty;
            }

            using var page = engine.Process(img);
            var text = page.GetText();

            if (!string.IsNullOrEmpty(text?.Trim()) && callback != null) // 处理文本相应的位置
            {
                try
                {
                    var list = page.GetSegmentedRegions(PageIteratorLevel.TextLine);
                    if (list != null && list.Count > 0)
                    {
                        var lines = text.Split('\n');
                        int i = 0;

                        foreach (var line in lines)
                        {
                            if (string.IsNullOrWhiteSpace(line)) continue;
                            if (i < list.Count)
                            {
                                blocks.Add(new(line, new(list[i].X, list[i].Y, list[i].Width, list[i].Height)));
                                i++;
                            }
                        }
                    }
                    callback(blocks);
                }
                catch (Exception ex)
                {
                    XLogger.Error($"处理文本区域时异常: {ex.Message}");
                }
            }

            XLogger.Debug($"Tesseract识别结果：{text?.Trim() ?? string.Empty}");
            return text ?? string.Empty;
        }
    }
}