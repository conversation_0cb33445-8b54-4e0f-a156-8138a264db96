﻿using DanDing1.Helpers;
using DanDing1.Services;
using DanDing1.ViewModels.Windows;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using Wpf.Ui;
using Wpf.Ui.Abstractions;
using Wpf.Ui.Appearance;
using Wpf.Ui.Controls;
using Wpf.Ui.Extensions;
using XHelper;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// 主窗口类
    /// </summary>
    public partial class MainWindow : FluentWindow, INavigationWindow
    {
        private readonly IContentDialogService contentDialogService;

        /// <summary>
        /// 安全时钟
        /// </summary>
        private DispatcherTimer clock;

        private NavigationView NowView;
        private ScriptStatusReporterService _statusReporter;

        public MainWindow(
            MainWindowViewModel viewModel,
            INavigationViewPageProvider pageService,
            INavigationService navigationService,
            IContentDialogService contentDialogService,
            IServiceProvider serviceProvider
        )
        {
            InitializeComponent();

            //new Tests.Test_AppStart().Test();
            ViewModel = viewModel;
            this.contentDialogService = contentDialogService;
            ViewModel.GameCountChanged = (count) =>
            {
                if (count == 4)
                    AddGamesPanel.Visibility = Visibility.Hidden;
                var t = RootNavigation.MenuItems[count] as NavigationViewItem;
                if (t is null)
                    return;

                Navigate(t.TargetPageType);
            };//当游戏数量上限时 ，将按钮隐藏

            DataContext = this;

            SystemThemeWatcher.Watch(this);

            SetPageService(pageService);
            navigationService.SetNavigationControl(RootNavigation);
            contentDialogService.SetDialogHost(RootContentDialog);
            //获取判断启动参数
            // 获取启动参数
            string[] args = Environment.GetCommandLineArgs();

            CheckForUpdateZip(args);

            // 解析启动参数
            string? picsVer = GetArgument(args, "-PicsVer");

            if (!string.IsNullOrEmpty(picsVer))
            {
                XLogger.Debug($"图库版本参数被默认为: {picsVer}");
                GlobalData.Instance.PicServerVer = picsVer;
            }
            this.Loaded += MainWindow_Loaded;

            //初始化ws状态上报
            // 获取服务
            var statusReporter = serviceProvider.GetService<ScriptStatusReporterService>();

            // 获取超级多开视图模型
            var superMultiVM = serviceProvider.GetService<SuperMultiGamesWindowViewModel>();

            // 初始化和启动服务
            statusReporter.Initialize(superMultiVM, pollingIntervalMs: 2500).Start();

            // 存储以便在窗口关闭时停止服务
            _statusReporter = statusReporter;
        }

        public MainWindowViewModel ViewModel { get; }

        public void CloseWindow() => Close();

        INavigationView INavigationWindow.GetNavigation()
        {
            return GetNavigation();
        }

        public void SetServiceProvider(IServiceProvider serviceProvider)
        {
            // 实现接口方法
        }

        /// <summary>
        /// Raises the closed event.
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            base.OnClosed(e);
            // 停止服务
            _statusReporter?.Stop();
            // Make sure that closing this window will begin the process of closing the application.
            Application.Current.Shutdown();
        }

        private void CheckForUpdateZip(string[] args)
        {
            string zipPattern = @"^DanDing\d+\.\d+\.\d+([CT]\d+)?\.zip$";
            foreach (var arg in args)
            {
                if (Regex.IsMatch(Path.GetFileName(arg), zipPattern))
                {
                    string version = Path.GetFileNameWithoutExtension(arg);
                    if (System.Windows.MessageBox.Show($"程序在启动时接收到版本{version}的压缩文件，是否执行覆盖安装？安装后程序将自动启动！", "确认", System.Windows.MessageBoxButton.YesNo) == System.Windows.MessageBoxResult.Yes)
                    // 需要安装，退出程序并执行安装
                    {
                        GlobalData.Instance.Global_Data.TryAdd("自动更新", true);
                        XLogger.Info("用户选择本地覆盖更新！");
                        InstallAndRestart(arg);
                    }
                }
            }
        }

        private void Clock_Tick(object? sender, EventArgs e)
        {
            string[] targetProcesses = { "ollydbg", "x64dbg", "windbg", "fiddler" };
            foreach (string processName in targetProcesses)
            {
                if (Utils.IsProcessRunning(processName))
                {
                    Console.WriteLine($"警告：检测到可疑进程运行中：{processName}，设置窗口不可截屏。");
                    Utils.ApplyDisplayAffinity(GlobalData.Instance.MainWindowsHwnd);
                    clock.Stop();
                }
            }
        }

        private async void FluentWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            bool g1st = GlobalData.Instance?.Game1RunningConfig?.isRunning ?? false;
            bool g2st = GlobalData.Instance?.Game2RunningConfig?.isRunning ?? false;
            bool g3st = GlobalData.Instance?.Game3RunningConfig?.isRunning ?? false;
            bool g4st = GlobalData.Instance?.Game4RunningConfig?.isRunning ?? false;
            if (!g1st && !g2st && !g3st && !g4st)
                Application.Current.Shutdown();

            // 取消默认的关闭行为，确保不会直接关闭窗口
            e.Cancel = true;

            string str = "正在执行的客户端：";
            if (g1st) str += "游戏1 ";
            if (g2st) str += "游戏2 ";
            if (g3st) str += "游戏3 ";
            if (g4st) str += "游戏4 ";

            // 显示异步对话框
            ContentDialogResult result = await contentDialogService.ShowSimpleDialogAsync(
                new SimpleContentDialogCreateOptions()
                {
                    Title = "结束了？",
                    Content = "确认继续退出吗？请注意您当前正在运行脚本哦！\r\n" + str,
                    CloseButtonText = "返回",
                    PrimaryButtonText = "退出"
                }
            );

            // 如果用户选择"返回"或取消（None），取消关闭操作
            if (result is ContentDialogResult.None)
            {
                return;  // 返回，不继续关闭
            }

            // 如果用户确认退出，手动关闭窗口
            Application.Current.Shutdown();
        }

        /// <summary>
        /// 从启动参数中获取特定参数的值
        /// </summary>
        /// <param name="args"></param>
        /// <param name="argumentName"></param>
        /// <returns></returns>
        private string? GetArgument(string[] args, string argumentName)
        {
            for (int i = 0; i < args.Length; i++)
            {
                if (args[i].Equals(argumentName, StringComparison.OrdinalIgnoreCase) && i + 1 < args.Length)
                {
                    return args[i + 1];
                }
            }
            return null;
        }

        private void InstallAndRestart(string updateZipPath)
        {
            try
            {
                // 确保路径存在且有效
                if (!File.Exists(updateZipPath))
                {
                    XLogger.Error($"更新文件不存在: {updateZipPath}");
                    System.Windows.MessageBox.Show($"更新文件不存在: {updateZipPath}", "更新失败", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    return;
                }

                // 记录更新文件路径
                XLogger.Info($"更新文件路径: {updateZipPath}");

                // 使用简单直接的方法，不依赖批处理脚本
                string extractPath = Path.Combine(Path.GetTempPath(), "DanDingUpdate");
                string appPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DanDing1.exe");
                string logFilePath = Path.Combine(Path.GetTempPath(), "DanDingUpdate.log");

                // 创建日志文件
                using (StreamWriter logWriter = new StreamWriter(logFilePath, false, System.Text.Encoding.UTF8))
                {
                    logWriter.WriteLine($"开始更新过程 - {DateTime.Now}");
                    logWriter.WriteLine($"更新文件路径: {updateZipPath}");
                    logWriter.WriteLine($"文件大小: {new FileInfo(updateZipPath).Length} 字节");
                    logWriter.WriteLine($"应用程序路径: {appPath}");
                    logWriter.WriteLine($"解压路径: {extractPath}");

                    try
                    {
                        // 清理并创建解压目录
                        if (Directory.Exists(extractPath))
                        {
                            logWriter.WriteLine("清理旧的解压目录...");
                            Directory.Delete(extractPath, true);
                        }
                        Directory.CreateDirectory(extractPath);
                        logWriter.WriteLine("创建解压目录成功");

                        // 直接解压文件
                        logWriter.WriteLine("开始解压文件...");
                        System.IO.Compression.ZipFile.ExtractToDirectory(updateZipPath, extractPath);
                        logWriter.WriteLine("解压文件成功");

                        // 检查是否有嵌套的ZIP文件
                        string nestedZipPath = Path.Combine(extractPath, "danding1.2.1.zip");
                        if (File.Exists(nestedZipPath))
                        {
                            logWriter.WriteLine("检测到嵌套的ZIP文件，进行二次解压...");
                            string tempExtractPath = Path.Combine(extractPath, "temp");
                            Directory.CreateDirectory(tempExtractPath);

                            System.IO.Compression.ZipFile.ExtractToDirectory(nestedZipPath, tempExtractPath);
                            logWriter.WriteLine("二次解压成功");

                            // 将二次解压的文件复制到主解压目录
                            logWriter.WriteLine("复制二次解压的文件到主目录...");
                            foreach (string dirPath in Directory.GetDirectories(tempExtractPath, "*", SearchOption.AllDirectories))
                            {
                                string newDirPath = dirPath.Replace(tempExtractPath, extractPath);
                                if (!Directory.Exists(newDirPath))
                                {
                                    Directory.CreateDirectory(newDirPath);
                                }
                            }

                            foreach (string filePath in Directory.GetFiles(tempExtractPath, "*.*", SearchOption.AllDirectories))
                            {
                                string newFilePath = filePath.Replace(tempExtractPath, extractPath);
                                File.Copy(filePath, newFilePath, true);
                            }

                            // 删除临时目录和嵌套ZIP
                            Directory.Delete(tempExtractPath, true);
                            File.Delete(nestedZipPath);
                            logWriter.WriteLine("处理嵌套ZIP完成");
                        }

                        // 创建批处理文件用于复制文件和重启应用
                        string batchFilePath = Path.Combine(Path.GetTempPath(), "update_final.bat");
                        logWriter.WriteLine($"创建最终更新批处理文件: {batchFilePath}");

                        string batchScript = $@"
                            @echo off
                            chcp 65001 > nul
                            echo 开始最终更新过程 >> ""{logFilePath}""
                            timeout /t 2 > nul

                            echo 尝试结束进程... >> ""{logFilePath}""
                            taskkill /f /im {Path.GetFileName(appPath)} >> ""{logFilePath}"" 2>&1

                            echo 复制文件... >> ""{logFilePath}""
                            xcopy ""{extractPath}\*.*"" ""{Path.GetDirectoryName(appPath)}"" /E /Y /C /H /R >> ""{logFilePath}"" 2>&1

                            echo 清理临时文件... >> ""{logFilePath}""
                            rmdir /s /q ""{extractPath}"" >> ""{logFilePath}"" 2>&1

                            echo 启动应用程序... >> ""{logFilePath}""
                            start """" ""{appPath}"" >> ""{logFilePath}"" 2>&1

                            echo 更新完成 >> ""{logFilePath}""
                            del ""%~f0""
                            ";

                        // 写入批处理文件
                        File.WriteAllText(batchFilePath, batchScript, System.Text.Encoding.UTF8);
                        logWriter.WriteLine("批处理文件创建成功");

                        // 启动批处理文件
                        logWriter.WriteLine("启动最终更新批处理...");
                        Process.Start(new ProcessStartInfo()
                        {
                            FileName = batchFilePath,
                            CreateNoWindow = false,
                            UseShellExecute = true,
                            Verb = "runas"
                        });

                        logWriter.WriteLine("更新脚本已启动，应用程序即将关闭");
                    }
                    catch (Exception ex)
                    {
                        logWriter.WriteLine($"更新过程中发生错误: {ex.Message}");
                        logWriter.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                        throw; // 重新抛出异常以便外层捕获
                    }
                }

                // 关闭应用程序
                XLogger.Info("更新脚本已启动，应用程序即将关闭");
                Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                XLogger.Error($"更新过程中发生错误: {ex.Message}");
                System.Windows.MessageBox.Show($"更新过程中发生错误: {ex.Message}", "更新失败", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            GlobalData.Instance.MainWindowsHwnd = new System.Windows.Interop.WindowInteropHelper(this).Handle;
            //Utils.ApplyDisplayAffinity(GlobalData.Instance.MainWindowsHwnd);
            //开启一个时钟，间隔2秒执行一次
            clock = new DispatcherTimer();
            clock.Interval = new TimeSpan(0, 0, 2);
            clock.Tick += Clock_Tick;
            clock.Start();

            // 自动启动调度器（后台）
            await AutoStartSchedulerInBackground();
        }

        /// <summary>
        /// 在后台自动启动调度器
        /// </summary>
        private async Task AutoStartSchedulerInBackground()
        {
            try
            {
                XLogger.Info("正在初始化后台调度器...");

                // 获取单例ViewModel
                var schedulerViewModel = DanDing1.ViewModels.Windows.SchedulerWindowViewModel.Instance;

                // 初始化ViewModel
                await schedulerViewModel.InitializeAsync();

                // 将ViewModel注册到TrayIconService
                if (TrayIconService.Instance != null)
                {
                    TrayIconService.Instance.SetSchedulerViewModel(schedulerViewModel);
                }

                // 检查是否需要自动启动
                if (schedulerViewModel.AutoStartScheduler)
                {
                    XLogger.Info("根据用户设置，在后台自动启动调度器");
                    await schedulerViewModel.StartSchedulerAsync();
                    XLogger.Info("调度器已在后台启动完成");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"后台启动调度器失败: {ex.Message}");
            }
        }

        #region INavigationWindow methods

        public INavigationView GetNavigation() => RootNavigation;

        public bool Navigate(Type pageType) => RootNavigation.Navigate(pageType);

        public void SetPageService(INavigationViewPageProvider pageService) => RootNavigation.SetPageProviderService(pageService);

        public void ShowWindow() => Show();

        #endregion INavigationWindow methods

        private void RootNavigation_SelectionChanged(NavigationView sender, RoutedEventArgs args)
        {
            NowView = sender;

            // 获取当前选中的导航项
            var selectedItem = sender.SelectedItem as NavigationViewItem;
            if (selectedItem != null)
            {
                // 获取Tag属性中存储的标题文本
                var pageTitle = selectedItem.Tag as string;
                if (!string.IsNullOrEmpty(pageTitle))
                {
                    // 更新标题的最简单方式：设置窗口标题
                    Title = $"{ViewModel.ApplicationTitle} - {pageTitle}";

                    // 可选：尝试更新TitleBar上的标题（如果有）
                    if (TitleBar != null)
                    {
                        TitleBar.Title = $"{ViewModel.ApplicationTitle} - {pageTitle}";
                    }

                    // 如果将来需要更新BreadcrumbBar，应该通过正确的API或数据绑定来实现
                    // 而不是直接修改Items集合
                }
            }
        }
    }
}