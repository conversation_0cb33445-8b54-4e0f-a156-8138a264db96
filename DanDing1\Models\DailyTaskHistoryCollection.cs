using System;
using System.Collections.Generic;
using System.Linq;

namespace DanDing1.Models
{
    /// <summary>
    /// 按日期组织的任务历史记录集合
    /// </summary>
    public class DailyTaskHistoryCollection
    {
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 该日期的历史记录列表
        /// </summary>
        public List<TaskHistoryRecord> Records { get; set; } = new List<TaskHistoryRecord>();

        /// <summary>
        /// 成功任务数量
        /// </summary>
        public int SuccessCount => Records.Count(r => r.Status == "成功");

        /// <summary>
        /// 失败任务数量
        /// </summary>
        public int FailureCount => Records.Count(r => r.Status == "失败");

        /// <summary>
        /// 被中断任务数量
        /// </summary>
        public int CancelledCount => Records.Count(r => r.Status == "中断");

        /// <summary>
        /// 执行中的任务数量
        /// </summary>
        public int RunningCount => Records.Count(r => r.Status == "执行中");

        /// <summary>
        /// 总任务数量
        /// </summary>
        public int TotalCount => Records.Count;

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate => TotalCount > 0 ? (double)SuccessCount / TotalCount * 100 : 0;

        /// <summary>
        /// 格式化的日期（yyyy-MM-dd）
        /// </summary>
        public string FormattedDate => Date.ToString("yyyy-MM-dd");

        /// <summary>
        /// 平均执行时长（仅计算已完成的任务）
        /// </summary>
        public TimeSpan AverageExecutionTime
        {
            get
            {
                var completedRecords = Records.Where(r => r.Status == "成功" || r.Status == "失败").ToList();
                if (completedRecords.Count == 0)
                    return TimeSpan.Zero;

                double totalSeconds = completedRecords.Sum(r => r.Duration.TotalSeconds);
                return TimeSpan.FromSeconds(totalSeconds / completedRecords.Count);
            }
        }

        /// <summary>
        /// 格式化的平均执行时长
        /// </summary>
        public string FormattedAverageExecutionTime
        {
            get
            {
                var timeSpan = AverageExecutionTime;
                if (timeSpan.TotalDays >= 1)
                {
                    return $"{(int)timeSpan.TotalDays}天 {timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
                }
                else
                {
                    return $"{timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
                }
            }
        }

        /// <summary>
        /// 添加记录
        /// </summary>
        public void AddRecord(TaskHistoryRecord record)
        {
            Records.Add(record);
        }

        /// <summary>
        /// 获取指定任务ID的记录
        /// </summary>
        public TaskHistoryRecord GetRecordById(string recordId)
        {
            return Records.FirstOrDefault(r => r.Id == recordId);
        }

        /// <summary>
        /// 更新记录状态
        /// </summary>
        public bool UpdateRecordStatus(string recordId, string status, string errorMessage = null)
        {
            var record = GetRecordById(recordId);
            if (record != null)
            {
                record.Status = status;
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    record.ErrorMessage = errorMessage;
                }
                record.EndTime = DateTime.Now;
                return true;
            }
            return false;
        }
    }
}