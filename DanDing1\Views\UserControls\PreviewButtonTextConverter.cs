using System;
using System.Globalization;
using System.Windows.Data;

namespace DanDing1.Views.UserControls
{
    public class PreviewButtonTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value is bool isPreviewActive && isPreviewActive ? "停止预览" : "同步预览";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}