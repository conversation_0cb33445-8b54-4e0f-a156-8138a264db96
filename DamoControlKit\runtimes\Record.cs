﻿using DamoControlKit.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DamoControlKit.runtimes
{
    /// <summary>
    /// 运行过程中记录点击次数类
    /// </summary>
    public static class Record
    {
        private static int click_Number = 0;
        private static Dictionary<int, List<Point>> dmIDpairs_Points = [];
        private static Dictionary<int, int> dmIDpairs_ClickNumber = [];
        private static List<Point> click_Points = new();

        /// <summary>
        /// 重新记录
        /// </summary>
        public static void ReSet(bool Save = false)
        {
            if (Save) SaveTo("\\");
            click_Number = 0;
            click_Points = new();
        }

        /// <summary>
        /// 重新记录
        /// </summary>
        public static void ReSet(int dmID, bool Save = false)
        {
            dmIDpairs_Points.TryGetValue(dmID, out var points);
            if (points is null)
            {
                dmIDpairs_Points.Add(dmID, new());
                dmIDpairs_Points.TryGetValue(dmID, out points);
                if (points is null) points = new();
            }
            dmIDpairs_ClickNumber.TryGetValue(dmID, out var clicknumber);

            if (Save) SaveTo("\\");
            points = new();
            clicknumber = 0;
        }

        public static void SaveTo(string path)
        {
            path += $"Click_Record {DateTime.Now.ToString("yy-MM-d H-m-s")}.Log";
            using StreamWriter file = new StreamWriter(path, false);
            file.WriteLine($"[{DateTime.Now}]: 开始保存Click点击坐标数据，本次共点击{click_Number}次");
            foreach (var item in click_Points)
                file.WriteLine(item.X + "," + item.Y);
            file.WriteLine("");
        }

        public static void GetData(int dmID, out int number, out List<Point> points)
        {
            number = 0;
            points = [];
            if (!dmIDpairs_Points.TryGetValue(dmID, out _))
                dmIDpairs_Points.Add(dmID, []);
            dmIDpairs_ClickNumber.TryGetValue(dmID, out number);
        }

        public static void GetData(out int number, out List<Point> points)
        {
            number = click_Number;
            points = click_Points;
        }

        /// <summary>
        /// 存储点击数据
        /// </summary>
        public static void SaveClick(int x, int y)
        {
            click_Number++;
            click_Points.Add(new(x, y));
        }

        /// <summary>
        /// 根据大漠ID 存储点击数据
        /// </summary>
        public static void SaveClick(int dmID, int x, int y)
        {
            dmIDpairs_Points.TryGetValue(dmID, out var points);
            if (points is null)
            {
                dmIDpairs_Points.Add(dmID, []);
                dmIDpairs_Points.TryGetValue(dmID, out points);
                points ??= [];
            }
            if (!dmIDpairs_ClickNumber.TryGetValue(dmID, out _)) dmIDpairs_ClickNumber[dmID] = 0;

            dmIDpairs_ClickNumber[dmID]++;
            points.Add(new(x, y));
        }

        /// <summary>
        /// 根据大漠ID 存储点击数据
        /// </summary>
        public static void SaveClick(int dmID, string str)
        {
            dmIDpairs_Points.TryGetValue(dmID, out var points);
            if (points is null)
            {
                dmIDpairs_Points.Add(dmID, []);
                dmIDpairs_Points.TryGetValue(dmID, out points);
                points ??= [];
            }
            if (!dmIDpairs_ClickNumber.TryGetValue(dmID, out _)) dmIDpairs_ClickNumber[dmID] = 0;
            dmIDpairs_ClickNumber[dmID]++;
            points.Add(new(str));
        }

        /// <summary>
        /// 存储点击数据
        /// </summary>
        public static void SaveClick(string str)
        {
            click_Number++;
            click_Points.Add(new(str));
        }
    }
}