﻿using DanDing1.Models;
using ScriptEngine;
using System.Text.Json;
using Wpf.Ui;
using Wpf.Ui.Appearance;
using Wpf.Ui.Controls;
using Wpf.Ui.Extensions;
using XHelper;
using DanDing1.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Wpf.Ui.Abstractions.Controls;

namespace DanDing1.ViewModels.Pages
{
    public partial class SettingsViewModel : ObservableObject, INavigationAware
    {
        [ObservableProperty]
        private string _appVersion = String.Empty;

        [ObservableProperty]
        private ApplicationTheme _currentTheme = ApplicationTheme.Unknown;

        private bool _isInitialized = false;

        /// <summary>
        /// 登录状态
        /// </summary>
        [ObservableProperty]
        private bool _isLoading = false;

        [ObservableProperty]
        private UserConfigModel _userConfig;

        private string To_PicsVer;

        public SettingsViewModel(IContentDialogService contentDialogService)
        {
            ContentDialogService = contentDialogService;
        }

        public IContentDialogService ContentDialogService { get; }

        /// <summary>
        /// 是否启用HTTP日志上报
        /// </summary>
        public bool EnableHttpLogReporting
        {
            get => GlobalData.Instance.appConfig.EnableHttpLogReporting;
            set
            {
                GlobalData.Instance.appConfig.EnableHttpLogReporting = value;
                XConfig.SaveValueToFile("AppConfig", "EnableHttpLogReporting", value);
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 是否启用WebSocket通信
        /// </summary>
        public bool EnableWebSocketCommunication
        {
            get => GlobalData.Instance.appConfig.EnableWebSocketCommunication;
            set
            {
                GlobalData.Instance.appConfig.EnableWebSocketCommunication = value;
                XConfig.SaveValueToFile("AppConfig", "EnableWS", value);
                OnPropertyChanged();
            }
        }

        public async Task OnNavigatedFromAsync()
        {
            if (To_PicsVer != GlobalData.Instance.PicServerVer)
                DynamicData.ReSetInitStatus();
            //保存用户配置
            bool dontSave = UserConfig.Equals(GlobalData.Instance.UserConfig);
            if (!dontSave)
            {
                GlobalData.Instance.UserConfig = (UserConfigModel)UserConfig.Clone();
                if (GlobalData.Instance.appConfig.IsFree)
                {
                    XLogger.Warn("试用状态的用户无法进行云端配置的保存！");
                    return;
                }
                //上传保存配置
                string? v = await GlobalData.Instance.appConfig.dNet.User.SaveUserConfigAsync(JsonSerializer.Serialize(UserConfig));
                if (!string.IsNullOrEmpty(v))
                    XLogger.Info($"用户云端配置保存成功，{v}");
                else
                    XLogger.Info($"保存云端配置失败！请检查网络！");
            }
        }

        public Task OnNavigatedToAsync()
        {
            if (!_isInitialized)
                InitializeViewModel();
            To_PicsVer = GlobalData.Instance.PicServerVer;
            UserConfig = (UserConfigModel)GlobalData.Instance.UserConfig.Clone();
            IsLoading = GlobalData.Instance.appConfig.IsLogin;
            ShowVersionTipIfNeeded();
            return Task.CompletedTask;
        }

        private string GetAssemblyVersion()
        {
            return GlobalData.Instance.appConfig.Info.Now_Ver;
        }

        private void InitializeViewModel()
        {
            CurrentTheme = ApplicationThemeManager.GetAppTheme();
            AppVersion = $"当前版本：{GetAssemblyVersion()}";

            _isInitialized = true;
        }

        [RelayCommand]
        private void OnChangeTheme(string parameter)
        {
            switch (parameter)
            {
                case "theme_light":
                    if (CurrentTheme == ApplicationTheme.Light)
                        break;

                    ApplicationThemeManager.Apply(ApplicationTheme.Light);
                    CurrentTheme = ApplicationTheme.Light;

                    break;

                default:
                    if (CurrentTheme == ApplicationTheme.Dark)
                        break;

                    ApplicationThemeManager.Apply(ApplicationTheme.Dark);
                    CurrentTheme = ApplicationTheme.Dark;

                    break;
            }
        }

        private async void ShowVersionTipIfNeeded()
        {
            // 检查是否需要显示版本号双击提示
            bool hasShownVersionTip = XConfig.LoadValueFromFile<bool>("HasShownVersionTip");
            if (!hasShownVersionTip)
            {
                await ContentDialogService.ShowSimpleDialogAsync(
                    new SimpleContentDialogCreateOptions
                    {
                        Title = "小提示",
                        Content = "您可以双击关于 -> 版本号来检查是否有新版本哦！\n此检查会忽略\"跳过此版本\"和\"明天提醒\"的设置。",
                        CloseButtonText = "我知道了"
                    });
                XConfig.SaveValueToFile("HasShownVersionTip", true);
            }
        }
    }
}