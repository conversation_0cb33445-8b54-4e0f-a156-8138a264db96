﻿using DamoControlKit;
using DamoControlKit.Model;
using DamoControlKit.runtimes;
using ScriptEngine.Core;
using ScriptEngine.Factorys;
using ScriptEngine.Tasks;
using ScriptEngine.Tasks.Base;
using ScriptEngine.Tests;
using System.Diagnostics;
using System.Reflection;
using XHelper;
using static ScriptEngine.Model.TaskConfigsModel;

namespace ScriptEngine.Frames
{
    /// <summary>
    /// 脚本框架
    /// </summary>
    public class ScriptFrame : BaseFrame
    {
        /*
        此类目的：
        1、启动时进行安全检查：提示用户检查模拟器设置，并设置5秒确认等待时间，确保运行环境正确。
        2、插件管理：负责大漠插件的主线程和检测线程的对象创建与绑定，确保多线程协同工作。
        3、资源管理：负责加载和解析程序运行所需的图像识别库、图色特征值和字库资源。
        4、任务调度：根据用户配置进行任务分发和执行控制，支持多任务队列和循环执行。
        5、异常处理：实现运行时的错误捕获和处理机制，确保程序稳定性。
        6、资源释放：负责程序结束时的资源回收，包括对象解绑和内存释放。
         */

        /// <summary>
        /// 获取大漠ID 回调
        /// </summary>
        public Getdmid GetDmID;

        /// <summary>
        /// 任务用户通知信息
        /// </summary>
        public Dictionary<string, string> Task_UserNotificationMessage = new();

        private const string StartTip = "欢迎使用蛋定助手，遇到异常、停止请蛋定，仔细查看帮助链接，请确保您的模拟器设置符合要求！\r\n" +
                            "若有意料之外的错误，您可以加群(621016172)咨询管理员，谢谢！\r\n" +
            "5S后将继续执行，再说一遍：请确保您模拟器的设置符合我们的要求(分辨率、渲染引擎···)！";

        private dmsoft dm_Main;

        private dmsoft dm_Sub;

        /// <summary>
        /// 是否存在定时任务
        /// </summary>
        private bool haveTimerTask = false;

        private Task Maintask;

        private Task Subtask;

        /// <summary>
        /// 任务名转换类名
        /// </summary>
        private Dictionary<string, string> TaskClassName = new()
        {
            {"组队御魂","YuHun_ZuDui" },
            {"御魂","YuHun" },
            {"突破","TuPo" },
            {"斗技","DouJi" },
            {"御灵","YuLing" },
            {"组队探索","TanSuo_ZuDui" },
            {"六道","LiuDao.LiuDaoTask" },
            {"契灵","QiLing.QiLingTask" },
            {"探索","TanSuo" },
            {"觉醒","JueXing" },
            {"寮突","LiaoTu" },
            {"业原火","YeYuanHuo" },
            {"英杰","YingJieTask" },
            {"日轮","RiLun" },
            {"永生","YongSheng" },
            {"调试1","DebugTask1" },
            {"等待","DelayTask" },
            {"百鬼","BaiGuiTask" },
            {"Buff","BuffTask" },
            {"定时任务","TimerTask" },
            {"日常任务","DailyTask" },
            {"活动","Actives.ActiveTask" },//通用活动 ActiveTask || 灵染试炼 LingRanShiLianTask || 狭间幻境 XiaJianHuanJingTask
            {"每周秘闻","FastTasks.MeiZhouMiWenTask" },
            {"抽厕纸","FastTasks.ChouCeZhi" },
            {"半自动","FastTasks.BanZiDong" },
        };

#pragma warning disable CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑声明为可以为 null。

        private bool TimerInited = false;

        public ScriptFrame(CancellationTokenSource ct, DDBuilder dB) : base(ct, dB)
        {
            DB.ShareData = new();
        }

#pragma warning restore CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑声明为可以为 null。

        public delegate void Getdmid(int main_id, int sub_id);

        private Log log => DB.Log;

        /// <summary>
        /// 释放对象
        /// </summary>
        public void Free()
        {
            Debug.WriteLine("[Free] 开始释放大漠对象...");

            // 解绑副对象
            if (dm_Sub != null)
            {
                try
                {
                    Debug.WriteLine($"[Free] 准备解绑副对象，对象ID: {dm_Sub.GetID()}");
                    dm_Sub.UnBindWindow();
                    Debug.WriteLine("[Free] 副对象解绑成功");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[Free] 副对象解绑失败: {ex.Message}");
                    log.Debug($"释放副对象(UnBindWindow)时发生异常: {ex.Message}");
                }
            }

            // 解绑主对象
            if (dm_Main != null)
            {
                try
                {
                    Debug.WriteLine($"[Free] 准备解绑主对象，对象ID: {dm_Main.GetID()}");
                    dm_Main.UnBindWindow();
                    Debug.WriteLine("[Free] 主对象解绑成功");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[Free] 主对象解绑失败: {ex.Message}");
                    log.Debug($"释放主对象(UnBindWindow)时发生异常: {ex.Message}");
                }
            }

            // 休眠以等待COM对象资源释放
            try
            {
                Thread.Sleep(500);
            }
            catch (Exception ex)
            {
                log.Debug($"释放对象延迟过程中发生异常: {ex.Message}");
            }

            // 释放副对象
            if (dm_Sub != null)
            {
                try
                {
                    dm_Sub.ReleaseObj();
                    Debug.WriteLine("[Free] 副对象释放成功");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[Free] 副对象释放失败: {ex.Message}");
                    log.Debug($"释放副对象(ReleaseObj)时发生异常: {ex.Message}");
                }
                dm_Sub = null;
            }

            // 释放主对象
            if (dm_Main != null)
            {
                try
                {
                    dm_Main.ReleaseObj();
                    Debug.WriteLine("[Free] 主对象释放成功");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[Free] 主对象释放失败: {ex.Message}");
                    log.Debug($"释放主对象(ReleaseObj)时发生异常: {ex.Message}");
                }
                dm_Main = null;
            }
            //GC
            GC.Collect();
            Debug.WriteLine("[Free] 大漠对象释放完成");
        }

        /// <summary>
        /// 开始任务
        /// </summary>
        public void Run()
        {
            log.Info(StartTip);
            if (!Debugger.IsAttached)
                Delay(5000);
            DB.UserConfigs.TryGetValue("CustClick_Result", out object _T1);
            DB.UserConfigs.TryGetValue("CustClick_Damo", out object _T2);
            DynamicData.UpdateClickPositions(_T1?.ToString() ?? "关闭", _T2?.ToString() ?? "关闭");

            try
            {
                DB.OnUpdateGameTask?.Invoke("绑定游戏");//通知UI更新当前执行的任务
                log.Info($"句柄[{DB.Simulator.Hwnd}],开始执行脚本流程！");
                log.Info($"主要对象开始绑定[主对象]");
                dm_Main = Bind();
                SetDicts(dm_Main);
                Delay(1000);
                log.Info($"辅助对象开始绑定[检测对象]");
                dm_Sub = Bind();
                SetDicts(dm_Sub);
                Delay(500);
                //log.Debug($"当前大漠插件对象数量：{dm_Main.GetDmCount()}");

                GetDmID?.Invoke(dm_Main.GetID(), dm_Sub.GetID());//捕获大漠ID

                if (Debugger.IsAttached) new ScriptFrame_Run_Test().Init(dm_Sub).Test();

                Record.ReSet(dm_Main.GetID());//重置大漠点击记录器 主
                Record.ReSet(dm_Sub.GetID());//重置大漠点击记录器 副
                log.Info(Utils.GetSystemInfo(dm_Main));
                Delay(2000);

                // 保持原有的任务启动顺序
                Subtask = Task.Run(SubTask, ct.Token);
                Delay(1000);
                if (DB.GameSetting.IsStartYYSToCourtyard)
                {
                    log.Info("等待游戏到庭院后再启动主任务！");
                    while (DB.GameSetting.IsStartYYSToCourtyard)
                    {
                        Delay(1000);
                    }
                }

                Maintask = Task.Run(MainTask, ct.Token);
                Delay(1000);
                try
                {
                    Maintask.Wait();
                }
                catch (AggregateException ae)
                {
                    // 检查是否为任务取消异常
                    if (ae.InnerExceptions.Any(e => e is TaskCanceledException || e.Message.Contains("A task was canceled")))
                    {
                        log.Info("任务已被取消");
                    }
                    else
                    {
                        foreach (var e in ae.InnerExceptions)
                        {
                            log.Error($"任务执行过程中发生异常: {e.Message}");
                            log.Error(e.StackTrace ?? "没有相应的堆栈信息！");
                        }
                        throw;
                    }
                }
                catch (Exception e)
                {
                    log.Error($"任务等待过程中发生异常: {e.Message}");
                    log.Error(e.StackTrace ?? "没有相应的堆栈信息！");
                    throw;
                }
            }
            catch (TaskCanceledException)
            {
                // 仅标记任务已取消
                log.Info("任务已被取消");
            }
            catch (Exception e)
            {
                if (e.Message.Contains("A task was canceled"))
                {
                    log.Info("任务已被取消");
                }
                else
                {
                    log.Error($"发生未知异常！({e.Message})");
                    log.Error(e?.StackTrace?.ToString() ?? "没有相应的堆栈信息！");
                    log.Error(e?.InnerException?.Message ?? "没有相应的异常描述信息！");
                    string nowtime = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                    string errorDir = $"{AppDomain.CurrentDomain.BaseDirectory}Logs\\Error-{DB.Log.LogClassName}-{nowtime}";
                    Directory.CreateDirectory(errorDir);
                    dm_Main.Capture(0, 0, 2000, 2000, errorDir + "\\LastErrorImage.bmp");
                    string zipFilePath = XLogger.PackLogs(errorDir, [errorDir + "\\LastErrorImage.bmp", $"{AppDomain.CurrentDomain.BaseDirectory}Logs\\mumu_json_output.json"]);
                    log.Warn($"错误信息已保存到{errorDir}中，将文件夹中的zip文件提交给我，我会尽快处理并修复问题！");
                    File.Delete(errorDir + "\\LastErrorImage.bmp");
                }
            }
            finally
            {
                // 确保先调用取消令牌和记录日志
                if (!ct.IsCancellationRequested)
                    ct.Cancel();

                log.Info($"脚本任务结束");

                // 保存记录器数据
                DB?._recorder?.WriteToFile($".\\runtimes\\Debug\\{DateTime.Now:yyyyMMddHHmmssfff}.sr");
                DB._recorder ??= null;

                try
                {
                    // 安全释放资源
                    Free();
                }
                catch (Exception ex)
                {
                    log.Error($"释放资源时发生异常: {ex.Message}");
                }

                GC.Collect();
            }
        }

        /// <summary>
        /// 设置字库
        /// </summary>
        private static void SetDicts(dmsoft dm)
        {
            dm.ClearDict(0);
            foreach (var str in DynamicData.DictList)
            {
                dm.UseDict(0);
                dm.AddDict(0, str);
            }
        }

        /// <summary>
        /// 绑定窗口
        /// </summary>
        /// <returns></returns>
        private dmsoft Bind(dmsoft? _dm = null)
        {
            try
            {
                DB.BindSetting.Hwnd = DB.Simulator.Hwnd;
                BindModel bindset = DB.BindSetting;
                if (!bindset.IsReady)
                    throw new Exception("句柄已失效，无法绑定游戏窗口，请重新选择模拟器句柄!");

                return _dm != null
                    ? HandleExistingDmObject(_dm, bindset)
                    : CreateNewDmObject(bindset);
            }
            catch (Exception ex)
            {
                return HandleMemoryException(ex, _dm);
            }
        }

        /// <summary>
        /// 主线启动方法
        /// </summary>
        private async Task MainTask()
        {
            try
            {
                log.Info("开始分析依次执行任务清单！列表整体循环次数：" + DB.GameSetting.LoopCount);
                log.Debug("设置的庭院皮肤为：" + DB.GameSetting.CourtyardSkin);
                //检查初始化定时任务
                await TimerInitAndLoop();

                for (int i = 0; i < DB.GameSetting.LoopCount; i++)
                {
                    log.Info($"开始执行第{i + 1}次任务循环！");
                    // 使用ToArray创建集合快照，防止迭代过程中集合被修改
                    DB.TaskLists.ForEach((str, config) =>
                    {
                    KaPingReStart:
                        try
                        {
                            if (DB.ShareData != null)
                            {
                                DB.ShareData.MainNowTask = config.Name;//更新当前执行的任务
                            }
                            DB.OnUpdateGameTask?.Invoke(config.Name);//通知UI更新当前执行的任务
                            ReloadPreset(config.Name, config); //重设预设
                            TaskClassName.TryGetValue(config.Name, out string? className);
                            if (className is null) throw new Exception($"系统发现一个未知的任务[{config.Name}]..无法继续执行！");
                            Assembly assembly = Assembly.GetExecutingAssembly(); // 获取当前程序集
                            Type type = assembly.GetType("ScriptEngine.Tasks." + className) ?? throw new Exception($"系统发现一个未知的任务类[{className}]..无法继续执行！");      // 通过类名获取同名类
                            var obj = Activator.CreateInstance(type);       // 创建实例
                            if (DB.ShareData != null)
                            {
                                DB.ShareData.RunningTask = obj as BaseTask;
                            }
                            log.Info($"开始执行任务：{str}");

                            MethodInfo method = type.GetMethod("Init")
                                ?? throw new Exception("任务初始化Init异常...");

                            method.Invoke(obj, [DB, dm_Main, ct, ""]);

                            method = type.GetMethod("Start") ?? throw new Exception("任务初始化Start异常...");
                            try
                            {
                                method.Invoke(obj, [config]);
                                try
                                {
                                    var buffValue = DB.Data.Get("关Buff");
                                    if (buffValue != null)
                                    {
                                        string? iss1s = buffValue.ToString();
                                        if (iss1s != null && int.TryParse(iss1s, out int turnoffbuff))
                                        {
                                            turnoffbuff++;
                                            DB.Data.Set("关Buff", turnoffbuff);
                                            if (turnoffbuff >= 3)
                                            {
                                                DB.Data.Remove("关Buff");
                                                RunTaskFactory.Run<BuffTask>(DB, dm_Main, ct, 1, "自动Buff", new() { { "所有", "0" } });
                                            }
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    log.Debug($"处理关Buff发生异常: {ex.Message}");
                                }
                            }
                            catch (Exception e)
                            {
                                if (e?.InnerException?.Message.Contains("canceled") ?? false)
                                {
                                    var userNotificationMessage1 = type.GetProperty("UserNotificationMessage")?.GetValue(obj);
                                    string key1 = config.Name;
                                    int id1 = 1;
                                    while (Task_UserNotificationMessage.ContainsKey(key1))
                                    {
                                        key1 = $"{config.Name}_{id1++}";
                                    }
                                    Task_UserNotificationMessage.Add(key1, userNotificationMessage1?.ToString() ?? "");
                                    throw new TaskCanceledException();
                                }
                                else if (e?.InnerException?.Message.Contains("卡屏") ?? false)
                                    goto KaPingReStart;
                                else if (e?.InnerException?.Message.Contains("定时任务执行结束") ?? false)
                                    goto KaPingReStart;
                                else
                                {
                                    log.Write($"任务：{str} 发生未知异常！");
                                    log.Write(e?.StackTrace?.ToString() ?? "没有相应的堆栈信息！");
                                    log.Write(e?.InnerException?.Message ?? "没有相应的异常描述信息！");
                                    throw;
                                }
                            }
                            var userNotificationMessage = type.GetProperty("UserNotificationMessage")?.GetValue(obj);
                            string key = config.Name;
                            int id = 1;
                            while (Task_UserNotificationMessage.ContainsKey(key))
                            {
                                key = $"{config.Name}_{id++}";
                            }
                            Task_UserNotificationMessage.Add(key, userNotificationMessage?.ToString() ?? "");
                        }
                        catch (Exception ex)
                        {
                            if (ex is TaskCanceledException) throw;
                            log.Error($"任务：{config.Name} {config.Count}次 执行出错: {ex.Message}");
                        }
                        return 1;
                    });
                }

                await TimerInitAndLoop(true);
            }
            catch (Exception ex)
            {
                if (ex is TaskCanceledException) throw;
                if (!ex.Message.Contains("canceled")) log.Error($"主任务执行出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 重新判断加载预设
        /// </summary>
        private void ReloadPreset(string str, Configs config)
        {
            Dictionary<string, string> topairs = new()
            {
                {"御魂", "Gal_YuShe_Yuhun"},
                {"觉醒", "Gal_YuShe_Juexing"},
                {"探索", "Gal_YuShe_Tansuo"},
                {"日轮", "Gal_YuShe_Rilun"},
                {"永生", "Gal_YuShe_Yongsheng"},
                {"御灵", "Gal_YuShe_Yuling"},
                {"业原火", "Gal_YuShe_Yeyuanhuo"},
                {"突破", "Gal_YuShe_Tupo"},
                {"悬赏", "Gal_YuShe_Xuanshang"},
                {"六道", "Gal_YuShe_Liudao"},
                {"契灵", "Gal_YuShe_Qiling"},
                {"斗技", "Gal_YuShe_Douji"},
                {"英杰", "Gal_YuShe_Yingjie"}
            };

            if (DB.UserConfigs.TryGetValue("Gal_YuShe", out object? _T) && _T is bool && (bool)_T)
                if (topairs.TryGetValue(str, out string presetKey))
                    if (DB.UserConfigs.TryGetValue(presetKey, out object? _T1) && _T1 is string && !string.IsNullOrEmpty((string)_T1))
                        config.Others["Preset"] = _T1.ToString();
        }

        /// <summary>
        /// 辅助启动方法
        /// </summary>
        private void SubTask()
        {
            SubTask subTask = new();
            subTask.Init(DB, dm_Sub, ct);
            try { subTask.Start(new() { Name = "Sub", Count = -1 }); }
            catch (TaskCanceledException)
            {
                Task_UserNotificationMessage.TryAdd("检测线程", subTask.UserNotificationMessage);
            }
            catch (Exception e)
            {
                Task_UserNotificationMessage.TryAdd("检测线程", subTask.UserNotificationMessage);
                throw new Exception(e.Message);
            }
        }

        /// <summary>
        /// 定时任务初始化
        /// </summary>
        private async Task TimerInitAndLoop(bool over = false)
        {
            if (over && haveTimerTask)
            {
                log.Warn("所有任务执行完毕，但是有定时任务，进行值守...请手动结束任务！");
                await ZhiShou();
                return;
            }

            if (!TimerInited)
            {
                DB.OnUpdateGameTask?.Invoke("初始定时任务");//通知UI更新当前执行的任务

                foreach (var item in DB.TaskLists.TaskLists)
                {
                    if (item.Value.Name.Contains("定时任务"))
                    {
                        log.Info("您当前列表中存在定时任务，现在开始初始化定时任务！");
                        haveTimerTask = true;
                        DB.TimerTask = new TimerTask();
                        DB.TimerTask.Init(DB, dm_Main, ct, "定时任务");
                        DB.TimerTask.Start(item.Value);
                        break;
                    }
                }
                TimerInited = true;
            }

            if (haveTimerTask)
            {
                DB.TaskLists.Delete("定时任务");
                if (DB.TaskLists.TaskLists.Count == 0)
                {
                    log.Warn("当前除了定时任务外，没有其他任务，主任务线程进入等待状态，请手动结束任务！");
                    await ZhiShou();
                    return;
                }
            }
        }

        /// <summary>
        /// 尝试使用指定模式绑定窗口
        /// </summary>
        /// <param name="bindset">绑定设置</param>
        /// <param name="mode">绑定模式</param>
        /// <returns>绑定成功的大漠对象</returns>
        private dmsoft TryBindWithMode(BindModel bindset, int mode)
        {
            // 保存原始模式
            int originalMode = bindset.Mode;

            try
            {
                // 设置新的绑定模式
                bindset.Mode = mode;
                log.Debug($"尝试使用绑定模式 {mode} 绑定窗口");

                dmsoft dm = DamoKit.BindHwnd(bindset, out string errorStr);
                if (errorStr != "") throw new Exception(errorStr);

                return dm;
            }
            catch
            {
                // 恢复原始模式
                bindset.Mode = originalMode;
                throw;
            }
        }

        private async Task ZhiShou()
        {
            //关闭模拟器
            void OffLineGame()
            {
                int index = int.Parse(DB.Data.Get("MuMuIndex")?.ToString() ?? "0");

                if (DB.TimerTask.OffLine && index >= 0) // 离线值守-下线模拟器
                {
                    dm_Main.UnBindWindow();
                    dm_Sub.UnBindWindow();
                    DB.OnUpdateGameTask?.Invoke("定时离线值守");//通知UI更新当前执行的任务
                    log.Info_Green("关闭模拟器，下线等待下次定时任务触发！");
                    DB.MumuManager.CloseByIndex(index);
                    DB.OnUpdateGameHwnd?.Invoke(0, 0); //通知UI更新模拟器窗口句柄
                }
            }

            //打开模拟器
            async Task OnLineGame()
            {
                int index;
                var mumuIndexValue = DB.Data.Get("MuMuIndex");
                if (mumuIndexValue != null)
                {
                    index = Convert.ToInt32(mumuIndexValue);
                }
                else
                {
                    index = 0;
                    log.Warn("未找到MuMuIndex值，使用默认值0");
                }

                if (DB.TimerTask.OffLine && index >= 0) // 离线值守-上线模拟器、启动阴阳师
                {
                    // 先检查并关闭模拟器
                    log.Info_Green("检查并关闭模拟器..");
                    DB.MumuManager.CloseByIndex(index);
                    Delay(2000); // 等待2秒确保模拟器完全关闭

                    log.Info_Green("启动模拟器..");
                    var result = await DB.MumuManager.OpenByIndexAsync(index);
                    if (!result.success)
                    {
                        log.Error("启动模拟器失败，无法继续执行寄养任务！");
                        throw new Exception("启动模拟器失败，无法继续执行寄养任务！");
                    }
                    log.Info_Green("启动模拟器完成..5s后重新绑定新模拟器句柄、并等待8s启动阴阳师..");
                    DB.OnUpdateGameHwnd?.Invoke(result.mainWndHandle, result.renderWndHandle); //通知UI更新模拟器窗口句柄
                    Delay(5000);
                    DB.BindSetting.Hwnd = DB.Simulator.Hwnd = result.renderWndHandle;
                    dm_Main = Bind(dm_Main);
                    dm_Sub = Bind(dm_Sub);
                    Delay(8000);
                    log.Info_Green("启动阴阳师..等待阴阳师到庭院后继续定时操作..");
                    DB.MumuManager.Start_App("阴阳师", DB.MumuManager.GetAdbPortByIndex(index).port);
                    Delay(5000);
                    DB.GameSetting.IsStartYYSToCourtyard = true;
                    if (DB.GameSetting.IsStartYYSToCourtyard)
                        while (DB.GameSetting.IsStartYYSToCourtyard)
                            Delay(1000);
                }
            }

            DB.OnSendNotification?.Invoke($"[{DB.Log.LogClassName}]定时值守开始通知", $"您的所有任务已完成，但您添加了定时任务\r\n" +
                $"进入自动值守状态，以下是下次任务的触发时间\r\n" +
                $"下次寄养任务触发时间为：{(DB.TimerTask?.Config_JiYang != null ? DB.TimerTask.Config_JiYang.DoTime.ToString("yyyy-MM-dd HH:mm:ss") : "未启用")}\r\n" +
                $"下次放卡任务触发时间为：{(DB.TimerTask?.Config_FangKa != null ? DB.TimerTask.Config_FangKa.DoTime.ToString("yyyy-MM-dd HH:mm:ss") : "未启用")}\r\n");

            DB.OnUpdateGameTask?.Invoke("定时任务值守");//通知UI更新当前执行的任务
            if (DB.TimerTask != null) DB.TimerTask.isSubScan = false;
            if (DB.ShareData != null) DB.ShareData.MainNowTask = "等待";
            OffLineGame();
            while (true)
            {
                if (DB.TimerTask != null && DB.TimerTask.Check_AllTask())
                {
                    await OnLineGame();
                    DB.TimerTask.DoAllTask();
                    OffLineGame();
                }
                Delay(1000);
            }
        }

        #region 绑定相关私有方法

        /// <summary>
        /// 配置大漠插件通用设置
        /// </summary>
        private void ConfigureDmSettings(dmsoft dm)
        {
            dm.EnableRealKeypad(1);
            dm.EnableRealMouse(2, 20, 30);
            dm.DisablePowerSave();
            dm.DisableScreenSave();
            dm.SetShowErrorMsg(0);
        }

        /// <summary>
        /// 创建新dm对象绑定
        /// </summary>
        private dmsoft CreateNewDmObject(BindModel bindset)
        {
            const int maxRetries = 10;
            return RetryBindOperation(() =>
            {
                var dm = TryBindWithMode(bindset, bindset.Mode);
                return (dm, string.Empty);
            },
            (successDm) => ConfigureDmSettings(successDm),
            maxRetries,
            "新对象");
        }

        /// <summary>
        /// 处理传入的dm对象绑定
        /// </summary>
        private dmsoft HandleExistingDmObject(dmsoft dm, BindModel bindset)
        {
            const int maxRetries = 10;
            return RetryBindOperation(() =>
            {
                string errorStr = string.Empty;
                bool result = DamoKit.BindHwndWithObject(dm, bindset, out errorStr);
                return (dm, errorStr);
            },
            (successDm) => ConfigureDmSettings(successDm),
            maxRetries,
            "传入对象");
        }

        /// <summary>
        /// 处理内存访问异常
        /// </summary>
        private dmsoft HandleMemoryException(Exception ex, dmsoft existingDm)
        {
            if (!(ex.Message.Contains("Attempted to read or write protected memory") ||
                ex.Message.Contains("Exception has been thrown by the target of an invocation")))
            {
                throw ex;
            }

            log.Error($"绑定窗口时发生异常: {ex.Message}");
            log.Error(ex.StackTrace);

            int[] alternativeModes = new int[] { 2, 101, 103 };
            BindModel bindset = DB.BindSetting;

            return existingDm != null
                ? TryAlternativeModes(existingDm, alternativeModes, bindset)
                : TryCreateWithModes(alternativeModes, bindset);
        }

        /// <summary>
        /// 通用重试绑定操作
        /// </summary>
        private dmsoft RetryBindOperation(
            Func<(dmsoft, string)> bindAction,
            Action<dmsoft> configureAction,
            int maxRetries,
            string operationType)
        {
            int retryCount = 0;
            int baseDelay = 5000;
            Exception lastException = null;

            while (retryCount < maxRetries)
            {
                try
                {
                    var (dm, error) = bindAction();
                    if (!string.IsNullOrEmpty(error)) throw new Exception(error);

                    configureAction(dm);
                    return dm;
                }
                catch (Exception ex)
                {
                    if (!ex.Message.Contains("绑定发生错误"))
                    {
                        throw;
                    }
                    lastException = ex;
                    log.Error($"{operationType}绑定失败 (第{retryCount + 1}次尝试): {ex.Message}");

                    int delayMs = Math.Min(baseDelay * (int)Math.Pow(2, retryCount), 60000);
                    log.Info($"延时{delayMs / 1000}秒后重试绑定操作...");
                    Delay(delayMs);
                    log.Info($"开始重试{operationType}绑定操作");
                }
                retryCount++;
            }
            log.Error($"绑定操作已达到最大重试次数({maxRetries}次)，终止重试");
            throw new Exception($"{operationType}绑定失败，已重试{maxRetries}次：{lastException?.Message}", lastException);
        }

        /// <summary>
        /// 使用备选模式尝试绑定现有对象
        /// </summary>
        private dmsoft TryAlternativeModes(dmsoft dm, int[] modes, BindModel bindset)
        {
            foreach (int mode in modes)
            {
                try
                {
                    log.Info($"尝试使用绑定模式 {mode} 重新绑定传入对象");
                    int originalMode = bindset.Mode;
                    bindset.Mode = mode;

                    string errorStr = string.Empty;
                    bool bindResult = DamoKit.BindHwndWithObject(dm, bindset, out errorStr);
                    if (!string.IsNullOrEmpty(errorStr))
                        throw new Exception(errorStr);

                    ConfigureDmSettings(dm);
                    return dm;
                }
                catch (Exception retryEx)
                {
                    log.Error($"使用绑定模式 {mode} 重试失败: {retryEx.Message}");
                    bindset.Mode = bindset.Mode; // 恢复原始模式
                }
            }
            throw new Exception("所有备选模式尝试失败");
        }

        /// <summary>
        /// 使用备选模式尝试创建新对象
        /// </summary>
        private dmsoft TryCreateWithModes(int[] modes, BindModel bindset)
        {
            foreach (int mode in modes)
            {
                try
                {
                    log.Info($"尝试使用绑定模式 {mode} 重新绑定窗口");
                    dmsoft dm = TryBindWithMode(bindset, mode);
                    ConfigureDmSettings(dm);
                    return dm;
                }
                catch (Exception retryEx)
                {
                    log.Error($"使用绑定模式 {mode} 重试失败: {retryEx.Message}");
                }
            }
            throw new Exception("所有备选模式尝试失败");
        }

        #endregion 绑定相关私有方法
    }
}