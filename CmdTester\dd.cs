﻿using DamoControlKit;
using DamoControlKit.Model;
using OpenCvSharp;
using ScriptEngine;
using ScriptEngine.Model;
using ScriptEngine.MuMu;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Runtime;
using System.Text;
using System.Threading.Tasks;
using XHelper;
using XHelper.OCR;
using static ScriptEngine.Model.TaskConfigsModel;
using System.IO; // Added for File operations
using System.Collections.Concurrent; // Added for ConcurrentBag
using System.Threading; // Added for Threading concepts, Task.Run

namespace CmdTester
{
    internal class dd
    {
        public dd()
        {
            Console.WriteLine("Testdd COTR Init...");
        }

        /// <summary>
        /// 获取任务类
        /// </summary>
        /// <param name="configs"></param>
        /// <returns></returns>
        private static TaskConfigsModel GetTaskConfigs(ObservableCollection<TaskConfigsModel.Configs> configs)
        {
            TaskConfigsModel TaskConfigs = new();
            foreach (var config in configs)
                TaskConfigs.AddConfig(config.Name, config);
            return TaskConfigs;
        }

        /// <summary>
        /// 测试保存的任务
        /// </summary>
        public async void run()
        {
            var GameTaskLists = XConfig.LoadValueFromFile<ObservableCollection<TaskConfigsModel.Configs>>("TaskList", "游戏1") ?? [];
            var tcm = GetTaskConfigs(GameTaskLists);
            DDBuilder dBuilder = new();
            dBuilder.SetSimulator("mumu", HWND)
                .SetBindSetting(BindModels.GetBindModel("mumu", 1))
                .SetTaskList(tcm)
                .InitLog("游戏1")
                .SetGameSettings(new() { XuanShang = true, CourtyardSkin = "枫色秋庭", LoopCount = 1 });
            await dBuilder.SetPicsVerAsync("本地", "888");
            if (!DamoKit.IsReg) XLogger.Info("插件初始化中.. 首次执行请耐心等待3s左右的时间..");
            //检查句柄
            if (!await dBuilder.CheckAsync("xsllovemlj83e580fa0fd8214b5b9a70864794b53a"))
            {
                XLogger.Warn("警告", "无法开始任务，句柄已经失效，请重新绑定！");
                return;
            }

            DDScript dScript = new()
            {
                TaskEnded_Data = (t) =>
                {
                    RecordData data = t as RecordData;
                    XLogger.Info($"任务执行数据：点击了{data.MainClick}次,坐标{data.MainPoints.Count}个");
                }
            };
            dScript.Start(dBuilder);
            XLogger.Debug("控制流程结束！后台执行任务中！");
        }

        private const int HWND = 202036;

        /// <summary>
        /// 测试双倍速
        /// </summary>
        public void speed()
        {
            if (!DamoKit.Reg("xsllovemlj83e580fa0fd8214b5b9a70864794b53a", out string errorStr))
                throw new Exception("注册失败：" + errorStr);
            var dm = DamoKit.BindHwnd(new BindModel() { Hwnd = HWND }.SetModel(Bind_SetModel.speed),
                                        out errorStr);//绑定窗口 速度模式
            XLogger.Info("绑定完成");
            dm.delay(1000);
            if (dm.IsBind(HWND) == 0)
                throw new Exception("绑定失败");
            dm.CapturePng(0, 0, 2000, 2000, "test.png");

            XLogger.Info("开启加速2倍速");
            dm.HackSpeed(2);
            dm.delay(1000000);
        }

        private static void ShowImageInfo(string imagePath, string label)
        {
            try
            {
                using (Mat img = Cv2.ImRead(imagePath))
                {
                    if (img.Empty())
                    {
                        Console.WriteLine($"{label} ({imagePath}): 无法读取图像");
                        return;
                    }

                    long fileSize = new FileInfo(imagePath).Length;
                    string fileSizeStr = fileSize < 1024 ? $"{fileSize} 字节" : $"{fileSize / 1024.0:F2} KB";

                    Console.WriteLine($"{label} ({imagePath}):");
                    Console.WriteLine($"  尺寸: {img.Width}x{img.Height} 像素");
                    Console.WriteLine($"  通道数: {img.Channels()}");
                    Console.WriteLine($"  文件大小: {fileSizeStr}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{label} ({imagePath}): 读取图像信息时出错: {ex.Message}");
            }
        }

        public void a()
        {
            Console.WriteLine("OCR小图像处理测试 - 十轮循环验证");

            // 测试图片路径 - 分别测试正常图像和小图像
            string normalImagePath = "D:\\1.jpg";
            string smallImagePath = "D:\\2.jpg";

            if (!File.Exists(normalImagePath))
            {
                Console.WriteLine($"正常测试图片 {normalImagePath} 不存在");
                Console.WriteLine("请按任意键退出...");
                Console.ReadKey();
                return;
            }

            if (!File.Exists(smallImagePath))
            {
                Console.WriteLine($"小图测试图片 {smallImagePath} 不存在");
                Console.WriteLine("请按任意键退出...");
                Console.ReadKey();
                return;
            }

            try
            {
                // 显示图像信息
                ShowImageInfo(normalImagePath, "正常图像");
                ShowImageInfo(smallImagePath, "小图像");

                Console.WriteLine("\n开始十轮OCR循环测试:");

                // 执行十轮循环测试
                for (int round = 1; round <= 10; round++)
                {
                    Console.WriteLine($"\n===== 第 {round} 轮测试 =====");

                    // 测试正常图像
                    Console.WriteLine($"[{DateTime.Now.ToString("HH:mm:ss")}][ INFO] 测试正常图像，图片路径：{normalImagePath}");
                    string normalResult = TestOcr(normalImagePath);
                    Console.WriteLine($"[{DateTime.Now.ToString("HH:mm:ss")}][ INFO] 正常图像识别结果：{normalResult}");

                    // 测试小图像
                    Console.WriteLine($"[{DateTime.Now.ToString("HH:mm:ss")}][ INFO] 测试小图像，图片路径：{smallImagePath}");
                    string smallResult = TestOcr(smallImagePath);
                    Console.WriteLine($"[{DateTime.Now.ToString("HH:mm:ss")}][ INFO] 小图像识别结果：{smallResult}");

                    // 输出内存使用情况
                    ReportMemoryUsage();
                }

                // 最后再测试一次正常图像以验证OCR引擎是否仍然可用
                Console.WriteLine($"\n===== 最终验证测试 =====");
                Console.WriteLine($"[{DateTime.Now.ToString("HH:mm:ss")}][ INFO] 验证OCR引擎状态，图片路径：{normalImagePath}");
                string finalResult = TestOcr(normalImagePath);
                Console.WriteLine($"[{DateTime.Now.ToString("HH:mm:ss")}][ INFO] 文本OCR识别结果：{finalResult}");

                // 添加内存监控循环，每5秒输出一次内存使用情况
                Console.WriteLine("\n===== 开始内存监控 (每5秒输出一次) =====");
                Console.WriteLine("按ESC键停止监控并退出程序...");
                Console.WriteLine("按G键手动触发一次完整GC回收");
                Console.WriteLine("按空格键获取一次详细内存快照");

                // 启动一个后台任务进行监控
                bool monitoring = true;

                // 记录初始内存使用，用于检测泄漏
                long initialMemory = GC.GetTotalMemory(true);
                DateTime startTime = DateTime.Now;

                // 创建一个线程来监听键盘输入
                var inputTask = Task.Run(() =>
                {
                    while (monitoring)
                    {
                        var key = Console.ReadKey(true);
                        if (key.Key == ConsoleKey.Escape)
                        {
                            monitoring = false;
                        }
                        else if (key.Key == ConsoleKey.G)
                        {
                            Console.WriteLine("\n----- 手动触发完整GC回收 -----");
                            // 触发完整的垃圾回收
                            long beforeGC = GC.GetTotalMemory(false);
                            GC.Collect(2, GCCollectionMode.Forced, true, true);
                            GC.WaitForPendingFinalizers();
                            GC.Collect(2, GCCollectionMode.Forced, true, true);
                            long afterGC = GC.GetTotalMemory(true);
                            Console.WriteLine($"  GC前内存: {beforeGC / (1024 * 1024)} MB");
                            Console.WriteLine($"  GC后内存: {afterGC / (1024 * 1024)} MB");
                            Console.WriteLine($"  释放内存: {(beforeGC - afterGC) / (1024 * 1024)} MB");
                        }
                        else if (key.Key == ConsoleKey.Spacebar)
                        {
                            Console.WriteLine("\n----- 详细内存快照 -----");
                            ReportDetailedMemoryUsage();
                        }
                    }
                });

                // 每5秒输出一次内存信息
                int monitorCount = 1;
                while (monitoring)
                {
                    Console.WriteLine($"\n----- 内存监控 #{monitorCount++} [{DateTime.Now.ToString("HH:mm:ss")}] -----");
                    ReportMemoryUsage();

                    // 检测内存泄漏
                    TimeSpan runTime = DateTime.Now - startTime;
                    long currentMemory = GC.GetTotalMemory(false);
                    long memoryChange = currentMemory - initialMemory;
                    Console.WriteLine($"  运行时间: {runTime.TotalMinutes:F1}分钟, 内存变化: {memoryChange / (1024 * 1024)} MB");

                    // 等待5秒
                    Task.Delay(5000).Wait();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生异常: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine("\n测试完成，按任意键退出...");
            Console.ReadKey();
        }

        private static void ReportDetailedMemoryUsage()
        {
            using (var process = Process.GetCurrentProcess())
            {
                process.Refresh();

                // 显示更详细的进程内存信息
                Console.WriteLine($"  进程ID: {process.Id}");
                Console.WriteLine($"  工作集: {process.WorkingSet64 / (1024 * 1024)} MB (物理内存)");
                Console.WriteLine($"  专用内存: {process.PrivateMemorySize64 / (1024 * 1024)} MB");
                Console.WriteLine($"  分页内存: {process.PagedMemorySize64 / (1024 * 1024)} MB");
                Console.WriteLine($"  非分页内存: {process.NonpagedSystemMemorySize64 / (1024 * 1024)} MB");
                Console.WriteLine($"  虚拟内存: {process.VirtualMemorySize64 / (1024 * 1024)} MB");

                // GC详细信息
                Console.WriteLine($"  GC模式: {(GCSettings.IsServerGC ? "服务器" : "工作站")}");
                Console.WriteLine($"  GC延迟模式: {GCSettings.LatencyMode}");

                // 触发一次后台GC并获取内存情况
                Console.WriteLine($"  当前托管内存: {GC.GetTotalMemory(false) / (1024 * 1024)} MB");
                Console.WriteLine($"  GC代数统计: Gen0={GC.CollectionCount(0)}, Gen1={GC.CollectionCount(1)}, Gen2={GC.CollectionCount(2)}");

                // 显示应用程序域信息
                Console.WriteLine($"  应用程序域: {AppDomain.CurrentDomain.FriendlyName}");
                Console.WriteLine($"  加载的程序集数量: {AppDomain.CurrentDomain.GetAssemblies().Length}");
            }
        }

        private static void ReportMemoryUsage()
        {
            // 获取当前进程
            using (var process = Process.GetCurrentProcess())
            {
                // 刷新进程信息
                process.Refresh();

                // 输出内存使用情况
                Console.WriteLine($"  内存使用: 工作集={process.WorkingSet64 / (1024 * 1024)} MB, " +
                                  $"专用内存={process.PrivateMemorySize64 / (1024 * 1024)} MB, " +
                                  $"GC内存={(GC.GetTotalMemory(false) / (1024 * 1024))} MB");
            }
        }

        private static string TestOcr(string imagePath)
        {
            try
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();
                string result = XOcr.Local_Ocr_String(File.ReadAllBytes(imagePath));
                sw.Stop();
                Console.WriteLine($"  OCR处理耗时: {sw.ElapsedMilliseconds} 毫秒");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"OCR处理发生异常: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"内部异常: {ex.InnerException.Message}");
                }
                return string.Empty;
            }
        }

        public void b()
        {
            string normalImagePath = "D:\\1.jpg";
            TestOcrs(normalImagePath, 12).Wait();
        }

        /// <summary>
        /// Tests XOcr.Local_Ocr_String concurrently with multiple threads.
        /// </summary>
        /// <param name="imagePath">The path to the image file for OCR.</param>
        /// <param name="threadCount">The number of concurrent threads to use for testing.</param>
        private static async Task TestOcrs(string imagePath, int threadCount)
        {
            Console.WriteLine($"\n===== 开始多线程OCR测试 ({threadCount} 个线程) =====");
            Console.WriteLine($"测试图片: {imagePath}");

            if (!File.Exists(imagePath))
            {
                Console.WriteLine($"错误: 测试图片 {imagePath} 不存在。");
                return;
            }

            byte[] imageBytes;
            try
            {
                imageBytes = File.ReadAllBytes(imagePath);
                Console.WriteLine($"图片加载成功，大小: {imageBytes.Length / 1024.0:F2} KB");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: 加载图片 {imagePath} 失败: {ex.Message}");
                return;
            }

            var results = new ConcurrentBag<(string result, long duration, int threadId, Exception exception)>();
            var tasks = new List<Task>();

            Stopwatch totalSw = Stopwatch.StartNew();
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 主线程: 开始分派 {threadCount} 个OCR任务...");

            for (int i = 0; i < threadCount; i++)
            {
                int currentThreadId = i + 1; // Closure capture
                tasks.Add(Task.Run(() =>
                {
                    Stopwatch sw = new Stopwatch();
                    string ocrResult = string.Empty;
                    Exception ocrException = null;
                    try
                    {
                        // Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 线程 {currentThreadId}: 开始OCR处理...");
                        sw.Start();
                        ocrResult = XOcr.Local_Ocr_String(imageBytes);
                        sw.Stop();
                        // Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 线程 {currentThreadId}: OCR完成, 耗时: {sw.ElapsedMilliseconds} 毫秒, 结果预览: \"{(ocrResult.Length > 50 ? ocrResult.Substring(0, 50) + "..." : ocrResult)}\"");
                    }
                    catch (Exception ex)
                    {
                        sw.Stop(); // Ensure stopwatch is stopped in case of exception
                        ocrException = ex;
                        Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 线程 {currentThreadId}: OCR处理发生异常: {ex.Message}, 耗时: {sw.ElapsedMilliseconds} 毫秒");
                        if (ex.InnerException != null)
                        {
                            Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 线程 {currentThreadId}: 内部异常: {ex.InnerException.Message}");
                        }
                    }
                    results.Add((ocrResult, sw.ElapsedMilliseconds, currentThreadId, ocrException));
                }));
            }

            await Task.WhenAll(tasks);
            totalSw.Stop();
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 主线程: 所有 {threadCount} 个OCR任务完成。总耗时: {totalSw.ElapsedMilliseconds} 毫秒。");

            Console.WriteLine($"\n===== 多线程OCR测试结果 ({DateTime.Now:HH:mm:ss.fff}) =====");
            int successCount = 0;
            int failureCount = 0;
            long totalDurationSuccessful = 0;
            List<long> durations = new List<long>();

            // Sort results by threadId for ordered output, though ConcurrentBag is unordered.
            foreach (var res in results.OrderBy(r => r.threadId))
            {
                if (res.exception == null)
                {
                    successCount++;
                    totalDurationSuccessful += res.duration;
                    durations.Add(res.duration);
                    Console.WriteLine($"线程 {res.threadId}: 成功, 耗时: {res.duration} 毫秒, 结果预览: \"{(res.result.Length > 50 ? res.result.Substring(0, 50) + "..." : res.result)}\"");
                }
                else
                {
                    failureCount++;
                    Console.WriteLine($"线程 {res.threadId}: 失败, 耗时: {res.duration} 毫秒, 异常: {res.exception.Message}");
                }
            }

            Console.WriteLine($"\n测试总结:");
            Console.WriteLine($"  总线程数: {threadCount}");
            Console.WriteLine($"  成功次数: {successCount}");
            Console.WriteLine($"  失败次数: {failureCount}");

            if (successCount > 0)
            {
                Console.WriteLine($"  成功OCR平均耗时: {totalDurationSuccessful / (double)successCount:F2} 毫秒");
                Console.WriteLine($"  成功OCR最小耗时: {durations.Min()} 毫秒");
                Console.WriteLine($"  成功OCR最大耗时: {durations.Max()} 毫秒");
            }
            Console.WriteLine($"  整体测试总耗时 (包括任务调度): {totalSw.ElapsedMilliseconds} 毫秒");
            Console.WriteLine("=========================================================");
        }
    }
}