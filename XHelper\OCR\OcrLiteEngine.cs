using OcrLiteLib;
using System;
using System.Collections.Generic;
using XHelper.Models;

namespace XHelper.OCR
{
    /// <summary>
    /// OcrLite引擎实现类，用于普通文本识别
    /// </summary>
    internal class OcrLiteEngine : IOcrEngine
    {
        private readonly OcrConfiguration _config;
        private readonly IImageProcessor _imageProcessor;
        private OcrLite? _ocrEngine;
        private bool _initialized = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">OCR配置</param>
        /// <param name="imageProcessor">图像处理器</param>
        public OcrLiteEngine(OcrConfiguration config, IImageProcessor imageProcessor)
        {
            _config = config;
            _imageProcessor = imageProcessor;
        }

        /// <summary>
        /// 初始化OCR引擎
        /// </summary>
        /// <returns>是否初始化成功</returns>
        public bool Initialize()
        {
            // 已初始化则直接返回
            if (_initialized)
                return true;

            try
            {
                // 检查模型文件是否存在
                if (!_config.CheckModelFilesExist("ocrlite"))
                {
                    return false;
                }

                // 初始化OCR引擎
                _ocrEngine = new OcrLite();
                _ocrEngine.InitModels(
                    _config.DetModelPath,
                    _config.ClsModelPath,
                    _config.RecModelPath,
                    _config.KeysPath,
                    4); // 线程数固定为4

                _initialized = true;
                return true;
            }
            catch (Exception ex)
            {
                XLogger.Error($"OcrLite引擎初始化异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从图像路径识别文本
        /// </summary>
        /// <param name="imagePath">图像路径</param>
        /// <param name="callback">可选的回调函数，用于接收文本块信息</param>
        /// <returns>识别结果文本</returns>
        public string RecognizeText(string imagePath, Action<List<XOcr_TextBlock>>? callback = null)
        {
            try
            {
                // 优化图像
                byte[] imageData = _imageProcessor.OptimizeImage(imagePath);
                if (imageData.Length == 0)
                {
                    XLogger.Error("图像优化失败");
                    return string.Empty;
                }

                return RecognizeText(imageData, callback);
            }
            catch (Exception ex)
            {
                XLogger.Error($"从图像路径识别文本异常: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 从图像字节数组识别文本
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <param name="callback">可选的回调函数，用于接收文本块信息</param>
        /// <returns>识别结果文本</returns>
        public string RecognizeText(byte[] imageData, Action<List<XOcr_TextBlock>>? callback = null)
        {
            try
            {
                // 检查输入
                if (imageData == null || imageData.Length == 0)
                {
                    XLogger.Warn("输入图像数据为空");
                    return string.Empty;
                }

                // 初始化OCR引擎
                if (!Initialize())
                {
                    XLogger.Error("OCR引擎初始化失败");
                    return string.Empty;
                }

                // 执行OCR识别
                OcrLiteLib.OcrResult ocrResult = _ocrEngine!.Detect(
                    imageData,
                    _config.Padding,
                    _config.ImgResize,
                    _config.BoxScoreThresh,
                    _config.BoxThresh,
                    _config.UnClipRatio,
                    _config.DoAngle,
                    _config.MostAngle);

                // 执行回调
                if (callback != null && ocrResult?.TextBlocks != null)
                {
                    try
                    {
                        List<XOcr_TextBlock> blocks = new List<XOcr_TextBlock>();
                        foreach (var block in ocrResult.TextBlocks)
                        {
                            if (block != null)
                            {
                                // 计算宽度和高度
                                int w = block.BoxPoints[2].X - block.BoxPoints[0].X;
                                int h = block.BoxPoints[2].Y - block.BoxPoints[0].Y;

                                blocks.Add(new XOcr_TextBlock(block.Text ?? string.Empty,
                                          new System.Drawing.Rectangle(block.BoxPoints[0].X, block.BoxPoints[0].Y, w, h)));
                            }
                        }

                        // 使用图像处理器和OCR配置矫正坐标，同时考虑Padding参数
                        var correctedBlocks = OcrCoordinateCorrector.CorrectOcrLiteCoordinates(blocks, _imageProcessor, _config);
                        callback(correctedBlocks);
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"执行回调时发生异常: {ex.Message}");
                    }
                }

                return ocrResult?.StrRes ?? string.Empty;
            }
            catch (Exception ex)
            {
                XLogger.Error($"文本识别异常: {ex.Message}");
                return string.Empty;
            }
        }
    }
}