﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// TextShareWindow.xaml 的交互逻辑
    /// </summary>
    public partial class TextShareWindow : Window
    {
        private string _shareText;
        private string _shareCode;

        public TextShareWindow(string shareText, string shareCode)
        {
            InitializeComponent();
            _shareText = shareText ?? string.Empty;
            _shareCode = shareCode ?? string.Empty;
        }

        /// <summary>
        /// 窗口加载完成事件处理
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // 设置分享文本到文本框
            ShareTextBox.Text = _shareText;

            // 自动全选文本
            ShareTextBox.Focus();
            ShareTextBox.SelectAll();
        }

        /// <summary>
        /// 关闭按钮点击事件处理
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}