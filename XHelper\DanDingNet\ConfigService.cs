using System.Net.Http.Headers;
using System.Text.Json;
using XHelper.Models;

namespace XHelper.DanDingNet
{
    /// <summary>
    /// 配置共享服务类，提供与配置共享相关的API交互功能
    /// </summary>
    public class ConfigService : BaseService
    {
        public ConfigService(HttpClient client, string macCode, string host, string version)
            : base(client, macCode, host, version)
        {
        }

        /// <summary>
        /// 分享配置
        /// </summary>
        /// <param name="configName">配置名称</param>
        /// <param name="configJson">JSON格式的配置字符串</param>
        /// <param name="version">用户为该配置指定的版本号</param>
        /// <param name="expiresInDays">可选。配置的有效天数。如果提供，将在指定天数后自动过期。如果未提供或小于等于0，则表示永不过期。</param>
        /// <returns>分享配置结果，包含生成的共享码</returns>
        public async Task<Response_ShareConfigData?> ShareConfigAsync(string configName, string configJson, string version, int? expiresInDays = null)
        {
            if (CheckObjisNull(_client)) return null;

            // 验证参数
            if (string.IsNullOrEmpty(configName) || string.IsNullOrEmpty(configJson) || string.IsNullOrEmpty(version))
            {
                XLogger.Error("分享配置失败：配置名称、配置内容或版本号不能为空");
                return null;
            }

            // 验证JSON格式
            try
            {
                JsonSerializer.Deserialize<object>(configJson);
            }
            catch (JsonException)
            {
                XLogger.Error("分享配置失败：配置内容不是有效的JSON格式");
                return null;
            }

            // 构造请求数据
            var data = new
            {
                config_name = configName,
                config_json = configJson,
                version,
                expires_in_days = expiresInDays
            };

            var message = GetBaseRequest(HttpMethod.Post, _host + "configs/share");
            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                Response_ShareConfigData? obj = JsonSerializer.Deserialize<Response_ShareConfigData>(body, _jsonOptions);

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"[ShareConfigAsync] 分享结果：{obj?.IsSuccess}|{obj?.Message}");
                return obj;
            }
            catch (Exception ex)
            {
                XLogger.Error($"分享配置异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 通过共享码获取配置
        /// </summary>
        /// <param name="shareCode">共享码</param>
        /// <returns>配置内容及相关元数据</returns>
        public async Task<Response_GetConfigData?> GetConfigAsync(string shareCode)
        {
            if (CheckObjisNull(_client)) return null;

            // 验证参数
            if (string.IsNullOrEmpty(shareCode))
            {
                XLogger.Error("获取配置失败：共享码不能为空");
                return null;
            }

            var message = GetBaseRequest(HttpMethod.Get, _host + $"configs/{shareCode}");

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                Response_GetConfigData? obj = JsonSerializer.Deserialize<Response_GetConfigData>(body, _jsonOptions);

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"[GetConfigAsync] 获取结果：{obj?.IsSuccess}|{obj?.Message}");
                return obj;
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取配置异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取当前用户分享的配置列表
        /// </summary>
        /// <param name="page">页码，从1开始</param>
        /// <param name="perPage">每页记录数，默认为10，最大为50</param>
        /// <param name="status">按状态筛选 (active, expired, deleted)</param>
        /// <param name="sortBy">排序字段 (create_time, expires_at)</param>
        /// <param name="order">排序方向 (asc, desc)</param>
        /// <returns>用户分享的配置列表</returns>
        public async Task<Response_MySharesData?> GetMySharesAsync(int page = 1, int perPage = 10, string? status = null, string sortBy = "create_time", string order = "desc")
        {
            if (CheckObjisNull(_client)) return null;

            // 构建URL参数
            var queryParams = new List<string>
            {
                $"page={page}",
                $"per_page={Math.Min(perPage, 50)}" // 限制每页最大记录数为50
            };

            if (!string.IsNullOrEmpty(status))
            {
                queryParams.Add($"status={status}");
            }

            queryParams.Add($"sort_by={sortBy}");
            queryParams.Add($"order={order}");

            string queryString = string.Join("&", queryParams);

            var message = GetBaseRequest(HttpMethod.Get, _host + $"configs/my-shares?{queryString}");

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                Response_MySharesData? obj = JsonSerializer.Deserialize<Response_MySharesData>(body, _jsonOptions);

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"[GetMySharesAsync] 获取结果：{obj?.IsSuccess}|{obj?.Message}");
                return obj;
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取我的分享列表异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 更新我分享的配置状态
        /// </summary>
        /// <param name="shareCode">共享码</param>
        /// <param name="action">操作类型 (delete/expire_now)</param>
        /// <returns>更新状态结果</returns>
        public async Task<ResponseBaseData?> UpdateShareStatusAsync(string shareCode, string action)
        {
            if (CheckObjisNull(_client)) return null;

            // 验证参数
            if (string.IsNullOrEmpty(shareCode) || string.IsNullOrEmpty(action))
            {
                XLogger.Error("更新配置状态失败：共享码或操作类型不能为空");
                return null;
            }

            if (action != "delete" && action != "expire_now")
            {
                XLogger.Error("更新配置状态失败：操作类型无效，必须为 delete 或 expire_now");
                return null;
            }

            var message = GetBaseRequest(HttpMethod.Patch, _host + $"configs/my-shares/{shareCode}?action={action}");

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                ResponseBaseData? obj = JsonSerializer.Deserialize<ResponseBaseData>(body, _jsonOptions);

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"[UpdateShareStatusAsync] 更新结果：{obj?.IsSuccess}|{obj?.Message}");
                return obj;
            }
            catch (Exception ex)
            {
                XLogger.Error($"更新配置状态异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 浏览所有活跃的共享配置
        /// </summary>
        /// <param name="page">页码，从1开始</param>
        /// <param name="perPage">每页记录数，默认为10，最大为50</param>
        /// <param name="searchTerm">搜索关键词 (匹配版本号)</param>
        /// <returns>活跃的共享配置列表</returns>
        public async Task<Response_BrowseConfigsData?> BrowseConfigsAsync(int page = 1, int perPage = 10, string? searchTerm = null)
        {
            if (CheckObjisNull(_client)) return null;

            // 构建URL参数
            var queryParams = new List<string>
            {
                $"page={page}",
                $"per_page={Math.Min(perPage, 50)}" // 限制每页最大记录数为50
            };

            if (!string.IsNullOrEmpty(searchTerm))
            {
                queryParams.Add($"search_term={Uri.EscapeDataString(searchTerm)}");
            }

            string queryString = string.Join("&", queryParams);

            var message = GetBaseRequest(HttpMethod.Get, _host + $"configs/browse?{queryString}");

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                Response_BrowseConfigsData? obj = JsonSerializer.Deserialize<Response_BrowseConfigsData>(body, _jsonOptions);

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"[BrowseConfigsAsync] 浏览结果：{obj?.IsSuccess}|{obj?.Message}");
                return obj;
            }
            catch (Exception ex)
            {
                XLogger.Error($"浏览配置异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取配置分享统计数据
        /// </summary>
        /// <returns>配置分享的整体及用户个人统计</returns>
        public async Task<Response_ConfigStatsData?> GetConfigStatsAsync()
        {
            if (CheckObjisNull(_client)) return null;

            var message = GetBaseRequest(HttpMethod.Get, _host + "configs/stats");

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                Response_ConfigStatsData? obj = JsonSerializer.Deserialize<Response_ConfigStatsData>(body, _jsonOptions);

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"[GetConfigStatsAsync] 获取结果：{obj?.IsSuccess}|{obj?.Message}");
                return obj;
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取配置统计数据异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取热门配置列表
        /// </summary>
        /// <param name="page">页码，从1开始</param>
        /// <param name="perPage">每页记录数，默认为10，最大为50</param>
        /// <returns>热门配置列表</returns>
        public async Task<Response_PopularConfigsData?> GetPopularConfigsAsync(int page = 1, int perPage = 10)
        {
            if (CheckObjisNull(_client)) return null;

            string queryString = $"page={page}&per_page={Math.Min(perPage, 50)}";
            var message = GetBaseRequest(HttpMethod.Get, _host + $"configs/popular?{queryString}");

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                Response_PopularConfigsData? obj = JsonSerializer.Deserialize<Response_PopularConfigsData>(body, _jsonOptions);

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"[GetPopularConfigsAsync] 获取结果：{obj?.IsSuccess}|{obj?.Message}");
                return obj;
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取热门配置异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取配置统计历史数据
        /// </summary>
        /// <param name="days">查询天数，默认为7，最大为30</param>
        /// <returns>最近N天的每日配置分享和获取次数</returns>
        public async Task<Response_ConfigStatsHistoryData?> GetConfigStatsHistoryAsync(int days = 7)
        {
            if (CheckObjisNull(_client)) return null;

            // 限制最大查询天数为30天
            int queryDays = Math.Min(days, 30);

            var message = GetBaseRequest(HttpMethod.Get, _host + $"configs/stats/history?days={queryDays}");

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                Response_ConfigStatsHistoryData? obj = JsonSerializer.Deserialize<Response_ConfigStatsHistoryData>(body, _jsonOptions);

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"[GetConfigStatsHistoryAsync] 获取结果：{obj?.IsSuccess}|{obj?.Message}");
                return obj;
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取配置统计历史数据异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取配置接口性能数据
        /// </summary>
        /// <returns>配置API的性能统计</returns>
        public async Task<Response_ConfigPerformanceData?> GetConfigPerformanceAsync()
        {
            if (CheckObjisNull(_client)) return null;

            var message = GetBaseRequest(HttpMethod.Get, _host + "configs/stats/performance");

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                Response_ConfigPerformanceData? obj = JsonSerializer.Deserialize<Response_ConfigPerformanceData>(body, _jsonOptions);

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"[GetConfigPerformanceAsync] 获取结果：{obj?.IsSuccess}|{obj?.Message}");
                return obj;
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取配置性能数据异常: {ex.Message}");
                return null;
            }
        }
    }
}