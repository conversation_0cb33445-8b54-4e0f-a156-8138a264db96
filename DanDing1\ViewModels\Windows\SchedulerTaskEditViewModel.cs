using CommunityToolkit.Mvvm.ComponentModel;
using DanDing1.Helpers.Extensions;
using DanDing1.Models;
using ScriptEngine.Model;
using System;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Cronos;
using System.Windows;

namespace DanDing1.ViewModels.Windows
{
    /// <summary>
    /// 调度任务编辑视图模型
    /// </summary>
    public partial class SchedulerTaskEditViewModel : ObservableObject
    {
        #region 属性

        /// <summary>
        /// 可用模拟器列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<EmulatorItem> _availableEmulators = new();

        /// <summary>
        /// 选中的模拟器
        /// </summary>
        [ObservableProperty]
        private EmulatorItem _selectedEmulator;

        /// <summary>
        /// 调度类型列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _scheduleTypes = new ObservableCollection<string>
        {
            "一次性", "每日", "每周", "每月", "间隔执行", "循环执行"
        };

        /// <summary>
        /// 选中的调度类型
        /// </summary>
        [ObservableProperty]
        private string _selectedScheduleType = "一次性";

        /// <summary>
        /// 一次性任务日期
        /// </summary>
        [ObservableProperty]
        private DateTime _oneTimeDate = DateTime.Today;

        /// <summary>
        /// 小时选项
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _hourOptions = new ObservableCollection<string>(
            Enumerable.Range(0, 24).Select(h => h.ToString("00")));

        /// <summary>
        /// 分钟选项
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _minuteOptions = new ObservableCollection<string>(
            Enumerable.Range(0, 60).Select(m => m.ToString("00")));

        /// <summary>
        /// 星期选项
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _weekdayOptions = new ObservableCollection<string>
        {
            "星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"
        };

        /// <summary>
        /// 日期选项（1-31）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _dayOptions = new ObservableCollection<string>(
            Enumerable.Range(1, 31).Select(d => d.ToString()));

        /// <summary>
        /// 选中的小时
        /// </summary>
        [ObservableProperty]
        private string _selectedHour = DateTime.Now.Hour.ToString("00");

        /// <summary>
        /// 选中的分钟
        /// </summary>
        [ObservableProperty]
        private string _selectedMinute = DateTime.Now.Minute.ToString("00");

        /// <summary>
        /// 选中的星期
        /// </summary>
        [ObservableProperty]
        private string _selectedWeekday = "星期一";

        /// <summary>
        /// 选中的日期
        /// </summary>
        [ObservableProperty]
        private string _selectedDay = "1";

        /// <summary>
        /// 是否为编辑模式
        /// </summary>
        [ObservableProperty]
        private bool _isEditMode = false;

        /// <summary>
        /// 任务名称
        /// </summary>
        [ObservableProperty]
        private string _taskName = "";

        /// <summary>
        /// 时间间隔（用于间隔执行）
        /// </summary>
        [ObservableProperty]
        private int _intervalValue = 60;

        /// <summary>
        /// 间隔单位列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _intervalUnits = new ObservableCollection<string>
        {
            "分钟", "小时", "天"
        };

        /// <summary>
        /// 选中的间隔单位
        /// </summary>
        [ObservableProperty]
        private string _selectedIntervalUnit = "分钟";

        /// <summary>
        /// Cron表达式（用于循环执行）
        /// </summary>
        [ObservableProperty]
        private string _cronExpression = "0 * * * *"; // 默认每小时执行一次

        /// <summary>
        /// 下次执行时间预览
        /// </summary>
        [ObservableProperty]
        private string _nextExecutionPreview = "";

        /// <summary>
        /// 常用Cron表达式列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<CronTemplate> _cronTemplates = new ObservableCollection<CronTemplate>
        {
            new CronTemplate { Name = "每分钟", Expression = "* * * * *" },
            new CronTemplate { Name = "每小时", Expression = "0 * * * *" },
            new CronTemplate { Name = "每天午夜", Expression = "0 0 * * *" },
            new CronTemplate { Name = "每天中午", Expression = "0 12 * * *" },
            new CronTemplate { Name = "每周一上午8点", Expression = "0 8 * * 1" },
            new CronTemplate { Name = "每月1号0点", Expression = "0 0 1 * *" },
            new CronTemplate { Name = "工作日8点-18点每小时", Expression = "0 8-18 * * 1-5" },
            new CronTemplate { Name = "每30分钟", Expression = "*/30 * * * *" }
        };

        /// <summary>
        /// 选中的Cron模板
        /// </summary>
        [ObservableProperty]
        private CronTemplate _selectedCronTemplate;

        #endregion

        #region 计算属性

        /// <summary>
        /// 是否为一次性任务
        /// </summary>
        public bool IsOneTime => SelectedScheduleType == "一次性";

        /// <summary>
        /// 是否为每日任务
        /// </summary>
        public bool IsDaily => SelectedScheduleType == "每日";

        /// <summary>
        /// 是否为每周任务
        /// </summary>
        public bool IsWeekly => SelectedScheduleType == "每周";

        /// <summary>
        /// 是否为每月任务
        /// </summary>
        public bool IsMonthly => SelectedScheduleType == "每月";

        /// <summary>
        /// 是否为间隔执行任务
        /// </summary>
        public bool IsInterval => SelectedScheduleType == "间隔执行";

        /// <summary>
        /// 是否为循环执行任务
        /// </summary>
        public bool IsCron => SelectedScheduleType == "循环执行";

        #endregion

        #region 属性变更通知

        partial void OnSelectedScheduleTypeChanged(string value)
        {
            // 触发计算属性的更新
            OnPropertyChanged(nameof(IsOneTime));
            OnPropertyChanged(nameof(IsDaily));
            OnPropertyChanged(nameof(IsWeekly));
            OnPropertyChanged(nameof(IsMonthly));
            OnPropertyChanged(nameof(IsInterval));
            OnPropertyChanged(nameof(IsCron));

            // 如果是循环执行类型，更新预览
            if (IsCron)
                UpdateCronPreview();
            else if (IsInterval)
                UpdateIntervalPreview();
        }

        partial void OnCronExpressionChanged(string value)
        {
            // 当Cron表达式变更时，更新预览
            if (IsCron)
                UpdateCronPreview();
        }

        partial void OnSelectedCronTemplateChanged(CronTemplate value)
        {
            // 当选择了预设模板时，应用该模板的表达式
            if (value != null)
            {
                CronExpression = value.Expression;
            }
        }

        partial void OnIntervalValueChanged(int value)
        {
            // 当间隔值变更时，更新预览
            if (IsInterval)
                UpdateIntervalPreview();
        }

        partial void OnSelectedIntervalUnitChanged(string value)
        {
            // 当间隔单位变更时，更新预览
            if (IsInterval)
                UpdateIntervalPreview();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 获取调度任务
        /// </summary>
        /// <param name="taskType">任务类型</param>
        /// <param name="taskLiasts">任务参数</param>
        /// <returns>调度任务对象</returns>
        public ScheduledTask GetScheduledTask(string taskType, string taskLiasts)
        {
            // 创建调度任务
            var task = new ScheduledTask
            {
                // 编辑模式下不生成新ID，由调用方保持原ID
                Id = Guid.NewGuid().ToString().Substring(0, 5),
                // 使用编辑窗口中的任务名称或传入的任务类型
                Name = IsEditMode ? TaskName : taskType,
                EmulatorName = SelectedEmulator.Name,
                ScheduleType = SelectedScheduleType,
                Status = "待执行",
                Enabled = true,
                TaskParameters = taskLiasts // 存储任务参数
            };

            // 设置特定调度类型所需的属性
            if (IsInterval)
            {
                task.IntervalMinutes = GetTotalIntervalMinutes();
            }
            else if (IsCron)
            {
                task.CronExpression = CronExpression;
            }

            // 设置下次执行时间
            task.NextExecutionTime = GetNextExecutionTime();

            return task;
        }

        /// <summary>
        /// 验证任务
        /// </summary>
        /// <returns>验证结果</returns>
        public bool ValidateTask()
        {
            // 验证是否选中模拟器
            if (SelectedEmulator == null)
                return false;

            // 一次性任务验证
            if (IsOneTime)
            {
                var scheduledTime = OneTimeDate.Add(TimeSpan.Parse($"{SelectedHour}:{SelectedMinute}"));
                if (scheduledTime <= DateTime.Now)
                    return false;
            }

            // 间隔执行验证
            if (IsInterval && IntervalValue <= 0)
                return false;

            // 循环执行验证
            if (IsCron)
            {
                try
                {
                    // 验证Cron表达式格式
                    var expression = Cronos.CronExpression.Parse(CronExpression);
                    var next = expression.GetNextOccurrence(DateTime.UtcNow, TimeZoneInfo.Local);
                    if (next == null)
                        return false;
                }
                catch
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 加载现有任务信息进行编辑
        /// </summary>
        /// <param name="task">要编辑的任务</param>
        public void LoadExistingTask(ScheduledTask task)
        {
            // 设置为编辑模式
            IsEditMode = true;

            // 设置任务基本信息
            TaskName = task.Name;

            // 设置选中的模拟器
            SelectedEmulator = AvailableEmulators.FirstOrDefault(e => e.Name == task.EmulatorName);

            // 设置调度类型
            SelectedScheduleType = task.ScheduleType;

            // 根据调度类型设置特定属性
            if (task.ScheduleType == "间隔执行")
            {
                // 设置间隔执行所需属性
                SetIntervalFromMinutes(task.IntervalMinutes);
            }
            else if (task.ScheduleType == "循环执行")
            {
                // 设置循环执行所需属性
                CronExpression = task.CronExpression;
                UpdateCronPreview();
            }
            else
            {
                // 根据下次执行时间解析时间设置
                if (DateTime.TryParse(task.NextExecutionTime, out DateTime nextExecTime))
                {
                    SelectedHour = nextExecTime.Hour.ToString("00");
                    SelectedMinute = nextExecTime.Minute.ToString("00");

                    // 根据调度类型设置对应的时间属性
                    switch (task.ScheduleType)
                    {
                        case "一次性":
                            OneTimeDate = nextExecTime.Date;
                            break;
                        case "每周":
                            // 转换星期几为中文表示
                            int dayOfWeek = (int)nextExecTime.DayOfWeek;
                            if (dayOfWeek == 0) dayOfWeek = 7; // 周日
                            SelectedWeekday = WeekdayOptions[dayOfWeek - 1]; // 数组从0开始，所以减1
                            break;
                        case "每月":
                            SelectedDay = nextExecTime.Day.ToString();
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// 更新Cron表达式预览
        /// </summary>
        public void UpdateCronPreview()
        {
            try
            {
                // 处理输入的Cron表达式
                string normalizedCron = CronExpression;
                string[] parts = CronExpression.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 6)
                {
                    // 如果是6段格式，移除秒字段
                    normalizedCron = string.Join(" ", parts.Skip(1));
                }
                else if (parts.Length != 5)
                {
                    throw new Exception($"无效的Cron格式：需要5个字段 (分 时 日 月 周)，但提供了{parts.Length}个字段");
                }

                var expression = Cronos.CronExpression.Parse(normalizedCron);
                var nextOccurrences = GetNextOccurrences(expression, 3);

                if (nextOccurrences.Count > 0)
                {
                    NextExecutionPreview = string.Join("\n", nextOccurrences.Select(d => d.ToString("yyyy-MM-dd HH:mm:ss")));
                }
                else
                {
                    NextExecutionPreview = "无法计算下次执行时间";
                }
            }
            catch (Exception ex)
            {
                NextExecutionPreview = $"Cron表达式无效: {ex.Message}";
            }
        }

        /// <summary>
        /// 更新间隔预览
        /// </summary>
        public void UpdateIntervalPreview()
        {
            try
            {
                int totalMinutes = GetTotalIntervalMinutes();
                DateTime nextTime = DateTime.Now.AddMinutes(totalMinutes);

                NextExecutionPreview = $"下次执行时间: {nextTime.ToString("yyyy-MM-dd HH:mm:ss")}";
            }
            catch (Exception)
            {
                NextExecutionPreview = "无法计算下次执行时间";
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取下次执行时间
        /// </summary>
        /// <returns>下次执行时间字符串</returns>
        private string GetNextExecutionTime()
        {
            DateTime nextTime;

            switch (SelectedScheduleType)
            {
                case "一次性":
                    nextTime = OneTimeDate.Add(TimeSpan.Parse($"{SelectedHour}:{SelectedMinute}"));
                    break;

                case "每日":
                    var dailyTime = TimeSpan.Parse($"{SelectedHour}:{SelectedMinute}");
                    nextTime = DateTime.Today.Add(dailyTime);
                    if (nextTime < DateTime.Now)
                        nextTime = nextTime.AddDays(1);
                    break;

                case "每周":
                    int targetDayOfWeek = GetDayOfWeekFromString(SelectedWeekday);
                    int currentDayOfWeek = (int)DateTime.Now.DayOfWeek;

                    // 转换为星期一为1，星期日为7的格式
                    if (currentDayOfWeek == 0) currentDayOfWeek = 7;
                    if (targetDayOfWeek == 0) targetDayOfWeek = 7;

                    int daysToAdd = (targetDayOfWeek - currentDayOfWeek + 7) % 7;
                    if (daysToAdd == 0) // 同一天，检查时间
                    {
                        var weeklyTime = TimeSpan.Parse($"{SelectedHour}:{SelectedMinute}");
                        if (DateTime.Now.TimeOfDay > weeklyTime)
                            daysToAdd = 7; // 如果今天的时间已过，则等到下周
                    }

                    nextTime = DateTime.Today.AddDays(daysToAdd).Add(TimeSpan.Parse($"{SelectedHour}:{SelectedMinute}"));
                    break;

                case "每月":
                    int day = int.Parse(SelectedDay);
                    var now = DateTime.Now;
                    var monthlyTime = TimeSpan.Parse($"{SelectedHour}:{SelectedMinute}");

                    // 尝试当前月份
                    if (day >= now.Day && (day > now.Day || now.TimeOfDay < monthlyTime))
                    {
                        // 如果选择的日期大于当前日期，或者是当天但时间未到
                        nextTime = new DateTime(now.Year, now.Month, day).Add(monthlyTime);
                    }
                    else
                    {
                        // 否则等到下个月
                        var nextMonth = now.AddMonths(1);
                        // 确保日期有效（处理2月等问题）
                        int maxDaysInMonth = DateTime.DaysInMonth(nextMonth.Year, nextMonth.Month);
                        day = Math.Min(day, maxDaysInMonth);
                        nextTime = new DateTime(nextMonth.Year, nextMonth.Month, day).Add(monthlyTime);
                    }
                    break;

                case "间隔执行":
                    // 计算下次执行时间 = 当前时间 + 间隔时间
                    int totalMinutes = GetTotalIntervalMinutes();
                    nextTime = DateTime.Now.AddMinutes(totalMinutes);
                    break;

                case "循环执行":
                    try
                    {
                        // 使用cron表达式计算下次执行时间
                        string normalizedCron = CronExpression;
                        string[] parts = CronExpression.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length == 6)
                        {
                            // 如果是6段格式，移除秒字段
                            normalizedCron = string.Join(" ", parts.Skip(1));
                        }
                        else if (parts.Length != 5)
                        {
                            throw new Exception($"无效的Cron格式：需要5个字段");
                        }

                        var expression = Cronos.CronExpression.Parse(normalizedCron);
                        var next = expression.GetNextOccurrence(DateTime.UtcNow, TimeZoneInfo.Local);
                        nextTime = next?.ToLocalTime() ?? DateTime.Now.AddHours(1);
                    }
                    catch (Exception ex)
                    {
                        // 如果cron表达式无效，默认一小时后执行
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            MessageBox.Show($"Cron表达式格式无效: {ex.Message}\n系统将使用默认时间：一小时后执行",
                                "表达式错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                        });
                        nextTime = DateTime.Now.AddHours(1);
                    }
                    break;

                default:
                    nextTime = DateTime.Now;
                    break;
            }

            return nextTime.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 从星期字符串获取对应的星期几数值
        /// </summary>
        /// <param name="weekday">星期字符串</param>
        /// <returns>星期几数值（1-7）</returns>
        private int GetDayOfWeekFromString(string weekday)
        {
            switch (weekday)
            {
                case "星期一": return 1;
                case "星期二": return 2;
                case "星期三": return 3;
                case "星期四": return 4;
                case "星期五": return 5;
                case "星期六": return 6;
                case "星期日": return 7;
                default: return 1;
            }
        }

        /// <summary>
        /// 获取总间隔分钟数
        /// </summary>
        /// <returns>分钟数</returns>
        private int GetTotalIntervalMinutes()
        {
            int factor = 1;
            switch (SelectedIntervalUnit)
            {
                case "小时":
                    factor = 60;
                    break;
                case "天":
                    factor = 60 * 24;
                    break;
            }
            return IntervalValue * factor;
        }

        /// <summary>
        /// 从分钟数设置间隔值和单位
        /// </summary>
        /// <param name="totalMinutes">总分钟数</param>
        private void SetIntervalFromMinutes(int totalMinutes)
        {
            if (totalMinutes % (24 * 60) == 0)
            {
                IntervalValue = totalMinutes / (24 * 60);
                SelectedIntervalUnit = "天";
            }
            else if (totalMinutes % 60 == 0)
            {
                IntervalValue = totalMinutes / 60;
                SelectedIntervalUnit = "小时";
            }
            else
            {
                IntervalValue = totalMinutes;
                SelectedIntervalUnit = "分钟";
            }
        }

        /// <summary>
        /// 获取下几次执行时间
        /// </summary>
        /// <param name="expression">Cron表达式</param>
        /// <param name="count">次数</param>
        /// <returns>执行时间列表</returns>
        private List<DateTime> GetNextOccurrences(Cronos.CronExpression expression, int count)
        {
            var occurrences = new List<DateTime>();
            DateTime? currentTime = DateTime.UtcNow;

            for (int i = 0; i < count; i++)
            {
                currentTime = expression.GetNextOccurrence(currentTime.Value, TimeZoneInfo.Local);
                if (currentTime.HasValue)
                {
                    occurrences.Add(currentTime.Value.ToLocalTime());
                    currentTime = currentTime.Value.AddSeconds(1); // 加1秒避免获取相同时间
                }
                else
                    break;
            }

            return occurrences;
        }

        #endregion
    }

    /// <summary>
    /// Cron表达式模板
    /// </summary>
    public class CronTemplate
    {
        public string Name { get; set; }
        public string Expression { get; set; }
    }
}