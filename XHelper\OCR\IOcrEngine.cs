using System;
using System.Collections.Generic;
using XHelper.Models;

namespace XHelper.OCR
{
    /// <summary>
    /// OCR引擎接口
    /// </summary>
    internal interface IOcrEngine
    {
        /// <summary>
        /// 初始化OCR引擎
        /// </summary>
        /// <returns>是否初始化成功</returns>
        bool Initialize();

        /// <summary>
        /// 从图像字节数组识别文本
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <param name="callback">可选的回调函数，用于接收文本块信息</param>
        /// <returns>识别结果文本</returns>
        string RecognizeText(byte[] imageData, Action<List<XOcr_TextBlock>>? callback = null);

        /// <summary>
        /// 从图像路径识别文本
        /// </summary>
        /// <param name="imagePath">图像路径</param>
        /// <param name="callback">可选的回调函数，用于接收文本块信息</param>
        /// <returns>识别结果文本</returns>
        string RecognizeText(string imagePath, Action<List<XOcr_TextBlock>>? callback = null);
    }
}