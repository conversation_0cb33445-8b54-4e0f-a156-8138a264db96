﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Tasks
{
    internal class YeYuanHuo : BaseTask
    {
        private bool Biaoji = false;//是否开启标记功能
        private int Ncount = 0;//任务次数

        private bool BiaoJi_Status = false;

        private List<string> DontSendLog = ["标记"];

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "业原火");
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Ncount = GetConfig.Count;
            GetConfig.Others.TryGetValue("Biaoji", out string? s);
            try { Biaoji = s is null ? false : bool.Parse(s); } catch (Exception) { Biaoji = false; }

            log.Info("业原火任务开始，当前任务次数：" + Ncount + "，是否开启标记功能：" + Biaoji);
            //场景判断
            if (!Scene.NowIsScene("业原火"))
            {
                //先去探索，获取突破卷数量
                if (!Scene.TO.TanSuo())
                {
                    log.Warn("业原火任务无法继续，当前游戏所在场景未知，请调整到庭院或探索主界面开始脚本！");
                    return;
                }
                var tupo_Count = Fast.Scence.TanSuo_GetTuPoCount();
                log.Debug("本地Ocr识别突破卷结果：" + tupo_Count);
                if (tupo_Count == 30)
                    Tmp.Do_Tupo(); //执行临时执行突破

                //再去任务场景
                Scene.TO.YeYuanHuo();
            }
            main();
            UserNotificationMessage = $"共战斗{count}/{Ncount}次.";
        }

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            //点击开始
            Fast.Click("1115,600,1199,676");
            log.Info("战斗点击开始");
            var pics = Mp.Filter("业原火");
            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                FindOkFun(p.Name, p);
                if (!DontSendLog.Any(p.Name.Contains)) log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("胜利") || p.Name.Contains("达摩"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败"))
                {
                    ret_bol = false;
                    isbreak = true;
                }
            }
            if (ret_bol)
                Combat_End();//等待Yuhun界面

            return ret_bol;
        }

        /// <summary>
        /// 觉醒胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("业原火");
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                if (p.Name.Contains("挑战"))
                {
                    isbreak = true;
                    continue;
                }
                log.Info($"执行点击：{p._Name}");
                p.Click();
            }
        }

        /// <summary>
        /// 结束任务方法
        /// </summary>
        private void EndCallBack()
        {
            log.Info("执行业原火任务收尾方法,等待返回后,退出到探索。");
            var mps = new MemPics()
                .Add(Mp.Filter("业原火.准备"))
                .Add(Mp.Filter("业原火.达摩"));

            while (!Mp.Filter("业原火.挑战").FindAll())
            {
                mps.FindAllAndClick();
                Sleep(1000);
            }
            log.Info("退出到探索..");
            Sleep(500);
            Fast.Click("32,22,76,62");
            Sleep(1200);
        }

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            if (Biaoji && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.2秒，标记位置：5号位");
                Sleep(200);
                Fast.Click("1070,479,1106,540");
                return false;
            }
            return true;
        }

        private int count = 0;

        private void main()
        {
            if (UserConfig_Preset != null)
            {
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入式神录，开始应用预设{UserConfig_Preset}");
                Fast.Click("973,651,1001,681");
                Sleep(1500);
                Tmp.Do_Preset(preset);
            }

            int clickcount = 0;
        Re:
            while (count < Ncount)
            {
                if (!WaitYeYuanHuo())
                {
                    clickcount++;
                    if (clickcount > 5)
                    {
                        Fast.Click(449, 664, 627, 698);
                        Sleep(200);
                        clickcount = 0;
                    }
                    goto Re;
                }

                Anti.RandomDelay();//防封等待
                if (Anti.ShouldTriggerRandomYysAuto())//判断是否需要穿插纸人
                {
                    Fast.Click(825, 562, 859, 595); //打开小纸人
                    Sleep(500);
                    int do_Count = Random.Shared.Next(1, 5); // 1-4次随机次数
                    if (Tmp.Do_YysAuto(do_Count))
                    {
                        count += do_Count;
                        log.Info($"触发随机穿插纸人战斗结束..脚本继续接管..");
                        Anti.ResetRandomYysAuto();
                        goto Re;
                    }
                    else Sleep(1000);
                }
                if (Db.PendingTimerTask) //执行定时任务
                {
                    Db.PendingTimerTask = false;
                    log.Info("暂停当前任务，执行定时任务，退出到探索..");
                    EndCallBack();
                    Db?.TimerTask?.DoAllTask();
                    Sleep(1000);
                    throw new Exception("定时任务执行结束，重新执行当前的主任务..");
                }
                Tmp.Do_ClearYuHun(); //执行临时执行御魂

                if (Combat())
                {
                    count++;
                    log.Info($"业原火战斗胜利，战斗次数：{count}/{Ncount}");
                }
                else
                {
                    log.Warn($"业原火战斗失败，请检查您的队伍配置是否正常！战斗次数：{count}/{Ncount}");
                    Defeated();
                }
            }
            EndCallBack();
        }

        /// <summary>
        /// 等待觉醒开始界面
        /// </summary>
        /// <returns></returns>
        private bool WaitYeYuanHuo()
        {
            //string nows = Scene.NowScene;
            //if (nows == "觉醒")
            //{
            var pics = Mp.Filter("挑战");
            if (pics.Wait()) return true;
            //}
            return false;
        }
    }
}