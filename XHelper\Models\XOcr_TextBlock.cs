﻿using OcrLiteLib;
using System.Drawing;

namespace XHelper.Models
{
    public class XOcr_TextBlock
    {
        public List<Point> BoxPoints { get; set; }
        public string Text { get; set; }
        public Rectangle Rect { get; private set; }

        public Point Center
        {
            get
            {
                int centerX = (int)(Rect.X + Rect.Width / 2.0);
                int centerY = (int)(Rect.Y + Rect.Height / 2.0);
                return new Point(centerX, centerY);
            }
        }

        public XOcr_TextBlock(TextBlock textBlock)
        {
            BoxPoints = textBlock.BoxPoints;
            Text = textBlock.Text;

            // 计算矩形
            var points = BoxPoints;
            int minX = points.Min(p => p.X);
            int maxX = points.Max(p => p.X);
            int minY = points.Min(p => p.Y);
            int maxY = points.Max(p => p.Y);

            Rect = new Rectangle(minX, minY, maxX - minX, maxY - minY);
        }

        public XOcr_TextBlock(string text, Rectangle rect)
        {
            Text = text;
            Rect = rect;

            // 生成四个坐标点（假设矩形四个顶点为：左上角、右上角、右下角、左下角）
            BoxPoints = new List<Point>
            {
                // 左上角
                new Point(rect.X, rect.Y),
                // 右上角
                new Point(rect.X + rect.Width, rect.Y),
                // 右下角
                new Point(rect.X + rect.Width, rect.Y + rect.Height),
                // 左下角
                new Point(rect.X, rect.Y + rect.Height)
            };
        }

        /// <summary>
        /// 设置基准原点坐标，重新计算所有坐标点
        /// </summary>
        /// <param name="x">X坐标偏移量</param>
        /// <param name="y">Y坐标偏移量</param>
        public void SetBasePoint(int x, int y)
        {
            // 更新矩形位置
            Rect = new Rectangle(Rect.X + x, Rect.Y + y, Rect.Width, Rect.Height);

            // 更新BoxPoints中的每个点坐标
            for (int i = 0; i < BoxPoints.Count; i++)
            {
                BoxPoints[i] = new Point(BoxPoints[i].X + x, BoxPoints[i].Y + y);
            }

            // Center属性是基于Rect计算的，会自动更新，不需要单独处理
        }

        public override string ToString()
        {
            return $"Text: {Text}, Center: ({Center.X}, {Center.Y})";
        }
    }
}