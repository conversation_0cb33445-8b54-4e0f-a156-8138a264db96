﻿<Window
    x:Class="DanDing1.Views.Windows.UpdateWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:DanDing1.Views.Windows"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="软件更新"
    Width="763"
    Height="519"
    MinWidth="763"
    MinHeight="519"
    ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
    ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    FontFamily="微软雅黑"
    Background="{DynamicResource ApplicationBackgroundBrush}"
    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    ScrollViewer.CanContentScroll="False"
    mc:Ignorable="d">
    <Grid Height="467"
          Margin="20,0">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="auto"/>
            <ColumnDefinition/>
        </Grid.ColumnDefinitions>
        <ui:ImageIcon
            Grid.Column="0"
            Width="80"
            Height="80"
            Margin="0,25,15,0"
            VerticalAlignment="Top"
            Source="pack://application:,,,/Assets/egg-icon-256.png"/>
        <StackPanel Grid.Column="1"
                    Margin="0,20,5,5">
            <TextBlock
                FontSize="24"
                FontWeight="Bold"
                Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                Text="新版本的蛋定助手已经发布"/>
            <StackPanel Margin="0,12,0,0"
                        Orientation="Horizontal">
                <TextBlock x:Name="NewVer"
                           FontSize="14"
                           Text="Ver "/>
                <TextBlock FontSize="14"
                           Text=" 版本可更新，您现在的版本是 "/>
                <TextBlock x:Name="NowVer"
                           FontSize="14"
                           Text="Ver "/>
                <TextBlock FontSize="14"
                           Text="。要现在更新吗？"/>
            </StackPanel>
            <TextBlock Margin="0,20,0,8"
                       FontSize="16"
                       FontWeight="SemiBold"
                       Text="更新信息："/>
            <Border
                Width="600"
                Height="250"
                HorizontalAlignment="Left"
                Background="{ui:ThemeResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{ui:ThemeResource ControlStrokeColorDefaultBrush}"
                BorderThickness="1"
                Effect="{DynamicResource SharedShadow}"
                CornerRadius="8">
                <TextBlock
                    x:Name="UpdataLog"
                    Margin="15"
                    FontSize="14"
                    LineHeight="22"
                    Text="暂无更新信息！"/>
            </Border>
            <Grid Margin="0,25,0,0">
                <ui:Button
                    x:Name="SkipVersionButton"
                    Width="120"
                    Height="40"
                    HorizontalAlignment="Left"
                    Appearance="Secondary"
                    Content="跳过此版本"
                    Click="SkipVersionButton_Click"/>
                <StackPanel HorizontalAlignment="Right"
                            Orientation="Horizontal">

                    <ui:Button
                        x:Name="RemindTomorrowButton"
                        Width="120"
                        Height="40"
                        Margin="12,0,0,0"
                        Appearance="Secondary"
                        Content="明天提醒"
                        Click="RemindTomorrowButton_Click"/>
                    <ui:Button
                        x:Name="ManualDownloadButton"
                        Width="80"
                        Margin="12,0,0,0"
                        Height="40"
                        Appearance="Secondary"
                        Icon="Link24"
                        Content="自行下载"
                        Click="ManualDownloadButton_Click"/>
                    <ui:Button
                        x:Name="InstallButton"
                        Width="160"
                        Height="40"
                        Margin="5,0,0,0"
                        Appearance="Primary"
                        Icon="ArrowDownload24"
                        Click="InstallButton_Click"
                        Content="下载并安装"/>
                </StackPanel>
            </Grid>
            <ProgressBar x:Name="DownloadProgressBar"
                         Margin="0,12,0,0"
                         HorizontalAlignment="Right"
                         VerticalAlignment="Center"
                         Width="160"
                         Height="4"/>
        </StackPanel>
    </Grid>
</Window>