﻿#pragma checksum "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "337C3B83BF824EA83A7AAFF1D1AEDA3914801FF9"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Views.Windows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Windows {
    
    
    /// <summary>
    /// MaterialCollectorWindow
    /// </summary>
    public partial class MaterialCollectorWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 30 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button BindWindow;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WindowInfo;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button CaptureButton;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button DeleteButton;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoCapture;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox ImageList;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image PreviewImage;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/windows/materialcollectorwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BindWindow = ((Wpf.Ui.Controls.Button)(target));
            
            #line 32 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
            this.BindWindow.Click += new System.Windows.RoutedEventHandler(this.BindWindow_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.WindowInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.CaptureButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 46 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
            this.CaptureButton.Click += new System.Windows.RoutedEventHandler(this.CaptureButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SaveButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 53 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.DeleteButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 61 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
            this.DeleteButton.Click += new System.Windows.RoutedEventHandler(this.DeleteButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.AutoCapture = ((System.Windows.Controls.CheckBox)(target));
            
            #line 72 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
            this.AutoCapture.Checked += new System.Windows.RoutedEventHandler(this.AutoCapture_CheckedChanged);
            
            #line default
            #line hidden
            
            #line 73 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
            this.AutoCapture.Unchecked += new System.Windows.RoutedEventHandler(this.AutoCapture_CheckedChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ImageList = ((System.Windows.Controls.ListBox)(target));
            
            #line 87 "..\..\..\..\..\..\Views\Windows\MaterialCollectorWindow.xaml"
            this.ImageList.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ImageList_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.PreviewImage = ((System.Windows.Controls.Image)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

