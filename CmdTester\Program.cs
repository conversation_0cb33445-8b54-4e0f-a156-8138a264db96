﻿using System.Reflection;

namespace CmdTester
{
    public class Program
    {
        private static void Main(string[] args)
        {
            Console.WriteLine("Welcome My XSL's CmdTests Program！");
            Console.WriteLine("Let's Go！");
            Console.WriteLine("============= 功能目录 =============");
            Console.WriteLine("图片、位置、颜色打包：main.dabao");
            Console.WriteLine("游戏图像图库仅识别：main.Debug1");
            Console.WriteLine("检测大漠注册码是否可以使用：A.j");
            Console.WriteLine("==================================");
            string input = "";
            if (args.Length != 0)
            {
                input = args[0];
                Console.WriteLine("启动参数为：");
                foreach (var item in args)
                    Console.WriteLine(item);
            }

        Re:
            Console.WriteLine("Please Input Class.Fun Name：");
            // 从控制台获取输入
            if (input == "")
                input = Console.ReadLine() ?? "";

            if (input == "")
                goto Re;

            // 假设输入的格式是 "A.a"
            string className = "CmdTester." + input.Split('.')[0]; // 包含命名空间
            string methodName = input.Split('.')[1];
            try
            {
                Type type = Type.GetType(className);
                if (type != null)
                {
                    ConstructorInfo constructor = type.GetConstructor(Type.EmptyTypes);
                    if (constructor != null)
                    {
                        object instance = constructor.Invoke(null);
                        MethodInfo method = type.GetMethod(methodName);
                        if (method != null)
                            method.Invoke(instance, null);
                        else
                            Console.WriteLine("Method not found.");
                    }
                    else
                        Console.WriteLine("Constructor not found.");
                }
                else
                    Console.WriteLine("Class not found or incorrect namespace.");
            }
            catch (Exception ex)
            {
                Console.WriteLine("An error occurred: " + ex.InnerException);
            }
            input = "";
            goto Re;
        }
    }
}