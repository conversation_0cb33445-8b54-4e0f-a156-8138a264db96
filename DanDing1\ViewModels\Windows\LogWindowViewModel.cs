﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper.Models;
using XHelper;
using DanDing1.ViewModels.Base;

namespace DanDing1.ViewModels.Windows
{
    public partial class LogWindowViewModel : ObservableObject
    {
        public LogWindowViewModel(string gameName)
        {
            GameName = gameName;
            // 尝试从文件加载保存的标题
            string? savedTitle = XConfig.LoadValueFromFile<string>(GameName, "LogName");
            Title_Str = savedTitle ?? "日志 - " + gameName;
            Log = XLogger.GetLogObj(GameName);
        }

        private string _showTime;

        public string ShowTime
        {
            get
            {
                return gameView?.ShowTime ?? "00:00:00";
            }
            set { SetProperty(ref _showTime, value); }
        }

        /// <summary>
        /// 游戏配置视图模型
        /// </summary>
        private GameViewBaseModel? _gameView = null;
        internal GameViewBaseModel? gameView
        {
            get { return _gameView; }
            set
            {
                if (_gameView != null)
                {
                    // 取消对旧gameView的属性变更订阅
                    _gameView.PropertyChanged -= GameView_PropertyChanged;
                }

                _gameView = value;

                if (_gameView != null)
                {
                    // 订阅新gameView的属性变更事件
                    _gameView.PropertyChanged += GameView_PropertyChanged;
                    // 立即更新ShowTime
                    OnPropertyChanged(nameof(ShowTime));
                }
            }
        }

        private void GameView_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // 当gameView的ShowTime属性变化时，通知UI更新
            if (e.PropertyName == nameof(GameViewBaseModel.ShowTime))
            {
                OnPropertyChanged(nameof(ShowTime));
            }
        }

        private ObservableCollection<LogModel> _log = [];

        public ObservableCollection<LogModel> Log
        {
            get
            {
                return XLogger.GetLogObj(GameName);
            }
            set { SetProperty(ref _log, value); }
        }

        [ObservableProperty]
        private string _gameName;

        private string _title_Str;

        public string Title_Str
        {
            get => _title_Str;
            set
            {
                if (SetProperty(ref _title_Str, value))
                {
                    OnPropertyChanged(nameof(Title_Str));
                    // 保存标题到文件
                    if (!string.IsNullOrEmpty(GameName))
                    {
                        XConfig.SaveValueToFile(GameName, "LogName", value);
                    }
                }
            }
        }
    }
}