﻿using DanDing1.Helpers;
using DanDing1.Models;
using DanDing1.Services;
using DanDing1.Views;
using DanDing1.Views.Windows;
using System.Collections.ObjectModel;
using System.Net.Http;
using System.Windows.Media.Imaging;
using Wpf.Ui;
using Wpf.Ui.Abstractions.Controls;
using Wpf.Ui.Controls;
using Wpf.Ui.Extensions;
using XHelper;
using XHelper.DanDingNet;
using XHelper.Models;

namespace DanDing1.ViewModels.Pages
{
    /// <summary>
    /// 布尔值转换为可见性
    /// </summary>
    public class BooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Visible;
            }
            return false;
        }
    }

    public partial class LoadViewModel : ObservableObject, INavigationAware
    {
        private readonly IContentDialogService _contentDialogService;

        private readonly HttpClient _httpClient = new HttpClient();

        private readonly string Points_Info = "\n----------积分累计规则----------\n" +
                    "用户每次主动使用卡密、增加天数后，\n系统会自动将用户增加的天数按\n1天 = 1积分 的规则添加相应的账号积分；";

        /// <summary>
        /// 用户权益列表
        /// </summary>
        private readonly Dictionary<int, string> UserRights = new()
        {
            {1,"偏好设置的云端同步功能" },
            {7,"超级多开使用权限" },
            {30,"任务设置次数无视上限" },
            {100,"当登录状态失效后允许自动登录至多3次" },
            {200,"双设备同时在线权益" }
        };

        /// <summary>
        /// 自助加时按钮是否启用
        /// </summary>
        [ObservableProperty]
        private bool _activityButtonEnabled = true;

        /// <summary>
        /// 自助加时按钮文本
        /// </summary>
        [ObservableProperty]
        private string _activityButtonText = "自助加时";

        /// <summary>
        /// 用户积分
        /// </summary>
        [ObservableProperty]
        private int _info_Points = 0;

        /// <summary>
        /// 用户积分记录
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<_PointsRecordInfo> _info_PointsRecords = new();

        [ObservableProperty]
        private int _longestScriptRunningMinutes;

        /// <summary>
        /// 网络是否正常
        /// </summary>
        [ObservableProperty]
        private bool _netisok = false;

        /// <summary>
        /// QQ号码
        /// </summary>
        [ObservableProperty]
        private string _qq_ID = "未绑定";

        /// <summary>
        /// 发送验证码开停控制
        /// </summary>
        [ObservableProperty]
        private bool _regSendCodeButton = true;

        /// <summary>
        /// 发送验证码开停控制
        /// </summary>
        [ObservableProperty]
        private bool _resetSendCodeButton = true;

        /// <summary>
        /// 功能布局选择索引
        /// 0:登录
        /// 1:注册
        /// 2:修改密码
        /// 3:忘记密码
        /// 4:账号信息
        /// 5:充值账号
        /// </summary>
        [ObservableProperty]
        private int _select_index = 0;

        [ObservableProperty]
        private string _show_Login = "未登录";

        /// <summary>
        /// 自助加时按钮是否显示
        /// </summary>
        [ObservableProperty]
        private bool _showActivityButton = false;

        /// <summary>
        /// WebToken控件是否显示
        /// </summary>
        [ObservableProperty]
        private bool _showWebTokenControl = false;

        [ObservableProperty]
        private int _totalBountyCount;

        [ObservableProperty]
        private int _totalScriptClickCount;

        [ObservableProperty]
        private int _totalScriptRunningMinutes;

        [ObservableProperty]
        private int _totalTaskStartCount;

        /// <summary>
        /// WebToken按钮是否启用
        /// </summary>
        [ObservableProperty]
        private bool _webTokenButtonEnabled = true;

        /// <summary>
        /// WebToken按钮文本
        /// </summary>
        [ObservableProperty]
        private string _webTokenButtonText = "点我设置WebToken";

        /// <summary>
        /// 欢迎文本
        /// </summary>
        [ObservableProperty]
        private string _welcomeText = "欢迎使用蛋定助手！";

        private string BetaTester_Log = "";

        /// <summary>
        /// 活动状态是否已检查
        /// </summary>
        private bool isActivityChecked = false;

        private bool isHaveUserinfo = false;
        private bool isRunTimeStart = false;

        /// <summary>
        /// 只能展示一次更新窗口
        /// </summary>
        private bool ShowUpdataWindowsed = false;

        public LoadViewModel(IContentDialogService contentDialogService, INavigationService navigationService)
        {
            _contentDialogService = contentDialogService;
            //InitYanZhengMa();//初始化验证码图片
            SynchronizationContext syncContext = SynchronizationContext.Current;
            XLogger.InitThread(syncContext);
            LoadUserAndPwd();//加载本地用户名和密码
            NavigationView = navigationService.GetNavigationControl() as NavigationView ?? throw new Exception("软件视图组件注册失败！无法继续！");
        }

        /// <summary>
        /// api 简
        /// </summary>
        private DanDingNet _api => _config.dNet;

        /// <summary>
        /// 配置 简
        /// </summary>
        private AppConfig _config => GlobalData.Instance.appConfig;

        private NavigationView NavigationView { get; }

        /// <summary>
        /// 快捷执行
        /// </summary>
        /// <returns></returns>
        public async Task Do(string name)
        {
            if (name == "登录")
                await OnLoginUser();
        }

        public Task OnNavigatedFromAsync()
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// 初始化
        /// </summary>
        public async Task OnNavigatedToAsync()
        {
            if (!_config.IsLogin)
            {
                isRunTimeStart = false;
                isHaveUserinfo = false;
                Select_index = 0;
                Show_Login = "未登录";
                _config.IsLogin = false;
            }

            _infoBar?.Show("服务器连接正在初始化", "服务器连接正在初始化，请您耐心等待，您无法在公告出来前进行网络操作！\r\n若无法连接服务器，您可以去设置中选择可连接的节点！", "Warning");
            if (!await _config.InitAsync()) return;
            Notice = _config.Info.Notice;//初始化公告
            UpdataLog = BetaTester_Log is "" ? _config.Info.UpdataLog : BetaTester_Log;//初始化更新日志
            Netisok = true;
            _infoBar?.Hidden();

            // 只在第一次检查活动状态
            if (!isActivityChecked)
            {
                await CheckActivityStatus();
                isActivityChecked = true;
            }

            //验证账号消息
            if (!isRunTimeStart)
                if (await _api.Auth.CheckLoginStatusAsync())
                {
                    isRunTimeStart = true;
                    await SetUserInfo();
                    _config.IsLogin = true;
                }
        }

        /// <summary>
        /// 刷新用户信息
        /// </summary>
        [RelayCommand]
        public async Task RefreshUserInfo()
        {
            // 重置用户信息状态，允许重新获取
            isHaveUserinfo = false;

            // 获取用户消息
            var user = await _api.User.GetUserInfoAsync();
            if (user != null && user.IsSuccess)
            {
                isHaveUserinfo = true;
                // 设置用户信息
                Info_Id = user.Data?.id ?? -1;
                Info_Email = user.Data?.email ?? "";
                Info_Ip = user.Data?.ip ?? "";
                Info_Status = user.Data?.status ?? "";
                Info_UserName = user.Data?.username ?? "";
                Info_Viptime = user.Data?.vip_time ?? "";
                Qq_ID = user.Data?.qq ?? "未绑定";
                if (Qq_ID == "") Qq_ID = "未绑定";

                // 获取用户积分信息
                await GetUserPointsInfo();

                // 更新欢迎文本
                await UpdateWelcomeText();

                // 更新用户行为统计数据
                var profile = GlobalData.Instance.UserProfile;
                TotalTaskStartCount = profile.TotalTaskStartCount;
                TotalScriptClickCount = profile.TotalScriptClickCount;
                TotalScriptRunningMinutes = profile.TotalScriptRunningMinutes;
                TotalBountyCount = profile.TotalBountyCount;
                LongestScriptRunningMinutes = profile.LongestScriptRunningMinutes;

                // 确保用户在信息页面
                //Select_index = 4;
                Show_Login = "已登录";

                if (Info_Status.ToString().Contains("试用"))
                    _config.IsFree = true;

                if (!_config.IsFree && user?.Data?.config != null)//不是试用 载入用户偏好设置
                {
                    GlobalData.Instance.UserConfig = XSerializer.DeserializeJsonTxtToObject<UserConfigModel>(user?.Data?.config?.ToString());
                    //Ntfy接口
                    var rep = await _api.User.GetNtfyKeyAsync();
                    if (rep?.IsSuccess ?? false)
                    {
                        GlobalData.Instance.appConfig.Ntfy_Key = rep.Data.app_key;
                    }
                }

                if (_config.IsFree)
                    Qq_ID = "试用禁止";

                _infoBar?.Show("刷新用户信息", $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - 成功刷新用户[{Info_UserName}]的信息", "Success", 3000);
            }
            else
            {
                _infoBar?.Show("刷新用户信息", $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - 刷新用户信息失败：{user?.Message ?? "未知错误"}", "Error", 5000);
            }
        }

        /// <summary>
        /// 检查活动状态
        /// </summary>
        private async Task CheckActivityStatus()
        {
            try
            {
                var activityInfo = await _api.Activity.GetActivityAsync(1);

                if (activityInfo?.Data == null) return;

                // 检查活动是否过期
                if (!activityInfo.Data.IsExpired)
                {
                    ShowActivityButton = true;
                    ActivityButtonText = activityInfo.Data.ActivityName;
                }
                else
                {
                    ShowActivityButton = false;
                }
            }
            catch (Exception ex)
            {
                XLogger.SaveException(ex);
                XLogger.Error($"检查活动1的状态时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查Beta版本更新
        /// </summary>
        /// <returns></returns>
        private async Task CheckBetaUpdate()
        {
            var vs = await _api.System.GetInfoBetaAsync();
            if (vs[0] != "")
            {
                Notice = vs[3];
                UpdataLog = vs[2];
            }
            if (vs[0] != "" && vs[0] != _config.Info.Now_Ver)
            {
                if (UpdateWindow.ShouldShowUpdateWindow(vs[0]))
                {
                    var updateWindow = new UpdateWindow(_config.Info.Now_Ver, vs[0], vs[2], vs[1]);
                    updateWindow.ShowDialog();
                    ShowUpdataWindowsed = true;
                }
            }
        }

        // 检查常规更新
        private async Task CheckRegularUpdate()
        {
            if (_config.Info.Ver != "" && _config.Info.IsNeedUpData)
            {
                string tiptxt = GlobalData.IsBetaVersion ? "您没有参加内测计划，但您当前正在使用内测版本\r\n，请您自行选择是否更新到正式版本，或去设置参加内测计划！\r\n" : "";
                if (UpdateWindow.ShouldShowUpdateWindow(_config.Info.Ver))
                {
                    var updateWindow = new UpdateWindow("", "", tiptxt + GlobalData.Instance.appConfig.Info.UpdataLog, "");
                    updateWindow.ShowDialog();
                    ShowUpdataWindowsed = true;
                }
            }
        }

        private async void CheckVerAndShow()
        {
            // 检查内测状态
            if (GlobalData.IsBetaVersion && !GlobalData.Instance.UserConfig.BetaTesterToggle)
            {
                await ShowBetaTestDialog();
                return;
            }

            // 检查版本更新
            if (!GlobalData.Instance.UserConfig.BetaTesterToggle && !ShowUpdataWindowsed)
            {
                await CheckRegularUpdate();
                return;
            }

            // 检查Beta版本更新
            if (GlobalData.Instance.UserConfig.BetaTesterToggle && !ShowUpdataWindowsed)
            {
                await CheckBetaUpdate();
            }
        }

        /// <summary>
        /// 检查并更新WebToken状态
        /// </summary>
        private async Task CheckWebTokenStatus()
        {
            // 如果用户未登录或是试用状态，隐藏WebToken控件
            if (!_config.IsLogin || _config.IsFree)
            {
                ShowWebTokenControl = false;
                return;
            }

            ShowWebTokenControl = true;

            // 检查WebToken状态
            var tokenStatus = await _api.GetWebTokenStatusAsync();
            if (tokenStatus?.IsSuccess == true)
            {
                // 用户已设置WebToken
                WebTokenButtonText = "已设置WebToken";
                WebTokenButtonEnabled = false;
            }
            else
            {
                // 用户未设置WebToken
                WebTokenButtonText = "点我设置WebToken";
                WebTokenButtonEnabled = true;
            }
        }

        /// <summary>
        /// 连接到WebSocket服务器
        /// </summary>
        private async Task ConnectToWebSocketServer()
        {
            try
            {
                if (!GlobalData.Instance.appConfig.EnableWebSocketCommunication)
                {
                    XLogger.Debug("WebSocket通信功能未启用，不连接WebSocket服务器");
                    return;
                }

                // 检查是否是试用用户，试用用户不连接WebSocket
                if (_config.IsFree)
                {
                    // 如果当前已连接，则断开连接
                    if (XWebsocket.IsConnected)
                    {
                        await XWebsocket.DisconnectAsync();
                        XLogger.Debug("试用用户不应连接WebSocket服务器，已断开连接");
                    }
                    return;
                }

                // 检查是否已登录
                if (!_config.IsLogin)
                {
                    XLogger.Debug("用户未登录，不连接WebSocket服务器");
                    return;
                }

                // 从Auth服务获取当前的Token
                string token = _api.Auth.GetToken();
                if (!string.IsNullOrEmpty(token))
                {
                    // 检查WebSocket是否已经连接
                    if (!XWebsocket.IsConnected)
                    {
                        // 获取WebSocket服务器地址，优先从配置文件获取，否则使用默认地址
                        string wsServerUrl = XConfig.LoadValueFromFile<string>("WsServerUrl") ?? GlobalData.WS_HOST;

                        // 如果地址为空，则使用默认地址
                        if (string.IsNullOrEmpty(wsServerUrl))
                        {
                            wsServerUrl = GlobalData.WS_HOST;
                            XLogger.Debug("WebSocket服务器地址为空，使用默认地址");
                        }

                        bool connected = await XWebsocket.ConnectAsync(wsServerUrl, token);
                        if (connected)
                        {
                            XLogger.Debug($"成功连接到WebSocket服务器");
                            // 设置自动重连
                            XWebsocket.AutoReconnect = true;

                            // 确保重新注册WebSocket消息处理器
                            var appHostService = App.GetService<ApplicationHostService>();
                            if (appHostService != null)
                            {
                                // 通过反射调用私有方法RegisterWebSocketHandlers
                                var method = appHostService.GetType().GetMethod("RegisterWebSocketHandlers",
                                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                                if (method != null)
                                {
                                    method.Invoke(appHostService, null);
                                    XLogger.Debug("WebSocket连接成功后，通过ApplicationHostService重新注册了消息处理器");
                                }
                            }

                            // 如果无法通过ApplicationHostService注册，则尝试使用LogSyncService
                            var logSyncService = App.GetService<LogSyncService>();
                            if (logSyncService != null)
                            {
                                logSyncService.RegisterMessageHandlers();
                                XLogger.Debug("WebSocket连接成功后，已重新注册消息处理器");
                            }
                            else
                            {
                                // 如果无法获取LogSyncService实例，则手动创建一个临时实例来注册处理器
                                var logStorage = App.GetService<ILogStorage>();
                                if (logStorage != null)
                                {
                                    new LogSyncService(logStorage);
                                    XLogger.Debug("已创建临时LogSyncService实例并注册消息处理器");
                                }
                                else
                                {
                                    XLogger.Warn("无法获取LogStorage服务，无法注册WebSocket消息处理器");
                                }
                            }
                        }
                        else
                        {
                            XLogger.Debug($"连接WebSocket服务器失败");
                        }
                    }
                    else
                    {
                        XLogger.Debug("WebSocket已连接，无需重新连接");

                        // 即使已连接，也确保消息处理器已注册
                        var logSyncService = App.GetService<LogSyncService>();
                        if (logSyncService != null)
                        {
                            logSyncService.RegisterMessageHandlers();
                            XLogger.Debug("WebSocket已连接状态下，已重新注册消息处理器");
                        }
                    }
                }
                else
                {
                    XLogger.Debug("无法连接WebSocket服务器: Token为空");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"连接WebSocket服务器时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户积分信息
        /// </summary>
        private async Task GetUserPointsInfo()
        {
            try
            {
                var pointsData = await _api.User.GetUserPointsAsync();
                if (pointsData != null && pointsData.IsSuccess && pointsData.Data != null)
                {
                    GlobalData.Instance.appConfig.User_Points = Info_Points = pointsData.Data.Points;
                    // 清空并添加新记录
                    Info_PointsRecords.Clear();
                    foreach (var record in pointsData.Data.Records)
                        Info_PointsRecords.Add(record);
                }
                else
                {
                    // 积分获取失败，设置为0
                    GlobalData.Instance.appConfig.User_Points = Info_Points = 0;
                    Info_PointsRecords.Clear();
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取用户积分信息失败: {ex.Message}");
                GlobalData.Instance.appConfig.User_Points = Info_Points = 0;
                Info_PointsRecords.Clear();
            }
        }

        /// <summary>
        /// 初始化图形验证码
        /// </summary>
        private void InitYanZhengMa()
        {
            //生成验证码
            captcha = new();
            BitmapImage bitmapImage = new()
            {
                CacheOption = BitmapCacheOption.OnLoad
            };
            bitmapImage.BeginInit();
            bitmapImage.StreamSource = captcha.GetImgMemoryStream();
            bitmapImage.EndInit();

            VerificationCodeImg = bitmapImage;
        }

        /// <summary>
        /// 导入用户名和密码
        /// </summary>
        private void LoadUserAndPwd()
        {
            Load_Username = XConfig.LoadValueFromFile<string>("Account_User") ?? "";
            Utils.GetAppKeyAndIV(out string key, out string iv);
            Load_Password = XConfig.LoadValueFromFile_Decrypt("Account_Pwd", key, iv) ?? "";
        }

        /// <summary>
        /// 自助加时
        /// </summary>
        [RelayCommand]
        private async Task OnActivityDo()
        {
            if (string.IsNullOrEmpty(UseKami_User))
            {
                _infoBar?.Show("警告", "请输入用户名！", "Warning", 3000);
                return;
            }

            if (string.IsNullOrEmpty(UseKami_User_Again))
            {
                _infoBar?.Show("警告", "请输入重复用户名！", "Warning", 3000);
                return;
            }

            if (UseKami_User != UseKami_User_Again)
            {
                _infoBar?.Show("警告", "用户名与重复用户名不一致！请确认！", "Warning", 3000);
                return;
            }

            // 显示验证码窗口
            var captchaWindow = new CaptchaWindow();
            if (captchaWindow.ShowDialog() != true)
            {
                _infoBar?.Show("提示", "验证已取消", "Warning", 3000);
                return;
            }

            // 如果验证码验证失败，直接返回
            if (!captchaWindow.IsVerified)
            {
                _infoBar?.Show("错误", "验证码验证失败", "Error", 3000);
                return;
            }

            var userIdInfo = await _api.User.QueryUserIdAsync(UseKami_User);
            if (userIdInfo is null)
            {
                _infoBar?.Show("错误", "网络请求失败！", "Error", 3000);
                return;
            }

            if (!userIdInfo.IsSuccess)
            {
                _infoBar?.Show("错误", userIdInfo.Message, "Error", 3000);
                return;
            }

            var result = await _api.Activity.DoActivityAsync(1, userIdInfo.Data.UserId);
            if (result is null)
            {
                _infoBar?.Show("错误", "网络请求失败！", "Error", 3000);
                return;
            }

            if (result.Code != 200)
            {
                _infoBar?.Show("提示", result.Message, "Warning", 3000);
                return;
            }

            _infoBar?.Show("成功", result.Message, "Success", 3000);
            //跳转到登录界面
            Select_index = 0;
        }

        /// <summary>
        /// 改密码
        /// </summary>
        /// <param name="email"></param>
        [RelayCommand]
        private async void OnChangePassword()
        {
            // 验证用户名
            var (isUsernameValid, usernameError) = InputValidator.ValidateUsername(Change_Username);
            if (!isUsernameValid)
            {
                _infoBar?.Show("警告", usernameError, "Warning", 3000);
                return;
            }

            // 验证原密码
            var (isOldPasswordValid, oldPasswordError) = InputValidator.ValidatePassword(Change_OldPassword);
            if (!isOldPasswordValid)
            {
                _infoBar?.Show("警告", oldPasswordError, "Warning", 3000);
                return;
            }

            // 验证新密码
            var (isNewPasswordValid, newPasswordError) = InputValidator.ValidatePassword(Change_NewPassword);
            if (!isNewPasswordValid)
            {
                _infoBar?.Show("警告", newPasswordError, "Warning", 3000);
                return;
            }

            // 验证重复输入的新密码
            if (Change_NewPassword != Change_NewPassword_Again)
            {
                _infoBar?.Show("警告", "两次输入的新密码不一致！", "Warning", 3000);
                return;
            }

            // 检查用户名是否包含可疑内容
            if (RequestValidator.ContainsSuspiciousContent(Change_Username))
            {
                _infoBar?.Show("警告", "用户名包含不允许的字符", "Warning", 3000);
                // 记录可疑的操作尝试
                RequestValidator.AuditRequest(Change_Username, "修改密码尝试被拒绝", "包含可疑内容", "本地应用");
                return;
            }

            // 清理输入数据
            string sanitizedUsername = InputValidator.SanitizeInput(Change_Username);
            string sanitizedOldPassword = Change_OldPassword; // 密码不进行清理
            string sanitizedNewPassword = Change_NewPassword; // 密码不进行清理

            // 记录密码修改尝试
            RequestValidator.AuditRequest(sanitizedUsername, "修改密码尝试", "开始修改密码流程", "本地应用");

            var obj = await _api.Auth.ChangePasswordAsync(sanitizedUsername, sanitizedOldPassword, sanitizedNewPassword);
            if (!obj?.IsSuccess ?? false)
            {
                _infoBar?.Show("修改密码错误", $"{obj?.Message}", "Error", 5000);
                // 记录密码修改失败
                RequestValidator.AuditRequest(sanitizedUsername, "修改密码失败", obj?.Message ?? "未知错误", "本地应用");
                return;
            }
            _infoBar?.Show("修改密码成功", $"{obj?.Message}", "Success", 5000);
            // 记录密码修改成功
            RequestValidator.AuditRequest(sanitizedUsername, "修改密码成功", "用户成功修改密码", "本地应用");

            Select_index = 0;
            Load_Username = sanitizedUsername;
            Load_Password = sanitizedNewPassword;
        }

        /// <summary>
        /// 试用登录
        /// </summary>
        [RelayCommand]
        private async Task OnFreeUser()
        {
            if (_api.HaveCookie())
                //验证账号消息
                if (await _api.Auth.CheckLoginStatusAsync())
                {
                    isRunTimeStart = true;
                    await SetUserInfo();
                    _config.IsLogin = true;
                    _config.IsFree = true;

                    // 确保断开WebSocket连接，因为试用用户不应连接WebSocket
                    await XWebsocket.DisconnectAsync();
                    XLogger.Debug("试用用户登录，确保WebSocket连接已断开并禁用");
                    XWebsocket.IsDisabled = true;
                    return;
                }

            bool isokReady = await _api.Activity.CheckFreeUserAsync();
            if (isokReady)
            {
                ContentDialogResult result = await _contentDialogService.ShowSimpleDialogAsync(
                new SimpleContentDialogCreateOptions()
                {
                    Title = "确认开始试用吗？",
                    Content = "每天只能免费试用一次，每次试用时间为1小时；您准备好今天的试用了吗？",
                    PrimaryButtonText = "开始体验吧",
                    CloseButtonText = "我再想想",
                }
                );
                if (result is ContentDialogResult.Secondary or ContentDialogResult.None) return;
            }
            var ret = await _api.Activity.RegisterFreeUserAsync();
            if (ret is null || !(ret?.IsSuccess ?? false))
            {
                _infoBar?.Show("试用软件失败", ret?.Message, "Warning", 5000);
                return;
            }
            _infoBar?.Show(ret?.Message, ret?.Data.msg, "Success", 5000);
            _config.IsLogin = true;
            _config.IsFree = true;

            // 确保断开WebSocket连接，因为试用用户不应连接WebSocket
            await XWebsocket.DisconnectAsync();
            XLogger.Debug("试用用户登录，确保WebSocket连接已断开并禁用");
            XWebsocket.IsDisabled = true;

            await SetUserInfo(false);
        }

        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="code"></param>
        [RelayCommand]
        private async Task OnLoginUser()
        {
            // 验证用户名
            var (isUsernameValid, usernameError) = InputValidator.ValidateUsername(Load_Username);
            if (!isUsernameValid)
            {
                _infoBar?.Show("警告", usernameError, "Warning", 3000);
                return;
            }

            // 验证密码
            var (isPasswordValid, passwordError) = InputValidator.ValidatePassword(Load_Password);
            if (!isPasswordValid)
            {
                _infoBar?.Show("警告", passwordError, "Warning", 3000);
                return;
            }

            // 清理输入，防止注入攻击
            string sanitizedUsername = InputValidator.SanitizeInput(Load_Username);
            string sanitizedPassword = Load_Password; // 密码不进行清理

            // 检查用户名是否包含可疑内容
            if (RequestValidator.ContainsSuspiciousContent(Load_Username))
            {
                _infoBar?.Show("警告", "用户名包含不允许的字符", "Warning", 3000);
                // 记录可疑的登录尝试
                RequestValidator.AuditRequest(sanitizedUsername, "登录尝试被拒绝", "包含可疑内容", "本地应用");
                return;
            }

            // 记录登录尝试
            RequestValidator.AuditRequest(sanitizedUsername, "登录尝试", "开始登录流程", "本地应用");

            var obj = await _api.Auth.LoginAsync(sanitizedUsername, sanitizedPassword);//登录账号
            if (!obj?.IsSuccess ?? false)
            {
                _infoBar?.Show("登录错误", $"{obj?.Message}", "Error", 5000);
                // 记录登录失败
                RequestValidator.AuditRequest(sanitizedUsername, "登录失败", obj?.Message ?? "未知错误", "本地应用");
                if (obj?.Message.Contains("已到期") ?? false)
                    Select_index = 5;
                return;
            }
            _infoBar?.Show("提示", $"您已成功登录账户 {sanitizedUsername}", "Success", 3000);
            // 记录登录成功
            RequestValidator.AuditRequest(sanitizedUsername, "登录成功", "用户成功登录", "本地应用");

            _config.IsLogin = true;
            SaveUserAndPwd();
            await SetUserInfo(false);

            // 连接WebSocket服务器
            await ConnectToWebSocketServer();
        }

        /// <summary>
        /// 退出登录
        /// </summary>
        /// <param name="email"></param>
        [RelayCommand]
        private async void OnOutLogin()
        {
            if (string.IsNullOrEmpty(Info_UserName?.ToString()))
            {
                _infoBar?.Show("错误", $"您当前处于未登录状态！", "Error", 5000);
                return;
            }

            try
            {
                await _api.Auth.OutLoginAsync(Info_UserName?.ToString() ?? "");
                _infoBar?.Show("提示", $"您已成功退出账户 {Load_Username}", "Success", 3000);
                isRunTimeStart = false;
                isHaveUserinfo = false;
                Select_index = 0;
                Show_Login = "未登录";
                _config.IsLogin = false;

                // 断开WebSocket连接
                await XWebsocket.DisconnectAsync();
                XLogger.Debug("用户退出登录，WebSocket连接已断开");
            }
            catch (Exception ex)
            {
                _infoBar?.Show("错误", $"{ex.Message}，请尝试重新启动!", "Error", 5000);
            }
        }

        /// <summary>
        /// 注册
        /// </summary>
        /// <param name="code"></param>
        [RelayCommand]
        private async Task OnReg(string code)
        {
            // 验证卡密
            var (isKamiValid, kamiError) = InputValidator.ValidateKami(Reg_Kami);
            if (!isKamiValid)
            {
                _infoBar?.Show("警告", kamiError, "Warning", 3000);
                return;
            }

            // 验证验证码
            var (isCodeValid, codeError) = InputValidator.ValidateCode(code);
            if (!isCodeValid)
            {
                _infoBar?.Show("警告", codeError, "Warning", 3000);
                return;
            }

            // 验证邮箱
            var (isEmailValid, emailError) = InputValidator.ValidateEmail(Reg_Email);
            if (!isEmailValid)
            {
                _infoBar?.Show("警告", emailError, "Warning", 3000);
                return;
            }

            // 验证用户名
            var (isUsernameValid, usernameError) = InputValidator.ValidateUsername(Reg_Username);
            if (!isUsernameValid)
            {
                _infoBar?.Show("警告", usernameError, "Warning", 3000);
                return;
            }

            // 验证密码
            var (isPasswordValid, passwordError) = InputValidator.ValidatePassword(Reg_Password);
            if (!isPasswordValid)
            {
                _infoBar?.Show("警告", passwordError, "Warning", 3000);
                return;
            }

            // 检查输入是否包含可疑内容
            if (RequestValidator.ContainsSuspiciousContent(Reg_Username) ||
            RequestValidator.ContainsSuspiciousContent(Reg_Email) ||
            RequestValidator.ContainsSuspiciousContent(Reg_Kami) ||
            RequestValidator.ContainsSuspiciousContent(code))
            {
                _infoBar?.Show("警告", "您的输入包含不允许的字符", "Warning", 3000);
                // 记录可疑的注册尝试
                RequestValidator.AuditRequest(Reg_Username, "注册尝试被拒绝", "包含可疑内容", "本地应用");
                return;
            }

            // 清理输入数据
            string sanitizedUsername = InputValidator.SanitizeInput(Reg_Username);
            string sanitizedEmail = InputValidator.SanitizeInput(Reg_Email);
            string sanitizedKami = InputValidator.SanitizeInput(Reg_Kami);
            string sanitizedCode = InputValidator.SanitizeInput(code);
            string sanitizedPassword = Reg_Password; // 密码不进行清理

            // 记录注册尝试
            RequestValidator.AuditRequest(sanitizedUsername, "注册尝试", $"邮箱: {sanitizedEmail}", "本地应用");

            var obj = await _api.Auth.RegisterAsync(sanitizedUsername, sanitizedPassword, sanitizedKami, sanitizedEmail, sanitizedCode);
            if (!obj?.IsSuccess ?? false)
            {
                _infoBar?.Show("错误", $"{obj?.Message}", "Error", 5000);
                // 记录注册失败
                RequestValidator.AuditRequest(sanitizedUsername, "注册失败", obj?.Message ?? "未知错误", "本地应用");
                return;
            }
            _infoBar?.Show("注册成功", $"{obj?.Message}", "Success", 5000);
            // 记录注册成功
            RequestValidator.AuditRequest(sanitizedUsername, "注册成功", "用户成功注册", "本地应用");

            Select_index = 0;
            Load_Username = sanitizedUsername;
            Load_Password = sanitizedPassword;
        }

        /// <summary>
        /// 注册_发送邮件
        /// </summary>
        /// <param name="email"></param>
        [RelayCommand]
        private async Task OnRegSendEmailCode(string email)
        {
            // 验证邮箱
            var (isEmailValid, emailError) = InputValidator.ValidateEmail(email);
            if (!isEmailValid)
            {
                _infoBar?.Show("警告", emailError, "Warning", 3000);
                return;
            }

            // 验证用户名
            var (isUsernameValid, usernameError) = InputValidator.ValidateUsername(Reg_Username);
            if (!isUsernameValid)
            {
                _infoBar?.Show("警告", usernameError, "Warning", 3000);
                return;
            }

            // 验证密码
            var (isPasswordValid, passwordError) = InputValidator.ValidatePassword(Reg_Password);
            if (!isPasswordValid)
            {
                _infoBar?.Show("警告", passwordError, "Warning", 3000);
                return;
            }

            // 清理输入数据
            string sanitizedEmail = InputValidator.SanitizeInput(email);
            string sanitizedUsername = InputValidator.SanitizeInput(Reg_Username);

            bool isOk = false;
            string errorstr = "";
            try
            {
                isOk = await _api.Auth.SendEmailAsync(sanitizedEmail, sanitizedUsername);
            }
            catch (Exception e) { errorstr = e.Message; }
            if (isOk)
            {
                StopButtonSameTime("reg", 1000 * 100);
                _infoBar?.Show("提示", $"验证码已发送至您的邮箱 {sanitizedEmail}，若无法接收，请在垃圾箱中查找！[100s后恢复此按钮]", "Success", 6666);
            }
            else
                _infoBar?.Show("发送错误", $"网络或服务器连接异常！{errorstr}", "Error", 5000);
        }

        /// <summary>
        /// 重置密码
        /// </summary>
        [RelayCommand]
        private async Task OnResetPassword(string email)
        {
            // 验证邮箱
            var (isEmailValid, emailError) = InputValidator.ValidateEmail(email);
            if (!isEmailValid)
            {
                _infoBar?.Show("警告", emailError, "Warning", 3000);
                return;
            }

            // 验证用户名
            var (isUsernameValid, usernameError) = InputValidator.ValidateUsername(Reset_Username);
            if (!isUsernameValid)
            {
                _infoBar?.Show("警告", usernameError, "Warning", 3000);
                return;
            }

            // 验证验证码
            var (isCodeValid, codeError) = InputValidator.ValidateCode(Reset_Code);
            if (!isCodeValid)
            {
                _infoBar?.Show("警告", codeError, "Warning", 3000);
                return;
            }

            // 清理输入数据
            string sanitizedEmail = InputValidator.SanitizeInput(email);
            string sanitizedUsername = InputValidator.SanitizeInput(Reset_Username);
            string sanitizedCode = InputValidator.SanitizeInput(Reset_Code);

            string errorstr = "";
            Response_ResetPwdData? res_data = null;
            try
            {
                res_data = await _api.Auth.ResetPasswordAsync(sanitizedEmail, sanitizedUsername, sanitizedCode);
            }
            catch (Exception e) { errorstr = e.Message; }

            if (res_data != null)
                if (res_data.IsSuccess)
                {
                    _infoBar?.Show("提示", "密码重置成功！", "Success", 3000);
                    //弹窗
                    ContentDialogResult result = await _contentDialogService.ShowSimpleDialogAsync(
                    new SimpleContentDialogCreateOptions()
                    {
                        Title = "您的密码重置成功！",
                        Content = "用户名：" + sanitizedUsername + " 新密码：" + res_data.Data.new_pwd + "\r\n请及时更改或保存您的密码，谢谢！",
                        PrimaryButtonText = "置剪贴板",
                        CloseButtonText = "好的",
                    }
                    );
                    //置剪贴板
                    if (result == ContentDialogResult.Primary)
                    {
                        if (!Utils.SetClipboardText(res_data.Data.new_pwd, out string error_str))
                            _infoBar?.Show("错误", error_str, "Warning", 3000);
                        else
                            _infoBar?.Show("提示", "重置的密码已复制到剪贴板！", "Success", 3000);
                    }
                    //写到桌面txt
                    Utils.WriteToDesktopTxt($"您更改了您的蛋定助手用户密码，最新的账号及密码如下：\r\n" +
                    $"用户名：{sanitizedUsername}\r\n" +
                    $"密码：{res_data.Data.new_pwd}");
                    Reset_Username = Reset_Code = "";
                }
                else
                    _infoBar?.Show("提示", $"密码重置失败！{res_data.Message}", "Error", 3000);
        }

        /// <summary>
        /// 忘记密码_发送邮件
        /// </summary>
        /// <param name="email"></param>
        [RelayCommand]
        private async Task OnResetSendEmailCode(string email)
        {
            if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(Reset_Username))
            {
                _infoBar?.Show("警告", "邮箱或用户名为空！", "Warning", 3000);
                return;
            }
            if (!XNet.IsEmail(email))
            {
                _infoBar?.Show("警告", "邮箱一栏输入的并非邮箱格式！", "Warning", 3000);
                return;
            }
            bool isOk = false;
            string errorstr = "";
            try
            {
                isOk = await _api.Auth.SendEmailAsync(email, Reset_Username);
            }
            catch (Exception e) { errorstr = e.Message; }
            if (isOk)
            {
                StopButtonSameTime("reg", 1000 * 60);
                _infoBar?.Show("提示", $"验证码已发送至您的邮箱 {email}，若无法接收，请在垃圾箱中查找！[60s后恢复此按钮]", "Success", 6666);
            }
            else
                _infoBar?.Show("发送错误", $"网络或服务器连接异常！{errorstr}", "Error", 5000);
        }

        /// <summary>
        /// 卡密充值
        /// </summary>
        /// <param name="code"></param>
        [RelayCommand]
        private async Task OnUseKami()
        {
            // 验证用户名一致性
            if (UseKami_User != UseKami_User_Again)
            {
                _infoBar?.Show("警告", "用户名与重复用户名不一致！请确认！", "Warning", 3000);
                return;
            }

            // 验证用户名
            var (isUsernameValid, usernameError) = InputValidator.ValidateUsername(UseKami_User);
            if (!isUsernameValid)
            {
                _infoBar?.Show("警告", usernameError, "Warning", 3000);
                return;
            }

            // 验证卡密
            var (isKamiValid, kamiError) = InputValidator.ValidateKami(UseKami_Kami);
            if (!isKamiValid)
            {
                _infoBar?.Show("警告", kamiError, "Warning", 3000);
                return;
            }

            // 清理输入数据
            string sanitizedUsername = InputValidator.SanitizeInput(UseKami_User);
            string sanitizedKami = InputValidator.SanitizeInput(UseKami_Kami);

            var obj = await _api.Activity.UseKamiAsync(sanitizedUsername, sanitizedKami);
            if (!obj?.IsSuccess ?? false)
            {
                _infoBar?.Show("错误", $"{obj?.Message}", "Warning", 3000);
                return;
            }
            _infoBar?.Show("提示", $"{obj?.Message},用户{obj.Data.username} 到期时间:{obj.Data.vip_time}", "Success", 5000);
            if (Show_Login == "已登录")
            {
                Select_index = 4;
                // 更新用户到期时间
                Info_Viptime = obj?.Data?.vip_time ?? "获取异常，请重新登录";
                // 更新用户积分信息
                await GetUserPointsInfo();
                // 清空卡密输入框
                UseKami_Kami = "";

                // 显示积分更新提示
                _infoBar?.Show("积分更新", $"您的积分已更新，当前积分：{Info_Points}", "Success", 3000);
            }
            else
            {
                Select_index = 0;
                Load_Username = UseKami_User;
            }
        }

        /// <summary>
        /// 打开Web日志查询页面命令
        /// </summary>
        [RelayCommand]
        private void OpenWebLogPage()
        {
            try
            {
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "https://web.danding.vip",
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                Utils.ShowMessage("打开网页失败", $"无法打开Web页面: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存用户名和密码
        /// </summary>
        private void SaveUserAndPwd()
        {
            XConfig.SaveValueToFile("Account_User", Load_Username);
            Utils.GetAppKeyAndIV(out string key, out string iv);
            XConfig.SaveValueToFile_Encrypt("Account_Pwd", Load_Password, key, iv);
        }

        /// <summary>
        /// 初始化设置用户信息
        /// </summary>
        /// <param name="Shuax">强制刷新</param>
        /// <exception cref="Exception"></exception>
        private async Task SetUserInfo(bool showTip = true)
        {
            if (isHaveUserinfo)
            {
                Select_index = 4;
                return;
            }

            //获取用户消息
            var user = await _api.User.GetUserInfoAsync();
            if (user != null && user.IsSuccess)
            {
                isHaveUserinfo = true;
                // 设置用户信息
                Info_Id = user.Data?.id ?? -1;
                Info_Email = user.Data?.email ?? "";
                Info_Ip = user.Data?.ip ?? "";
                Info_Status = user.Data?.status ?? "";
                Info_UserName = user.Data?.username ?? "";
                Info_Viptime = user.Data?.vip_time ?? "";
                Qq_ID = user.Data?.qq ?? "未绑定";
                if (Qq_ID == "") Qq_ID = "未绑定";

                // 获取用户积分信息
                await GetUserPointsInfo();

                await UpdateWelcomeText();

                // 更新用户行为统计数据
                var profile = GlobalData.Instance.UserProfile;
                TotalTaskStartCount = profile.TotalTaskStartCount;
                TotalScriptClickCount = profile.TotalScriptClickCount;
                TotalScriptRunningMinutes = profile.TotalScriptRunningMinutes;
                TotalBountyCount = profile.TotalBountyCount;
                LongestScriptRunningMinutes = profile.LongestScriptRunningMinutes;

                Select_index = 4;
                Show_Login = "已登录";
                if (Info_Status.ToString().Contains("试用"))
                    _config.IsFree = true;
                if (!_config.IsFree && user?.Data?.config != null)//不是试用 载入用户偏好设置
                {
                    GlobalData.Instance.UserConfig = XSerializer.DeserializeJsonTxtToObject<UserConfigModel>(user?.Data?.config?.ToString());
                    //Ntfy接口
                    var rep = await _api.User.GetNtfyKeyAsync();
                    if (rep?.IsSuccess ?? false)
                    {
                        GlobalData.Instance.appConfig.Ntfy_Key = rep.Data.app_key;
                    }
                }
                if (_config.IsFree)
                    Qq_ID = "试用禁止";

                _config.IsLogin = true;
                if (showTip) _infoBar?.Show("获取用户信息", $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - 获取用户信息成功，已获取到用户[{Info_UserName}]的信息", "Success");
                CheckVerAndShow();

                // 连接WebSocket服务器
                await ConnectToWebSocketServer();

                // 检查WebToken状态
                await CheckWebTokenStatus();
            }
            else
            {
                if (showTip) _infoBar?.Show("获取用户信息", $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - 获取用户信息失败：{user?.Message ?? "未知错误"}", "Error");
            }
        }

        /// <summary>
        /// 设置WebToken命令
        /// </summary>
        [RelayCommand]
        private async Task SetWebToken()
        {
            try
            {
                var inputWindow = new InputWindow("请输入您的WebToken（8-20个字符）");
                inputWindow.ShowDialog();

                if (inputWindow.IsOK && !string.IsNullOrEmpty(inputWindow.InputText))
                {
                    string webToken = inputWindow.InputText.Trim();

                    // 验证WebToken长度
                    if (webToken.Length < 8 || webToken.Length > 20)
                    {
                        Utils.ShowMessage("设置失败", "WebToken长度必须在8-20个字符之间");
                        return;
                    }

                    // 调用API设置WebToken
                    var result = await _api.SetWebTokenAsync(webToken);

                    if (result?.IsSuccess == true)
                    {
                        Utils.ShowMessage("设置成功", "WebToken已成功设置");
                        // 更新WebToken状态
                        WebTokenButtonText = "已设置WebToken";
                        WebTokenButtonEnabled = false;
                    }
                    else
                    {
                        string errorMsg = result?.Message ?? "未知错误";
                        Utils.ShowMessage("设置失败", errorMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                Utils.ShowMessage("设置异常", $"设置WebToken时出错: {ex.Message}");
            }
        }

        // 显示内测提示对话框
        private async Task ShowBetaTestDialog()
        {
            ContentDialogResult result = await _contentDialogService.ShowSimpleDialogAsync(
            new SimpleContentDialogCreateOptions()
            {
                Title = "您的账号没有参加内测！",
                Content = "当前的版本是内测版本，您需要在设置中加入内测体验计划才能接收到后续的内测更新！",
                PrimaryButtonText = "切换到设置",
                CloseButtonText = "我知道了",
            }
            );
            if (result is ContentDialogResult.Primary)
            {
                NavigationView.Navigate(typeof(Views.Pages.SettingsPage));
            }
        }

        #region View变量

        /// <summary>
        /// 用户信息 用户名
        /// </summary>
        [ObservableProperty]
        public object _info_UserName;

        /// <summary>
        /// 更改密码 新密码
        /// </summary>
        [ObservableProperty]
        private string _change_NewPassword = "";

        /// <summary>
        /// 更改密码 新密码重复
        /// </summary>
        [ObservableProperty]
        private string _change_NewPassword_Again = "";

        /// <summary>
        /// 更改密码 旧密码
        /// </summary>
        [ObservableProperty]
        private string _change_OldPassword = "";

        /// <summary>
        /// 更改密码 用户名
        /// </summary>
        [ObservableProperty]
        private string _change_Username;

        /// <summary>
        /// 用户信息 邮箱
        /// </summary>
        [ObservableProperty]
        private object _info_Email;

        /// <summary>
        /// 用户信息 ID
        /// </summary>
        [ObservableProperty]
        private object _info_Id;

        /// <summary>
        /// 用户信息 IP
        /// </summary>
        [ObservableProperty]
        private object _info_Ip;

        /// <summary>
        /// 用户信息 用户类别
        /// </summary>
        [ObservableProperty]
        private object _info_Status;

        /// <summary>
        /// 用户信息 到期时间
        /// </summary>
        [ObservableProperty]
        private object _info_Viptime;

        [ObservableProperty]
        private InfoBarModel _infoBar = new();

        /// <summary>
        /// 登录 密码
        /// </summary>
        [ObservableProperty]
        private string _load_Password = "";

        /// <summary>
        /// 登录 用户名
        /// </summary>
        [ObservableProperty]
        private string _load_Username;

        /// <summary>
        /// 公告
        /// </summary>
        [ObservableProperty]
        private string _notice;

        /// <summary>
        /// 注册 验证码
        /// </summary>
        [ObservableProperty]
        private string _reg_Code;

        /// <summary>
        /// 注册 邮箱
        /// </summary>
        [ObservableProperty]
        private string _reg_Email;

        /// <summary>
        /// 注册 卡密
        /// </summary>
        [ObservableProperty]
        private string _reg_Kami = "";

        /// <summary>
        /// 注册 密码
        /// </summary>
        [ObservableProperty]
        private string _reg_Password = "";

        /// <summary>
        /// 注册用户名
        /// </summary>
        [ObservableProperty]
        private string _reg_Username;

        /// <summary>
        /// 重置密码 验证码
        /// </summary>
        [ObservableProperty]
        private string _reset_Code;

        /// <summary>
        /// 重置密码 邮箱
        /// </summary>
        [ObservableProperty]
        private string _reset_Email;

        /// <summary>
        /// 重置密码 用户名
        /// </summary>
        [ObservableProperty]
        private string _reset_Username;

        /// <summary>
        /// 更新日志
        /// </summary>
        [ObservableProperty]
        private string _updataLog;

        /// <summary>
        /// 充值卡密 卡密
        /// </summary>
        [ObservableProperty]
        private string _useKami_Kami = "";

        /// <summary>
        /// 充值卡密 用户名
        /// </summary>
        [ObservableProperty]
        private string _useKami_User = "";

        /// <summary>
        /// 充值卡密 重复用户名
        /// </summary>
        [ObservableProperty]
        private string _useKami_User_Again = "";

        /// <summary>
        /// 验证码框
        /// </summary>
        [ObservableProperty]
        private object _verificationCodeImg;

        /// <summary>
        /// 验证码对象
        /// </summary>
        private EasyCaptcha captcha;

        #endregion View变量

        /// <summary>
        /// 显示积分记录对话框
        /// </summary>
        [RelayCommand]
        private async Task ShowPointsRecords()
        {
            try
            {
                // 构建对话框内容
                System.Windows.Controls.TextBlock content = new System.Windows.Controls.TextBlock
                {
                    TextWrapping = TextWrapping.Wrap
                };

                if (Info_PointsRecords.Count > 0)
                {
                    StringBuilder sb = new StringBuilder();
                    sb.AppendLine($"当前积分：{Info_Points}");
                    sb.AppendLine("\n积分变更记录：");

                    foreach (var record in Info_PointsRecords)
                    {
                        sb.AppendLine($"时间：{record.CreateTime}");
                        sb.AppendLine($"卡密：{record.KamiCode}");
                        sb.AppendLine($"添加天数：{record.DaysAdded}");
                        sb.AppendLine($"添加积分：{record.PointsAdded}");
                        sb.AppendLine("------------------------");
                    }

                    content.Text = sb.ToString();
                }
                else
                {
                    content.Text = "暂无积分记录";
                }

                content.Text += Points_Info;

                // 显示对话框
                await _contentDialogService.ShowSimpleDialogAsync(
                new SimpleContentDialogCreateOptions
                {
                    Title = "积分记录",
                    Content = content,
                    CloseButtonText = "关闭"
                });
            }
            catch (Exception ex)
            {
                XLogger.Error($"显示积分记录失败: {ex.Message}");
                _infoBar?.Show("错误", $"显示积分记录失败: {ex.Message}", "Error");
            }
        }

        /// <summary>
        /// 显示用户权益对话框
        /// </summary>
        [RelayCommand]
        private async Task ShowUserRightsRecords()
        {
            try
            {
                // 构建对话框内容
                System.Windows.Controls.TextBlock content = new System.Windows.Controls.TextBlock
                {
                    TextWrapping = TextWrapping.Wrap
                };

                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"当前积分：{Info_Points}");
                sb.AppendLine("\n账号拥有如下权益：");

                int count = 0;
                foreach (var item in UserRights)
                {
                    if (Info_Points >= item.Key)
                    {
                        count++;
                        sb.AppendLine($"★{item.Value}★");
                    }
                    else
                        break;
                }

                sb.AppendLine("\n----------即将拥有的权益----------");

                bool hasUpcomingRights = false;
                foreach (var item in UserRights.OrderBy(x => x.Key))
                {
                    if (Info_Points < item.Key)
                    {
                        hasUpcomingRights = true;
                        sb.AppendLine($"{item.Value}（需要{item.Key}积分，还差{item.Key - Info_Points}积分）");
                    }
                }

                if (!hasUpcomingRights)
                    sb.AppendLine("已拥有当前蛋定提供的全部权益！");

                sb.AppendLine(Points_Info);

                content.Text = sb.ToString();

                // 显示对话框
                await _contentDialogService.ShowSimpleDialogAsync(
                new SimpleContentDialogCreateOptions
                {
                    Title = "账号权益",
                    Content = content,
                    CloseButtonText = "关闭"
                });
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取账号权益失败: {ex.Message}");
                _infoBar?.Show("错误", $"获取账号权益失败: {ex.Message}", "Error");
            }
        }

        /// <summary>
        /// 冷静方法
        /// </summary>
        /// <param name="time"></param>
        private async void StopButtonSameTime(string button, int time = 2000)
        {
            if (button == "reg")
            {
                RegSendCodeButton = false;
                await Task.Delay(time);
                RegSendCodeButton = true;
            }
            else if (button == "reset")
            {
                ResetSendCodeButton = false;
                await Task.Delay(time);
                ResetSendCodeButton = true;
            }
        }

        /// <summary>
        /// 获取一言API内容
        /// </summary>
        private async Task UpdateWelcomeText()
        {
            try
            {
                var response = await _httpClient.GetStringAsync("https://v1.hitokoto.cn/?c=f&encode=text");
                WelcomeText = response;
            }
            catch
            {
                WelcomeText = "欢迎使用蛋定助手！";
            }
        }
    }

    /// <summary>
    /// 转换器控制登录状态到布尔值
    /// </summary>
    public class LoginStatusToBoolConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            string showView = value as string;
            return showView == "已登录";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 拼接字符串类
    /// </summary>
    public class MultiStringConcatConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            // Combine all values into a single string
            return string.Join("", values.Select(v => v?.ToString()));
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 转换器控制显示布局
    /// </summary>
    public class ViewVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            Debug.WriteLine(value);
            Debug.WriteLine(parameter);

            string viewName = parameter as string;
            string showView = value as string;

            if (viewName == showView)
            {
                Debug.WriteLine(1);
                return Visibility.Visible;
            }
            else
            {
                Debug.WriteLine(2);
                return Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}