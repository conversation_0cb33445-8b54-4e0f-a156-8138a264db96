using XHelper;

namespace DanDing1.Commands
{
    /// <summary>
    /// 游戏1相关命令处理类
    /// </summary>
    internal class Game1Commands : BaseCommand
    {
        private Test_DebugPics? _debugPics;

        public override void Execute(string[] parameters)
        {
            if (!ValidateParameterCount(parameters, 4))
                return;

            if (!ValidateGame1Config())
                return;

            bool isGalleryCommand = (parameters[2] == "图库" || parameters[2] == "pics");
            if (!isGalleryCommand)
            {
                XLogger.Error("无效的图库命令参数");
                return;
            }

            HandleGalleryCommand(parameters[3]);
        }

        /// <summary>
        /// 验证游戏1配置
        /// </summary>
        private bool ValidateGame1Config()
        {
            if (GlobalData.Instance.Game1RunningConfig == null)
            {
                XLogger.Error("您没有激活游戏1的设置布局，请切换到游戏1界面激活后再来试试！");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 处理图库命令
        /// </summary>
        private void HandleGalleryCommand(string parameter)
        {
            switch (parameter)
            {
                case "1":
                    StartDebugPics();
                    break;
                case "0":
                    StopDebugPics();
                    break;
                default:
                    XLogger.Error("无效的图库开关参数");
                    break;
            }
        }

        /// <summary>
        /// 启动调试图库
        /// </summary>
        private void StartDebugPics()
        {
            _debugPics ??= new Test_DebugPics();
            _debugPics.Start(GlobalData.Instance.Game1RunningConfig!.SelectHwnd);
        }

        /// <summary>
        /// 停止调试图库
        /// </summary>
        private void StopDebugPics()
        {
            _debugPics?.Stop();
        }
    }
}