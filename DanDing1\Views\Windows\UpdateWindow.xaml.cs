﻿using System.Windows.Shapes;
using XHelper;
using System;
using System.IO;
using System.Windows;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows.Media.Animation;
using System.Windows.Controls;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// UpdateWindow.xaml 的交互逻辑
    /// </summary>
    public partial class UpdateWindow : Window
    {
        /// <summary>
        /// 下载地址
        /// </summary>
        private string Url = "";

        private string newVersion = "";
        private const string CONFIG_FILE = "update_config.json";

        public static bool ShouldShowUpdateWindow(string version)
        {
            try
            {
                if (File.Exists(CONFIG_FILE))
                {
                    string json = File.ReadAllText(CONFIG_FILE);
                    var config = System.Text.Json.JsonSerializer.Deserialize<UpdateConfig>(json);
                    if (config != null)
                    {
                        // 检查是否跳过此版本
                        if (config.SkippedVersion == version)
                            return false;

                        // 检查是否在提醒时间之前
                        if (config.RemindDate.HasValue && DateTime.Now < config.RemindDate.Value)
                            return false;
                    }
                }
            }
            catch { }
            return true;
        }

        public UpdateWindow(string nowver = "", string newver = "", string log = "", string url = "")
        {
            InitializeComponent();
            this.newVersion = newver == "" ? GlobalData.Instance.appConfig.Info.Ver : newver;
            this.NewVer.Text = "Ver " + this.newVersion;
            this.NowVer.Text = "Ver " + (nowver == "" ? GlobalData.Instance.appConfig.Info.Now_Ver : nowver);
            this.UpdataLog.Text = (log == "" ? GlobalData.Instance.appConfig.Info.UpdataLog : log);
            this.Url = (url == "" ? GlobalData.Instance.appConfig.Info.NewVerUrl : url);
        }

        private void SkipVersionButton_Click(object sender, RoutedEventArgs e)
        {
            SaveUpdateConfig(new UpdateConfig
            {
                SkippedVersion = this.newVersion,
                LastSkipDate = DateTime.Now
            });
            this.Close();
        }

        private void RemindTomorrowButton_Click(object sender, RoutedEventArgs e)
        {
            SaveUpdateConfig(new UpdateConfig
            {
                RemindDate = DateTime.Now.AddDays(1),
                LastSkipDate = DateTime.Now
            });
            this.Close();
        }

        private void ManualDownloadButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = this.Url,
                    UseShellExecute = true
                });
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开下载链接失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveUpdateConfig(UpdateConfig config)
        {
            try
            {
                string json = System.Text.Json.JsonSerializer.Serialize(config);
                File.WriteAllText(CONFIG_FILE, json);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private class UpdateConfig
        {
            public string SkippedVersion { get; set; }
            public DateTime? RemindDate { get; set; }
            public DateTime LastSkipDate { get; set; }
        }

        private async void InstallButton_Click(object sender, RoutedEventArgs e)
        {
            File.Delete(".\\runtimes\\update.zip");
            InstallButton.IsEnabled = false;
            int ReTry = 1;

        Re:
            DownloadProgressBar.Value = 0;
            DownloadProgressBar.Maximum = 100;
            if (!File.Exists(@".\runtimes\update.zip"))
            {
                if (ReTry > 3)
                    throw new Exception("网络异常！无法下载最新的软件更新包！请检查网络环境正常后重试！");

                double lastProgress = 0;
                Progress<double> progress = new(p =>
                {
                    // 只有当进度变化超过0.1%时才更新显示
                    if (Math.Abs(p - lastProgress) >= 0.1)
                    {
                        InstallButton.Content = $"下载进度{ReTry}:{p:F1}%";
                        lastProgress = p;

                        // 创建平滑的动画效果
                        DoubleAnimation animation = new DoubleAnimation
                        {
                            To = p,
                            Duration = TimeSpan.FromMilliseconds(100), // 缩短动画时间以提高响应度
                            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                        };
                        DownloadProgressBar.BeginAnimation(ProgressBar.ValueProperty, animation);
                    }
                });
                await XNet.DownloadFileAsync(Url, @".\runtimes\update.zip", progress);
                ReTry++;
                goto Re;
            }
            InstallButton.Content = $"1S后执行覆盖重启...";
            await Task.Delay(1200);
            InstallAndRestart();
        }

        private void InstallAndRestart()
        {
            try
            {
                string updateZipPath = @".\runtimes\update.zip";
                string extractPath = System.IO.Path.Combine(System.IO.Path.GetTempPath(), "DanDingUpdate");
                string appPath = @".\DanDing1.exe";
                string logFilePath = System.IO.Path.Combine(System.IO.Path.GetTempPath(), "DanDingUpdate.log");

                // 确保更新文件存在
                if (!File.Exists(updateZipPath))
                {
                    MessageBox.Show($"更新文件不存在: {updateZipPath}", "更新失败", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 创建日志文件
                using (StreamWriter logWriter = new StreamWriter(logFilePath, false, System.Text.Encoding.UTF8))
                {
                    logWriter.WriteLine($"开始更新过程 - {DateTime.Now}");
                    logWriter.WriteLine($"更新文件路径: {updateZipPath}");
                    logWriter.WriteLine($"文件大小: {new FileInfo(updateZipPath).Length} 字节");
                    logWriter.WriteLine($"应用程序路径: {appPath}");
                    logWriter.WriteLine($"解压路径: {extractPath}");

                    try
                    {
                        // 清理并创建解压目录
                        if (Directory.Exists(extractPath))
                        {
                            logWriter.WriteLine("清理旧的解压目录...");
                            Directory.Delete(extractPath, true);
                        }
                        Directory.CreateDirectory(extractPath);
                        logWriter.WriteLine("创建解压目录成功");

                        // 直接解压文件
                        logWriter.WriteLine("开始解压文件...");
                        System.IO.Compression.ZipFile.ExtractToDirectory(updateZipPath, extractPath);
                        logWriter.WriteLine("解压文件成功");

                        // 创建批处理文件用于复制文件和重启应用
                        string batchFilePath = System.IO.Path.Combine(System.IO.Path.GetTempPath(), "update_final.bat");
                        logWriter.WriteLine($"创建最终更新批处理文件: {batchFilePath}");

                        string batchScript = $@"
                            @echo off
                            chcp 65001 > nul
                            echo 开始最终更新过程 >> ""{logFilePath}""
                            timeout /t 2 > nul

                            echo 尝试结束进程... >> ""{logFilePath}""
                            taskkill /f /im {System.IO.Path.GetFileName(appPath)} >> ""{logFilePath}"" 2>&1

                            echo 复制文件... >> ""{logFilePath}""
                            xcopy ""{extractPath}\*.*"" ""{System.IO.Path.GetDirectoryName(appPath)}"" /E /Y /C /H /R >> ""{logFilePath}"" 2>&1

                            echo 清理临时文件... >> ""{logFilePath}""
                            rmdir /s /q ""{extractPath}"" >> ""{logFilePath}"" 2>&1
                            del ""{updateZipPath}"" >> ""{logFilePath}"" 2>&1

                            echo 启动应用程序... >> ""{logFilePath}""
                            start """" ""{appPath}"" >> ""{logFilePath}"" 2>&1

                            echo 更新完成 >> ""{logFilePath}""
                            del ""%~f0""
                            ";

                        // 写入批处理文件
                        File.WriteAllText(batchFilePath, batchScript, System.Text.Encoding.UTF8);
                        logWriter.WriteLine("批处理文件创建成功");

                        // 启动批处理文件
                        logWriter.WriteLine("启动最终更新批处理...");
                        Process.Start(new ProcessStartInfo()
                        {
                            FileName = batchFilePath,
                            CreateNoWindow = false,
                            UseShellExecute = true,
                            Verb = "runas"
                        });

                        logWriter.WriteLine("更新脚本已启动，应用程序即将关闭");
                    }
                    catch (Exception ex)
                    {
                        logWriter.WriteLine($"更新过程中发生错误: {ex.Message}");
                        logWriter.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                        throw; // 重新抛出异常以便外层捕获
                    }
                }

                // 关闭应用程序
                Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新过程中发生错误: {ex.Message}", "更新失败", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}