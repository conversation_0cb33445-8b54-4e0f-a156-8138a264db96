﻿using DamoControlKit;
using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks;
using ScriptEngine.Tasks.Base;
using XHelper;

namespace ScriptEngine
{
    /// <summary>
    /// 配置数据存储类
    /// </summary>
    public class DDBuilder
    {
#pragma warning disable CS8618

        /// <summary>
        /// 用于方法同步的信号量
        /// </summary>
        private readonly SemaphoreSlim _checkLock = new SemaphoreSlim(1, 1);

        public DDBuilder()
        {
            Data = new();
        }

#pragma warning restore CS8618

        /// <summary>
        /// 游戏日志委托类型，用于DanDing1.exe输出日志
        /// </summary>
        /// <param name="gameId">游戏ID</param>
        /// <param name="message">日志消息</param>
        public delegate void GameLogDelegate(string message);

        /// <summary>
        /// 更新视图游戏当前任务委托类型
        /// </summary>
        /// <param name="gameId">游戏ID</param>
        /// <param name="taskName">任务名称</param>
        public delegate void UpdateGameTaskDelegate(string taskName);

        /// <summary>
        /// 超级多开_当前游戏任务更新显示的委托
        /// </summary>
        public UpdateGameTaskDelegate? OnUpdateGameTask { get; private set; }

        /// <summary>
        /// 更新视图游戏当前的游戏句柄
        /// </summary>
        /// <param name="gameId">游戏ID</param>
        /// <param name="taskName">任务名称</param>
        public delegate void UpdateGameHwndDelegate(int MumuHwnd, int GameHwnd);

        /// <summary>
        /// 超级多开_当前游戏任务更新mumu句柄的委托
        /// </summary>
        public UpdateGameHwndDelegate? OnUpdateGameHwnd { get; private set; }

        /// <summary>
        /// 发送用户通知委托类型
        /// </summary>
        /// <param name="title">通知标题</param>
        /// <param name="content">通知内容</param>
        /// <returns>发送是否成功</returns>
        public delegate Task<bool> SendNotificationDelegate(string title, string content);

        /// <summary>
        /// 发送用户通知的委托
        /// </summary>
        public SendNotificationDelegate? OnSendNotification { get; private set; }

        /// <summary>
        /// 屏幕截图录制器
        /// </summary>
        internal ScreenshotRecorder? _recorder { get; set; } = null;

        /// <summary>
        /// MuMu控制器
        /// </summary>
        internal MuMu.MuMu MumuManager { get; set; } = new();

        /// <summary>
        /// 是否需要执行定时任务
        /// </summary>
        internal bool PendingTimerTask { get; set; } = false;

        /// <summary>
        /// 全局_反检测对象
        /// </summary>
        internal AntiDetect? Anti { get; set; }

        /// <summary>
        /// 绑定设置
        /// </summary>
        internal BindModel BindSetting { get; set; }

        /// <summary>
        /// 其它配置
        /// </summary>
        internal G_Data Data { get; set; }

        /// <summary>
        /// 用户游戏-脚本设置
        /// </summary>
        internal GameSettingsModel GameSetting { get; set; }

        /// <summary>
        /// 日志分类展示类
        /// </summary>
        internal Log Log { get; set; }

        /// <summary>
        /// 共享数据
        /// </summary>
        public Data_Share ShareData { get; set; }

        /// <summary>
        /// 模拟器参数
        /// </summary>
        internal SimulatorModel Simulator { get; set; }

        /// <summary>
        /// 任务列表
        /// </summary>
        internal TaskConfigsModel TaskLists { get; set; }

        /// <summary>
        /// 定时任务对象
        /// </summary>
        internal TimerTask? TimerTask { get; set; }

        /// <summary>
        /// 用户偏好设置
        /// </summary>
        internal Dictionary<string, object> UserConfigs { get; set; }

        /// <summary>
        /// 添加Data
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public DDBuilder AddData(string key, object value)
        {
            Data.Set(key, value);
            return this;
        }

        public bool Check(string code)
        {
            // 获取同步锁
            _checkLock.Wait();
            try
            {
                //检查插件注册状态
                if (!DamoKit.IsReg)
                    if (!DamoKit.Reg(code, out string e_str))
                    {
                        throw new Exception(e_str);
                    }
                ;

                if (Simulator is null) throw new Exception("模拟器参数配置未设置！");
                if (BindSetting is null) throw new Exception("模拟器绑定配置未设置！");
                BindSetting.Hwnd = Simulator.Hwnd;
                if (!BindSetting.IsReady) return false;
                if (TaskLists is null) throw new Exception("未配置任务清单！");
                return true;
            }
            finally
            {
                // 释放锁
                _checkLock.Release();
            }
        }

        public async Task<bool> CheckAsync(string code)
        {
            // 获取异步锁
            await _checkLock.WaitAsync();
            try
            {
                //检查插件注册状态
                if (!DamoKit.IsReg)
                    try
                    {
                        await DamoKit.RegAsync(code);
                        XLogger.Debug("插件版本：" + new dmsoft().Ver());
                    }
                    catch (Exception) { throw; }

                if (Simulator is null) throw new Exception("模拟器参数配置未设置！");
                if (BindSetting is null) throw new Exception("模拟器绑定配置未设置！");
                BindSetting.Hwnd = Simulator.Hwnd;
                if (!BindSetting.IsReady) return false;
                if (TaskLists is null) throw new Exception("未配置任务清单！");
                return true;
            }
            finally
            {
                // 释放锁
                _checkLock.Release();
            }
        }

        /// <summary>
        /// 初始化日志分类输出
        /// </summary>
        /// <returns></returns>
        public DDBuilder InitLog(string logClassName, GameLogDelegate? logDelegate = null)
        {
            Log = new(logClassName, logDelegate);
            return this;
        }

        /// <summary>
        /// 设置绑定模式
        /// </summary>
        /// <param name="display"></param>
        /// <param name="keypad"></param>
        /// <param name="mouse"></param>
        /// <returns></returns>
        public DDBuilder SetBindSetting(Display display, Keypad keypad, Mouse mouse, int mode = 0, string pub = "")
        {
            BindSetting = new BindModel()
            {
                Display = display,
                Hwnd = -1,
                Keypad = keypad,
                Mode = mode,
                Mouse = mouse,
                Public = pub
            };
            return this;
        }

        /// <summary>
        /// 设置绑定模式
        /// </summary>
        /// <param name="bindModel"></param>
        /// <returns></returns>
        public DDBuilder SetBindSetting(BindModel bindModel)
        {
            BindSetting = bindModel;
            return this;
        }

        /// <summary>
        /// 设置所有委托
        /// </summary>
        /// <param name="updateGameTask">更新游戏当前任务委托_适用超级多开</param>
        /// <returns></returns>
        public DDBuilder SetDelegates(UpdateGameTaskDelegate updateGameTask)
        {
            OnUpdateGameTask = updateGameTask;
            return this;
        }

        /// <summary>
        /// 设置更新句柄委托
        /// </summary>
        /// <param name="updateGameHwnd">更新游戏句柄委托_适用超级多开</param>
        /// <returns></returns>
        public DDBuilder SetDelegates(UpdateGameHwndDelegate updateGameHwnd)
        {
            OnUpdateGameHwnd = updateGameHwnd;
            return this;
        }

        /// <summary>
        /// 设置全部委托
        /// </summary>
        /// <param name="updateGameTask">更新游戏当前任务委托_适用超级多开</param>
        /// <param name="updateGameHwnd">更新游戏句柄委托_适用超级多开</param>
        /// <returns></returns>
        public DDBuilder SetDelegates(UpdateGameTaskDelegate updateGameTask, UpdateGameHwndDelegate updateGameHwnd)
        {
            OnUpdateGameTask = updateGameTask;
            OnUpdateGameHwnd = updateGameHwnd;
            return this;
        }

        /// <summary>
        /// 设置多个委托（任务更新、句柄更新、通知发送）
        /// </summary>
        /// <param name="updateGameTask">更新游戏当前任务委托</param>
        /// <param name="updateGameHwnd">更新游戏句柄委托</param>
        /// <param name="sendNotification">发送用户通知委托</param>
        /// <returns>当前DDBuilder实例</returns>
        public DDBuilder SetDelegates(UpdateGameTaskDelegate updateGameTask, UpdateGameHwndDelegate updateGameHwnd, SendNotificationDelegate sendNotification)
        {
            OnUpdateGameTask = updateGameTask;
            OnUpdateGameHwnd = updateGameHwnd;
            OnSendNotification = sendNotification;
            return this;
        }

        /// <summary>
        /// 设置游戏过程中配置
        /// </summary>
        /// <returns></returns>
        public DDBuilder SetGameSettings(GameSettingsModel setting)
        {
            GameSetting = setting;
            return this;
        }

        /// <summary>
        /// 设置MuMu模拟器索引
        /// </summary>
        /// <param name="index">模拟器索引，用于启动特定模拟器实例</param>
        /// <returns>当前DDBuilder实例</returns>
        public DDBuilder SetMuMuIndex(int index)
        {
            if (index < 0)
                throw new ArgumentException("模拟器索引不能为负数，请保证当前mumu模拟器版本≥4.0.0.3179！", nameof(index));

            // 在全局数据存储中保存MuMu索引
            Data.Set("MuMuIndex", index);
            return this;
        }

        /// <summary>
        /// 设置图库版本
        /// 并初始化
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public DDBuilder SetPicsVer(string ver)
        {
            DynamicData.SetHost(Data.Get("Base_Url").ToString() + "/DD/DD_Core/");
            DynamicData.Init(ver);
            return this;
        }

        /// <summary>
        /// 设置图库版本
        /// 并初始化
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SetPicsVerAsync(string server, string ver)
        {
            try
            {
                if (server != "本地")
                    DynamicData.SetHost(Data.Get("Base_Url").ToString() + "DD/DD_Core/");
                await DynamicData.InitAsync(server, ver);
            }
            catch (Exception e)
            {
                XLogger.SaveException(e);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 设置模拟器
        /// </summary>
        /// <param name="typeName">mumu或雷电</param>
        /// <param name="hwnd">窗口句柄</param>
        /// <returns></returns>
        public DDBuilder SetSimulator(string typeName, int hwnd)
        {
            if (string.IsNullOrEmpty(typeName) || hwnd == 0)
                throw new Exception("启动前 模拟器相关信息初始化错误！");
            Simulator = new()
            {
                Type = typeName,
                Hwnd = hwnd
            };
            return this;
        }

        /// <summary>
        /// 设置执行任务清单
        /// </summary>
        /// <returns></returns>
        public DDBuilder SetTaskList(TaskConfigsModel taskLists)
        {
            TaskLists = taskLists;
            return this;
        }

        /// <summary>
        /// 设置用户偏好设置
        /// </summary>
        /// <param name="pairs"></param>
        /// <returns></returns>
        public DDBuilder SetUserConfigs(Dictionary<string, object> pairs)
        {
            UserConfigs = pairs;
            return this;
        }
    }
}