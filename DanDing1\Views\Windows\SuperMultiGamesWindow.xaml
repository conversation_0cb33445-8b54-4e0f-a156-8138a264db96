﻿<Window x:Class="DanDing1.Views.Windows.SuperMultiGamesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:localControl="clr-namespace:DanDing1.Views.UserControls"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        xmlns:sysbool="clr-namespace:System;assembly=System.Runtime"
        xmlns:super="clr-namespace:DanDing1.Models.Super"
        xmlns:viewModels="clr-namespace:DanDing1.ViewModels.Windows"
        x:Name="SuperMultiGamesWindowInstance"
        Title="{Binding ViewModel.ApplicationTitle}"
        Height="770"
        Width="1400"
        d:DataContext="{d:DesignInstance local:SuperMultiGamesWindow,IsDesignTimeCreatable=True}"
        ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
        ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        ScrollViewer.CanContentScroll="False"
        WindowStartupLocation="CenterScreen"
        Closing="Window_Closing"
        Loaded="Window_Loaded"
        mc:Ignorable="d">
    <Window.Resources>
        <ObjectDataProvider x:Key="IdentityValues"
                            MethodName="GetValues"
                            ObjectType="{x:Type sys:Enum}">
            <ObjectDataProvider.MethodParameters>
                <x:Type TypeName="super:Team_Identity_Unit"/>
            </ObjectDataProvider.MethodParameters>
        </ObjectDataProvider>

        <!-- BooleanToVisibilityConverter -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- 卡片样式 -->
        <Style x:Key="CardStyle"
               TargetType="Border">
            <Setter Property="Background"
                    Value="{DynamicResource ControlFillColorDefaultBrush}"/>
            <Setter Property="BorderBrush"
                    Value="{DynamicResource ControlElevationBorderBrush}"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="CornerRadius"
                    Value="8"/>
            <Setter Property="Padding"
                    Value="12"/>
            <Setter Property="Margin"
                    Value="0,0,0,10"/>
        </Style>

        <!-- 卡片标题样式 -->
        <Style x:Key="CardTitleStyle"
               TargetType="TextBlock">
            <Setter Property="FontSize"
                    Value="16"/>
            <Setter Property="FontWeight"
                    Value="SemiBold"/>
            <Setter Property="Margin"
                    Value="0,0,0,8"/>
        </Style>
    </Window.Resources>

    <!-- 主布局分为左右两部分 -->
    <Grid x:Name="MainGrid">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="930"
                              MinWidth="360"
                              MaxWidth="916"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"
                              MinWidth="300"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧区域 - 包含任务控制、游戏列表、中控面板和日志 -->
        <ScrollViewer Grid.Column="0"
                      VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="10">
                <!-- 任务控制区 -->
                <localControl:AddTaskControl Margin="0 0 0 10"
                                             x:Name="AddTask"
                                             Visibility="{Binding DataContext.ViewModel.IsTaskControlVisible, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <!-- 游戏列表卡片 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <!-- 标题栏和刷新按钮 -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="75"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock  Grid.Column="0"
                                        Margin="0 3 0 0"
                                        Text="游戏列表"
                                        Style="{StaticResource CardTitleStyle}"/>
                            <ui:Button Grid.Column="1"
                                       HorizontalAlignment="Left"
                                       VerticalAlignment="Center"
                                       ToolTip="刷新模拟器数据"
                                       Appearance="Secondary"
                                       Command="{Binding ViewModel.RefreshMuMuDataCommand}"
                                       CommandParameter="True"
                                       Padding="8,4">
                                <ui:SymbolIcon Symbol="ArrowClockwise12"/>
                            </ui:Button>
                        </Grid>
                        <ui:DataGrid x:Name="DataGrid"
                                     Height="300"
                                     ItemsSource="{Binding ViewModel.SuperMultiGame_DataModelCollection, Mode=TwoWay}"
                                     SelectionMode="Single"
                                     SelectedItem="{Binding ViewModel.SelectedGameModel, Mode=TwoWay}"
                                     SelectionChanged="DataGrid_SelectionChanged"
                                     AutoGenerateColumns="False"
                                     CanUserAddRows="False"
                                     CanUserDeleteRows="False"
                                     PreviewMouseRightButtonDown="DataGrid_PreviewMouseRightButtonDown"
                                     CanUserReorderColumns="False">
                            <ui:DataGrid.ContextMenu>
                                <ContextMenu>
                                    <MenuItem Header="删除选中"
                                              Command="{Binding ViewModel.DeleteSelectedGameCommand}"
                                              CommandParameter="{Binding ViewModel.SelectedGameModel}">
                                        <MenuItem.Icon>
                                            <ui:SymbolIcon Symbol="Delete24"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                    <MenuItem Header="刷新选中"
                                              Command="{Binding ViewModel.RefreshSelectedGameCommand}"
                                              CommandParameter="{Binding ViewModel.SelectedGameModel}">
                                        <MenuItem.Icon>
                                            <ui:SymbolIcon Symbol="ArrowClockwise24"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                    <MenuItem Header="一键配置模拟器"
                                              Command="{Binding ViewModel.ConfigureSelectedSimulatorCommand}"
                                              CommandParameter="{Binding ViewModel.SelectedGameModel}">
                                        <MenuItem.Icon>
                                            <ui:SymbolIcon Symbol="Settings24"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                    <MenuItem Header="启动模拟器"
                                              Command="{Binding ViewModel.LaunchSimulatorCommand}"
                                              CommandParameter="{Binding ViewModel.SelectedGameModel}">
                                        <MenuItem.Icon>
                                            <ui:SymbolIcon Symbol="Play24"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                    <MenuItem Header="启动阴阳师"
                                              Command="{Binding ViewModel.LaunchYysCommand}"
                                              CommandParameter="{Binding ViewModel.SelectedGameModel}">
                                        <MenuItem.Icon>
                                            <ui:SymbolIcon Symbol="Games24"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                </ContextMenu>
                            </ui:DataGrid.ContextMenu>
                            <ui:DataGrid.Columns>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="ID"
                                                    Binding="{Binding GameId}"
                                                    Width="45"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="类型"
                                                    Binding="{Binding SimulatorType}"
                                                    Width="68"/>
                                <DataGridTextColumn Header="游戏名称"
                                                    Binding="{Binding GameName}"
                                                    Width="120"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="MuMu句柄"
                                                    Binding="{Binding MumuHandle}"
                                                    Width="100"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="游戏句柄"
                                                    Binding="{Binding GameHandle}"
                                                    Width="100"/>
                                <DataGridTemplateColumn Header="身份"
                                                        Visibility="Hidden"
                                                        Width="78">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <ComboBox SelectedItem="{Binding Identity, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                      ItemsSource="{Binding Source={StaticResource IdentityValues}}"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="当前任务"
                                                    Binding="{Binding CurrentTask}"
                                                    Width="120"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="运行时长"
                                                    Binding="{Binding RunningDuration, Converter={StaticResource TimeSpanWithDaysConverter}}"
                                                    Width="85"/>
                                <DataGridTextColumn IsReadOnly="True"
                                                    Header="运行状态"
                                                    Binding="{Binding RunningStatus}"
                                                    Width="83"/>
                                <DataGridTemplateColumn Header="操作"
                                                        Width="*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <ui:Button Content="配置"
                                                           Margin="5 2 2 2"
                                                           Command="{Binding DataContext.ViewModel.SecondaryConfigCommand, RelativeSource={RelativeSource AncestorType=ui:DataGrid}}"
                                                           CommandParameter="{Binding}"
                                                           Padding="8,2"/>
                                                <ui:Button Command="{Binding DataContext.ViewModel.ToggleGameStatusCommand, RelativeSource={RelativeSource AncestorType=ui:DataGrid}}"
                                                           CommandParameter="{Binding}"
                                                           Margin="2 2 2 2"
                                                           Padding="8,2"
                                                           IsEnabled="{Binding IsButtonEnabled, UpdateSourceTrigger=PropertyChanged}">
                                                    <ui:Button.Style>
                                                        <Style TargetType="ui:Button"
                                                               BasedOn="{StaticResource {x:Type ui:Button}}">
                                                            <Setter Property="Content"
                                                                    Value="启动"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding RunningStatus}"
                                                                             Value="运行中">
                                                                    <Setter Property="Content"
                                                                            Value="停止"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding RunningStatus}"
                                                                             Value="启动中">
                                                                    <Setter Property="IsEnabled"
                                                                            Value="False"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </ui:Button.Style>
                                                </ui:Button>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </ui:DataGrid.Columns>
                        </ui:DataGrid>
                    </StackPanel>
                </Border>

                <!-- 中控面板卡片 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="中控面板"
                                   Style="{StaticResource CardTitleStyle}"/>

                        <!-- 游戏管理分类 -->
                        <TextBlock Text="游戏管理"
                                   FontWeight="SemiBold"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="5,5,0,2"/>

                        <!-- 游戏管理相关按钮 - 使用WrapPanel实现自动换行 -->
                        <WrapPanel Orientation="Horizontal">
                            <ui:Button Content="配置模拟器"
                                       Command="{Binding ViewModel.ConfigureSimulatorCommand}"
                                       Margin="5"
                                       Height="50"
                                       Width="100"
                                       HorizontalContentAlignment="Center"
                                       VerticalContentAlignment="Center"/>

                            <ui:Button Content="添加游戏"
                                       Command="{Binding ViewModel.AddGameCommand}"
                                       Margin="5"
                                       Height="50"
                                       Width="100"
                                       HorizontalContentAlignment="Center"
                                       VerticalContentAlignment="Center"/>

                            <ui:Button Content="添加所有&#x0a;  模拟器"
                                       Command="{Binding ViewModel.AddAllSimulatorsCommand}"
                                       Margin="5"
                                       Height="50"
                                       Width="100"
                                       HorizontalContentAlignment="Center"
                                       VerticalContentAlignment="Center"/>

                            <ui:Button Content="添加运行&#x0a;  模拟器"
                                       Command="{Binding ViewModel.AddOnlyRunningSimulatorsCommand}"
                                       Margin="5"
                                       Height="50"
                                       Width="100"
                                       HorizontalContentAlignment="Center"
                                       VerticalContentAlignment="Center"/>

                            <ui:Button Content="保存列表"
                                       Command="{Binding ViewModel.SaveGameListCommand}"
                                       Margin="5"
                                       Height="50"
                                       Width="100"
                                       HorizontalContentAlignment="Center"
                                       VerticalContentAlignment="Center"/>
                        </WrapPanel>

                        <!-- 杂项分类标题 -->
                        <TextBlock Text="杂项控制"
                                   FontWeight="SemiBold"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="5,15,0,2"/>

                        <!-- 杂项控制相关按钮 - 使用WrapPanel实现自动换行 -->
                        <WrapPanel Orientation="Horizontal">
                            <ui:Button Content="启动全部"
                                       Command="{Binding ViewModel.StartAllCommand}"
                                       Margin="5"
                                       Height="50"
                                       Width="100"
                                       HorizontalContentAlignment="Center"
                                       VerticalContentAlignment="Center"/>

                            <ui:Button Content="停止全部"
                                       Command="{Binding ViewModel.StopAllCommand}"
                                       Margin="5"
                                       Height="50"
                                       Width="100"
                                       HorizontalContentAlignment="Center"
                                       VerticalContentAlignment="Center"/>

                            <ui:Button Command="{Binding ViewModel.ToggleWindowTopMostCommand}"
                                       Margin="5"
                                       Height="50"
                                       Width="100"
                                       HorizontalContentAlignment="Center"
                                       VerticalContentAlignment="Center">
                                <ui:Button.Style>
                                    <Style TargetType="ui:Button"
                                           BasedOn="{StaticResource {x:Type ui:Button}}">
                                        <Setter Property="Content"
                                                Value="窗口置顶"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding ViewModel.IsWindowTopMost}"
                                                         Value="True">
                                                <Setter Property="Content"
                                                        Value="取消置顶"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ui:Button.Style>
                            </ui:Button>

                            <ui:Button Command="{Binding ViewModel.ToggleTaskControlCommand}"
                                       Margin="5"
                                       Height="50"
                                       Width="100"
                                       HorizontalContentAlignment="Center"
                                       VerticalContentAlignment="Center">
                                <ui:Button.Style>
                                    <Style TargetType="ui:Button"
                                           BasedOn="{StaticResource {x:Type ui:Button}}">
                                        <Setter Property="Content"
                                                Value="显示添加&#x0a;任务列表"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding ViewModel.IsTaskControlVisible}"
                                                         Value="True">
                                                <Setter Property="Content"
                                                        Value="隐藏添加&#x0a;任务列表"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ui:Button.Style>
                            </ui:Button>
                        </WrapPanel>
                    </StackPanel>
                </Border>

                <!-- 日志卡片 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="日志输出"
                                   Style="{StaticResource CardTitleStyle}"/>

                        <!-- 选项卡控件 -->
                        <TabControl x:Name="LogTabControl"
                                    Height="200"
                                    SelectedIndex="{Binding ViewModel.SelectedLogTabIndex, Mode=TwoWay}">
                            <TabControl.Resources>
                                <Style TargetType="TabItem">
                                    <Setter Property="Width"
                                            Value="Auto"/>
                                    <Setter Property="MinWidth"
                                            Value="80"/>
                                    <Setter Property="Padding"
                                            Value="8,4"/>
                                </Style>
                            </TabControl.Resources>
                            <TabControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel Orientation="Horizontal"/>
                                </ItemsPanelTemplate>
                            </TabControl.ItemsPanel>
                            <!-- 默认操作日志选项卡 -->
                            <TabItem Header="操作日志">
                                <ui:TextBox IsReadOnly="True"
                                            TextWrapping="Wrap"
                                            VerticalScrollBarVisibility="Auto"
                                            Text="{Binding ViewModel.LogOutput, Mode=OneWay}"
                                            BorderThickness="0"
                                            Padding="5"/>
                            </TabItem>

                            <!-- 使用代码生成动态选项卡 -->
                            <!-- 游戏日志选项卡将在代码中动态添加 -->
                        </TabControl>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- 可拖动分隔线 -->
        <GridSplitter Grid.Column="1"
                      Width="5"
                      HorizontalAlignment="Stretch"
                      VerticalAlignment="Stretch"
                      Background="{DynamicResource ControlElevationBorderBrush}"
                      ShowsPreview="False"
                      ResizeBehavior="PreviousAndNext"
                      ResizeDirection="Columns"
                      Margin="0"
                      Cursor="SizeWE">
            <GridSplitter.Template>
                <ControlTemplate TargetType="{x:Type GridSplitter}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{DynamicResource ControlElevationBorderBrush}"
                            BorderThickness="0,0,0,0"
                            Width="5">
                        <Rectangle Width="1"
                                   Fill="{DynamicResource TextFillColorTertiaryBrush}"
                                   HorizontalAlignment="Center"/>
                    </Border>
                </ControlTemplate>
            </GridSplitter.Template>
        </GridSplitter>

        <!-- 右侧区域 - 使用多游戏预览 -->
        <Grid Grid.Column="2"
              Margin="10">
            <Border Style="{StaticResource CardStyle}">
                <localControl:MultiGamePreviewControl x:Name="GamePreview"/>
            </Border>
        </Grid>

        <!-- 通知面板 - 覆盖在最上层 -->
        <localControl:NotificationsPanel Grid.ColumnSpan="3"
                                         x:Name="NotificationsPanel"
                                         Panel.ZIndex="1000"
                                         HorizontalAlignment="Right"
                                         VerticalAlignment="Bottom"
                                         IsHitTestVisible="True"/>
    </Grid>
</Window>