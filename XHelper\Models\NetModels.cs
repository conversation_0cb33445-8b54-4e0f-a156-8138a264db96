﻿using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Text.Json.Serialization;
using OpenCvSharp.Detail;

namespace XHelper.Models
{
    /// <summary>
    /// 图库版本信息
    /// </summary>
    public class _PicVersion
    {
        public string Version { get; set; } = string.Empty;
    }

    /// <summary>
    /// 图库版本响应数据
    /// </summary>
    public class Response_PicVersionData : ResponseBaseData
    {
        public class _PicVersionResponse
        {
            public List<_PicVersion> Versions { get; set; } = new();
        }

        public new _PicVersionResponse? Data { get; set; }
    }

    /// <summary>
    /// 公告Data
    /// </summary>
    public class _InfoData
    {
        public int Id { get; set; }
        public string Notice { get; set; } = "";
        public string Updata { get; set; } = "";
        public string Ver { get; set; } = "";
        public string Url { get; set; } = "";
    }

    public class _LoginData
    {
        public string msg { get; set; } = "";
        public string username { get; set; } = "";
    }

    public class _DmData
    {
        public string code { get; set; } = "";
        public string k { get; set; } = "";
    }

    /// <summary>
    /// 注册请求模型
    /// </summary>
    public class Request_RegisterData
    {
        public string username { get; set; } = "";
        public string password { get; set; } = "";
        public string kami { get; set; } = "";
        public string user_email { get; set; } = "";
        public string qq { get; set; } = "";
        public string computer_Code { get; set; } = "";
        public string code { get; set; } = "";
    }

    /// <summary>
    /// 登录模型
    /// </summary>
    public class Request_LoginData
    {
        public string computer_Code { get; set; } = "";
        public string password { get; set; } = "";
        public string username { get; set; } = "";
    }

    /// <summary>
    /// 公告模型
    /// </summary>
    public class Response_InfoData : ResponseBaseData
    {
        public new _InfoData? Data { get; set; }
    }

    /// <summary>
    /// 内测公告模型
    /// </summary>
    public class Response_InfoBetaData : ResponseBaseData
    {
        public class InfoBetaData
        {
            public string BetaStr { get; set; } = "";
            public string BetaNotice { get; set; } = "";
            public string BetaUpdata { get; set; } = "";
            public string BetaVer { get; set; } = "";
            public string BetaUrl { get; set; } = "";
        }

        public new InfoBetaData? Data { get; set; }
    }

    /// <summary>
    /// 登录响应模型
    /// </summary>
    public class Response_LoginData : ResponseBaseData
    {
        public new _LoginData? Data { get; set; }
    }

    /// <summary>
    /// 大漠注册码响应模型
    /// </summary>
    public class Response_DmData : ResponseBaseData
    {
        public new _DmData? Data { get; set; }
    }

    /// <summary>
    /// 试用用户响应模型
    /// </summary>
    public class Response_FreeData : ResponseBaseData
    {
        public class _FreeData
        {
            public string stoptime { get; set; } = "";
            public string msg { get; set; } = "";
        }

        public new _FreeData? Data { get; set; }
    }

    /// <summary>
    /// 注册响应模型
    /// </summary>
    public class Response_RegData : ResponseBaseData
    {
        public class _RegData
        {
            public string username { get; set; } = "";
            public string vip_time { get; set; } = "";
            public string msg { get; set; } = "";
        }

        public new _RegData? Data { get; set; }
    }

    /// <summary>
    /// 试用用户响应模型
    /// </summary>
    public class Response_UseKami : ResponseBaseData
    {
        public class _UseKami
        {
            public string username { get; set; } = "";
            public string vip_time { get; set; } = "";
        }

        public new _UseKami? Data { get; set; }
    }

    /// <summary>
    /// Ntfy获取Key响应模型
    /// </summary>
    public class Response_NtfyKey : ResponseBaseData
    {
        public class _NtfyKey
        {
            public string ip { get; set; } = "";
            public string host { get; set; } = "";

            public string username { get; set; } = "";
            public string app_key { get; set; } = "";
        }

        public new _NtfyKey? Data { get; set; }
    }

    /// <summary>
    /// 用户消息获取响应模型
    /// </summary>
    public class Response_UserData : ResponseBaseData
    {
        public class _UserData
        {
            public string status { get; set; } = "";
            public string username { get; set; } = "";
            public int id { get; set; } = -1;
            public string email { get; set; } = "";
            public string ip { get; set; } = "";
            public string vip_time { get; set; } = "";
            public string qq { get; set; } = "";
            public object? config { get; set; }
        }

        public new _UserData? Data { get; set; }
    }

    /// <summary>
    /// 用户重置密码的响应模型
    /// </summary>
    public class Response_ResetPwdData : ResponseBaseData
    {
        public class _ResetPwdData
        {
            public string username { get; set; } = "";
            public string new_pwd { get; set; } = "";
        }

        public new _ResetPwdData? Data { get; set; }
    }

    /// <summary>
    /// 基础响应模型
    /// </summary>
    public class ResponseBaseData
    {
        public int Code { get; set; } = 0;
        public object? Data { get; set; }
        public bool IsSuccess => Code == 200;
        public string Message { get; set; } = "";
        public string detail { get; set; } = "";

        public override string ToString()
        {
            return $"[{Code}] [{Message}] [{Data?.ToString()}] [{detail}]";
        }
    }

    /// <summary>
    /// 活动信息模型
    /// </summary>
    public class _ActivityInfo
    {
        [JsonPropertyName("活动名")]
        public string? ActivityName { get; set; }

        [JsonPropertyName("活动截止时间")]
        public string? EndTime { get; set; }

        [JsonPropertyName("活动公告")]
        public string? Notice { get; set; }

        [JsonPropertyName("活动注意点")]
        public string? Attention { get; set; }

        [JsonPropertyName("是否过期")]
        public bool IsExpired { get; set; }
    }

    /// <summary>
    /// 活动信息响应模型
    /// </summary>
    public class Response_ActivityData : ResponseBaseData
    {
        public new _ActivityInfo? Data { get; set; }
    }

    /// <summary>
    /// 用户ID信息模型
    /// </summary>
    public class _UserIdInfo
    {
        [JsonPropertyName("username")]
        public string? Username { get; set; }

        [JsonPropertyName("user_id")]
        public int UserId { get; set; }
    }

    /// <summary>
    /// 用户ID查询响应模型
    /// </summary>
    public class Response_UserIdData : ResponseBaseData
    {
        public new _UserIdInfo? Data { get; set; }
    }

    /// <summary>
    /// 用户积分记录信息模型
    /// </summary>
    public class _PointsRecordInfo
    {
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        [JsonPropertyName("kami_code")]
        public string? KamiCode { get; set; }

        [JsonPropertyName("days_added")]
        public int DaysAdded { get; set; }

        [JsonPropertyName("points_added")]
        public int PointsAdded { get; set; }
    }

    /// <summary>
    /// 用户积分信息模型
    /// </summary>
    public class _UserPointsInfo
    {
        [JsonPropertyName("username")]
        public string? Username { get; set; }

        [JsonPropertyName("points")]
        public int Points { get; set; }

        [JsonPropertyName("records")]
        public List<_PointsRecordInfo> Records { get; set; } = new();
    }

    /// <summary>
    /// 用户积分查询响应模型
    /// </summary>
    public class Response_UserPointsData : ResponseBaseData
    {
        public new _UserPointsInfo? Data { get; set; }
    }

    /// <summary>
    /// 绑定QQ请求模型
    /// </summary>
    public class Request_BindQQData
    {
        public string username { get; set; } = "";
        public string qq { get; set; } = "";
        public string code { get; set; } = "";
    }

    /// <summary>
    /// 绑定QQ响应模型
    /// </summary>
    public class Response_BindQQData : ResponseBaseData
    {
        public class _BindQQData
        {
            public string username { get; set; } = "";
            public string qq { get; set; } = "";
        }

        public new _BindQQData? Data { get; set; }
    }

    /// <summary>
    /// 用户日志请求模型
    /// </summary>
    public class Request_StoreLogData
    {
        public string log_content { get; set; } = "";
    }

    /// <summary>
    /// 用户日志响应模型
    /// </summary>
    public class Response_StoreLogData : ResponseBaseData
    {
        public class _StoreLogData
        {
            public string username { get; set; } = "";
            public string expires_in { get; set; } = "";
        }

        public new _StoreLogData? Data { get; set; }
    }

    #region 配置共享相关模型

    /// <summary>
    /// 分享配置响应模型
    /// </summary>
    public class Response_ShareConfigData : ResponseBaseData
    {
        public class _ShareConfigData
        {
            [JsonPropertyName("share_code")]
            public string ShareCode { get; set; } = "";

            [JsonPropertyName("config_name")]
            public string ConfigName { get; set; } = "";

            [JsonPropertyName("message")]
            public string Message { get; set; } = "";
        }

        public new _ShareConfigData? Data { get; set; }
    }
    /// <summary>
    /// 获取配置响应模型
    /// </summary>
    public class Response_GetConfigData : ResponseBaseData
    {
        public class _GetConfigData
        {
            [JsonPropertyName("config_json")]
            public object? ConfigJson { get; set; }
            
            [JsonPropertyName("config_name")]
            public string ConfigName { get; set; } = "";

            [JsonPropertyName("version")]
            public string Version { get; set; } = "";

            [JsonPropertyName("shared_by_user_id")]
            public int SharedByUserId { get; set; }

            [JsonPropertyName("shared_by_username")]
            public string SharedByUsername { get; set; } = "";

            [JsonPropertyName("shared_at")]
            public string SharedAt { get; set; } = "";

            [JsonPropertyName("expires_at")]
            public string? ExpiresAt { get; set; }

            [JsonPropertyName("get_count")]
            public int GetCount { get; set; }
        }

        public new _GetConfigData? Data { get; set; }
    }

    /// <summary>
    /// 我的分享列表项模型
    /// </summary>
    public class _MyShareItem
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }

        [JsonPropertyName("share_code")]
        public string ShareCode { get; set; } = "";

        [JsonPropertyName("config_name")]
        public string ConfigName { get; set; } = "";

        [JsonPropertyName("version")]
        public string Version { get; set; } = "";

        [JsonPropertyName("status")]
        public string Status { get; set; } = "";

        [JsonPropertyName("created_at")]
        public string CreatedAt { get; set; } = "";

        [JsonPropertyName("expires_at")]
        public string? ExpiresAt { get; set; }

        [JsonPropertyName("get_count")]
        public int GetCount { get; set; }

        [JsonPropertyName("config_preview")]
        public string ConfigPreview { get; set; } = "";
    }

    /// <summary>
    /// 我的分享列表响应模型
    /// </summary>
    public class Response_MySharesData : ResponseBaseData
    {
        public class _MySharesData
        {
            [JsonPropertyName("items")]
            public List<_MyShareItem> Items { get; set; } = new List<_MyShareItem>();

            [JsonPropertyName("total")]
            public int Total { get; set; }

            [JsonPropertyName("page")]
            public int Page { get; set; }

            [JsonPropertyName("per_page")]
            public int PerPage { get; set; }

            [JsonPropertyName("pages")]
            public int Pages { get; set; }
        }

        public new _MySharesData? Data { get; set; }
    }

    /// <summary>
    /// 浏览配置列表项模型
    /// </summary>
    public class _BrowseConfigItem
    {
        [JsonPropertyName("share_code")]
        public string ShareCode { get; set; } = "";

        [JsonPropertyName("config_name")]
        public string ConfigName { get; set; } = "";

        [JsonPropertyName("version")]
        public string Version { get; set; } = "";

        [JsonPropertyName("shared_by_username")]
        public string SharedByUsername { get; set; } = "";

        [JsonPropertyName("shared_at")]
        public string SharedAt { get; set; } = "";

        [JsonPropertyName("expires_at")]
        public string? ExpiresAt { get; set; }

        [JsonPropertyName("get_count")]
        public int GetCount { get; set; }

        [JsonPropertyName("config_preview")]
        public string ConfigPreview { get; set; } = "";
    }

    /// <summary>
    /// 浏览配置列表响应模型
    /// </summary>
    public class Response_BrowseConfigsData : ResponseBaseData
    {
        public class _BrowseConfigsData
        {
            [JsonPropertyName("items")]
            public List<_BrowseConfigItem> Items { get; set; } = new List<_BrowseConfigItem>();

            [JsonPropertyName("total")]
            public int Total { get; set; }

            [JsonPropertyName("page")]
            public int Page { get; set; }

            [JsonPropertyName("per_page")]
            public int PerPage { get; set; }

            [JsonPropertyName("pages")]
            public int Pages { get; set; }
        }

        public new _BrowseConfigsData? Data { get; set; }
    }

    /// <summary>
    /// 热门配置项模型
    /// </summary>
    public class _PopularConfigItem
    {
        [JsonPropertyName("share_code")]
        public string ShareCode { get; set; } = "";

        [JsonPropertyName("version")]
        public string Version { get; set; } = "";

        [JsonPropertyName("shared_by_username")]
        public string SharedByUsername { get; set; } = "";

        [JsonPropertyName("shared_at")]
        public string SharedAt { get; set; } = "";

        [JsonPropertyName("get_count")]
        public int GetCount { get; set; }

        [JsonPropertyName("config_preview")]
        public string ConfigPreview { get; set; } = "";
    }

    /// <summary>
    /// 热门配置列表响应模型
    /// </summary>
    public class Response_PopularConfigsData : ResponseBaseData
    {
        public class _PopularConfigsData
        {
            [JsonPropertyName("items")]
            public List<_PopularConfigItem> Items { get; set; } = new List<_PopularConfigItem>();

            [JsonPropertyName("total")]
            public int Total { get; set; }

            [JsonPropertyName("page")]
            public int Page { get; set; }

            [JsonPropertyName("per_page")]
            public int PerPage { get; set; }

            [JsonPropertyName("pages")]
            public int Pages { get; set; }
        }

        public new _PopularConfigsData? Data { get; set; }
    }

    /// <summary>
    /// 配置统计数据模型
    /// </summary>
    public class Response_ConfigStatsData : ResponseBaseData
    {
        public class _ConfigStatsData
        {
            [JsonPropertyName("total_shares")]
            public int TotalShares { get; set; }

            [JsonPropertyName("total_gets")]
            public int TotalGets { get; set; }

            [JsonPropertyName("active_shares")]
            public int ActiveShares { get; set; }

            [JsonPropertyName("status_counts")]
            public Dictionary<string, int> StatusCounts { get; set; } = new Dictionary<string, int>();

            [JsonPropertyName("user_stats")]
            public Dictionary<string, int> UserStats { get; set; } = new Dictionary<string, int>();

            [JsonPropertyName("popular_shares")]
            public List<_PopularConfigItem> PopularShares { get; set; } = new List<_PopularConfigItem>();
        }

        public new _ConfigStatsData? Data { get; set; }
    }

    /// <summary>
    /// 配置统计历史数据项模型
    /// </summary>
    public class _ConfigHistoryItem
    {
        [JsonPropertyName("date")]
        public string Date { get; set; } = "";

        [JsonPropertyName("config_share_count")]
        public int ConfigShareCount { get; set; }

        [JsonPropertyName("config_get_count")]
        public int ConfigGetCount { get; set; }
    }

    /// <summary>
    /// 配置统计历史数据响应模型
    /// </summary>
    public class Response_ConfigStatsHistoryData : ResponseBaseData
    {
        public class _ConfigStatsHistoryData
        {
            [JsonPropertyName("history")]
            public List<_ConfigHistoryItem> History { get; set; } = new List<_ConfigHistoryItem>();

            [JsonPropertyName("today")]
            public string Today { get; set; } = "";

            [JsonPropertyName("start_date")]
            public string StartDate { get; set; } = "";
        }

        public new _ConfigStatsHistoryData? Data { get; set; }
    }

    /// <summary>
    /// 配置性能数据项模型
    /// </summary>
    public class _ConfigPerformanceItem
    {
        [JsonPropertyName("endpoint")]
        public string Endpoint { get; set; } = "";

        [JsonPropertyName("avg_response_time")]
        public double AvgResponseTime { get; set; }

        [JsonPropertyName("requests_count")]
        public int RequestsCount { get; set; }

        [JsonPropertyName("success_rate")]
        public double SuccessRate { get; set; }
    }

    /// <summary>
    /// 配置性能数据响应模型
    /// </summary>
    public class Response_ConfigPerformanceData : ResponseBaseData
    {
        public class _ConfigPerformanceData
        {
            [JsonPropertyName("endpoints")]
            public List<_ConfigPerformanceItem> Endpoints { get; set; } = new List<_ConfigPerformanceItem>();

            [JsonPropertyName("overall_avg_response_time")]
            public double OverallAvgResponseTime { get; set; }

            [JsonPropertyName("overall_success_rate")]
            public double OverallSuccessRate { get; set; }

            [JsonPropertyName("collected_since")]
            public string CollectedSince { get; set; } = "";
        }

        public new _ConfigPerformanceData? Data { get; set; }
    }

    #endregion

    /// <summary>
    /// WebToken设置请求模型
    /// </summary>
    public class Request_SetWebTokenData
    {
        public string web_token { get; set; } = "";
    }

    /// <summary>
    /// WebToken设置响应模型
    /// </summary>
    public class Response_SetWebTokenData : ResponseBaseData
    {
        public class _SetWebTokenData
        {
            [JsonPropertyName("username")]
            public string Username { get; set; } = "";

            [JsonPropertyName("web_token")]
            public string WebToken { get; set; } = "";
        }

        public new _SetWebTokenData? Data { get; set; }
    }

    /// <summary>
    /// WebToken状态响应模型
    /// </summary>
    public class Response_WebTokenStatusData : ResponseBaseData
    {
        public class _WebTokenStatusData
        {
            [JsonPropertyName("username")]
            public string Username { get; set; } = "";

            [JsonPropertyName("has_web_token")]
            public bool HasWebToken { get; set; } = false;

            [JsonPropertyName("web_token")]
            public string? WebToken { get; set; } = null;
        }

        public new _WebTokenStatusData? Data { get; set; }
    }
}