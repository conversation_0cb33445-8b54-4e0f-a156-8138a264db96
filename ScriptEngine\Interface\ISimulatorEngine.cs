﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Interface
{
    /// <summary>
    /// 模拟器引擎控制接口
    /// </summary>
    internal interface ISimulatorEngine
    {
        /// <summary>
        /// 检查是否合法、正常运行
        /// </summary>
        /// <returns></returns>
        public bool CheckLegality();

        /// <summary>
        /// 关闭模拟器
        /// </summary>
        public void Close();

        /// <summary>
        /// 连接adb设备
        /// </summary>
        /// <returns></returns>
        public bool ConnectToAdbDevice(int port);

        /// <summary>
        /// 获取所有包名
        /// </summary>
        /// <returns></returns>
        public List<string> GetPackages();

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="Path">模拟器目录</param>
        /// <returns></returns>
        public bool Init(string Path);

        /// <summary>
        /// 安装APK应用到设备
        /// </summary>
        /// <param name="apkPath">APK文件的完整路径</param>
        /// <param name="adbPort">可选的ADB端口参数，如果提供则会尝试自动连接该端口</param>
        /// <param name="options">安装选项，例如"-r"表示重新安装保留数据，"-g"表示授予所有权限</param>
        /// <returns>安装是否成功</returns>
        public bool InstallApp(string apkPath, int adbPort = 0, string options = "");

        /// <summary>
        /// 异步安装APK应用到设备
        /// </summary>
        /// <param name="apkPath">APK文件的完整路径</param>
        /// <param name="adbPort">可选的ADB端口参数，如果提供则会尝试自动连接该端口</param>
        /// <param name="options">安装选项，例如"-r"表示重新安装保留数据，"-g"表示授予所有权限</param>
        /// <returns>安装是否成功的任务</returns>
        public Task<bool> InstallAppAsync(string apkPath, int adbPort = 0, string options = "");

        /// <summary>
        /// 启动模拟器
        /// </summary>
        public void Open();

        /// <summary>
        /// 关闭应用
        /// </summary>
        /// <param name="packageName"></param>
        public void Stop_App(string packageName);
    }
}