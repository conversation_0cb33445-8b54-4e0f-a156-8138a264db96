﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Helpers
{
    public class MergeDataJson(Data_Pics dpics, Data_Poss dposs)
    {
        private Data_Pics Dpics { get; } = dpics;
        private Data_Poss Dposs { get; } = dposs;

        public MemPics GetMemPics()
        {
            MemPics memPics = new();
            //解析图片
            int ptr, size;
            string ptrinfo;
            foreach (var item in Dpics.DataPics)
            {
                string name = $"{item.Class}.{item.Name}";
                ptr = item.GetMemPtr(out size);
                ptrinfo = $"{ptr},{size}";
                MemPic mp = new(name, ptrinfo);
                memPics.Add(mp);
            }
            //解析范围 载入图片
            foreach (var item in Dposs.DataPoss)
            {
                string name = $"{item.Class}.{item.Name}";
                Position pos = new(item.ClickPos);
                pos.SetIdentifier(name);
                memPics.GetMemPic(name, true)?.AddClickPosition(pos);

                Position findpos = new(item.FindPos);
                pos.SetIdentifier(name);
                memPics.GetMemPic(name, true)?.SetFindPosition(findpos);
            }
            return memPics;
        }
    }
}