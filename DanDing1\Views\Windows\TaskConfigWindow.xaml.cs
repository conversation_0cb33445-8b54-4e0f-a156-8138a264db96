﻿using DanDing1.Models;
using DanDing1.ViewModels.Windows;
using ScriptEngine.Model;
using ShareX.HelpersLib;
using System.Collections.ObjectModel;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Wpf.Ui.Controls;
using XHelper;
using XHelper.DanDingNet;
using static ScriptEngine.Model.TaskConfigsModel;
using System.Linq;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// TaskConfigWindow.xaml 的交互逻辑
    /// </summary>
    public partial class TaskConfigWindow : Window
    {
        public TaskConfigWindow(TaskConfigWindowViewModel viewModel)
        {
            InitializeComponent();
            ViewModel = viewModel;
            DataContext = ViewModel;
        }

        public TaskConfigWindowViewModel ViewModel { get; }

        public ObservableCollection<string> Plan;

        public Action<ObservableCollection<TaskConfigsModel.Configs>> OnTaskChanged;

        public void InitConfig(ObservableCollection<TaskConfigsModel.Configs> _task, Action<ObservableCollection<TaskConfigsModel.Configs>> _onTaskChanged)
        {
            OnTaskChanged = _onTaskChanged;
            if (_task is not [] && _task is not null)
            {
                ViewModel.Tasks = _task;
                ViewModel.SelectedTask = _task[0];
                TaskListBox.SelectedItem = _task[0];
                ViewModel.ApplicationTitle = "任务列表详情(载入成功)";
            }
            InitPlanComboBox();
        }

        private void TaskListBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            ListBox? listBox = (ListBox)sender;

            if (listBox?.SelectedItem != null)
            {
                var selectedTask = (Configs)listBox.SelectedItem;
                ViewModel.SelectedTask = selectedTask;
                ViewModel.SelectedTaskOthers = new();
                ViewModel.SelectedTaskOthers_Obj = new();
                foreach (var item in selectedTask.Others)
                {
                    if (item.Key == "" || item.Value == "")
                        continue;
                    ViewModel.SelectedTaskOthers.Add(new(item.Key, item.Value));
                }
                foreach (var item in selectedTask.Others_Obj)
                {
                    if (item.Key == "" || item.Value is null)
                        continue;
                    ViewModel.SelectedTaskOthers_Obj.Add(new(item.Key, item.Value.ToString()));
                }
            }
        }

        private bool init_bool = false;

        /// <summary>
        /// 初始化方案下拉框，并在初始化完成后自动导入选择的方案
        /// </summary>
        /// <param name="selectedPlan">要选择的方案名称</param>
        private void InitPlanComboBox(string selectedPlan = "")
        {
            Plan = new();
            for (int i = 1; i < 5; i++)
            {
                var tasklist = XConfig.LoadValueFromFile<ObservableCollection<TaskConfigsModel.Configs>>("TaskList", $"游戏{i}");
                if (tasklist is not [] and not null)
                    Plan.Add($"游戏{i}|上次运行");
            }
            //读取用户自定义任务列表
            //遍历文件夹 \runtimes\AppConfig\UserTaskList 下的所有文件，添加到 PlanComboBox 的 ItemsSource 中
            DirectoryInfo dir = new DirectoryInfo(".\\runtimes\\AppConfig\\UserTaskList\\");
            //判断文件夹是否存在
            if (!dir.Exists)
                dir.Create();//不存在则创建文件夹
            foreach (var file in dir.GetFiles())
                Plan.Add(file.Name);

            PlanComboBox.ItemsSource = Plan;
            if (PlanComboBox.Items.Count > 0)
                PlanComboBox.SelectedIndex = 0;
            if (selectedPlan is not "")
                PlanComboBox.SelectedItem = selectedPlan;
            if (init_bool)
            {
                // 获取下拉列表中选中的配置方案并载入配置
                var selectedPlan1 = PlanComboBox.SelectedItem as string;
                if (selectedPlan1 is not null && selectedPlan1.Contains('|'))
                {
                    var tasklist = XConfig.LoadValueFromFile<ObservableCollection<TaskConfigsModel.Configs>>("TaskList", selectedPlan1.Split('|')[0]);
                    if (tasklist is not [] and not null)
                    {
                        ViewModel.Tasks = tasklist;
                        ViewModel.SelectedTask = ViewModel.Tasks[0];
                        TaskListBox.SelectedItem = ViewModel.Tasks[0];
                        OnTaskChanged.Invoke(tasklist);
                        ViewModel.ApplicationTitle = "任务列表详情(载入成功)";
                    }
                }
                else if (selectedPlan1 is not null)
                {
                    var usertasklist = XConfig.LoadValueFromFile<ObservableCollection<TaskConfigsModel.Configs>>("UserTaskList", selectedPlan1);
                    if (usertasklist is not [] and not null)
                    {
                        ViewModel.Tasks = usertasklist;
                        ViewModel.SelectedTask = usertasklist[0];
                        TaskListBox.SelectedItem = usertasklist[0];
                        OnTaskChanged.Invoke(usertasklist);
                        ViewModel.ApplicationTitle = "任务列表详情(载入成功)";
                    }
                }
                return;
            }
            init_bool = true;
        }

        /// <summary>
        /// PlanComboBox选择项改变事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void PlanComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (!init_bool)
                return;

            ComboBox comboBox = (ComboBox)sender;
            if (comboBox.SelectedItem == null)
                return;

            // 获取选中的方案名称
            string plan = comboBox.SelectedItem.ToString();
            if (string.IsNullOrEmpty(plan))
                return;

            // 自动导入选中的方案
            if (plan.Contains('|'))
            {
                var tasklist = XConfig.LoadValueFromFile<ObservableCollection<TaskConfigsModel.Configs>>("TaskList", plan.Split('|')[0]);
                if (tasklist is not [] and not null)
                {
                    ViewModel.Tasks = tasklist;
                    ViewModel.SelectedTask = ViewModel.Tasks[0];
                    TaskListBox.SelectedItem = ViewModel.Tasks[0];
                    OnTaskChanged.Invoke(tasklist);
                    ViewModel.ApplicationTitle = "任务列表详情(自动导入成功)";
                }
                return;
            }

            // 读取用户自定义任务列表
            var usertasklist = XConfig.LoadValueFromFile<ObservableCollection<TaskConfigsModel.Configs>>("UserTaskList", plan);
            if (usertasklist is not [] and not null)
            {
                ViewModel.Tasks = usertasklist;
                ViewModel.SelectedTask = usertasklist[0];
                TaskListBox.SelectedItem = usertasklist[0];
                OnTaskChanged.Invoke(usertasklist);
                ViewModel.ApplicationTitle = "任务列表详情(自动导入成功)";
            }
        }

        private void ImportButton_Click(object sender, RoutedEventArgs e)
        {
            // 保存当前选择的方案名称
            string currentPlan = PlanComboBox.SelectedItem as string;

            // 刷新方案列表
            InitPlanComboBox(currentPlan);

            // 显示刷新成功消息
            System.Windows.MessageBox.Show("方案列表刷新成功！\n选择方案后将自动导入。", "刷新成功", System.Windows.MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            var input = new InputWindow("", "请输入配置方案名称");
            input.ShowDialog();
            if (input.IsOK && input.InputText is not "")
            {
                if (input.InputText.Contains('|'))
                {
                    System.Windows.MessageBox.Show("任务名称不能包含'|'字符！");
                    return;
                }

                // 生成安全的配置名称
                string configName = GenerateSafeConfigName(input.InputText.Trim());

                // 保存配置
                if (XConfig.SaveValueToFile("UserTaskList", configName, ViewModel.Tasks))
                {
                    // 显示保存成功的消息
                    System.Windows.MessageBox.Show($"配置方案已保存为：{configName}", "保存成功", System.Windows.MessageBoxButton.OK, MessageBoxImage.Information);

                    // 刷新下拉列表
                    InitPlanComboBox(configName);
                }
                else
                {
                    System.Windows.MessageBox.Show("保存配置方案失败，请重试", "保存失败", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void DelButton_Click(object sender, RoutedEventArgs e)
        {
            //判断 PlanComboBox 是否有选择
            if (PlanComboBox.SelectedItem is not string plan)
                return;
            if (plan.Contains('|'))
            {
                System.Windows.MessageBox.Show("不能删除系统默认保存的任务方案！");
                return;
            }
            //弹出是否确认删除对话框
            var result = System.Windows.MessageBox.Show($"确认删除方案\"{plan}\"？", "提示", System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Question);
            if (result == System.Windows.MessageBoxResult.No)
                return;
            var path = ".\\runtimes\\AppConfig\\UserTaskList\\" + plan;
            if (File.Exists(path))
            {
                File.Delete(path);
                InitPlanComboBox();
            }
        }

        /// <summary>
        /// 使用配置码导入按钮点击事件处理
        /// </summary>
        private async void ImportCodeButton_Click(object sender, RoutedEventArgs e)
        {
            // 弹出输入窗口，让用户输入配置码
            var inputWindow = new InputWindow("", "请输入配置码");
            inputWindow.ShowDialog();

            // 检查用户是否确认输入
            if (!inputWindow.IsOK || string.IsNullOrWhiteSpace(inputWindow.InputText))
                return;

            // 从用户输入中提取配置码
            string shareCode = ExtractShareCode(inputWindow.InputText.Trim());
            if (string.IsNullOrWhiteSpace(shareCode))
            {
                System.Windows.MessageBox.Show("未能识别有效的配置码，请确认输入内容", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            try
            {
                // 使用加载中对话框显示
                var cursor = Mouse.OverrideCursor;
                Mouse.OverrideCursor = Cursors.Wait;

                // 获取全局 DanDingNet 实例
                var dandingNet = GlobalData.Instance.appConfig.dNet;
                if (dandingNet == null)
                {
                    System.Windows.MessageBox.Show("网络服务未初始化，请先登录", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                    Mouse.OverrideCursor = cursor;
                    return;
                }

                // 调用后端接口获取配置
                var result = await dandingNet.Config.GetConfigAsync(shareCode);
                Mouse.OverrideCursor = cursor;

                // 检查结果
                if (result == null || !result.IsSuccess || result.Data == null)
                {
                    string errorMsg = result?.Message ?? "获取配置失败";
                    System.Windows.MessageBox.Show($"获取配置失败: {errorMsg}", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 尝试将配置 JSON 反序列化为任务列表
                try
                {
                    if (result.Data.ConfigJson == null)
                    {
                        System.Windows.MessageBox.Show("配置内容为空", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    string configJson = result.Data.ConfigJson.ToString();
                    var tasklist = JsonSerializer.Deserialize<ObservableCollection<TaskConfigsModel.Configs>>(configJson);

                    if (tasklist == null || tasklist.Count == 0)
                    {
                        System.Windows.MessageBox.Show("配置格式错误或任务列表为空", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // 更新任务列表
                    ViewModel.Tasks = tasklist;
                    ViewModel.SelectedTask = tasklist[0];
                    TaskListBox.SelectedItem = tasklist[0];
                    OnTaskChanged.Invoke(tasklist);
                    string taskName = result.Data.ConfigName;

                    // 将网络配置同时保存到本地
                    string localConfigName = "";
                    try
                    {
                        // 生成配置名并保存
                        string baseConfigName = $"{taskName}_{result.Data.Version}";
                        localConfigName = SaveImportedConfig(tasklist, baseConfigName);

                        XLogger.Info($"成功将配置码导入的任务保存到本地: {localConfigName}");
                    }
                    catch (Exception saveEx)
                    {
                        XLogger.Warn($"保存配置码导入的任务到本地失败: {saveEx.Message}");
                        // 此处不弹出错误窗口，以免影响用户体验
                    }

                    // 显示导入成功的消息，包含分享者信息
                    System.Windows.MessageBox.Show($"成功导入配置 \"{result.Data.Version}\"\n" +
                        $"分享者: {result.Data.SharedByUsername}\n" +
                        $"分享时间: {result.Data.SharedAt}\n" +
                        $"配置已同时保存到本地" + (!string.IsNullOrEmpty(localConfigName) ? $"，保存为：{localConfigName}" : "") +
                        "，可在下拉列表中选择使用",
                        "导入成功", System.Windows.MessageBoxButton.OK, MessageBoxImage.Information);

                    ViewModel.ApplicationTitle = $"任务列表详情(配置码导入成功)";
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"解析配置失败: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"导入过程中发生错误: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 保存导入的配置到本地
        /// </summary>
        /// <param name="taskList">任务列表</param>
        /// <param name="baseConfigName">基础配置名称</param>
        /// <returns>保存后的配置名称</returns>
        private string SaveImportedConfig(ObservableCollection<TaskConfigsModel.Configs> taskList, string baseConfigName)
        {
            // 生成安全的配置名称
            string configName = GenerateSafeConfigName(baseConfigName);

            // 保存配置
            if (XConfig.SaveValueToFile("UserTaskList", configName, taskList))
            {
                // 刷新下拉列表并选中新导入的配置
                InitPlanComboBox(configName);
                return configName;
            }
            else
            {
                throw new Exception("保存配置文件失败");
            }
        }

        /// <summary>
        /// 生成合法的配置文件名
        /// </summary>
        /// <param name="baseName">基础文件名</param>
        /// <returns>处理后的合法文件名</returns>
        private string GenerateSafeConfigName(string baseName)
        {
            // 替换非法字符
            string safeName = baseName;
            foreach (char c in Path.GetInvalidFileNameChars())
            {
                safeName = safeName.Replace(c, '_');
            }

            // 替换可能引起问题的其他字符
            safeName = safeName.Replace('|', '_')
                               .Replace(' ', '_')
                               .Replace('.', '_');

            // 限制文件名长度，避免过长
            if (safeName.Length > 50)
            {
                safeName = safeName.Substring(0, 50);
            }

            // 检查文件是否已存在，如果存在则添加数字后缀
            string baseFileName = safeName;
            int counter = 1;
            string filePath = Path.Combine(".\\runtimes\\AppConfig\\UserTaskList\\", safeName);

            while (File.Exists(filePath))
            {
                safeName = $"{baseFileName}_{counter}";
                filePath = Path.Combine(".\\runtimes\\AppConfig\\UserTaskList\\", safeName);
                counter++;
            }

            return safeName;
        }

        /// <summary>
        /// 从分享文本中提取配置码
        /// </summary>
        /// <param name="input">用户输入的文本</param>
        /// <returns>提取的配置码，如果未找到则返回原文本</returns>
        private string ExtractShareCode(string input)
        {
            // 如果输入文本不包含特定标记，认为用户直接输入了配置码
            if (!input.Contains("【蛋定助手】") && !input.Contains("配置码导入"))
            {
                return input;
            }

            try
            {
                // 查找"请使用配置码导入:"后的内容
                int importIndex = input.IndexOf("请使用配置码导入:");
                if (importIndex >= 0)
                {
                    // 取冒号后的内容
                    string afterImport = input.Substring(importIndex + "请使用配置码导入:".Length).Trim();

                    // 提取到下一个换行符前的内容（如果有）
                    int nextLineBreak = afterImport.IndexOf('\n');
                    if (nextLineBreak > 0)
                    {
                        return afterImport.Substring(0, nextLineBreak).Trim();
                    }

                    return afterImport;
                }

                // 尝试寻找通用格式："配置码:"或"配置码："后的内容
                foreach (string marker in new[] { "配置码:", "配置码：", "方案码:", "方案码：" })
                {
                    int markerIndex = input.IndexOf(marker);
                    if (markerIndex >= 0)
                    {
                        string afterMarker = input.Substring(markerIndex + marker.Length).Trim();

                        // 提取到下一个换行符前的内容（如果有）
                        int nextLineBreak = afterMarker.IndexOf('\n');
                        if (nextLineBreak > 0)
                        {
                            return afterMarker.Substring(0, nextLineBreak).Trim();
                        }

                        return afterMarker;
                    }
                }

                // 如果上述方法都没找到，尝试在整个文本中查找符合配置码格式的部分
                // 假设配置码是一个不包含空格的字符串，长度在5-20之间
                var words = input.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (var word in words)
                {
                    // 如果单词长度合适且仅包含字母数字，可能是配置码
                    if (word.Length >= 5 && word.Length <= 20 && word.All(c => char.IsLetterOrDigit(c)))
                    {
                        return word;
                    }
                }
            }
            catch (Exception ex)
            {
                XLogger.Warn($"提取配置码异常: {ex.Message}");
            }

            // 如果所有方法都失败，返回原始输入
            return input;
        }

        /// <summary>
        /// 共享当前配置按钮点击事件处理
        /// </summary>
        private async void ShareConfigButton_Click(object sender, RoutedEventArgs e)
        {
            // 检查是否有任务可以共享
            if (ViewModel.Tasks == null || ViewModel.Tasks.Count == 0)
            {
                System.Windows.MessageBox.Show("没有可共享的任务配置", "提示", System.Windows.MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // 检查是否为系统默认配置
            string selectedPlan = PlanComboBox.SelectedItem?.ToString() ?? string.Empty;
            if (selectedPlan.Contains("|上次运行"))
            {
                var result = System.Windows.MessageBox.Show(
                    "系统默认配置不允许直接共享，是否自动另存为自定义配置后再共享？",
                    "需要另存为",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    // 自动生成配置名称（使用系统配置名称但移除"|上次运行"部分）
                    string autoConfigName = selectedPlan.Split('|')[0] + "_自定义";
                    string safeName = GenerateSafeConfigName(autoConfigName);

                    // 保存为自定义配置
                    if (XConfig.SaveValueToFile("UserTaskList", safeName, ViewModel.Tasks))
                    {
                        // 刷新下拉列表并选中新保存的配置
                        InitPlanComboBox(safeName);

                        // 提示用户保存成功，继续共享流程
                        System.Windows.MessageBox.Show(
                            $"已自动保存为自定义配置：{safeName}\n现在可以继续共享操作",
                            "保存成功",
                            System.Windows.MessageBoxButton.OK,
                            System.Windows.MessageBoxImage.Information);
                    }
                    else
                    {
                        System.Windows.MessageBox.Show(
                            "保存为自定义配置失败，无法继续共享操作",
                            "保存失败",
                            System.Windows.MessageBoxButton.OK,
                            System.Windows.MessageBoxImage.Error);
                        return;
                    }
                }
                else
                {
                    return; // 用户选择不自动另存，取消共享操作
                }
            }

            // 检查用户是否已登录
            if (!GlobalData.Instance.appConfig.IsLogin)
            {
                System.Windows.MessageBox.Show("请先登录后再使用此功能", "提示", System.Windows.MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 检查是否为试用账号
            if (GlobalData.Instance.appConfig.IsFree)
            {
                System.Windows.MessageBox.Show("试用账号无法使用配置共享功能", "提示", System.Windows.MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                // 再次检查当前选中的配置是否为系统默认配置
                string currentPlan = PlanComboBox.SelectedItem?.ToString() ?? string.Empty;
                if (currentPlan.Contains("|上次运行"))
                {
                    System.Windows.MessageBox.Show(
                        "当前仍为系统默认配置，无法继续共享操作",
                        "操作取消",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Warning);
                    return;
                }

                // 打开共享配置窗口
                var shareWindow = new ShareConfigWindow(PlanComboBox.SelectedItem?.ToString() ?? "任务配置");
                if (shareWindow.ShowDialog() != true || !shareWindow.IsConfirmed)
                {
                    return; // 用户取消操作
                }

                // 获取配置名称和有效期
                string configName = shareWindow.ConfigName;
                int expiryDays = shareWindow.ExpiryDays;

                // 显示等待光标
                var cursor = Mouse.OverrideCursor;
                Mouse.OverrideCursor = Cursors.Wait;

                // 序列化当前任务列表
                string configJson = JsonSerializer.Serialize(ViewModel.Tasks);

                // 调用API共享配置
                var result = await GlobalData.Instance.appConfig.dNet.Config.ShareConfigAsync(configName, configJson, GlobalData.Ver, expiryDays > 0 ? expiryDays : null);

                // 检查结果
                if (result == null || !result.IsSuccess || result.Data == null)
                {
                    string errorMsg = result?.Message ?? "原因未知";
                    System.Windows.MessageBox.Show($"共享配置失败: {errorMsg}", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 获取共享码
                string shareCode = result.Data.ShareCode;

                // 生成分享文本
                string appVer = GlobalData.Ver; // 使用全局版本号
                string shareText = $"【蛋定助手】我分享了一个「{configName}」的任务配置方案\n" +
                                   $"最佳版本: {appVer}\n" +
                                   $"请使用配置码导入: {shareCode}\n" +
                                   (expiryDays > 0 ? $"有效期: {expiryDays} 天\n" : "永久有效\n") +
                                   "------------------------\n" +
                                   "打开蛋定->任务配置->使用方案码导入";

                // 恢复光标
                Mouse.OverrideCursor = cursor;

                // 复制到剪贴板（添加异常处理和重试机制防止剪贴板繁忙或被占用）
                bool clipboardSuccess = false;
                try
                {
                    Clipboard.SetText(shareText);
                    clipboardSuccess = true;
                }
                catch (Exception clipboardEx)
                {
                    XLogger.Warn($"复制到剪贴板失败: {clipboardEx.Message}");
                    // 所有重试都失败，显示文本分享窗口让用户手动复制
                    var textShareWindow = new TextShareWindow(shareText, shareCode);
                    textShareWindow.ShowDialog();
                    return; // 直接返回，不显示成功消息
                }

                // 只有在剪贴板复制成功的情况下才显示成功消息
                if (clipboardSuccess)
                {
                    System.Windows.MessageBox.Show(
                        $"配置共享成功！\n\n" +
                        $"配置码: {shareCode}\n\n" +
                        $"分享文本已复制到剪贴板，可直接粘贴发送给其他用户。",
                        "共享成功",
                        System.Windows.MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"共享配置时发生错误: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                XLogger.Error($"共享配置异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 从云端导入按钮点击事件处理
        /// </summary>
        private void FromCloudButton_Click(object sender, RoutedEventArgs e)
        {
            // 检查用户是否已登录
            if (!GlobalData.Instance.appConfig.IsLogin)
            {
                System.Windows.MessageBox.Show("请先登录后再使用此功能", "提示", System.Windows.MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 检查网络服务是否初始化
            if (GlobalData.Instance.appConfig.dNet == null)
            {
                System.Windows.MessageBox.Show("网络服务未初始化，请先登录", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            try
            {
                // 打开云端配置窗口
                var cloudConfigWindow = new CloudConfigWindow(OnTaskChanged);

                // 如果用户成功导入了配置（窗口返回true），则更新当前窗口状态
                if (cloudConfigWindow.ShowDialog() == true)
                {
                    // 获取保存的配置名称
                    DirectoryInfo dir = new DirectoryInfo(".\\runtimes\\AppConfig\\UserTaskList\\");
                    if (dir.Exists)
                    {
                        // 按修改时间排序获取最新的配置文件
                        var latestFile = dir.GetFiles()
                            .OrderByDescending(f => f.LastWriteTime)
                            .FirstOrDefault();

                        if (latestFile != null)
                        {
                            // 刷新下拉列表并选中最新导入的配置
                            InitPlanComboBox(latestFile.Name);
                            ViewModel.ApplicationTitle = "任务列表详情(云端导入成功)";

                            // 确保下拉菜单选中的配置与当前加载的任务列表一致
                            if (PlanComboBox.SelectedItem is string selectedPlan)
                            {
                                // 记录日志
                                XLogger.Info($"从云端导入后选中配置：{selectedPlan}");
                            }
                        }
                        else
                        {
                            // 如果没找到文件，只刷新列表
                            InitPlanComboBox();
                            ViewModel.ApplicationTitle = "任务列表详情(云端导入成功)";
                        }
                    }
                    else
                    {
                        // 如果目录不存在，只刷新列表
                        InitPlanComboBox();
                        ViewModel.ApplicationTitle = "任务列表详情(云端导入成功)";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"打开云端配置窗口时发生错误: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                XLogger.Error($"打开云端配置窗口异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 我分享的配置按钮点击事件处理
        /// </summary>
        private void MySharesButton_Click(object sender, RoutedEventArgs e)
        {
            // 检查用户是否已登录
            if (!GlobalData.Instance.appConfig.IsLogin)
            {
                System.Windows.MessageBox.Show("请先登录后再使用此功能", "提示", System.Windows.MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 检查是否为试用账号
            if (GlobalData.Instance.appConfig.IsFree)
            {
                System.Windows.MessageBox.Show("试用账号无法使用配置共享功能", "提示", System.Windows.MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 检查网络服务是否初始化
            if (GlobalData.Instance.appConfig.dNet == null)
            {
                System.Windows.MessageBox.Show("网络服务未初始化，请先登录", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            try
            {
                // 打开我分享的配置窗口
                var mySharesWindow = new MySharesWindow();
                mySharesWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"打开我分享的配置窗口时发生错误: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, MessageBoxImage.Error);
                XLogger.Error($"打开我分享的配置窗口异常: {ex.Message}");
            }
        }
    }
}