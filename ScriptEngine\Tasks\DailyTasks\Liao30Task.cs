﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using XHelper.Models;

namespace ScriptEngine.Tasks.DailyTasks
{
    /// <summary>
    /// 每日免费礼包领取
    /// </summary>
    internal class Liao30Task : BaseTask
    {
        private Dictionary<string, Position> ClickPos_Pairs = new Dictionary<string, Position>()
        {
            {"打开阴阳寮",new(544,623,593,662) },
            {"提交任务1",new(250,472,349,513) },
            {"提交任务2",new(588,476,685,513) },
            {"关闭提交",new(1147,102,1190,140) },
            {"返回",new(33,23,77,61) },
        };

        private Dictionary<string, Position> Find_Pairs = new()
        {
            {"是否可以交材料",new(3,151,237,333) }, // OCR结果中如果包含 "今日已完成"、"集体任务" 则可以点击跳转 交材料
            {"是否在集体任务界面",new(457,33,796,94) }, // OCR结果中如果包含 "任务" 则可以点击跳转 交材料
            {"识别任务1",new(156,182,444,280) }, // OCR结果中如果包含 "提交"并有"觉醒" 则可以点击跳转 交材料
            {"识别任务2",new(489,184,780,308) }, // OCR结果中如果包含 "提交"并有"觉醒" 则可以点击跳转 交材料
        };

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, className);
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            log.Info("开始执行任务：" + configs.Name);

            // 判断场景是否为庭院
            GoToTingYuan();

            log.Info("点击进入阴阳寮..");
            Fast.Click(ClickPos_Pairs["打开阴阳寮"]);
            Sleep(3000);

            if (Scene.NowScene != "阴阳寮")
            {
                log.Error("无法进入阴阳寮失败，结束任务..");
                return;
            }

            // 执行提交材料
            CollectLiao30();
            log.Info("退出阴阳寮...结束寮提交材料任务..");
            Fast.Click(ClickPos_Pairs["返回"]);
            Sleep(2000);
        }

        /// <summary>
        /// 提交材料
        /// </summary>
        private void CollectLiao30()
        {
            List<XOcr_TextBlock> boxs = new();
            string res = Fast.Ocr_String(Find_Pairs["是否可以交材料"], t => boxs = t);
            bool open = false;
            foreach (var box in boxs)
            {
                box.SetBasePoint(3, 151);
                if (box.Text.Contains("集体") || box.Text.Contains("今日已完成"))
                {
                    Fast.Click(box.Center.X, box.Center.Y);
                    Sleep(1000);
                    open = true;
                    break;
                }
            }
            if (open)
            {
                WaitTiJiao();
                string leixing = "";
                int tijiao = 0;
                // 定义需要检查的材料类型
                string[] materialTypes = { "觉醒", "御魂", "御灵" };

                // 检查任务1和任务2
                for (int i = 1; i <= 2; i++)
                {
                    string renwuText = Fast.Ocr_String(Find_Pairs[$"识别任务{i}"]);
                    if (renwuText.Contains("提交"))
                    {
                        foreach (string type in materialTypes)
                        {
                            if (renwuText.Contains(type))
                            {
                                tijiao = i;
                                leixing = type;
                                break;
                            }
                        }
                    }

                    // 如果已找到可提交的任务，跳出循环
                    if (tijiao != 0)
                    {
                        break;
                    }
                }

                if (tijiao != 0)
                {
                    Fast.Click(ClickPos_Pairs["提交任务" + tijiao.ToString()]);
                    Sleep(1500);
                    Tijiao(leixing);
                    return;
                }
                log.Warn("没有识别到提交任务... 结束任务..");
                log.Info("点击关闭提交界面");
                Fast.Click(ClickPos_Pairs["关闭提交"]);
                Sleep(1000);
            }
            else
            {
                log.Warn("没有识别到可进行的寮30任务，结束任务..");
                log.Info("点击关闭提交界面");
                Fast.Click(ClickPos_Pairs["关闭提交"]);
                Sleep(1000);
            }
        }

        /// <summary>
        /// 等待材料界面
        /// </summary>
        private void WaitTiJiao()
        {
        Reocr:
            log.Info("等待集体任务界面...");
            Sleep(1000);
            string res = Fast.Ocr_String(Find_Pairs["是否在集体任务界面"]);
            if (res.Contains("集体任务"))
                return;
            else
                goto Reocr;
        }

        /// <summary>
        /// 提交材料
        /// </summary>
        private void Tijiao(string LeiXing)
        {
            if (LeiXing == "觉醒" || LeiXing == "御灵")
            {
                log.Info("通过简单的方式拉满觉醒或预览材料...");
                Operational.Slide_Pos(new Point(704, 165), new(929, 166));
                Sleep(500);
                Operational.Slide_Pos(new Point(703, 291), new(931, 289));
                Sleep(500);
                Operational.Slide_Pos(new Point(705, 412), new(928, 411));
                Sleep(500);
                Operational.Slide_Pos(new Point(704, 537), new(928, 534));
                Sleep(500);
                log.Info("点击提交");
                Fast.Click(642, 626);
                Sleep(2000);
                Fast.Click(631, 62);
                Sleep(1000);
                Fast.Click(631, 62);
                log.Info("点击关闭提交界面");
                Fast.Click(ClickPos_Pairs["关闭提交"]);
                Sleep(1000);
                return;
            }
            if (LeiXing == "御魂")
            {
                log.Info("通过简单的方式全选5x御魂材料...");
                Fast.LongClick(225, 195, 3000);
                log.Info("点击提交...");
                Fast.Click(909, 617);
                Sleep(2000);
                Fast.Click(631, 62);
                Sleep(1000);
                Fast.Click(631, 62);
                log.Info("点击关闭提交界面");
                Fast.Click(ClickPos_Pairs["关闭提交"]);
                Sleep(1000);
                Fast.Click(ClickPos_Pairs["关闭提交"]);
                Sleep(1000);
            }
        }

        /// <summary>
        /// 确保角色在庭院场景
        /// </summary>
        private bool GoToTingYuan()
        {
        Retry:
            // 判断庭院位置
            if (Scene.NowScene != "庭院")
            {
                log.Info("当前不在庭院，尝试前往庭院");
                if (!Scene.TO.TingYuan())
                {
                    if (!Scene.TO.ResetScene(out var s))
                    {
                        log.Error("无法进入庭院，结束任务");
                        return false;
                    }
                    else
                        goto Retry;
                }
                Sleep(1500); // 等待1.5秒

                // 再次确认是否在庭院
                if (Scene.NowScene != "庭院")
                {
                    if (!Scene.TO.ResetScene(out var s))
                    {
                        log.Error("无法进入庭院，结束任务");
                        return false;
                    }
                    else
                        goto Retry;
                }
            }

            log.Info("已确认在庭院场景");
            return true;
        }
    }
}