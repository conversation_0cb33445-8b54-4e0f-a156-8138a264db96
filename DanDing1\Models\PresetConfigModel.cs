using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;
using XHelper;

namespace DanDing1.Models
{
    /// <summary>
    /// 预设配置参数类
    /// </summary>
    public class PresetParameter : INotifyPropertyChanged
    {
        private int _setIndex; // 套号（1-8）
        private int _presetIndex; // 预设号（1-4）

        /// <summary>
        /// 套号（1-8）
        /// </summary>
        public int SetIndex
        {
            get => _setIndex;
            set
            {
                if (_setIndex != value)
                {
                    _setIndex = Math.Clamp(value, 1, 8); // 确保值在1-8范围内
                    OnPropertyChanged(nameof(SetIndex));
                }
            }
        }

        /// <summary>
        /// 预设号（1-4）
        /// </summary>
        public int PresetIndex
        {
            get => _presetIndex;
            set
            {
                if (_presetIndex != value)
                {
                    _presetIndex = Math.Clamp(value, 1, 4); // 确保值在1-4范围内
                    OnPropertyChanged(nameof(PresetIndex));
                }
            }
        }

        /// <summary>
        /// 获取唯一标识字符串
        /// </summary>
        public string UniqueKey => $"{SetIndex}-{PresetIndex}";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 预设配置类
    /// </summary>
    public class PresetConfig : INotifyPropertyChanged
    {
        private string _name;
        private ObservableCollection<PresetParameter> _parameters;
        private bool _allScenes = true; // 默认为全部场景
        private bool _yuhun = false; // 御魂
        private bool _tupo = false; // 突破
        private bool _tansuo = false; // 探索
        private bool _yuling = false; // 御灵
        private bool _douji = false; // 斗技
        private bool _yingxiong = false; // 英杰
        private bool _liudao = false; // 六道
        private bool _qiling = false; // 契灵
        private bool _richang = false; // 日常

        /// <summary>
        /// 配置名称
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged(nameof(Name));
                }
            }
        }

        /// <summary>
        /// 配置参数列表
        /// </summary>
        public ObservableCollection<PresetParameter> Parameters
        {
            get => _parameters;
            set
            {
                if (_parameters != value)
                {
                    _parameters = value;
                    OnPropertyChanged(nameof(Parameters));
                }
            }
        }

        /// <summary>
        /// 是否应用于全部场景
        /// </summary>
        public bool AllScenes
        {
            get => _allScenes;
            set
            {
                if (_allScenes != value)
                {
                    _allScenes = value;
                    OnPropertyChanged(nameof(AllScenes));

                    // 如果选择了全部场景，那么取消选中其它所有场景
                    if (_allScenes)
                    {
                        Yuhun = false;
                        Tupo = false;
                        Tansuo = false;
                        Yuling = false;
                        Douji = false;
                        Yingxiong = false;
                        Liudao = false;
                        Qiling = false;
                        Richang = false;
                    }
                }
            }
        }

        /// <summary>
        /// 是否应用于御魂场景
        /// </summary>
        public bool Yuhun
        {
            get => _yuhun;
            set
            {
                if (_yuhun != value)
                {
                    _yuhun = value;
                    OnPropertyChanged(nameof(Yuhun));

                    // 如果选择了具体场景，取消全部场景选项
                    if (_yuhun)
                    {
                        AllScenes = false;
                    }
                }
            }
        }

        /// <summary>
        /// 是否应用于突破场景
        /// </summary>
        public bool Tupo
        {
            get => _tupo;
            set
            {
                if (_tupo != value)
                {
                    _tupo = value;
                    OnPropertyChanged(nameof(Tupo));

                    if (_tupo)
                    {
                        AllScenes = false;
                    }
                }
            }
        }

        /// <summary>
        /// 是否应用于探索场景
        /// </summary>
        public bool Tansuo
        {
            get => _tansuo;
            set
            {
                if (_tansuo != value)
                {
                    _tansuo = value;
                    OnPropertyChanged(nameof(Tansuo));

                    if (_tansuo)
                    {
                        AllScenes = false;
                    }
                }
            }
        }

        /// <summary>
        /// 是否应用于御灵场景
        /// </summary>
        public bool Yuling
        {
            get => _yuling;
            set
            {
                if (_yuling != value)
                {
                    _yuling = value;
                    OnPropertyChanged(nameof(Yuling));

                    if (_yuling)
                    {
                        AllScenes = false;
                    }
                }
            }
        }

        /// <summary>
        /// 是否应用于斗技场景
        /// </summary>
        public bool Douji
        {
            get => _douji;
            set
            {
                if (_douji != value)
                {
                    _douji = value;
                    OnPropertyChanged(nameof(Douji));

                    if (_douji)
                    {
                        AllScenes = false;
                    }
                }
            }
        }

        /// <summary>
        /// 是否应用于英杰场景
        /// </summary>
        public bool Yingxiong
        {
            get => _yingxiong;
            set
            {
                if (_yingxiong != value)
                {
                    _yingxiong = value;
                    OnPropertyChanged(nameof(Yingxiong));

                    if (_yingxiong)
                    {
                        AllScenes = false;
                    }
                }
            }
        }

        /// <summary>
        /// 是否应用于六道场景
        /// </summary>
        public bool Liudao
        {
            get => _liudao;
            set
            {
                if (_liudao != value)
                {
                    _liudao = value;
                    OnPropertyChanged(nameof(Liudao));

                    if (_liudao)
                    {
                        AllScenes = false;
                    }
                }
            }
        }

        /// <summary>
        /// 是否应用于契灵场景
        /// </summary>
        public bool Qiling
        {
            get => _qiling;
            set
            {
                if (_qiling != value)
                {
                    _qiling = value;
                    OnPropertyChanged(nameof(Qiling));

                    if (_qiling)
                    {
                        AllScenes = false;
                    }
                }
            }
        }

        /// <summary>
        /// 是否应用于日常场景
        /// </summary>
        public bool Richang
        {
            get => _richang;
            set
            {
                if (_richang != value)
                {
                    _richang = value;
                    OnPropertyChanged(nameof(Richang));

                    if (_richang)
                    {
                        AllScenes = false;
                    }
                }
            }
        }

        public PresetConfig()
        {
            _name = string.Empty;
            _parameters = new ObservableCollection<PresetParameter>();
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 预设配置管理类
    /// </summary>
    public class PresetConfigManager
    {
        /// <summary>
        /// 预设配置存储的类别名称
        /// </summary>
        private static readonly string ConfigClass = "Presets";

        /// <summary>
        /// 预设配置文件名
        /// </summary>
        private static readonly string ConfigName = "PresetConfigs";

        private ObservableCollection<PresetConfig> _configs;

        /// <summary>
        /// 所有预设配置
        /// </summary>
        public ObservableCollection<PresetConfig> Configs
        {
            get => _configs;
            private set => _configs = value;
        }

        public PresetConfigManager()
        {
            _configs = new ObservableCollection<PresetConfig>();
            LoadConfigs();
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfigs()
        {
            var configs = XConfig.LoadValueFromFile<ObservableCollection<PresetConfig>>(ConfigClass, ConfigName);
            if (configs != null)
            {
                _configs = configs;
            }
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void ReloadConfigs()
        {
            _configs.Clear();
            LoadConfigs();
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfigs()
        {
            XConfig.SaveValueToFile(ConfigClass, ConfigName, _configs);
        }

        /// <summary>
        /// 添加新的预设配置
        /// </summary>
        public void AddConfig(PresetConfig config)
        {
            // 检查是否有重名
            if (_configs.Any(c => c.Name == config.Name))
            {
                // 可以选择抛出异常或返回错误信息
                throw new InvalidOperationException($"配置名 '{config.Name}' 已存在");
            }

            _configs.Add(config);
            SaveConfigs();
        }

        /// <summary>
        /// 更新预设配置
        /// </summary>
        public void UpdateConfig(PresetConfig config)
        {
            var existing = _configs.FirstOrDefault(c => c.Name == config.Name);
            if (existing != null)
            {
                int index = _configs.IndexOf(existing);
                _configs[index] = config;
                SaveConfigs();
            }
        }

        /// <summary>
        /// 删除预设配置
        /// </summary>
        public void DeleteConfig(string name)
        {
            var config = _configs.FirstOrDefault(c => c.Name == name);
            if (config != null)
            {
                _configs.Remove(config);
                SaveConfigs();
            }
        }

        /// <summary>
        /// 获取所有配置名称
        /// </summary>
        public IEnumerable<string> GetAllConfigNames()
        {
            return _configs.Select(c => c.Name).ToList();
        }
    }
}