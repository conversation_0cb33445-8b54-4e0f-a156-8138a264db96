﻿<Page
    x:Class="DanDing1.Views.Pages.UserConfigPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:DanDing1.Views.Pages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:DanDing1.Models"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    d:DataContext="{d:DesignInstance local:UserConfigPage,
    IsDesignTimeCreatable=False}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
    ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    ScrollViewer.CanContentScroll="False"
    mc:Ignorable="d">

    <StackPanel>
        <TextBlock
            Margin="0,8,0,0"
            FontSize="20"
            FontWeight="Medium"
            Text="您的脚本偏好设置"/>
        <Border
            Width="165"
            Height="2"
            HorizontalAlignment="Left"
            Background="{ui:ThemeResource CardBackgroundFillColorDefaultBrush}"
            BorderBrush="{ui:ThemeResource CardStrokeColorDefaultBrush}"
            BorderThickness="0,0,0,2"/>
        <TextBlock Text="使用您的账号可以云端保存此处的配置哦~"/>
        <ScrollViewer Height="450"
                      VerticalScrollBarVisibility="Auto"
                      CanContentScroll="True"
                      HorizontalScrollBarVisibility="Auto">
            <StackPanel>
                <!--通知合集-->
                <ui:CardExpander Icon="{ui:SymbolIcon MegaphoneLoud24}"
                                 Margin="0,10,10,0">
                    <ui:CardExpander.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:TextBlock
                                FontWeight="Black"
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="[通知] "/>
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="{Binding ViewModel.EnabledNoticeTypes}"/>
                        </StackPanel>
                    </ui:CardExpander.Header>
                    <StackPanel Margin="0 -5 0 0">
                        <!--WxPush-->
                        <StackPanel Orientation="Horizontal">
                            <CheckBox Content="WxPush"
                                      FontWeight="DemiBold"
                                      IsChecked="{Binding ViewModel.UserConfig.Notice_WxPush}"/>
                            <ui:TextBlock Margin="-10 -2 0 0"
                                          VerticalAlignment="Center"
                                          FontSize="16"
                                          Text="您的UID："/>
                            <TextBox MaxWidth="350"
                                     FontSize="16"
                                     Text="{Binding ViewModel.UserConfig.Notice_WxPush_UID}"/>
                            <ui:Button Padding="-5"
                                       Margin="5 0 0 0"
                                       Width="30"
                                       Height="30"
                                       VerticalAlignment="Center"
                                       ToolTip="点击扫码绑定公众号"
                                       Click="ShowQrCodeImage_Click">
                                <ui:SymbolIcon FontSize="20"
                                               Symbol="QrCode28"/>
                            </ui:Button>
                            <ui:Button Appearance="Secondary"
                                       Padding="-5"
                                       Margin="5 0 0 0"
                                       Width="30"
                                       Height="30"
                                       VerticalAlignment="Center"
                                       ToolTip="测试微信推送通知"
                                       Click="TestNotification_Click"
                                       Tag="微信推送">
                                <ui:SymbolIcon FontSize="18"
                                               Symbol="Send28"/>
                            </ui:Button>
                        </StackPanel>
                        <!--分割线-->
                        <Separator Margin="0 3 0 3"/>
                        <!--App通知-Ntfy-->
                        <StackPanel Orientation="Horizontal">
                            <CheckBox Content="App通知-Ntfy"
                                      FontWeight="DemiBold"
                                      IsChecked="{Binding ViewModel.UserConfig.Notice_Ntfy}"/>
                            <ui:TextBlock Margin="-4 -2 0 0"
                                          VerticalAlignment="Center"
                                          FontSize="16"
                                          Text="您的Key："/>
                            <TextBox MaxWidth="350"
                                     FontSize="16"
                                     Text="{Binding ViewModel.Notice_Ntfy_Key}"/>
                            <ui:Button Padding="-5"
                                       Margin="5 0 0 0"
                                       Width="30"
                                       Height="30"
                                       VerticalAlignment="Center"
                                       ToolTip="点击查看使用说明"
                                       Click="ShowNtfyHelp_Click">
                                <ui:SymbolIcon FontSize="20"
                                               Symbol="QuestionCircle24"/>
                            </ui:Button>
                            <ui:Button Appearance="Secondary"
                                       Padding="-5"
                                       Margin="5 0 0 0"
                                       Width="30"
                                       Height="30"
                                       VerticalAlignment="Center"
                                       ToolTip="测试App通知"
                                       Click="TestNotification_Click"
                                       Tag="App">
                                <ui:SymbolIcon FontSize="18"
                                               Symbol="Send28"/>
                            </ui:Button>
                        </StackPanel>
                        <!--分割线-->
                        <Separator Margin="0 3 0 3"/>
                        <!--Pushplus推送-->
                        <StackPanel Orientation="Horizontal">
                            <CheckBox Content="Pushplus推送"
                                      FontWeight="DemiBold"
                                      IsChecked="{Binding ViewModel.UserConfig.Notice_Pushplus}"/>
                            <ui:TextBlock Margin="-4 -2 0 0"
                                          VerticalAlignment="Center"
                                          FontSize="16"
                                          Text="您的Token："/>
                            <TextBox MaxWidth="300"
                                     FontSize="16"
                                     Text="{Binding ViewModel.UserConfig.Notice_Pushplus_Token}"/>
                            <ui:Button Padding="-5"
                                       Margin="5 0 0 0"
                                       Width="30"
                                       Height="30"
                                       VerticalAlignment="Center"
                                       ToolTip="点击查看使用说明"
                                       Click="ShowPushplusHelp_Click">
                                <ui:SymbolIcon FontSize="20"
                                               Symbol="QuestionCircle24"/>
                            </ui:Button>
                            <ui:Button Appearance="Secondary"
                                       Padding="-5"
                                       Margin="5 0 0 0"
                                       Width="30"
                                       Height="30"
                                       VerticalAlignment="Center"
                                       ToolTip="测试Pushplus推送通知"
                                       Click="TestNotification_Click"
                                       Tag="Pushplus推送">
                                <ui:SymbolIcon FontSize="18"
                                               Symbol="Send28"/>
                            </ui:Button>
                        </StackPanel>
                        <!--分割线-->
                        <Separator Margin="0 3 0 3"/>
                        <!--喵提醒-->
                        <StackPanel Orientation="Horizontal">
                            <CheckBox Content="喵提醒"
                                      FontWeight="DemiBold"
                                      IsChecked="{Binding ViewModel.UserConfig.Notice_Miaotixing}"/>
                            <ui:TextBlock Margin="-4 -2 0 0"
                                          VerticalAlignment="Center"
                                          FontSize="16"
                                          Text="您的喵码："/>
                            <TextBox MaxWidth="350"
                                     FontSize="16"
                                     Text="{Binding ViewModel.UserConfig.Notice_Miaotixing_ID}"/>
                            <ui:Button Padding="-5"
                                       Margin="5 0 0 0"
                                       Width="30"
                                       Height="30"
                                       VerticalAlignment="Center"
                                       ToolTip="点击查看使用说明"
                                       Click="ShowMiaotixingHelp_Click">
                                <ui:SymbolIcon FontSize="20"
                                               Symbol="QuestionCircle24"/>
                            </ui:Button>
                            <ui:Button Appearance="Secondary"
                                       Padding="-5"
                                       Margin="5 0 0 0"
                                       Width="30"
                                       Height="30"
                                       VerticalAlignment="Center"
                                       ToolTip="测试喵提醒通知"
                                       Click="TestNotification_Click"
                                       Tag="喵提醒">
                                <ui:SymbolIcon FontSize="18"
                                               Symbol="Send28"/>
                            </ui:Button>
                        </StackPanel>
                    </StackPanel>
                </ui:CardExpander>
                <!--全局预设-->
                <ui:CardExpander Icon="{ui:SymbolIcon GlobeSurface24}"
                                 Margin="0,2,10,0">
                    <ui:CardExpander.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:TextBlock
                                FontWeight="Black"
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="[全局预设] "/>
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="使你的所有游戏中的某任务应用同一位置的预设方案："/>
                            <ui:ToggleSwitch
                                IsChecked="{Binding ViewModel.UserConfig.Gal_YuShe}"
                                OffContent="关"
                                OnContent="开"/>
                        </StackPanel>
                    </ui:CardExpander.Header>
                    <StackPanel Margin="10 -5 0 0">
                        <ui:TextBlock
                            VerticalAlignment="Center"
                            FontSize="16"
                            Text="设置注意点：第一个数字为预设组号、第二个数字为预设编号，如 1-1、1-1|2-1"
                            FontWeight="Black"/>
                        <ui:TextBlock
                            Margin="0 2 0 0"
                            VerticalAlignment="Center"
                            FontSize="16"
                            Text="                一次应用多预设设置，如 1-1|2-1；组号支持1-8组，预设号支持1-4号"
                            FontWeight="Black"/>
                        <Separator Margin="0 3 0 3"/>
                        <StackPanel Orientation="Horizontal">
                            <!--御魂-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="御魂："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Yuhun}"/>
                            </StackPanel>
                            <!--觉醒-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="觉醒："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Juexing}"/>
                            </StackPanel>
                            <!--探索-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="探索："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Tansuo}"/>
                            </StackPanel>
                            <!--日轮-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="日轮："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Rilun}"/>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <!--永生-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="永生："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Yongsheng}"/>
                            </StackPanel>
                            <!--御灵-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="御灵："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Yuling}"/>
                            </StackPanel>
                            <!--业原火-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="业原火："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Yeyuanhuo}"/>
                            </StackPanel>
                            <!--突破-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="突破："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Tupo}"/>
                            </StackPanel>
                        </StackPanel>

                        <StackPanel Orientation="Horizontal">
                            <!--悬赏-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="悬赏："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Xuanshang}"/>
                            </StackPanel>
                            <!--六道-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="六道："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Liudao}"/>
                            </StackPanel>
                            <!--契灵-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="契灵："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Qiling}"/>
                            </StackPanel>
                            <!--英杰试炼-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="英杰试炼："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Yingjie}"/>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <!--斗技-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="斗技："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Douji}"/>

                            </StackPanel>
                            <!--日常-->
                            <StackPanel Margin="10 2"
                                        Orientation="Horizontal">
                                <ui:TextBlock  VerticalAlignment="Center"
                                              FontSize="16"
                                              Text="日常："/>
                                <TextBox MaxWidth="350"
                                         FontSize="16"
                                         Text="{Binding ViewModel.UserConfig.Gal_YuShe_Daily}"/>
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>
                </ui:CardExpander>
                <!--突破满30打图片-->
                <ui:CardExpander Icon="{ui:SymbolIcon Broom24}"
                                 Margin="0,2,10,0">
                    <ui:CardExpander.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:TextBlock
                                FontWeight="Black"
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="[突破] "/>
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="执行任务前检查突破卷数量，若突破卷满30张就去打突破："/>
                            <ui:ToggleSwitch
                                IsChecked="{Binding ViewModel.UserConfig.Tupo30run}"
                                OffContent="关"
                                OnContent="开"/>
                        </StackPanel>
                    </ui:CardExpander.Header>
                    <StackPanel Orientation="Horizontal">
                        <ui:TextBlock
                            VerticalAlignment="Center"
                            FontSize="16"
                            Text="   •清卷设置："
                            FontWeight="Black"/>
                        <ui:TextBlock
                            VerticalAlignment="Center"
                            FontSize="15"
                            Text="战斗次数："/>
                        <TextBox Text="{Binding ViewModel.UserConfig.Tupo30_Count}"/>
                        <ui:TextBlock
                            VerticalAlignment="Center"
                            FontSize="15"
                            Text="  | 标记位置："/>
                        <ComboBox ItemsSource="{Binding ViewModel.Tpo_Biaojis}"
                                  SelectedItem="{Binding ViewModel.UserConfig.Tupo30_Biaoji}"/>
                        <TextBlock FrameworkElement.VerticalAlignment="Center"
                                   TextBlock.Text="  | 卡级功能："/>
                        <ui:ToggleSwitch
                            IsChecked="{Binding ViewModel.UserConfig.Tupo30_Tui4}"
                            OffContent="关"
                            OnContent="开"
                            ToolTipService.ToolTip="开启后最后一个目标默认失败4次"/>
                    </StackPanel>
                </ui:CardExpander>
                <!--自动喂养默认开启-->
                <ui:CardExpander Icon="{ui:SymbolIcon Food24}"
                                 Margin="0,2,10,0">
                    <ui:CardExpander.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:TextBlock
                                FontWeight="Black"
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="[御魂] "/>
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="自动喂养宠物开关："/>
                            <ui:ToggleSwitch
                                IsChecked="{Binding ViewModel.UserConfig.YuhunFood}"
                                OffContent="关"
                                OnContent="开"/>
                        </StackPanel>
                    </ui:CardExpander.Header>
                    <ui:TextBlock
                        VerticalAlignment="Center"
                        FontSize="16"
                        Text="自动喂养默认开启，检测到待投喂图标就会喂养宠物（在御魂战斗过程中）"
                        FontWeight="Black"/>
                </ui:CardExpander>
                <!--同一任务战斗失败过多时，提前停止脚本-->
                <ui:CardExpander Icon="{ui:SymbolIcon ShieldTask20}"
                                 Margin="0,2,10,0">
                    <ui:CardExpander.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:TextBlock
                                FontWeight="Black"
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="[防封] "/>
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="同一任务战斗失败过多时，提前停止脚本："/>
                            <TextBox
                                Width="40"
                                LostFocus="TextBox_LostFocus"
                                Text="{Binding ViewModel.UserConfig.DefeatedEnd}"/>
                            <ui:TextBlock
                                Margin="5,0,0,0"
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="次后停止；"/>
                        </StackPanel>
                    </ui:CardExpander.Header>
                    <ui:TextBlock Text="注意：不需要开启此功能可填 0 ；"/>
                </ui:CardExpander>
                <!--当游戏提示御魂满后执行的操作-->
                <ui:CardExpander Icon="{ui:SymbolIcon ShieldTask20}"
                                 Margin="0,2,10,0">
                    <ui:CardExpander.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:TextBlock
                                FontWeight="Black"
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="[防封] "/>
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="当游戏提示御魂满后执行的操作："/>
                            <ComboBox
                                ItemsSource="{Binding ViewModel.FullYuHun_DoLists}"
                                SelectedItem="{Binding ViewModel.UserConfig.FullYuHun_Do}"/>
                        </StackPanel>
                    </ui:CardExpander.Header>
                    <StackPanel>
                        <ui:TextBlock Text="注意：1、部分任务支持不需要重启游戏的御魂清理，参见下表："/>
                        <ui:TextBlock Text="               单人御魂、御灵、业原火、单人探索、日轮、觉醒、契灵、活动"/>
                        <ui:TextBlock Margin="1 2 0 0"
                                      Text="          2、重启需要模拟器开启ADB调试，以及设置中的MuMu配置；"/>
                    </StackPanel>
                </ui:CardExpander>
                <!--任务之间、每次战斗之间的随机等待概率-->
                <ui:CardExpander Icon="{ui:SymbolIcon ShieldTask20}"
                                 Margin="0,2,10,0">
                    <ui:CardExpander.Header>
                        <StackPanel   Orientation="Horizontal">
                            <ui:TextBlock
                                FontWeight="Black"
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="[防封] "/>
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="任务之间、每次战斗之间的随机等待概率："/>
                            <TextBox
                                LostFocus="DecimalTextBox_LostFocus"
                                Text="{Binding ViewModel.UserConfig.RandomWait}"/>
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="  %"/>
                        </StackPanel>
                    </ui:CardExpander.Header>
                    <ui:TextBlock Text="注意：不需要开启此功能可填 0 ；"/>
                </ui:CardExpander>
                <!--任务执行过程中随机穿插小纸人的概率-->
                <ui:CardExpander Icon="{ui:SymbolIcon ShieldTask20}"
                                 Margin="0,2,10,0">
                    <ui:CardExpander.Header>
                        <StackPanel  Orientation="Horizontal">
                            <ui:TextBlock
                                FontWeight="Black"
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="[防封] "/>
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="任务执行过程中随机穿插小纸人的概率："/>
                            <TextBox
                                LostFocus="DecimalTextBox_LostFocus"
                                Text="{Binding ViewModel.UserConfig.RandomYysAuto}"/>
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="  %"/>
                        </StackPanel>
                    </ui:CardExpander.Header>
                    <StackPanel>
                        <ui:TextBlock Text="注意1：不需要开启此功能可填 0 ；"/>
                        <ui:TextBlock Text="注意2：建议概率填小一点可以为0.1%，该功能一般爬塔啥的用；"/>
                        <ui:TextBlock Text="注意3：当前仅支持任务《御魂单人、探索单人、御灵、业原火、觉醒、日轮、契灵》；"/>
                    </StackPanel>
                </ui:CardExpander>
                <!--发生卡屏后，执行的操作-->
                <ui:CardExpander Icon="{ui:SymbolIcon ShieldTask20}"
                                 Margin="0,2,10,0">
                    <ui:CardExpander.Header>
                        <StackPanel  Orientation="Horizontal">
                            <ui:TextBlock
                                FontWeight="Black"
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="[防封] "/>
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="发生卡屏后，执行的操作："/>
                            <ComboBox
                                ItemsSource="{Binding ViewModel.KaPing_DoLists}"
                                SelectedItem="{Binding ViewModel.UserConfig.KaPing_Do}"/>
                        </StackPanel>
                    </ui:CardExpander.Header>
                    <ui:TextBlock Text="注意：不需要开启此功能可填 0 ；"/>
                </ui:CardExpander>
                <!--自定义自己常用的点击范围-->
                <ui:CardExpander Icon="{ui:SymbolIcon ShieldTask20}"
                                 Margin="0,2,10,0">
                    <ui:CardExpander.Header>
                        <StackPanel  Orientation="Horizontal">
                            <ui:TextBlock
                                FontWeight="Black"
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="[防封] "/>
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="15"
                                Text="自定义自己常用的点击范围"/>
                            <ui:TextBlock
                                Margin="10 0 0 0"
                                VerticalAlignment="Bottom"
                                FontSize="12"
                                FontWeight="UltraLight"
                                Text="定制自己常用的点击位置！"/>
                        </StackPanel>
                    </ui:CardExpander.Header>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="16"
                                Text="   •达摩奖励界面："
                                FontWeight="Black"/>
                            <ComboBox ItemsSource="{Binding ViewModel.CustClick_Damos}"
                                      SelectedItem="{Binding ViewModel.UserConfig.CustClick_Damo}"/>
                            <ui:Button Padding="-5"
                                       Margin="5 0 0 0"
                                       Width="30"
                                       Height="30"
                                       VerticalAlignment="Center"
                                       ToolTip="点击查看详细说明"
                                       Click="ShowDamoHelpImage_Click">
                                <ui:SymbolIcon FontSize="20"
                                               Symbol="QuestionCircle24"/>
                            </ui:Button>
                        </StackPanel>
                        <StackPanel Margin="0 5 0 0"
                                    Orientation="Horizontal">
                            <ui:TextBlock
                                VerticalAlignment="Center"
                                FontSize="16"
                                Text="   •胜利、失败界面："
                                FontWeight="Black"/>
                            <ComboBox ItemsSource="{Binding ViewModel.CustClick_Results}"
                                      SelectedItem="{Binding ViewModel.UserConfig.CustClick_Result}"/>
                            <ui:Button Padding="-5"
                                       Margin="5 0 0 0"
                                       Width="30"
                                       Height="30"
                                       VerticalAlignment="Center"
                                       ToolTip="点击查看详细说明"
                                       Click="ShowResultHelpImage_Click">
                                <ui:SymbolIcon FontSize="20"
                                               Symbol="QuestionCircle24"/>
                            </ui:Button>
                        </StackPanel>
                    </StackPanel>
                </ui:CardExpander>
            </StackPanel>
        </ScrollViewer>
    </StackPanel>
</Page>