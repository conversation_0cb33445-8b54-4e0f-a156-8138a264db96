﻿<Window
    x:Class="DanDing1.Views.Windows.TeamWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:DanDing1.Views.Windows"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="组队-队友选择器"
    Width="500"
    Height="300"
    d:DataContext="{d:DesignInstance local:TeamWindow,
    IsDesignTimeCreatable=True}"
    d:DesignHeight="278"
    d:DesignWidth="500"
    ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
    ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    Background="{DynamicResource ApplicationBackgroundBrush}"
    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    ResizeMode="NoResize"
    ScrollViewer.CanContentScroll="False"
    mc:Ignorable="d">
    <StackPanel>
        <StackPanel Margin="5,0,0,0"
                    Orientation="Horizontal">
            <ui:TextBlock
                VerticalAlignment="Center"
                FontSize="14"
                FontWeight="Black"
                Text="窗口句柄：" />
            <ui:TextBlock
                x:Name="Screenshot_HWND"
                VerticalAlignment="Center"
                Text="" />
            <ui:TextBlock
                Margin="10,0,0,0"
                VerticalAlignment="Center"
                FontSize="14"
                FontWeight="Black"
                Text="ID位号：" />
            <ComboBox x:Name="Screenshot_ID" />
            <Button
                Margin="5,0,5,0"
                Click="Button_Click"
                Content="截图" />
        </StackPanel>
        <StackPanel Margin="5,5,0,0"
                    Orientation="Horizontal">
            <ui:TextBlock
                VerticalAlignment="Center"
                FontSize="14"
                FontWeight="Black"
                Text="ID预览：" />
            <StackPanel>
                <ui:Image
                    x:Name="Screenshot"
                    Width="176"
                    Height="28" />
                <ui:TextBlock
                    x:Name="Ocr_Name"
                    MouseLeftButtonUp="Ocr_Name_MouseLeftButtonUp"
                    HorizontalAlignment="Left"
                    FontSize="12" />
            </StackPanel>
            <ComboBox x:Name="TeamName"
                      Margin="3,0,0,0" />
            <ui:Button
                Margin="5,0,0,0"
                Click="Button_Click_1"
                Content="保存到列表" />
            <ui:TextBlock
                x:Name="Status_Tip"
                Margin="5,0,0,0"
                VerticalAlignment="Center"
                FontSize="14" />
        </StackPanel>
        <StackPanel Orientation="Horizontal">
            <StackPanel Width="200"
                        Margin="5,5,0,0">
                <ui:TextBlock
                    FontSize="14"
                    FontWeight="Black"
                    Text="组队-队友选择器提示您：" />
                <ui:TextBlock Margin="0,3,0,0"
                              FontSize="12"
                              Text="1.选择目标队友ID的位号后点击截图." />
                <ui:TextBlock Margin="0,3,0,0"
                              FontSize="12"
                              Text="2.截图后观察预览图是不是目标ID." />
                <ui:TextBlock Margin="0,3,0,0"
                              FontSize="12"
                              Text="3.若列表中没有点击保存到列表." />
                <ui:TextBlock Margin="0,3,0,0"
                              FontSize="12"
                              Text="4.在列表中选中目标ID后点击选中." />
                <ui:TextBlock Margin="0,8,0,0"
                              FontSize="12"
                              Text="因此为前台截图，游戏不能被遮挡！" />
            </StackPanel>
            <StackPanel Width="140">
                <ListBox x:Name="ListBox_TeamName"
                         Height="155">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <StackPanel VerticalAlignment="Center"
                                        Orientation="Horizontal">
                                <!--  自定义每个Item的内容  -->
                                <TextBlock
                                    VerticalAlignment="Center"
                                    FontSize="12"
                                    Text="{Binding}" />
                            </StackPanel>
                        </DataTemplate>
                    </ListBox.ItemTemplate>

                    <!--  调整ListBoxItem的高度和Margin  -->
                    <ListBox.ItemContainerStyle>
                        <Style TargetType="ListBoxItem">
                            <Setter Property="Height"
                                    Value="35" />
                            <!--  设置Item的高度  -->
                            <Setter Property="Width"
                                    Value="140" />
                            <!--  设置Item的高度  -->
                            <Setter Property="Margin"
                                    Value="2,2,10,2" />
                            <!--  控制每个Item的外边距  -->
                        </Style>
                    </ListBox.ItemContainerStyle>
                </ListBox>
            </StackPanel>
            <StackPanel HorizontalAlignment="Right"
                        VerticalAlignment="Bottom">
                <ui:Button
                    Margin="5"
                    Click="Button_Click_2"
                    Content="❌删除" />
                <ui:Button
                    Margin="5"
                    Click="Button_Click_3"
                    Content="✔选中" />
            </StackPanel>
        </StackPanel>
    </StackPanel>
</Window>