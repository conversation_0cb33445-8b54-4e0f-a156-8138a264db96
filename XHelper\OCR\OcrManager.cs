using System;

namespace XHelper.OCR
{
    /// <summary>
    /// OCR管理器类，采用单例模式管理OCR相关资源
    /// </summary>
    internal class OcrManager
    {
        private static readonly Lazy<OcrManager> _instance = new Lazy<OcrManager>(() => new OcrManager());

        /// <summary>
        /// 获取OcrManager的单例实例
        /// </summary>
        public static OcrManager Instance => _instance.Value;

        /// <summary>
        /// OCR配置
        /// </summary>
        public OcrConfiguration Configuration { get; }

        /// <summary>
        /// 图像处理器
        /// </summary>
        public IImageProcessor ImageProcessor { get; }

        /// <summary>
        /// 专用于PaddleOCR的图像处理器
        /// </summary>
        public PaddleOcrImageProcessor PaddleImageProcessor { get; }

        /// <summary>
        /// OCR引擎工厂
        /// </summary>
        public OcrEngineFactory EngineFactory { get; }

        /// <summary>
        /// 私有构造函数，防止外部实例化
        /// </summary>
        private OcrManager()
        {
            Configuration = new OcrConfiguration();
            ImageProcessor = new OpenCvImageProcessor(Configuration);
            PaddleImageProcessor = new PaddleOcrImageProcessor(Configuration);
            EngineFactory = new OcrEngineFactory(Configuration, ImageProcessor);
        }

        /// <summary>
        /// 设置是否保存临时优化图像
        /// </summary>
        /// <param name="save">是否保存</param>
        public void SetSaveTempImages(bool save)
        {
            Configuration.SaveTempImages = save;
            ImageProcessor.SetSaveTempImages(save);
            PaddleImageProcessor.SetSaveTempImages(save);
        }

        /// <summary>
        /// 获取指定类型的OCR引擎
        /// </summary>
        /// <param name="engineType">引擎类型</param>
        /// <returns>OCR引擎实例</returns>
        public IOcrEngine GetEngine(OcrEngineType engineType)
        {
            return EngineFactory.CreateEngine(engineType);
        }
    }
}