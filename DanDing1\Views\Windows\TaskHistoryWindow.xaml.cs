using DanDing1.ViewModels.Windows;
using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using XHelper;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// TaskHistoryWindow.xaml 的交互逻辑
    /// </summary>
    public partial class TaskHistoryWindow : Window
    {
        private TaskHistoryViewModel _viewModel;

        /// <summary>
        /// 构造函数
        /// </summary>
        public TaskHistoryWindow()
        {
            // 在设计时可能看不到该方法，但它将在编译时由 XAML 编译器生成
            InitializeComponent();
            _viewModel = DataContext as TaskHistoryViewModel;

            // 注册窗口关闭事件
            this.Closing += Window_Closing;
        }

        /// <summary>
        /// 初始化窗口
        /// </summary>
        /// <param name="emulatorNames">可用的模拟器名称列表</param>
        public void Initialize(List<string> emulatorNames)
        {
            _viewModel?.Initialize(emulatorNames);
        }

        /// <summary>
        /// 历史记录选择变更事件处理
        /// </summary>
        private void HistoryRecords_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 选择变更事件处理
            // 注意：绑定已经自动将选中项设置到ViewModel的SelectedRecord属性

            // 如果需要可以添加额外的逻辑，比如自动展开详细信息面板等
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // 通过将IsAutoRefreshEnabled设为false来停止自动刷新定时器
                if (_viewModel != null)
                {
                    _viewModel.IsAutoRefreshEnabled = false;
                }

                XLogger.Debug("任务历史记录窗口已关闭，自动刷新已停止");
            }
            catch (System.Exception ex)
            {
                XLogger.Error($"关闭任务历史记录窗口时出错: {ex.Message}");
            }
        }
    }
}