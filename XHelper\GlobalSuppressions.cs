﻿// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.

using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Interoperability", "CA1416:验证平台兼容性", Justification = "<挂起>", Scope = "member", Target = "~M:XHelper.XImageProcessor.SaveAsCompressedJpeg(System.Drawing.Image,System.Int64)~System.Byte[]")]
[assembly: SuppressMessage("Interoperability", "CA1416:验证平台兼容性", Justification = "<挂起>", Scope = "member", Target = "~M:XHelper.XImageProcessor.ConfigureHighQualityGraphics(System.Drawing.Graphics)")]
[assembly: SuppressMessage("Interoperability", "CA1416:验证平台兼容性", Justification = "<挂起>", Scope = "member", Target = "~M:XHelper.XImageProcessor.GenerateThumbnail(System.Byte[],System.Int32)~System.Byte[]")]
[assembly: SuppressMessage("Interoperability", "CA1416:验证平台兼容性", Justification = "<挂起>", Scope = "member", Target = "~M:XHelper.XImageProcessor.GetJpegEncoder~System.Drawing.Imaging.ImageCodecInfo")]
