using DanDing1.Models;
using DanDing1.ViewModels.Pages;
using DanDing1.ViewModels.Windows;
using ScriptEngine.MuMu;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using XHelper;

namespace DanDing1.Services
{
    /// <summary>
    /// 配置导出导入服务
    /// </summary>
    public class ConfigExportImportService
    {
        // 序列化选项
        private readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            IgnoreNullValues = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        /// <summary>
        /// 导出系统配置到文件
        /// </summary>
        /// <param name="filePath">导出文件路径</param>
        /// <param name="emulators">模拟器列表</param>
        /// <param name="tasks">任务列表</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ExportConfigAsync(string filePath,
            List<EmulatorItem> emulators,
            List<ScheduledTask> tasks)
        {
            try
            {
                XLogger.Info($"开始导出系统配置到文件: {filePath}");

                // 创建导出模型
                var exportModel = new ConfigExportModel
                {
                    ExportTime = DateTime.Now,
                    Version = "1.0"
                };

                // 导出模拟器配置
                foreach (var emulator in emulators)
                {
                    exportModel.Emulators.Add(new EmulatorExportItem
                    {
                        Name = emulator.Name,
                        SimulatorIndex = emulator.SimulatorIndex,
                        SimulatorConfig = emulator.SimulatorConfig,
                        Config = emulator.Config
                    });
                }

                // 导出任务配置
                foreach (var task in tasks)
                {
                    // 查找关联的模拟器 - 使用EmulatorName而非EmulatorId
                    var emulator = emulators.FirstOrDefault(e => e.Name == task.EmulatorName);
                    string emulatorName = emulator?.Name ?? string.Empty;

                    exportModel.ScheduledTasks.Add(new ScheduledTaskExport
                    {
                        Id = task.Id,
                        Name = task.Name,
                        Description = "", // 使用空字符串作为默认描述
                        Enabled = task.Enabled,
                        EmulatorName = task.EmulatorName,
                        CronExpression = "", // 使用空字符串作为默认Cron表达式
                        TaskParameters = task.TaskParameters
                        // TaskProperty属性不存在，所以不包含
                    });
                }

                // 导出系统配置
                exportModel.SystemConfig = LoadSystemConfig();

                // 序列化为JSON并保存到文件
                string jsonString = JsonSerializer.Serialize(exportModel, _jsonOptions);
                await File.WriteAllTextAsync(filePath, jsonString, Encoding.UTF8);

                XLogger.Info($"成功导出系统配置到文件: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                XLogger.Error($"导出系统配置失败: {ex.Message}");
                XLogger.SaveException(ex);
                return false;
            }
        }

        /// <summary>
        /// 导入系统配置
        /// </summary>
        /// <param name="filePath">导入文件路径</param>
        /// <param name="currentEmulators">当前模拟器列表</param>
        /// <param name="currentTasks">当前任务列表</param>
        /// <returns>导入报告</returns>
        public async Task<ImportReport> ImportConfigAsync(string filePath,
            List<EmulatorItem> currentEmulators,
            List<ScheduledTask> currentTasks)
        {
            var report = new ImportReport();
            var details = new StringBuilder();

            try
            {
                XLogger.Info($"开始从文件导入系统配置: {filePath}");

                // 检查文件是否存在
                if (!File.Exists(filePath))
                {
                    XLogger.Error($"导入文件不存在: {filePath}");
                    report.Success = false;
                    report.Details = $"导入文件不存在: {filePath}";
                    return report;
                }

                // 读取并反序列化文件内容
                string jsonString = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
                var importModel = JsonSerializer.Deserialize<ConfigExportModel>(jsonString, _jsonOptions);

                if (importModel == null)
                {
                    XLogger.Error("导入文件格式无效");
                    report.Success = false;
                    report.Details = "导入文件格式无效";
                    return report;
                }

                details.AppendLine($"导出版本: {importModel.Version}");
                details.AppendLine($"导出时间: {importModel.ExportTime}");
                details.AppendLine($"包含模拟器数量: {importModel.Emulators.Count}");
                details.AppendLine($"包含任务数量: {importModel.ScheduledTasks.Count}");
                details.AppendLine();

                // 验证模拟器名称并导入模拟器配置
                report.TotalEmulators = importModel.Emulators.Count;
                await ImportEmulatorsAsync(importModel.Emulators, currentEmulators, report, details);

                // 导入任务配置
                await ImportTasksAsync(importModel.ScheduledTasks, currentEmulators, currentTasks, report, details);

                // 导入系统配置
                await ImportSystemConfigAsync(importModel.SystemConfig);

                // 完成导入，保存更改到磁盘
                await SaveConfigurationsAsync(currentEmulators, currentTasks);

                // 设置需要刷新UI
                report.RequiresUIRefresh = true;

                // 完成导入
                report.Success = report.FailedEmulatorNames.Count == 0;
                report.Details = details.ToString();

                XLogger.Info($"系统配置导入完成，成功导入模拟器: {report.ImportedEmulators}，任务: {report.ImportedTasks}");
                return report;
            }
            catch (Exception ex)
            {
                XLogger.Error($"导入系统配置失败: {ex.Message}");
                XLogger.SaveException(ex);

                report.Success = false;
                report.Details = $"导入失败: {ex.Message}\n\n{details}";
                return report;
            }
        }

        /// <summary>
        /// 导入模拟器配置
        /// </summary>
        private async Task ImportEmulatorsAsync(List<EmulatorExportItem> importEmulators,
            List<EmulatorItem> currentEmulators,
            ImportReport report,
            StringBuilder details)
        {
            details.AppendLine("\n【模拟器配置导入】");

            // 初始化MuMu模拟器，用于获取当前模拟器索引情况
            var mumu = new MuMu();
            string path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (!string.IsNullOrEmpty(path))
            {
                mumu.Init(path);
            }

            // 获取系统中实际安装的模拟器实例
            var instances = await mumu._GetInstancesAsync();

            details.AppendLine("本机系统中的模拟器实例：");
            if (instances != null && instances.Instances.Count > 0)
            {
                foreach (var instance in instances.Instances.Values)
                {
                    details.AppendLine($"- 系统模拟器: [{instance.Name}], 索引: {instance.Index}");
                }
            }
            else
            {
                details.AppendLine("- 未找到系统中安装的模拟器实例");
            }

            details.AppendLine("\n软件中的模拟器配置：");
            foreach (var emu in currentEmulators)
            {
                details.AppendLine($"- 软件模拟器: [{emu.Name}], 索引: {emu.SimulatorIndex}");
            }

            int successCount = 0;
            foreach (var importEmulator in importEmulators)
            {
                details.AppendLine($"\n处理导入模拟器: [{importEmulator.Name}]");

                // 1. 先在系统中查找匹配实例
                bool foundSystemInstance = false;
                string systemInstanceIndex = null;

                if (instances != null && instances.Instances.Count > 0)
                {
                    foreach (var instance in instances.Instances.Values)
                    {
                        if (string.Equals(instance.Name.Trim(), importEmulator.Name.Trim(),
                                         StringComparison.OrdinalIgnoreCase))
                        {
                            systemInstanceIndex = instance.Index;
                            foundSystemInstance = true;
                            details.AppendLine($"- 在系统中找到匹配的模拟器实例: [{instance.Name}]，索引: {systemInstanceIndex}");
                            break;
                        }
                    }
                }

                if (!foundSystemInstance)
                {
                    details.AppendLine($"- 系统中未找到模拟器 [{importEmulator.Name}] 的实例，请先安装该模拟器");
                    report.FailedEmulatorNames.Add(importEmulator.Name);
                    continue;
                }

                // 2. 在软件中查找匹配配置
                var matchedEmulator = currentEmulators
                    .FirstOrDefault(e => string.Equals(e.Name.Trim(), importEmulator.Name.Trim(),
                                                     StringComparison.OrdinalIgnoreCase));

                try
                {
                    if (matchedEmulator != null)
                    {
                        // 已有配置，更新它
                        details.AppendLine($"- 软件中已有此模拟器配置，进行更新");

                        // 使用系统中的实际索引
                        matchedEmulator.SimulatorIndex = systemInstanceIndex;

                        // 更新其他配置
                        matchedEmulator.SimulatorConfig = importEmulator.SimulatorConfig;
                        matchedEmulator.Config = importEmulator.Config;
                    }
                    else
                    {
                        // 软件中没有此模拟器配置，创建新的
                        details.AppendLine($"- 软件中没有此模拟器配置，创建新的");

                        var newEmulator = new EmulatorItem
                        {
                            Id = GetNextEmulatorId(currentEmulators), // 使用安全的ID生成方法
                            Name = importEmulator.Name,
                            SimulatorIndex = systemInstanceIndex,
                            SimulatorConfig = importEmulator.SimulatorConfig,
                            Config = importEmulator.Config,
                            Enabled = true,
                            Status = "离线"
                        };

                        // 添加到当前列表
                        currentEmulators.Add(newEmulator);
                    }

                    successCount++;
                    report.ImportedEmulators++;
                    details.AppendLine($"- 成功导入模拟器 [{importEmulator.Name}] 配置");
                }
                catch (Exception ex)
                {
                    details.AppendLine($"- 导入模拟器 [{importEmulator.Name}] 配置时出错: {ex.Message}");
                    report.FailedEmulatorNames.Add(importEmulator.Name);
                }
            }

            details.AppendLine($"\n共成功导入 {successCount} 个模拟器配置");
        }

        /// <summary>
        /// 导入任务配置
        /// </summary>
        private async Task ImportTasksAsync(List<ScheduledTaskExport> importTasks,
            List<EmulatorItem> currentEmulators,
            List<ScheduledTask> currentTasks,
            ImportReport report,
            StringBuilder details)
        {
            details.AppendLine("任务导入情况：");

            foreach (var importTask in importTasks)
            {
                // 查找关联的模拟器
                var matchingEmulator = currentEmulators.FirstOrDefault(e => e.Name == importTask.EmulatorName);

                if (matchingEmulator == null)
                {
                    // 如果找不到匹配的模拟器，跳过该任务
                    report.SkippedTasks++;
                    details.AppendLine($"- 跳过任务 [{importTask.Name}]，未找到关联的模拟器 [{importTask.EmulatorName}]");
                    continue;
                }

                // 查找是否有相同ID的任务
                var existingTask = currentTasks.FirstOrDefault(t => t.Id == importTask.Id);

                if (existingTask != null)
                {
                    // 更新已有任务 - 只使用实际存在的属性
                    existingTask.Name = importTask.Name;
                    existingTask.Enabled = importTask.Enabled;
                    existingTask.EmulatorName = importTask.EmulatorName;
                    existingTask.TaskParameters = importTask.TaskParameters;

                    // 如果任务有额外的属性，可以在这里设置
                    // 例如：existingTask.Status = "已更新";

                    report.UpdatedTasks++;
                    details.AppendLine($"- 已更新任务 [{importTask.Name}]，关联模拟器: [{importTask.EmulatorName}]");
                }
                else
                {
                    try
                    {
                        // 创建新任务 - 只使用实际存在的属性
                        var newTask = new ScheduledTask
                        {
                            Id = Guid.NewGuid().ToString().Substring(0, 5), // 生成新ID，避免冲突，只取前5个字符与创建新任务保持一致
                            Name = importTask.Name,
                            Enabled = importTask.Enabled,
                            EmulatorName = importTask.EmulatorName,
                            TaskParameters = importTask.TaskParameters
                            // 可以设置其他默认值
                            // Status = "已导入"
                        };

                        currentTasks.Add(newTask);
                        report.NewTasks++;
                        details.AppendLine($"- 已添加新任务 [{importTask.Name}]，关联模拟器: [{importTask.EmulatorName}]");
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"创建新任务 [{importTask.Name}] 失败: {ex.Message}");
                        details.AppendLine($"- 创建任务 [{importTask.Name}] 失败: {ex.Message}");
                    }
                }

                report.ImportedTasks++;
            }

            details.AppendLine();
        }

        /// <summary>
        /// 导入系统配置
        /// </summary>
        private async Task ImportSystemConfigAsync(Models.SchedulerConfig importConfig)
        {
            try
            {
                // 获取配置文件路径
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SchedulerConfig.json");

                // 如果配置文件存在，先读取现有配置
                Models.SchedulerConfig config = importConfig;
                if (File.Exists(configPath))
                {
                    string json = await File.ReadAllTextAsync(configPath);
                    var currentConfig = JsonSerializer.Deserialize<Models.SchedulerConfig>(json);

                    if (currentConfig != null)
                    {
                        // 合并配置
                        config = currentConfig;
                        config.MaxConcurrentEmulators = importConfig.MaxConcurrentEmulators;
                        config.DefaultTaskTimeout = importConfig.DefaultTaskTimeout;
                        config.AutoShutdownIdleTime = importConfig.AutoShutdownIdleTime;
                        config.EnableTaskRetry = importConfig.EnableTaskRetry;
                        config.MaxRetryCount = importConfig.MaxRetryCount;
                    }
                }

                // 保存配置到文件
                string newJson = JsonSerializer.Serialize(config, _jsonOptions);
                await File.WriteAllTextAsync(configPath, newJson, Encoding.UTF8);

                XLogger.Info("系统配置导入成功");
            }
            catch (Exception ex)
            {
                XLogger.Error($"导入系统配置失败: {ex.Message}");
                XLogger.SaveException(ex);
                throw;
            }
        }

        /// <summary>
        /// 加载系统配置
        /// </summary>
        private Models.SchedulerConfig LoadSystemConfig()
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SchedulerConfig.json");

                if (File.Exists(configPath))
                {
                    string json = File.ReadAllText(configPath);
                    var config = JsonSerializer.Deserialize<Models.SchedulerConfig>(json);

                    if (config != null)
                    {
                        return config;
                    }
                }

                // 如果文件不存在或解析失败，返回默认配置
                return new Models.SchedulerConfig();
            }
            catch (Exception ex)
            {
                XLogger.Error($"加载系统配置失败: {ex.Message}");
                XLogger.SaveException(ex);
                return new Models.SchedulerConfig();
            }
        }

        /// <summary>
        /// 保存所有配置到磁盘
        /// </summary>
        private async Task SaveConfigurationsAsync(List<EmulatorItem> emulators, List<ScheduledTask> tasks)
        {
            try
            {
                XLogger.Info("开始将导入的配置保存到磁盘...");

                // 保存模拟器配置到原文件
                string emulatorConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SchedulerEmulators.json");
                string emulatorJson = JsonSerializer.Serialize(emulators, _jsonOptions);
                await File.WriteAllTextAsync(emulatorConfigPath, emulatorJson, Encoding.UTF8);

                // 保存任务配置到原文件
                string taskConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SchedulerTasks.json");
                string taskJson = JsonSerializer.Serialize(tasks, _jsonOptions);
                await File.WriteAllTextAsync(taskConfigPath, taskJson, Encoding.UTF8);

                XLogger.Info($"导入的配置已成功保存到原始配置文件");
            }
            catch (Exception ex)
            {
                XLogger.Error($"保存配置到磁盘失败: {ex.Message}");
                XLogger.SaveException(ex);
                throw;
            }
        }

        /// <summary>
        /// 显示导入结果报告
        /// </summary>
        /// <param name="report">导入报告</param>
        public void ShowImportReport(ImportReport report)
        {
            StringBuilder message = new StringBuilder();

            message.AppendLine("配置导入完成");
            message.AppendLine();

            message.AppendLine($"模拟器：共{report.TotalEmulators}个，成功导入{report.ImportedEmulators}个");

            if (report.FailedEmulatorNames.Count > 0)
            {
                message.AppendLine($"未找到匹配模拟器：{report.FailedEmulatorNames.Count}个");
                foreach (var name in report.FailedEmulatorNames)
                {
                    message.AppendLine($" - {name}");
                }
            }

            message.AppendLine();
            message.AppendLine($"任务：成功导入{report.ImportedTasks}个");
            message.AppendLine($" - 新增任务：{report.NewTasks}个");
            message.AppendLine($" - 更新任务：{report.UpdatedTasks}个");
            message.AppendLine($" - 跳过任务：{report.SkippedTasks}个（因模拟器不匹配）");

            if (report.ImportedEmulators > 0 || report.ImportedTasks > 0)
            {
                message.AppendLine();
                message.AppendLine("配置已保存，请点击刷新按钮或重启应用以使更改生效。");
            }

            if (!report.Success)
            {
                message.AppendLine();
                message.AppendLine("注意：部分模拟器未找到匹配项，请先创建同名模拟器后再尝试导入！");
            }

            // 显示消息框
            MessageBox.Show(message.ToString(),
                report.Success ? "导入成功" : "导入部分成功",
                MessageBoxButton.OK,
                report.Success ? MessageBoxImage.Information : MessageBoxImage.Warning);
        }

        /// <summary>
        /// 获取下一个可用的模拟器ID
        /// </summary>
        private string GetNextEmulatorId(List<EmulatorItem> emulators)
        {
            if (emulators == null || !emulators.Any())
            {
                return "1";
            }

            // 过滤出有效的ID值，并尝试转换为整数
            var validIds = new List<int>();
            foreach (var emulator in emulators)
            {
                if (!string.IsNullOrEmpty(emulator.Id) && int.TryParse(emulator.Id, out int id))
                {
                    validIds.Add(id);
                }
            }

            // 如果没有有效ID，则返回1
            if (!validIds.Any())
            {
                return "1";
            }

            // 返回最大ID+1
            return (validIds.Max() + 1).ToString();
        }
    }
}