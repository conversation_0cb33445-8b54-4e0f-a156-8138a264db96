﻿using DamoControlKit;
using ScriptEngine;
using ScriptEngine.Model;
using XHelper;

namespace DanDing1.Commands
{
    internal class Test_DebugPics
    {
        private DDScript? dScript;
        private bool isRuinng = false;

        public async void Start(string hwnd)
        {
            if (isRuinng)
            {
                XLogger.Error("任务正在运行中，请勿重复执行！");
                return;
            }
            if (hwnd == "")
            {
                XLogger.Error("您没有选择任何游戏句柄，请绑定后再来试试！");
                return;
            }
            DmSettings.Init(true);

            //开始配置任务清单
            TaskConfigsModel tcm = new();
            tcm.Add("调试1", 0);

            DDBuilder dBuilder = new();
            dBuilder.SetSimulator("mumu", int.Parse(hwnd))
                .SetBindSetting(BindModels.GetBindModel("mumu", 1))
                .SetTaskList(tcm)
                .InitLog("游戏1")
                .SetGameSettings(new() { XuanShang = true })
                .AddData("Base_Url", GlobalData.Instance.appConfig.dNet.System.GetCurrentHost())
                .SetUserConfigs(GlobalData.Instance.UserConfig.Pairs);
            await dBuilder.SetPicsVerAsync(XConfig.LoadValueFromFile<string>("PicServer") ?? "默认", GlobalData.Instance.PicServerVer ?? GlobalData.Instance.appConfig.Info.Now_Ver);

            //检查句柄
            if (!await dBuilder.CheckAsync(await GlobalData.Instance.appConfig.dNet.User.GetDmsoftCodeAsync()))
            {
                XLogger.Warn("无法开始任务，句柄已经失效，请重新绑定！");
                return;
            }

            dScript ??= new();
            dScript.TaskEnded = () =>
            {
                XLogger.Debug("dScript.TaskEnded");
            };
            dScript.Start(dBuilder);
            XLogger.Debug("测试流程在后台执行中！");
            isRuinng = true;
        }

        public async void Stop()
        {
            if (dScript is null || !isRuinng)
            {
                XLogger.Debug("当前没有任务在执行中！无法停止任何任务！");
                return;
            }
            await dScript.Stop();
            isRuinng = false;

            XLogger.Debug("测试流程在已经结束！");
        }
    }
}