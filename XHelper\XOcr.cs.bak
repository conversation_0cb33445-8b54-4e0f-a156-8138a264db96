﻿using OcrLiteLib;
using OpenCvSharp;
using Tesseract;
using XHelper.Models;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;

namespace XHelper
{
    public static class XOcr
    {
        /// <summary>
        /// 初始化标志
        /// </summary>
        private static bool _Init = false;

        /// <summary>
        /// 是否保存优化图片备份
        /// </summary>
        private static bool isSaveTmp = false;

        /// <summary>
        /// 文字Ocr引擎
        /// </summary>
        private static OcrLite ocrEngin;

        private static int padding = 50;
        private static int imgResize = 1024;
        private static float boxScoreThresh = 0.618F;
        private static float boxThresh = 0.300F;
        private static float unClipRatio = 2.0F;
        private static bool doAngle = true;
        private static bool mostAngle = true;

        /// <summary>
        /// 是否使用Pro版本 OCR识别
        /// </summary>
        /// <returns></returns>
        private static bool is_Use_Pro()
        {
            try
            {
                string Exe = Path.Combine(XPath.GetCurrentPath(), "runtimes", "PaddleOCR_cpp.exe");

                // 添加额外的诊断信息
                XLogger.Debug($"检查Pro版本OCR路径: {Exe}");

                //检查程序的运行路径中是否包含空格
                if (Exe.Contains(' '))
                {
                    XLogger.Warn("PaddleOCR_cpp.exe的路径中包含空格，这可能导致程序无法正常工作!");
                    XLogger.Warn("建议将程序移动到不包含空格的路径下，例如：C:\\OCR\\");
                    return false;
                }

                //判断是否存在
                if (File.Exists(Exe))
                {
                    // 检查文件是否可执行
                    try
                    {
                        FileInfo fileInfo = new FileInfo(Exe);
                        XLogger.Debug($"Pro版本OCR文件大小: {fileInfo.Length} 字节");
                        if (fileInfo.Length < 1000)
                        {
                            XLogger.Warn("PaddleOCR_cpp.exe文件异常，文件大小过小!");
                            return false;
                        }
                        return true;
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"检查Pro版本OCR文件信息时发生异常: {ex.Message}");
                        return false;
                    }
                }
                else
                {
                    XLogger.Warn($"未找到Pro版本OCR文件: {Exe}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"检查Pro版本OCR时发生异常: {ex.Message}");
                return false;
            }
        }

        public static string Local_Ocr_String_Pro(string imgPath, Action<List<XOcr_TextBlock>>? callback = null)
        {
            try
            {
                if (!is_Use_Pro())
                {
                    XLogger.Warn("Pro版本OCR未正确安装或配置");
                    return "-1";
                }

                if (string.IsNullOrEmpty(imgPath))
                {
                    XLogger.Warn("输入图像路径为空");
                    return "-1";
                }

                // 移除路径中可能的引号
                imgPath = imgPath.Trim('"', '\'');

                if (!File.Exists(imgPath))
                {
                    XLogger.Warn($"输入图像文件不存在: {imgPath}");
                    return "-1";
                }

                XLogger.Debug($"开始使用Pro版本OCR识别图片: {imgPath}");
                string res = Ocrx64_Cmd(imgPath);
                if (string.IsNullOrEmpty(res) || res == "-1")
                {
                    XLogger.Warn("Pro版本OCR返回结果为空或错误");
                    return "-1";
                }

                // 直接使用返回的JSON创建PaddleOcr_Model
                Models.PaddleOcr_Model paddleModel;
                try
                {
                    paddleModel = new Models.PaddleOcr_Model(res);
                }
                catch (ArgumentException ex)
                {
                    XLogger.Error($"JSON解析失败: {ex.Message}");
                    return "-1";
                }

                // 如果需要兼容原有的回调方式
                if (callback != null && paddleModel.Results != null)
                {
                    try
                    {
                        var blocks_list = paddleModel.Results.Select(r =>
                            new XOcr_TextBlock(r.Text ?? string.Empty,
                                new System.Drawing.Rectangle(r.X, r.Y, r.Width, r.Height))
                        ).ToList();
                        callback(blocks_list);
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"执行回调时出错: {ex.Message}");
                    }
                }

                return paddleModel.GetAllText();
            }
            catch (Exception ex)
            {
                XLogger.Error($"Local_Ocr_String_Pro异常: {ex.Message}");
                return "-1";
            }
        }

        private static string Ocrx64_Cmd(string cmd)
        {
            if (!is_Use_Pro()) return "-1";
            string Dir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "runtimes", "PaddleOCR_cpp.exe");

            if (!File.Exists(Dir))
            {
                XLogger.Error($"OCR执行文件不存在: {Dir}");
                return "-1";
            }

            try
            {
                // 处理图片路径，移除可能的引号
                string imagePath = cmd.Trim('"', '\'');

                // 检查文件是否存在
                if (!File.Exists(imagePath))
                {
                    XLogger.Error($"图片文件不存在: {imagePath}");
                    return "-1";
                }

                // 为图片路径添加双引号，确保路径中的空格能被正确处理
                string adbCommand = $"\"{imagePath}\"";

                XLogger.Debug($"OCR处理图片: {adbCommand}");

                System.Diagnostics.ProcessStartInfo processStartInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = Dir,
                    Arguments = adbCommand,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    StandardOutputEncoding = System.Text.Encoding.UTF8,
                    StandardErrorEncoding = System.Text.Encoding.UTF8,
                    UseShellExecute = false
                };
                using System.Diagnostics.Process? process = System.Diagnostics.Process.Start(processStartInfo);
                if (process == null)
                {
                    XLogger.Error("无法启动OCR进程");
                    return "-1";
                }

                string? output = process.StandardOutput.ReadToEnd();
                string? error = process.StandardError.ReadToEnd();

                // 等待进程完成，设置超时
                bool exited = process.WaitForExit(30000); // 30秒超时
                if (!exited)
                {
                    try
                    {
                        process.Kill();
                        XLogger.Error("OCR进程执行超时，已强制终止");
                    }
                    catch { }
                    return "-1";
                }

                // 检查是否有错误输出
                if (!string.IsNullOrEmpty(error))
                {
                    XLogger.Error($"OCR进程错误输出: {error}");
                    return "-1";
                }

                // 检查进程退出代码
                if (process.ExitCode != 0)
                {
                    XLogger.Error($"OCR进程退出代码: {process.ExitCode}");
                    if (process.ExitCode == -1073741819)
                    {
                        XLogger.Error("OCR进程发生访问冲突 (0xC0000005)，可能是系统兼容性问题");
                    }
                    return "-1";
                }

                return output ?? "";
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                XLogger.Error($"执行OCR命令时异常: {ex.Message}, 命令: {cmd}");
                return "-1";
            }
        }

        private static bool Init()
        {
            if (_Init)
                return true;

            try
            {
                string modelsDir = @".\runtimes";

                if (!Directory.Exists(modelsDir))
                {
                    XLogger.Error($"OCR模型目录不存在: {Path.GetFullPath(modelsDir)}");
                    return false;
                }

                string detPath = modelsDir + "\\dbnet.onnx";
                string clsPath = modelsDir + "\\angle_net.onnx";
                string recPath = modelsDir + "\\crnn_lite_lstm.onnx";
                string keysPath = modelsDir + "\\keys.txt";

                // 检查模型文件是否存在
                bool filesExist = true;
                Dictionary<string, string> modelFiles = new Dictionary<string, string>
                {
                    { "检测模型", detPath },
                    { "分类模型", clsPath },
                    { "识别模型", recPath },
                    { "字典文件", keysPath }
                };

                foreach (var file in modelFiles)
                {
                    if (!File.Exists(file.Value))
                    {
                        XLogger.Error($"OCR{file.Key}文件不存在: {Path.GetFullPath(file.Value)}");
                        filesExist = false;
                    }
                }

                if (!filesExist)
                    return false;

                try
                {
                    ocrEngin = new OcrLite();
                    ocrEngin.InitModels(detPath, clsPath, recPath, keysPath, 4);
                    _Init = true;
                    return true;
                }
                catch (Exception ex)
                {
                    XLogger.Error($"OCR引擎初始化失败: {ex.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"OCR初始化异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 识别文本
        /// </summary>
        /// <returns></returns>
        public static string Local_Ocr_String(byte[] bytes, Action<List<XOcr_TextBlock>>? callback = null)
        {
            try
            {
                // 检查输入
                if (bytes == null || bytes.Length == 0)
                {
                    XLogger.Warn("输入图像数据为空");
                    return string.Empty;
                }

                // 初始化OCR引擎
                if (!Init())
                {
                    XLogger.Error("OCR引擎初始化失败");
                    return string.Empty;
                }

                // 执行OCR识别
                OcrResult ocrResult = ocrEngin.Detect(bytes, padding, imgResize, boxScoreThresh, boxThresh, unClipRatio, doAngle, mostAngle);

                // 执行回调
                if (callback is not null && ocrResult?.TextBlocks != null)
                {
                    try
                    {
                        List<XOcr_TextBlock> blocks = new List<XOcr_TextBlock>();
                        foreach (var block in ocrResult.TextBlocks)
                        {
                            if (block != null)
                                blocks.Add(new(block));
                        }
                        callback(blocks);
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"执行回调时发生异常: {ex.Message}");
                    }
                }

                return ocrResult?.StrRes ?? string.Empty;
            }
            catch (Exception ex)
            {
                XLogger.Error($"文本识别异常: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 识别 路径图片
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public static string Local_Ocr_Number(string path)
        {
            if (string.IsNullOrEmpty(path) || !File.Exists(path))
            {
                XLogger.Warn($"图像文件不存在: {path}");
                return string.Empty;
            }

            try
            {
                List<XOcr_TextBlock> blocks = new();

                // 优化图像
                byte[] optimizedImageBytes = OptimizeImage(path);
                if (optimizedImageBytes == null || optimizedImageBytes.Length == 0)
                {
                    XLogger.Error("图像优化失败");
                    return string.Empty;
                }

                // 初始化Tesseract引擎
                string tessDataPath = @"./runtimes";
                if (!Directory.Exists(tessDataPath))
                {
                    XLogger.Error($"Tesseract数据目录不存在: {Path.GetFullPath(tessDataPath)}");
                    return string.Empty;
                }

                // 使用正确的TesseractEngine初始化方式，添加EngineMode.Default参数
                using var engine = new TesseractEngine(tessDataPath, "num", EngineMode.Default);

                // 设置字符白名单，限制识别范围为数字和斜杠，提高识别准确性
                engine.SetVariable("tessedit_char_whitelist", "0123456789/");

                // 设置页面分割模式为单行文本，适合数字识别
                engine.SetVariable("tessedit_pageseg_mode", "7"); // PSM_SINGLE_LINE

                using var img = Pix.LoadFromMemory(optimizedImageBytes);
                if (img == null)
                {
                    XLogger.Error("无法加载图像数据");
                    return string.Empty;
                }

                using var page = engine.Process(img);
                var text = page.GetText();

                if (!string.IsNullOrEmpty(text?.Trim())) // 处理文本相应的位置
                {
                    try
                    {
                        var list = page.GetSegmentedRegions(PageIteratorLevel.TextLine);
                        if (list != null && list.Count > 0)
                        {
                            var lines = text.Split('\n');
                            int i = 0;

                            foreach (var line in lines)
                            {
                                if (string.IsNullOrWhiteSpace(line)) continue;
                                if (i < list.Count)
                                {
                                    blocks.Add(new(line, new(list[i].X, list[i].Y, list[i].Width, list[i].Height)));
                                    i++;
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"处理文本区域时异常: {ex.Message}");
                    }
                }

                XLogger.Debug($"Local_Ocr结果：{text?.Trim() ?? string.Empty}");
                return text ?? string.Empty;
            }
            catch (TesseractException tex)
            {
                XLogger.Error($"Tesseract引擎异常: {tex.Message}");
                return string.Empty;
            }
            catch (Exception ex)
            {
                XLogger.Error($"数字识别异常: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 识别 图片字节集
        /// </summary>
        /// <param name="bytes"></param>
        /// <returns></returns>
        public static string Local_Ocr_Number(byte[] bytes)
        {
            if (bytes == null || bytes.Length == 0)
            {
                XLogger.Warn("输入图像数据为空");
                return string.Empty;
            }

            try
            {
                // 优化图像
                byte[] optimizedImageBytes = OptimizeImage(bytes);
                if (optimizedImageBytes == null || optimizedImageBytes.Length == 0)
                {
                    XLogger.Error("图像优化失败");
                    return string.Empty;
                }

                // 初始化Tesseract引擎
                string tessDataPath = @"./runtimes";
                if (!Directory.Exists(tessDataPath))
                {
                    XLogger.Error($"Tesseract数据目录不存在: {Path.GetFullPath(tessDataPath)}");
                    return string.Empty;
                }

                // 使用正确的TesseractEngine初始化方式，添加EngineMode.Default参数
                using var engine = new TesseractEngine(tessDataPath, "num", EngineMode.Default);

                // 设置字符白名单，限制识别范围为数字和斜杠，提高识别准确性
                engine.SetVariable("tessedit_char_whitelist", "0123456789/");

                // 设置页面分割模式为单行文本，适合数字识别
                engine.SetVariable("tessedit_pageseg_mode", "7"); // PSM_SINGLE_LINE

                using var img = Pix.LoadFromMemory(optimizedImageBytes);
                if (img == null)
                {
                    XLogger.Error("无法加载图像数据");
                    return string.Empty;
                }

                using var page = engine.Process(img);
                var text = page.GetText();

                XLogger.Debug($"Local_Ocr结果：{text?.Trim() ?? string.Empty}");
                return text ?? string.Empty;
            }
            catch (TesseractException tex)
            {
                XLogger.Error($"Tesseract引擎异常: {tex.Message}");
                return string.Empty;
            }
            catch (Exception ex)
            {
                XLogger.Error($"数字识别异常: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 设置是否保存优化图片备份
        /// </summary>
        /// <param name="isSave"></param>
        public static void SetSaveTmp(bool isSave)
        {
            isSaveTmp = isSave;
            //XLogger.Debug($"XOcr_SetSaveTmp：{isSaveTmp}");
        }

        /// <summary>
        /// 优化图片 使用Opencv
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        private static byte[] OptimizeImage(string path)
        {
            if (string.IsNullOrEmpty(path) || !File.Exists(path))
            {
                XLogger.Error($"图像文件不存在: {path}");
                return new byte[0];
            }

            try
            {
                // 加载图片
                Mat image = Cv2.ImRead(path, ImreadModes.Color);
                if (image == null || image.Empty())
                {
                    XLogger.Error($"无法读取图像文件: {path}");
                    return new byte[0];
                }

                byte[] result = ProcessImage(image);
                image.Dispose(); // 显式释放资源
                return result;
            }
            catch (OpenCVException ex)
            {
                XLogger.Error($"OpenCV处理图像异常: {ex.Message}");
                return new byte[0];
            }
            catch (Exception ex)
            {
                XLogger.Error($"图像优化异常: {ex.Message}");
                return new byte[0];
            }
        }

        /// <summary>
        /// 优化图片 使用Opencv
        /// </summary>
        /// <param name="bytes"></param>
        /// <returns></returns>
        private static byte[] OptimizeImage(byte[] bytes)
        {
            if (bytes == null || bytes.Length == 0)
            {
                XLogger.Error("输入图像数据为空");
                return new byte[0];
            }

            try
            {
                // 加载图片 bytes
                Mat image = Cv2.ImDecode(bytes, ImreadModes.Color);
                if (image == null || image.Empty())
                {
                    XLogger.Error("无法解码图像数据");
                    return new byte[0];
                }

                return ProcessImage(image);
            }
            catch (OpenCVException ex)
            {
                XLogger.Error($"OpenCV处理图像异常: {ex.Message}");
                return new byte[0];
            }
            catch (Exception ex)
            {
                XLogger.Error($"图像优化异常: {ex.Message}");
                return new byte[0];
            }
        }

        /// <summary>
        /// 图像处理共享逻辑
        /// </summary>
        private static byte[] ProcessImage(Mat image)
        {
            if (image == null || image.Empty())
                return new byte[0];

            Mat grayImage = null;
            Mat binaryImage = null;
            Mat dilateImage = null;
            Mat kernel = null;
            Mat invertedImage = null;

            try
            {
                // 转换为灰度图
                grayImage = new Mat();
                Cv2.CvtColor(image, grayImage, ColorConversionCodes.BGR2GRAY);

                // 二值化
                binaryImage = new Mat();
                Cv2.Threshold(grayImage, binaryImage, 0, 255, ThresholdTypes.Binary | ThresholdTypes.Otsu);

                // 膨胀
                dilateImage = new Mat();
                kernel = Cv2.GetStructuringElement(MorphShapes.Rect, new Size(1, 1));
                Cv2.Dilate(binaryImage, dilateImage, kernel);

                invertedImage = new Mat();
                if (IsWhiteBackground(dilateImage))
                {
                    // 转换为黑底白字
                    Cv2.BitwiseNot(dilateImage, invertedImage);
                }
                else
                {
                    invertedImage = dilateImage.Clone();
                }

                // 保存图片
                if (isSaveTmp)
                {
                    try
                    {
                        XPath.CheckPathIsExist(".\\runtimes\\Debug\\");
                        XLogger.Debug("OptimizeImage SaveImagePath:./Debug/Image_Tmp.bmp");
                        Cv2.ImWrite(@"./runtimes/Debug/Image_Tmp.bmp", invertedImage);
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"保存优化图像失败: {ex.Message}");
                    }
                }

                // 返回图片字节
                return invertedImage.ToBytes(".png");
            }
            catch (Exception ex)
            {
                XLogger.Error($"图像处理异常: {ex.Message}");
                return new byte[0];
            }
            finally
            {
                // 显式释放资源
                grayImage?.Dispose();
                binaryImage?.Dispose();
                dilateImage?.Dispose();
                kernel?.Dispose();
                // 不释放invertedImage，因为它可能已经被用于返回结果
                if (invertedImage != null && invertedImage != dilateImage)
                {
                    dilateImage?.Dispose();
                }
                if (invertedImage != null)
                {
                    invertedImage.Dispose();
                }
            }
        }

        private static bool IsWhiteBackground(Mat image)
        {
            if (image == null || image.Empty())
                return false;

            try
            {
                // 计算图像的平均亮度
                Scalar meanScalar = Cv2.Mean(image);
                double meanBrightness = meanScalar.Val0;

                // 如果平均亮度高于阈值（如128），认为是白底黑字
                return meanBrightness > 128;
            }
            catch (Exception ex)
            {
                XLogger.Error($"判断图像背景异常: {ex.Message}");
                return false;
            }
        }
    }
}