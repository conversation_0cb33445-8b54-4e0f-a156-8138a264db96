﻿using DanDing1.Helpers;
using DanDing1.Models.Super;
using DanDing1.ViewModels.Pages;
using DanDing1.ViewModels.Windows;
using DanDing1.Views.Windows;
using ScriptEngine.Model;
using ScriptEngine.MuMu;
using ScriptEngine;
using static ScriptEngine.DDBuilder;
using DanDing1.Services.Notification;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper;
using System.Windows;
using XHelper.Interface;
using DanDing1.Helpers.Extensions;
using DamoControlKit.Model;
using DamoControlKit;
using System.IO;
using System.Text.Json;
using System.Threading;

namespace DanDing1.Services
{
    /// <summary>
    /// 对接DanDing1的脚本相关服务
    /// 主要用于 定时调度的独立功能
    /// </summary>
    internal static class GameControlService
    {
        // 定义日志回调委托类型
        public delegate void LogCallback(string message);

        // 存储脚本ID与任务的对应关系
        private static Dictionary<string, int> _taskScriptIds = new Dictionary<string, int>();

        // 存储任务的开始时间和模型信息，用于超时检测和恢复
        private static Dictionary<string, TaskExecutionInfo> _taskExecutionInfo = new Dictionary<string, TaskExecutionInfo>();

        private static bool isStartMumu;

        // 定义一个模拟器状态变更的事件
        public static event Action EmulatorStatusChanged;

        // 取消令牌源，用于在调度器停止时取消任务
        private static CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// 设置取消令牌源
        /// </summary>
        /// <param name="cancellationTokenSource">调度器的取消令牌源</param>
        public static void SetCancellationTokenSource(CancellationTokenSource cancellationTokenSource)
        {
            _cancellationTokenSource = cancellationTokenSource;
        }

        /// <summary>
        /// 清除取消令牌源
        /// </summary>
        public static void ClearCancellationTokenSource()
        {
            _cancellationTokenSource = null;
        }

        /// <summary>
        /// 检查当前是否可以执行任务
        /// </summary>
        /// <returns>是否可以执行任务</returns>
        public static bool CanExecuteTask()
        {
            if (_cancellationTokenSource == null) return false;
            return !_cancellationTokenSource.IsCancellationRequested;
        }

        /// <summary>
        /// 任务执行信息类，用于跟踪任务执行状态
        /// </summary>
        private class TaskExecutionInfo
        {
            public DateTime StartTime { get; set; }
            public AddTaskPropertyViewModel Model { get; set; }
            public int TimeoutSeconds { get; set; }
            public int RetryCount { get; set; }
            public int MaxRetryCount { get; set; }
            public bool IsRetrying { get; set; }
        }

        /// <summary>
        /// 公共方法：关闭模拟器
        /// </summary>
        /// <param name="emulator">模拟器信息</param>
        /// <returns>关闭操作是否成功</returns>
        public static async Task<bool> CloseEmulator(EmulatorItem emulator)
        {
            try
            {
                // 确保有有效的模拟器索引
                if (string.IsNullOrEmpty(emulator.SimulatorIndex))
                {
                    XLogger.Warn("模拟器索引无效，无法关闭模拟器");
                    return false;
                }

                // 初始化MuMu模拟器
                var mumu = new MuMu();
                string path = XConfig.LoadValueFromFile<string>("MuMuPath");

                if (!string.IsNullOrEmpty(path) && mumu.Init(path))
                {
                    XLogger.Info("正在关闭模拟器...");

                    int index = int.Parse(emulator.SimulatorIndex);
                    mumu.CloseByIndex(index);

                    // 重置状态
                    emulator.SimulatorConfig.IsRunning = false;

                    // 触发模拟器状态变更事件
                    OnEmulatorStatusChanged();

                    XLogger.Info("模拟器已关闭");
                    return true;
                }
                else
                {
                    XLogger.Warn("无法初始化MuMu模拟器，无法关闭模拟器");
                    return false;
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"关闭模拟器时出错: {ex.Message}");
                XLogger.SaveException(ex);
                return false;
            }
        }

        /// <summary>
        /// 检查脚本是否已完成执行
        /// </summary>
        /// <param name="emulator">模拟器信息</param>
        /// <returns>脚本是否已完成执行</returns>
        public static async Task<bool> IsScriptCompleted(EmulatorItem emulator)
        {
            try
            {
                // 查找与此模拟器相关的脚本ID
                int scriptId = -1;

                // 查找任务ID
                string taskId = null;
                foreach (var kvp in _taskScriptIds)
                {
                    // 尝试从任务ID中提取模拟器名称（如果有存储模式）
                    if (kvp.Key.Contains(emulator.Name))
                    {
                        taskId = kvp.Key;
                        scriptId = kvp.Value;
                        break;
                    }
                }

                if (scriptId == -1)
                {
                    // 没有找到关联的脚本ID，可能脚本已经完成或尚未开始
                    XLogger.Info($"无法找到与模拟器 [{emulator.Name}] 关联的活动脚本");
                    return true; // 假定已完成，因为没有正在运行的脚本
                }

                // 检查脚本是否正在运行
                string errorStr;
                bool isRunning = Scripts.IsRunning(scriptId, out errorStr);

                // 如果脚本不在运行状态，则认为已完成
                if (!isRunning)
                {
                    // 如果有任务ID，从字典中移除已完成的任务
                    if (taskId != null && _taskScriptIds.ContainsKey(taskId))
                    {
                        _taskScriptIds.Remove(taskId);
                        XLogger.Info($"脚本任务 [{taskId}] 已完成，已从跟踪列表中移除");
                    }
                    return true;
                }

                return false; // 脚本仍在运行中
            }
            catch (Exception ex)
            {
                XLogger.Error($"检查脚本完成状态时出错: {ex.Message}");
                XLogger.SaveException(ex);
                return false; // 出错时保守返回未完成
            }
        }

        /// <summary>
        /// 快速启动一个脚本任务
        /// </summary>
        /// <returns>启动是否成功</returns>
        public static async Task<bool> Start(AddTaskPropertyViewModel model, EmulatorItem emulator, ScheduledTask task, LogCallback logCallback = null)
        {
            try
            {
                // 首先检查取消令牌状态
                if (!CanExecuteTask())
                {
                    XLogger.Warn($"调度器已停止，任务 [{task.Name}] 不会启动");
                    if (logCallback != null)
                    {
                        logCallback($"调度器已停止，任务 [{task.Name}] 不会启动");
                    }
                    return false;
                }

                // 验证启动条件
                if (!await ValidateStartConditions(model, emulator))
                {
                    return false;
                }

                // 启动模拟器和阴阳师应用
                if (!await StartEmulatorAndYYS(emulator))
                {
                    return false;
                }

                // 再次检查取消令牌状态，确保在启动模拟器后仍未取消
                if (!CanExecuteTask())
                {
                    XLogger.Warn($"调度器已停止，任务 [{task.Name}] 初始化已中断");
                    if (logCallback != null)
                    {
                        logCallback($"调度器已停止，任务 [{task.Name}] 初始化已中断");
                    }
                    return false;
                }

                // 获取游戏设置
                var gameSettings = await GetGameSettings(model, emulator);

                // 初始化大漠构建器
                var dBuilder = await InitializeDDBuilder(model, emulator, gameSettings, task.Id, logCallback);
                if (dBuilder == null)
                {
                    return false;
                }

                // 再次检查取消令牌状态，确保在初始化大漠构建器后仍未取消
                if (!CanExecuteTask())
                {
                    XLogger.Warn($"调度器已停止，任务 [{task.Name}] 启动已中断");
                    if (logCallback != null)
                    {
                        logCallback($"调度器已停止，任务 [{task.Name}] 启动已中断");
                    }
                    return false;
                }

                // 验证大漠插件
                if (!await ValidateDamoPlugin(dBuilder))
                {
                    return false;
                }

                // 最后一次检查取消令牌状态，确保在启动脚本前仍未取消
                if (!CanExecuteTask())
                {
                    XLogger.Warn($"调度器已停止，任务 [{task.Name}] 启动已中断");
                    if (logCallback != null)
                    {
                        logCallback($"调度器已停止，任务 [{task.Name}] 启动已中断");
                    }
                    return false;
                }

                // 启动脚本执行
                bool startResult = await StartScriptExecution(model, emulator, dBuilder, task.Id);

                if (startResult)
                {
                    // 记录任务开始时间和相关信息用于超时检测
                    string taskId = $"{emulator.Name}_{task.Id}";

                    // 获取任务超时设置
                    int timeoutSeconds = 3600; // 默认值为1小时
                    int maxRetryCount = 3;     // 默认最大重试次数
                    bool enableTaskRetry = true; // 默认启用重试

                    try
                    {
                        // 从配置文件加载超时设置
                        string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SchedulerConfig.json");
                        if (File.Exists(configPath))
                        {
                            string json = File.ReadAllText(configPath);
                            var config = JsonSerializer.Deserialize<SchedulerConfig>(json);
                            if (config != null)
                            {
                                timeoutSeconds = config.DefaultTaskTimeout;
                                maxRetryCount = config.MaxRetryCount;
                                enableTaskRetry = config.EnableTaskRetry;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Warn($"读取任务超时配置失败，使用默认值: {ex.Message}");
                    }

                    // 存储任务执行信息
                    _taskExecutionInfo[taskId] = new TaskExecutionInfo
                    {
                        StartTime = DateTime.Now,
                        Model = model,
                        TimeoutSeconds = timeoutSeconds,
                        RetryCount = 0,
                        MaxRetryCount = maxRetryCount,
                        IsRetrying = false
                    };

                    // 启动超时监控
                    _ = MonitorTaskTimeout(taskId, emulator, task);
                }

                return startResult;
            }
            catch (Exception ex)
            {
                XLogger.Error($"启动任务失败: {ex.Message}");
                XLogger.SaveException(ex);
                return false;
            }
        }

        /// <summary>
        /// 监控任务是否超时
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="emulator">模拟器信息</param>
        /// <param name="task">调度任务</param>
        /// <returns>异步任务</returns>
        private static async Task MonitorTaskTimeout(string taskId, EmulatorItem emulator, ScheduledTask task)
        {
            try
            {
                // 首先检查取消令牌状态
                if (!CanExecuteTask())
                {
                    XLogger.Info($"调度器已停止，任务 [{taskId}] 的超时监控不会启动");
                    return;
                }

                if (!_taskExecutionInfo.TryGetValue(taskId, out var executionInfo))
                {
                    XLogger.Warn($"无法找到任务 [{taskId}] 的执行信息，取消超时监控");
                    return;
                }

                XLogger.Info($"开始监控任务 [{taskId}] 执行超时，最长执行时间：{executionInfo.TimeoutSeconds}秒");

                // 每10秒检查一次是否超时
                int checkIntervalSeconds = 10;
                int elapsedTime = 0;

                while (true)
                {
                    // 等待间隔时间
                    try
                    {
                        // 使用带取消令牌的延迟，以便能够响应取消请求
                        if (_cancellationTokenSource != null)
                        {
                            await Task.Delay(checkIntervalSeconds * 1000, _cancellationTokenSource.Token);
                        }
                        else
                        {
                            await Task.Delay(checkIntervalSeconds * 1000);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        // 如果取消令牌被触发，停止监控
                        XLogger.Info($"任务 [{taskId}] 的超时监控已取消，调度器已停止");
                        return;
                    }

                    // 检查取消令牌状态
                    if (!CanExecuteTask())
                    {
                        XLogger.Info($"调度器已停止，任务 [{taskId}] 的超时监控已终止");
                        return;
                    }

                    // 重新检查执行信息，因为可能已被移除
                    if (!_taskExecutionInfo.TryGetValue(taskId, out executionInfo))
                    {
                        XLogger.Info($"任务 [{taskId}] 的执行信息已不存在，停止超时监控");
                        return;
                    }

                    // 检查脚本是否已完成
                    bool isCompleted = await IsScriptCompleted(emulator);

                    if (isCompleted)
                    {
                        XLogger.Info($"任务 [{taskId}] 已正常完成，停止超时监控");
                        _taskExecutionInfo.Remove(taskId);
                        return;
                    }

                    // 增加已监控的时间
                    elapsedTime += checkIntervalSeconds;

                    // 检查是否超时
                    if (elapsedTime >= executionInfo.TimeoutSeconds)
                    {
                        XLogger.Warn($"任务 [{taskId}] 执行超时（{executionInfo.TimeoutSeconds}秒），准备执行恢复流程");

                        // 检查是否已达到最大重试次数
                        if (executionInfo.RetryCount >= executionInfo.MaxRetryCount)
                        {
                            XLogger.Warn($"任务 [{taskId}] 已达到最大重试次数（{executionInfo.MaxRetryCount}次），停止重试");

                            // 尝试停止当前任务
                            await Stop(emulator);

                            // 从跟踪字典中移除
                            _taskExecutionInfo.Remove(taskId);
                            XLogger.Info($"已从监控列表中移除任务 [{taskId}]");
                            return;
                        }

                        // 检查调度器是否已停止
                        if (!CanExecuteTask())
                        {
                            XLogger.Info($"调度器已停止，任务 [{taskId}] 的恢复流程将不会执行");

                            // 尝试停止当前任务
                            await Stop(emulator);

                            // 从跟踪字典中移除
                            _taskExecutionInfo.Remove(taskId);
                            XLogger.Info($"已从监控列表中移除任务 [{taskId}]");
                            return;
                        }

                        // 执行恢复流程：停止脚本 -> 重新启动任务
                        XLogger.Info($"开始执行任务 [{taskId}] 的恢复流程");
                        bool recoveryResult = await RecoverTimeoutTask(taskId, emulator, task);

                        if (recoveryResult)
                        {
                            XLogger.Info($"任务 [{taskId}] 恢复流程执行成功");
                        }
                        else
                        {
                            XLogger.Warn($"任务 [{taskId}] 恢复流程执行失败");

                            // 确保任务被清理
                            if (_taskExecutionInfo.ContainsKey(taskId))
                            {
                                _taskExecutionInfo.Remove(taskId);
                                XLogger.Info($"已从监控列表中移除任务 [{taskId}]");
                            }
                        }

                        return;
                    }
                    else if (elapsedTime % 60 == 0) // 每分钟记录一次
                    {
                        // 记录已运行时间
                        XLogger.Info($"任务 [{taskId}] 已运行 {elapsedTime} 秒，超时阈值：{executionInfo.TimeoutSeconds} 秒");
                    }
                }
            }
            catch (OperationCanceledException)
            {
                XLogger.Info($"任务 [{taskId}] 的超时监控已取消");

                // 确保任务被清理
                if (_taskExecutionInfo.ContainsKey(taskId))
                {
                    _taskExecutionInfo.Remove(taskId);
                    XLogger.Info($"已从监控列表中移除任务 [{taskId}]");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"监控任务 [{taskId}] 超时过程中出错: {ex.Message}");
                XLogger.SaveException(ex);

                // 确保任务被清理
                if (_taskExecutionInfo.ContainsKey(taskId))
                {
                    _taskExecutionInfo.Remove(taskId);
                    XLogger.Info($"已从监控列表中移除任务 [{taskId}]");
                }
            }
        }

        /// <summary>
        /// 恢复超时的任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="emulator">模拟器信息</param>
        /// <param name="task">调度任务</param>
        /// <returns>恢复是否成功</returns>
        private static async Task<bool> RecoverTimeoutTask(string taskId, EmulatorItem emulator, ScheduledTask task)
        {
            try
            {
                // 检查取消令牌状态，如果调度器已停止，则不执行恢复
                if (!CanExecuteTask())
                {
                    XLogger.Info($"调度器已停止，任务 [{taskId}] 的恢复流程已取消");

                    // 确保清理任务状态
                    if (_taskExecutionInfo.ContainsKey(taskId))
                    {
                        _taskExecutionInfo.Remove(taskId);
                        XLogger.Info($"已清理任务 [{taskId}] 的执行信息");
                    }

                    return false;
                }

                if (!_taskExecutionInfo.TryGetValue(taskId, out var executionInfo))
                {
                    XLogger.Warn($"无法找到任务 [{taskId}] 的执行信息，无法执行恢复流程");
                    return false;
                }

                // 增加重试计数
                executionInfo.RetryCount++;
                executionInfo.IsRetrying = true;

                XLogger.Info($"开始执行任务 [{taskId}] 的恢复流程，当前重试次数：{executionInfo.RetryCount}/{executionInfo.MaxRetryCount}");

                // 停止当前脚本
                await Stop(emulator);

                // 再次检查取消令牌状态
                if (!CanExecuteTask())
                {
                    XLogger.Info($"调度器已停止，任务 [{taskId}] 的恢复流程已中断");

                    // 清理任务状态
                    if (_taskExecutionInfo.ContainsKey(taskId))
                    {
                        _taskExecutionInfo.Remove(taskId);
                        XLogger.Info($"已清理任务 [{taskId}] 的执行信息");
                    }

                    return false;
                }

                // 等待一段时间确保脚本完全停止
                try
                {
                    if (_cancellationTokenSource != null)
                    {
                        await Task.Delay(5000, _cancellationTokenSource.Token);
                    }
                    else
                    {
                        await Task.Delay(5000);
                    }
                }
                catch (OperationCanceledException)
                {
                    XLogger.Info($"任务 [{taskId}] 的恢复流程已取消，调度器已停止");

                    // 清理任务状态
                    if (_taskExecutionInfo.ContainsKey(taskId))
                    {
                        _taskExecutionInfo.Remove(taskId);
                        XLogger.Info($"已清理任务 [{taskId}] 的执行信息");
                    }

                    return false;
                }

                // 最后一次检查取消令牌状态
                if (!CanExecuteTask())
                {
                    XLogger.Info($"调度器已停止，任务 [{taskId}] 的恢复流程已中断");

                    // 清理任务状态
                    if (_taskExecutionInfo.ContainsKey(taskId))
                    {
                        _taskExecutionInfo.Remove(taskId);
                        XLogger.Info($"已清理任务 [{taskId}] 的执行信息");
                    }

                    return false;
                }

                // 重新启动任务
                XLogger.Info($"重新启动任务 [{taskId}]，重试次数：{executionInfo.RetryCount}/{executionInfo.MaxRetryCount}");

                // 使用原始模型重新启动任务
                bool result = await Start(executionInfo.Model, emulator, task);

                if (result)
                {
                    XLogger.Info($"任务 [{taskId}] 重试启动成功");
                    executionInfo.IsRetrying = false;
                    return true;
                }
                else
                {
                    XLogger.Warn($"任务 [{taskId}] 重试启动失败");
                    executionInfo.IsRetrying = false;

                    // 清理任务状态
                    if (_taskExecutionInfo.ContainsKey(taskId))
                    {
                        _taskExecutionInfo.Remove(taskId);
                        XLogger.Info($"已清理任务 [{taskId}] 的执行信息");
                    }

                    return false;
                }
            }
            catch (OperationCanceledException)
            {
                XLogger.Info($"任务 [{taskId}] 的恢复流程已取消");

                // 清理任务状态
                if (_taskExecutionInfo.ContainsKey(taskId))
                {
                    _taskExecutionInfo.Remove(taskId);
                    XLogger.Info($"已清理任务 [{taskId}] 的执行信息");
                }

                return false;
            }
            catch (Exception ex)
            {
                XLogger.Error($"恢复任务 [{taskId}] 时出错: {ex.Message}");
                XLogger.SaveException(ex);

                // 清理任务状态
                if (_taskExecutionInfo.ContainsKey(taskId))
                {
                    _taskExecutionInfo.Remove(taskId);
                    XLogger.Info($"已清理任务 [{taskId}] 的执行信息");
                }

                return false;
            }
        }

        /// <summary>
        /// 配置类定义
        /// </summary>
        private class SchedulerConfig
        {
            public int MaxConcurrentEmulators { get; set; } = 4;
            public int DefaultTaskTimeout { get; set; } = 3600;
            public int AutoShutdownIdleTime { get; set; } = 300;
            public bool EnableTaskRetry { get; set; } = true;
            public int MaxRetryCount { get; set; } = 3;
        }

        /// <summary>
        /// 公共方法：启动模拟器
        /// </summary>
        /// <param name="emulator">模拟器信息</param>
        /// <returns>启动操作是否成功</returns>
        public static async Task<bool> StartEmulator(EmulatorItem emulator)
        {
            try
            {
                // 初始化MuMu实例
                var mumu = new MuMu();
                var path = XConfig.LoadValueFromFile<string>("MuMuPath");
                if (!mumu.Init(path))
                {
                    XLogger.Warn("MuMu模拟器初始化失败，无法启动模拟器");
                    return false;
                }

                // 解析模拟器索引
                if (!int.TryParse(emulator.SimulatorIndex, out int index))
                {
                    XLogger.Warn($"无法解析模拟器索引: {emulator.SimulatorIndex}");
                    return false;
                }

                // 使用OpenByIndex方法启动模拟器
                var res = await mumu.OpenByIndexAsync(index);

                if (res.success && res.mainWndHandle != 0 && res.renderWndHandle != 0)
                {
                    // 等待模拟器启动
                    await Task.Delay(2000);

                    // 更新模拟器句柄信息
                    emulator.SimulatorConfig.RenderWnd = res.renderWndHandle.ToString("X8"); // 按照官网示例格式，8位十六进制，不带0x前缀
                    emulator.SimulatorConfig.IsRunning = true;

                    // 触发模拟器状态变更事件
                    OnEmulatorStatusChanged();

                    XLogger.Info($"成功获取模拟器句柄: MumuHandle={res.mainWndHandle}, GameHandle={res.renderWndHandle}");

                    // 等待额外6秒，确保模拟器已完全启动
                    await Task.Delay(6000);

                    return true;
                }
                else
                {
                    XLogger.Warn("模拟器启动失败，请重试！");
                    return false;
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"启动模拟器过程中出错: {ex.Message}");
                XLogger.SaveException(ex);
                return false;
            }
        }

        /// <summary>
        /// 公共方法：启动阴阳师游戏
        /// </summary>
        /// <param name="emulator">模拟器信息</param>
        /// <returns>启动游戏是否成功</returns>
        public static async Task<bool> StartGame(EmulatorItem emulator)
        {
            try
            {
                // 初始化MuMu实例
                var mumu = new MuMu();
                var path = XConfig.LoadValueFromFile<string>("MuMuPath");
                if (!mumu.Init(path))
                {
                    XLogger.Warn("MuMu模拟器初始化失败，无法启动游戏");
                    return false;
                }

                // 解析模拟器索引
                if (!int.TryParse(emulator.SimulatorIndex, out int index))
                {
                    XLogger.Warn($"无法解析模拟器索引: {emulator.SimulatorIndex}");
                    return false;
                }

                // 启动阴阳师应用
                int adbPort = mumu.GetAdbPortByIndex(index).port;
                mumu.Start_App("阴阳师", adbPort);

                // 设置启动标志，表示我们已经启动过模拟器
                isStartMumu = true;

                await Task.Delay(5000); // 等待游戏启动

                XLogger.Info("已启动阴阳师应用");
                return true;
            }
            catch (Exception ex)
            {
                XLogger.Error($"启动阴阳师游戏过程中出错: {ex.Message}");
                XLogger.SaveException(ex);
                return false;
            }
        }

        /// <summary>
        /// 停止模拟器上运行的脚本
        /// </summary>
        /// <param name="emulator">模拟器信息</param>
        /// <returns>停止操作是否成功</returns>
        public static async Task<bool> Stop(EmulatorItem emulator)
        {
            try
            {
                // 查找与此模拟器相关的脚本ID
                int scriptId = -1;
                string taskId = null;

                foreach (var kvp in _taskScriptIds)
                {
                    // 尝试从任务ID中提取模拟器名称
                    if (kvp.Key.Contains(emulator.Name))
                    {
                        taskId = kvp.Key;
                        scriptId = kvp.Value;
                        break;
                    }
                }

                if (scriptId == -1)
                {
                    XLogger.Warn($"无法找到与模拟器 [{emulator.Name}] 关联的活动脚本，停止操作取消");
                    return false;
                }

                // 异步停止脚本
                bool stopResult = await Scripts.StopAsync(scriptId);

                if (stopResult)
                {
                    XLogger.Info($"成功停止了模拟器 [{emulator.Name}] 上的脚本");

                    // 从跟踪字典中移除
                    if (taskId != null && _taskScriptIds.ContainsKey(taskId))
                    {
                        _taskScriptIds.Remove(taskId);
                    }

                    // 清理任务执行信息，停止超时监控
                    if (taskId != null && _taskExecutionInfo.ContainsKey(taskId))
                    {
                        XLogger.Info($"清理任务 [{taskId}] 的执行信息和超时监控");
                        _taskExecutionInfo.Remove(taskId);
                    }

                    return true;
                }
                else
                {
                    XLogger.Warn($"停止模拟器 [{emulator.Name}] 上的脚本失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"停止脚本时出错: {ex.Message}");
                XLogger.SaveException(ex);
                return false;
            }
        }

        /// <summary>
        /// 停止所有正在执行的定时任务
        /// </summary>
        /// <returns>是否成功停止所有任务</returns>
        public static async Task<bool> StopAllTasks()
        {
            try
            {
                // 清除取消令牌源引用，确保不会再启动新任务
                ClearCancellationTokenSource();

                if (_taskScriptIds.Count == 0)
                {
                    XLogger.Info("当前没有正在执行的定时任务");
                    return true;
                }

                XLogger.Info($"正在停止所有定时任务，总计: {_taskScriptIds.Count}");

                int successCount = 0;
                int failCount = 0;

                // 创建临时集合以避免在迭代时修改集合
                var taskEntries = _taskScriptIds.ToList();

                foreach (var kvp in taskEntries)
                {
                    string taskId = kvp.Key;
                    int scriptId = kvp.Value;

                    try
                    {
                        // 异步停止脚本
                        bool stopResult = await Scripts.StopAsync(scriptId);

                        if (stopResult)
                        {
                            XLogger.Info($"成功停止定时任务: {taskId}");
                            successCount++;
                        }
                        else
                        {
                            XLogger.Warn($"停止定时任务失败: {taskId}");
                            failCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"停止定时任务 {taskId} 时出错: {ex.Message}");
                        failCount++;
                    }
                }

                // 清空任务字典
                _taskScriptIds.Clear();

                // 清空任务执行信息，停止所有超时监控
                if (_taskExecutionInfo.Count > 0)
                {
                    XLogger.Info($"清理所有任务执行信息和超时监控，总计: {_taskExecutionInfo.Count}");
                    _taskExecutionInfo.Clear();
                }

                XLogger.Info($"定时任务停止结果: 成功 {successCount}, 失败 {failCount}");
                return failCount == 0;
            }
            catch (Exception ex)
            {
                XLogger.Error($"停止所有定时任务时出错: {ex.Message}");
                XLogger.SaveException(ex);
                return false;
            }
        }

        /// <summary>
        /// 内部方法：关闭模拟器
        /// </summary>
        /// <param name="emulator">模拟器信息</param>
        private static async Task CloseEmulatorInternal(EmulatorItem emulator)
        {
            // 调用公共方法实现
            await CloseEmulator(emulator);
        }

        /// <summary>
        /// 创建游戏设置模型
        /// </summary>
        /// <param name="model">任务模型</param>
        /// <param name="emulator">模拟器信息</param>
        /// <returns>游戏设置模型</returns>
        private static async Task<GameSettingsModel> GetGameSettings(AddTaskPropertyViewModel model, EmulatorItem emulator)
        {
            // 异步获取模拟器实例并计算AdbPort
            var mumuInstances = await new MuMu()._GetInstancesAsync();
            int adbPort = emulator.SimulatorConfig.AdbPort;

            return new GameSettingsModel
            {
                AdbPort = adbPort,
                XuanShang = model.XShangChecked,
                LoopCount = 1,
                CourtyardSkin = model.Game_Scene,
                IsRecord = false, // 默认不录制
                IsTifu = emulator.Config.IsTifu,
                RecordQuality = "原生质量（不压缩）",
                SpeedSwitch = emulator.Config.SpeedSwitch, // 默认不加速
                IsStartYYSToCourtyard = isStartMumu // 从GameData中获取的值
            };
        }

        /// <summary>
        /// 获取脚本ID
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>脚本ID</returns>
        private static int GetScriptId(string taskId)
        {
            string scriptName = $"ScheduleTask_{taskId}";

            if (!_taskScriptIds.ContainsKey(taskId))
            {
                // 添加脚本名称并获取ID
                int scriptId = Scripts.AddScriptName(scriptName);
                if (scriptId != -1) // 如果添加成功（返回值不是-1）
                {
                    _taskScriptIds.Add(taskId, scriptId);
                    return scriptId;
                }
            }

            // 如果脚本ID已存在，直接返回
            return _taskScriptIds.TryGetValue(taskId, out int existingScriptId) ? existingScriptId : -1;
        }

        /// <summary>
        /// 初始化大漠构建器
        /// </summary>
        /// <param name="model">任务模型</param>
        /// <param name="emulator">模拟器信息</param>
        /// <param name="gameSettings">游戏设置</param>
        /// <returns>初始化后的DDBuilder实例</returns>
        private static async Task<DDBuilder> InitializeDDBuilder(AddTaskPropertyViewModel model,
            EmulatorItem emulator,
            GameSettingsModel gameSettings,
            string id,
            LogCallback logCallback = null)
        {
            try
            {
                // 创建并配置DDBuilder
                var dBuilder = new DDBuilder();

                // 委托用于输出到当前窗口
                GameLogDelegate gameLogDelegate = (message) =>
                {
                    // 注释掉这行代码，因为它会导致日志重复记录
                    // 在ScriptEngine/Log.cs中的Info方法已经调用了XLogger.Info
                    // XLogger.Info($"定时任务({id}): {message}");

                    // 如果提供了日志回调函数，则调用它
                    if (logCallback != null)
                    {
                        try
                        {
                            // 可以在UI线程上调用回调
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                logCallback($"[{DateTime.Now:HH:mm:ss}] {message}");
                            });
                        }
                        catch (Exception ex)
                        {
                            XLogger.Error($"调用日志回调时出错: {ex.Message}");
                        }
                    }
                };

                // 初始化更新任务委托
                UpdateGameTaskDelegate OnUpdateGameTask = (taskName) =>
                {
                    XLogger.Info($"定时任务({id}) 当前执行: {taskName}");
                };

                // 初始化更新游戏模拟器句柄的委托
                UpdateGameHwndDelegate OnUpdateGameHwnd = (mumuhwnd, gamehwnd) =>
                {
                    //emulator.SimulatorConfig.MainHandle = mumuhwnd;
                    //emulator.SimulatorConfig.RenderHandle = gamehwnd;
                };

                // 创建通知发送器工厂
                var notificationSenderFactory = new NotificationSenderFactory(GlobalData.Instance.appConfig.dNet.User);
                var notificationService = new TaskNotificationService(notificationSenderFactory);

                SendNotificationDelegate sendNotificationDelegate = async (title, content) =>
                {
                    if (!emulator?.Config?.Notice_IsChecked ?? false) return false;
                    var type = emulator?.Config?.Notice_SelectItem ?? "邮件";
                    if (type == "自定义" && GlobalData.Instance.UserConfig.Notice_Ntfy)
                        await notificationService.SendSimpleNotificationAsync("App", title, content);
                    if (type == "自定义" && GlobalData.Instance.UserConfig.Notice_WxPush)
                        await notificationService.SendSimpleNotificationAsync("微信推送", title, content);
                    if (type == "自定义" && GlobalData.Instance.UserConfig.Notice_Pushplus)
                        await notificationService.SendSimpleNotificationAsync("Pushplus推送", title, content);
                    if (type == "自定义" && GlobalData.Instance.UserConfig.Notice_Miaotixing)
                        await notificationService.SendSimpleNotificationAsync("喵提醒", title, content);
                    return true;
                };

                // 获取任务配置列表
                var configs = model.GameTaskLists;

                // 获取任务配置
                var tcm = await Task.Run(() => Utils.GetTaskConfigs(configs));

                if (MumuUtils.TryParseHexHandle(emulator.SimulatorConfig.RenderWnd, out int renderWndHandle))
                {
                    if (renderWndHandle <= 0)
                    {
                        XLogger.Error($"模拟器句柄无效..");
                        return null;
                    }
                }

                dBuilder.SetDelegates(OnUpdateGameTask, OnUpdateGameHwnd, sendNotificationDelegate)
                    .SetSimulator("mumu", renderWndHandle)
                    .SetBindSetting(BindModels.GetBindModel("mumu", 1))
                    .SetTaskList(tcm)
                    .InitLog($"定时任务({id})", gameLogDelegate)
                    .SetGameSettings(gameSettings)
                    .SetUserConfigs(GlobalData.Instance.UserConfig.Pairs)
                    .SetMuMuIndex(int.Parse(emulator.SimulatorIndex))
                    .AddData("Base_Url", GlobalData.Instance.appConfig.dNet.System.GetCurrentHost());

                // 初始化图片服务器
                if (!await InitializePicServer(dBuilder))
                    return null;

                XLogger.Info("初始化大漠构建器成功");
                return dBuilder;
            }
            catch (Exception ex)
            {
                XLogger.Error($"初始化大漠构建器失败: {ex.Message}");
                XLogger.SaveException(ex);
                return null;
            }
        }

        /// <summary>
        /// 初始化图片服务器
        /// </summary>
        /// <param name="dBuilder">DDBuilder实例</param>
        /// <returns>初始化是否成功</returns>
        private static async Task<bool> InitializePicServer(DDBuilder dBuilder)
        {
            var picServer = await Task.Run(() => XConfig.LoadValueFromFile<string>("PicServer") ?? "默认");
            var picVer = GlobalData.Instance.PicServerVer ?? GlobalData.Instance.appConfig.Info.Now_Ver;

            XLogger.Info($"图库初始化中. 图库源：{picServer}，图库版本：{picVer}");

            if (!await dBuilder.SetPicsVerAsync(picServer, picVer))
            {
                XLogger.Warn("无法开始任务 云端图库初始化失败！请检查设置图库版本是否正确！");
                return false;
            }

            XLogger.Info("图库初始化完成，" + DynamicData.ToString());
            return true;
        }

        // 触发模拟器状态变更事件的方法
        private static void OnEmulatorStatusChanged()
        {
            EmulatorStatusChanged?.Invoke();
        }

        /// <summary>
        /// 发送完成通知
        /// </summary>
        /// <param name="emulator">模拟器信息</param>
        /// <param name="data">记录数据</param>
        /// <param name="executionTimeInMinutes">执行时长（分钟）</param>
        private static async Task SendCompletionNotice(EmulatorItem emulator, RecordData data, double executionTimeInMinutes)
        {
            try
            {
                // 创建通知发送器工厂
                var senderFactory = new NotificationSenderFactory(GlobalData.Instance.appConfig.dNet.User);
                var notificationService = new TaskNotificationService(senderFactory);

                // 准备通知消息
                var taskMessages = new Dictionary<string, string>
                {
                    { "执行任务", string.Join(",", data.TaskNames) },
                    { "点击次数", (data.MainClick + data.SubClick).ToString() }
                };

                // 格式化时长为两位小数
                string formattedTime = executionTimeInMinutes.ToString("0.00");

                // 发送任务完成通知
                bool success = await notificationService.SendTaskCompletionNoticeAsync(
                    "定时任务",
                    formattedTime,
                    data.MainClick,
                    data.SubClick,
                    data.TaskNames,
                    taskMessages,
                    emulator?.Config?.Notice_SelectItem ?? "邮件");

                if (success)
                {
                    XLogger.Info($"任务结束通知发送成功！执行时长：{formattedTime}分钟");
                }
                else
                {
                    XLogger.Warn("任务结束通知发送失败！");
                }
            }
            catch (Exception ex)
            {
                XLogger.Warn($"通知发送失败: {ex.Message}");
                XLogger.SaveException(ex);
            }
        }

        /// <summary>
        /// 启动模拟器和阴阳师应用
        /// </summary>
        /// <param name="emulator">模拟器信息</param>
        /// <returns>启动是否成功</returns>
        private static async Task<bool> StartEmulatorAndYYS(EmulatorItem emulator)
        {
            try
            {
                // 如果模拟器已经在运行，则不需要启动
                if (emulator.SimulatorConfig.IsRunning)
                {
                    XLogger.Info("模拟器已经在运行，无需启动");
                    return true;
                }

                // 初始化MuMu实例
                var mumu = new MuMu();
                var path = XConfig.LoadValueFromFile<string>("MuMuPath");
                if (!mumu.Init(path))
                {
                    XLogger.Warn("MuMu模拟器初始化失败，无法启动模拟器");
                    return false;
                }

                // 解析模拟器索引
                if (!int.TryParse(emulator.SimulatorIndex, out int index))
                {
                    XLogger.Warn($"无法解析模拟器索引: {emulator.SimulatorIndex}");
                    return false;
                }

                // 使用OpenByIndex方法启动模拟器
                var res = await mumu.OpenByIndexAsync(index);

                if (res.success && res.mainWndHandle != 0 && res.renderWndHandle != 0)
                {
                    // 等待模拟器启动
                    await Task.Delay(2000);

                    // 更新模拟器句柄信息
                    emulator.SimulatorConfig.RenderWnd = res.renderWndHandle.ToString("X8"); // 按照官网示例格式，8位十六进制，不带0x前缀
                    emulator.SimulatorConfig.IsRunning = true;

                    XLogger.Info($"成功获取模拟器和游戏句柄: MumuHandle={res.mainWndHandle}, GameHandle={res.renderWndHandle}");

                    // 等待额外6秒，确保模拟器已完全启动
                    await Task.Delay(6000);

                    // 启动阴阳师应用
                    mumu.Start_App("阴阳师", mumu.GetAdbPortByIndex(index).port);
                    await Task.Delay(5000);

                    isStartMumu = true;

                    // 触发模拟器状态变更事件
                    OnEmulatorStatusChanged();

                    XLogger.Info("已启动阴阳师应用");
                    return true;
                }
                else
                {
                    XLogger.Warn("模拟器启动失败，请重试！");
                    return false;
                }
            }
            catch (Exception ex)
            {
                XLogger.Warn($"启动模拟器过程中出错: {ex.Message}");
                XLogger.SaveException(ex);
                return false;
            }
        }

        /// <summary>
        /// 启动脚本执行
        /// </summary>
        /// <param name="model">任务模型</param>
        /// <param name="emulator">模拟器信息</param>
        /// <param name="dBuilder">大漠构建器</param>
        /// <returns>启动是否成功</returns>
        private static async Task<bool> StartScriptExecution(AddTaskPropertyViewModel model, EmulatorItem emulator, DDBuilder dBuilder, string id)
        {
            // 检查取消令牌状态
            if (!CanExecuteTask())
            {
                XLogger.Warn($"调度器已停止，脚本 [{id}] 不会启动");
                return false;
            }

            // 生成唯一的任务标识符
            string taskId = $"{emulator.Name}_{id}";

            // 获取脚本ID
            int scriptId = GetScriptId(taskId);

            // 设置Builder
            Scripts.SetBuilder(scriptId, dBuilder);

            // 设置脚本结束回调
            Scripts.SetScrpitCallBack_Data(scriptId, (data) => TaskEndCallback(emulator, data));

            // 再次检查取消令牌状态，确保在启动脚本前仍未取消
            if (!CanExecuteTask())
            {
                XLogger.Warn($"调度器已停止，脚本 [{id}] 不会启动");
                return false;
            }

            // 启动脚本
            string errorStr = "";
            bool startSuccess = await Task.Run(() => Scripts.Start(scriptId, out errorStr));

            if (!startSuccess)
            {
                XLogger.Error($"启动任务失败: {errorStr}");
                return false;
            }

            XLogger.Info($"任务'{model.GameTaskLists.GetScheduledTaskName()}'启动成功");
            return true;
        }

        /// <summary>
        /// 任务结束回调
        /// </summary>
        /// <param name="emulator">模拟器信息</param>
        /// <param name="data">记录数据</param>
        private static async void TaskEndCallback(EmulatorItem emulator, RecordData data)
        {
            try
            {
                // 记录任务完成信息
                XLogger.Info($"定时任务已完成，点击次数：{data.MainClick + data.SubClick}，执行任务：{string.Join(",", data.TaskNames)}");

                // 清理任务执行信息，停止超时监控
                string taskIdPrefix = emulator.Name + "_";
                string taskIdToRemove = null;
                double executionTimeInSeconds = 0;

                // 查找与此模拟器相关的任务ID和计算任务执行时长
                foreach (var kvp in _taskExecutionInfo)
                {
                    if (kvp.Key.StartsWith(taskIdPrefix))
                    {
                        taskIdToRemove = kvp.Key;
                        // 计算任务执行时间（秒）
                        executionTimeInSeconds = (DateTime.Now - kvp.Value.StartTime).TotalSeconds;
                        break;
                    }
                }

                // 移除任务执行信息
                if (taskIdToRemove != null)
                {
                    XLogger.Info($"清理任务 [{taskIdToRemove}] 的执行信息和超时监控");
                    _taskExecutionInfo.Remove(taskIdToRemove);
                }

                // 将秒转换为分钟
                double executionTimeInMinutes = executionTimeInSeconds / 60.0;

                // 记录执行时长
                XLogger.Info($"任务执行时长：{executionTimeInMinutes.ToString("0.00")}分钟");

                // 发送通知
                if (emulator?.Config?.Notice_IsChecked == true)
                    await SendCompletionNotice(emulator, data, executionTimeInMinutes);

                // 关闭模拟器
                await CloseEmulatorInternal(emulator);
            }
            catch (Exception ex)
            {
                XLogger.Error($"处理任务结束回调时出错: {ex.Message}");
                XLogger.SaveException(ex);
            }
        }

        /// <summary>
        /// 验证大漠插件状态
        /// </summary>
        /// <param name="dBuilder">大漠构建器</param>
        /// <returns>验证是否通过</returns>
        private static async Task<bool> ValidateDamoPlugin(DDBuilder dBuilder)
        {
            try
            {
                var dmsoftCode = await GlobalData.Instance.appConfig.dNet.User.GetDmsoftCodeAsync();

                // 验证大漠插件
                if (!await dBuilder.CheckAsync(dmsoftCode))
                {
                    XLogger.Warn("大漠插件验证失败，句柄可能已失效");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                XLogger.Error($"验证大漠插件失败: {ex.Message}");
                XLogger.SaveException(ex);
                return false;
            }
        }

        /// <summary>
        /// 验证启动条件
        /// </summary>
        /// <param name="model">任务模型</param>
        /// <param name="emulator">模拟器信息</param>
        /// <returns>验证是否通过</returns>
        private static async Task<bool> ValidateStartConditions(AddTaskPropertyViewModel model, EmulatorItem emulator)
        {
            // 检查任务配置是否有效
            if (model.GameTaskLists == null || model.GameTaskLists.Count == 0)
            {
                XLogger.Warn("没有配置任务，请先配置任务！");
                return false;
            }

            // 检查模拟器配置是否有效
            if (emulator == null || string.IsNullOrEmpty(emulator.SimulatorIndex))
            {
                XLogger.Warn("没有有效的模拟器索引，无法自动启动");
                return false;
            }

            // 获取MuMu模拟器路径
            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (string.IsNullOrEmpty(path))
            {
                XLogger.Warn("MuMu模拟器路径未设置，无法启动模拟器");
                return false;
            }

            return true;
        }
    }
}