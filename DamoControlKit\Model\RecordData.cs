﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DamoControlKit.Model
{
    /// <summary>
    /// 用户通知+记录数据类
    /// </summary>
    public class RecordData
    {
        /// <summary>
        /// 辅助点击次数
        /// </summary>
        public int SubClick;

        /// <summary>
        /// 辅助点击坐标列表
        /// </summary>
        public List<Point> SubPoints;

        /// <summary>
        /// 主键点击次数
        /// </summary>
        public int MainClick;

        /// <summary>
        /// 主键点击坐标列表
        /// </summary>
        public List<Point> MainPoints;

        /// <summary>
        /// 用户任务名
        /// </summary>
        public List<string> TaskNames;
    }
}