﻿using DanDing1.ViewModels.Base;

namespace DanDing1.Models
{
    /// <summary>
    /// 日志游戏控制模型
    /// </summary>
    /// <param name="gameViewBaseModel"></param>
    public class LogGameControlModel(GameViewBaseModel gameViewBaseModel)
    {
        private GameViewBaseModel _Base { get; } = gameViewBaseModel;

        /// <summary>
        /// 启动任务
        /// </summary>
        /// <returns></returns>
        public async Task StartGame()
        {
            await _Base.OnStart(null);
        }

        /// <summary>
        /// 停止任务
        /// </summary>
        /// <returns></returns>
        public async Task StopGame()
        {
            await _Base.OnStop();
        }

        /// <summary>
        /// 打开方案管理
        /// </summary>
        public void OpenTaskPage()
        {
            _Base.OnOpenTaskPage();
        }

        /// <summary>
        /// 设置开始成功回调
        /// </summary>
        /// <param name="action"></param>
        internal void SetStartedCallBack(LogGameControl_TaskStartedCallBack action)
        {
            _Base.TaskStartedCallBack = action;
        }

        /// <summary>
        /// 设置结束回调
        /// </summary>
        /// <param name="action"></param>
        internal void SetEndedCallBack(LogGameControl_TaskEndedCallBack action)
        {
            _Base.TaskEndedCallBack = action;
        }
    }
}