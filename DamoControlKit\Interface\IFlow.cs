﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DamoControlKit.Interface
{
    /// <summary>
    /// 流程功能接口
    /// </summary>
    internal interface IFlow
    {
        /// <summary>
        /// 启动
        /// </summary>
        void Start();
        /// <summary>
        /// 停止
        /// </summary>
        void Stop();
        /// <summary>
        /// 初始化
        /// </summary>
        void Init();
    }
}
