﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System.Runtime.InteropServices;
using XHelper.Models;
using Point = DamoControlKit.Model.Point;

namespace ScriptEngine.Tasks.TimerTasks
{
    internal class JiYangConfig
    {
        public JiYangConfig(TaskConfigsModel.Configs configs)
        {
            // 设置下一次寄养时间为当前时间
            DoTime = DateTime.Now;

            // 设置寄养优先级，默认为空字符串
            if (configs.Others.TryGetValue("JiYang_Priority", out string priority))
                Priority = priority;
            else
                Priority = "先太鼓-后斗鱼";

            // 设置是否跳过低级寄养，默认为false
            if (configs.Others.TryGetValue("JiYang_PassLowLevel", out string passLowLevelStr) && bool.TryParse(passLowLevelStr, out bool passLowLevel))
                PassLowLevel = passLowLevel;
            else
                PassLowLevel = true;

            // 设置指定寄养的好友名称
            if (configs.Others.TryGetValue("JiYang_DesignatedName", out string designatedName))
                DesignatedName = designatedName;
            else
                DesignatedName = "";

            // 设置指定寄养的策略
            if (configs.Others.TryGetValue("JiYang_DesignatedTactics", out string designatedTactics))
                DesignatedTactics = designatedTactics;
            else
                DesignatedTactics = "找不到选其它合适坑位";
        }

        /// <summary>
        /// 指定寄养的好友名称
        /// </summary>
        public string DesignatedName { get; set; }

        /// <summary>
        /// 指定寄养的策略
        /// </summary>
        public string DesignatedTactics { get; set; }

        /// <summary>
        /// 下一次寄养时间
        /// </summary>
        public DateTime DoTime { get; set; }

        /// <summary>
        /// 是否需要执行寄养操作
        /// </summary>
        public bool isDo => DateTime.Now >= DoTime;

        /// <summary>
        /// 是否跳过低级寄养
        /// </summary>
        public bool PassLowLevel { get; set; }

        /// <summary>
        /// 寄养优先级
        /// </summary>
        public string Priority { get; set; }
    }

    internal class JiYangTask : BaseTask
    {
        public JiYangConfig? Config;

        private Dictionary<string, (byte[] data, int size)> NameToPic = new() { };
        private Dictionary<string, string> NameToSy = new() { };

        // 添加重试计数器变量
        private const int MAX_REPINGGU_COUNT = 3; // 最多允许3次重新评估

        private List<Pixel> StarColorx6 =
            [
                new Pixel(848,334,"f4822b-060804",0.95),
                new Pixel(863,334,"f4822b-060804",0.95),
                new Pixel(878,334,"f4822b-060804",0.95),
                new Pixel(892,334,"f4822b-060804",0.95),
                new Pixel(907,334,"f4822b-060804",0.95),
                new Pixel(922,334,"f4822b-060804",0.95),
            ];

        public void CheckAndDo()
        {
            if (!Config.isDo) return;
            // 检查场景-庭院
            if (Scene.NowScene != "庭院")
            {
                log.Info("当前场景不在庭院，尝试前往庭院；");
                if (!Scene.TO.TingYuan())
                {
                    log.Info("无法前往庭院，任务结束；");
                    Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]寄养异常通知", $"寄养任务异常通知：脚本未能将您的游戏场景移动到庭院···已终止当前定时寄养任务，请手动上线查看！");
                    throw new Exception("无法前往庭院，任务结束；");
                }
            }

            log.Info("进入阴阳寮..");
            Fast.Click(550, 617, 591, 660);
            Sleep(2500);
            // 定义重试计数器
            int retryCount = 0;
            int maxRetries = 3;
        ReTryYYL:
            // 检查重试次数是否超过限制
            if (retryCount >= maxRetries)
            {
                log.Info($"尝试进入阴阳寮已达最大重试次数({maxRetries}次)，任务结束；");
                throw new TaskCanceledException();
            }
            // 检查场景-阴阳寮
            if (Scene.NowScene != "阴阳寮")
            {
                log.Info("当前场景不在阴阳寮，再试一次；");
                if (Scene.NowScene == "庭院")
                {
                    // 增加重试计数
                    retryCount++;
                    log.Info($"第{retryCount}次尝试进入阴阳寮...");
                    Fast.Click(550, 617, 591, 660);
                    Sleep(2500);
                    goto ReTryYYL;
                }
            }

            log.Info("进入结界..");
            Fast.Click(1072, 639, 1123, 686);
            Sleep(2500);
            // 定义结界重试计数器
            int retryJieJieCount = 0;
            int maxJieJieRetries = 3;
        ReTryJieJie:
            // 检查重试次数是否超过限制
            if (retryJieJieCount >= maxJieJieRetries)
            {
                log.Info($"尝试进入结界已达最大重试次数({maxJieJieRetries}次)，任务结束；");
                throw new TaskCanceledException();
            }
            // 检查场景-结界
            if (Scene.NowScene != "寄养")
            {
                log.Info("当前场景不在结界，再试一次；");
                if (Scene.NowScene == "阴阳寮")
                {
                    // 增加重试计数
                    retryJieJieCount++;
                    log.Info($"第{retryJieJieCount}次尝试进入结界...");
                    Fast.Click(1072, 639, 1123, 686);
                    Sleep(2500);
                    goto ReTryJieJie;
                }
            }

            Main();
        }

        /// <summary>
        /// 执行寄养流程
        /// </summary>
        public void Do_JiYang()
        {
            bool isLiao = false;
            NameToSy.Clear();
            NameToPic.Clear();

            // 添加跳转计数器
            int rePingGuCount = 0;

        RePingGu:
            // 增加计数并检查是否超出限制
            rePingGuCount++;
            if (rePingGuCount > MAX_REPINGGU_COUNT)
            {
                log.Warn($"寄养评估已达最大尝试次数({MAX_REPINGGU_COUNT}次)，暂时放弃本次寄养");
                Config.DoTime = DateTime.Now.AddMinutes(30); // 推迟半小时后再尝试
                Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]寄养任务通知",
                    $"经过多次尝试，未能找到合适的寄养对象。系统将在30分钟后（{Config.DoTime:yyyy-MM-dd HH:mm:ss}）重新尝试寄养任务。");

                // 返回庭院
                Fast.Click(449, 32, 723, 59);
                Sleep(2500);
                Fast.Click(20, 26, 64, 60);
                Sleep(1000);
                Fast.Click(20, 26, 64, 60);
                Sleep(2500);
                Fast.Click(20, 26, 64, 60);
                Sleep(2500);
                return;
            }

            log.Info($"开始第{rePingGuCount}次评估寄养对象..");
            bool isok = false;
            // 记录评估过程中的滑动次数
            int slideTimes = 0;
            // 记录连续无新数据的滑动次数
            int noNewDataSlides = 0;
            // 滑动前的数据量
            int prevDataCount = 0;
            while (!isok)
            {
                // 记录滑动前字典大小
                prevDataCount = NameToSy.Count;

                Fast.Ocr_String(312, 176, 533, 599, HandelUsers);
                foreach (var item in NameToSy)
                    if (item.Value.Contains("未放置") || item.Value.Contains("太阴"))
                    {
                        isok = true;
                        break;
                    }

                // 检查滑动后是否有新增数据
                if (NameToSy.Count > prevDataCount)
                {
                    // 有新数据，重置无新数据计数
                    noNewDataSlides = 0;
                }
                else
                {
                    // 无新数据，增加无新数据计数
                    noNewDataSlides++;
                    // 如果连续两次滑动没有新数据，退出循环
                    if (noNewDataSlides >= 2)
                    {
                        log.Info("连续两次滑动没有发现新的寄养对象，停止寻找...");
                        isok = true;
                        break;
                    }
                }

                if (!isok)
                {
                    Operational.Slide_Pos(new Point(413, 592), new Point(441, 360));
                    slideTimes++; // 增加滑动计数
                }
                Sleep(500);
            }

            var selectedUser = SelectPriorityUser();
            if (string.IsNullOrEmpty(selectedUser))
            {
                if (!isLiao)
                {
                    log.Warn("没有找到符合要求的寄养对象，尝试查找跨区好友..");
                    isLiao = true;
                    Fast.Click(357, 110, 423, 143);
                    Sleep(2000);
                    NameToSy.Clear();
                    NameToPic.Clear();
                    goto RePingGu;
                }
                else
                {
                    log.Warn("没有找到符合要求的寄养对象，等待10分钟后定时任务重新执行...");
                    Config.DoTime = DateTime.Now.AddMinutes(10);
                    Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]寄养任务通知", $"未找到符合条件的寄养对象，已搜索完所有好友和跨区好友。\r\n" +
                        $"系统将在10分钟后（{Config.DoTime:yyyy-MM-dd HH:mm:ss}）重新尝试寄养任务。");

                    Fast.Click(449, 32, 723, 59);
                    Sleep(2500);
                    Fast.Click(20, 26, 64, 60);
                    Sleep(1000);
                    Fast.Click(20, 26, 64, 60);
                    Sleep(2500);
                    Fast.Click(20, 26, 64, 60);
                    Sleep(2500);
                    return;
                }
            }

            // 点击选择的用户
            isok = false;
            // 设置最大滑动次数为评估阶段滑动次数+1
            int maxSlideTimes = slideTimes + 1;
            int currentSlideTimes = 0;
            while (!isok)
            {
                Fast.Ocr_String(312, 176, 533, 599, t =>
                {
                    foreach (var item in t)
                    {
                        if (item.Text.Contains(selectedUser))
                        {
                            Fast.Click(item.Center.X + 312, item.Center.Y + 170);
                            Sleep(1000);
                            isok = true;
                            break;
                        }
                    }
                });
                if (!isok)
                {
                    if (FindAndClickMemPicForName(selectedUser))
                        break;
                    Sleep(1000);
                    Operational.Slide_Pos(new Point(441, 400), new Point(413, 592));
                    currentSlideTimes++; // 增加当前滑动计数

                    // 如果超过最大滑动次数，跳转回重新评估
                    if (currentSlideTimes > maxSlideTimes)
                    {
                        log.Info("已达到最大滑动次数限制，重新评估寄养对象...");
                        // 检查重试次数是否达到上限
                        if (rePingGuCount < MAX_REPINGGU_COUNT)
                            goto RePingGu;
                        else
                        {
                            log.Warn($"寄养评估已达最大尝试次数({MAX_REPINGGU_COUNT}次)，暂时放弃本次寄养");
                            Config.DoTime = DateTime.Now.AddMinutes(30);
                            Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]寄养任务通知",
                                $"经过多次尝试，未能找到合适的寄养对象。系统将在30分钟后（{Config.DoTime:yyyy-MM-dd HH:mm:ss}）重新尝试寄养任务。");

                            // 返回庭院
                            Fast.Click(449, 32, 723, 59);
                            Sleep(2500);
                            Fast.Click(20, 26, 64, 60);
                            Sleep(1000);
                            Fast.Click(20, 26, 64, 60);
                            Sleep(2500);
                            Fast.Click(20, 26, 64, 60);
                            Sleep(2500);
                            return;
                        }
                    }
                }
                Sleep(500);
            }

            //截图 保存到.\\寄养 文件夹
            var path = Path.Combine(Environment.CurrentDirectory, "寄养");
            if (!Directory.Exists(path))
                Directory.CreateDirectory(path);
            Dm.CaptureJpg(0, 0, 2000, 2000, path + $"\\[{log.LogClassName}] {DateTime.Now:yyyyMMddHHmmss}.jpg", 100);

            // 进入他的结界
            Sleep(500);
            Fast.Click(846, 550, 937, 572);
            Sleep(2500);
            log.Info("判断能否寄养..");
            if (!Fast.Ocr_String(4, 589, 137, 710).Contains("全部"))
            {
                log.Warn("当前无法寄养，可能被其它玩家抢占了坑位..重新查找其它对象...");
                NameToSy.Clear();
                NameToPic.Clear();
                Fast.Click(22, 29, 66, 69);
                Sleep(1000);
                Fast.Click(22, 29, 66, 69);
                Sleep(2500);
                log.Info("重新打开式神育成..");
                Fast.Click(613, 310);
                Sleep(1500);
                log.Info("点击寄养式神..");
                Fast.Click(1187, 92);
                Sleep(1500);

                // 检查重试次数是否达到上限
                if (rePingGuCount < MAX_REPINGGU_COUNT)
                    goto RePingGu;
                else
                {
                    log.Warn($"寄养评估已达最大尝试次数({MAX_REPINGGU_COUNT}次)，暂时放弃本次寄养");
                    Config.DoTime = DateTime.Now.AddMinutes(30);
                    Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]寄养任务通知",
                        $"尝试寄养多次失败，可能是坑位被抢占。系统将在30分钟后（{Config.DoTime:yyyy-MM-dd HH:mm:ss}）重新尝试寄养任务。");

                    // 返回庭院
                    Fast.Click(449, 32, 723, 59);
                    Sleep(2500);
                    Fast.Click(20, 26, 64, 60);
                    Sleep(1000);
                    Fast.Click(20, 26, 64, 60);
                    Sleep(2500);
                    Fast.Click(20, 26, 64, 60);
                    Sleep(2500);
                    return;
                }
            }
            // 选择式神
            Fast.Click(193, 555, 242, 611);
            Sleep(1000);

            var pix = new Pixel(738, 543, "f3b25e-101010", 0.95);
            // 添加超时机制
            int timeout = 0;
            const int MAX_TIMEOUT = 20; // 最多等待10秒
            while (!pix.Find(Dm) && timeout < MAX_TIMEOUT)
            {
                Sleep(500);
                timeout++;
            }
            if (timeout >= MAX_TIMEOUT)
            {
                log.Warn("等待确认按钮超时，继续执行后续流程");
            }

            // 点击确定
            Fast.Click(710, 528, 772, 556);
            Sleep(1000);

            // 设置下次执行时间 +6小时
            Config.DoTime = DateTime.Now.AddHours(6);
            log.Info($"下次寄养时间设置为：{Config.DoTime:yyyy-MM-dd HH:mm:ss}");
            NameToSy.TryGetValue(selectedUser, out string? notifinfo);
            Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]寄养完成通知", $"当前寄养操作已完成，已寄养在用户：{selectedUser}【{notifinfo}】下\r\n" +
                $"下次寄养任务触发时间为：{Config.DoTime:yyyy-MM-dd HH:mm:ss}");

            // 返回
            Fast.Click(20, 26, 64, 60);
            Sleep(1000);
            Fast.Click(20, 26, 64, 60);
            Sleep(1000);
            Fast.Click(20, 26, 64, 60);
            Sleep(2500);
            Fast.Click(20, 26, 64, 60);
            Sleep(2500);
        }

        /// <summary>
        /// 设置下次寄养时间
        /// </summary>
        public void Do_SetJiYangTime()
        {
            // 最大重试次数
            const int maxRetries = 5;
            // 当前重试次数
            int retryCount = 0;
            // 是否成功解析时间
            bool parseSuccess = false;
            // 时间跨度对象
            TimeSpan timeSpan = TimeSpan.Zero;

            // 循环尝试解析时间，直到成功或达到最大重试次数
            while (!parseSuccess && retryCount <= maxRetries)
            {
                // 获取OCR识别到的时间字符串
                string timeStr = Fast.Ocr_String_V2(new(1098, 121, 1275, 152));
                timeStr = timeStr.Trim();
                log.Info($"识别到的寄养时间: {timeStr} (尝试 {retryCount + 1}/{maxRetries + 1})");

                // 使用正则表达式匹配时间格式 xx:xx:xx
                var match = System.Text.RegularExpressions.Regex.Match(timeStr, @"(\d{1,2}:\d{1,2}:\d{1,2})");
                if (match.Success)
                {
                    // 提取匹配到的时间字符串
                    string cleanTimeStr = match.Groups[1].Value;
                    log.Info($"正则匹配提取的时间: {cleanTimeStr}");
                    timeStr = cleanTimeStr;
                }

                // 尝试解析时间字符串为TimeSpan
                if (TimeSpan.TryParse(timeStr, out timeSpan))
                {
                    // 检查时间是否超过6小时
                    if (timeSpan.TotalHours <= 6)
                    {
                        // 解析成功且时间合理
                        parseSuccess = true;
                        log.Info($"成功解析时间: {timeSpan}");
                    }
                    else
                    {
                        // 时间超过6小时，视为解析失败
                        retryCount++;
                        log.Warn($"解析的时间 {timeSpan} 超过6小时，视为无效，1秒后重试 ({retryCount}/{maxRetries})");
                        Sleep(1000);
                    }
                }
                else
                {
                    // 解析失败，增加重试计数
                    retryCount++;

                    // 如果没有达到最大重试次数，等待1秒后重试
                    if (retryCount <= maxRetries)
                    {
                        log.Warn($"时间格式解析失败: {timeStr}，1秒后重试 ({retryCount}/{maxRetries})");
                        Sleep(1000);
                    }
                }
            }

            // 根据解析结果设置下次寄养时间
            if (parseSuccess)
            {
                // 检查解析出的时间是否超过6小时
                if (timeSpan.TotalHours > 6)
                {
                    // 如果超过6小时，将其限制为6小时
                    timeSpan = TimeSpan.FromHours(6);
                    log.Info("解析出的时间超过6小时，已限制为6小时");
                }

                // 计算下次寄养时间（当前时间 + 识别到的时间间隔）
                DateTime nextTime = DateTime.Now.Add(timeSpan);
                // 设置下次寄养时间
                Config.DoTime = nextTime;
                log.Info($"下次寄养时间设置为: {nextTime:yyyy-MM-dd HH:mm:ss}");
                Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]下次寄养通知", $"当前寄养以初始化完成\r\n 下次寄养任务触发时间为：{Config.DoTime:yyyy-MM-dd HH:mm:ss}");
            }
            else
            {
                // 所有重试都失败，设置默认时间（当前时间 + 6小时）
                DateTime defaultNextTime = DateTime.Now.AddHours(6);
                Config.DoTime = defaultNextTime;
                log.Warn($"经过{maxRetries}次重试后仍无法解析时间格式，设置默认等待时间6小时，下次寄养时间: {defaultNextTime:yyyy-MM-dd HH:mm:ss}");
            }

            // 返回庭院
            log.Info("寄养任务完成，返回庭院...");
            Fast.Click(20, 26, 64, 60);
            Sleep(1000);
            Fast.Click(20, 26, 64, 60);
            Sleep(2500);
            Fast.Click(20, 26, 64, 60);
            Sleep(2500);
        }

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "寄养");
            StarColorx6.ForEach(x => x.SetXsoft(dm));
        }

        /// <summary>
        /// Main
        /// </summary>
        public void Main()
        {
            //还不在寄养界面
            if (Scene.NowScene != "寄养")
            {
                log.Info($"尝试强制点击点击式神育成..");
                Fast.Click(601, 298, 629, 327);
                Sleep(2500);
            }
            else
            {
                log.Info("点击式神育成..");
                Fast.Click(601, 298, 629, 327);
                Sleep(2500);
            }

            if (!Mp.Filter("式神育成").FindAll())
            {
                log.Warn("不在式神育成界面，无法继续..");
                return;
            }

            log.Info("检查是否可以寄养..");
            if (Mp.Filter("可寄养").FindAll())
            {
                log.Info("当前状态：未寄养，执行寄养流程；");
                Fast.Click(1161, 68, 1209, 115);
                Sleep(2500);
                if (!Mp.Filter("寄养界面").FindAll()) { log.Warn("不在寄养界面，无法继续.."); return; }

                if (!string.IsNullOrEmpty(Config.DesignatedName))
                    Do_JiYang_Designated();
                else
                    Do_JiYang();
            }
            else
            {
                log.Info("当前状态：已经寄养，重新设置下次任务执行时间；");
                Do_SetJiYangTime();
            }
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Config = new JiYangConfig(configs);
            CheckAndDo();
        }

        private void Do_JiYang_Designated()
        {
            log.Info($"查找指定寄养的好友名称：{Config.DesignatedName}");

            bool isLiao = false;
            NameToSy.Clear();
            NameToPic.Clear();

            // 添加跳转计数器
            int rePingGuCount = 0;

        RePingGu:
            // 增加计数并检查是否超出限制
            rePingGuCount++;
            if (rePingGuCount > MAX_REPINGGU_COUNT)
            {
                log.Warn($"指定寄养评估已达最大尝试次数({MAX_REPINGGU_COUNT}次)，暂时放弃本次寄养");
                Config.DoTime = DateTime.Now.AddMinutes(30); // 推迟半小时后再尝试
                Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]寄养任务通知",
                    $"经过多次尝试，未能找到指定的寄养对象 {Config.DesignatedName}。系统将在30分钟后（{Config.DoTime:yyyy-MM-dd HH:mm:ss}）重新尝试寄养任务。");

                // 返回庭院
                Fast.Click(449, 32, 723, 59);
                Sleep(2500);
                Fast.Click(20, 26, 64, 60);
                Sleep(1000);
                Fast.Click(20, 26, 64, 60);
                Sleep(2500);
                Fast.Click(20, 26, 64, 60);
                Sleep(2500);
                return;
            }

            log.Info($"开始第{rePingGuCount}次评估指定寄养对象..");
            bool isok = false;
            // 记录评估过程中的滑动次数
            int slideTimes = 0;
            // 记录连续无新数据的滑动次数
            int noNewDataSlides = 0;
            // 滑动前的数据量
            int prevDataCount = 0;
            // 是否找到指定好友
            bool foundDesignatedUser = false;
            // 保存匹配到的实际好友名称
            string matchedUserName = "";

            while (!isok)
            {
                // 记录滑动前字典大小
                prevDataCount = NameToSy.Count;

                Fast.Ocr_String(312, 176, 533, 599, HandelUsers);

                bool end = false;
                // 检查是否找到了包含指定好友名称的用户
                foreach (var userName in NameToSy.Keys)
                {
                    if (NameToSy[userName].Contains("未放置") || NameToSy[userName].Contains("太阴"))
                        end = true;

                    // 使用包含方法检查，而不是完全匹配
                    if (userName.Contains(Config.DesignatedName))
                    {
                        log.Info($"找到匹配好友：{userName}，匹配关键词：{Config.DesignatedName}");
                        foundDesignatedUser = true;
                        matchedUserName = userName; // 保存匹配到的完整名称
                        isok = true;
                        break;
                    }
                }

                if (foundDesignatedUser || end)
                    break;

                // 检查滑动后是否有新增数据
                if (NameToSy.Count > prevDataCount)
                {
                    // 有新数据，重置无新数据计数
                    noNewDataSlides = 0;
                }
                else
                {
                    // 无新数据，增加无新数据计数
                    noNewDataSlides++;
                    // 如果连续两次滑动没有新数据，退出循环
                    if (noNewDataSlides >= 2)
                    {
                        log.Info("连续两次滑动没有发现新的寄养对象，停止寻找...");
                        isok = true;
                        break;
                    }
                }

                if (!isok)
                {
                    Operational.Slide_Pos(new Point(413, 592), new Point(441, 360));
                    slideTimes++; // 增加滑动计数
                }
                Sleep(500);
            }

            // 如果找到了匹配好友，检查该好友是否有可用坑位
            if (foundDesignatedUser && !string.IsNullOrEmpty(matchedUserName))
            {
                // 使用实际匹配到的用户名获取信息，而不是指定的关键词
                NameToSy.TryGetValue(matchedUserName, out string userInfo);
                log.Info($"匹配好友信息：{matchedUserName} - {userInfo}");

                // 检查是否有可用坑位
                bool hasSlot = userInfo.Contains("太鼓") || userInfo.Contains("斗鱼");
                int stars = ExtractStars(userInfo);

                // 如果启用了跳过低级寄养且星级低于等于3，并且策略是选择其他合适坑位
                if (Config.PassLowLevel && stars <= 3 && hasSlot && Config.DesignatedTactics == "找不到选其它合适坑位")
                {
                    log.Info($"匹配好友 {matchedUserName} 星级过低（{stars}星），根据策略尝试其它好友");
                    Fast.Click(462, 24, 629, 61);
                    Sleep(1000);
                    Fast.Click(1159, 71, 1206, 111);
                    Sleep(1000);
                    // 执行常规寄养流程
                    Do_JiYang();
                    return;
                }

                if (hasSlot)
                {
                    // 点击选择的用户
                    isok = false;
                    // 设置最大滑动次数为评估阶段滑动次数+1
                    int maxSlideTimes = slideTimes + 1;
                    int currentSlideTimes = 0;
                    while (!isok)
                    {
                        Fast.Ocr_String(312, 176, 533, 599, t =>
                        {
                            foreach (var item in t)
                            {
                                // 使用包含关系检查，而不是完全匹配
                                if (item.Text.Contains(matchedUserName) || matchedUserName.Contains(item.Text))
                                {
                                    Fast.Click(item.Center.X + 312, item.Center.Y + 170);
                                    Sleep(1000);
                                    isok = true;
                                    break;
                                }
                            }
                        });
                        if (!isok)
                        {
                            if (FindAndClickMemPicForName(matchedUserName)) // 使用匹配到的完整名称
                                break;
                            Sleep(1000);
                            Operational.Slide_Pos(new Point(441, 400), new Point(413, 592));
                            currentSlideTimes++; // 增加当前滑动计数

                            // 如果超过最大滑动次数，跳转回重新评估
                            if (currentSlideTimes > maxSlideTimes)
                            {
                                log.Info("已达到最大滑动次数限制，重新评估寄养对象...");
                                // 检查重试次数是否达到上限
                                if (rePingGuCount < MAX_REPINGGU_COUNT)
                                    goto RePingGu;
                                else
                                {
                                    log.Warn($"指定寄养评估已达最大尝试次数({MAX_REPINGGU_COUNT}次)，暂时放弃本次寄养");
                                    Config.DoTime = DateTime.Now.AddMinutes(30);
                                    Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]寄养任务通知",
                                        $"经过多次尝试，未能在列表中找到匹配的指定寄养对象。系统将在30分钟后（{Config.DoTime:yyyy-MM-dd HH:mm:ss}）重新尝试寄养任务。");

                                    // 返回庭院
                                    Fast.Click(449, 32, 723, 59);
                                    Sleep(2500);
                                    Fast.Click(20, 26, 64, 60);
                                    Sleep(1000);
                                    Fast.Click(20, 26, 64, 60);
                                    Sleep(2500);
                                    Fast.Click(20, 26, 64, 60);
                                    Sleep(2500);
                                    return;
                                }
                            }
                        }
                        Sleep(500);
                    }

                    //截图 保存到.\\寄养 文件夹
                    var path = Path.Combine(Environment.CurrentDirectory, "寄养");
                    if (!Directory.Exists(path))
                        Directory.CreateDirectory(path);
                    Dm.CaptureJpg(0, 0, 2000, 2000, path + $"\\[{log.LogClassName}] {DateTime.Now:yyyyMMddHHmmss}.jpg", 100);

                    // 进入他的结界
                    Sleep(500);
                    Fast.Click(846, 550, 937, 572);
                    Sleep(2500);

                    // 选择式神
                    Fast.Click(193, 555, 242, 611);
                    Sleep(1000);

                    var pix = new Pixel(738, 543, "f3b25e-101010", 0.95);
                    // 添加超时机制
                    int timeout = 0;
                    const int MAX_TIMEOUT = 20; // 最多等待10秒
                    while (!pix.Find(Dm) && timeout < MAX_TIMEOUT)
                    {
                        Sleep(500);
                        timeout++;
                    }
                    if (timeout >= MAX_TIMEOUT)
                    {
                        log.Warn("等待确认按钮超时，继续执行后续流程");
                    }

                    // 点击确定
                    Fast.Click(710, 528, 772, 556);
                    Sleep(1000);

                    // 设置下次执行时间 +6小时
                    Config.DoTime = DateTime.Now.AddHours(6);
                    log.Info($"下次寄养时间设置为：{Config.DoTime:yyyy-MM-dd HH:mm:ss}");
                    Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]寄养完成通知",
                        $"当前寄养操作已完成，已寄养在匹配好友：{matchedUserName}【{userInfo}】下\r\n" +
                        $"下次寄养任务触发时间为：{Config.DoTime:yyyy-MM-dd HH:mm:ss}");

                    // 返回
                    Fast.Click(20, 26, 64, 60);
                    Sleep(1000);
                    Fast.Click(20, 26, 64, 60);
                    Sleep(1000);
                    Fast.Click(20, 26, 64, 60);
                    Sleep(2500);
                    Fast.Click(20, 26, 64, 60);
                    Sleep(2500);
                }
                else
                {
                    log.Info($"匹配好友 {matchedUserName} 没有可用坑位");
                    // 根据策略决定后续操作
                    if (Config.DesignatedTactics == "找不到等待10分钟再试")
                    {
                        log.Info("根据策略，等待10分钟后重试");
                        Config.DoTime = DateTime.Now.AddMinutes(10);
                        Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]寄养等待通知",
                            $"匹配好友 {matchedUserName} 没有可用坑位，将在10分钟后重试\r\n" +
                            $"下次寄养任务触发时间为：{Config.DoTime:yyyy-MM-dd HH:mm:ss}");

                        // 返回庭院
                        Fast.Click(449, 32, 723, 59);
                        Sleep(2500);
                        Fast.Click(20, 26, 64, 60);
                        Sleep(1000);
                        Fast.Click(20, 26, 64, 60);
                        Sleep(2500);
                        Fast.Click(20, 26, 64, 60);
                        Sleep(2500);
                    }
                    else
                    {
                        // 执行常规寄养流程
                        log.Info("根据策略，尝试寄养到其它合适坑位");
                        Fast.Click(462, 24, 629, 61);
                        Sleep(1000);
                        Fast.Click(1159, 71, 1206, 111);
                        Sleep(1000);
                        Do_JiYang();
                    }
                }
            }
            else
            {
                // 没有找到指定好友，检查是否查找跨区好友
                if (!isLiao)
                {
                    log.Warn("没有找到匹配指定关键词的好友，尝试查找跨区好友..");
                    isLiao = true;
                    Fast.Click(357, 110, 423, 143);
                    Sleep(2000);
                    NameToSy.Clear();
                    NameToPic.Clear();
                    goto RePingGu;
                }
                else
                {
                    log.Warn($"在寮和跨区好友中都没有找到匹配关键词 {Config.DesignatedName} 的好友");
                    // 根据策略决定后续操作
                    if (Config.DesignatedTactics == "找不到等待10分钟再试")
                    {
                        log.Info("根据策略，等待10分钟后重试");
                        Config.DoTime = DateTime.Now.AddMinutes(10);
                        Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]寄养等待通知",
                            $"未能找到匹配关键词 {Config.DesignatedName} 的好友，将在10分钟后重试\r\n" +
                            $"下次寄养任务触发时间为：{Config.DoTime:yyyy-MM-dd HH:mm:ss}");

                        // 返回庭院
                        Fast.Click(449, 32, 723, 59);
                        Sleep(2500);
                        Fast.Click(20, 26, 64, 60);
                        Sleep(1000);
                        Fast.Click(20, 26, 64, 60);
                        Sleep(2500);
                        Fast.Click(20, 26, 64, 60);
                        Sleep(2500);
                    }
                    else
                    {
                        // 执行常规寄养流程
                        log.Info("根据策略，尝试寄养到其它合适坑位");
                        Fast.Click(462, 24, 629, 61);
                        Sleep(1000);
                        Fast.Click(1159, 71, 1206, 111);
                        Sleep(1000);
                        Do_JiYang();
                    }
                }
            }
        }

        /// <summary>
        /// 从字符串中提取星级
        /// </summary>
        /// <param name="userInfo">用户信息字符串</param>
        /// <returns>星级数值</returns>
        private int ExtractStars(string userInfo)
        {
            if (string.IsNullOrEmpty(userInfo))
                return 0;

            int starIndex = userInfo.LastIndexOf('|');
            if (starIndex >= 0 && starIndex < userInfo.Length - 1)
            {
                string starPart = userInfo.Substring(starIndex + 1);
                if (int.TryParse(starPart.Replace("星", ""), out int stars))
                    return stars;
            }

            return 0;
        }

        /// <summary>
        /// 查找并点击 NameToPic 图片的位置
        /// </summary>
        /// <returns>找到并点击返回true，否则返回false</returns>
        private bool FindAndClickMemPicForName(string name)
        {
            // 现在使用保存的数据
            if (!string.IsNullOrEmpty(name) && NameToPic.TryGetValue(name, out var picData))
            {
                // 创建一个固定内存区域来存放字节数据
                GCHandle handle = GCHandle.Alloc(picData.data, GCHandleType.Pinned);
                try
                {
                    // 获取固定内存的指针
                    IntPtr ptr = handle.AddrOfPinnedObject();
                    // 使用指针调用FindPicMemE
                    var result = Dm.FindPicMemE(312, 176, 533, 599, $"{ptr.ToInt64()},{picData.size}", "202020", 0.9, 0);

                    // 解析返回的结果
                    if (!string.IsNullOrEmpty(result) && result != "-1|-1|-1")
                    {
                        // 分割返回的结果字符串 "index|x|y"
                        string[] parts = result.Split('|');
                        if (parts.Length == 3)
                        {
                            if (int.TryParse(parts[1], out int x) && int.TryParse(parts[2], out int y))
                            {
                                // 点击找到的坐标
                                log.Info($"找到用户 {name} 的匹配图像，点击坐标 ({x}, {y})");
                                Fast.Click(x, y);
                                Sleep(1000); // 点击后等待一秒
                                return true;
                            }
                        }
                    }
                    else
                    {
                        log.Info($"未找到用户 {name} 的匹配图像");
                    }
                }
                finally
                {
                    // 释放固定内存
                    if (handle.IsAllocated)
                        handle.Free();
                }
            }

            return false;
        }

        /// <summary>
        /// 根据类型寻找最佳寄养用户
        /// </summary>
        /// <param name="type">类型：太鼓或斗鱼</param>
        /// <returns>最佳用户名</returns>
        private string FindBestUserByType(string type)
        {
            string bestUser = "";
            int bestStars = 0;

            foreach (var user in NameToSy)
            {
                // 跳过未放置的用户，只处理有太鼓或斗鱼的用户
                if (user.Value.Contains("未放置") || !user.Value.Contains(type))
                    continue;

                // 提取星级
                int stars = ExtractStars(user.Value);

                // 如果设置了跳过低级寄养且星级小于3，则跳过
                if (Config.PassLowLevel && stars <= 3)
                    continue;

                // 找到更高星级的用户
                if (stars > bestStars)
                {
                    bestUser = user.Key;
                    bestStars = stars;
                }
            }

            if (!string.IsNullOrEmpty(bestUser))
                log.Info($"找到最佳{type}寄养对象: {bestUser}, 星级: {bestStars}");

            return bestUser;
        }

        /// <summary>
        /// 获取星星数量
        /// </summary>
        /// <returns></returns>
        private int GetStarNumber()
        {
            int stat = 0;
            foreach (var item in StarColorx6)
            {
                if (item.Find(null)) stat++;
            }
            return stat;
        }

        /// <summary>
        /// 处理识别到的用户
        /// </summary>
        /// <param name="t"></param>
        private void HandelUsers(List<XOcr_TextBlock> t)
        {
            foreach (var item in t)
            {
                item.Text = item.Text.Trim();
                var iscon = Dm.FindColor(item.BoxPoints[0].X + 312, item.BoxPoints[0].Y + 176, item.BoxPoints[2].X + 312, item.BoxPoints[2].Y + 176, "af4c26-101010", 0.98, 0, out int _, out int _);
                //Dm.CaptureJpg(item.BoxPoints[0].X + 312, item.BoxPoints[0].Y + 176, item.BoxPoints[2].X + 312, item.BoxPoints[2].Y + 176, "asdasd.jpg", 100);
                if (iscon >= 1) continue;

                if (string.IsNullOrEmpty(item.Text) || item.Text.Contains("最近寄养")
                    || item.Text.Contains("俄江有斤")
                    || item.Text.Contains("最近等养")
                    || item.Text.Contains("最近奇行")) continue;
                if (NameToSy.ContainsKey(item.Text)) continue;

                Dm.GetScreenDataBmp(item.BoxPoints[0].X + 332, item.BoxPoints[0].Y + 176, item.BoxPoints[2].X + 312, item.BoxPoints[2].Y + 176, out int dataPtr, out int size);
                // 将截图数据保存到字典中 - 创建数据副本
                if (!string.IsNullOrEmpty(item.Text))
                {
                    // 创建字节数组以保存数据副本
                    byte[] dataCopy = new byte[size];
                    // 使用Marshal类从非托管内存复制数据
                    Marshal.Copy(new IntPtr(dataPtr), dataCopy, 0, size);
                    // 保存数据副本到字典
                    NameToPic[item.Text] = (dataCopy, size);
                }

                Fast.Click(item.Center.X + 312, item.Center.Y + 176);
                Sleep(200);
                string userinfo = Fast.Ocr_String(781, 348, 986, 457);
                int starNumber = GetStarNumber();
                NameToSy.TryAdd(item.Text, userinfo + "|" + starNumber.ToString() + "星");
                if (userinfo.Contains("未放置")) break;
            }
        }

        private string SelectPriorityUser()
        {
            // 检查是否有可寄养的用户
            if (NameToSy.Count == 0)
            {
                log.Info("没有找到可用的寄养对象");
                return "";
            }

            log.Info($"使用优先级策略: {Config.Priority}");
            string selectedUser = "";

            // 根据优先级筛选
            switch (Config.Priority)
            {
                case "先太鼓-后斗鱼":
                    selectedUser = FindBestUserByType("太鼓");
                    if (string.IsNullOrEmpty(selectedUser))
                        selectedUser = FindBestUserByType("斗鱼");
                    break;

                case "先斗鱼-后太鼓":
                    selectedUser = FindBestUserByType("斗鱼");
                    if (string.IsNullOrEmpty(selectedUser))
                        selectedUser = FindBestUserByType("太鼓");
                    break;

                case "只寄养太鼓":
                    selectedUser = FindBestUserByType("太鼓");
                    break;

                case "只寄养斗鱼":
                    selectedUser = FindBestUserByType("斗鱼");
                    break;

                default:
                    // 默认使用"先太鼓-后斗鱼"策略
                    log.Info($"未知的优先级设置 '{Config.Priority}'，使用默认策略'先太鼓-后斗鱼'");
                    selectedUser = FindBestUserByType("太鼓");
                    if (string.IsNullOrEmpty(selectedUser))
                        selectedUser = FindBestUserByType("斗鱼");
                    break;
            }

            if (string.IsNullOrEmpty(selectedUser))
            {
                log.Info($"按照优先级 '{Config.Priority}' 没有找到合适的寄养对象");
                return "";
            }

            return selectedUser;
        }
    }
}