﻿using DanDing1.ViewModels.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Wpf.Ui;

namespace DanDing1.ViewModels.Pages
{
    public partial class Game4ViewModel : GameViewBaseModel
    {
        public Game4ViewModel(INavigationService navigationService, IServiceProvider serviceProvider, IContentDialogService contentDialogService) : base(navigationService, serviceProvider, contentDialogService)
        {
        }
    }
}