﻿namespace XHelper.DanDingNet
{
    public class DDApi
    {
        public static Dictionary<string, string> Api = new()
        {
            {"公告","DD/info" },
            {"登录","DD/login" },
            {"注册","DD/register" },
            {"检测账号状态","DD/check" },
            {"试用","DD/free" },
            {"检查试用","DD/check_free" },
            {"退出登录","DD/logout" },
            {"大漠注册码","DD/get_dmsoft_code" },
            {"用户信息","DD/userinfo" },
            {"发送验证码","DD/send_Code" },
            {"卡密充值","DD/use_kami" },
            {"发送通知","DD/send_notice" },
            {"获取app通知key","DD/get_ntfy_key" },
            {"获取临时图库源地址","DD/get_pics_baseurl" },
            {"保存云端配置","DD/save_config" },
            {"重置密码","DD/resSetPwd" },
            {"修改密码","DD/changePwd" },
            {"获取内测版本","DD/info_beta" },
            {"执行活动","DD/activity_do" },
            {"获取活动","DD/get_activity" },
            {"查询用户ID","DD/query_user_id" },
            {"获取图库版本","DD/get_all_pic_vers" },
            {"获取图库文件","DD/DD_Core" },
            {"查询用户积分","DD/user_points" },
            {"绑定QQ", "DD/bindQQ" },
            {"存储日志", "DD/store_log" },
            {"设置WebToken", "DD/set_web_token" },
            {"获取WebToken状态", "DD/get_web_token_status" },
        };

        public static Dictionary<string, string> ServerHost = new()
        {
            {"港式接口1","https://api.danding.vip/" },
            {"中转线路","http://**************:8002/" },
            {"中转线路2","http://*************:8001/" },
            //{"从机测试","http://************:8001/" },
            {"局域网测试","http://************:8002/" },
            {"本机测试","http://127.0.0.1:8002/" },
        };
    }
}