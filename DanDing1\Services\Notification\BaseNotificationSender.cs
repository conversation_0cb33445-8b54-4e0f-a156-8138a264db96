using System.Threading.Tasks;
using XHelper;

namespace DanDing1.Services.Notification
{
    /// <summary>
    /// 通知发送器基类，提供通用实现
    /// </summary>
    public abstract class BaseNotificationSender : INotificationSender
    {
        /// <summary>
        /// 通知类型标识符
        /// </summary>
        public abstract string NoticeType { get; }

        /// <summary>
        /// 通知类型显示名称
        /// </summary>
        public abstract string DisplayName { get; }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        protected virtual int MaxRetryCount => 3;

        /// <summary>
        /// 发送通知
        /// </summary>
        /// <param name="title">通知标题</param>
        /// <param name="content">通知内容</param>
        /// <param name="extraParams">额外参数</param>
        /// <returns>发送是否成功</returns>
        public virtual async Task<bool> SendAsync(string title, string content, object extraParams = null)
        {
            int retryCount = 0;
            bool success = false;

            // 格式化内容
            string formattedContent = FormatContent(content);

            while (retryCount < MaxRetryCount && !success)
            {
                try
                {
                    success = await SendNotificationCoreAsync(title, formattedContent, extraParams);

                    if (!success)
                    {
                        XLogger.Warn($"[{DisplayName}] 通知发送失败，正在重试 ({retryCount + 1}/{MaxRetryCount})");
                        retryCount++;
                    }
                }
                catch (System.Exception ex)
                {
                    XLogger.Warn($"[{DisplayName}] 通知发送异常，正在重试 ({retryCount + 1}/{MaxRetryCount}): {ex.Message}");
                    XLogger.SaveException(ex);
                    retryCount++;
                }

                if (!success && retryCount < MaxRetryCount)
                {
                    await Task.Delay(2000); // 失败后等待2秒再重试
                }
            }

            return success;
        }

        /// <summary>
        /// 发送通知的核心实现，由子类实现
        /// </summary>
        /// <param name="title">通知标题</param>
        /// <param name="content">格式化后的通知内容</param>
        /// <param name="extraParams">额外参数</param>
        /// <returns>发送是否成功</returns>
        protected abstract Task<bool> SendNotificationCoreAsync(string title, string content, object extraParams);

        /// <summary>
        /// 格式化通知内容
        /// </summary>
        /// <param name="content">原始内容</param>
        /// <returns>格式化后的内容</returns>
        public abstract string FormatContent(string content);
    }
}