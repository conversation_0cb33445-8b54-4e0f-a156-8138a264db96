﻿using Compunet.YoloSharp.Data;
using DamoControlKit.Model;
using ScriptEngine.Core;
using System.Runtime.InteropServices;
using XHelper;
using XHelper.Models;

namespace ScriptEngine.Tasks.Base
{
    /// <summary>
    /// 快速操作类
    /// </summary>
    internal class Fast
    {
        public Fast(dmsoft dm, CancellationTokenSource ct, BaseTask task)
        {
            Dm = dm;
            Ct = ct;
            Scence = new(this);
            Task = task;
        }

        private BaseTask Task { get; }
        private CancellationTokenSource Ct { get; }
        public dmsoft Dm { get; }

        /// <summary>
        /// 快速场景操作
        /// </summary>
        public ScenceModel Scence { get; }

        /// <summary>
        /// 快速点击范围
        /// </summary>
        public void Click(int x, int y)
        {
            Point p = new(x, y);
            p.SetXsoft(Dm);
            p.Click();
        }

        public void Click(Position pos) => Click(pos.X, pos.Y, pos.X1, pos.Y1);

        /// <summary>
        /// 快速点击范围
        /// </summary>
        /// <param name="pos_str"></param>
        public void Click(string pos_str)
        {
            Position p = new Position(pos_str);
            p.SetXsoft(Dm);
            p.Click();
        }

        /// <summary>
        /// 快速点击范围 Fast！！！
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <param name="x1"></param>
        /// <param name="y1"></param>
        public void Click(int x, int y, int x1, int y1)
        {
            Position p = new Position(x, y, x1, y1);
            p.SetXsoft(Dm);
            p.Click();
        }

        /// <summary>
        /// 比对颜色
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <param name="color"></param>
        /// <param name="sim"></param>
        /// <returns></returns>
        public bool CmpColor(int x, int y, string color, double sim = 0.9)
        {
            return Dm.CmpColor(x, y, color, sim) == 0;
        }

        /// <summary>
        /// 长按
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <param name="time"></param>
        public void LongClick(int x, int y, int time)
        {
            Dm.MoveTo(x, y);
            Dm.LeftDown();
            Dm.delay(time);
            Dm.LeftUp();
        }

        public string Ocr_Local(Position pos) =>
            Ocr_Local(pos.X, pos.Y, pos.X1, pos.Y1);

        /// <summary>
        /// 本地Ocr
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <param name="x1"></param>
        /// <param name="y1"></param>
        /// <returns></returns>
        public string Ocr_Local(int x, int y, int x1, int y1)
        {
            Dm.GetScreenDataBmp(x, y, x1, y1, out int data, out int size);
            byte[] genePic2 = new byte[size];
            nint ptr = new(data);
            for (int i = 0; i < size; i++)
                genePic2[i] = Marshal.ReadByte(ptr + 1 * i);
            string txt = XOcr.Local_Ocr_Number(genePic2);
            return txt.Replace('\n', ' ');
        }

        public string Ocr_String(Position pos, Action<List<XOcr_TextBlock>>? callback = null) =>
             Ocr_String(pos.X, pos.Y, pos.X1, pos.Y1, callback);

        public string Ocr_String_V2(Position pos, Action<List<XOcr_TextBlock>>? callback = null) =>
                Ocr_String(pos.X, pos.Y, pos.X1, pos.Y1, callback, useV2: true);

        private bool disOcrPro = false;

        /// <summary>
        /// 本地Ocr_字符串
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <param name="x1"></param>
        /// <param name="y1"></param>
        /// <returns></returns>
        public string Ocr_String(int x, int y, int x1, int y1, Action<List<XOcr_TextBlock>>? callback = null, bool useV2 = false)
        {
            if (useV2) goto v2;
            if (!disOcrPro)
            {
                if (!Directory.Exists(Utils.GetTempFolder)) Directory.CreateDirectory(Utils.GetTempFolder);//验证能否使用OCR-Pro
                string fileName = "tmp" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".jpg";
                Dm.CaptureJpg(x, y, x1, y1, Utils.GetTempFolder + "\\" + fileName, 95);
                var res = XOcr.Local_Ocr_String_Pro(Utils.GetTempFolder + "\\" + fileName, callback);
                File.Delete(Utils.GetTempFolder + "\\" + fileName);//删除文件
                if (res != "-1")
                    return res;
                else
                {
                    XLogger.Write("OCRPro识别失败，为提高速度，已禁止使用OCRPro方式进行文字识别..");
                    disOcrPro = true;
                }
            }

        v2:
            Dm.GetScreenDataBmp(x, y, x1, y1, out int data, out int size);
            byte[] genePic2 = new byte[size];
            nint ptr = new(data);
            for (int i = 0; i < size; i++)
                genePic2[i] = Marshal.ReadByte(ptr + 1 * i);
            string txt = XOcr.Local_Ocr_String(genePic2, callback);
            return txt.Replace('\n', ' ');
        }

        /// <summary>
        /// 等待点-颜色
        /// </summary>
        /// <returns></returns>
        public void WaitColor(int x, int y, string color, double sim = 0.9)
        {
            while (!CmpColor(x, y, color, sim))
            {
                Dm.delay(200);
                if (Ct.IsCancellationRequested)
                    return;
            }
        }

        /// <summary>
        /// Yolo 解析全屏的对象
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public YoloResult<Detection>? YoloV8Det_All(YoloModels models)
        {
            Dm.GetScreenDataBmp(0, 0, 2000, 2000, out int data, out int size);
            byte[] genePic2 = new byte[size];
            nint ptr = new(data);
            for (int i = 0; i < size; i++)
                genePic2[i] = Marshal.ReadByte(ptr + 1 * i);
            return (YoloResult<Detection>?)XYoloV8.yoloV8(models, genePic2);
        }

        /// <summary>
        /// Yolo 解析全屏的对象 - 百鬼
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public YoloResult<Detection>? YoloDet_Baigui_All()
        {
            Dm.GetScreenDataBmp(0, 0, 2000, 2000, out int data, out int size);
            byte[] genePic2 = new byte[size];
            nint ptr = new(data);
            for (int i = 0; i < size; i++)
                genePic2[i] = Marshal.ReadByte(ptr + 1 * i);
            return (YoloResult<Detection>?)XYoloV8.yoloV8(YoloModels.DetectBg, genePic2);
        }

        /// <summary>
        /// 快速场景操作
        /// </summary>
        public class ScenceModel
        {
            public ScenceModel(Fast fast)
            {
                Dm = fast.Dm;
                Fast = fast;
            }

            private dmsoft Dm { get; }
            private Fast Fast { get; }

            /// <summary>
            /// 探索K28—获取突破卷
            /// 需要在探索场景内
            /// </summary>
            /// <returns></returns>
            public int K28_GetTuPoCount()
            {
                try
                {
                    var str = Fast.Ocr_Local(939, 13, 1016, 46);
                    if (str == "") return -1;

                    return int.Parse(str.Split('/')[0]);
                }
                catch (Exception e)
                {
                    XLogger.Warn("探索K28—获取突破卷失败");
                    XLogger.SaveException(e);
                    return -1;
                }
            }

            /// <summary>
            /// 探索—获取突破卷
            /// 需要在探索场景内
            /// </summary>
            /// <returns></returns>
            public int TanSuo_GetTuPoCount()
            {
                try
                {
                    var str = Fast.Ocr_Local(740, 14, 818, 44);
                    if (str == "") return -1;

                    return int.Parse(str.Split('/')[0]);
                }
                catch (Exception e)
                {
                    XLogger.Warn("探索—获取突破卷失败");
                    XLogger.SaveException(e);
                    return -1;
                }
            }

            /// <summary>
            /// 突破—获取突破卷
            /// 需要在突破场景内
            /// </summary>
            /// <returns></returns>
            public int TuPo_GetTuPoCount()
            {
                try
                {
                    var str = Fast.Ocr_Local(1142, 16, 1224, 47);
                    if (str == "") return -1;

                    return int.Parse(str.Split('/')[0]);
                }
                catch (Exception e)
                {
                    XLogger.Warn("突破—获取突破卷失败");
                    XLogger.SaveException(e);
                    return -1;
                }
            }
        }
    }
}