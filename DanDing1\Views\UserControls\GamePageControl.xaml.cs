﻿using ScriptEngine.Model;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Wpf.Ui.Controls;
using XHelper;
using DanDing1.Models;
using ScriptEngine.MuMu;
using ShareX.HelpersLib;
using System.IO;
using DanDing1.ViewModels.Base;

namespace DanDing1.Views.UserControls
{
    /// <summary>
    /// GamePageControl.xaml 的交互逻辑
    /// </summary>
    public partial class GamePageControl : UserControl
    {
        public WindowInfo SelectedWindow { get; private set; }

        // 缓存模拟器实例信息
        private List<SimulatorConfig> _cachedSimulators;

        private DateTime _lastCacheTime = DateTime.MinValue;
        private const int CACHE_TIMEOUT_SECONDS = 10;

        public GamePageControl()
        {
            InitializeComponent();

            OverNotice.IsChecked = XConfig.LoadValueFromFile<bool>("OverNotice");
        }

        private void Hyperlink_RequestNavigate(object sender, RequestNavigateEventArgs e)
        {
            // 使用默认浏览器打开链接
            Process.Start(new ProcessStartInfo(e.Uri.AbsoluteUri) { UseShellExecute = true });
            e.Handled = true;
        }

        private void OverNotice_Unchecked(object sender, RoutedEventArgs e)
        {
            var checkBox = sender as CheckBox;
            if (checkBox != null)
            {
                if (GlobalData.Instance.appConfig.IsFree)
                    checkBox.IsChecked = false;
                XConfig.SaveValueToFile("OverNotice", checkBox?.IsChecked ?? false);
            }
        }

        private List<string> GetPlan_List()
        {
            List<string> Plan = new();
            for (int i = 1; i < 5; i++)
            {
                var tasklist = XConfig.LoadValueFromFile<ObservableCollection<TaskConfigsModel.Configs>>("TaskList", $"游戏{i}");
                if (tasklist is not [] and not null)
                    Plan.Add($"游戏{i}|上次运行");
            }
            //读取用户自定义任务列表
            DirectoryInfo dir = new DirectoryInfo(".\\runtimes\\AppConfig\\UserTaskList\\");
            if (!dir.Exists)
                dir.Create();//不存在则创建文件夹
            foreach (var file in dir.GetFiles())
                Plan.Add(file.Name);

            return Plan;
        }

        private void TasksManager_MouseRightButtonUp(object sender, MouseButtonEventArgs e)
        {
            // 获取任务列表
            var lists = GetPlan_List();
            if (lists.Count == 0)
            {
                ContextMenu contextMenu_noTask = new ContextMenu();
                contextMenu_noTask.Items.Add(new Wpf.Ui.Controls.MenuItem()
                {
                    Header = "没有保存的任务列表,无法快速导入！",
                    Foreground = Brushes.Red
                });
                // 设置ContextMenu的显示位置为鼠标点击的位置
                contextMenu_noTask.Placement = PlacementMode.MousePoint;

                // 将ContextMenu与按钮关联
                ((Wpf.Ui.Controls.Button)sender).ContextMenu = contextMenu_noTask;

                // 显示ContextMenu
                contextMenu_noTask.IsOpen = true;
                return;
            }

            // 创建一个新的ContextMenu
            ContextMenu contextMenu = new ContextMenu();
            foreach (var taskname in lists)
            {
                Wpf.Ui.Controls.MenuItem menuItem = new();
                menuItem.Header = taskname;
                menuItem.Click += (s, e) =>
                {
                    if (s is not Wpf.Ui.Controls.MenuItem ss) return;

                    switch (GameModelName.Text)
                    {
                        case "游戏1":
                            GlobalData.Instance.Game1RunningConfig?.SetNowTaskPlan(ss.Header?.ToString() ?? "");
                            break;

                        case "游戏2":
                            GlobalData.Instance.Game2RunningConfig?.SetNowTaskPlan(ss.Header?.ToString() ?? "");
                            break;

                        case "游戏3":
                            GlobalData.Instance.Game3RunningConfig?.SetNowTaskPlan(ss.Header?.ToString() ?? "");
                            break;

                        case "游戏4":
                            GlobalData.Instance.Game4RunningConfig?.SetNowTaskPlan(ss.Header?.ToString() ?? "");
                            break;
                    }
                    ;
                }; // 为菜单项添加点击事件

                contextMenu.Items.Add(menuItem);
            }

            // 设置ContextMenu的显示位置为鼠标点击的位置
            contextMenu.Placement = PlacementMode.MousePoint;

            // 将ContextMenu与按钮关联
            ((Wpf.Ui.Controls.Button)sender).ContextMenu = contextMenu;

            // 显示ContextMenu
            contextMenu.IsOpen = true;
        }

        #region Simulator Menu Handling

        private async void AutoBindButton_Click(object sender, RoutedEventArgs e)
        {
            var contextMenu = CreateLoadingContextMenu();
            try
            {
                var path = XConfig.LoadValueFromFile<string>("MuMuPath");
                if (string.IsNullOrEmpty(path))
                {
                    ShowErrorMenuItem(contextMenu, "请在设置中设置MUMU模拟器路径");
                    return;
                }

                var simulators = await GetSimulatorsAsync(path);
                await PopulateSimulatorMenu(contextMenu, simulators);
            }
            catch (Exception ex)
            {
                ShowErrorMenuItem(contextMenu, $"加载失败: {ex.Message}");
            }
        }

        private ContextMenu CreateLoadingContextMenu()
        {
            var contextMenu = new ContextMenu();
            contextMenu.Items.Add(new Wpf.Ui.Controls.MenuItem
            {
                Header = "正在加载模拟器列表...",
                IsEnabled = false
            });
            contextMenu.Placement = PlacementMode.MousePoint;
            AutoBindButton.ContextMenu = contextMenu;
            contextMenu.IsOpen = true;
            return contextMenu;
        }

        private async Task<List<SimulatorConfig>> GetSimulatorsAsync(string path)
        {
            if (_cachedSimulators != null && (DateTime.Now - _lastCacheTime).TotalSeconds < CACHE_TIMEOUT_SECONDS)
            {
                return _cachedSimulators;
            }

            var simulators = await Task.Run(() =>
            {
                List<SimulatorConfig> result = [];
                MuMu mumu = new();
                if (mumu.Init(path))
                {
                    var datas = mumu._GetInstances();
                    if (datas?.Instances != null)
                    {
                        foreach (var data in datas.Instances)
                            result.Add(new SimulatorConfig().SetData(data.Value));
                    }
                }
                return result;
            });

            _cachedSimulators = simulators;
            _lastCacheTime = DateTime.Now;
            return simulators;
        }

        private async Task PopulateSimulatorMenu(ContextMenu contextMenu, List<SimulatorConfig> simulators)
        {
            contextMenu.Items.Clear();
            var runningSimulators = simulators.Where(s => s.IsRunning).ToList();

            if (runningSimulators.Count == 0)
            {
                ShowErrorMenuItem(contextMenu, "当前没有正在运行的模拟器");
                return;
            }

            if (runningSimulators.Count == 1)
            {
                // 如果只有一个模拟器，直接自动选择
                var simulator = runningSimulators[0];
                var hwnd = int.Parse(simulator.RenderWnd, NumberStyles.HexNumber);
                await HandleSimulatorSelection(hwnd, contextMenu);
                return;
            }

            // 多个模拟器时显示菜单
            foreach (var simulator in runningSimulators)
            {
                contextMenu.Items.Add(CreateSimulatorMenuItem(simulator, contextMenu));
            }
        }

        private Wpf.Ui.Controls.MenuItem CreateSimulatorMenuItem(SimulatorConfig simulator, ContextMenu contextMenu)
        {
            var menuItem = new Wpf.Ui.Controls.MenuItem
            {
                Header = $"模拟器 - {simulator.Name} (句柄: {simulator.RenderWnd})"
            };

            var hwnd = int.Parse(simulator.RenderWnd, NumberStyles.HexNumber);
            menuItem.Click += async (s, args) => await HandleSimulatorSelection(hwnd, contextMenu);
            return menuItem;
        }

        private async Task HandleSimulatorSelection(int hwnd, ContextMenu contextMenu)
        {
            try
            {
                await DisableMenuItems(contextMenu);
                var window = await Task.Run(() => new WindowInfo(hwnd));
                UpdateGameConfiguration(window, hwnd);
                contextMenu.IsOpen = false;
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"获取窗口信息失败: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                await EnableMenuItems(contextMenu);
            }
        }

        private void UpdateGameConfiguration(WindowInfo window, int hwnd)
        {
            var config = GetGameConfig();
            if (config == null) return;

            SelectedWindow = window;
            config.SelectHwnd = hwnd.ToString();
            config.SelectDpi = $"{window.ClientRectangle.Width}*{window.ClientRectangle.Height}";
            config.SendTipAboutHwnd();
            config.AddTaskControl.ViewModel.SelectHwnd = hwnd;
        }

        private GameViewBaseModel? GetGameConfig()
        {
            return GameModelName.Text switch
            {
                "游戏1" => GlobalData.Instance.Game1RunningConfig,
                "游戏2" => GlobalData.Instance.Game2RunningConfig,
                "游戏3" => GlobalData.Instance.Game3RunningConfig,
                "游戏4" => GlobalData.Instance.Game4RunningConfig,
                _ => null
            };
        }

        private void ShowErrorMenuItem(ContextMenu contextMenu, string message)
        {
            contextMenu.Items.Clear();
            contextMenu.Items.Add(new Wpf.Ui.Controls.MenuItem
            {
                Header = message,
                Foreground = Brushes.Red
            });
        }

        private async Task DisableMenuItems(ContextMenu contextMenu)
        {
            await Task.Run(() =>
            {
                Application.Current?.Dispatcher?.Invoke(() =>
                {
                    foreach (Wpf.Ui.Controls.MenuItem item in contextMenu.Items)
                    {
                        item.IsEnabled = false;
                    }
                });
            });
        }

        private async Task EnableMenuItems(ContextMenu contextMenu)
        {
            await Task.Run(() =>
            {
                Application.Current?.Dispatcher?.Invoke(() =>
                {
                    foreach (Wpf.Ui.Controls.MenuItem item in contextMenu.Items)
                    {
                        item.IsEnabled = true;
                    }
                });
            });
        }

        #endregion Simulator Menu Handling
    }
}