using DanDing1.Models;
using DanDing1.Services;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Windows.Input;
using System.Windows.Threading;
using XHelper;

namespace DanDing1.ViewModels.Windows
{
    /// <summary>
    /// 任务历史记录视图模型
    /// </summary>
    public partial class TaskHistoryViewModel : ObservableObject
    {
        #region 字段

        private readonly TaskHistoryService _historyService;
        private List<TaskHistoryRecord> _allRecords;
        private DispatcherTimer _autoRefreshTimer;
        private bool _autoRefreshEnabled = true;

        #endregion

        #region 属性

        /// <summary>
        /// 过滤后的历史记录
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<TaskHistoryRecord> _filteredRecords = new ObservableCollection<TaskHistoryRecord>();

        /// <summary>
        /// 选中的历史记录
        /// </summary>
        [ObservableProperty]
        private TaskHistoryRecord _selectedRecord;

        /// <summary>
        /// 开始日期
        /// </summary>
        [ObservableProperty]
        private DateTime _startDate = DateTime.Today.AddDays(-7);

        /// <summary>
        /// 结束日期
        /// </summary>
        [ObservableProperty]
        private DateTime _endDate = DateTime.Today;

        /// <summary>
        /// 选中的模拟器
        /// </summary>
        [ObservableProperty]
        private string _selectedEmulator = "全部";

        /// <summary>
        /// 选中的状态
        /// </summary>
        [ObservableProperty]
        private string _selectedStatus = "全部";

        /// <summary>
        /// 搜索文本
        /// </summary>
        [ObservableProperty]
        private string _searchText = string.Empty;

        /// <summary>
        /// 可用模拟器列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _availableEmulators = new ObservableCollection<string>();

        /// <summary>
        /// 可用状态列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _availableStatuses = new ObservableCollection<string>();

        /// <summary>
        /// 是否正在加载
        /// </summary>
        [ObservableProperty]
        private bool _isLoading = false;

        /// <summary>
        /// 总记录数
        /// </summary>
        [ObservableProperty]
        private int _totalRecords = 0;

        /// <summary>
        /// 成功记录数
        /// </summary>
        [ObservableProperty]
        private int _successRecords = 0;

        /// <summary>
        /// 失败记录数
        /// </summary>
        [ObservableProperty]
        private int _failureRecords = 0;

        /// <summary>
        /// 中断记录数
        /// </summary>
        [ObservableProperty]
        private int _cancelledRecords = 0;

        /// <summary>
        /// 执行中记录数
        /// </summary>
        [ObservableProperty]
        private int _runningRecords = 0;

        /// <summary>
        /// 成功率
        /// </summary>
        [ObservableProperty]
        private double _successRate = 0;

        /// <summary>
        /// 保留天数
        /// </summary>
        [ObservableProperty]
        private int _daysToKeep = 30;

        /// <summary>
        /// 统计信息
        /// </summary>
        [ObservableProperty]
        private Dictionary<string, object> _statistics = new Dictionary<string, object>();

        /// <summary>
        /// 自动刷新间隔（秒）
        /// </summary>
        [ObservableProperty]
        private int _refreshInterval = 30;

        /// <summary>
        /// 是否启用自动刷新
        /// </summary>
        [ObservableProperty]
        private bool _isAutoRefreshEnabled = true;

        /// <summary>
        /// 每页记录数
        /// </summary>
        [ObservableProperty]
        private int _pageSize = 50;

        /// <summary>
        /// 当前页码
        /// </summary>
        [ObservableProperty]
        private int _currentPage = 1;

        /// <summary>
        /// 总页数
        /// </summary>
        [ObservableProperty]
        private int _totalPages = 1;

        /// <summary>
        /// 是否可以前往下一页
        /// </summary>
        public bool CanGoToNextPage => CurrentPage < TotalPages;

        /// <summary>
        /// 当前页的记录
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<TaskHistoryRecord> _currentPageRecords = new ObservableCollection<TaskHistoryRecord>();

        partial void OnIsAutoRefreshEnabledChanged(bool value)
        {
            _autoRefreshEnabled = value;
            if (value)
            {
                StartAutoRefreshTimer();
            }
            else
            {
                StopAutoRefreshTimer();
            }
        }

        partial void OnRefreshIntervalChanged(int value)
        {
            // 更新自动刷新定时器的间隔
            if (_autoRefreshTimer != null && _autoRefreshEnabled)
            {
                StopAutoRefreshTimer();
                StartAutoRefreshTimer();
            }
        }

        partial void OnCurrentPageChanged(int value)
        {
            UpdatePagedRecords();
            OnPropertyChanged(nameof(CanGoToNextPage));
        }

        partial void OnPageSizeChanged(int value)
        {
            // 更新当前页为第一页
            CurrentPage = 1;
            UpdatePagedRecords();
            OnPropertyChanged(nameof(CanGoToNextPage));
        }

        #endregion

        #region 命令

        /// <summary>
        /// 加载历史记录命令
        /// </summary>
        [RelayCommand]
        private void LoadRecords()
        {
            try
            {
                IsLoading = true;

                // 加载指定日期范围的历史记录
                _allRecords = _historyService.LoadRecordsByDateRange(StartDate, EndDate);

                // 更新统计信息
                UpdateStatistics(_allRecords);

                // 应用过滤器
                ApplyFilters();

                // 确保通知CanGoToNextPage属性变更
                OnPropertyChanged(nameof(CanGoToNextPage));

                XLogger.Info($"已加载{StartDate:yyyy-MM-dd}至{EndDate:yyyy-MM-dd}的历史记录，共{_allRecords.Count}条");
            }
            catch (Exception ex)
            {
                XLogger.Error($"加载历史记录失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 导出到CSV命令
        /// </summary>
        [RelayCommand]
        private void ExportToCsv()
        {
            try
            {
                if (FilteredRecords.Count == 0)
                {
                    XLogger.Warn("没有记录可供导出");
                    return;
                }

                // 创建保存文件对话框
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV文件|*.csv",
                    Title = "导出历史记录",
                    FileName = $"任务历史记录_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // 导出记录
                    bool result = _historyService.ExportToCsv(FilteredRecords.ToList(), saveFileDialog.FileName);

                    if (result)
                    {
                        XLogger.Info($"成功导出{FilteredRecords.Count}条记录到{saveFileDialog.FileName}");
                        System.Windows.MessageBox.Show($"已成功导出{FilteredRecords.Count}条记录。", "导出成功",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                    }
                    else
                    {
                        XLogger.Error("导出记录失败");
                        System.Windows.MessageBox.Show("导出记录失败，请查看日志获取详细信息。", "导出失败",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"导出记录失败: {ex.Message}");
                System.Windows.MessageBox.Show($"导出记录失败: {ex.Message}", "导出失败",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 清理过期记录命令
        /// </summary>
        [RelayCommand]
        private void CleanupRecords()
        {
            try
            {
                // 确认对话框
                var result = System.Windows.MessageBox.Show(
                    $"确定要清理{DaysToKeep}天前的历史记录吗？此操作无法撤销。",
                    "确认清理",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Warning);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    IsLoading = true;

                    // 执行清理
                    int cleanedCount = _historyService.CleanupOldRecords(DaysToKeep);

                    // 重新加载记录
                    LoadRecords();

                    XLogger.Info($"已清理{cleanedCount}个过期历史记录文件");
                    System.Windows.MessageBox.Show($"已清理{cleanedCount}个过期历史记录文件。", "清理完成",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"清理过期记录失败: {ex.Message}");
                System.Windows.MessageBox.Show($"清理过期记录失败: {ex.Message}", "清理失败",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 应用过滤器命令
        /// </summary>
        [RelayCommand]
        private void ApplyFilters()
        {
            try
            {
                if (_allRecords == null)
                {
                    return;
                }

                // 应用过滤条件
                var query = _allRecords.AsQueryable();

                // 按模拟器过滤
                if (!string.IsNullOrEmpty(SelectedEmulator) && SelectedEmulator != "全部")
                {
                    query = query.Where(r => r.EmulatorName == SelectedEmulator);
                }

                // 按状态过滤
                if (!string.IsNullOrEmpty(SelectedStatus) && SelectedStatus != "全部")
                {
                    query = query.Where(r => r.Status == SelectedStatus);
                }

                // 按搜索文本过滤
                if (!string.IsNullOrEmpty(SearchText))
                {
                    string searchLower = SearchText.ToLower();
                    query = query.Where(r =>
                        r.TaskName.ToLower().Contains(searchLower) ||
                        r.EmulatorName.ToLower().Contains(searchLower) ||
                        (r.ErrorMessage != null && r.ErrorMessage.ToLower().Contains(searchLower)));
                }

                // 获取结果
                var filteredList = query.ToList();

                // 更新UI集合
                FilteredRecords.Clear();
                foreach (var record in filteredList)
                {
                    FilteredRecords.Add(record);
                }

                // 重置为第一页
                CurrentPage = 1;
                // 确保通知CanGoToNextPage属性变更
                OnPropertyChanged(nameof(CanGoToNextPage));

                // 更新分页
                UpdatePagedRecords();

                // 更新统计信息
                UpdateStatistics(filteredList);

                XLogger.Debug($"应用过滤器后，显示{FilteredRecords.Count}/{_allRecords.Count}条记录");
            }
            catch (Exception ex)
            {
                XLogger.Error($"应用过滤器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取统计信息命令
        /// </summary>
        [RelayCommand]
        private void RefreshStatistics()
        {
            try
            {
                IsLoading = true;

                // 获取最近7天的统计信息
                Statistics = _historyService.GetStatistics(7);

                XLogger.Debug("已刷新统计信息");
            }
            catch (Exception ex)
            {
                XLogger.Error($"刷新统计信息失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 下一页命令
        /// </summary>
        [RelayCommand]
        private void NextPage()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
            }
        }

        /// <summary>
        /// 上一页命令
        /// </summary>
        [RelayCommand]
        private void PreviousPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
            }
        }

        /// <summary>
        /// 跳转到第一页命令
        /// </summary>
        [RelayCommand]
        private void FirstPage()
        {
            CurrentPage = 1;
        }

        /// <summary>
        /// 跳转到最后一页命令
        /// </summary>
        [RelayCommand]
        private void LastPage()
        {
            CurrentPage = TotalPages;
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public TaskHistoryViewModel()
        {
            try
            {
                // 初始化历史记录服务
                _historyService = new TaskHistoryService();

                // 初始化可用状态列表
                AvailableStatuses.Add("全部");
                AvailableStatuses.Add("成功");
                AvailableStatuses.Add("失败");
                AvailableStatuses.Add("中断");
                AvailableStatuses.Add("执行中");

                // 初始化可用模拟器列表（将在Initialize方法中更新）
                AvailableEmulators.Add("全部");

                // 启动自动刷新定时器
                StartAutoRefreshTimer();

                XLogger.Debug("任务历史记录视图模型已初始化");
            }
            catch (Exception ex)
            {
                XLogger.Error($"初始化任务历史记录视图模型失败: {ex.Message}");
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 初始化视图模型
        /// </summary>
        /// <param name="emulatorNames">可用的模拟器名称列表</param>
        public void Initialize(List<string> emulatorNames)
        {
            try
            {
                // 更新可用模拟器列表
                AvailableEmulators.Clear();
                AvailableEmulators.Add("全部");

                foreach (var name in emulatorNames)
                {
                    AvailableEmulators.Add(name);
                }

                // 加载最近7天的历史记录
                LoadRecords();

                // 获取统计信息
                RefreshStatistics();

                XLogger.Debug("任务历史记录视图模型已初始化完成");
            }
            catch (Exception ex)
            {
                XLogger.Error($"初始化任务历史记录视图模型失败: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 启动自动刷新定时器
        /// </summary>
        private void StartAutoRefreshTimer()
        {
            if (!_autoRefreshEnabled)
                return;

            if (_autoRefreshTimer == null)
            {
                _autoRefreshTimer = new DispatcherTimer();
                _autoRefreshTimer.Tick += AutoRefreshTimer_Tick;
            }

            _autoRefreshTimer.Interval = TimeSpan.FromSeconds(RefreshInterval);
            _autoRefreshTimer.Start();

            XLogger.Debug($"已启动自动刷新，间隔：{RefreshInterval}秒");
        }

        /// <summary>
        /// 停止自动刷新定时器
        /// </summary>
        private void StopAutoRefreshTimer()
        {
            if (_autoRefreshTimer != null)
            {
                _autoRefreshTimer.Stop();
                XLogger.Debug("已停止自动刷新");
            }
        }

        /// <summary>
        /// 自动刷新定时器触发事件
        /// </summary>
        private void AutoRefreshTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                // 如果当前正在加载，跳过本次刷新
                if (IsLoading)
                    return;

                // 记录当前选中的记录ID
                string selectedRecordId = SelectedRecord?.Id;

                // 加载历史记录
                LoadRecords();

                // 尝试恢复选中状态
                if (!string.IsNullOrEmpty(selectedRecordId))
                {
                    SelectedRecord = FilteredRecords.FirstOrDefault(r => r.Id == selectedRecordId);
                }

                XLogger.Debug("自动刷新历史记录完成");
            }
            catch (Exception ex)
            {
                XLogger.Error($"自动刷新历史记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics(List<TaskHistoryRecord> records)
        {
            TotalRecords = records.Count;
            SuccessRecords = records.Count(r => r.Status == "成功");
            FailureRecords = records.Count(r => r.Status == "失败");
            CancelledRecords = records.Count(r => r.Status == "中断");
            RunningRecords = records.Count(r => r.Status == "执行中");

            // 计算成功率
            SuccessRate = TotalRecords > 0
                ? Math.Round((double)SuccessRecords / TotalRecords * 100, 2)
                : 0;
        }

        /// <summary>
        /// 更新分页记录
        /// </summary>
        private void UpdatePagedRecords()
        {
            try
            {
                // 计算总页数
                TotalPages = (int)Math.Ceiling((double)FilteredRecords.Count / PageSize);
                // 添加CanGoToNextPage属性变更通知
                OnPropertyChanged(nameof(CanGoToNextPage));

                // 确保当前页有效
                if (CurrentPage < 1)
                    CurrentPage = 1;
                if (CurrentPage > TotalPages && TotalPages > 0)
                    CurrentPage = TotalPages;

                // 获取当前页的记录
                int startIndex = (CurrentPage - 1) * PageSize;
                int count = Math.Min(PageSize, FilteredRecords.Count - startIndex);

                // 如果没有记录，清空当前页
                if (FilteredRecords.Count == 0 || count <= 0)
                {
                    CurrentPageRecords.Clear();
                    return;
                }

                // 更新当前页记录
                CurrentPageRecords.Clear();
                for (int i = 0; i < count; i++)
                {
                    if (startIndex + i < FilteredRecords.Count)
                    {
                        CurrentPageRecords.Add(FilteredRecords[startIndex + i]);
                    }
                }

                XLogger.Debug($"已更新分页，当前第{CurrentPage}/{TotalPages}页，每页{PageSize}条，共{FilteredRecords.Count}条记录");
            }
            catch (Exception ex)
            {
                XLogger.Error($"更新分页记录失败: {ex.Message}");
            }
        }

        #endregion
    }
}