﻿using Newtonsoft.Json;
using System.Text.RegularExpressions;

namespace ScriptEngine.MuMu
{
    /// <summary>
    /// mumu模拟器单信息
    /// </summary>
    public class MuMuInstance
    {
        /// <summary>
        /// adb 域名，只有启动才会有
        /// </summary>
        [JsonProperty("adb_host_ip")]
        public string AdbHostIp { get; set; }

        /// <summary>
        /// adb端口，只有启动才会有
        /// </summary>
        [JsonProperty("adb_port")]
        public int? AdbPort { get; set; }

        /// <summary>
        /// 模拟器创建时间戳
        /// </summary>
        [JsonProperty("created_timestamp")]
        public long CreatedTimestamp { get; set; }

        /// <summary>
        /// 模拟器磁盘占用大小，以字节为单位
        /// </summary>
        [JsonProperty("disk_size_bytes")]
        public long DiskSizeBytes { get; set; }

        /// <summary>
        /// 模拟器列表错误码
        /// </summary>
        [JsonProperty("error_code")]
        public int ErrorCode { get; set; }

        /// <summary>
        /// 虚拟机进程PID，只有启动才会有
        /// </summary>
        [JsonProperty("headless_pid")]
        public int? HeadlessPid { get; set; }

        /// <summary>
        /// HyperV是否开启
        /// </summary>
        [JsonProperty("hyperv_enabled")]
        public bool HypervEnabled { get; set; }

        /// <summary>
        /// 模拟器索引
        /// </summary>
        [JsonProperty("index")]
        public string Index { get; set; }

        /// <summary>
        /// 是否安卓启动成功
        /// </summary>
        [JsonProperty("is_android_started")]
        public bool IsAndroidStarted { get; set; }

        /// <summary>
        /// 是否是主模拟器
        /// </summary>
        [JsonProperty("is_main")]
        public bool IsMain { get; set; }

        /// <summary>
        /// 是否进程启动
        /// </summary>
        [JsonProperty("is_process_started")]
        public bool IsProcessStarted { get; set; }

        /// <summary>
        /// 启动错误码，只有启动才会有
        /// </summary>
        [JsonProperty("launch_err_code")]
        public int? LaunchErrCode { get; set; }

        /// <summary>
        /// 启动错误描述，只有启动才会有
        /// </summary>
        [JsonProperty("launch_err_msg")]
        public string LaunchErrMsg { get; set; }

        /// <summary>
        /// 启动耗时（毫秒），只有启动才会有
        /// </summary>
        [JsonProperty("launch_time")]
        public int? LaunchTime { get; set; }

        /// <summary>
        /// 主窗口句柄，只有启动才会有
        /// </summary>
        [JsonProperty("main_wnd")]
        public string MainWnd { get; set; }

        /// <summary>
        /// 模拟器名称
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// 模拟器外壳进程PID，只有启动才会有
        /// </summary>
        [JsonProperty("pid")]
        public int? Pid { get; set; }

        /// <summary>
        /// 模拟器外壳启动阶段状态，只有启动才会有
        /// </summary>
        [JsonProperty("player_state")]
        public string PlayerState { get; set; }

        /// <summary>
        /// 渲染窗口句柄，只有启动才会有
        /// </summary>
        [JsonProperty("render_wnd")]
        public string RenderWnd { get; set; }

        /// <summary>
        /// 是否开启VT虚拟化，只有启动才会有
        /// </summary>
        [JsonProperty("vt_enabled")]
        public bool? VtEnabled { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }

    /// <summary>
    /// mumu模拟器信息类
    /// </summary>
    public class MuMuInstances
    {
        public Dictionary<string, MuMuInstance> Instances { get; set; } = new Dictionary<string, MuMuInstance>();

        /// <summary>
        /// 预处理JSON字符串，修复可能的未终止字符串问题
        /// </summary>
        /// <param name="json">原始JSON字符串</param>
        /// <returns>修复后的JSON字符串</returns>
        private static string PreprocessJson(string json)
        {
            if (string.IsNullOrEmpty(json))
                return json;

            try
            {
                // 尝试修复"disk_size_bytes"属性附近可能存在的未终止字符串问题
                // 使用正则表达式查找可能存在问题的字符串片段
                string pattern = @"(""disk_size_bytes""\s*:\s*"")([^""]*?)(\s*,|\s*})";
                if (Regex.IsMatch(json, pattern))
                {
                    // 修复未正确闭合的双引号
                    json = Regex.Replace(json, pattern, "$1$2\"$3");
                }

                return json;
            }
            catch (Exception ex)
            {
                XHelper.XLogger.Debug($"预处理JSON失败: {ex.Message}");
                return json; // 如果预处理失败，返回原始字符串
            }
        }

        public static MuMuInstances FromJson(string json)
        {
            if (string.IsNullOrEmpty(json))
            {
                XHelper.XLogger.Debug("解析MuMu实例信息失败: 输入JSON为空");
                return new MuMuInstances(); // 返回空对象而不是null
            }

            // 对JSON进行预处理，修复可能的语法问题
            json = PreprocessJson(json);

            try
            {
                // 首先检查JSON是否为单个对象（非数组/字典）
                if (json.Trim().StartsWith("{") && json.Trim().EndsWith("}") && !json.Contains("\"0\":") && !json.Contains("\"1\":"))
                {
                    // 尝试直接解析为单一实例
                    var instance = JsonConvert.DeserializeObject<MuMuInstance>(json);
                    if (instance != null)
                    {
                        var result = new MuMuInstances();
                        result.Instances = new Dictionary<string, MuMuInstance>();

                        // 检查索引是否为空或有效
                        if (string.IsNullOrEmpty(instance.Index))
                        {
                            instance.Index = "0"; // 设置默认索引为0
                        }

                        result.Instances[instance.Index] = instance;
                        return result;
                    }
                }

                // 如果不是单个对象或单个对象解析失败，尝试解析为字典格式
                var instances = JsonConvert.DeserializeObject<Dictionary<string, MuMuInstance>>(json);
                if (instances == null || instances.Count == 0)
                {
                    return new MuMuInstances();
                }

                // 确保每个实例都有有效的索引
                foreach (var key in instances.Keys.ToList())
                {
                    var instance = instances[key];
                    if (string.IsNullOrEmpty(instance.Index))
                    {
                        instance.Index = key;
                    }
                }

                return new MuMuInstances { Instances = instances };
            }
            catch (Exception ex)
            {
                XHelper.XLogger.Debug($"解析MuMu实例信息失败: {ex.Message}，尝试替代解析方法");

                try
                {
                    // 如果标准解析失败，尝试作为单一实例解析
                    var instance = JsonConvert.DeserializeObject<MuMuInstance>(json);
                    if (instance != null)
                    {
                        var result = new MuMuInstances();
                        result.Instances = new Dictionary<string, MuMuInstance>();

                        // 检查索引是否为空
                        if (string.IsNullOrEmpty(instance.Index))
                        {
                            instance.Index = "0"; // 设置默认索引
                        }

                        result.Instances[instance.Index] = instance;
                        return result;
                    }
                }
                catch (Exception innerEx)
                {
                    XHelper.XLogger.Debug($"替代解析方法也失败: {innerEx.Message}");
                }

                return new MuMuInstances(); // 返回空对象而不是null
            }
        }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
}