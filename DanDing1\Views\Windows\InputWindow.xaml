﻿<Window x:Class="DanDing1.Views.Windows.InputWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
        ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Title="InputWindow" Height="200" Width="400" WindowStartupLocation="CenterScreen">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>

        <!-- 提示文本 -->
        <TextBlock Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="3"
                   Text="请输入新的 值：" FontSize="14" VerticalAlignment="Center" />

        <!-- 输入框 -->
        <TextBox x:Name="InputTextBox" Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="3"
                 Margin="0,10,0,10" FontSize="14" VerticalAlignment="Center"
                 KeyDown="InputTextBox_KeyDown" />

        <!-- 确认按钮 -->
        <Button Content="确认" Grid.Row="2" Grid.Column="1" Width="80" Margin="5" Click="ConfirmButton_Click" />

        <!-- 取消按钮 -->
        <Button Content="取消" Grid.Row="2" Grid.Column="2" Width="80" Margin="5" Click="CancelButton_Click" />
    </Grid>
</Window>