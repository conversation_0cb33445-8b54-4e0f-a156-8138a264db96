﻿using DanDing1.Helpers;
using DanDing1.Models;
using DanDing1.ViewModels.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Wpf.Ui.Controls;
using XHelper;
using System.Text.Json;
using DanDing1.Helpers.Extensions;

namespace DanDing1.Views.UserControls
{
    /// <summary>
    /// AddTaskControl.xaml 的交互逻辑
    /// </summary>
    public partial class AddTaskControl : UserControl
    {
        private AddTaskPropertyViewModel _viewModel;

        /// <summary>
        /// 声明一个任务变更事件
        /// </summary>
        public event EventHandler TasksChanged;

        internal AddTaskPropertyViewModel ViewModel
        {
            get => _viewModel;
            set
            {
                if (_viewModel != null)
                {
                    // 取消旧ViewModel的事件订阅
                    _viewModel.PropertyChanged -= ViewModel_PropertyChanged;
                }

                _viewModel = value;
                DataContext = _viewModel; // 更新 DataContext，确保 UI 重新绑定

                if (_viewModel != null)
                {
                    // 订阅新ViewModel的属性变更事件
                    _viewModel.PropertyChanged += ViewModel_PropertyChanged;
                }
            }
        }

        private void ViewModel_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // 当GameTaskLists属性变更时触发TasksChanged事件
            if (e.PropertyName == nameof(AddTaskPropertyViewModel.GameTaskLists))
            {
                TasksChanged?.Invoke(this, EventArgs.Empty);
            }
        }

        /// <summary>
        /// 切换到超级多开模式
        /// </summary>
        public void SetSuperMode()
        {
            ViewModel.SetMode("超级多开", () =>
            {
                NomalMode1.Margin = new Thickness(5, 5, 5, 5);
                NomalMode2.Width = 640;
            });
        }

        /// <summary>
        /// 切换到定时调度模式
        /// </summary>
        public void SetSchedulerMode()
        {
            ViewModel.SetMode("超级多开", () =>
            {
                //NomalMode1.Margin = new Thickness(5, 5, 5, 5);
                //NomalMode2.Width = 640;
            });
        }

        public AddTaskControl()
        {
            InitializeComponent();
            ViewModel = new AddTaskPropertyViewModel();
            InitData();
            ViewModel.SetMode("普通", () =>
            {
                NomalMode1.Margin = new Thickness(0, 0, 0, 0);
                NomalMode2.Width = 692;
            });

            Yhun_ZuDui_SelectButton.IsEnabled = false;
            Yhun_ZuDui.Checked += Yhun_ZuDui_Checked;
            Yhun_ZuDui.Unchecked += Yhun_ZuDui_Unchecked;
            Yhun_ZuDui_Location.SelectionChanged += Yhun_ZuDui_Location_SelectionChanged;

            TSuo_ZuDui_SelectButton.IsEnabled = false;
            TSuo_ZuDui.Checked += TSuo_ZuDui_Checked;
            TSuo_ZuDui.Unchecked += TSuo_ZuDui_Unchecked;
            TSuo_ZuDui_Location.SelectionChanged += TSuo_ZuDui_Location_SelectionChanged;
        }

        private void TSuo_ZuDui_Location_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ComboBox box = sender as ComboBox;
            if (box != null)
            {
                if (box.SelectedItem == "队长")
                    TSuo_ZuDui_SelectButton.Visibility = Visibility.Visible;
                else
                    TSuo_ZuDui_SelectButton.Visibility = Visibility.Collapsed;
            }
        }

        private void TSuo_ZuDui_Unchecked(object sender, RoutedEventArgs e)
        {
            TSuo_ZuDui_SelectButton.IsEnabled = false;
            TSuo_ZuDui_SelectButton.Visibility = Visibility.Collapsed;
        }

        private void TSuo_ZuDui_Checked(object sender, RoutedEventArgs e)
        {
            TSuo_ZuDui_SelectButton.IsEnabled = true;
            TSuo_ZuDui_SelectButton.Visibility = Visibility.Visible;
        }

        private void Yhun_ZuDui_Location_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ComboBox box = sender as ComboBox;
            if (box != null)
            {
                if (box.SelectedItem == "队长")
                    Yhun_ZuDui_SelectButton.Visibility = Visibility.Visible;
                else
                    Yhun_ZuDui_SelectButton.Visibility = Visibility.Collapsed;
            }
        }

        private void Yhun_ZuDui_Unchecked(object sender, RoutedEventArgs e)
        {
            Yhun_ZuDui_SelectButton.IsEnabled = false;
        }

        private void Yhun_ZuDui_Checked(object sender, RoutedEventArgs e)
        {
            Yhun_ZuDui_SelectButton.IsEnabled = true;
        }

        private void ScrollViewer_PreviewMouseWheel(object sender, MouseWheelEventArgs e)
        {
            var scrollViewer = sender as ScrollViewer;
            if (scrollViewer != null)
            {
                // 如果鼠标滚轮向上，则向右滚动，反之向左滚动
                if (e.Delta > 0)
                {
                    scrollViewer.LineLeft(); // 水平向左滚动一行
                    scrollViewer.LineLeft(); // 水平向左滚动一行
                }
                else
                {
                    scrollViewer.LineRight(); // 水平向右滚动一行
                    scrollViewer.LineRight(); // 水平向右滚动一行
                }
                e.Handled = true; // 表示事件已处理，防止纵向滚动
            }
        }

        private async void ToggleSwitch_Checked(object sender, RoutedEventArgs e)
        {
            InfoBar.Title = "提示";
            InfoBar.Message = "开关的标记功能是默认标记从左向右第五个（右1）位置！";
            InfoBar.IsOpen = true;
            InfoBar.Severity = InfoBarSeverity.Success;
            await Task.Delay(2000);
            InfoBar.Title = "";
            InfoBar.Message = "";
            InfoBar.IsOpen = false;
        }

        private void TasksManager_MouseRightButtonUp(object sender, MouseButtonEventArgs e)
        {
        }

        private void InitData()
        {
            ViewModel.Yhun_Level ??= ViewModel.Yhun_LevelLists[0];
            ViewModel.Yling_Class ??= ViewModel.Yling_ClassLists[0];
            ViewModel.Tsuo_CombatStr ??= ViewModel.Tsuo_CombatStrLists[0];
            ViewModel.Jxing_Class ??= ViewModel.Jxing_ClassLists[0];
            ViewModel.Yhun_ZuDui_Location ??= ViewModel.Yhun_ZuDui_Locations[0];
            ViewModel.TSuo_ZuDui_Location ??= ViewModel.TSuo_ZuDui_Locations[0];
            ViewModel.Dji_Biaoji ??= ViewModel.Dji_Biaojis[0];
            ViewModel.Tpo_Biaoji ??= ViewModel.Tpo_Biaojis[0];
            ViewModel.Qiling_TaskType ??= ViewModel.Qiling_TaskTypeLists[0];
            ViewModel.Game_Scene ??= ViewModel.Game_SceneLists[1];
            ViewModel.Yhun_YuShe ??= "不换预设";
            ViewModel.Yjie_YuShe ??= "不换预设";
            ViewModel.Yjie_Class ??= ViewModel.Yjie_ClassLists[0];
            ReloadResources_Click(null, null);
        }

        /// <summary>
        /// 刷新任务列表控件
        /// </summary>
        public void RefreshTaskList()
        {
            InitData();
            // 刷新GameTaskLists控件数据
            if (GameTaskLists != null)
            {
                // 临时保存选中项索引
                int selectedIndex = GameTaskLists.SelectedIndex;

                // 重新设置ItemsSource触发UI更新
                GameTaskLists.ItemsSource = null;
                GameTaskLists.ItemsSource = ViewModel.GameTaskLists;

                // 恢复选中项
                if (selectedIndex >= 0 && selectedIndex < ViewModel.GameTaskLists.Count)
                {
                    GameTaskLists.SelectedIndex = selectedIndex;
                }
            }

            // 强制更新控件布局
            UpdateLayout();

            // 触发任务变更事件
            TasksChanged?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 预设刷新 - 下拉框打开时触发
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void YuShe_YuHun_DropDownOpened(object sender, EventArgs e)
        {
            if (sender is ComboBox comboBox)
            {
                string viewModelProperty = GetViewModelPropertyForComboBox(comboBox.Name);
                if (!string.IsNullOrEmpty(viewModelProperty))
                {
                    _ = RefreshPresetListAsync(comboBox, viewModelProperty);
                }
            }
        }

        /// <summary>
        /// 预设下拉框加载完成时确保默认值设置正确
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void YuShe_YuHun_Loaded(object sender, RoutedEventArgs e)
        {
            if (sender is ComboBox comboBox)
            {
                string viewModelProperty = GetViewModelPropertyForComboBox(comboBox.Name);
                if (string.IsNullOrEmpty(viewModelProperty))
                {
                    return;
                }

                // 确保默认预设值设置正确
                if (comboBox.SelectedItem == null)
                {
                    SetViewModelProperty(viewModelProperty, "不换预设");
                    comboBox.SelectedItem = "不换预设";

                    // 延迟异步刷新预设列表，避免与初始化冲突
                    Dispatcher.InvokeAsync(() => RefreshPresetListAsync(comboBox, viewModelProperty),
                        System.Windows.Threading.DispatcherPriority.Background);
                }
            }
        }

        /// <summary>
        /// 预设刷新 - 鼠标点击触发（保留以兼容现有代码）
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void YuShe_YuHun_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is ComboBox comboBox)
            {
                string viewModelProperty = GetViewModelPropertyForComboBox(comboBox.Name);
                if (!string.IsNullOrEmpty(viewModelProperty))
                {
                    _ = RefreshPresetListAsync(comboBox, viewModelProperty);
                }
            }
        }

        /// <summary>
        /// 根据ComboBox名称获取对应的ViewModel属性名
        /// </summary>
        /// <param name="comboBoxName">ComboBox的名称</param>
        /// <returns>对应的ViewModel属性名，找不到则返回null</returns>
        private string GetViewModelPropertyForComboBox(string comboBoxName)
        {
            // 映射ComboBox名称到ViewModel属性
            switch (comboBoxName)
            {
                case "YuShe_YuHun":
                    return "Yhun_YuShe";

                case "YuShe_Yling":
                    return "Yling_YuShe";

                case "YuShe_Liudao":
                    return "Liudao_YuShe";

                case "YuShe_Qiling":
                    return "Qiling_YuShe";

                case "YuShe_HDong":
                    return "HDong_YuShe";

                case "YuShe_Qta":
                    return "Qta_YuShe";

                case "YuShe_Dji":
                    return "Dji_YuShe";

                case "YuShe_Tpo":
                    return "Tpo_YuShe";

                case "YuShe_TSuo":
                    return "TSuo_YuShe";

                case "YingJie_Yling":
                    return "Yjie_YuShe";

                case "YuShe_RiChang":
                    return "RChang_YuShe";

                default:
                    return null;
            }
        }

        /// <summary>
        /// 根据ComboBox名称获取对应的场景类型
        /// </summary>
        /// <param name="comboBoxName">ComboBox的名称</param>
        /// <returns>对应的场景类型，找不到则返回空字符串</returns>
        private string GetSceneTypeForComboBox(string comboBoxName)
        {
            switch (comboBoxName)
            {
                case "YuShe_YuHun":
                    return "御魂";

                case "YuShe_Yling":
                    return "御灵";

                case "YuShe_Liudao":
                    return "六道";

                case "YuShe_Qiling":
                    return "契灵";

                case "YuShe_HDong":
                    return "活动";

                case "YuShe_Qta":
                    return "日常";

                case "YuShe_Dji":
                    return "斗技";

                case "YuShe_Tpo":
                    return "突破";

                case "YuShe_TSuo":
                    return "探索";

                case "YingJie_Yling":
                    return "英杰";

                case "YuShe_RiChang":
                    return "日常";

                default:
                    return "";
            }
        }

        /// <summary>
        /// 刷新预设列表
        /// </summary>
        private void RefreshPresetList()
        {
            // 保留旧方法以保持兼容性，调用异步版本
            _ = RefreshPresetListAsync();
        }

        /// <summary>
        /// 异步刷新预设列表
        /// </summary>
        private async Task RefreshPresetListAsync()
        {
            await RefreshPresetListAsync(YuShe_YuHun, "Yhun_YuShe");
        }

        /// <summary>
        /// 异步刷新预设列表 - 通用版本
        /// </summary>
        /// <param name="comboBox">要刷新的预设ComboBox</param>
        /// <param name="viewModelProperty">对应ViewModel的属性名</param>
        private async Task RefreshPresetListAsync(ComboBox comboBox, string viewModelProperty)
        {
            try
            {
                // 保存当前选中的项（如果有）
                object selectedItem = comboBox.SelectedItem;

                // 设置加载状态指示
                comboBox.IsEnabled = false;
                if (InfoBar != null)
                {
                    InfoBar.Title = "加载中";
                    InfoBar.Message = "正在加载预设配置...";
                    InfoBar.IsOpen = true;
                    InfoBar.Severity = InfoBarSeverity.Informational;
                }

                // 获取当前场景类型
                string sceneType = GetSceneTypeForComboBox(comboBox.Name);

                // 异步加载预设列表
                var defaultItems = new List<string> { "不换预设" };

                // 在后台线程中获取配置名称并按场景过滤
                var filteredConfigNames = await Task.Run(() =>
                {
                    try
                    {
                        var presetManager = new PresetConfigManager();
                        presetManager.ReloadConfigs();

                        // 如果没有指定场景类型，返回所有预设
                        if (string.IsNullOrEmpty(sceneType))
                        {
                            return presetManager.GetAllConfigNames().ToList();
                        }

                        // 过滤符合当前场景类型的预设
                        return presetManager.Configs
                            .Where(config =>
                                config.AllScenes || // 应用于所有场景
                                ( // 或者应用于特定场景
                                    sceneType == "御魂" && config.Yuhun ||
                                    sceneType == "突破" && config.Tupo ||
                                    sceneType == "探索" && config.Tansuo ||
                                    sceneType == "御灵" && config.Yuling ||
                                    sceneType == "斗技" && config.Douji ||
                                    sceneType == "英杰" && config.Yingxiong ||
                                    sceneType == "六道" && config.Liudao ||
                                    sceneType == "契灵" && config.Qiling ||
                                    sceneType == "日常" && config.Richang ||
                                    sceneType == "活动" // 活动默认接受所有预设
                                )
                            )
                            .Select(config => config.Name)
                            .ToList();
                    }
                    catch (Exception ex)
                    {
                        // 记录异常，但返回空列表以避免UI崩溃
                        Console.WriteLine($"加载预设配置时出错: {ex.Message}");
                        return new List<string>();
                    }
                });

                // 在UI线程更新控件
                await Dispatcher.InvokeAsync(() =>
                {
                    // 创建完整的项列表
                    var allItems = new List<string>(defaultItems);
                    allItems.AddRange(filteredConfigNames);

                    // 更新ComboBox数据源
                    comboBox.ItemsSource = null;
                    comboBox.ItemsSource = allItems; // 使用过滤后的项列表作为数据源

                    // 尝试恢复先前选中的项
                    if (selectedItem != null && allItems.Contains(selectedItem))
                    {
                        comboBox.SelectedItem = selectedItem;
                    }
                    else if (allItems.Count > 0)
                    {
                        // 确保默认值设置正确
                        comboBox.SelectedItem = "不换预设";

                        // 设置对应的ViewModel属性
                        SetViewModelProperty(viewModelProperty, "不换预设");
                    }

                    // 重置加载状态
                    comboBox.IsEnabled = true;

                    if (InfoBar != null)
                    {
                        InfoBar.IsOpen = false;
                    }
                });
            }
            catch (Exception ex)
            {
                // 处理并显示错误
                if (InfoBar != null)
                {
                    InfoBar.Title = "错误";
                    InfoBar.Message = $"加载预设配置失败: {ex.Message}";
                    InfoBar.IsOpen = true;
                    InfoBar.Severity = InfoBarSeverity.Error;

                    // 3秒后自动关闭错误提示
                    _ = Task.Delay(3000).ContinueWith(_ =>
                    {
                        Dispatcher.Invoke(() =>
                        {
                            InfoBar.IsOpen = false;
                        });
                    });
                }

                // 确保UI控件恢复可用状态
                comboBox.IsEnabled = true;
            }
        }

        /// <summary>
        /// 设置ViewModel的属性值
        /// </summary>
        /// <param name="propertyName">属性名</param>
        /// <param name="value">属性值</param>
        private void SetViewModelProperty(string propertyName, object value)
        {
            // 使用反射设置ViewModel属性
            var property = ViewModel.GetType().GetProperty(propertyName);
            if (property != null && property.CanWrite)
            {
                property.SetValue(ViewModel, value);
            }
        }

        private void OnlyNextTip_MouseDown(object sender, MouseButtonEventArgs e)
        {
            OnlyNext.IsChecked = !OnlyNext.IsChecked;
        }

        /// <summary>
        /// 打开资源目录
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void OpenResources_Click(object sender, RoutedEventArgs e)
        {
            Utils.OpenFolder(AppDomain.CurrentDomain.BaseDirectory + "Resources");
        }

        /// <summary>
        /// 重载资源图片
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ReloadResources_Click(object sender, RoutedEventArgs e)
        {
            string dir = AppDomain.CurrentDomain.BaseDirectory + "Resources";
            // 检查目录是否存在，不存在则创建
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);

            try
            {
                // 检查文件夹是否存在
                if (Directory.Exists(dir))
                {
                    // 获取所有.bmp文件数量
                    int bmpCount = Directory.GetFiles(dir, "*.bmp", SearchOption.AllDirectories).Length;

                    // 更新TextBlock显示
                    ResourcesTip.Text = $"当前用户自己的图片数量：{bmpCount}张";

                    //// 显示成功提示
                    //InfoBar.Title = "成功";
                    //InfoBar.Message = $"已重新加载资源图片，共找到{bmpCount}张图片";
                    //InfoBar.IsOpen = true;
                    //InfoBar.Severity = InfoBarSeverity.Success;

                    //// 3秒后自动关闭提示
                    //_ = Task.Delay(3000).ContinueWith(_ =>
                    //{
                    //    Dispatcher.Invoke(() =>
                    //    {
                    //        InfoBar.IsOpen = false;
                    //    });
                    //});
                }
                else
                {
                    // 文件夹不存在时的处理
                    ResourcesTip.Text = "当前用户自己的图片数量：0张";

                    InfoBar.Title = "警告";
                    InfoBar.Message = "资源目录不存在，请先创建Resources文件夹";
                    InfoBar.IsOpen = true;
                    InfoBar.Severity = InfoBarSeverity.Warning;
                }
            }
            catch (Exception ex)
            {
                // 异常处理
                InfoBar.Title = "错误";
                InfoBar.Message = $"加载资源图片失败: {ex.Message}";
                InfoBar.IsOpen = true;
                InfoBar.Severity = InfoBarSeverity.Error;
            }
        }

        /// <summary>
        /// 获取当前选中的任务类型
        /// </summary>
        /// <returns>任务类型名称</returns>
        public string GetSelectedTaskType()
        {
            return ViewModel.GameTaskLists.GetScheduledTaskName();
        }
    }
}