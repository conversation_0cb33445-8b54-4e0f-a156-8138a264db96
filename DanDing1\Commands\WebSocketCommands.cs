using System;
using System.Threading.Tasks;
using XHelper;
using DanDing1.Services;
using System.Text;
using System.Collections.Generic;
using DanDing1.Models.Ws_Models;
using Microsoft.Extensions.DependencyInjection;

namespace DanDing1.Commands
{
    /// <summary>
    /// WebSocket测试命令处理类
    /// 支持连接、断开、查询状态、恢复功能、主动上报等操作
    /// </summary>
    internal class WebSocketCommands : BaseCommand
    {
        // 用于缓存服务实例
        private static ScriptStatusReporterService? _scriptStatusReporterService;
        private static WebSocketLogForwarderService? _logForwarderService;

        // 支持的子命令
        private enum SubCommand
        {
            Connect,
            Disconnect,
            Status,
            Enable,
            Report,
            Help
        }

        public override void Execute(string[] parameters)
        {
            if (!ValidateParameterCount(parameters, 3))
            {
                PrintHelp();
                return;
            }

            // 解析子命令
            string subCommandStr = parameters[2].ToLower();

            switch (subCommandStr)
            {
                case "connect":
                    HandleConnect(parameters);
                    break;
                case "disconnect":
                    HandleDisconnect();
                    break;
                case "status":
                    HandleStatus();
                    break;
                case "enable":
                    HandleEnable();
                    break;
                case "report":
                    HandleReport(parameters);
                    break;
                case "help":
                case "?":
                    PrintHelp();
                    break;
                default:
                    XLogger.Error($"未知的WebSocket子命令: {subCommandStr}");
                    PrintHelp();
                    break;
            }
        }

        /// <summary>
        /// 处理连接命令
        /// </summary>
        private void HandleConnect(string[] parameters)
        {
            string url;
            string token = string.Empty;

            // 检查是否提供了URL参数
            if (parameters.Length < 4)
            {
                // 未提供URL参数，尝试使用本地默认值
                XLogger.Info("未提供WebSocket服务器URL，将尝试使用本地默认值");

                // 获取WebSocket服务器地址，优先从配置文件获取，否则使用默认地址
                url = XConfig.LoadValueFromFile<string>("WsServerUrl") ?? GlobalData.WS_HOST;

                // 如果地址为空，则使用默认地址
                if (string.IsNullOrEmpty(url))
                {
                    url = GlobalData.WS_HOST;
                    XLogger.Debug("WebSocket服务器地址为空，使用默认地址");
                }

                // 获取认证Token
                try
                {
                    // 尝试获取App实例
                    var app = System.Windows.Application.Current as App;
                    if (app != null)
                    {
                        // 尝试获取DanDingNet API
                        var api = app.GetType().GetProperty("Api")?.GetValue(app);
                        if (api != null)
                        {
                            // 尝试获取Auth服务
                            var authService = api.GetType().GetProperty("Auth")?.GetValue(api);
                            if (authService != null)
                            {
                                // 获取Token
                                var getTokenMethod = authService.GetType().GetMethod("GetToken");
                                if (getTokenMethod != null)
                                {
                                    token = (string)getTokenMethod.Invoke(authService, null);
                                    if (!string.IsNullOrEmpty(token))
                                    {
                                        XLogger.Info("成功获取本地认证Token");
                                    }
                                    else
                                    {
                                        XLogger.Warn("获取到的本地Token为空");
                                    }
                                }
                            }
                        }
                    }

                    // 如果通过反射方式失败，尝试从全局配置获取
                    if (string.IsNullOrEmpty(token) && GlobalData.Instance?.appConfig?.dNet?.Auth != null)
                    {
                        token = GlobalData.Instance.appConfig.dNet.Auth.GetToken();
                        if (!string.IsNullOrEmpty(token))
                        {
                            XLogger.Info("成功从全局配置获取认证Token");
                        }
                        else
                        {
                            XLogger.Warn("全局配置中的Token为空");
                        }
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Error($"获取本地认证Token时发生异常: {ex.Message}");
                }
            }
            else
            {
                // 使用用户提供的URL
                url = parameters[3];

                // 如果提供了Token参数，则使用参数值
                token = parameters.Length > 4 ? parameters[4] : string.Empty;
            }

            // 验证URL格式
            if (!url.StartsWith("ws://") && !url.StartsWith("wss://"))
            {
                XLogger.Error("WebSocket URL格式不正确，必须以ws://或wss://开头");
                return;
            }

            // 准备连接
            XLogger.Info($"正在尝试连接到WebSocket服务器: {url}");
            if (!string.IsNullOrEmpty(token))
            {
                XLogger.Debug($"使用Token进行认证: {token}");
            }
            else
            {
                XLogger.Warn("未提供认证Token，连接可能会被拒绝");
            }

            // 异步连接
            Task.Run(async () =>
            {
                try
                {
                    // 设置自动重连
                    XWebsocket.AutoReconnect = true;

                    // 连接到服务器
                    bool success = await XWebsocket.ConnectAsync(url, token);

                    if (success)
                    {
                        XLogger.Info($"成功连接到WebSocket服务器: {url}");
                    }
                    else
                    {
                        XLogger.Error($"连接到WebSocket服务器失败: {url}");
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Error($"连接WebSocket时发生异常: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 处理断开连接命令
        /// </summary>
        private void HandleDisconnect()
        {
            // 检查当前是否已连接
            if (!XWebsocket.IsConnected)
            {
                XLogger.Info("WebSocket当前未连接，无需断开");
                return;
            }

            // 异步断开连接
            Task.Run(async () =>
            {
                try
                {
                    XLogger.Info("正在断开WebSocket连接...");
                    await XWebsocket.DisconnectAsync();
                    XLogger.Info("WebSocket连接已断开");
                }
                catch (Exception ex)
                {
                    XLogger.Error($"断开WebSocket连接时发生异常: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 处理状态查询命令
        /// </summary>
        private void HandleStatus()
        {
            // 获取状态信息
            var state = XWebsocket.State;
            bool isConnected = XWebsocket.IsConnected;
            bool isDisabled = XWebsocket.IsDisabled;
            bool autoReconnect = XWebsocket.AutoReconnect;

            // 构建状态信息
            StringBuilder statusInfo = new StringBuilder();
            statusInfo.AppendLine("WebSocket当前状态:");
            statusInfo.AppendLine($"- 连接状态: {GetStateDescription(state)}");
            statusInfo.AppendLine($"- 是否已连接: {(isConnected ? "是" : "否")}");
            statusInfo.AppendLine($"- 是否已禁用: {(isDisabled ? "是" : "否")}");
            statusInfo.AppendLine($"- 自动重连: {(autoReconnect ? "已启用" : "已禁用")}");

            // 输出状态信息
            XLogger.Info(statusInfo.ToString());
        }

        /// <summary>
        /// 获取WebSocket状态的描述
        /// </summary>
        private string GetStateDescription(System.Net.WebSockets.WebSocketState state)
        {
            return state switch
            {
                System.Net.WebSockets.WebSocketState.None => "未初始化",
                System.Net.WebSockets.WebSocketState.Connecting => "正在连接",
                System.Net.WebSockets.WebSocketState.Open => "已连接",
                System.Net.WebSockets.WebSocketState.CloseSent => "正在关闭(已发送关闭帧)",
                System.Net.WebSockets.WebSocketState.CloseReceived => "正在关闭(已接收关闭帧)",
                System.Net.WebSockets.WebSocketState.Closed => "已关闭",
                System.Net.WebSockets.WebSocketState.Aborted => "已终止",
                _ => $"未知状态({state})"
            };
        }

        /// <summary>
        /// 处理恢复WebSocket功能命令
        /// </summary>
        private void HandleEnable()
        {
            // 检查当前是否已禁用
            if (!XWebsocket.IsDisabled)
            {
                XLogger.Info("WebSocket功能当前未被禁用，无需恢复");
                return;
            }

            // 异步恢复功能
            Task.Run(async () =>
            {
                try
                {
                    XLogger.Info("正在尝试恢复WebSocket功能...");
                    bool success = await XWebsocket.ReenableWebSocketAsync();

                    if (success)
                    {
                        XLogger.Info("WebSocket功能已成功恢复");
                    }
                    else
                    {
                        XLogger.Error("恢复WebSocket功能失败");
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Error($"恢复WebSocket功能时发生异常: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 处理上报命令
        /// </summary>
        private void HandleReport(string[] parameters)
        {
            if (parameters.Length < 4)
            {
                XLogger.Error("上报命令格式不正确，请指定上报类型");
                XLogger.Info("支持的上报类型: status (状态), log (日志)");
                return;
            }

            string reportType = parameters[3].ToLower();
            switch (reportType)
            {
                case "status":
                    HandleReportStatus();
                    break;
                case "log":
                    HandleReportLog(parameters);
                    break;
                default:
                    XLogger.Error($"未知的上报类型: {reportType}");
                    XLogger.Info("支持的上报类型: status (状态), log (日志)");
                    break;
            }
        }

        /// <summary>
        /// 尝试获取ScriptStatusReporterService实例
        /// </summary>
        private ScriptStatusReporterService? GetScriptStatusReporterService()
        {
            // 如果已缓存实例，则直接返回
            if (_scriptStatusReporterService != null)
                return _scriptStatusReporterService;

            try
            {
                // 尝试从App.Current.Services中获取（如果项目使用了依赖注入）
                var app = System.Windows.Application.Current as App;
                if (app != null)
                {
                    var serviceProvider = app.GetType().GetProperty("Services")?.GetValue(app) as IServiceProvider;
                    if (serviceProvider != null)
                    {
                        _scriptStatusReporterService = serviceProvider.GetService<ScriptStatusReporterService>();
                        if (_scriptStatusReporterService != null)
                            return _scriptStatusReporterService;
                    }
                }

                // 如果找不到，尝试反射查找单例实例
                var serviceType = typeof(ScriptStatusReporterService);
                var instanceField = serviceType.GetField("_instance", System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.NonPublic);
                if (instanceField != null)
                {
                    _scriptStatusReporterService = instanceField.GetValue(null) as ScriptStatusReporterService;
                    if (_scriptStatusReporterService != null)
                        return _scriptStatusReporterService;
                }

                // 最后尝试在App中查找单例变量或属性
                if (app != null)
                {
                    foreach (var prop in app.GetType().GetProperties())
                    {
                        if (prop.PropertyType == serviceType)
                        {
                            _scriptStatusReporterService = prop.GetValue(app) as ScriptStatusReporterService;
                            if (_scriptStatusReporterService != null)
                                return _scriptStatusReporterService;
                        }
                    }
                }

                // 如果所有尝试都失败，记录警告
                XLogger.Warn("无法获取ScriptStatusReporterService实例，将尝试自行构建上报消息");
                return null;
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取ScriptStatusReporterService时发生异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 处理状态上报命令
        /// </summary>
        private void HandleReportStatus()
        {
            // 检查WebSocket连接状态
            if (!XWebsocket.IsConnected)
            {
                XLogger.Error("WebSocket未连接，无法上报状态");
                return;
            }

            // 尝试获取ScriptStatusReporterService服务实例
            var statusReporterService = GetScriptStatusReporterService();
            if (statusReporterService == null)
            {
                XLogger.Error("无法获取ScriptStatusReporterService服务实例");
                XLogger.Info("将尝试创建空的状态更新消息...");

                // 构造一个简单的状态消息
                SendEmptyStatusUpdate();
                return;
            }

            // 获取所有脚本状态
            try
            {
                var scriptStatuses = statusReporterService.GetAllScriptStatuses();
                if (scriptStatuses == null || scriptStatuses.Count == 0)
                {
                    XLogger.Info("当前没有活动的脚本状态可供上报");
                    // 发送空状态更新
                    SendEmptyStatusUpdate();
                    return;
                }

                // 构造并发送消息
                Task.Run(async () =>
                {
                    try
                    {
                        XLogger.Info($"准备上报{scriptStatuses.Count}个脚本状态...");

                        // 构建批量更新消息
                        var batchUpdateMessage = new
                        {
                            type = "SCRIPT_STATUS_SYNC_BATCH_UPDATE",
                            payload = new
                            {
                                requestId = Guid.NewGuid().ToString(),
                                statuses = scriptStatuses
                            }
                        };

                        // 发送消息
                        bool success = await XWebsocket.SendObjectAsync(batchUpdateMessage);
                        if (success)
                        {
                            XLogger.Info($"成功上报{scriptStatuses.Count}个脚本状态");
                        }
                        else
                        {
                            XLogger.Error("上报脚本状态失败");
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"上报脚本状态时发生异常: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取脚本状态时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送空的状态更新（测试用）
        /// </summary>
        private void SendEmptyStatusUpdate()
        {
            Task.Run(async () =>
            {
                try
                {
                    // 创建一个测试状态数据
                    var testStatus = new ScriptStatusData
                    {
                        InternalScriptId = -1,
                        ScriptInstanceId = "test-instance",
                        Status = ScriptRuntimeStatus.Running,
                        Timestamp = DateTime.Now,
                        Message = null,
                        FriendlyName = "测试实例",
                        ScriptType = "Regular"
                    };

                    // 构建批量更新消息
                    var batchUpdateMessage = new
                    {
                        type = "SCRIPT_STATUS_SYNC_BATCH_UPDATE",
                        payload = new
                        {
                            requestId = Guid.NewGuid().ToString(),
                            statuses = new List<ScriptStatusData> { testStatus }
                        }
                    };

                    // 发送消息
                    XLogger.Info("准备发送测试状态数据...");
                    bool success = await XWebsocket.SendObjectAsync(batchUpdateMessage);

                    if (success)
                    {
                        XLogger.Info("成功发送测试状态数据");
                    }
                    else
                    {
                        XLogger.Error("发送测试状态数据失败");
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Error($"发送测试状态数据时发生异常: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 处理日志上报命令
        /// </summary>
        private void HandleReportLog(string[] parameters)
        {
            // 检查参数数量
            if (parameters.Length < 5)
            {
                XLogger.Error("日志上报命令格式不正确，需要指定日志内容");
                XLogger.Info("正确格式: test ws report log [消息内容] [日志级别]");
                XLogger.Info("示例: test ws report log \"测试日志消息\" info");
                return;
            }

            // 检查WebSocket连接状态
            if (!XWebsocket.IsConnected)
            {
                XLogger.Error("WebSocket未连接，无法上报日志");
                return;
            }

            // 获取日志内容和级别
            string logContent = parameters[4];
            string logLevel = parameters.Length > 5 ? parameters[5].ToUpper() : "INFO";

            // 验证日志级别
            if (logLevel != "DEBUG" && logLevel != "INFO" && logLevel != "WARN" && logLevel != "ERROR")
            {
                XLogger.Warn($"未知的日志级别: {logLevel}，将使用默认级别INFO");
                logLevel = "INFO";
            }

            // 尝试获取WebSocketLogForwarderService服务实例或者直接使用XWebsocket发送
            try
            {
                // 创建日志条目
                var logEntry = new XHelper.Models.RemoteLogEntry(
                    DateTime.Now,
                    logLevel,
                    $"[用户手动上报] {logContent}",
                    null  // 任务ID
                );

                // 构造日志上报消息
                var logPayload = new
                {
                    type = "LOG_ENTRIES_PUSH",
                    payload = new
                    {
                        entries = new List<XHelper.Models.RemoteLogEntry> { logEntry }
                    }
                };

                // 发送消息
                Task.Run(async () =>
                {
                    try
                    {
                        XLogger.Info($"正在上报日志: [{logLevel}] {logContent}");
                        bool success = await XWebsocket.SendObjectAsync(logPayload);

                        if (success)
                        {
                            XLogger.Info("日志上报成功");
                        }
                        else
                        {
                            XLogger.Error("日志上报失败");
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"上报日志时发生异常: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                XLogger.Error($"准备日志上报时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 打印帮助信息
        /// </summary>
        private void PrintHelp()
        {
            XLogger.Info(@"WebSocket命令用法:
test ws connect [url] [token]  - 连接到WebSocket服务器 (url和token可选，如未提供将使用本地默认值)
test ws disconnect             - 断开WebSocket连接
test ws status                 - 查询WebSocket当前状态
test ws enable                 - 恢复WebSocket功能(从禁用状态)
test ws report status          - 主动上报当前脚本状态
test ws report log [消息] [级别] - 主动上报日志(级别可选: debug,info,warn,error)
test ws help                   - 显示帮助信息");
        }
    }
}