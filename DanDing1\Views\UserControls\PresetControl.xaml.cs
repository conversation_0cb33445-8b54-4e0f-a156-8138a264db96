using DanDing1.ViewModels.Pages;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Markup;
using Wpf.Ui;
using Wpf.Ui.Controls;

namespace DanDing1.Views.UserControls
{
    /// <summary>
    /// PresetControl.xaml 的交互逻辑
    /// </summary>
    public partial class PresetControl : UserControl
    {
        public PresetControlViewModel ViewModel { get; }

        public PresetControl()
        {
            // 从DI容器获取ContentDialogService，但不立即设置给ViewModel
            // 允许父窗口为ViewModel设置一个特定窗口的ContentDialogService
            var contentDialogService = App.GetService<IContentDialogService>();

            // 创建ViewModel实例
            ViewModel = new PresetControlViewModel(contentDialogService);

            // 为设计时支持创建ViewModel
            if (System.ComponentModel.DesignerProperties.GetIsInDesignMode(this))
            {
                ViewModel = new PresetControlViewModel(new ContentDialogService());
            }

            // 设置DataContext
            DataContext = this;

            // 初始化组件 (WPF自动生成的方法)
            InitializeComponent();

            // 为ViewModel添加刷新数值的方法
            ViewModel.RefreshNumberBoxValues = () =>
            {
                // 强制更新NumberBox的值到ViewModel
                BindingOperations.GetBindingExpression(SetNumberBox, NumberBox.ValueProperty)?.UpdateSource();
                BindingOperations.GetBindingExpression(PresetNumberBox, NumberBox.ValueProperty)?.UpdateSource();
            };
        }
    }
}