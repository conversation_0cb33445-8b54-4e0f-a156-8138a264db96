﻿using DanDing1.Helpers;
using DanDing1.Models;
using DanDing1.ViewModels.Pages;
using System.Windows.Controls;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Extensions;
using Wpf.Ui.Abstractions.Controls;
using XHelper;
using System.Runtime.InteropServices;
using System.Windows.Navigation;
using Microsoft.Win32;
using DanDing1.Views.Windows;
using System.Collections.ObjectModel;
using System.IO;
using System.ComponentModel;
using System.Net.Http;
using System.Threading.Tasks;
using XHelper.DanDingNet;

namespace DanDing1.Views.Pages
{
    /// <summary>
    /// 插件安装进度回调
    /// </summary>
    /// <param name="status">当前状态文本</param>
    public delegate void PluginInstallProgressCallback(string status);

    /// <summary>
    /// 插件安装器
    /// </summary>
    public class PluginInstaller
    {
        private readonly string _downloadUrl;
        private readonly string _pluginFileName;
        private readonly string _installDir;
        private readonly PluginInstallProgressCallback _progressCallback;

        public PluginInstaller(string downloadUrl, string pluginFileName, string installDir, PluginInstallProgressCallback progressCallback)
        {
            _downloadUrl = downloadUrl;
            _pluginFileName = pluginFileName;
            _installDir = installDir;
            _progressCallback = progressCallback;
        }

        public async Task InstallAsync()
        {
            _progressCallback("准备下载...");

            // 创建临时目录
            string tempDir = Path.Combine(Path.GetTempPath(), "DanDing1_Temp");
            Directory.CreateDirectory(tempDir);
            string zipPath = Path.Combine(tempDir, _pluginFileName);

            try
            {
                // 下载文件
                using (var client = new HttpClient())
                {
                    var response = await client.GetAsync(_downloadUrl, HttpCompletionOption.ResponseHeadersRead);
                    var totalBytes = response.Content.Headers.ContentLength ?? 0;
                    using (var stream = await response.Content.ReadAsStreamAsync())
                    using (var fileStream = new FileStream(zipPath, FileMode.Create))
                    {
                        var buffer = new byte[8192];
                        var totalBytesRead = 0L;
                        int bytesRead;

                        while ((bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                        {
                            await fileStream.WriteAsync(buffer, 0, bytesRead);
                            totalBytesRead += bytesRead;
                            var percentage = (int)((totalBytesRead * 100) / totalBytes);
                            _progressCallback($"正在下载...{percentage}%");
                        }
                    }
                }

                _progressCallback("正在安装...");

                // 确保安装目录存在
                Directory.CreateDirectory(_installDir);

                // 解压文件
                System.IO.Compression.ZipFile.ExtractToDirectory(zipPath, _installDir, true);

                // 删除临时文件
                File.Delete(zipPath);
                try { Directory.Delete(tempDir); } catch { }

                _progressCallback("已安装");
            }
            catch
            {
                // 清理临时文件
                try
                {
                    if (File.Exists(zipPath)) File.Delete(zipPath);
                    if (Directory.Exists(tempDir)) Directory.Delete(tempDir);
                }
                catch { }
                throw; // 重新抛出异常
            }
        }
    }

    public partial class SettingsPage : INavigableView<SettingsViewModel>, INotifyPropertyChanged
    {
        [DllImport("user32.dll", SetLastError = true)]
        private static extern bool SetWindowDisplayAffinity(IntPtr hwnd, uint dwAffinity);

        private readonly IContentDialogService contentDialogService;

        private string _installPaddleOCRText = "安装 (约30.8MB)";

        public string InstallPaddleOCRText
        {
            get => _installPaddleOCRText;
            set
            {
                _installPaddleOCRText = value;
                OnPropertyChanged(nameof(InstallPaddleOCRText));
            }
        }

        private bool _installPaddleOCREnabled = true;

        public bool InstallPaddleOCREnabled
        {
            get => _installPaddleOCREnabled;
            set
            {
                _installPaddleOCREnabled = value;
                OnPropertyChanged(nameof(InstallPaddleOCREnabled));
            }
        }

        private string _machineCode = "";

        public string MachineCode
        {
            get => _machineCode;
            set
            {
                _machineCode = value;
                OnPropertyChanged(nameof(MachineCode));
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public SettingsViewModel ViewModel { get; }

        public SettingsPage(IContentDialogService contentDialogService,
            SettingsViewModel viewModel)
        {
            this.contentDialogService = contentDialogService;
            ViewModel = viewModel;
            DataContext = this;

            InitializeComponent();

            // 获取机器码
            try
            {
                MachineCode = MachineIdentifier.GetMachineCode();
            }
            catch (Exception ex)
            {
                MachineCode = "获取失败";
                XLogger.Error($"获取机器码失败: {ex.Message}");
            }

            // 检查PaddleOCR是否存在
            string paddleOcrPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "runtimes", "PaddleOCR_cpp.exe");
            bool isInstalled = File.Exists(paddleOcrPath);
            InstallPaddleOCRText = isInstalled ? "已安装" : "安装 (约30.8MB)";
            InstallPaddleOCREnabled = !isInstalled;

            OpenLogItems.ItemsSource = new List<string>() { "直接切换", "弹出小窗" };
            //载入持久化存储数据
            ShowDebug.IsChecked = XConfig.LoadValueFromFile<bool>("ShowDebug");
            StartOpenLog.IsChecked = XConfig.LoadValueFromFile<bool>("StartOpenLog");
            StartSaveTasks.IsChecked = XConfig.LoadValueFromFile<bool>("StartSaveTasks");
            OpenLogItems.SelectedItem = XConfig.LoadValueFromFile<string>("OpenLogItems") ?? "弹出小窗";
            OpenLogItems.SelectionChanged += OpenLogItems_SelectionChanged;

            //录制质量
            RecordQuality.ItemsSource = new List<string>() { "原生质量（不压缩）", "缩略图质量（压缩）" };
            RecordQuality.SelectedItem = XConfig.LoadValueFromFile<string>("RecordQuality") ?? "原生质量（不压缩）";
            RecordQuality.SelectionChanged += RecordQuality_SelectionChanged;

            //自定义节点接口
            ServerHost.ItemsSource = new List<string>() { "港式接口1", "中转线路", "中转线路2", "从机测试" };
            if (Debugger.IsAttached)
                ServerHost.ItemsSource = new List<string>() { "港式接口1", "中转线路", "中转线路2", "局域网测试", "本机测试" };

            string? mr = XConfig.LoadValueFromFile<string>("ServerHost");
            if (string.IsNullOrEmpty(mr))
            {
                XConfig.SaveValueToFile("ServerHost", "中转线路2");
                ServerHost.SelectedIndex = 0;
            }
            else
                ServerHost.SelectedItem = mr;
            ServerHost.SelectionChanged += ServerHost_SelectionChanged;

            //图库接口设置
            PicServer.ItemsSource = new List<string>() { "默认", "临时", "本地" };
            mr = XConfig.LoadValueFromFile<string>("PicServer");
            if (string.IsNullOrEmpty(mr))
            {
                XConfig.SaveValueToFile("PicServer", "默认");
                PicServer.SelectedIndex = 0;
            }
            else
                PicServer.SelectedItem = mr;
            PicServer.SelectionChanged += PicServer_SelectionChanged;

            PicServerVer.Text = GlobalData.Instance.PicServerVer;
            PicServerVer.TextChanged += PicServerVer_TextChanged;

            CheckLocalConfig();
        }

        /// <summary>
        /// 录制质量选择
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void RecordQuality_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ComboBox? t = sender as ComboBox;
            if (t is null) return;
            XConfig.SaveValueToFile("RecordQuality", t.SelectedValue);
        }

        private void Hyperlink_RequestNavigate(object sender, RequestNavigateEventArgs e)
        {
            // 使用默认浏览器打开链接
            Process.Start(new ProcessStartInfo(e.Uri.AbsoluteUri) { UseShellExecute = true });
            e.Handled = true;
        }

        private void OpenLogItems_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ComboBox? t = sender as ComboBox;
            if (t is null) return;
            XConfig.SaveValueToFile("OpenLogItems", t.SelectedValue);
        }

        private void PicServer_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ComboBox? t = sender as ComboBox;
            if (t is null) return;
            XConfig.SaveValueToFile("PicServer", t.SelectedValue);
        }

        private void PicServerVer_TextChanged(object sender, TextChangedEventArgs e)
        {
            System.Windows.Controls.TextBox? box = sender as System.Windows.Controls.TextBox;
            if (box is not null && !string.IsNullOrEmpty(box.Text))
                GlobalData.Instance.PicServerVer = box.Text;
        }

        private async void ServerHost_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ComboBox? t = sender as ComboBox;
            if (t is null) return;
            DDApi.ServerHost.TryGetValue(t.SelectedValue.ToString() ?? "", out var host);
            if (host is not null)
            {
                ServerHost_Tip.Text = "正在检测节点状态..";
                try
                {
                    var info = await GlobalData.Instance.appConfig.dNet.System.GetInfoAsync(host);
                    if (info.AppInfo != "")
                    {
                        ServerHost_Tip.Text = "该连接可用！";
                        XConfig.SaveValueToFile("ServerHost", t.SelectedValue);
                        await GlobalData.Instance.appConfig.UpNowHost();
                    }
                    else
                        ServerHost_Tip.Text = "该连接不可用! 请重新选择其它节点!";
                }
                catch
                {
                    ServerHost_Tip.Text = "该连接不可用! 请重新选择其它节点!";
                }
            }
            else
            {
                ServerHost_Tip.Text = "该连接不存在! 请重新选择其它节点!";
                return;
            }
        }

        private void ShowDebug_Checked(object sender, RoutedEventArgs e)
        {
            CheckBox? box = sender as CheckBox;
            if (box is null)
                return;
            XConfig.SaveValueToFile("ShowDebug", box?.IsChecked ?? false);
        }

        private void StartOpenLog_Checked(object sender, RoutedEventArgs e)
        {
            CheckBox? box = sender as CheckBox;
            if (box == null)
                return;
            XConfig.SaveValueToFile("StartOpenLog", box?.IsChecked ?? false);
        }

        /// <summary>
        /// 仅检查版本，不考虑跳过和提醒设置
        /// </summary>
        private async Task<bool> CheckVersionOnly()
        {
            //检查版本号是否需要更新
            if (!GlobalData.Instance.UserConfig.BetaTesterToggle && GlobalData.Instance.appConfig.Info.Ver != "" && GlobalData.Instance.appConfig.Info.IsNeedUpData)
            {
                //弹出更新界面
                var updateWindow = new UpdateWindow();
                updateWindow.ShowDialog();
                return true;
            }
            //检查Beta版本是否需要更新
            if (GlobalData.Instance.UserConfig.BetaTesterToggle)
            {
                var vs = await GlobalData.Instance.appConfig.dNet.System.GetInfoBetaAsync();
                if (vs[0] != "" && vs[0] != GlobalData.Instance.appConfig.Info.Now_Ver)
                {
                    //弹出更新界面
                    var updateWindow = new UpdateWindow(GlobalData.Instance.appConfig.Info.Now_Ver, vs[0], vs[2], vs[1]);
                    updateWindow.ShowDialog();
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 检查版本是否为最新版本
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void TextBlock_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                if (!await CheckVersionOnly())
                    await contentDialogService.ShowSimpleDialogAsync(
                        new SimpleContentDialogCreateOptions()
                        {
                            Title = "您不需要更新",
                            Content = "您当前的软件版本就是最新版本了，不需要更新！",
                            CloseButtonText = "OK"
                        });
            }
        }

        /// <summary>
        /// 检查更新按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void CheckUpdateButton_Click(object sender, RoutedEventArgs e)
        {
            if (!await CheckVersionOnly())
                await contentDialogService.ShowSimpleDialogAsync(
                    new SimpleContentDialogCreateOptions()
                    {
                        Title = "您不需要更新",
                        Content = "您当前的软件版本就是最新版本了，不需要更新！",
                        CloseButtonText = "OK"
                    });
        }

        private void StartSaveTasks_Checked(object sender, RoutedEventArgs e)
        {
            CheckBox? box = sender as CheckBox;
            if (box == null)
                return;
            XConfig.SaveValueToFile("StartSaveTasks", box?.IsChecked ?? false);
        }

        /// <summary>
        /// 打开软件所在文件夹
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void OpenAppFolder_Click(object sender, RoutedEventArgs e)
        {
            Utils.OpenFolder(AppDomain.CurrentDomain.BaseDirectory);
        }

        /// <summary>
        /// 创建快捷方式
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void CreateShortcut_Click(object sender, RoutedEventArgs e)
        {
            string appPath = AppDomain.CurrentDomain.BaseDirectory + "DanDing1.exe"; // 应用程序路径
            string shortcutName = "蛋定助手";      // 快捷方式名称
            string description = "蛋定助手 -祝您生活愉快~"; // 描述
            Utils.CreateShortcut(shortcutName, appPath, description);
            //快捷方式创建成功后弹出提示框
            contentDialogService.ShowSimpleDialogAsync(new SimpleContentDialogCreateOptions()
            {
                Title = "创建快捷方式成功",
                Content = "快捷方式已创建到桌面，快去启动吧！",
                CloseButtonText = "OK"
            });
        }

        /// <summary>
        /// 解析录制文件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ParseSRFile_Click(object sender, RoutedEventArgs e)
        {
            // 创建 OpenFileDialog 实例
            OpenFileDialog openFileDialog = new OpenFileDialog();

            // 设置文件类型过滤器
            openFileDialog.Filter = "录制文件|*.sr";

            // 设置对话框标题
            openFileDialog.Title = "选择录制文件";

            //组成目录 ".\\runtimes\\Debug"
            string runtimeDir = AppDomain.CurrentDomain.BaseDirectory + "runtimes\\Debug";
            openFileDialog.InitialDirectory = runtimeDir;

            // 显示对话框并检查用户是否选择了文件
            if (openFileDialog.ShowDialog() == true)
            {
                // 获取用户选择的文件路径
                string filePath = openFileDialog.FileName;
                // 解析录制文件
                try
                {
                    new HistoryRecordWindow(filePath).Show();
                }
                catch (Exception)
                {
                }
            }
        }

        /// <summary>
        /// 打开mumu设置窗口
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void OpenMuMuConfig_Click(object sender, RoutedEventArgs e)
        {
            new MuMuConfigsWindow().ShowDialog();
            CheckLocalConfig();
        }

        /// <summary>
        /// 检查本地配置，并提示用户进行配置
        /// </summary>
        private void CheckLocalConfig()
        {
            //检查是否有MuMu的配置
            ObservableCollection<Models.SimulatorConfig>? configs = XConfig.LoadValueFromFile<ObservableCollection<Models.SimulatorConfig>>("MuMuConfigs");
            if (configs is null || configs.Count == 0)
            {
                //添加未配置提示
                OpenMuMuConfig.Content = "MuMu配置 (未配置)";
                OpenMuMuConfig.FontWeight = FontWeights.Bold;
            }
            else
            {
                //恢复默认显示
                OpenMuMuConfig.Content = "MuMu配置";
                OpenMuMuConfig.FontWeight = FontWeights.Normal;
            }
        }

        private async void InstallPaddleOCR_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                InstallPaddleOCREnabled = false;

                var installer = new PluginInstaller(
                    downloadUrl: "https://cfcdn.180402.xyz/Plugin/PaddleOCR_cpp.zip",
                    pluginFileName: "PaddleOCR_cpp.zip",
                    installDir: Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "runtimes"),
                    progressCallback: (status) => InstallPaddleOCRText = status
                );

                await installer.InstallAsync();
                InstallPaddleOCREnabled = false;

                await contentDialogService.ShowSimpleDialogAsync(
                    new SimpleContentDialogCreateOptions()
                    {
                        Title = "安装成功",
                        Content = "PaddleOCR 插件已成功安装！",
                        CloseButtonText = "确定"
                    });
            }
            catch (Exception ex)
            {
                InstallPaddleOCRText = "安装 (约30.8MB)";
                InstallPaddleOCREnabled = true;
                await contentDialogService.ShowSimpleDialogAsync(
                    new SimpleContentDialogCreateOptions()
                    {
                        Title = "安装失败",
                        Content = $"安装PaddleOCR时发生错误：{ex.Message}",
                        CloseButtonText = "确定"
                    });
            }
        }

        private void MaterialCollector_Click(object sender, RoutedEventArgs e)
        {
            var window = new MaterialCollectorWindow();
            window.ShowDialog();
        }

        private void DownloadPicServer_Click(object sender, RoutedEventArgs e)
        {
            new PicsDownLoadWindow().ShowDialog();
        }

        private void MultipleInstanceNameButton_Click(object sender, RoutedEventArgs e)
        {
            var gameNameWindow = new GameNameCustomWindow();
            gameNameWindow.ShowDialog();
        }

        private void CopyMachineCode_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            try
            {
                System.Windows.Clipboard.SetText(MachineCode);
                // 显示复制成功提示
                System.Windows.MessageBox.Show("机器码已复制到剪贴板", "成功", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                XLogger.Error($"复制机器码失败: {ex.Message}");
            }
        }
    }
}