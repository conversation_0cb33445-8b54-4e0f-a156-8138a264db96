﻿/*
 * 文件名: SuperMultiGamesWindowViewModel.Configurations.cs
 * 职责描述: 管理应用配置和设置
 * 该文件处理配置的加载、保存和更新，包括用户偏好设置和游戏配置参数的持久化
 */

using DanDing1.Models;
using DanDing1.Models.Super;
using DanDing1.ViewModels.Pages;
using DanDing1.ViewModels.Windows.SuperMultiGamesWindowViewModels;
using DanDing1.Helpers;
using System.Collections.ObjectModel;
using System.Text.Json;
using System.Windows;
using XHelper;

namespace DanDing1.ViewModels.Windows
{
    public partial class SuperMultiGamesWindowViewModel : ObservableObject
    {
        /// <summary>
        /// 自动保存任务配置
        /// </summary>
        /// <param name="game">游戏模型</param>
        private void AutoSaveTaskConfigs(SuperMultiGame_DataModel game)
        {
            // 优先使用模拟器真实名称，如果为空则使用模拟器ID
            string saveKey = !string.IsNullOrEmpty(game.MumuRealName)
                ? game.MumuRealName
                : $"MumuId_{game.GameId}";
            if (game.TaskConfiguration != null && XConfig.LoadValueFromFile<bool>(LocalConfig.StartSaveTasks))
            {
                // 保存任务列表
                XConfig.SaveValueToFile("SuperMultiGame/TaskList", saveKey, game.TaskConfiguration.GameTaskLists);

                //寄养队友名字：
                //XConfig.SaveValueToFile(GameName, "JiYang_DesignatedName", AddTaskControl.ViewModel.Timer_JiYang_DesignatedName);

                // 保存额外配置(Game_Scene和XShangChecked)
                var extraConfig = new Dictionary<string, object>
                {
                    { "Game_Scene", game.TaskConfiguration.Game_Scene },
                    { "XShangChecked", game.TaskConfiguration.XShangChecked },
                    { "JiYang_DesignatedName", game.TaskConfiguration.Timer_JiYang_DesignatedName },
                };
                XConfig.SaveValueToFile("SuperMultiGame/ExtraConfig", saveKey, extraConfig);
                if (game.Config is not null)
                {
                    game.Config.IsRecord = false;
                    XConfig.SaveValueToFile("SuperMultiGame/Config", saveKey, game.Config);
                }
                game.TaskConfiguration.SaveConfig(saveKey);
            }
            AddLog($"已经为 {game.GameName}自动保存了任务列表、游戏配置！");
            AddGameLog(game.GameId, $"游戏配置已自动保存至 {saveKey}");
        }

        /// <summary>
        /// 从本地加载保存的配置
        /// </summary>
        private void LoadSavedConfiguration()
        {
            try
            {
                // 尝试加载保存的配置
                var savedGames = XConfig.LoadValueFromFile<List<SuperMultiGame_DataModel>>("SuperMultiGame", "GameList");

                if (savedGames != null && savedGames.Count > 0)
                {
                    // 清空当前列表
                    SuperMultiGame_DataModelCollection.Clear();

                    // 先清空游戏日志集合
                    GameLogs.Clear();

                    // 手动创建临时游戏日志列表
                    List<GameLogItem> tempGameLogs = new List<GameLogItem>();

                    // 添加保存的游戏
                    foreach (var game in savedGames)
                    {
                        SuperMultiGame_DataModelCollection.Add(game);

                        // 创建新的游戏日志项并添加初始日志
                        var gameLog = new GameLogItem(game.GameName, game.GameId);
                        gameLog.AddLog($"加载游戏配置，主窗口句柄：{game.MumuHandle}，渲染窗口句柄：{game.GameHandle}");
                        tempGameLogs.Add(gameLog);

                        // 为加载的游戏分配脚本ID
                        GetScriptId(game.GameId);

                        // 尝试加载该游戏的任务配置
                        LoadTaskConfiguration(game);
                    }

                    // 一次性添加所有游戏日志，减少UI更新次数
                    foreach (var log in tempGameLogs)
                    {
                        GameLogs.Add(log);
                    }

                    AddLog($"成功加载 {savedGames.Count} 个游戏配置");

                    // 手动触发主线程日志更新
                    OnMainLogUpdated?.Invoke();

                    // 手动触发游戏日志更新事件，确保UI更新
                    OnGameLogUpdated?.Invoke();

                    // 静默刷新模拟器状态
                    RefreshMuMuData("False");
                }
            }
            catch (Exception ex)
            {
                AddLog($"加载配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存当前配置到本地
        /// </summary>
        private void SaveConfiguration()
        {
            try
            {
                // 创建一个新列表，只包含需要保存的属性
                var filteredGameList = SuperMultiGame_DataModelCollection.Select(game => new SuperMultiGame_DataModel
                {
                    GameId = game.GameId,
                    GameName = game.GameName,
                    Identity = game.Identity,
                    MumuRealName = game.MumuRealName,
                    GameData = game.GameData,
                    SimulatorType = game.SimulatorType
                    // 不保存：MumuHandle, GameHandle, CurrentTask, RunningDuration, RunningStatus, IsButtonEnabled, LastLogMessage
                }).ToList();

                // 保存精简后的列表到本地
                XConfig.SaveValueToFile("SuperMultiGame", "GameList", filteredGameList);

                // 为每个游戏保存Config和TaskConfiguration
                foreach (var game in SuperMultiGame_DataModelCollection)
                {
                    // 使用与任务配置相同的键名策略
                    string saveKey = !string.IsNullOrEmpty(game.MumuRealName)
                        ? game.MumuRealName
                        : $"MumuId_{game.GameId}";

                    // 保存Config到独立文件
                    if (game.Config != null)
                    {
                        XConfig.SaveValueToFile("SuperMultiGame/Config", saveKey, game.Config);
                        AddGameLog(game.GameId, $"游戏配置已保存至 {saveKey}");
                    }

                    // 保存TaskConfiguration到独立文件
                    if (game.TaskConfiguration != null)
                    {
                        // 保存任务列表
                        XConfig.SaveValueToFile("SuperMultiGame/TaskList", saveKey, game.TaskConfiguration.GameTaskLists);

                        // 保存额外配置(Game_Scene和XShangChecked)
                        var extraConfig = new Dictionary<string, object>
                        {
                            { "Game_Scene", game.TaskConfiguration.Game_Scene },
                            { "XShangChecked", game.TaskConfiguration.XShangChecked },
                            { "JiYang_DesignatedName", game.TaskConfiguration.Timer_JiYang_DesignatedName },
                        };
                        XConfig.SaveValueToFile("SuperMultiGame/ExtraConfig", saveKey, extraConfig);
                    }
                }

                AddLog($"成功保存游戏列表配置，总共 {SuperMultiGame_DataModelCollection.Count} 个游戏");
                System.Windows.MessageBox.Show("成功保存游戏列表配置，下次启动时会自动加载当前列表和配置!~", "保存成功", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                AddLog($"保存配置失败: {ex.Message}");
                System.Windows.MessageBox.Show($"保存配置失败: {ex.Message}", "保存失败", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 尝试为游戏加载已保存的任务配置
        /// </summary>
        /// <param name="game">游戏模型</param>
        private void LoadTaskConfiguration(SuperMultiGame_DataModel game)
        {
            try
            {
                if (game == null) return;

                game.TaskConfiguration ??= new AddTaskPropertyViewModel();

                // 尝试使用模拟器真实名称作为键
                string primaryKey = !string.IsNullOrEmpty(game.MumuRealName)
                    ? game.MumuRealName
                    : $"MumuId_{game.GameId}";

                // 尝试加载任务配置
                var taskList = XConfig.LoadValueFromFile<ObservableCollection<ScriptEngine.Model.TaskConfigsModel.Configs>>(
                    "SuperMultiGame/TaskList", primaryKey);

                // 尝试加载游戏Config
                var gameConfig = XConfig.LoadValueFromFile<SuperMultiGameConfigWindowViewModel>("SuperMultiGame/Config", primaryKey);

                // 如果找不到配置，尝试使用游戏名称作为键（兼容旧版本）
                if (taskList == null || taskList.Count == 0)
                {
                    taskList = XConfig.LoadValueFromFile<ObservableCollection<ScriptEngine.Model.TaskConfigsModel.Configs>>(
                        "SuperMultiGame/TaskList", game.GameName);

                    // 如果通过游戏名找到了配置，同时使用游戏名读取Config
                    if (taskList != null && taskList.Count > 0)
                    {
                        primaryKey = game.GameName;
                        gameConfig = XConfig.LoadValueFromFile<SuperMultiGameConfigWindowViewModel>("SuperMultiGame/Config", primaryKey);
                    }
                }

                // 如果找到Config，加载到游戏中
                if (gameConfig != null)
                {
                    // 更新通知列表为当前程序定义的最新列表，避免使用保存时的旧列表
                    gameConfig.Notice_Lists = new SuperMultiGameConfigWindowViewModel().Notice_Lists;
                    game.Config = gameConfig;
                    AddGameLog(game.GameId, "已加载保存的游戏配置");
                }

                // 如果找到配置，加载到游戏中
                if (taskList != null && taskList.Count > 0)
                {
                    //game.TaskConfiguration.GameTaskLists = taskList;
                    game.TaskConfiguration.GameTaskLists.Clear();
                    foreach (var item in taskList)
                        game.TaskConfiguration.GameTaskLists.Add(item);

                    // 加载额外配置(Game_Scene和XShangChecked)
                    var extraConfig = XConfig.LoadValueFromFile<Dictionary<string, object>>("SuperMultiGame/ExtraConfig", primaryKey);
                    if (extraConfig != null)
                    {
                        // 加载Game_Scene
                        if (extraConfig.ContainsKey("Game_Scene") && extraConfig["Game_Scene"] != null)
                        {
                            try
                            {
                                game.TaskConfiguration.Game_Scene = extraConfig["Game_Scene"]?.ToString() ?? string.Empty;
                            }
                            catch { /* 继续执行，不中断加载过程 */ }
                        }

                        // 加载XShangChecked
                        if (extraConfig.ContainsKey("XShangChecked") && extraConfig["XShangChecked"] != null)
                        {
                            try
                            {
                                // JSON反序列化可能会把bool转为JsonElement，需要处理
                                if (extraConfig["XShangChecked"] is JsonElement element)
                                {
                                    game.TaskConfiguration.XShangChecked = element.ValueKind == JsonValueKind.True;
                                }
                                else
                                {
                                    game.TaskConfiguration.XShangChecked = Convert.ToBoolean(extraConfig["XShangChecked"]);
                                }
                            }
                            catch { /* 继续执行，不中断加载过程 */ }
                        }

                        // 加载JiYang_DesignatedName
                        if (extraConfig.ContainsKey("JiYang_DesignatedName") && extraConfig["JiYang_DesignatedName"] != null)
                        {
                            try
                            {
                                game.TaskConfiguration.Timer_JiYang_DesignatedName = extraConfig["JiYang_DesignatedName"]?.ToString() ?? string.Empty;
                            }
                            catch { /* 继续执行，不中断加载过程 */ }
                        }
                    }

                    // 尝试加载任务配置
                    game.TaskConfiguration.LoadConfig(primaryKey);
                    game.TaskConfiguration.ResetAppStutas();

                    AddLog($"已为游戏 {game.GameName} 加载保存的任务配置，共 {taskList.Count} 个任务");
                    AddGameLog(game.GameId, $"已加载保存的任务配置，共 {taskList.Count} 个任务");
                }
            }
            catch (Exception ex)
            {
                AddLog($"为游戏 {game.GameName} 加载任务配置失败: {ex.Message}");
                AddGameLog(game.GameId, $"加载任务配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 确保游戏的Config存在，如果不存在则创建默认配置并保存
        /// </summary>
        /// <param name="game">游戏模型</param>
        internal void EnsureGameConfigExists(SuperMultiGame_DataModel game)
        {
            try
            {
                if (game == null) return;

                // 如果Config为空，创建默认配置
                if (game.Config == null)
                {
                    var defaultConfig = ServiceProvider.GetService(typeof(SuperMultiGameConfigWindowViewModel)) as SuperMultiGameConfigWindowViewModel;
                    if (defaultConfig != null)
                    {
                        game.Config = defaultConfig;
                        // 保存新创建的默认配置
                        string saveKey = !string.IsNullOrEmpty(game.MumuRealName)
                            ? game.MumuRealName
                            : $"MumuId_{game.GameId}";
                        XConfig.SaveValueToFile("SuperMultiGame/Config", saveKey, defaultConfig);
                        AddLog($"已为游戏 {game.GameName} 创建并保存默认配置");
                        AddGameLog(game.GameId, "已创建并保存默认配置");
                    }
                }
            }
            catch (Exception ex)
            {
                AddLog($"为游戏 {game.GameName} 创建默认配置失败: {ex.Message}");
                AddGameLog(game.GameId, $"创建默认配置失败: {ex.Message}");
            }
        }
    }
}