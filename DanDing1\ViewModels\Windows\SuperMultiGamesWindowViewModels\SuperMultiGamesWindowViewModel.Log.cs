﻿/*
 * 文件名: SuperMultiGamesWindowViewModel.Log.cs
 * 职责描述: 提供日志记录功能
 * 该文件实现了系统日志和游戏日志的记录、管理和显示功能
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Windows.Gaming.Input;

namespace DanDing1.ViewModels.Windows.SuperMultiGamesWindowViewModels
{
    /// <summary>
    /// 游戏日志条目类，每个游戏对应一个日志条目
    /// </summary>
    public partial class GameLogItem : ObservableObject
    {
        [ObservableProperty]
        private string _gameName;

        [ObservableProperty]
        private string _logContent = "";

        [ObservableProperty]
        private int _gameId;

        [ObservableProperty]
        private bool _isSelected;

        public GameLogItem(string gameName, int gameId)
        {
            GameName = gameName;
            GameId = gameId;
            LogContent = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 创建游戏 {GameName}\n";
            IsSelected = false;
        }

        public void AddLog(string message)
        {
            LogContent += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}\n";
            // 如果日志太长，则只保留最新的50条记录
            string[] logLines = LogContent.Split('\n');
            if (logLines.Length > 50)
            {
                LogContent = string.Join("\n", logLines.Skip(logLines.Length - 50));
            }
        }
    }
}