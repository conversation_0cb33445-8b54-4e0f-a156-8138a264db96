﻿<Page
    x:Class="DanDing1.Views.Pages.Game4Page"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:local="clr-namespace:DanDing1.Views.Pages"
    xmlns:localControl="clr-namespace:DanDing1.Views.UserControls"
    xmlns:localcs="clr-namespace:DanDing1.ViewModels.Pages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:DanDing1.Models"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="Game4Page"
    d:DataContext="{d:DesignInstance local:Game4Page,
                                     IsDesignTimeCreatable=False}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
    ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    ScrollViewer.CanContentScroll="False"
    mc:Ignorable="d">

    <localControl:GamePageControl x:Name="GameView" />
</Page>