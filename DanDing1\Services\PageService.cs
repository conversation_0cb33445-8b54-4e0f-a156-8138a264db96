using Wpf.Ui;
using Wpf.Ui.Abstractions;

namespace DanDing1.Services
{
    /// <summary>
    /// Service that provides pages for navigation.
    /// </summary>
    public class PageService : INavigationViewPageProvider
    {
        /// <summary>
        /// Service which provides the instances of pages.
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// Creates new instance and attaches the <see cref="IServiceProvider"/>.
        /// </summary>
        public PageService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// 获取泛型页面
        /// </summary>
        public T? GetPage<T>()
            where T : class
        {
            if (!typeof(System.Windows.FrameworkElement).IsAssignableFrom(typeof(T)))
                throw new InvalidOperationException("The page should be a WPF control.");

            return (T?)_serviceProvider.GetService(typeof(T));
        }

        /// <summary>
        /// 获取指定类型的页面
        /// </summary>
        public object? GetPage(Type pageType)
        {
            if (!typeof(System.Windows.FrameworkElement).IsAssignableFrom(pageType))
                throw new InvalidOperationException("The page should be a WPF control.");

            return _serviceProvider.GetService(pageType);
        }
    }
}
