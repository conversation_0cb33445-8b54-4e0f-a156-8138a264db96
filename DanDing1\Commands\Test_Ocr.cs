using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using XHelper;
using XHelper.Models;

namespace DanDing1.Commands
{
    /// <summary>
    /// OCR测试命令类
    /// </summary>
    internal class Test_Ocr : BaseCommand
    {
        // 定义测试图片URL
        private const string NumberImageUrl = "https://image.180402.xyz/2025/03/27/67e4b303ccc1e.bmp";
        private const string TextImageUrl = "https://image.180402.xyz/2025/03/27/67e4b308b9327.bmp";

        // 临时文件夹路径
        private readonly string _tempFolder;

        public Test_Ocr()
        {
            _tempFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "temp");
            if (!Directory.Exists(_tempFolder))
            {
                Directory.CreateDirectory(_tempFolder);
            }
        }

        public override void Execute(string[] parameters)
        {
            if (!ValidateParameterCount(parameters, 3))
                return;

            // 获取子命令
            string subCommand = parameters[2].ToLower();

            switch (subCommand)
            {
                case "数字":
                case "number":
                    TestOcrNumber(parameters);
                    break;

                case "文本":
                case "text":
                    TestOcrText(parameters);
                    break;

                case "文本pro":
                case "textpro":
                case "pro":
                    TestOcrTextPro(parameters);
                    break;

                case "auto":
                case "自动":
                case "全部":
                    XLogger.Info("开始执行OCR自动测试...");
                    _ = Task.Run(TestOcrAuto);
                    break;

                default:
                    PrintHelp();
                    break;
            }
        }

        /// <summary>
        /// 处理路径，移除引号
        /// </summary>
        private string ProcessPath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return string.Empty;

            // 移除路径两端的引号
            path = path.Trim('"', '\'');

            return path;
        }

        /// <summary>
        /// 测试数字识别
        /// </summary>
        private void TestOcrNumber(string[] parameters)
        {
            if (!ValidateParameterCount(parameters, 4))
            {
                XLogger.Error("请提供图片路径，格式：test ocr 数字 [图片路径]");
                return;
            }

            string imagePath = ProcessPath(parameters[3]);
            if (!File.Exists(imagePath))
            {
                XLogger.Error($"图片文件不存在：{imagePath}");
                return;
            }

            XLogger.Info($"开始测试数字OCR识别，图片路径：{imagePath}");

            try
            {
                string result = XOcr.Local_Ocr_Number(imagePath);
                XLogger.Info($"数字OCR识别结果：{result}");
            }
            catch (Exception ex)
            {
                XLogger.Error($"数字OCR识别异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 测试文本识别
        /// </summary>
        private void TestOcrText(string[] parameters)
        {
            if (!ValidateParameterCount(parameters, 4))
            {
                XLogger.Error("请提供图片路径，格式：test ocr 文本 [图片路径]");
                return;
            }

            string imagePath = ProcessPath(parameters[3]);
            if (!File.Exists(imagePath))
            {
                XLogger.Error($"图片文件不存在：{imagePath}");
                return;
            }

            XLogger.Info($"开始测试文本OCR识别，图片路径：{imagePath}");

            try
            {
                // 读取图片为字节数组
                byte[] imageBytes = File.ReadAllBytes(imagePath);

                // 执行OCR识别
                string result = XOcr.Local_Ocr_String(imageBytes, blocks =>
                {
                    XLogger.Debug($"识别到 {blocks.Count} 个文本块：");
                    foreach (var block in blocks)
                    {
                        XLogger.Debug($"文本：{block.Text}，位置：{block.Rect}");
                    }
                });

                XLogger.Info($"文本OCR识别结果：{result}");
            }
            catch (Exception ex)
            {
                XLogger.Error($"文本OCR识别异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 测试Pro版本文本识别
        /// </summary>
        private void TestOcrTextPro(string[] parameters)
        {
            if (!ValidateParameterCount(parameters, 4))
            {
                XLogger.Error("请提供图片路径，格式：test ocr pro [图片路径]");
                return;
            }

            string imagePath = ProcessPath(parameters[3]);
            if (!File.Exists(imagePath))
            {
                XLogger.Error($"图片文件不存在：{imagePath}");
                return;
            }

            XLogger.Info($"开始测试Pro版本文本OCR识别，图片路径：{imagePath}");

            // 检查Pro版本OCR程序是否存在
            string ocrProPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "runtimes", "PaddleOCR_cpp.exe");
            if (!File.Exists(ocrProPath))
            {
                XLogger.Error($"Pro版本OCR程序不存在: {ocrProPath}");
                return;
            }

            // 检查路径是否包含空格
            if (ocrProPath.Contains(" "))
            {
                XLogger.Error($"Pro版本OCR程序路径包含空格，这可能导致程序崩溃: {ocrProPath}");
                XLogger.Error("请将程序移动到不包含空格的路径下");
            }

            // 检查文件是否存在
            XLogger.Debug($"Pro版本OCR程序路径: {ocrProPath}");
            XLogger.Debug($"图片路径: {imagePath}");

            try
            {
                string result = XOcr.Local_Ocr_String_Pro(imagePath, blocks =>
                {
                    if (blocks != null && blocks.Count > 0)
                    {
                        XLogger.Debug($"识别到 {blocks.Count} 个文本块：");
                        foreach (var block in blocks)
                        {
                            XLogger.Debug($"文本：{block.Text}，位置：{block.Rect}");
                        }
                    }
                    else
                    {
                        XLogger.Debug("未识别到任何文本块");
                    }
                });

                if (result == "-1")
                {
                    XLogger.Error("Pro版本OCR识别失败或未安装Pro版本");

                    // 检查错误代码 -1073741819 (0xC0000005)，这是访问冲突错误
                    XLogger.Error("错误代码 -1073741819 通常表示程序访问冲突 (0xC0000005)");
                    XLogger.Error("可能原因：");
                    XLogger.Error("1. PaddleOCR_cpp.exe 与当前系统不兼容");
                    XLogger.Error("2. 缺少必要的运行库，如 Visual C++ Redistributable");
                    XLogger.Error("3. 运行时内存不足");
                    XLogger.Error("建议解决方案：");
                    XLogger.Error("1. 重新安装最新版本的 Visual C++ Redistributable");
                    XLogger.Error("2. 检查 PaddleOCR_cpp.exe 是否为正确版本");
                }
                else
                {
                    XLogger.Info($"Pro版本OCR识别结果：{result}");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"Pro版本OCR识别异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 自动测试所有OCR功能
        /// </summary>
        private async Task TestOcrAuto()
        {
            XLogger.Info("=== OCR自动测试开始 ===");
            bool allTestsPassed = true;

            try
            {
                // 提前下载所有需要的图片
                XLogger.Info("正在准备测试资源...");

                // 下载数字测试图片
                string numberImagePath = await DownloadImage(NumberImageUrl, "ocr_number_test.bmp");
                if (string.IsNullOrEmpty(numberImagePath))
                {
                    XLogger.Error("下载数字测试图片失败，自动测试中止");
                    return;
                }

                // 下载文本测试图片（文本和文本Pro共用）
                string textImagePath = await DownloadImage(TextImageUrl, "ocr_text_test.bmp");
                if (string.IsNullOrEmpty(textImagePath))
                {
                    XLogger.Error("下载文本测试图片失败，自动测试中止");
                    return;
                }

                XLogger.Info("测试资源准备完成，开始执行测试...");

                // 测试数字OCR
                allTestsPassed &= await TestNumberOcrAuto(numberImagePath);

                // 测试文本OCR
                allTestsPassed &= await TestTextOcrAuto(textImagePath);

                // 测试Pro版本文本OCR (使用同一张文本图片)
                allTestsPassed &= await TestTextProOcrAuto(textImagePath);

                // 输出总结报告
                XLogger.Info("=== OCR自动测试报告 ===");
                if (allTestsPassed)
                {
                    XLogger.Info_Green("所有测试项目通过！");
                }
                else
                {
                    XLogger.Error("部分测试项目未通过，请查看详细日志。");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"OCR自动测试异常: {ex.Message}");
                XLogger.Error("自动测试中止。");
            }

            XLogger.Info("=== OCR自动测试结束 ===");
        }

        /// <summary>
        /// 自动测试数字OCR功能
        /// </summary>
        /// <param name="imagePath">已下载的图片路径</param>
        private async Task<bool> TestNumberOcrAuto(string imagePath)
        {
            XLogger.Info("--- 数字OCR测试 ---");

            try
            {
                XLogger.Info($"使用图片进行数字OCR测试: {imagePath}");

                // 执行OCR识别
                string result = XOcr.Local_Ocr_Number(imagePath);
                XLogger.Info($"数字OCR识别结果: {result}");

                // 验证结果
                bool passed = result.Contains("24");
                if (passed)
                {
                    XLogger.Info_Green("数字OCR测试通过: 结果包含预期数字'24'");
                }
                else
                {
                    XLogger.Error("数字OCR测试失败: 结果未包含预期数字'24'");
                }

                return passed;
            }
            catch (Exception ex)
            {
                XLogger.Error($"数字OCR自动测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 自动测试文本OCR功能
        /// </summary>
        /// <param name="imagePath">已下载的图片路径</param>
        private async Task<bool> TestTextOcrAuto(string imagePath)
        {
            XLogger.Info("--- 文本OCR测试 ---");

            try
            {
                XLogger.Info($"使用图片进行文本OCR测试: {imagePath}");

                // 读取图片为字节数组
                byte[] imageBytes = File.ReadAllBytes(imagePath);

                // 执行OCR识别
                string result = XOcr.Local_Ocr_String(imageBytes, blocks =>
                {
                    XLogger.Debug($"识别到 {blocks.Count} 个文本块：");
                    foreach (var block in blocks)
                    {
                        XLogger.Debug($"文本：{block.Text}，位置：{block.Rect}");
                    }
                });

                XLogger.Info($"文本OCR识别结果: {result}");

                // 验证结果
                bool passed = VerifyTextResult(result);
                if (passed)
                {
                    XLogger.Info_Green("文本OCR测试通过: 结果包含预期文本");
                }
                else
                {
                    XLogger.Error("文本OCR测试失败: 结果未包含所有预期文本");
                }

                return passed;
            }
            catch (Exception ex)
            {
                XLogger.Error($"文本OCR自动测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 自动测试Pro版本文本OCR功能
        /// </summary>
        /// <param name="imagePath">已下载的图片路径</param>
        private async Task<bool> TestTextProOcrAuto(string imagePath)
        {
            XLogger.Info("--- 文本Pro OCR测试 ---");

            try
            {
                // 检查Pro版本OCR程序是否存在
                string ocrProPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "runtimes", "PaddleOCR_cpp.exe");
                if (!File.Exists(ocrProPath))
                {
                    XLogger.Error($"Pro版本OCR程序不存在: {ocrProPath}");
                    return false;
                }

                // 检查路径是否包含空格
                if (ocrProPath.Contains(" "))
                {
                    XLogger.Warn($"Pro版本OCR程序路径包含空格，可能导致程序崩溃: {ocrProPath}");
                }

                XLogger.Info($"使用图片进行文本Pro OCR测试: {imagePath}");

                // 执行OCR识别
                string result = XOcr.Local_Ocr_String_Pro(imagePath, blocks =>
                {
                    if (blocks != null && blocks.Count > 0)
                    {
                        XLogger.Debug($"识别到 {blocks.Count} 个文本块：");
                        foreach (var block in blocks)
                        {
                            XLogger.Debug($"文本：{block.Text}，位置：{block.Rect}");
                        }
                    }
                    else
                    {
                        XLogger.Debug("未识别到任何文本块");
                    }
                });

                if (result == "-1")
                {
                    XLogger.Error("Pro版本OCR识别失败或未安装Pro版本");
                    return false;
                }

                XLogger.Info($"文本Pro OCR识别结果: {result}");

                // 验证结果
                bool passed = VerifyTextResult(result);
                if (passed)
                {
                    XLogger.Info_Green("文本Pro OCR测试通过: 结果包含预期文本");
                }
                else
                {
                    XLogger.Error("文本Pro OCR测试失败: 结果未包含所有预期文本");
                }

                return passed;
            }
            catch (Exception ex)
            {
                XLogger.Error($"文本Pro OCR自动测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证文本OCR结果是否包含预期内容
        /// </summary>
        private bool VerifyTextResult(string result)
        {
            if (string.IsNullOrEmpty(result))
                return false;

            // 定义要检查的关键词
            string[] expectedTexts = { "第一行", "第二行", "行文本", "fafafa" };

            // 验证结果中是否包含所有关键词
            foreach (var text in expectedTexts)
            {
                if (!result.Contains(text))
                {
                    XLogger.Warn($"结果中未找到预期文本: '{text}'");
                    return false;
                }
                else
                {
                    XLogger.Debug($"结果中找到预期文本: '{text}'");
                }
            }

            return true;
        }

        /// <summary>
        /// 下载图片到临时目录
        /// </summary>
        /// <param name="url">图片URL</param>
        /// <param name="fileName">保存的文件名</param>
        /// <returns>下载后的图片路径</returns>
        private async Task<string> DownloadImage(string url, string fileName)
        {
            string filePath = Path.Combine(_tempFolder, fileName);

            try
            {
                // 检查文件是否已存在
                if (File.Exists(filePath))
                {
                    XLogger.Debug($"图片已存在: {filePath}");
                    return filePath;
                }

                XLogger.Debug($"开始下载图片: {url}");
                using (HttpClient client = new HttpClient())
                {
                    // 设置超时
                    client.Timeout = TimeSpan.FromSeconds(30);

                    // 下载图片
                    byte[] imageBytes = await client.GetByteArrayAsync(url);

                    // 保存图片
                    await File.WriteAllBytesAsync(filePath, imageBytes);

                    XLogger.Debug($"图片下载完成，保存到: {filePath}");
                    return filePath;
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"下载图片失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 打印帮助信息
        /// </summary>
        private void PrintHelp()
        {
            XLogger.Info("OCR测试命令使用说明：");
            XLogger.Info("1. 测试数字识别：test ocr 数字 [图片路径]");
            XLogger.Info("2. 测试文本识别：test ocr 文本 [图片路径]");
            XLogger.Info("3. 测试Pro版本文本识别：test ocr pro [图片路径]");
            XLogger.Info("4. 自动测试全部功能：test ocr auto");
        }
    }
}