using OpenCvSharp;
using System;

namespace XHelper.OCR
{
    /// <summary>
    /// 图像处理接口
    /// </summary>
    internal interface IImageProcessor
    {
        /// <summary>
        /// 从文件路径优化图像
        /// </summary>
        /// <param name="imagePath">图像文件路径</param>
        /// <returns>优化后的图像字节数组</returns>
        byte[] OptimizeImage(string imagePath);

        /// <summary>
        /// 从字节数组优化图像
        /// </summary>
        /// <param name="imageData">图像字节数组</param>
        /// <returns>优化后的图像字节数组</returns>
        byte[] OptimizeImage(byte[] imageData);

        /// <summary>
        /// 设置是否保存临时优化图像
        /// </summary>
        /// <param name="save">是否保存</param>
        void SetSaveTempImages(bool save);
        
        /// <summary>
        /// 获取上边框尺寸
        /// </summary>
        /// <returns>像素值</returns>
        int GetTopPadding();
        
        /// <summary>
        /// 获取下边框尺寸
        /// </summary>
        /// <returns>像素值</returns>
        int GetBottomPadding();
        
        /// <summary>
        /// 获取左边框尺寸
        /// </summary>
        /// <returns>像素值</returns>
        int GetLeftPadding();
        
        /// <summary>
        /// 获取右边框尺寸
        /// </summary>
        /// <returns>像素值</returns>
        int GetRightPadding();
    }
}