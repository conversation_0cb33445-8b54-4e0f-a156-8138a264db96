using System.Text;
using System.Text.Json;

namespace DanDing1.Helpers;

/// <summary>
/// 提供本地数据持久化存储的静态工具类
/// </summary>
public static class LocalStorage
{
    private static readonly string AppDataPath;

    static LocalStorage()
    {
        // 获取应用数据根目录
        var roamingPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        AppDataPath = Path.Combine(roamingPath, "DanDing1");

        // 确保目录存在
        if (!Directory.Exists(AppDataPath))
        {
            Directory.CreateDirectory(AppDataPath);
        }
    }

    /// <summary>
    /// 保存文本数据
    /// </summary>
    public static void SaveText(string fileName, string content)
    {
        var filePath = Path.Combine(AppDataPath, fileName);
        File.WriteAllText(filePath, content, Encoding.UTF8);
    }

    /// <summary>
    /// 读取文本数据
    /// </summary>
    public static string? LoadText(string fileName)
    {
        var filePath = Path.Combine(AppDataPath, fileName);
        return File.Exists(filePath) ? File.ReadAllText(filePath, Encoding.UTF8) : null;
    }

    /// <summary>
    /// 保存二进制数据
    /// </summary>
    public static void SaveBytes(string fileName, byte[] data)
    {
        var filePath = Path.Combine(AppDataPath, fileName);
        File.WriteAllBytes(filePath, data);
    }

    /// <summary>
    /// 读取二进制数据
    /// </summary>
    public static byte[]? LoadBytes(string fileName)
    {
        var filePath = Path.Combine(AppDataPath, fileName);
        return File.Exists(filePath) ? File.ReadAllBytes(filePath) : null;
    }

    /// <summary>
    /// 将对象序列化为JSON并保存
    /// </summary>
    public static void SaveJson<T>(string fileName, T data, JsonSerializerOptions? options = null)
    {
        var json = JsonSerializer.Serialize(data, options);
        SaveText(fileName, json);
    }

    /// <summary>
    /// 从JSON文件中读取并反序列化为对象
    /// </summary>
    public static T? LoadJson<T>(string fileName, JsonSerializerOptions? options = null)
    {
        var json = LoadText(fileName);
        return json == null ? default : JsonSerializer.Deserialize<T>(json, options);
    }

    /// <summary>
    /// 删除指定文件
    /// </summary>
    public static bool DeleteFile(string fileName)
    {
        var filePath = Path.Combine(AppDataPath, fileName);
        if (File.Exists(filePath))
        {
            File.Delete(filePath);
            return true;
        }
        return false;
    }

    /// <summary>
    /// 检查文件是否存在
    /// </summary>
    public static bool FileExists(string fileName)
    {
        var filePath = Path.Combine(AppDataPath, fileName);
        return File.Exists(filePath);
    }

    /// <summary>
    /// 获取文件的完整路径
    /// </summary>
    public static string GetFilePath(string fileName)
    {
        return Path.Combine(AppDataPath, fileName);
    }

    /// <summary>
    /// 保存整数数据
    /// </summary>
    public static void SaveInt(string fileName, int value)
    {
        SaveText(fileName, value.ToString());
    }

    /// <summary>
    /// 读取整数数据，如果文件不存在或转换失败则返回默认值
    /// </summary>
    public static int LoadInt(string fileName, int defaultValue = 0)
    {
        var text = LoadText(fileName);
        return text != null && int.TryParse(text, out var value) ? value : defaultValue;
    }
}