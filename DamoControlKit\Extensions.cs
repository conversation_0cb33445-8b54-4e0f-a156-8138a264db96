﻿using DamoControlKit.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DamoControlKit
{
    public static class Extensions
    {
        /// <summary>
        /// 获取范围中心点坐标
        /// </summary>
        /// <param name="Pos"></param>
        /// <returns></returns>
        public static Point GetCenterPoint(this Position Pos)
        {
            double cx = (Pos.X + Pos.X1) / 2.0;
            double cy = (Pos.Y + Pos.Y1) / 2.0;
            return new((int)cx, (int)cy);
        }
    }
}