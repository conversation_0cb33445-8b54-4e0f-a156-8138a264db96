﻿namespace DanDing1.Models.Ws_Models
{
    /// <summary>
    /// 脚本任务结束原因枚举
    /// </summary>
    public enum ScriptTaskEndReason
    {
        /// <summary>
        /// 正常完成
        /// </summary>
        Normal = 0,

        /// <summary>
        /// 用户取消
        /// </summary>
        UserCancelled = 1,

        /// <summary>
        /// 发生错误
        /// </summary>
        Error = 2,

        /// <summary>
        /// 系统重启
        /// </summary>
        SystemRestart = 3,

        /// <summary>
        /// 条件未满足
        /// </summary>
        ConditionNotMet = 4
    }

    /// <summary>
    /// 脚本回调数据模型，用于传递脚本执行结束的相关信息
    /// </summary>
    internal class ScriptCallBackDataModel
    {
        /// <summary>
        /// 任务执行结果，用于区分不同的结束原因
        /// </summary>
        public ScriptTaskEndReason EndReason { get; set; } = ScriptTaskEndReason.Normal;

        /// <summary>
        /// 错误信息（如果有）
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 是否用户取消
        /// </summary>
        public bool IsCancel { get; set; }

        /// <summary>
        /// 脚本ID
        /// </summary>
        public int ScriptId { get; set; }
    }
}