using System;
using System.Globalization;
using System.Windows.Data;

namespace DanDing1.Helpers
{
    /// <summary>
    /// 将null值转换为布尔值的转换器
    /// null -> false, 非null -> true
    /// </summary>
    public class NullToBoolConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value != null;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}