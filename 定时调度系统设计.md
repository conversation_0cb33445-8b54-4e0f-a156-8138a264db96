# 蛋定定时调度系统功能设计文档

## 1. 项目概述

### 1.1 项目目标
开发一套独立的定时调度系统，与现有超级多开和常规4开功能完全分离，实现模拟器任务的精确定时执行、并发控制和资源管理。该系统旨在增强蛋定软件的自动化能力，使用户能够设置复杂的定时任务，实现7×24小时无人值守运行。

### 1.2 功能需求概述
- 独立调度管理所有定时任务
- 精确控制任务执行时间（精确到秒）
- 自动管理模拟器（MuMu）生命周期
- 控制模拟器并发数量
- 支持任务队列和并行处理
- 提供友好的配置界面
- 与现有脚本引擎和模拟器管理模块无缝集成

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────────┐    ┌──────────────────┐
│  定时调度系统UI界面  │◄───►│  调度器核心      │
└─────────────────────┘    │  SchedulerCore   │
                           └────────┬─────────┘
                                    │
                           ┌────────▼─────────┐
                           │  模拟器管理器     │
                           │  EmulatorManager │
                           └────────┬─────────┘
                                    │
            ┌───────────────────────┼───────────────────────┐
            │                       │                       │
    ┌───────▼────────┐     ┌────────▼─────────┐     ┌──────▼──────────┐
    │  模拟器实例1    │     │  模拟器实例2      │     │  模拟器实例N    │
    │  Emulator      │     │  Emulator        │     │  Emulator       │
    └───────┬────────┘     └────────┬─────────┘     └──────┬──────────┘
            │                       │                       │
    ┌───────▼────────┐     ┌────────▼─────────┐     ┌──────▼──────────┐
    │  任务队列1      │     │  任务队列2        │     │  任务队列N      │
    │  TaskQueue     │     │  TaskQueue       │     │  TaskQueue      │
    └────────────────┘     └──────────────────┘     └─────────────────┘
```

### 2.2 核心组件职责

#### 2.2.1 调度器核心 (SchedulerCore)
- 全局时间管理与监控
- 根据时间点触发模拟器启动
- 控制并发模拟器数量
- 维护全局配置信息
- 管理定时任务的优先级

#### 2.2.2 模拟器管理器 (EmulatorManager) 
- 负责模拟器生命周期管理（基于现有MuMu模块）
- 启动、关闭模拟器
- 监控模拟器状态
- 资源回收管理
- 模拟器任务分配

#### 2.2.3 模拟器实例 (Emulator)
- 封装对单个模拟器的操作
- 与现有ScriptEngine/MuMu交互
- 状态监控与报告
- 任务执行结果反馈

#### 2.2.4 任务队列 (TaskQueue)
- 管理单个模拟器内的任务序列
- 任务顺序执行控制
- 任务完成状态监控
- 任务异常处理与重试

#### 2.2.5 任务执行器 (TaskExecutor)
- 集成现有ScriptEngine/Script.cs
- 执行具体任务逻辑
- 与游戏交互
- 任务执行状态反馈

## 3. 数据模型设计

### 3.1 系统配置模型
```csharp
public class SchedulerConfig
{
    /// <summary>
    /// 最大并发模拟器数量
    /// </summary>
    public int MaxConcurrentEmulators { get; set; } = 4;

    /// <summary>
    /// 默认任务超时时间(秒)
    /// </summary>
    public int DefaultTaskTimeout { get; set; } = 3600;

    /// <summary>
    /// 空闲自动关闭时间(秒)
    /// </summary>
    public int AutoShutdownIdleTime { get; set; } = 300;

    /// <summary>
    /// 是否启用日志
    /// </summary>
    public bool EnableLogging { get; set; } = true;

    /// <summary>
    /// 日志级别
    /// </summary>
    public LogLevel LogLevel { get; set; } = LogLevel.Info;

    /// <summary>
    /// 是否启用任务失败重试
    /// </summary>
    public bool EnableTaskRetry { get; set; } = true;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;
}
```

### 3.2 模拟器配置模型
```csharp
public class EmulatorConfig
{
    /// <summary>
    /// 模拟器标识
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 模拟器名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 模拟器路径
    /// </summary>
    public string Path { get; set; }

    /// <summary>
    /// 任务队列
    /// </summary>
    public List<TaskConfig> TaskQueue { get; set; } = new List<TaskConfig>();

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 启动参数
    /// </summary>
    public string StartupParameters { get; set; } = "";

    /// <summary>
    /// 模拟器优先级（数值越高优先级越高）
    /// </summary>
    public int Priority { get; set; } = 0;
}
```

### 3.3 任务配置模型
```csharp
public class TaskConfig
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 任务名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 脚本路径
    /// </summary>
    public string ScriptPath { get; set; }

    /// <summary>
    /// 调度类型(单次/每日/每周等)
    /// </summary>
    public ScheduleType ScheduleType { get; set; }

    /// <summary>
    /// 调度时间(HH:mm:ss)
    /// </summary>
    public string ScheduleTime { get; set; }

    /// <summary>
    /// 调度日期(适用于单次任务)
    /// </summary>
    public string ScheduleDate { get; set; }

    /// <summary>
    /// 调度星期(适用于每周任务)
    /// </summary>
    public List<DayOfWeek> WeekDays { get; set; } = new List<DayOfWeek>();

    /// <summary>
    /// 重复次数(-1表示无限)
    /// </summary>
    public int RepeatCount { get; set; } = 1;

    /// <summary>
    /// 任务参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 任务优先级
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 最大执行时间(秒)，超时将被强制终止
    /// </summary>
    public int Timeout { get; set; } = 3600;

    /// <summary>
    /// 失败后是否重试
    /// </summary>
    public bool RetryOnFailure { get; set; } = true;

    /// <summary>
    /// 失败重试间隔(秒)
    /// </summary>
    public int RetryInterval { get; set; } = 300;
}

public enum ScheduleType
{
    /// <summary>
    /// 单次执行
    /// </summary>
    OneTime,
    
    /// <summary>
    /// 每日执行
    /// </summary>
    Daily,
    
    /// <summary>
    /// 每周执行
    /// </summary>
    Weekly,
    
    /// <summary>
    /// 间隔执行(每隔一段时间执行一次)
    /// </summary>
    Interval,
    
    /// <summary>
    /// 循环执行(执行完立即再次执行)
    /// </summary>
    Continuous
}
```

## 4. 接口设计

### 4.1 调度器接口
```csharp
public interface ISchedulerCore
{
    /// <summary>
    /// 初始化调度器
    /// </summary>
    Task InitializeAsync();
    
    /// <summary>
    /// 启动调度器
    /// </summary>
    Task StartAsync();
    
    /// <summary>
    /// 停止调度器
    /// </summary>
    Task StopAsync();
    
    /// <summary>
    /// 添加模拟器配置
    /// </summary>
    Task AddEmulatorConfigAsync(EmulatorConfig config);
    
    /// <summary>
    /// 更新模拟器配置
    /// </summary>
    Task UpdateEmulatorConfigAsync(EmulatorConfig config);
    
    /// <summary>
    /// 删除模拟器配置
    /// </summary>
    Task RemoveEmulatorConfigAsync(string emulatorId);
    
    /// <summary>
    /// 获取所有模拟器配置
    /// </summary>
    Task<List<EmulatorConfig>> GetEmulatorConfigsAsync();
    
    /// <summary>
    /// 获取系统配置
    /// </summary>
    Task<SchedulerConfig> GetSystemConfigAsync();
    
    /// <summary>
    /// 更新系统配置
    /// </summary>
    Task UpdateSystemConfigAsync(SchedulerConfig config);
    
    /// <summary>
    /// 获取调度器状态
    /// </summary>
    Task<SchedulerStatus> GetStatusAsync();
    
    /// <summary>
    /// 立即执行指定任务
    /// </summary>
    Task<bool> ExecuteTaskNowAsync(string emulatorId, string taskId);
    
    /// <summary>
    /// 获取任务执行历史
    /// </summary>
    Task<List<TaskExecutionRecord>> GetTaskHistoryAsync(DateTime startDate, DateTime endDate);
}

public class SchedulerStatus
{
    /// <summary>
    /// 调度器是否运行中
    /// </summary>
    public bool Running { get; set; }
    
    /// <summary>
    /// 活跃模拟器数量
    /// </summary>
    public int ActiveEmulators { get; set; }
    
    /// <summary>
    /// 即将执行的任务列表
    /// </summary>
    public List<ScheduledTaskInfo> NextScheduledTasks { get; set; }
    
    /// <summary>
    /// 调度器启动时间
    /// </summary>
    public DateTime SchedulerStartTime { get; set; }
    
    /// <summary>
    /// 系统资源使用情况
    /// </summary>
    public SystemResourceInfo ResourceInfo { get; set; }
}
```

### 4.2 模拟器管理接口
```csharp
public interface IEmulatorManager
{
    /// <summary>
    /// 启动模拟器
    /// </summary>
    Task<bool> StartEmulatorAsync(string emulatorId);
    
    /// <summary>
    /// 关闭模拟器
    /// </summary>
    Task<bool> StopEmulatorAsync(string emulatorId);
    
    /// <summary>
    /// 获取模拟器状态
    /// </summary>
    Task<EmulatorStatus> GetEmulatorStatusAsync(string emulatorId);
    
    /// <summary>
    /// 获取所有活跃模拟器
    /// </summary>
    Task<List<string>> GetActiveEmulatorsAsync();
    
    /// <summary>
    /// 启动任务执行
    /// </summary>
    Task<bool> ExecuteTaskAsync(string emulatorId, string taskId);
    
    /// <summary>
    /// 停止任务执行
    /// </summary>
    Task<bool> StopTaskAsync(string emulatorId, string taskId);
    
    /// <summary>
    /// 获取模拟器资源使用情况
    /// </summary>
    Task<EmulatorResourceInfo> GetResourceUsageAsync(string emulatorId);
}

public class EmulatorStatus
{
    /// <summary>
    /// 模拟器ID
    /// </summary>
    public string Id { get; set; }
    
    /// <summary>
    /// 是否运行中
    /// </summary>
    public bool Running { get; set; }
    
    /// <summary>
    /// 当前执行任务
    /// </summary>
    public string CurrentTask { get; set; }
    
    /// <summary>
    /// 启动时间
    /// </summary>
    public DateTime StartTime { get; set; }
    
    /// <summary>
    /// 任务开始时间
    /// </summary>
    public DateTime TaskStartTime { get; set; }
    
    /// <summary>
    /// 任务队列
    /// </summary>
    public List<TaskInfo> TaskQueue { get; set; }
    
    /// <summary>
    /// 模拟器健康状态
    /// </summary>
    public EmulatorHealthStatus HealthStatus { get; set; }
}
```

## 5. UI设计

### 5.1 主界面
![调度系统主界面布局](https://placeholder-image.com/scheduler-main-ui.png)

- 显示所有配置的模拟器及其任务
- 显示下一个将执行的任务及时间
- 显示当前活跃模拟器数量
- 提供系统配置入口
- 提供添加/编辑模拟器配置入口
- 状态概览区域（系统运行状态、资源使用率）
- 任务历史记录查看入口

### 5.2 模拟器配置界面
- 模拟器基本信息设置
  - 名称、路径、启动参数
  - 优先级设置
  - 资源限制设置
- 任务队列管理(添加/编辑/删除/排序)
- 启用/禁用控制
- 模拟器性能配置（内存分配、CPU核心数等）

### 5.3 任务配置界面
- 任务基本信息设置
- 时间调度设置
   - 精确到时分秒的时间选择器
   - 调度类型选择(单次/每日/每周/间隔/循环)
   - 重复次数设置
- 任务参数配置（与现有ScriptEngine参数系统集成）
- 启用/禁用控制
- 超时设置
- 失败处理策略配置（重试次数、重试间隔）

### 5.4 系统设置界面
- 最大并发模拟器数量设置
- 默认任务超时设置
- 空闲自动关闭时间设置
- 日志级别设置
- 资源监控阈值设置
- 通知设置（任务完成、失败通知）
- 数据备份与恢复选项

### 5.5 任务历史与统计界面
- 任务执行历史查询（支持时间段筛选）
- 任务执行统计图表（成功率、平均执行时间等）
- 模拟器使用统计
- 资源使用趋势图

## 6. 与现有系统集成

### 6.1 与ScriptEngine集成
- 复用现有DDScript类实现任务执行
- 集成现有任务参数（DDBuilder）系统
- 沿用Script.cs中的任务回调机制

### 6.2 与MuMu模拟器集成
- 利用现有的模拟器路径探测功能
- 复用模拟器启动与管理代码
- 增强模拟器状态监测能力

### 6.3 与GlobalData集成
- 在GlobalData中添加定时调度系统实例
- 共享全局配置和用户设置
- 保持数据一致性

## 7. 任务分解

### 7.1 基础架构开发
- [ ] 设计并实现SchedulerCore核心类
- [ ] 设计并实现EmulatorManager类（集成现有MuMu模块）
- [ ] 设计并实现Emulator封装类
- [ ] 设计并实现TaskQueue管理类
- [ ] 设计并实现TaskExecutor执行类（集成现有ScriptEngine）
- [ ] 构建基础数据模型及接口

### 7.2 时间管理模块
- [ ] 实现精确定时触发机制
- [ ] 实现各种调度类型(单次/每日/每周/间隔/循环)
- [ ] 实现任务排队与优先级处理
- [ ] 开发时间冲突检测与解决策略

### 7.3 模拟器控制模块
- [ ] 集成现有MuMu模块
- [ ] 实现模拟器启动与关闭控制
- [ ] 实现模拟器状态监控
- [ ] 实现并发控制与资源管理
- [ ] 开发模拟器异常恢复机制

### 7.4 任务执行模块
- [ ] 集成现有ScriptEngine模块
- [ ] 实现任务脚本加载与执行
- [ ] 实现任务执行状态监控
- [ ] 实现任务超时与错误处理
- [ ] 开发任务重试机制

### 7.5 UI界面开发
- [ ] 设计并实现主界面
- [ ] 设计并实现模拟器配置界面
- [ ] 设计并实现任务配置界面
- [ ] 设计并实现系统设置界面
- [ ] 设计并实现任务历史与统计界面
- [ ] 实现数据绑定与实时更新

### 7.6 数据持久化
- [ ] 实现配置数据持久化存储
- [ ] 实现任务执行历史记录
- [ ] 实现配置导入/导出功能
- [ ] 实现运行时状态持久化
- [ ] 开发数据备份与恢复功能

### 7.7 测试与优化
- [ ] 单元测试各核心组件
- [ ] 集成测试调度流程
- [ ] 性能测试与优化
- [ ] UI交互体验优化
- [ ] 稳定性与异常处理测试

## 8. 里程碑计划

### 里程碑1：基础架构完成（2周）
- 完成基础架构开发任务
- 构建核心组件框架
- 实现基本接口定义
- 与现有模块初步集成

### 里程碑2：功能模块完成（3周）
- 完成时间管理模块
- 完成模拟器控制模块
- 完成任务执行模块
- 实现核心功能流程
- 完成数据持久化

### 里程碑3：UI界面完成（2周）
- 完成所有界面实现
- 实现数据绑定与交互
- 优化用户体验
- 实现实时状态更新

### 里程碑4：系统集成与测试（2周）
- 完成全面集成测试
- 修复发现的问题
- 优化性能与稳定性
- 实现异常处理与恢复机制

### 里程碑5：发布就绪版本（1周）
- 完成文档编写
- 用户手册完成
- 最终性能优化
- 发布准备就绪的系统

## 9. 风险评估与缓解策略

### 9.1 技术风险
- **风险**: 精确定时控制可能受系统负载影响
  - **缓解**: 实现时间偏差检测与补偿机制，预先启动模拟器进行准备
  
- **风险**: 模拟器启动与控制可能存在不稳定性
  - **缓解**: 增加启动超时检测，实现自动重试和健康检查机制
  
- **风险**: 多模拟器并发可能导致资源竞争
  - **缓解**: 实现资源监控与动态调节，根据系统负载调整并发数

### 9.2 功能风险
- **风险**: 任务队列复杂度可能超出预期
  - **缓解**: 设计灵活的优先级系统，支持任务动态调整与重排序
  
- **风险**: 用户配置错误可能导致系统行为异常
  - **缓解**: 实现配置验证与冲突检测，提供默认安全配置
  
- **风险**: 长时间运行稳定性未知
  - **缓解**: 实现自动恢复机制，定期进行内存与资源清理

### 9.3 集成风险
- **风险**: 与现有ScriptEngine/MuMu模块集成可能存在兼容性问题
  - **缓解**: 采用适配器模式，确保向后兼容并降低耦合度
  
- **风险**: 对现有系统的性能影响
  - **缓解**: 独立线程池管理，优化资源分配与调度算法

## 10. 技术栈选择

- **前端框架**: WPF (Windows Presentation Foundation)
- **后端语言**: C#/.NET 6.0
- **数据存储**: JSON配置文件 + SQLite（任务历史记录）
- **日志框架**: NLog（复用现有日志系统）
- **任务调度**: 自研调度器 + Quartz.NET
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **资源监控**: System.Diagnostics + 自定义性能计数器
- **线程管理**: Task Parallel Library(TPL)
- **模拟器操作**: 集成现有MuMu模块
- **状态管理**: 自研状态机 + 事件驱动架构
