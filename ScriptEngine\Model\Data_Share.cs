﻿using ScriptEngine.Tasks;
using ScriptEngine.Tasks.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Model
{
    /// <summary>
    /// 共享数据类
    /// </summary>
    public class Data_Share
    {
        /// <summary>
        /// 当前等待执行的任务
        /// </summary>
        public string MainNowTask = "";

        /// <summary>
        /// 当前执行的子任务
        /// </summary>
        public SubTask? SubTask;

        /// <summary>
        /// 当前正在运行的任务
        /// </summary>
        public BaseTask? RunningTask;
    }
}