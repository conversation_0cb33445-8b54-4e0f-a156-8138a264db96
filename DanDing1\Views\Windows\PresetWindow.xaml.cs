using System;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Wpf.Ui;
using Wpf.Ui.Controls;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// PresetWindow.xaml 的交互逻辑
    /// </summary>
    public partial class PresetWindow : FluentWindow
    {
        private readonly ContentDialogService _contentDialogService;

        public PresetWindow()
        {
            InitializeComponent();

            // 设置主题为应用程序当前主题
            Wpf.Ui.Appearance.ApplicationThemeManager.Apply(
                Wpf.Ui.Appearance.ApplicationThemeManager.GetAppTheme());

            // 创建对话框服务
            _contentDialogService = new ContentDialogService();

            // 窗口加载完成后设置对话框宿主和PresetControl
            Loaded += PresetWindow_Loaded;
        }

        private void PresetWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // 设置对话框宿主
            _contentDialogService.SetDialogHost(dialogHost);

            // 为PresetControl设置本地对话框服务
            if (FindName("presetControl") is UserControls.PresetControl presetControl)
            {
                presetControl.ViewModel.ContentDialogService = _contentDialogService;
            }
        }
    }
}