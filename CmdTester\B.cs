﻿using DamoControlKit;
using DamoControlKit.Model;
using ScriptEngine.Model;
using System.Diagnostics;
using System.Drawing;
using System.Text.Json;
using XHelper;

namespace CmdTester
{
    public class B
    {
        public B()
        {
            Console.WriteLine("TestB COTR Init...");
        }

        /// <summary>
        /// Log测试
        /// </summary>
        public void a()
        {
            XLogger.Debug("TestB a()...");

            XLogger.Write("Test Log Write");
            XLogger.Info("Test Log Info");
            XLogger.Warn("Test Log Warn");
            XLogger.Error("Test Log Error");

            XLogger.Debug("TestB a() Over...");
            XLogger.Warn("Debug模式：" + Debugger.IsAttached);
        }

        /// <summary>
        /// 验证码生成
        /// </summary>
        public void b()
        {
            EasyCaptcha captcha = new();
            captcha.CheckCaptcha("2536");
        }

        /// <summary>
        /// 二进制文件读写测试
        /// </summary>
        public void c()
        {
            Data_Pic Pic1 = new("1.bmp", "Test", File.ReadAllBytes("TestData/1.bmp"));
            Data_Pic Pic2 = new("2.bmp", "Test", File.ReadAllBytes("TestData/2.bmp"));
            Data_Pic Pic3 = new("3.bmp", "Test", File.ReadAllBytes("TestData/3.bmp"));
            Data_Pic Pic4 = new("4.bmp", "Test", File.ReadAllBytes("TestData/4.bmp"));
            Data_Pic Pic5 = new("5.bmp", "Test", File.ReadAllBytes("TestData/5.bmp"));
            Data_Pics Pics = new Data_Pics().Add([Pic1, Pic2, Pic3, Pic4, Pic5]);
            XSerializer.SerializeObjectToJsonFile(Pics, "TestData/Data.json");//写到json文件
            Data_Pics pics = XSerializer.DeserializeJsonFileToObject<Data_Pics>("TestData/Data.json");//解析json文件
            pics.WriteAllTmp();
        }

        /// <summary>
        /// 加密解密Json测试
        /// </summary>
        public void d()
        {
            Data_Pic Pic1 = new("1.bmp", "Test", File.ReadAllBytes("TestData/1.bmp"));
            Data_Pic Pic2 = new("2.bmp", "Test", File.ReadAllBytes("TestData/2.bmp"));
            Data_Pic Pic3 = new("3.bmp", "Test", File.ReadAllBytes("TestData/3.bmp"));
            Data_Pic Pic4 = new("4.bmp", "Test", File.ReadAllBytes("TestData/4.bmp"));
            Data_Pic Pic5 = new("5.bmp", "Test", File.ReadAllBytes("TestData/5.bmp"));
            Data_Pics Pics = new Data_Pics().Add([Pic1, Pic2, Pic3, Pic4, Pic5]);
            XSerializer.SerializeObjectToJsonFile(Pics, "TestData/Pics.json", true);//写到json文件
            Data_Pics pics = XSerializer.DeserializeJsonFileToObject<Data_Pics>("TestData/Pics.json", true);//解析json文件
            pics.WriteAllTmp();
        }

        /// <summary>
        /// 偏色测试
        /// </summary>
        public void e()
        {
            var str = CalculateColorDifference("a67d10", "a67d10");
            XLogger.Info($"偏色为：{str}");
        }

        public static string CalculateColorDifference(string colorHex1, string colorHex2)
        {
            // 将十六进制颜色字符串转换为Color对象
            Color color1 = ConvertHexToColor(colorHex1);
            Color color2 = ConvertHexToColor(colorHex2);

            // 计算R、G、B每个通道的差值
            int deltaR = Math.Abs(color1.R - color2.R);
            int deltaG = Math.Abs(color1.G - color2.G);
            int deltaB = Math.Abs(color1.B - color2.B);

            // 将差值转换回十六进制格式
            string deltaHexR = ConvertToHex(deltaR);
            string deltaHexG = ConvertToHex(deltaG);
            string deltaHexB = ConvertToHex(deltaB);

            // 返回偏色值的十六进制字符串
            return $"R: {deltaHexR}, G: {deltaHexG}, B: {deltaHexB}";
        }

        public void f()
        {
            //注册大漠插进
            if (!DamoKit.Reg("", out string errorStr))
                throw new Exception("注册失败：" + errorStr);
            dmsoft dm = new();

            XLogger.Info("字库个数：" + dm.GetDictCount(0));
            dm.AddDict(0, "082104608F31FE208C1182000004018060381C3F062004201C3E030$9$1.7.127$22");
            XLogger.Info("字库个数：" + dm.GetDictCount(0));
        }

        private static Color ConvertHexToColor(string hex)
        {
            // 使用Color.FromArgb方法将十六进制字符串转换为Color对象
            return Color.FromArgb(int.Parse(hex, System.Globalization.NumberStyles.HexNumber));
        }

        private static string ConvertToHex(int value)
        {
            // 将整数转换为十六进制字符串，并确保它是两位数
            return value.ToString("X2");
        }
    }
}