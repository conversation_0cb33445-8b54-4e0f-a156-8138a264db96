﻿using DanDing1.ViewModels.Windows;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// SuperMultiGameConfigWindow.xaml 的交互逻辑
    /// </summary>
    public partial class SuperMultiGameConfigWindow : Window
    {
        private readonly SuperMultiGameConfigWindowViewModel viewModel;

        public bool DialogResult { get; private set; }

        public SuperMultiGameConfigWindow(SuperMultiGameConfigWindowViewModel viewModel)
        {
            InitializeComponent();
            this.viewModel = viewModel;
            DataContext = viewModel;

            // 设置窗口置顶
            this.Topmost = true;

            // 订阅请求关闭窗口事件
            viewModel.RequestClose += ViewModel_RequestClose;
        }

        private void ViewModel_RequestClose(object sender, bool result)
        {
            DialogResult = result;
            this.Close();
        }

        private void OverNotice_Unchecked(object sender, RoutedEventArgs e)
        {
            // 这个方法会处理CheckBox的Checked和Unchecked事件
            // 在示例代码中没有实际逻辑，但保留方法以匹配XAML中的事件绑定
        }
    }
}