﻿using ScriptEngine.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper;
using static ScriptEngine.DDBuilder;

namespace ScriptEngine
{
    /// <summary>
    /// 包装日志类 分类输出
    /// </summary>
    /// <param name="logClassName"></param>
    public class Log
    {
        public Log(string logClassName, GameLogDelegate? logDelegate)
        {
            LogClassName = logClassName;
            LogDelegate = logDelegate;
            LogClassName = LogClassName switch
            {
                "游戏1" => Utils.UserGameName1 ?? "游戏1",
                "游戏2" => Utils.UserGameName2 ?? "游戏2",
                "游戏3" => Utils.UserGameName3 ?? "游戏3",
                "游戏4" => Utils.UserGameName4 ?? "游戏4",
                _ => LogClassName,
            };
        }

        public string LogClassName { get; }
        public GameLogDelegate? LogDelegate { get; }

        public void Info(string msg)
        {
            string logMsg = $" [{LogClassName}] " + msg;
            LogDelegate?.Invoke(logMsg);
            XLogger.Info(logMsg);
        }

        public void Info_Green(string msg)
        {
            string logMsg = $" [{LogClassName}] " + msg;
            LogDelegate?.Invoke(logMsg);
            XLogger.Info_Green(logMsg);
        }

        public void Warn(string msg)
        {
            string logMsg = $" [{LogClassName}] " + msg;
            LogDelegate?.Invoke(logMsg);
            XLogger.Warn(logMsg);
        }

        public void Error(string msg)
        {
            string logMsg = $" [{LogClassName}] " + msg;
            LogDelegate?.Invoke(logMsg);
            XLogger.Error(logMsg);
        }

        public void Write(string msg)
        {
            string logMsg = $" [{LogClassName}] " + msg;
            LogDelegate?.Invoke(logMsg);
            XLogger.Write(logMsg);
        }

        public void Debug(string msg)
        {
            string logMsg = $" [{LogClassName}] " + msg;
            LogDelegate?.Invoke(logMsg);
            XLogger.Debug(logMsg);
        }
    }
}