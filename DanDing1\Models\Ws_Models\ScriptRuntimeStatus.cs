﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DanDing1.Models.Ws_Models
{
    /// <summary>
    /// 脚本运行状态枚举
    /// </summary>
    public enum ScriptRuntimeStatus
    {
        Idle,         // 空闲/未运行 (初始状态或已正常结束)
        Starting,     // 启动中
        Running,      // 运行中 (正在执行主要任务逻辑)
        Paused,       // 暂停中 (例如等待游戏重启，Restarting = true)
        Waiting,      // 等待中 (例如等待定时任务，或等待用户干预如御魂满)
        Stopping,     // 停止中 (已请求停止，但尚未完全退出)
        Completed,    // 已成功完成所有任务
        Cancelled,    // 用户或系统取消
        Error         // 发生错误导致终止
    }
}