﻿using OcrLiteLib;
using OpenCvSharp;
using OpenCvSharp.Text;
using System.Drawing;
using System.Net;
using System.Net.Http.Headers;
using XHelper;

namespace CmdTester
{
    public class C
    {
        public C()
        {
            Console.WriteLine("TestC COTR Init...");
        }

        /// <summary>
        /// Await 抛出异常测试
        /// </summary>
        public async void a()
        {
            XLogger.Info("aa方法开始执行");

            Task<int> t = aa();

            XLogger.Info("做饭任务开始执行。");
            await Task.Delay(1000);
            XLogger.Info("做饭任务完成，等待aa任务结束.");
            await t;

            XLogger.Info("aa方法执行完毕");
        }

        public void b()
        {
            // 不断加入任务
            for (int i = 0; i < 8; i++)
                ThreadPool.QueueUserWorkItem(state =>
                {
                    Thread.Sleep(100);
                    Console.WriteLine("");
                });
            for (int i = 0; i < 8; i++)
                ThreadPool.QueueUserWorkItem(state =>
                {
                    Thread.Sleep(TimeSpan.FromSeconds(1));
                    Console.WriteLine("");
                });

            Console.WriteLine("     此计算机处理器数量：" + Environment.ProcessorCount);

            // 工作项、任务代表同一个意思
            Console.WriteLine("     当前线程池存在线程数：" + ThreadPool.ThreadCount);
            Console.WriteLine("     当前已处理的工作项数：" + ThreadPool.CompletedWorkItemCount);
            Console.WriteLine("     当前已加入处理队列的工作项数：" + ThreadPool.PendingWorkItemCount);
            int count;
            int ioCount;
            ThreadPool.GetMinThreads(out count, out ioCount);
            Console.WriteLine($"     默认最小辅助线程数：{count}，默认最小异步IO线程数：{ioCount}");

            ThreadPool.GetMaxThreads(out count, out ioCount);
            Console.WriteLine($"     默认最大辅助线程数：{count}，默认最大异步IO线程数：{ioCount}");
            Console.ReadKey();
        }

        private async Task<int> aa()
        {
            XLogger.Info("子线程执行中");
            await Task.Delay(2000);
            XLogger.Info("子线程等待2秒 后抛出异常");
            await Task.Delay(2000);
            throw new Exception("aa 任务出错！");
        }

        /// <summary>
        /// 登录测试
        /// </summary>
        public async void d()
        {
            var clientHandler = new HttpClientHandler
            {
                AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
            };
            var client = new HttpClient(clientHandler);
            var request = new HttpRequestMessage
            {
                Method = HttpMethod.Post,
                RequestUri = new Uri("http://127.0.0.1:8000/DD/login"),
                Headers =
                {
                    { "Accept", "*/*" },
                    { "User-Agent", "PostmanRuntime-ApipostRuntime/1.1.0" },
                    { "Connection", "keep-alive" },
                },
                Content = new StringContent("{  \"username\": \"xsllovemlj\",  \"password\": \"xsllovemlj\",  \"computer_Code\": \"alibabaalibaba\"}")
                {
                    Headers =
                    {
                        ContentType = new MediaTypeHeaderValue("application/json")
                    }
                }
            };
            using (var response = await client.SendAsync(request))
            {
                response.EnsureSuccessStatusCode();
                var body = await response.Content.ReadAsStringAsync();
                Console.WriteLine(body);
            }
        }

        /// <summary>
        /// 蛋定DDNet网络测试
        /// </summary>
        public void e()
        {
            //DDNet dNet = new("1.0");
            ////XLogger.Info("版本：" + dNet.NewVer);
            //XLogger.Info("公告：" + dNet.AppInfo);
            //XLogger.Info("更新日志：" + dNet.UpData);

            //var reg_obj = dNet.Register("1424473282", "xsllovemlj", "admin1424473282", "<EMAIL>");
            //if (!reg_obj?.IsSuccess ?? false)
            //{
            //    XLogger.Info($"注册失败！{reg_obj?.Message}");
            //    return;
            //}

            //var obj = dNet.Login("1424473282", "xsllovemlj");//登录账号
            //if (!obj?.IsSuccess ?? false)
            //{
            //    XLogger.Info($"登录失败！{obj?.Message}");
            //    return;
            //}

            //var isLogin = dNet.CheckLoginStatus();//检测账号状态
            //XLogger.Info("登录状态：" + isLogin);

            //var freeData = dNet.RegisterFreeUser();//试用软件
            //XLogger.Info($"试用返回：{freeData?.Message}|{freeData?.Data?.msg}|到期时间：{freeData?.Data?.stoptime}");

            //dNet.GetDmsoftCode(out string Code);
            //XLogger.Info($"大漠注册码：{Code}");
        }

        /// <summary>
        /// 配置文件读写测试
        /// </summary>
        public void f()
        {
            bool isShowDebug = false;
            XConfig.SaveValueToFile("ShowDebug", isShowDebug);
            //读取
            var obj = XConfig.LoadValueFromFile<bool>("ShowDebug");
            XLogger.Info(obj.ToString());
        }

        /// <summary>
        /// Ocr 文本识别测试
        /// </summary>
        public void g()
        {
            // 加载图像
            OpenCvSharp.Mat image = Cv2.ImRead(@"D:\1.png", OpenCvSharp.ImreadModes.Color);

            // 将图像转换为灰度图
            OpenCvSharp.Mat grayImage = new OpenCvSharp.Mat();
            Cv2.CvtColor(image, grayImage, ColorConversionCodes.BGR2GRAY);

            // 设置 Tesseract OCR 引擎的路径和语言
            string tesseractDataPath = @".\runtimes\"; // tessdata 文件夹的路径
            string language = "chi_sim"; // 使用简体中文语言包

            // 使用 OCRTesseract 进行文字识别
            using (var ocr = OCRTesseract.Create(tesseractDataPath, language, null, 3, 10))
            {
                // 执行 OCR 并获取识别结果
                ocr.Run(
                    grayImage,
                    out string outputText, // 识别到的完整文本
                    out Rect[] boxes,      // 每个识别到的文本区域
                    out string[] words,    // 每个识别到的单词
                    out float[] confidences); // 每个单词的置信度

                // 打印识别的文本
                Console.WriteLine("识别出的文本:");
                Console.WriteLine(outputText);

                // 如果你想显示识别的单词和相应的矩形框
                for (int i = 0; i < boxes.Length; i++)
                {
                    Cv2.Rectangle(image, boxes[i], Scalar.Red, 2); // 在图像上绘制矩形框
                    Console.WriteLine($"单词: {words[i]}, 置信度: {confidences[i]}");
                }

                // 显示结果图像
                Cv2.ImShow("OCR Result", image);
                Cv2.WaitKey(0);
            }
        }

        /// <summary>
        /// Ocr LiteOnnx
        /// </summary>
        public void h()
        {
            string modelsDir = @".\runtimes";
            string detPath = modelsDir + "\\dbnet.onnx";
            string clsPath = modelsDir + "\\angle_net.onnx";
            string recPath = modelsDir + "\\crnn_lite_lstm.onnx";
            string keysPath = modelsDir + "\\keys.txt";
            OcrLite ocrEngin = new OcrLite();
            ocrEngin.InitModels(detPath, clsPath, recPath, keysPath, 4);

            int padding = 50;
            int imgResize = 1024;
            float boxScoreThresh = 0.618F;
            float boxThresh = 0.300F;
            float unClipRatio = 2.0F;
            bool doAngle = true;
            bool mostAngle = true;
            OcrResult ocrResult = ocrEngin.Detect("D:\\1.png", padding, imgResize, boxScoreThresh, boxThresh, unClipRatio, doAngle, mostAngle);
            XLogger.Info("Ocr识别结果：" + ocrResult.ToString());
            XLogger.Info("RestTextBox结果：" + ocrResult.StrRes);

            //pictureBox.Image = ocrResult.BoxImg.ToBitmap();
        }

        /// <summary>
        /// XHelper Ocr测试
        /// </summary>
        public void bb()
        {
            string imgPath = @"D:\1.jpg";
            string? result = XOcr.Local_Ocr_String(File.ReadAllBytes(imgPath));
            Console.WriteLine(result);
        }

        /// <summary>
        /// ocr图片优化测试
        /// </summary>
        public void cc()
        {
            OptimizeImage(File.ReadAllBytes("E:\\OCR\\error\\21.bmp"));
        }

        /// <summary>
        /// 优化图片 使用Opencv
        /// </summary>
        /// <param name="bytes"></param>
        /// <returns></returns>
        private static byte[] OptimizeImage(byte[] bytes)
        {
            // 加载图片 bytes
            Mat image = Cv2.ImDecode(bytes, ImreadModes.Color);
            //Cv2.ImWrite(@"./runtimes/Image_Tmp_bak.bmp", image);

            // 转换为灰度图
            Mat grayImage = new Mat();
            Cv2.CvtColor(image, grayImage, ColorConversionCodes.BGR2GRAY);
            // 二值化
            Mat binaryImage = new Mat();
            Cv2.Threshold(grayImage, binaryImage, 0, 255, ThresholdTypes.Binary | ThresholdTypes.Otsu);
            // 膨胀
            Mat dilateImage = new Mat();
            Mat kernel = Cv2.GetStructuringElement(MorphShapes.Rect, new OpenCvSharp.Size(1, 1));
            Cv2.Dilate(binaryImage, dilateImage, kernel);

            Mat invertedImage = new Mat();
            if (IsWhiteBackground(dilateImage))
            {
                // 转换为黑底白字
                Cv2.BitwiseNot(dilateImage, invertedImage);
            }

            Cv2.ImWrite(@"E:/OCR/error/Image_Tmp.bmp", invertedImage);

            // 返回图片字节
            return invertedImage.ToBytes(".png");
        }

        private static bool IsWhiteBackground(Mat image)
        {
            // 计算图像的平均亮度
            Scalar meanScalar = Cv2.Mean(image);
            double meanBrightness = meanScalar.Val0;

            // 如果平均亮度高于阈值（如128），认为是白底黑字
            return meanBrightness > 128;
        }
    }
}