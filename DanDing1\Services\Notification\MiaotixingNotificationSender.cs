using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using XHelper;

namespace DanDing1.Services.Notification
{
    /// <summary>
    /// 喵提醒通知发送器
    /// 基于喵提醒API实现：https://www.showdoc.com.cn/miaotixing/9163883331817032
    /// </summary>
    public class MiaotixingNotificationSender : BaseNotificationSender
    {
        private readonly HttpClient _httpClient;
        private const string API_URL = "https://miaotixing.com/trigger"; // 喵提醒API地址

        /// <summary>
        /// 构造函数
        /// </summary>
        public MiaotixingNotificationSender()
        {
            _httpClient = new HttpClient();
        }

        /// <summary>
        /// 通知类型标识符
        /// </summary>
        public override string NoticeType => "miaotixing";

        /// <summary>
        /// 通知类型显示名称
        /// </summary>
        public override string DisplayName => "喵提醒";

        /// <summary>
        /// 格式化喵提醒内容
        /// </summary>
        /// <param name="content">原始内容</param>
        /// <returns>格式化后的内容</returns>
        public override string FormatContent(string content)
        {
            // 喵提醒支持换行符，直接替换即可
            return content.Replace("#换行", "\n");
        }

        /// <summary>
        /// 发送喵提醒通知的核心实现
        /// 基于喵提醒API：https://www.showdoc.com.cn/miaotixing/9163883331817032
        /// </summary>
        /// <param name="title">通知标题</param>
        /// <param name="content">通知内容</param>
        /// <param name="extraParams">额外参数，可包含miaoid(string)</param>
        /// <returns>发送是否成功</returns>
        protected override async Task<bool> SendNotificationCoreAsync(string title, string content, object extraParams)
        {
            try
            {
                // 获取喵提醒ID
                string miaoId = GetMiaoId(extraParams);
                if (string.IsNullOrEmpty(miaoId))
                {
                    XLogger.Error("喵提醒发送失败：未配置喵提醒ID");
                    return false;
                }

                // 构建请求参数
                var parameters = new Dictionary<string, string>
                {
                    { "id", miaoId },            // 喵码，必填
                    { "text", $"{title}\n{content}" },  // 将标题和内容合并
                    { "type", "json" }           // 返回JSON格式
                };

                // 添加自定义选项（可选）
                if (extraParams is Dictionary<string, object> dict && dict.TryGetValue("option", out var optionObj) && optionObj is string option)
                {
                    parameters.Add("option", option);
                }

                // 构建请求URL
                var requestUrl = $"{API_URL}?";
                foreach (var param in parameters)
                {
                    requestUrl += $"{param.Key}={Uri.EscapeDataString(param.Value)}&";
                }
                requestUrl = requestUrl.TrimEnd('&');

                // 发送GET请求
                var response = await _httpClient.GetAsync(requestUrl);
                
                // 解析响应
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<MiaotixingResponse>(responseContent);

                    if (result?.Code == 0)
                    {
                        XLogger.Info($"喵提醒发送成功，发送给{result.Data?.Users}个用户");
                        return true;
                    }
                    else
                    {
                        string errorMsg = GetErrorMessage(result?.Code ?? -1);
                        XLogger.Error($"喵提醒API返回错误: {errorMsg}, 代码: {result?.Code}");
                        return false;
                    }
                }

                XLogger.Error($"喵提醒请求失败，HTTP状态码: {response.StatusCode}");
                return false;
            }
            catch (Exception ex)
            {
                XLogger.Error($"发送喵提醒异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从额外参数或配置中获取喵提醒ID
        /// </summary>
        /// <param name="extraParams">额外参数</param>
        /// <returns>喵提醒ID</returns>
        private string GetMiaoId(object extraParams)
        {
            // 尝试从extraParams中获取miaoid
            if (extraParams is Dictionary<string, object> dict && 
                dict.TryGetValue("miaoid", out var miaoIdObj) && 
                miaoIdObj is string miaoId && 
                !string.IsNullOrEmpty(miaoId))
            {
                return miaoId;
            }

            // 从全局配置获取
            return GlobalData.Instance.UserConfig.Notice_Miaotixing_ID ?? string.Empty;
        }

        /// <summary>
        /// 根据错误码获取错误信息
        /// </summary>
        /// <param name="code">错误码</param>
        /// <returns>错误信息</returns>
        private string GetErrorMessage(int code)
        {
            switch (code)
            {
                case 0: return "提醒成功";
                case 101: return "参数格式有误";
                case 102: return "提醒过于频繁，未过冷却时间";
                case 103: return "找不到提醒，请检查喵码是否正确";
                case 104: return "提醒已被喵主禁用";
                case 105: return "该提醒的喵主未关注公众号";
                case 106: return "该提醒的喵主账户暂时无法使用";
                case 107: return "未能找到该提醒的喵主";
                case 108: return "该提醒的喵主未完成账号注册";
                case 109: return "提醒过于频繁，已加入缓冲池";
                case 110: return "时间戳已过期";
                case 201: return "未能找到提醒模板";
                case 202: return "内容与提醒模板格式不匹配";
                case 203: return "该提醒模板暂停使用";
                default: return "提醒失败（未指定原因）";
            }
        }

        /// <summary>
        /// 喵提醒响应数据结构
        /// </summary>
        private class MiaotixingResponse
        {
            /// <summary>
            /// 响应代码，0表示成功
            /// </summary>
            public int Code { get; set; }

            /// <summary>
            /// 响应消息
            /// </summary>
            public string Msg { get; set; }

            /// <summary>
            /// 响应数据
            /// </summary>
            public MiaotixingResponseData Data { get; set; }
        }

        /// <summary>
        /// 喵提醒响应数据详情
        /// </summary>
        private class MiaotixingResponseData
        {
            /// <summary>
            /// 成功发送的用户数
            /// </summary>
            public int Users { get; set; }

            /// <summary>
            /// 成功发送的详情
            /// </summary>
            public Dictionary<string, int> Success_sent { get; set; }

            /// <summary>
            /// 冷却时间剩余秒数
            /// </summary>
            public int? Remaining { get; set; }
        }
    }
} 