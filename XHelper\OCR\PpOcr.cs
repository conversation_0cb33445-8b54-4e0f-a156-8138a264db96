﻿using Microsoft.ML.OnnxRuntime;
using Microsoft.ML.OnnxRuntime.Tensors;
using OpenCvSharp;
using System.Collections.Concurrent;
using System.Text;
using System.Threading;
using System.Runtime.InteropServices;
using System.Buffers;
using System.Buffers.Binary;
using System.Diagnostics;

namespace XHelper.OCR
{
    public class OcrResult
    {
        public float Angle { get; set; }
        public Rect Box { get; set; }          // 文本框位置
        public float Confidence { get; set; }
        public string Text { get; set; }        // 识别的文本
    }

    public class PpOcr : IDisposable
    {
        // 使用固定大小的BlockingCollection替代ConcurrentBag，严格控制会话数量
        private readonly BlockingCollection<InferenceSession> _detSessionPool;

        private readonly BlockingCollection<InferenceSession> _recSessionPool;
        private readonly BlockingCollection<InferenceSession> _angleSessionPool;

        // 内存监控 - 可以考虑删除这些字段，简化内存管理
        private long _lastMemoryCheck = 0;

        private int _processedImageCount = 0;
        private readonly bool _enableMemoryLogging = false; // 关闭内存日志记录

        // 模型路径，用于创建新的会话
        private readonly string _detModelPath;

        private readonly string _recModelPath;
        private readonly string _angleModelPath;

        // 使用共享的ArrayPool管理内存
        private static readonly ArrayPool<float> _sharedArrayPool = ArrayPool<float>.Shared;

        private readonly string[] inputNames = new string[] { "x" };
        private readonly float[] meanValues = { 0.485f, 0.456f, 0.406f };
        private readonly float[] normValues = { 0.229f, 0.224f, 0.225f };
        private readonly int shortSize = 640;  // 降低短边大小以减少内存使用
        private string[] _characterDictionary;  // 用于存储字符字典

        // 定义并发度，控制最大会话数量
        private readonly int _maxConcurrency;

        private readonly SemaphoreSlim _sessionSemaphore;

        public PpOcr(string detModelPath, string recModelPath, string dictPath, int maxConcurrency = 4)
            : this(detModelPath, recModelPath, null, dictPath, maxConcurrency) // 调用完整构造函数
        {
        }

        // 完整参数的构造函数
        public PpOcr(string detModelPath, string recModelPath, string angleModelPath, string dictPath, int maxConcurrency = 4)
        {
            _detModelPath = detModelPath;
            _recModelPath = recModelPath;
            _angleModelPath = angleModelPath;
            _maxConcurrency = maxConcurrency;
            _sessionSemaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);

            // 初始化 BlockingCollection，容量为 maxConcurrency
            _detSessionPool = new BlockingCollection<InferenceSession>(maxConcurrency);
            _recSessionPool = new BlockingCollection<InferenceSession>(maxConcurrency);
            if (!string.IsNullOrEmpty(angleModelPath))
            {
                _angleSessionPool = new BlockingCollection<InferenceSession>(maxConcurrency);
            }

            // 预热会话池 - 创建所有会话实例
            for (int i = 0; i < maxConcurrency; i++)
            {
                var sessionOptions = CreateOptimizedSessionOptions();
                _detSessionPool.Add(new InferenceSession(detModelPath, sessionOptions));
                _recSessionPool.Add(new InferenceSession(recModelPath, sessionOptions));
                if (_angleSessionPool != null && !string.IsNullOrEmpty(angleModelPath))
                {
                    _angleSessionPool.Add(new InferenceSession(angleModelPath, sessionOptions));
                }
            }

            // 使用改进的字典加载方法
            LoadDictionary(dictPath);
        }

        public void Dispose()
        {
            // 释放BlockingCollection中的会话
            DisposeSessionPool(_detSessionPool);
            DisposeSessionPool(_recSessionPool);
            DisposeSessionPool(_angleSessionPool);

            _sessionSemaphore?.Dispose();

            // 强制清理OpenCV内存资源
            try
            {
                // 使用OpenCvSharp可能提供的资源释放方法
                Cv2.DestroyAllWindows();
                // 移除这里的GC.Collect，让Dispose模式自然工作
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"清理OpenCV资源时出错: {ex.Message}");
            }
        }

        // 辅助方法释放会话池
        private void DisposeSessionPool(BlockingCollection<InferenceSession> pool)
        {
            if (pool != null)
            {
                pool.CompleteAdding(); // 阻止后续添加
                while (pool.TryTake(out var session))
                {
                    session?.Dispose();
                }
                pool.Dispose();
            }
        }

        // 获取或创建检测会话
        private InferenceSession GetDetectionSession()
        {
            // 从池中获取，如果为空会阻塞等待
            return _detSessionPool.Take();
        }

        // 归还检测会话到池中
        private void ReturnDetectionSession(InferenceSession session)
        {
            if (session != null && !_detSessionPool.IsAddingCompleted) // 确保池未关闭
            {
                _detSessionPool.Add(session); // 归还到池中
            }
            else
            {
                session?.Dispose(); // 如果池已关闭或session为空，则直接释放
            }
        }

        // 获取或创建识别会话
        private InferenceSession GetRecognitionSession()
        {
            return _recSessionPool.Take();
        }

        // 归还识别会话到池中
        private void ReturnRecognitionSession(InferenceSession session)
        {
            if (session != null && !_recSessionPool.IsAddingCompleted)
            {
                _recSessionPool.Add(session);
            }
            else
            {
                session?.Dispose();
            }
        }

        // 获取或创建角度检测会话
        private InferenceSession GetAngleSession()
        {
            if (_angleSessionPool == null) return null;
            return _angleSessionPool.Take();
        }

        // 归还角度检测会话到池中
        private void ReturnAngleSession(InferenceSession session)
        {
            if (session != null && _angleSessionPool != null && !_angleSessionPool.IsAddingCompleted)
            {
                _angleSessionPool.Add(session);
            }
            else
            {
                session?.Dispose();
            }
        }

        // 执行OCR
        public List<OcrResult> RunOCR(string imgPath)
        {
            using (Mat srcImg = Cv2.ImRead(imgPath))
            {
                if (srcImg.Empty())
                {
                    throw new ArgumentException("无法读取图像文件");
                }

                return RunOCR(srcImg);
            }
        }

        // 直接从字节数组执行OCR
        public List<OcrResult> RunOCR(byte[] imageData)
        {
            try
            {
                // 将字节数组转换为Mat对象
                using (Mat srcImg = Cv2.ImDecode(imageData, ImreadModes.Color))
                {
                    if (srcImg.Empty())
                    {
                        throw new ArgumentException("无法解码图像数据");
                    }
                    //srcImg.SaveImage("D:\\1111s.bmp");
                    return RunOCR(srcImg);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从字节数组解析图像失败: {ex.Message}");
                return new List<OcrResult>();
            }
        }

        public List<OcrResult> RunOCR(Mat srcImg)
        {
            // 获取信号量，控制并发度
            _sessionSemaphore.Wait();

            // 用于记录从ArrayPool租用的缓冲区，确保最后归还
            float[] rentedBuffer = null;

            // 初始化会话引用为null
            InferenceSession detSession = null;
            InferenceSession recSession = null;
            InferenceSession angleSession = null;

            try
            {
                // 获取会话
                detSession = GetDetectionSession();
                recSession = GetRecognitionSession();
                angleSession = GetAngleSession();

                try
                {
                    // 立即获取尺寸，不再克隆原始图像
                    var originalSize = new OpenCvSharp.Size(srcImg.Cols, srcImg.Rows);

                    // 检查图像尺寸是否合理，避免处理极小图像导致内存问题
                    if (srcImg.Cols < 5 || srcImg.Rows < 5)
                    {
                        Debug.WriteLine($"图像尺寸过小，跳过OCR处理: {srcImg.Cols}x{srcImg.Rows}");
                        return new List<OcrResult>();
                    }

                    // 1. 在原始图像副本上转换为RGB格式 (避免修改传入的srcImg)
                    using Mat rgbImg = new Mat();
                    Cv2.CvtColor(srcImg, rgbImg, ColorConversionCodes.BGR2RGB);

                    // 2. 图像预处理并获取张量数据 (使用转换后的rgbImg)
                    var (processedImg, tensorData, shape) = PreprocessImage(rgbImg);

                    // 确保processedImg最终被释放
                    using (processedImg)
                    {
                        // 记录租用的缓冲区指针，用于在finally块中归还
                        rentedBuffer = MemoryMarshal.TryGetArray(tensorData, out ArraySegment<float> segment)
                            ? segment.Array
                            : null;

                        // 3. 文本检测 (传入处理后的图像和原始尺寸)
                        var detectionResults = DetectText(processedImg, tensorData, shape, originalSize, detSession);

                        // 4. 对每个检测框进行文本识别
                        var results = new List<OcrResult>(detectionResults.Count); // 预分配容量
                        foreach (var box in detectionResults)
                        {
                            try
                            {
                                // 使用原始图像(srcImg)和原始坐标(box)获取ROI
                                using Mat roi = GetRoiFromBox(srcImg, box);

                                // 检查ROI是否有效
                                if (roi == null || roi.Empty())
                                {
                                    Debug.WriteLine($"跳过无效的ROI，box: {box}");
                                    continue;
                                }

                                // 检测文本方向
                                float angle = 0;
                                if (angleSession != null)
                                {
                                    angle = DetectTextAngle(roi, angleSession);
                                }

                                // 优化旋转逻辑
                                Mat rotatedRoi = null; // 用于旋转后的Mat
                                Mat roiToRecognize = roi; // 默认使用原始ROI

                                try
                                {
                                    // 只在需要旋转时创建新的Mat
                                    if (Math.Abs(angle) > 1.0f)
                                    {
                                        rotatedRoi = RotateImage(roi, angle);
                                        roiToRecognize = rotatedRoi;
                                    }

                                    // 识别文本
                                    var recognitionResult = RecognizeText(roiToRecognize, recSession);
                                    results.Add(new OcrResult
                                    {
                                        Box = box,
                                        Text = recognitionResult.text,
                                        Confidence = recognitionResult.confidence,
                                        Angle = angle
                                    });
                                }
                                finally
                                {
                                    // 确保释放旋转图像
                                    rotatedRoi?.Dispose();
                                }
                            }
                            catch (ArgumentException argEx)
                            {
                                // 继续处理下一个区域
                                Debug.WriteLine($"处理ROI时参数异常: {argEx.Message}");
                            }
                            catch (Exception ex)
                            {
                                // 继续处理下一个区域
                                Debug.WriteLine($"处理ROI时异常: {ex.Message}");
                            }
                        }

                        return results;
                    }
                }
                finally
                {
                    // 归还会话资源
                    ReturnDetectionSession(detSession);
                    ReturnRecognitionSession(recSession);
                    ReturnAngleSession(angleSession);

                    // 归还从ArrayPool租用的缓冲区
                    if (rentedBuffer != null)
                    {
                        try
                        {
                            _sharedArrayPool.Return(rentedBuffer);
                            rentedBuffer = null;
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"归还缓冲区时异常: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"RunOCR出现错误: {ex.Message}\n堆栈: {ex.StackTrace}");
                return new List<OcrResult>(); // 返回空列表
            }
            finally
            {
                // 释放信号量
                _sessionSemaphore.Release();
            }
        }

        private string ConvertIndexToChar(int index)
        {
            // 检查字典是否加载成功以及索引是否有效
            if (_characterDictionary == null || index < 0 || index >= _characterDictionary.Length)
            {
                //Debug.WriteLine($"警告: 字符索引 {index} 超出字典范围 [0, {_characterDictionary?.Length ?? 0})");
                return "?"; // 返回一个占位符表示未知字符
            }

            // 从字典中查找字符
            return _characterDictionary[index];
        }

        private List<Rect> DetectText(Mat img, Memory<float> tensorData, int[] inputShape, OpenCvSharp.Size originalSize, InferenceSession session)
        {
            // 注意: tensorData是从RunOCR传递过来的，不需要在此方法中管理缓冲区

            try
            {
                // 直接使用传入的张量数据创建DenseTensor
                var inputTensor = new DenseTensor<float>(tensorData, inputShape);

                // 使用较小的初始容量创建NamedOnnxValue列表
                var inputs = new List<NamedOnnxValue>(1)
                {
                    NamedOnnxValue.CreateFromTensor(inputNames[0], inputTensor)
                };

                List<Rect> results = new List<Rect>();

                // 运行模型并立即处理结果
                using (var detResults = session.Run(inputs))
                {
                    results = ParseDetectionOutput(detResults, originalSize);
                }

                return results;
            }
            catch (System.OutOfMemoryException)
            {
                // 特别处理内存不足异常，不再手动强制GC
                Debug.WriteLine("内存不足，无法完成文本检测");
                // 主动清理一下内存
                GC.Collect(2, GCCollectionMode.Forced, true);
                return new List<Rect>();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检测模型执行错误: {ex.Message}");
                return new List<Rect>();
            }
        }

        private float DetectTextAngle(Mat textRegion, InferenceSession session)
        {
            // 如果角度检测模型为空，直接返回0度
            if (session == null)
            {
                return 0.0f;
            }

            Mat processedRegion = null;
            float[] rentedBuffer = null; // 用于记录从ArrayPool租用的缓冲区

            try
            {
                // 预处理文本区域
                processedRegion = PreprocessForAngleDetection(textRegion);

                // 准备张量输入数据
                int rows = processedRegion.Rows;
                int cols = processedRegion.Cols;
                int channels = 3;
                int totalSize = rows * cols * channels;

                // 从ArrayPool租用缓冲区
                rentedBuffer = _sharedArrayPool.Rent(totalSize);
                int bufferIndex = 0;

                // CHW顺序填充数据
                for (int c = 0; c < channels; c++)
                {
                    float cMean = meanValues[c];
                    float cNorm = normValues[c];

                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = 0; j < cols; j++)
                        {
                            float pix = processedRegion.Get<Vec3b>(i, j)[c];
                            rentedBuffer[bufferIndex++] = (pix / 255.0f - cMean) / cNorm;
                        }
                    }
                }

                // 创建张量形状
                int[] inputShape = { 1, channels, rows, cols };

                // 使用using减少内存压力
                var inputTensor = new DenseTensor<float>(rentedBuffer.AsMemory(0, totalSize), inputShape);
                var inputs = new List<NamedOnnxValue>
                {
                    NamedOnnxValue.CreateFromTensor(inputNames[0], inputTensor)
                };

                // 运行角度检测模型
                using var angleResults = session.Run(inputs);
                var angle = ParseAngleOutput(angleResults);

                return angle;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"角度检测错误: {ex.Message}");
                return 0.0f;
            }
            finally
            {
                // 确保资源被释放
                processedRegion?.Dispose();

                // 归还从ArrayPool租用的缓冲区
                if (rentedBuffer != null)
                {
                    _sharedArrayPool.Return(rentedBuffer);
                }
            }
        }

        // 在ParseRecognitionOutput方法里添加辅助函数处理维度的字符串化
        private string DimensionsToString(ReadOnlySpan<int> dimensions)
        {
            if (dimensions == null || dimensions.Length == 0)
                return "[]";

            var sb = new StringBuilder();
            sb.Append('[');

            for (int i = 0; i < dimensions.Length; i++)
            {
                if (i > 0)
                    sb.Append(", ");
                sb.Append(dimensions[i]);
            }

            sb.Append(']');
            return sb.ToString();
        }

        private Mat GetRoiFromBox(Mat srcImg, Rect box)
        {
            // 提前检查源图像
            if (srcImg == null || srcImg.Empty() || srcImg.Cols <= 0 || srcImg.Rows <= 0)
            {
                Debug.WriteLine("获取ROI失败: 源图像无效");
                return null;
            }

            // 检查ROI边界的合理性
            if (box.Width <= 0 || box.Height <= 0)
            {
                Debug.WriteLine($"警告: ROI宽度或高度为0或负值: Width={box.Width}, Height={box.Height}");
                return null;
            }

            // 移除此方法内的日志，避免重复打印
            // Debug.WriteLine($"原始ROI: X={box.X}, Y={box.Y}, Width={box.Width}, Height={box.Height}, 原图尺寸: {srcImg.Width}x{srcImg.Height}, Cols={srcImg.Cols}, Rows={srcImg.Rows}");

            // OpenCV中，图像尺寸应该用Cols和Rows而不是Width和Height
            int imgWidth = srcImg.Cols;
            int imgHeight = srcImg.Rows;

            // 确保坐标在有效范围内
            box.X = Math.Max(0, box.X);
            box.Y = Math.Max(0, box.Y);

            // 确保宽度和高度为正值
            box.Width = Math.Abs(box.Width);
            box.Height = Math.Abs(box.Height);

            // 确保宽度和高度在有效范围内
            box.Width = Math.Min(imgWidth - box.X, box.Width);
            box.Height = Math.Min(imgHeight - box.Y, box.Height);

            // 确保宽度和高度大于0
            if (box.Width <= 0 || box.Height <= 0)
            {
                Debug.WriteLine($"警告: 检测到无效的文本框 X={box.X}, Y={box.Y}, Width={box.Width}, Height={box.Height}");
                return null; // 返回null而不是强行创建可能无效的ROI
            }

            // 最后检查确保所有参数都在有效范围内
            if (box.X < 0 || box.Y < 0 || box.Width <= 0 || box.Height <= 0 ||
                box.X + box.Width > imgWidth || box.Y + box.Height > imgHeight)
            {
                // 不再抛出异常，而是返回null
                Debug.WriteLine($"警告: 无效的ROI参数: X={box.X}, Y={box.Y}, Width={box.Width}, Height={box.Height}, 图像尺寸: {imgWidth}x{imgHeight}");
                return null;
            }

            try
            {
                // 返回一个新的Mat对象（共享数据，但有自己的头部），调用者负责释放
                return new Mat(srcImg, box);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"创建ROI时出错: {ex.Message}");
                return null; // 创建失败也返回null
            }
        }

        private void LoadDictionary(string dictPath)
        {
            try
            {
                if (!File.Exists(dictPath))
                {
                    //Debug.WriteLine($"错误: 字典文件不存在 - {dictPath}");
                    throw new FileNotFoundException($"字典文件不存在 - {dictPath}");
                }

                //Debug.WriteLine($"正在加载字典: {dictPath}");

                // 读取字典文件中的所有字符
                string[] lines = File.ReadAllLines(dictPath);

                // 验证字典内容
                //Debug.WriteLine($"字典文件原始行数: {lines.Length}");

                // 创建新的字典列表 - 确保完全按照参考项目的格式处理
                List<string> charList = new List<string>();

                // 第一个字符必须是空白符，所有CTC解码的blank标记
                charList.Add("<blank>");  // 添加一个特殊标记，实际使用时会被忽略

                // 添加字典中的所有字符
                foreach (var line in lines)
                {
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        charList.Add(line.Trim());
                    }
                }

                // 最后添加空格字符，与参考项目保持一致
                charList.Add(" ");

                // 转换为数组
                _characterDictionary = charList.ToArray();

                //Debug.WriteLine($"字典处理完成，共 {_characterDictionary.Length} 个字符");
                //Debug.WriteLine($"第一个字符(blank): '{_characterDictionary[0]}'");

                // 输出前10个有效字符以便调试
                //if (_characterDictionary.Length > 10)
                //{
                //string firstChars = string.Join(", ", _characterDictionary.Skip(1).Take(9));
                //Debug.WriteLine($"字典前9个有效字符: {firstChars}");
                //Debug.WriteLine($"最后一个字符: '{_characterDictionary[_characterDictionary.Length - 1]}'");
                //}
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载字典时出错: {ex.Message}");
                throw;
            }
        }

        private float ParseAngleOutput(IDisposableReadOnlyCollection<DisposableNamedOnnxValue> outputs)
        {
            // 根据提供的信息，角度模型输出的张量名为softmax_0.tmp_0
            // 输出维度为 [batch_size, 2]，分别代表0度和180度的概率
            var outputTensor = outputs.First(o => o.Name == "softmax_0.tmp_0").AsTensor<float>();

            // 直接从张量读取概率值，避免创建中间数组
            float prob0 = outputTensor[0, 0]; // 0度的概率
            float prob180 = outputTensor[0, 1]; // 180度的概率

            //Debug.WriteLine($"角度检测结果 - 0度: {prob0:F3}, 180度: {prob180:F3}");

            // 返回概率最大的角度值
            return prob180 > prob0 ? 180f : 0f;
        }

        private List<Rect> ParseDetectionOutput(IDisposableReadOnlyCollection<DisposableNamedOnnxValue> outputs, OpenCvSharp.Size originalSize)
        {
            var boxes = new List<Rect>();

            // 根据提供的信息，检测模型输出的张量名为sigmoid_0.tmp_0
            var outputTensor = outputs.First(o => o.Name == "sigmoid_0.tmp_0").AsTensor<float>();

            // 解析检测结果
            float threshold = 0.3f; // 置信度阈值

            // 输出维度应为 [batch_size, 1, height, width]
            var dimensions = outputTensor.Dimensions; // 直接使用ReadOnlySpan<int>
            int height = dimensions[2];
            int width = dimensions[3];

            //Debug.WriteLine($"检测输出维度: [1, 1, {height}, {width}]");
            //Debug.WriteLine($"原始图像尺寸: {originalSize.Width}x{originalSize.Height}");

            // 将概率图转为二值图像
            Mat probMap = new Mat(height, width, MatType.CV_32FC1);
            try
            {
                // 通过索引直接从Tensor读取数据填充Mat
                for (int h = 0; h < height; h++)
                {
                    for (int w = 0; w < width; w++)
                    {
                        // 直接访问Tensor元素，避免创建托管副本
                        probMap.Set<float>(h, w, outputTensor[0, 0, h, w]);
                    }
                }

                // 阈值处理
                Mat binaryMap = new Mat();
                try
                {
                    Cv2.Threshold(probMap, binaryMap, threshold, 1.0, ThresholdTypes.Binary);

                    // 转换为8位图像，用于轮廓查找
                    Mat binaryMap8U = new Mat();
                    try
                    {
                        binaryMap.ConvertTo(binaryMap8U, MatType.CV_8UC1, 255);

                        // 查找轮廓
                        OpenCvSharp.Point[][] contours;
                        HierarchyIndex[] hierarchy;
                        Cv2.FindContours(binaryMap8U, out contours, out hierarchy, RetrievalModes.External, ContourApproximationModes.ApproxSimple);

                        //Debug.WriteLine($"找到 {contours.Length} 个轮廓");

                        // 缩放因子
                        float scaleX = (float)originalSize.Width / width;
                        float scaleY = (float)originalSize.Height / height;

                        // 处理每个轮廓
                        foreach (var contour in contours)
                        {
                            try
                            {
                                // 计算轮廓的最小旋转外接矩形
                                RotatedRect minAreaRect = Cv2.MinAreaRect(contour);
                                // 获取旋转矩形的四个顶点
                                Point2f[] points = minAreaRect.Points();
                                // 计算这四个顶点的正立边界框
                                Rect rect = Cv2.BoundingRect(points);

                                // 转换坐标到原始图像尺寸
                                int x = (int)(rect.X * scaleX);
                                int y = (int)(rect.Y * scaleY);
                                int w = (int)(rect.Width * scaleX);
                                int h = (int)(rect.Height * scaleY);
                                //if (h != 0)
                                //    ;
                                // --- 添加填充 ---
                                // 计算填充量 (例如，宽高的 60%)
                                int paddingX = (int)(w * 0.8);
                                int paddingY = (int)(h * 0.8);

                                // 调整坐标和尺寸
                                x = x - paddingX;
                                y = y - paddingY;
                                w = w + 2 * paddingX; // 宽度增加两倍填充量
                                h = h + 2 * paddingY; // 高度增加两倍填充量
                                // --- 填充结束 ---

                                // 确保坐标在图像范围内 (必须在填充之后重新检查和调整)
                                x = Math.Max(0, x);
                                y = Math.Max(0, y);

                                // 确保宽度和高度为正值
                                w = Math.Max(1, w); // 防止宽度为0或负数
                                h = Math.Max(1, h); // 防止高度为0或负数

                                // 确保扩展后的框不会超出图像边界
                                w = Math.Min(originalSize.Width - x, w); // 调整宽度
                                h = Math.Min(originalSize.Height - y, h); // 调整高度

                                // 确保矩形有一定的最小尺寸，且宽高有效 (再次检查调整后的值)
                                if (w > 5 && h > 5 && w <= originalSize.Width && h <= originalSize.Height && x < originalSize.Width && y < originalSize.Height)
                                {
                                    //Debug.WriteLine($"添加(阈值{threshold},带填充)文本区域: X={x}, Y={y}, Width={w}, Height={h}, 原图尺寸: {originalSize.Width}x{originalSize.Height}");
                                    boxes.Add(new Rect(x, y, w, h));
                                }
                                else
                                {
                                    // 检查是哪个条件不满足
                                    //Debug.WriteLine($"忽略(阈值{threshold},带填充)无效区域: X={x}, Y={y}, Width={w}, Height={h}, 原图尺寸: {originalSize.Width}x{originalSize.Height}. " +
                                    //$"条件检查: w>5 ({w > 5}), h>5 ({h > 5}), w<=W ({w <= originalSize.Width}), h<=H ({h <= originalSize.Height}), x<W ({x < originalSize.Width}), y<H ({y < originalSize.Height})");
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"处理轮廓时发生错误: {ex.Message}");
                            }
                        }
                    }
                    finally
                    {
                        binaryMap8U?.Dispose(); // 确保释放
                    }
                }
                finally
                {
                    binaryMap?.Dispose(); // 确保释放
                }
            }
            finally
            {
                probMap?.Dispose(); // 确保释放
            }

            //Debug.WriteLine($"检测到 {boxes.Count} 个文本区域");

            return boxes;
        }

        private (string text, float confidence) ParseRecognitionOutput(IDisposableReadOnlyCollection<DisposableNamedOnnxValue> outputs)
        {
            try
            {
                // 根据PaddleOCR模型输出格式获取张量
                var outputTensor = outputs.First(o => o.Name == "softmax_11.tmp_0").AsTensor<float>();
                var dimensions = outputTensor.Dimensions; // 直接使用ReadOnlySpan<int>

                int seqLen = dimensions[1]; // 序列长度
                int numClasses = dimensions[2]; // 字符类数

                //Debug.WriteLine($"识别输出维度: [1, {seqLen}, {numClasses}]");

                var result = new StringBuilder();
                float confidence = 0.0f;
                int validChars = 0;

                // 解码过程 - 获取每个时间步的最大概率字符索引
                var rawIndices = new List<int>(seqLen); // 预分配容量
                var rawProbs = new List<float>(seqLen);

                // 第1步：解析每个时间步的最高概率字符
                for (int t = 0; t < seqLen; t++)
                {
                    float maxProb = 0.0f;
                    int maxIndex = 0;

                    for (int c = 0; c < numClasses; c++)
                    {
                        // 直接从张量读取值，避免创建大型托管数组
                        float prob = outputTensor[0, t, c];

                        if (prob > maxProb)
                        {
                            maxProb = prob;
                            maxIndex = c;
                        }
                    }

                    // 收集当前时间步的最大概率字符
                    rawIndices.Add(maxIndex);
                    rawProbs.Add(maxProb);
                }

                // CTC解码 - 去除重复字符和空白字符
                int lastIndex = -1; // 用-1表示初始状态，绝不会与有效索引相同

                for (int i = 0; i < rawIndices.Count; i++)
                {
                    int index = rawIndices[i];

                    // 判断是否保留该字符
                    if (index == 0 || index == lastIndex || index >= _characterDictionary.Length)
                    {
                        // 跳过空白字符、重复字符或超出字典范围的字符
                    }
                    else
                    {
                        // 读取字典中对应的字符
                        string character = _characterDictionary[index];
                        result.Append(character);
                        confidence += rawProbs[i];
                        validChars++;
                    }

                    lastIndex = index;
                }

                // 计算置信度
                float avgConfidence = (validChars > 0) ? confidence / validChars : 0.0f;

                return (result.ToString(), avgConfidence);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"解析识别结果时出错: {ex.Message}");
                Debug.WriteLine($"错误堆栈: {ex.StackTrace}");
                return ("", 0.0f);
            }
        }

        private Mat PreprocessForAngleDetection(Mat img)
        {
            // 检查图像是否正常
            if (img.Empty() || img.Cols <= 0 || img.Rows <= 0)
            {
                Debug.WriteLine($"无效的图像用于角度检测: 尺寸={img?.Cols}x{img?.Rows}");
                // 返回一个小的空白图像，而不是抛出异常
                Mat safeImg = new Mat(48, 48, MatType.CV_8UC3, new Scalar(255, 255, 255));
                return safeImg;
            }

            // 根据提供的信息，角度检测模型需要合适的输入尺寸
            // PaddleOCR通常使用固定高度，宽度按比例调整
            int height = 48;
            int width = img.Cols * height / img.Rows;

            // 确保宽度合理
            width = Math.Max(width, 16);
            width = Math.Min(width, 192);  // 限制最大宽度

            Mat processed = new Mat();
            Cv2.Resize(img, processed, new OpenCvSharp.Size(width, height), interpolation: InterpolationFlags.Linear);

            //Debug.WriteLine($"角度检测预处理后尺寸: {processed.Width}x{processed.Height}");

            return processed;
        }

        private Mat PreprocessForRecognition(Mat img)
        {
            // 检查图像是否正常
            if (img.Empty() || img.Cols <= 0 || img.Rows <= 0)
            {
                Debug.WriteLine($"无效的图像用于文本识别: 尺寸={img?.Cols}x{img?.Rows}");
                // 返回一个小的空白图像，而不是抛出异常
                Mat safeImg = new Mat(48, 48, MatType.CV_8UC3, new Scalar(255, 255, 255));
                return safeImg;
            }

            // 根据参考项目，识别模型输入为 [batch_size, 3, 48, width]
            int height = 48;
            int width = img.Cols * height / img.Rows;

            // 确保宽度合理，防止图像过窄或过宽
            width = Math.Max(width, 16);
            width = Math.Min(width, 320);  // 参考项目使用320为最大宽度

            Mat processed = new Mat();
            Cv2.Resize(img, processed, new OpenCvSharp.Size(width, height), interpolation: InterpolationFlags.Linear);

            // 输出预处理后的图像尺寸
            //Debug.WriteLine($"文本识别预处理后尺寸: {processed.Width}x{processed.Height}");

            return processed;
        }

        private (Mat processedImg, Memory<float> tensorData, int[] shape) PreprocessImage(Mat srcImg)
        {
            try
            {
                // 添加最大尺寸限制
                using Mat resizedImg = new Mat();
                int maxAllowedSize = 1280; // 根据实际情况调整
                int minAllowedSize = 32;   // 添加最小尺寸限制，防止小图像导致错误

                // 检查图像是否过小，如果太小则放大到最小允许尺寸
                bool needInitialResizeSmall = srcImg.Rows < minAllowedSize || srcImg.Cols < minAllowedSize;
                // 首先判断是否需要进行初始缩放
                bool needInitialResizeLarge = srcImg.Rows > maxAllowedSize || srcImg.Cols > maxAllowedSize;
                Mat workImg = srcImg; // 默认使用原图

                if (needInitialResizeSmall)
                {
                    // 如果图像太小，放大到最小尺寸
                    double scale = Math.Max((double)minAllowedSize / srcImg.Rows, (double)minAllowedSize / srcImg.Cols);
                    Cv2.Resize(srcImg, resizedImg, new Size((int)(srcImg.Cols * scale), (int)(srcImg.Rows * scale)));
                    workImg = resizedImg; // 使用缩放后的图像
                    Debug.WriteLine($"图像过小，已放大: 原始尺寸={srcImg.Cols}x{srcImg.Rows}, 调整后={resizedImg.Cols}x{resizedImg.Rows}");
                }
                else if (needInitialResizeLarge)
                {
                    // 如果图像太大，缩小到最大尺寸
                    double scale = Math.Min((double)maxAllowedSize / srcImg.Rows, (double)maxAllowedSize / srcImg.Cols);
                    Cv2.Resize(srcImg, resizedImg, new Size((int)(srcImg.Cols * scale), (int)(srcImg.Rows * scale)));
                    workImg = resizedImg; // 使用缩放后的图像
                }

                int h = workImg.Rows;
                int w = workImg.Cols;
                float scaleH = 1;
                float scaleW = 1;

                // 计算缩放比例 - 使用较小的短边尺寸减少内存使用
                int localShortSize = shortSize;

                // 防止短边尺寸设置不合理
                if (localShortSize < 32) localShortSize = 32;

                // 计算缩放比例
                if (h < w)
                {
                    scaleH = (float)localShortSize / h;
                    float targetW = w * scaleH;
                    // 确保宽度不超过合理范围，过大的宽度会导致内存问题
                    targetW = Math.Min(targetW, 2560); // 限制最大宽度

                    targetW = targetW - targetW % 32;
                    targetW = Math.Max(32, targetW);
                    scaleW = targetW / w;
                }
                else
                {
                    scaleW = (float)localShortSize / w;
                    float targetH = h * scaleW;
                    // 确保高度不超过合理范围
                    targetH = Math.Min(targetH, 2560); // 限制最大高度

                    targetH = targetH - targetH % 32;
                    targetH = Math.Max(32, targetH);
                    scaleH = targetH / h;
                }

                // 创建目标图像
                Mat dstImg = new Mat();
                int targetWidth = (int)(scaleW * w);
                int targetHeight = (int)(scaleH * h);

                // 安全检查，防止尺寸异常
                if (targetWidth <= 0 || targetHeight <= 0 || targetWidth > 10000 || targetHeight > 10000)
                {
                    Debug.WriteLine($"警告: 缩放尺寸异常: {targetWidth}x{targetHeight}, 原始尺寸: {w}x{h}");
                    // 使用安全的默认尺寸
                    targetWidth = Math.Min(Math.Max(32, targetWidth), 1024);
                    targetHeight = Math.Min(Math.Max(32, targetHeight), 1024);
                }

                Cv2.Resize(workImg, dstImg, new OpenCvSharp.Size(targetWidth, targetHeight), interpolation: InterpolationFlags.Linear);

                // 像素值归一化 - 使用ArrayPool创建内存缓冲区
                int rows = dstImg.Rows;
                int cols = dstImg.Cols;
                int channels = 3;
                int totalSize = rows * cols * channels;

                // 从内存池租用缓冲区，而不是使用线程本地的List<float>
                float[] rentedBuffer = _sharedArrayPool.Rent(totalSize);
                int bufferIndex = 0;

                // CHW顺序填充数据
                for (int c = 0; c < channels; c++)
                {
                    float cMean = meanValues[c];
                    float cNorm = normValues[c];

                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = 0; j < cols; j++)
                        {
                            float pix = dstImg.Get<Vec3b>(i, j)[c];
                            rentedBuffer[bufferIndex++] = (pix / 255.0f - cMean) / cNorm;
                        }
                    }
                }

                // 创建张量形状
                int[] inputShape = { 1, channels, rows, cols };

                // 返回处理后的图像、内存段和形状，调用者负责归还内存
                return (dstImg, rentedBuffer.AsMemory(0, totalSize), inputShape);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"预处理图像时出错: {ex.Message}");
                throw;
            }
        }

        private (string text, float confidence) RecognizeText(Mat textRegion, InferenceSession session)
        {
            Mat processedRegion = null;
            float[] rentedBuffer = null; // 用于记录从ArrayPool租用的缓冲区

            try
            {
                // 预处理文本区域
                processedRegion = PreprocessForRecognition(textRegion);

                // 构建输入张量 - 使用ArrayPool而不是ThreadLocal
                int rows = processedRegion.Rows;
                int cols = processedRegion.Cols;
                int channels = 3;
                int totalSize = rows * cols * channels;

                // 从ArrayPool租用缓冲区
                rentedBuffer = _sharedArrayPool.Rent(totalSize);
                int bufferIndex = 0;

                // CHW顺序填充数据
                for (int c = 0; c < channels; c++)
                {
                    float cMean = meanValues[c];
                    float cNorm = normValues[c];

                    for (int i = 0; i < rows; i++)
                    {
                        for (int j = 0; j < cols; j++)
                        {
                            float pix = processedRegion.Get<Vec3b>(i, j)[c];
                            rentedBuffer[bufferIndex++] = (pix / 255.0f - cMean) / cNorm;
                        }
                    }
                }

                // 创建张量形状
                int[] inputShape = { 1, channels, rows, cols };

                // 创建输入张量和输入集合
                var inputTensor = new DenseTensor<float>(rentedBuffer.AsMemory(0, totalSize), inputShape);
                var inputs = new List<NamedOnnxValue>(1)
                {
                    NamedOnnxValue.CreateFromTensor(inputNames[0], inputTensor)
                };

                // 运行识别模型并立即解析结果
                using var recResults = session.Run(inputs);
                var result = ParseRecognitionOutput(recResults);

                return result;
            }
            catch (OutOfMemoryException)
            {
                // 内存不足，不再强制GC
                Debug.WriteLine("识别文本时内存不足");
                return ("", 0.0f);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"文字识别过程发生错误: {ex.Message}");
                return ("", 0.0f);
            }
            finally
            {
                // 确保资源被释放
                processedRegion?.Dispose();

                // 归还从ArrayPool租用的缓冲区
                if (rentedBuffer != null)
                {
                    _sharedArrayPool.Return(rentedBuffer);
                }
            }
        }

        private Mat RotateImage(Mat img, float angle)
        {
            Point2f center = new Point2f(img.Cols / 2f, img.Rows / 2f);
            // 使用using确保rotMatrix释放
            using Mat rotMatrix = Cv2.GetRotationMatrix2D(center, angle, 1.0);

            Mat rotated = new Mat();
            Cv2.WarpAffine(img, rotated, rotMatrix, img.Size());
            return rotated; // 返回旋转后的图像，调用者负责释放
        }

        // CreateOptimizedSessionOptions方法调整为更加保守的线程设置
        private SessionOptions CreateOptimizedSessionOptions()
        {
            var options = new SessionOptions();

            // 启用内存模式以重用中间张量的内存
            options.EnableMemoryPattern = false;
            options.EnableCpuMemArena = false;

            //options.AppendExecutionProvider_CUDA(0);
            options.AppendExecutionProvider_CPU();

            // 配置线程数 - 更保守的设置
            int availableCores = Environment.ProcessorCount;
            options.IntraOpNumThreads = Math.Max(1, availableCores / (_maxConcurrency * 2));
            options.InterOpNumThreads = Math.Max(1, availableCores / (_maxConcurrency * 2));

            // 图优化级别
            options.GraphOptimizationLevel = GraphOptimizationLevel.ORT_ENABLE_ALL;

            return options;
        }
    }
}