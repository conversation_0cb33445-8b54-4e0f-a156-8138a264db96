﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;

namespace ScriptEngine.Tasks
{
    internal class YingJieTask : BaseTask
    {
        private bool BiaoJi_Status = false;

        private string ClassStr;

        private Dictionary<string, Pixel> Colors = new Dictionary<string, Pixel>()
        {
            {"关闭弹窗",new Pixel(825,427,"f3b25e-101010",0.96) },
        };

        private int count = 0;
        private List<string> DontSendLog = ["标记", "喂食"];
        public bool Biaoji { get; private set; }
        public int Ncount { get; private set; }

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "英杰");
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Ncount = GetConfig.Count;
            GetConfig.Others.TryGetValue("Biaoji", out string? s);
            try { Biaoji = s is null ? false : bool.Parse(s); } catch (Exception) { Biaoji = false; }
            GetConfig.Others.TryGetValue("Class", out ClassStr);

            string ocrstr = Fast.Ocr_String(75, 10, 240, 62);
            if (ClassStr == "兵藏秘境" && ocrstr.Contains("兵藏秘境"))
            {
                log.Info("当前位置：兵藏秘境，直接开始..");
                Main_兵藏秘境();
                goto End;
            }
            else if (ClassStr == "鬼兵演武" && ocrstr.Contains("鬼兵演武"))
            {
                log.Info("当前位置：鬼兵演武，直接开始..");
                Main_鬼兵演武();
                goto End;
            }

            //进入场景
            string nowsecen = Scene.NowScene;
            if (nowsecen != "探索")
            {
                log.Info("当前场景不是探索，尝试进入探索");
                Scene.TO.TanSuo();
            }
            nowsecen = Scene.NowScene;
            if (nowsecen != "探索")
            {
                log.Error("进入探索失败，请手动进入");
                return;
            }

            var tupo_Count = Fast.Scence.TanSuo_GetTuPoCount();
            log.Debug("本地Ocr识别突破卷结果：" + tupo_Count);
            if (tupo_Count == 30)
                Tmp.Do_Tupo(); //执行临时执行突破

            log.Info("进入英杰试炼..");
            if (Db.GameSetting.IsTifu)
                Fast.Click(946, 650, 989, 687);
            else
                Fast.Click(847, 643, 890, 686);

            Sleep(1000);
            Sleep(1000);
            if (GetConfig.Others.TryGetValue("Class", out string? classStr1) && classStr1 == "鬼兵演武")
            {
                log.Info("进入鬼兵演武..");
                Fast.Click(96, 335, 131, 466);
                Sleep(1000);
                Sleep(1000);
                Main_鬼兵演武();
            }
            else if (GetConfig.Others.TryGetValue("Class", out string? classStr2) && classStr2 == "兵藏秘境")
            {
                log.Info("进入兵藏秘境..");
                Fast.Click(1090, 351, 1127, 468);
                Sleep(1000);
                Sleep(1000);
                Main_兵藏秘境();
            }
        End:
            EndCallBack();
        }

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            //点击开始
            Fast.Click(1115, 600, 1199, 676);
            Sleep(1000);
            log.Info("战斗点击开始");
            while (Colors["关闭弹窗"].Find(Dm))
            {
                log.Info("关闭弹窗提示..");
                Fast.Click(698, 415, 826, 447);
                Sleep(1000);
                Fast.Click(1115, 600, 1199, 676);
                Sleep(1000);
            }

            var pics = Mp.Filter("英杰.ZD");
            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                FindOkFun(p.Name, p);
                if (!DontSendLog.Any(p.Name.Contains)) log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("胜利") || p.Name.Contains("达摩"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败"))
                {
                    ret_bol = false;
                    isbreak = true;
                }
            }
            if (ret_bol)
                Combat_End();//等待界面

            return ret_bol;
        }

        /// <summary>
        /// 英杰胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("英杰.ZD");
            bool isbreak = false;
            DateTime lastExecutionTime = DateTime.MinValue;
            while (!isbreak)
            {
                // 检查是否已经过了2秒
                if ((DateTime.Now - lastExecutionTime).TotalSeconds >= 2)
                {
                    lastExecutionTime = DateTime.Now;

                    string ocrstr = Fast.Ocr_String(75, 10, 240, 62);
                    if (ocrstr.Contains("兵藏秘境") || ocrstr.Contains("鬼兵演武"))
                    {
                        isbreak = true;
                        continue;
                    }
                    string ocrstr_jl = Fast.Ocr_String(503, 33, 777, 106);
                    if (ocrstr_jl.Contains("选择奖励"))
                    {
                        SelectJL();
                        isbreak = true;
                        continue;
                    }
                }
                var p = pics.FindAllEa();
                if (p is null) continue;
                if (p.Name.Contains("挑战"))
                {
                    isbreak = true;
                    continue;
                }
                log.Info($"执行点击：{p._Name}");
                p.Click();
                Sleep(100); // 短暂休眠以避免CPU占用过高
            }
        }

        /// <summary>
        /// 结束任务方法
        /// </summary>
        private void EndCallBack()
        {
            log.Info("执行英杰任务收尾方法,等待返回后,退出到探索。");
            var mps = new MemPics()
                .Add(Mp.Filter("英杰.ZD_准备"))
                .Add(Mp.Filter("英杰.ZD_达摩"));

            while (!Mp.Filter("英杰.ZD_挑战").FindAll())
            {
                string ocrstr = Fast.Ocr_String(75, 10, 240, 62);
                if (ocrstr.Contains("兵藏秘境") || ocrstr.Contains("鬼兵演武"))
                    break;
                mps.FindAllAndClick();
                Sleep(1000);
            }
            log.Info("退出到探索..");
            Sleep(500);
            Fast.Click(32, 22, 76, 62);
            Sleep(1200);
            Fast.Click(32, 22, 76, 62);
            Sleep(1200);
        }

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            if (Biaoji && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.2秒，标记位置：5号位");
                Sleep(200);
                Fast.Click(1013, 454, 1082, 551);
                return false;
            }
            return true;
        }

        private void Main_兵藏秘境()
        {
            if (!Fast.Ocr_String(75, 10, 240, 62).Contains("兵藏秘境"))
            {
                log.Error("未进入到兵藏秘境界面");
                return;
            }
            var vs = Fast.Ocr_Local(714, 24, 836, 57);
            int currentCount = 0;
            if (!string.IsNullOrEmpty(vs))
            {
                string[] parts = vs.Split('/');
                if (parts.Length > 0 && int.TryParse(parts[0].Trim(), out int result))
                    currentCount = result;

                if (currentCount == 0)
                {
                    vs = Fast.Ocr_String(915, 24, 1011, 57); // 重新识别
                    if (!string.IsNullOrEmpty(vs))
                    {
                        if (int.TryParse(vs.Trim(), out int result1))
                            currentCount = result1;
                    }
                }
            }
            log.Debug($"当前兵藏秘境剩余次数：{currentCount}");
            if (Ncount > currentCount)
            {
                log.Info($"兵藏秘境剩余次数：{currentCount}，但是设定值为{Ncount}，无法全部完成，将次数调整至{currentCount}..");
                Ncount = currentCount;
            }
            if (UserConfig_Preset != null)
            {
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入式神录，开始应用预设{UserConfig_Preset}");
                Fast.Click(998, 574, 1030, 602);
                Sleep(1500);
                Tmp.Do_Preset(preset);
            }
        Re:
            while (count < Ncount)
            {
                if (!Wait兵藏秘境()) goto Re;

                Anti.RandomDelay();//防封等待
                if (Anti.ShouldTriggerRandomYysAuto())//判断是否需要穿插纸人
                {
                    Fast.Click(1031, 655, 1066, 689); //打开小纸人
                    Sleep(500);
                    int do_Count = Random.Shared.Next(1, 5); // 1-4次随机次数
                    if (Tmp.Do_YysAuto(do_Count))
                    {
                        count += do_Count;
                        log.Info($"触发随机穿插纸人战斗结束..脚本继续接管..");
                        Anti.ResetRandomYysAuto();
                        goto Re;
                    }
                    else Sleep(1000);
                }
                if (Db.PendingTimerTask) //执行定时任务
                {
                    Db.PendingTimerTask = false;
                    log.Info("暂停当前任务，执行定时任务，退出到探索..");
                    EndCallBack();
                    Db?.TimerTask?.DoAllTask();
                    Sleep(1000);
                    throw new Exception("定时任务执行结束，重新执行当前的主任务..");
                }
                Tmp.Do_ClearYuHun(); //判断是否需要执行清除御魂

                if (Combat())
                {
                    count++;
                    log.Info($"英杰-兵藏秘境-战斗胜利，战斗次数：{count}/{Ncount}");
                }
                else
                {
                    log.Warn($"英杰-兵藏秘境-战斗失败，请检查您的队伍配置是否正常！战斗次数：{count}/{Ncount}");
                    Defeated();
                }
            }
        }

        private void Main_鬼兵演武()
        {
            if (!Fast.Ocr_String(75, 10, 240, 62).Contains("鬼兵演武"))
            {
                log.Error("未进入到鬼兵演武界面");
                return;
            }
            if (UserConfig_Preset != null)
            {
                //使用预设
                List<string> preset = [.. UserConfig_Preset.Split('|')];
                log.Info($"进入式神录，开始应用预设{UserConfig_Preset}");
                Fast.Click(998, 574, 1030, 602);
                Sleep(1500);
                Tmp.Do_Preset(preset);
            }
        Re:
            while (count < Ncount)
            {
                if (!Wait鬼兵演武()) goto Re;

                Anti.RandomDelay();//防封等待
                if (Anti.ShouldTriggerRandomYysAuto())//判断是否需要穿插纸人
                {
                    Fast.Click(1031, 655, 1066, 689); //打开小纸人
                    Sleep(500);
                    int do_Count = Random.Shared.Next(1, 5); // 1-4次随机次数
                    if (Tmp.Do_YysAuto(do_Count))
                    {
                        count += do_Count;
                        log.Info($"触发随机穿插纸人战斗结束..脚本继续接管..");
                        Anti.ResetRandomYysAuto();
                        goto Re;
                    }
                    else Sleep(1000);
                }
                if (Db.PendingTimerTask) //执行定时任务
                {
                    Db.PendingTimerTask = false;
                    log.Info("暂停当前任务，执行定时任务，退出到探索..");
                    EndCallBack();
                    Db?.TimerTask?.DoAllTask();
                    Sleep(1000);
                    throw new Exception("定时任务执行结束，重新执行当前的主任务..");
                }
                Tmp.Do_ClearYuHun(); //判断是否需要执行清除御魂

                if (Combat())
                {
                    count++;
                    log.Info($"英杰-鬼兵演武-战斗胜利，战斗次数：{count}/{Ncount}");
                }
                else
                {
                    log.Warn($"英杰-鬼兵演武-战斗失败，请检查您的队伍配置是否正常！战斗次数：{count}/{Ncount}");
                    Defeated();
                }
            }
        }

        /// <summary>
        /// 选择奖励
        /// </summary>
        private void SelectJL()
        {
            log.Info("选择奖励..优先【八华斩血啸，八华斩追斩，八华斩增进，无畏附魂，无畏透甲】");
            List<string> JNs = [
                "血啸",
                "追斩",
                "增进",
                "附魂",
                "透甲",
                ];
            List<Position> OcrPoss = [
                new(102,348,271,403),
                new(388,350,586,403),
                new(701,349,885,400),
                new(1001,352,1190,401),
                ];
            List<Position> Clicks = [
                new(96,507,275,554),
                new(397,514,582,561),
                new(713,500,891,551),
                new(992,494,1197,565),
                ];

            // 存储所有技能及其位置和优先级
            Dictionary<int, (string skill, int priority)> foundSkills = new Dictionary<int, (string, int)>();

            // 一次性扫描所有技能
            for (int i = 0; i < OcrPoss.Count; i++)
            {
                string str = Fast.Ocr_String(OcrPoss[i]);
                for (int j = 0; j < JNs.Count; j++)
                {
                    if (str.Contains(JNs[j]))
                    {
                        // 记录技能位置和优先级(j越小优先级越高)
                        foundSkills[i] = (str, j);
                        break;
                    }
                }
            }

            // 如果找到了技能，选择优先级最高的
            if (foundSkills.Count > 0)
            {
                // 按优先级排序并获取第一个
                var bestSkill = foundSkills.OrderBy(s => s.Value.priority).First();
                log.Info($"选择奖励：{bestSkill.Value.skill}");
                Fast.Click(Clicks[bestSkill.Key]);
                Sleep(1000);
            }
            else
            {
                // 如果没有找到技能，默认选第一个
                log.Info("未找到优先技能，默认选择第一个");
                Fast.Click(Clicks[0]);
                Sleep(1000);
            }
            log.Info("点击确定..");
            Fast.Click(642, 655);
            Sleep(1000);
        }

        private bool Wait兵藏秘境()
        {
            // 设置最大等待时间为30秒，防止无限循环
            int maxWaitTime = 30000;
            int waitedTime = 0;
            int sleepInterval = 2000;
            string tiaoshi = Fast.Ocr_String(75, 10, 240, 62);
            log.Debug($"[英杰][{log.LogClassName}][Wait兵藏秘境 OCR结果][{tiaoshi}]");

            while (!Mp.Filter("CJ_兵藏秘境").FindAll() && !tiaoshi.Contains("兵藏秘境"))
            {
                Sleep(sleepInterval);
                waitedTime += sleepInterval;

                // 如果等待时间超过最大等待时间，返回false
                if (waitedTime >= maxWaitTime)
                {
                    log.Warn("等待兵藏秘境界面超时，可能未正确进入界面");
                    return false;
                }
            }
            return true;
        }

        private bool Wait鬼兵演武()
        {
            // 设置最大等待时间为30秒，防止无限循环
            int maxWaitTime = 30000;
            int waitedTime = 0;
            int sleepInterval = 2000;
            string tiaoshi = Fast.Ocr_String(75, 10, 240, 62);
            log.Debug($"[英杰][{log.LogClassName}][Wait鬼兵演武 OCR结果][{tiaoshi}]");
            while (!Mp.Filter("CJ_鬼兵演武").FindAll() && !tiaoshi.Contains("鬼兵演武"))
            {
                Sleep(sleepInterval);
                waitedTime += sleepInterval;

                // 如果等待时间超过最大等待时间，返回false
                if (waitedTime >= maxWaitTime)
                {
                    log.Warn("等待鬼兵演武界面超时，可能未正确进入界面");
                    return false;
                }
            }
            return true;
        }
    }
}