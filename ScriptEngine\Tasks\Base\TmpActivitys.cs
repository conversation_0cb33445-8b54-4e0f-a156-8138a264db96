﻿using DamoControlKit.Control;
using DamoControlKit.Model;
using ScriptEngine.Factorys;
using ScriptEngine.Model;
using static ScriptEngine.Model.TaskConfigsModel;

namespace ScriptEngine.Tasks.Base
{
    /// <summary>
    /// 临时执行的一些活动
    /// </summary>
    public class TmpActivitys
    {
        private string NowClassName { get; set; }

        public TmpActivitys(BaseTask _base)
        {
            NowClassName = _base.ClassName;
            _Base = _base;
            //初始化组件
            foreach (var item in Colors)
                item.Value.SetXsoft(_Base.Dm);
        }

        private Dictionary<string, Pixel> Colors = new Dictionary<string, Pixel>()
        {
            {"确定",new Pixel(760,431,"f3b25e-101010",0.96) },
            {"小纸人未开启限制次数",new Pixel(279,467,"9d9b7d-101010",0.98) },
        };

        public BaseTask _Base { get; }

        private Fast Fast => _Base.Fast;
        private Log log => _Base.log;

        /// <summary>
        /// 分组坐标
        /// </summary>
        private static readonly Dictionary<string, Point> _Preset1 = new()
        {
            { "1", new(1172,112) },
            { "2", new(1171,183) },
            { "3",new(1171,253) },
            { "4",new(1171,324) },
            { "5",new(1171,394) },
            { "6",new(1171,465) },
            { "7",new(1171,535) },
            { "8",new(1171,606) },
        };

        /// <summary>
        /// 预设坐标
        /// </summary>
        private static readonly Dictionary<string, Point> _Preset2 = new()
        {
            { "1", new(989,165) },
            { "2", new(988,315) },
            { "3",new(988,465) },
            { "4",new(988,615) },
        };

        /// <summary>
        /// 需要在式神录场景,使用预设
        /// </summary>
        /// <param name="presets"></param>
        internal void Do_Preset(List<string> presets)
        {
            log.Info($"当前位置：式神录，打开预设");
            Fast.Click(353, 77, 402, 110);
            Sleep(1000);

            // 记录上一次应用的组号
            string lastGroupNum = string.Empty;

            //使用预设
            foreach (var p in presets)
            {
                try
                {
                    if (string.IsNullOrEmpty(p) || !p.Contains("-"))
                    {
                        log.Warn($"预设格式错误: {p}，应为'组号-预设号'格式，跳过此预设");
                        continue;
                    }

                    string[] parts = p.Split("-", StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length != 2)
                    {
                        log.Warn($"预设格式错误: {p}，应为'组号-预设号'格式，跳过此预设");
                        continue;
                    }

                    string p1 = parts[0];
                    string p2 = parts[1];

                    if (!_Preset1.ContainsKey(p1))
                    {
                        log.Warn($"未找到组号: {p1}，跳过此预设");
                        continue;
                    }

                    if (!_Preset2.ContainsKey(p2))
                    {
                        log.Warn($"未找到预设号: {p2}，跳过此预设");
                        continue;
                    }

                    // 只有当组号改变时才执行滑动和点击组号的操作
                    if (p1 != lastGroupNum)
                    {
                        Sleep(300);
                        _Base.Operational.Slide_Pos(new Position("1149,111,1174,127"), new("1159,594,1173,609"));
                        Sleep(500);
                        log.Info($"点击组号: {p1}");
                        Fast.Click(_Preset1[p1].X, _Preset1[p1].Y);

                        // 更新上一次应用的组号
                        lastGroupNum = p1;
                    }
                    else
                    {
                        // 同组号，记录日志
                        log.Info($"复用上次组号: {p1}，无需重复点击");
                    }

                    Sleep(300);
                    _Base.Operational.Slide_Pos(new Position("755,159,773,167"), new("784,598,800,611"));
                    Sleep(500);
                    log.Info($"点击预设号: {p2}");
                    Fast.Click(_Preset2[p2].X, _Preset2[p2].Y);

                ReQueDing:
                    Sleep(1000);
                    //点击确定
                    if (Colors["确定"].Find(null))
                    {
                        log.Info("点击确定..");
                        Fast.Click(718, 412, 797, 448);
                        Sleep(600);
                        goto ReQueDing;
                    }
                }
                catch (Exception ex)
                {
                    log.Error($"应用预设 {p} 时出错: {ex.Message}，继续下一个预设");
                    // 发生错误时重置上一次组号，确保下次重新选择
                    lastGroupNum = string.Empty;
                }
            }
            log.Info("退出式神录..结束更换预设..");
            //退出式神录
            Fast.Click(33, 15, 62, 47);
            Sleep(1500);
        }

        /// <summary>
        /// 临时清理御魂
        /// </summary>
        internal void Do_ClearYuHun()
        {
            if (_Base.YuHunFulling)
            {
                Sleep(1000);
                Dictionary<string, Point> ssl = new()
                {
                    {"御魂",new(845,667) },
                    {"御灵",new(978,665) },
                    {"业原火",new(987,663) },
                    {"探索",new(812,665) },
                    {"日轮",new(844,664) },
                    {"觉醒",new(845,664) },
                    {"活动",new(845,664) },
                    {"契灵",new(873,586) }, // 契灵结契场景
                    {"英杰",new(1016,584) }, // 契灵结契场景
                };
                _Base.YuHunFulling = false;
                log.Info("当前任务支持非重启清理御魂，开始清理御魂.. 进入式神录...");
                Fast.Click(ssl[_Base.ClassName].X, ssl[_Base.ClassName].Y);
                Sleep(2000);
                _FullYuHun();
            }
        }

        /// <summary>
        /// 临时清空突破卷
        /// </summary>
        internal void Do_Tupo()
        {
            if (!_Base.UserConfig_Tupo30Run)
                return;

            string biaojiValue = _Base.UserConfig_Tupo30BiaoJi ?? "位置5";
            bool kajiValue = _Base.UserConfig_Tupo30KaJi;

            log.Info($"根据您的偏好设置，现在突破卷需要清理，为您先清理突破卷至0！（标记：{biaojiValue}，卡级：{kajiValue}）");
            var count = _Base.UserConfig_Tupo30Count <= 0 ? 1 : _Base.UserConfig_Tupo30Count;
            var others = new Dictionary<string, string>()
            {
                { "Biaoji", biaojiValue },
                { "Tui4", kajiValue.ToString() }
            };

            try
            {
                RunTaskFactory.Run<TuPo>(_Base.Db, _Base.Dm, _Base.Ct, count, $"突破 {count}次", others);
            }
            catch (Exception ex)
            {
                log.Error($"执行突破任务时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 御魂满了，进入式神录进行御魂清理操作
        /// </summary>
        private void _FullYuHun()
        {
            var pics_on = DynamicData.FilterPicsName("御魂满");
            pics_on.SetAllXsoft(_Base.Dm);
            Sleep(1500);
            log.Info("进入御魂配置界面..");
            Fast.Click(550, 394, 590, 434);
            log.Info("等待御魂清理提示..");
            if (pics_on.Wait(5))
            {
                log.Info("点击 去清理..");
                Sleep(2000);
                Fast.Click(491, 405, 584, 442);
            }
            Sleep(1000);
            log.Info("点击 奉纳..");
            Fast.Click(1164, 214, 1214, 282);
            Sleep(1000);
            log.Info("点击 贪吃鬼..");
            Fast.Click(1170, 627, 1215, 669);
            Sleep(1000);
            log.Info("点击 进食习惯..");
            Fast.Click(836, 539, 958, 579);
            Sleep(1000);
            log.Info("点击 立即进食..");
            Fast.Click(957, 617, 988, 648);
            Sleep(1000);
            Fast.Click(709, 412, 826, 444);
            Sleep(1500);
            log.Info("退出 贪吃鬼..");
            Fast.Click(1154, 215, 1219, 265);
            Fast.Click(1154, 215, 1219, 265);
            Sleep(2000);
            Fast.Click(935, 214, 965, 240);
            Fast.Click(935, 214, 965, 240);
            Sleep(1000);
            int i = 0;
            bool is_end = false;
            while (!is_end)
            {
                Sleep(1000);
                log.Info($"开始奉纳五星御魂{i + 1}次..请将御魂排序设置为星级升序！");
                Fast.LongClick(144, 278, 2000);
                Sleep(2000);
                //OCR
                string ocr_str = Fast.Ocr_Local(887, 571, 1016, 611);
                if (!ocr_str.Contains("200000"))
                {
                    log.Warn("当前奉纳御魂获得的金币不为200000，结束清理御魂..");
                    is_end = true;
                    continue;
                }
                Fast.Click(835, 646, 916, 688);
                Sleep(5000);
                Fast.Click(1027, 194, 1076, 276);
                Sleep(1000);
                i++;
            }
            log.Info("御魂清理结束，点击返回到任务界面..");
            Fast.Click(34, 25, 65, 55);
            Sleep(1500);
            Fast.Click(34, 25, 65, 55);
            Sleep(2000);
        }

        private void Sleep(int ms)
        {
            _Base.Sleep(ms);
        }

        private readonly Dictionary<string, (int, int, int, int)> YysAutoInitOCR = new()
        {
            {"御魂",(468,224,797,514) },
            {"日轮",(468,224,797,514) },
            {"觉醒",(468,224,797,514) },
            {"业原火",(468,224,797,514) },
            {"御灵",(468,224,797,514) },
            {"契灵",(468,224,797,514) },
            {"活动",(468,224,797,514) },
            {"英杰",(468,224,797,514) },
            {"探索",(874,245,1202,538) },
        };

        /// <summary>
        /// 御魂键盘坐标
        /// </summary>
        private static readonly Dictionary<int, Point> _Keyboard_Yuhun = new()
        {
            { 1, new(357,557) },
            { 2, new(434,558) },
            { 3,new(515,559) },
            { 4,new(356,616) },
            { 5,new(434,616) },
            { 6,new(515,616) },
            { 7,new(353,671) },
            { 8,new(434,671) },
            { 9,new(515,671) },
        };

        /// <summary>
        /// 探索键盘坐标
        /// </summary>
        private static readonly Dictionary<int, Point> _Keyboard_Tansuo = new()
        {
            { 1, new(680,576) },
            { 2, new(757,576) },
            { 3,new(834,576) },
            { 4,new(679,633) },
            { 5,new(757,633) },
            { 6,new(834,633) },
            { 7,new(679,690) },
            { 8,new(757,690) },
            { 9,new(834,690) },
        };

        /// <summary>
        /// 键盘坐标
        /// </summary>
        private Dictionary<string, Dictionary<int, Point>> _Keyboards = new()
        {
            {"御魂", _Keyboard_Yuhun },
            {"日轮", _Keyboard_Yuhun },
            {"觉醒", _Keyboard_Yuhun },
            {"业原火", _Keyboard_Yuhun },
            {"御灵", _Keyboard_Yuhun },
            {"契灵", _Keyboard_Yuhun },
            {"活动", _Keyboard_Yuhun },
            {"英杰", _Keyboard_Yuhun },
            {"探索", _Keyboard_Tansuo },
        };

        private Dictionary<string, Point> _Points = new()
        {
            {"御魂_点击喂食",new(771,450) },
            {"御魂_点击次数",new(345,495) },
            {"御魂_点击确定",new(572,666) },
            {"御魂_开始挑战",new(683,577) },

            {"英杰_点击喂食",new(771,450) },
            {"英杰_点击次数",new(345,495) },
            {"英杰_点击确定",new(572,666) },
            {"英杰_开始挑战",new(978,668) },

            {"日轮_点击喂食",new(771,450) },
            {"日轮_点击次数",new(345,495) },
            {"日轮_点击确定",new(572,666) },
            {"日轮_开始挑战",new(683,577) },

            {"觉醒_点击喂食",new(771,450) },
            {"觉醒_点击次数",new(345,495) },
            {"觉醒_点击确定",new(572,666) },
            {"觉醒_开始挑战",new(683,577) },

            {"业原火_点击喂食",new(771,450) },
            {"业原火_点击次数",new(345,495) },
            {"业原火_点击确定",new(572,666) },
            {"业原火_开始挑战",new(683,577) },

            {"御灵_点击喂食",new(771,450) },
            {"御灵_点击次数",new(345,495) },
            {"御灵_点击确定",new(572,666) },
            {"御灵_开始挑战",new(683,577) },

            {"契灵_点击喂食",new(771,450) },
            {"契灵_点击次数",new(345,495) },
            {"契灵_点击确定",new(572,666) },
            {"契灵镇墓兽_开始挑战",new(800,650) },
            {"契灵探查_开始挑战",new(980,665) },

            {"探索_点击喂食",new(1196,487) },
            {"探索_点击次数",new(751,516) },
            {"探索_点击确定",new(914,695) },
            {"探索_开始挑战",new(1069,667) },

            {"活动_点击喂食",new(771,450) },
            {"活动_点击次数",new(345,495) },
            {"活动_点击确定",new(572,666) },
            {"活动_开始挑战",new(879,663) },
        };

        /// <summary>
        /// 随机穿插纸人
        /// </summary>
        /// <param name="doCount"></param>
        /// <returns>F:未执行 T:执行成功</returns>
        internal bool Do_YysAuto(int doCount)
        {
            try
            {
                log.Info($"触发随机穿插纸人，开始对纸人进行初始化...预备执行{doCount}次纸人战斗");
                //初始化信息 OCR
                int x = 0, y = 0, x1 = 0, y1 = 0;
                if (YysAutoInitOCR.TryGetValue(_Base.ClassName, out (int, int, int, int) value))
                {
                    x = value.Item1;
                    y = value.Item2;
                    x1 = value.Item3;
                    y1 = value.Item4;
                }
                if (x == 0 || y == 0 || x1 == 0 || y1 == 0)
                {
                    log.Warn("当前场景未配置纸人坐标，跳过纸人操作");
                    return false;
                }
                int Ws_Count = 0;
                int Now_point = 0;
                int yb_Point = 0;
                try
                {
                    Fast.Ocr_String(x, y, x1, y1, t =>
                    {
                        foreach (var item in t)
                        {
                            // 提取剩余喂食次数
                            var wsMatch = System.Text.RegularExpressions.Regex.Match(item.Text, @"(\d+)次");
                            if (wsMatch.Success && wsMatch.Groups.Count > 1)
                            {
                                if (int.TryParse(wsMatch.Groups[1].Value, out int count))
                                {
                                    Ws_Count = count;
                                }
                            }

                            // 提取当前点数
                            var pointMatch = System.Text.RegularExpressions.Regex.Match(item.Text, @"(\d+)/100");
                            if (pointMatch.Success && pointMatch.Groups.Count > 1)
                            {
                                if (int.TryParse(pointMatch.Groups[1].Value, out int point))
                                {
                                    Now_point = point;
                                }
                            }

                            //从 5/500中提取出 5 作为剩余樱饼数量
                            var yingbiMatch = System.Text.RegularExpressions.Regex.Match(item.Text, @"(\d+)/500");
                            if (yingbiMatch.Success && yingbiMatch.Groups.Count > 1)
                            {
                                if (int.TryParse(yingbiMatch.Groups[1].Value, out int yingbi))
                                {
                                    yb_Point = yingbi;
                                }
                            }
                        }
                    });
                }
                catch (Exception ex)
                {
                    log.Error($"OCR识别失败: {ex.Message}，使用默认值继续执行");
                    // 使用默认值继续执行
                }

                log.Info($"当前剩余喂食次数: {Ws_Count}次，当前樱饼数量: {Now_point}，樱饼剩余库存数量：{yb_Point}");
                if (yb_Point < 10)
                {
                    log.Info("樱饼剩余库存数量小于10，直接跳过纸人操作..");
                    // 安全点击，确保返回操作成功
                    try
                    {
                        Fast.Click(275, 27, 370, 89);
                    }
                    catch (Exception ex)
                    {
                        log.Warn($"返回点击操作异常: {ex.Message}");
                    }
                    return false;
                }

                // 当喂食次数为0且樱饼数量小于12时返回false
                if (Ws_Count == 0 && Now_point <= 12)
                {
                    log.Info("喂食次数为0且樱饼数量小于12，跳过纸人操作");
                    // 安全点击，确保返回操作成功
                    try
                    {
                        Fast.Click(275, 27, 370, 89);
                    }
                    catch (Exception ex)
                    {
                        log.Warn($"返回点击操作异常: {ex.Message}");
                    }
                    return false;
                }

                // 喂食次数大于0且樱饼数量小于5时点击喂食
                if (Ws_Count > 0 && Now_point <= 5)
                {
                    log.Info("喂食次数大于0且樱饼数量小于5，点击喂食");
                    // 安全获取点击坐标
                    string key = _Base.ClassName + "_点击喂食";
                    if (!_Points.TryGetValue(key, out Point? point))
                    {
                        log.Warn($"未找到点击喂食坐标: {key}，使用默认坐标");
                        // 使用默认坐标
                        Fast.Click(771, 450);
                    }
                    else
                    {
                        Fast.Click(point.X, point.Y);
                    }
                    Sleep(1000);
                }

                //判断挑战次数是否为开启状态
                try
                {
                    if (Colors["小纸人未开启限制次数"].Find(null))
                    {
                        log.Info("小纸人未开启限制次数，选择开启");
                        Fast.Click(270, 453, 306, 480);
                        Sleep(1000);
                    }
                }
                catch (Exception ex)
                {
                    log.Warn($"判断挑战次数状态异常: {ex.Message}，继续执行后续操作");
                }

                //设置挑战次数
                log.Info("开始设置挑战次数..");
                string clickCountKey = _Base.ClassName + "_点击次数";
                if (!_Points.TryGetValue(clickCountKey, out Point? clickCountPoint))
                {
                    log.Warn($"未找到点击次数坐标: {clickCountKey}，使用默认坐标");
                    // 使用默认坐标
                    Fast.Click(345, 495);
                }
                else
                {
                    Fast.Click(clickCountPoint.X, clickCountPoint.Y);
                }
                Sleep(1000);

                //设置次数
                log.Info($"设置次数: {doCount}次");
                // 确保doCount在有效范围内
                if (!_Keyboards.TryGetValue(_Base.ClassName, out Dictionary<int, Point>? keyboard) || !keyboard.ContainsKey(doCount))
                {
                    log.Warn($"键盘坐标未找到 {_Base.ClassName} 的 {doCount} 键，使用默认值1");
                    doCount = 1; // 使用默认值
                }

                // 再次检查确保键盘坐标存在
                if (_Keyboards.TryGetValue(_Base.ClassName, out keyboard) && keyboard.TryGetValue(doCount, out Point keyPoint))
                {
                    Fast.Click(keyPoint.X, keyPoint.Y);
                }
                else
                {
                    log.Warn($"使用默认键盘坐标点击 {doCount}");
                    // 使用默认坐标(取第一个键)
                    if (keyboard != null && keyboard.Count > 0)
                    {
                        Fast.Click(keyboard.First().Value.X, keyboard.First().Value.Y);
                    }
                    else
                    {
                        log.Error("无法获取键盘坐标，跳过纸人操作");
                        return false;
                    }
                }
                Sleep(1000);

                //点击确定
                log.Info("点击确定..");
                string confirmKey = _Base.ClassName + "_点击确定";
                if (!_Points.TryGetValue(confirmKey, out Point? confirmPoint))
                {
                    log.Warn($"未找到确定按钮坐标: {confirmKey}，使用默认坐标");
                    // 使用默认坐标
                    Fast.Click(572, 666);
                }
                else
                {
                    Fast.Click(confirmPoint.X, confirmPoint.Y);
                }
                Sleep(1000);

                //退出小纸人
                log.Info("退出小纸人..");
                Fast.Click(275, 27, 370, 89);
                Sleep(1000);

                //开始自动挑战
                log.Info("开始自动挑战..");
                string TaskType = "";
                if (_Base.ClassName == "契灵")
                    TaskType = _Base.GetConfig.Others.TryGetValue("TaskType", out string? value1) ? value1 : "镇墓兽";

                string challengeKey = _Base.ClassName + TaskType + "_开始挑战";
                if (!_Points.TryGetValue(challengeKey, out Point? challengePoint))
                {
                    log.Warn($"未找到开始挑战按钮坐标: {challengeKey}，使用默认坐标");
                    // 使用默认坐标
                    Fast.Click(683, 577);
                }
                else
                {
                    Fast.Click(challengePoint.X, challengePoint.Y);
                }

                log.Info("等待自动挑战完成..");
                var ended_pics = DynamicData.FilterPicsName("纸人结束")
                    .Add(_Base.Mp.Filter("达摩"))
                    .Add(_Base.Mp.Filter("挑战"));
                ended_pics.SetAllXsoft(_Base.Dm);

                // 添加超时保护，最多等待5分钟
                int timeout = 5 * 60; // 5分钟
                int waitTime = 0;
                int damoCount = 0;
                int endCount = 0;
                while (waitTime < timeout)
                {
                    Sleep(1000);
                    waitTime++;

                    endCount = 0;
                    damoCount = 0;

                    if (waitTime % 10 == 0) // 每10秒输出一次日志
                        log.Info($"等待自动挑战完成..已等待{waitTime}秒");

                    DateTime lastOcrTime = DateTime.MinValue;
                    foreach (var pic in ended_pics.PicList)
                    {
                        // 检查是否已经过了2秒
                        if ((DateTime.Now - lastOcrTime).TotalSeconds >= 2)
                        {
                            lastOcrTime = DateTime.Now;
                            string ocrstr = Fast.Ocr_String_V2(new(427, 284, 853, 350));
                            if (ocrstr.Contains("已完成"))
                                goto ExitWhile;
                        }

                        string name = pic.Name;
                        if (pic.Find() && name.Contains("纸人结束"))
                        {
                            endCount++;
                            if (endCount >= 2)
                            {
                                endCount = 0;
                                damoCount = 0;
                                goto ExitWhile;
                            }
                        }
                        if (pic.Find() && name.Contains("达摩") && name.Contains("挑战"))
                        {
                            damoCount++;
                            if (damoCount >= 20)
                            {
                                log.Warn("纸人樱饼异常结束了，手动退出奖励界面；");
                                Fast.Click(494, 117, 825, 137);
                                Sleep(1000);
                                return false;
                            }
                        }
                    }
                }

            ExitWhile:
                // 检查是否因超时而退出循环
                if (waitTime >= timeout)
                {
                    log.Warn($"等待挑战完成超时(超过{timeout}秒)，强制结束等待");
                    Fast.Click(483, 418, 564, 446);
                    Sleep(1000);
                    return false;
                }

                log.Info("自动挑战完成..点击取消..");
                Sleep(1000);
                Fast.Click(483, 418, 564, 446);
                Sleep(1000);

                return true;
            }
            catch (Exception ex)
            {
                if (!ex.Message.Contains("task was canceled"))
                    log.Error($"执行纸人战斗时发生异常: {ex.Message}\n{ex.StackTrace}");
                else
                    throw;

                // 尝试返回到安全状态
                try
                {
                    Fast.Click(275, 27, 370, 89); // 尝试点击返回
                    Sleep(1000);
                }
                catch { }
                return false;
            }
        }
    }
}