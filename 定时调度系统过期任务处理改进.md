# 定时调度系统过期任务处理改进

## 问题描述

在原有的定时调度系统中，存在以下问题：

```
[2025-06-05 12:32:30] [INFO] 任务 [日常任务1] 已过期超过30分钟，不执行并标记为已过期
[2025-06-05 12:32:30] [INFO] 任务 [日常任务1] 下次执行时间已更新为: 2025-06-06 09:33:00
[2025-06-05 12:32:30] [INFO] 任务 [日常任务1] 已过期超过30分钟，不执行并标记为已过期
[2025-06-05 12:32:30] [INFO] 任务 [日常任务1] 下次执行时间已更新为: 2025-06-06 09:45:00
[2025-06-05 12:32:30] [INFO] 任务 [日常任务1] 已过期超过30分钟，不执行并标记为已过期
```

**核心问题**：系统在判断任务是否过期时，没有检查该任务是否在当天已经成功完成过，导致已完成的任务仍被错误地标记为"已过期"。

## 解决方案

### 1. 新增任务完成状态检查方法

在 `SchedulerService.cs` 中新增了 `CheckTaskCompletedToday` 方法：

```csharp
/// <summary>
/// 检查任务在指定日期是否已完成
/// </summary>
/// <param name="task">要检查的任务</param>
/// <param name="date">检查的日期</param>
/// <returns>是否已完成</returns>
private async Task<bool> CheckTaskCompletedToday(ScheduledTask task, DateTime date)
{
    try
    {
        // 创建临时的历史记录服务实例来检查
        var historyService = new TaskHistoryService();
        
        // 获取指定日期的历史记录
        var records = historyService.LoadRecordsByDate(date);
        
        // 检查是否有该任务在指定日期成功完成的记录
        bool hasCompleted = records.Any(r => 
            r.TaskId == task.Id && 
            r.TaskName == task.Name && 
            r.EmulatorName == task.EmulatorName &&
            (r.Status == "成功" || r.Status.Contains("成功")) &&
            r.StartTime.Date == date.Date);

        if (hasCompleted)
        {
            _logAction($"发现任务 [{task.Name}] 在 {date:yyyy-MM-dd} 已有成功完成记录");
        }

        return hasCompleted;
    }
    catch (Exception ex)
    {
        _logAction($"检查任务 [{task.Name}] 完成状态时出错: {ex.Message}");
        return false;
    }
}
```

### 2. 改进过期任务检查逻辑

修改了 `CheckScheduledTasks` 方法中的过期检查逻辑：

```csharp
// 检查是否过期超过30分钟
TimeSpan overdue = now - nextTime;
if (nextTime < now && overdue.TotalMinutes > 30)
{
    // 检查当天是否已经完成过该任务
    bool hasCompletedToday = await CheckTaskCompletedToday(task, nextTime.Date);
    
    if (hasCompletedToday)
    {
        // 当天已完成，不标记为过期，直接更新下次执行时间
        _logAction($"任务 [{task.Name}] 在 {nextTime.Date:yyyy-MM-dd} 已完成，跳过过期标记");
        UpdateNextExecutionTime(task);
        continue;
    }
    
    // 任务过期超过30分钟且当天未完成，标记为过期
    task.Status = "已过期";
    _logAction($"任务 [{task.Name}] 已过期超过30分钟，不执行并标记为已过期");

    // 更新下次执行时间
    UpdateNextExecutionTime(task);
    continue;
}
```

### 3. 更新任务执行判断方法

将 `ShouldExecuteTask` 方法改为异步方法 `ShouldExecuteTaskAsync`，并添加了相同的完成状态检查：

```csharp
/// <summary>
/// 判断任务是否应该执行
/// </summary>
private async Task<bool> ShouldExecuteTaskAsync(ScheduledTask task, DateTime now)
{
    if (string.IsNullOrEmpty(task.NextExecutionTime))
        return false;

    if (!DateTime.TryParse(task.NextExecutionTime, out DateTime nextTime))
        return false;

    // 如果下次执行时间已过
    if (nextTime <= now)
    {
        // 检查是否过期超过30分钟
        TimeSpan overdue = now - nextTime;
        if (overdue.TotalMinutes > 30)
        {
            // 检查当天是否已经完成过该任务
            bool hasCompletedToday = await CheckTaskCompletedToday(task, nextTime.Date);
            if (hasCompletedToday)
            {
                // 当天已完成，不执行
                return false;
            }
            
            // 过期超过30分钟且当天未完成，不执行
            return false;
        }
        // 未过期30分钟，可以执行
        return true;
    }

    return false;
}
```

## 改进效果

### 改进前的行为：
- 任务过期超过30分钟就会被标记为"已过期"
- 不考虑任务是否已在当天完成
- 可能导致已完成的任务被错误标记

### 改进后的行为：
- 检查任务是否在当天已完成
- 如果当天已完成，跳过过期标记，直接更新下次执行时间
- 只有当天未完成且过期超过30分钟的任务才会被标记为"已过期"
- 避免了已完成任务的重复标记

### 预期日志输出：
```
[2025-06-05 12:32:30] [INFO] 发现任务 [日常任务1] 在 2025-06-05 已有成功完成记录
[2025-06-05 12:32:30] [INFO] 任务 [日常任务1] 在 2025-06-05 已完成，跳过过期标记
[2025-06-05 12:32:30] [INFO] 任务 [日常任务1] 下次执行时间已更新为: 2025-06-06 09:33:00
```

## 测试验证

创建了单元测试 `SchedulerServiceTests.cs` 来验证改进的正确性：

1. `TestTaskNotMarkedExpiredWhenCompletedToday` - 验证当天已完成的任务不会被标记为过期
2. `TestTaskMarkedExpiredWhenNotCompletedToday` - 验证当天未完成的过期任务会被正确标记为过期

## 注意事项

1. 该改进依赖于 `TaskHistoryService` 正确记录任务执行历史
2. 检查完成状态时会创建临时的历史记录服务实例，对性能影响较小
3. 异常处理确保即使历史记录检查失败，系统仍能正常运行
4. 保持了原有的30分钟过期判断逻辑，只是增加了完成状态的检查

## 兼容性

- 该改进完全向后兼容
- 不影响现有的任务调度逻辑
- 只是优化了过期任务的判断机制
