using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Text;

namespace XHelper
{
    /// <summary>
    /// WebSocket客户端静态类，用于与WebSocket服务器进行通信
    /// </summary>
    public static class XWebsocket
    {
        private static readonly TimeSpan _defaultTimeout = TimeSpan.FromSeconds(10);
        private static readonly ConcurrentDictionary<string, Action<string>> _messageHandlers = new ConcurrentDictionary<string, Action<string>>();
        private static readonly TimeSpan _reconnectInterval = TimeSpan.FromSeconds(5);
        private static readonly SemaphoreSlim _sendLock = new SemaphoreSlim(1, 1);
        private static readonly object _stateLock = new object();
        private static bool _autoReconnect = false;
        private static Action<string>? _defaultMessageHandler;
        private static string? _lastConnectedUrl;
        private static string? _lastUsedToken;
        private static WebSocketState _lastKnownState = WebSocketState.None;
        private static CancellationTokenSource? _receiveCts;
        private static Task? _receiveTask;
        private static ClientWebSocket? _webSocket;

        // 新增：发送失败计数和监控
        private static int _sendFailureCount = 0;

        private static readonly int _maxSendFailures = 3; // 连续失败3次后禁用
        private static bool _wsDisabled = false;
        private static readonly object _failureCountLock = new object();
        private static DateTime _lastFailureTime = DateTime.MinValue;
        private static readonly TimeSpan _failureResetInterval = TimeSpan.FromMinutes(5); // 5分钟无失败则重置计数

        /// <summary>
        /// 获取或设置WebSocket功能是否被禁用
        /// </summary>
        public static bool IsDisabled
        {
            get =>
                _wsDisabled;
            set =>
                _wsDisabled = value;
        }

        /// <summary>
        /// 自动重连属性
        /// </summary>
        public static bool AutoReconnect
        {
            get => _autoReconnect;
            set => _autoReconnect = value;
        }

        /// <summary>
        /// 获取WebSocket的当前状态
        /// </summary>
        public static WebSocketState State
        {
            get
            {
                lock (_stateLock)
                {
                    return _webSocket?.State ?? _lastKnownState;
                }
            }
        }

        /// <summary>
        /// 检查WebSocket是否已连接
        /// </summary>
        public static bool IsConnected
        {
            get
            {
                lock (_stateLock)
                {
                    return _webSocket != null && _webSocket.State == WebSocketState.Open;
                }
            }
        }

        /// <summary>
        /// 连接到WebSocket服务器
        /// </summary>
        /// <param name="url">WebSocket服务器地址</param>
        /// <param name="token">用于认证的令牌</param>
        /// <param name="timeout">连接超时时间</param>
        /// <returns>连接是否成功</returns>
        public static async Task<bool> ConnectAsync(string url, string token, TimeSpan? timeout = null)
        {
            // 如果WebSocket功能已被禁用，则直接返回false
            if (_wsDisabled)
            {
                XLogger.OnlyWrite("WebSocket功能已被禁用，无法连接");
                return false;
            }

            try
            {
                // 如果已经连接，先断开现有连接
                if (IsConnected)
                {
                    XLogger.OnlyWrite($"WebSocket已连接，断开现有连接");
                    await DisconnectAsync();
                }

                timeout ??= _defaultTimeout;
                await DisconnectAsync();

                _lastConnectedUrl = url;
                _lastUsedToken = token;
                _webSocket = new ClientWebSocket();
                _webSocket.Options.KeepAliveInterval = TimeSpan.FromSeconds(30);

                // 添加授权头
                if (!string.IsNullOrEmpty(token))
                {
                    // 将令牌添加到 WebSocket 请求头中作为 Authorization
                    _webSocket.Options.SetRequestHeader("Authorization", $"Bearer {token}");

                    // 某些 WebSocket 服务器可能使用 Sec-WebSocket-Protocol 字段来携带认证信息
                    // _webSocket.Options.AddSubProtocol($"Bearer {token}");
                }

                using CancellationTokenSource cts = new CancellationTokenSource(timeout.Value);

                XLogger.OnlyWrite($"正在连接到WebSocket服务器");
                await _webSocket.ConnectAsync(new Uri(url), cts.Token);

                SetLastKnownState(_webSocket.State);
                XLogger.OnlyWrite($"WebSocket连接成功");

                _receiveCts = new CancellationTokenSource();
                _receiveTask = StartReceivingMessages(_receiveCts.Token);

                // 连接成功时重置失败计数
                ResetFailureCount();

                return true;
            }
            catch (Exception ex)
            {
                SetLastKnownState(WebSocketState.Closed);
                XLogger.OnlyWrite($"WebSocket连接失败: {ex.Message}");

                // 连接失败时增加失败计数
                IncrementFailureCount();

                if (_autoReconnect && !_wsDisabled)
                {
                    _ = AttemptReconnection();
                }

                return false;
            }
        }

        /// <summary>
        /// 连接到WebSocket服务器（无Token版本，用于向后兼容）
        /// </summary>
        /// <param name="url">WebSocket服务器地址</param>
        /// <param name="timeout">连接超时时间</param>
        /// <returns>连接是否成功</returns>
        public static Task<bool> ConnectAsync(string url, TimeSpan? timeout = null)
        {
            return ConnectAsync(url, string.Empty, timeout);
        }

        /// <summary>
        /// 断开WebSocket连接
        /// </summary>
        public static async Task DisconnectAsync()
        {
            try
            {
                if (_webSocket != null)
                {
                    _receiveCts?.Cancel();

                    if (_webSocket.State == WebSocketState.Open)
                    {
                        try
                        {
                            await _webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Client initiated close", CancellationToken.None);
                            XLogger.OnlyWrite("WebSocket连接已正常关闭");
                        }
                        catch (Exception ex)
                        {
                            XLogger.OnlyWrite($"关闭WebSocket连接时出错: {ex.Message}");
                        }
                    }

                    _webSocket.Dispose();
                    _webSocket = null;
                }

                if (_receiveTask != null && !_receiveTask.IsCompleted)
                {
                    try
                    {
                        await _receiveTask;
                    }
                    catch (OperationCanceledException)
                    {
                        // 这是预期的异常，由取消接收循环触发
                    }
                    catch (Exception ex)
                    {
                        XLogger.OnlyWrite($"终止接收循环时发生错误: {ex.Message}");
                    }
                }

                _receiveCts?.Dispose();
                _receiveCts = null;
                _receiveTask = null;

                SetLastKnownState(WebSocketState.Closed);
            }
            catch (Exception ex)
            {
                XLogger.OnlyWrite($"断开WebSocket连接时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 注册特定类型消息的处理器
        /// </summary>
        /// <param name="messageType">消息类型标识</param>
        /// <param name="handler">处理该类型消息的委托</param>
        public static void RegisterMessageHandler(string messageType, Action<string> handler)
        {
            _messageHandlers[messageType] = handler;
        }

        /// <summary>
        /// 发送字符串消息
        /// </summary>
        /// <param name="message">要发送的消息</param>
        /// <returns>是否发送成功</returns>
        public static async Task<bool> SendMessageAsync(string message)
        {
            // 如果WebSocket功能已被禁用，则直接返回false
            if (_wsDisabled)
            {
                XLogger.OnlyWrite("WebSocket功能已被禁用，无法发送消息");
                return false;
            }

            try
            {
                if (_webSocket?.State != WebSocketState.Open)
                {
                    XLogger.OnlyWrite("WebSocket连接未打开，无法发送消息");
                    IncrementFailureCount();
                    return false;
                }

                byte[] messageBytes = Encoding.UTF8.GetBytes(message);
                await _sendLock.WaitAsync();

                try
                {
                    await _webSocket.SendAsync(new ArraySegment<byte>(messageBytes),
                        WebSocketMessageType.Text,
                        true,
                        CancellationToken.None);

                    // 发送成功时重置失败计数
                    ResetFailureCount();

                    return true;
                }
                finally
                {
                    _sendLock.Release();
                }
            }
            catch (Exception ex)
            {
                XLogger.OnlyWrite($"发送WebSocket消息失败: {ex.Message}");

                // 发送失败时增加失败计数
                IncrementFailureCount();

                // 如果发送失败，可能连接已断开
                if (_autoReconnect && !_wsDisabled && (_webSocket?.State != WebSocketState.Open))
                {
                    _ = AttemptReconnection();
                }

                return false;
            }
        }

        /// <summary>
        /// 发送对象（序列化为JSON）
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要发送的对象</param>
        /// <returns>是否发送成功</returns>
        public static async Task<bool> SendObjectAsync<T>(T obj)
        {
            // 如果WebSocket功能已被禁用，则直接返回false
            if (_wsDisabled)
            {
                XLogger.OnlyWrite("WebSocket功能已被禁用，无法发送对象");
                return false;
            }

            // 检查WebSocket连接状态
            if (_webSocket?.State != WebSocketState.Open)
            {
                XLogger.OnlyWrite("WebSocket连接未打开，无法发送对象");
                return false;
            }

            try
            {
                string json = JsonConvert.SerializeObject(obj);
                return await SendMessageAsync(json);
            }
            catch (Exception ex)
            {
                XLogger.OnlyWrite($"序列化对象失败: {ex.Message}");
                IncrementFailureCount();
                return false;
            }
        }

        /// <summary>
        /// 设置默认消息处理器
        /// </summary>
        /// <param name="handler">处理接收到的消息的委托</param>
        public static void SetDefaultMessageHandler(Action<string> handler)
        {
            _defaultMessageHandler = handler;
        }

        /// <summary>
        /// 取消注册特定类型的消息处理器
        /// </summary>
        /// <param name="messageType">消息类型标识</param>
        public static void UnregisterMessageHandler(string messageType)
        {
            _messageHandlers.TryRemove(messageType, out _);
        }

        /// <summary>
        /// 增加失败计数并检查是否需要禁用WebSocket功能
        /// </summary>
        private static void IncrementFailureCount()
        {
            lock (_failureCountLock)
            {
                // 如果距离上次失败时间超过了重置间隔，则重置计数
                if (DateTime.Now - _lastFailureTime > _failureResetInterval)
                {
                    _sendFailureCount = 0;
                }

                _sendFailureCount++;
                _lastFailureTime = DateTime.Now;

                if (_sendFailureCount >= _maxSendFailures && !_wsDisabled)
                {
                    _wsDisabled = true;
                    XLogger.OnlyWrite($"WebSocket在短时间内连续失败{_maxSendFailures}次，已自动禁用WebSocket功能");
                }
            }
        }

        /// <summary>
        /// 重置失败计数
        /// </summary>
        private static void ResetFailureCount()
        {
            lock (_failureCountLock)
            {
                _sendFailureCount = 0;
            }
        }

        /// <summary>
        /// 尝试重新连接到WebSocket服务器
        /// </summary>
        private static async Task AttemptReconnection()
        {
            // 如果WebSocket功能已被禁用，则不尝试重连
            if (_wsDisabled)
            {
                XLogger.OnlyWrite("WebSocket功能已被禁用，跳过重连");
                return;
            }

            if (string.IsNullOrEmpty(_lastConnectedUrl))
            {
                XLogger.OnlyWrite("无法重连WebSocket：未知的上一次连接地址");
                return;
            }

            XLogger.OnlyWrite($"尝试重新连接到WebSocket服务器: {_lastConnectedUrl}");

            // 等待一段时间后重试
            await Task.Delay(_reconnectInterval);

            // 确保先断开现有连接
            await DisconnectAsync();

            // 尝试重新连接，使用上次的token
            bool reconnected = await ConnectAsync(_lastConnectedUrl, _lastUsedToken ?? string.Empty);

            if (reconnected)
            {
                XLogger.OnlyWrite("已成功重新连接到WebSocket服务器");
            }
            else
            {
                XLogger.OnlyWrite("重新连接WebSocket服务器失败");
            }
        }

        /// <summary>
        /// 处理接收到的消息
        /// </summary>
        private static Task ProcessReceivedMessage(string message)
        {
            try
            {
                // 尝试解析消息类型
                string? messageType = null;
                string? innerMessage = null;

                try
                {
                    // 首先尝试解析外层消息
                    var jsonObj = JsonConvert.DeserializeObject<dynamic>(message);

                    // 检查是否是嵌套消息格式 {"type":"message","message":"{...}"}
                    if (jsonObj?.type?.ToString() == "message" && jsonObj?.message != null)
                    {
                        // 这是一个嵌套消息，尝试解析内层消息
                        innerMessage = jsonObj?.message?.ToString();
                        XLogger.OnlyWrite($"检测到嵌套消息，外层type=message，内层消息: {innerMessage}");

                        if (!string.IsNullOrEmpty(innerMessage))
                        {
                            try
                            {
                                var innerJsonObj = JsonConvert.DeserializeObject<dynamic>(innerMessage);
                                messageType = innerJsonObj?.type?.ToString();
                                XLogger.OnlyWrite($"成功解析内层消息，type={messageType}");

                                // 使用内层消息的类型查找处理器
                                if (messageType != null && _messageHandlers.TryGetValue(messageType, out var handler1))
                                {
                                    // 调用处理器并传递内层消息
                                    XLogger.OnlyWrite($"找到内层消息类型[{messageType}]的处理器，正在调用...");
                                    handler1(innerMessage);
                                    return Task.CompletedTask;
                                }
                                else
                                {
                                    XLogger.OnlyWrite($"未找到内层消息类型[{messageType}]的处理器，已注册的处理器类型: {string.Join(", ", _messageHandlers.Keys)}");
                                }
                            }
                            catch (Exception ex)
                            {
                                XLogger.OnlyWrite($"解析内层消息失败: {ex.Message}");
                            }
                        }
                    }
                    else
                    {
                        // 常规消息处理
                        messageType = jsonObj?.type?.ToString();
                        if (messageType == "server-response")
                        {
                            XLogger.OnlyWrite($"常规消息，type={messageType}");
                            return Task.CompletedTask;
                        }
                        XLogger.OnlyWrite($"常规消息，type={messageType}");
                    }
                }
                catch (Exception ex)
                {
                    XLogger.OnlyWrite($"解析消息JSON失败: {ex.Message}");
                    // 不是JSON或解析失败，使用默认处理器
                }

                // 如果有匹配的处理器，则调用
                if (messageType != null && _messageHandlers.TryGetValue(messageType, out var handler))
                {
                    XLogger.OnlyWrite($"找到消息类型[{messageType}]的处理器，正在调用...");
                    handler(innerMessage ?? message);
                }
                // 否则使用默认处理器
                else if (_defaultMessageHandler != null)
                {
                    XLogger.OnlyWrite($"使用默认消息处理器处理消息");
                    _defaultMessageHandler(message);
                }
                else
                {
                    XLogger.OnlyWrite($"收到WebSocket消息但没有注册处理器: {message}，已注册的处理器类型: {string.Join(", ", _messageHandlers.Keys)}");
                }
            }
            catch (Exception ex)
            {
                XLogger.OnlyWrite($"处理WebSocket消息时发生错误: {ex.Message}");
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// 更新最后已知的WebSocket状态
        /// </summary>
        private static void SetLastKnownState(WebSocketState state)
        {
            lock (_stateLock)
            {
                _lastKnownState = state;
            }
        }

        /// <summary>
        /// 接收消息的后台任务
        /// </summary>
        private static async Task StartReceivingMessages(CancellationToken cancellationToken)
        {
            try
            {
                byte[] buffer = new byte[4096];
                StringBuilder messageBuilder = new StringBuilder();

                while (!cancellationToken.IsCancellationRequested && !_wsDisabled && _webSocket != null && _webSocket.State == WebSocketState.Open)
                {
                    WebSocketReceiveResult result;

                    try
                    {
                        result = await _webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        // 操作被取消，退出循环
                        break;
                    }
                    catch (WebSocketException ex)
                    {
                        XLogger.OnlyWrite($"WebSocket接收数据时发生错误: {ex.Message}");
                        IncrementFailureCount();
                        // 连接可能已断开，尝试重连
                        if (_autoReconnect && !_wsDisabled)
                        {
                            _ = AttemptReconnection();
                        }
                        break;
                    }

                    if (result.MessageType == WebSocketMessageType.Close)
                    {
                        XLogger.OnlyWrite($"服务器关闭了WebSocket连接: {result.CloseStatusDescription}");
                        if (_autoReconnect && !_wsDisabled)
                        {
                            _ = AttemptReconnection();
                        }
                        break;
                    }

                    string messagePart = Encoding.UTF8.GetString(buffer, 0, result.Count);
                    messageBuilder.Append(messagePart);

                    if (result.EndOfMessage)
                    {
                        string fullMessage = messageBuilder.ToString();
                        messageBuilder.Clear();

                        //XLogger.OnlyWrite($"收到WebSocket消息: {fullMessage}");

                        // 尝试解析消息类型并路由到相应的处理器
                        await ProcessReceivedMessage(fullMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                XLogger.OnlyWrite($"接收WebSocket消息时发生错误: {ex.Message}");
                IncrementFailureCount();
            }
            finally
            {
                if (_autoReconnect && !_wsDisabled && !cancellationToken.IsCancellationRequested && _webSocket?.State != WebSocketState.Open)
                {
                    _ = AttemptReconnection();
                }
            }
        }

        /// <summary>
        /// 重新启用WebSocket功能
        /// </summary>
        /// <returns>WebSocket重新启用是否成功</returns>
        public static async Task<bool> ReenableWebSocketAsync()
        {
            if (!_wsDisabled)
            {
                XLogger.OnlyWrite("WebSocket功能未被禁用，无需重新启用");
                return true;
            }

            XLogger.OnlyWrite("尝试重新启用WebSocket功能");
            _wsDisabled = false; //wss://ws.danding.vip
            ResetFailureCount();

            // 如果之前有连接过，则尝试重新连接
            if (!string.IsNullOrEmpty(_lastConnectedUrl))
            {
                bool reconnected = await ConnectAsync(_lastConnectedUrl, _lastUsedToken ?? string.Empty);
                if (reconnected)
                {
                    XLogger.OnlyWrite("WebSocket功能已重新启用，并成功连接到服务器");
                    return true;
                }
                else
                {
                    XLogger.OnlyWrite("WebSocket功能已重新启用，但连接服务器失败");
                    return false;
                }
            }

            XLogger.OnlyWrite("WebSocket功能已重新启用，但尚未连接到服务器");
            return true;
        }
    }
}