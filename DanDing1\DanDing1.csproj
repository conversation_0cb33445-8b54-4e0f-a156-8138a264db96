﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows10.0.17763.0</TargetFramework>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <ApplicationIcon>egg-icon.ico</ApplicationIcon>
    <UseWPF>true</UseWPF>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <Platforms>x86</Platforms>
    <UseWindowsForms>False</UseWindowsForms>
    <StartupObject>DanDing1.App</StartupObject>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x86'">
    <DebugType>full</DebugType>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="egg-icon.ico" />
  </ItemGroup>

	<ItemGroup>
    <PackageReference Include="Cronos" Version="0.11.0" />
    <PackageReference Include="Hardcodet.NotifyIcon.Wpf" Version="1.1.0" />
    <PackageReference Include="Lazy.Captcha.Core" Version="2.1.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.2" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="Microsoft.Toolkit.Uwp.Notifications" Version="7.1.3" />
    <PackageReference Include="Minio" Version="6.0.4" />
    <PackageReference Include="System.Diagnostics.PerformanceCounter" Version="9.0.5" />
    <PackageReference Include="WPF-UI" Version="4.0.2" />
    <PackageReference Include="YoloSharp" Version="6.0.2" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Assets\egg-icon-256.png" />
    <None Remove="Assets\胜利、失败界面.jpg" />
    <None Remove="Assets\达摩奖励界面.jpg" />
    <None Remove="egg-icon.ico" />
    <None Remove="tmp.txt" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Assets\egg-icon-256.png">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Assets\胜利、失败界面.jpg" />
    <Resource Include="Assets\达摩奖励界面.jpg" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DamoControlKit\DamoControlKit.csproj" />
    <ProjectReference Include="..\ScriptEngine\ScriptEngine.csproj" />
    <ProjectReference Include="..\XHelper\XHelper.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="ShareX.HelpersLib">
      <HintPath>..\lib\ShareX.HelpersLib.dll</HintPath>
    </Reference>
    <Reference Include="ShareX.ImageEffectsLib">
      <HintPath>..\lib\ShareX.ImageEffectsLib.dll</HintPath>
    </Reference>
    <Reference Include="ShareX.MediaLib">
      <HintPath>..\lib\ShareX.MediaLib.dll</HintPath>
    </Reference>
    <Reference Include="ShareX.ScreenCaptureLib">
      <HintPath>..\lib\ShareX.ScreenCaptureLib.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Views\Pages\Game4Page.xaml.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Update="Views\Pages\Game3Page.xaml.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Update="Views\Pages\Game2Page.xaml.cs">
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>
</Project>