﻿using DanDing1.Helpers;
using ScriptEngine.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;

namespace DanDing1.Views.Windows
{
    public partial class HistoryRecordWindow : Window
    {
        private Dictionary<DateTime, string> _logEntries;
        private string _logtxt = "";
        private ScreenshotRecorder _recorder;
        private Dictionary<DateTime, byte[]> _screenshots;

        protected override void OnClosed(EventArgs e)
        {
            // 释放 _recorder 的资源
            if (_recorder != null)
            {
                // 如果 _recorder 实现了 IDisposable 接口
                if (_recorder is IDisposable disposable)
                {
                    disposable.Dispose();
                }
                else
                {
                    // 手动释放 _recorder 的资源（如果有）
                    _recorder.ClearAllScreenshots();
                    _recorder.Dispose();
                    _recorder = null;
                    GC.Collect();
                }
            }

            // 调用基类的 OnClosed 方法
            base.OnClosed(e);
        }

        public HistoryRecordWindow(string srPath)
        {
            InitializeComponent();

            if (!File.Exists(srPath))
            {
                MessageBox.Show("录制文件不存在！无法继续！", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Close();
                return;
            }

            try
            {
                _recorder = new ScreenshotRecorder();
                _recorder.ReadFromFile(srPath);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Close();
                return;
            }

            InitData();
            PopulateLogList();
        }

        private void DisplayLogEntry(DateTime selectedTime)
        {
            if (_logEntries.TryGetValue(selectedTime, out string logEntry))
            {
                LogTextBox.Text = logEntry;
            }
            else
            {
                LogTextBox.Text = "No log entry for this time.";
            }
        }

        private void DisplayScreenshot(DateTime selectedTime)
        {
            if (LogListBox.SelectedItem is LogEntry selectedEntry && selectedEntry.HasScreenshot)
            {
                // 使用原始截图时间查找截图数据
                if (_screenshots.TryGetValue(selectedEntry.OriginalScreenshotTime, out byte[] screenshotData))
                {
                    var image = new BitmapImage();
                    using (var mem = new MemoryStream(screenshotData))
                    {
                        mem.Position = 0;
                        image.BeginInit();
                        image.CreateOptions = BitmapCreateOptions.PreservePixelFormat;
                        image.CacheOption = BitmapCacheOption.OnLoad;
                        image.UriSource = null;
                        image.StreamSource = mem;
                        image.EndInit();
                    }
                    ScreenshotImage.Source = image;
                }
                else
                {
                    ScreenshotImage.Source = null;
                }
            }
            else
            {
                ScreenshotImage.Source = null;
            }
        }

        /// <summary>
        /// 导出所有截图按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ExportAllImagesButton_Click(object sender, RoutedEventArgs e)
        {
            _recorder.ExportToFolder(".\\runtimes\\Debug\\" + DateTime.Now.ToString("yyyyMMddHHmmss"));
            //打开文件夹
            System.Diagnostics.Process.Start("explorer.exe", ".\\runtimes\\Debug\\" + DateTime.Now.ToString("yyyyMMddHHmmss"));
            MessageBox.Show("导出成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportCurrentImageButton_Click(object sender, RoutedEventArgs e)
        {
            // 检查是否有选中的日志项
            if (LogListBox.SelectedItem is LogEntry selectedEntry && selectedEntry.HasScreenshot)
            {
                // 获取当前截图数据
                if (_screenshots.TryGetValue(selectedEntry.OriginalScreenshotTime, out byte[] screenshotData))
                {
                    // 创建 SaveFileDialog
                    var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                    {
                        Filter = "BMP 图片|*.bmp", // 文件类型过滤器
                        FileName = $"截图_{selectedEntry.Time:yyyyMMdd_HHmmss}.bmp", // 默认文件名
                        Title = "保存截图" // 对话框标题
                    };

                    // 显示对话框并获取用户选择的路径
                    if (saveFileDialog.ShowDialog() == true)
                    {
                        try
                        {
                            // 将字节数组转换为 Bitmap
                            using (var ms = new MemoryStream(screenshotData))
                            using (var bitmap = new System.Drawing.Bitmap(ms))
                            {
                                // 保存为 BMP 文件
                                bitmap.Save(saveFileDialog.FileName, System.Drawing.Imaging.ImageFormat.Bmp);
                            }

                            MessageBox.Show("截图保存成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"保存截图时出错：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("未找到截图数据！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            else
            {
                MessageBox.Show("请先选择一个包含截图的日志项！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void InitData()
        {
            _logtxt = _recorder._logtxt;
            _screenshots = _recorder._screenshots;
            _logEntries = ParseLogEntries(_logtxt); // 解析日志并缓存
        }

        private void LogListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (LogListBox.SelectedItem is LogEntry selectedEntry)
            {
                // 显示截图
                DisplayScreenshot(selectedEntry.Time);

                // 显示日志
                DisplayLogEntry(selectedEntry.Time);
            }
        }

        private Dictionary<DateTime, string> ParseLogEntries(string logText)
        {
            var logEntries = new Dictionary<DateTime, string>();

            foreach (var line in logText.Split('\n'))
            {
                if (TryParseLogLine(line, out DateTime logTime, out string logTextContent))
                {
                    // 将时间截断到秒级
                    var truncatedTime = new DateTime(logTime.Year, logTime.Month, logTime.Day,
                                                     logTime.Hour, logTime.Minute, logTime.Second);

                    if (!logEntries.ContainsKey(truncatedTime))
                    {
                        logEntries[truncatedTime] = line.Trim();
                    }
                }
            }

            return logEntries;
        }

        private void PopulateLogList()
        {
            // 合并 _screenshots 和 _logEntries 的时间键，并截断到秒级
            var allTimes = new HashSet<DateTime>();

            foreach (var time in _screenshots.Keys)
            {
                var truncatedTime = new DateTime(time.Year, time.Month, time.Day,
                                                time.Hour, time.Minute, time.Second);
                allTimes.Add(truncatedTime);
            }

            foreach (var time in _logEntries.Keys)
            {
                var truncatedTime = new DateTime(time.Year, time.Month, time.Day,
                                                time.Hour, time.Minute, time.Second);
                allTimes.Add(truncatedTime);
            }

            // 创建 LogEntry 列表
            var logEntries = new List<LogEntry>();

            foreach (var time in allTimes)
            {
                // 检查是否有截图
                var screenshotTime = _screenshots.Keys
                    .FirstOrDefault(t =>
                        t.Year == time.Year && t.Month == time.Month && t.Day == time.Day &&
                        t.Hour == time.Hour && t.Minute == time.Minute && t.Second == time.Second);

                bool hasScreenshot = screenshotTime != default;

                // 检查是否有日志
                bool hasLog = _logEntries.Keys.Any(t =>
                    t.Year == time.Year && t.Month == time.Month && t.Day == time.Day &&
                    t.Hour == time.Hour && t.Minute == time.Minute && t.Second == time.Second);

                string status;
                if (hasScreenshot && hasLog)
                    status = "截图 + 日志";
                else if (hasScreenshot)
                    status = "截图";
                else if (hasLog)
                    status = "日志";
                else
                    status = "无";

                logEntries.Add(new LogEntry
                {
                    Time = time,
                    Status = status,
                    DisplayText = $"{time:yyyy/MM/dd HH:mm:ss} ({status})",
                    HasScreenshot = hasScreenshot,
                    HasLog = hasLog,
                    OriginalScreenshotTime = hasScreenshot ? screenshotTime : default // 存储原始截图时间
                });
            }

            // 按时间排序
            var sortedEntries = logEntries.OrderBy(entry => entry.Time).ToList();

            // 绑定到 ListBox
            LogListBox.ItemsSource = sortedEntries;
        }

        private bool TryParseLogLine(string line, out DateTime logTime, out string logText)
        {
            logTime = DateTime.MinValue;
            logText = null;

            if (string.IsNullOrEmpty(line) || line.Length < 30 || line[0] != '[')
                return false;

            int timeEndIndex = line.IndexOf(']', 1);
            if (timeEndIndex == -1)
                return false;

            string timePart = line.Substring(1, timeEndIndex - 1);
            if (!DateTime.TryParseExact(timePart, "yyyy-MM-dd HH:mm:ss.ffff", null, System.Globalization.DateTimeStyles.None, out logTime))
                return false;

            int logTextStartIndex = line.IndexOf(']', timeEndIndex + 1) + 1;
            if (logTextStartIndex == 0)
                return false;

            logText = line.Substring(logTextStartIndex).Trim();
            return true;
        }

        // 自定义 LogEntry 类
        private class LogEntry
        {
            public string DisplayText { get; set; }
            public bool HasLog { get; set; }
            public bool HasScreenshot { get; set; }
            public DateTime OriginalScreenshotTime { get; set; }
            public string Status { get; set; }
            public DateTime Time { get; set; }
            // 新增字段，存储原始截图时间

            public override string ToString() => DisplayText;
        }
    }
}