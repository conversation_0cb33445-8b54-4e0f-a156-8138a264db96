﻿using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;

namespace XHelper
{
    public class XImageProcessor
    {
        public byte[] GenerateThumbnail(byte[] originalBytes, int maxSize = 200)
        {
            using (var inputStream = new MemoryStream(originalBytes))
            using (var originalImage = new Bitmap(inputStream))
            {
                // 计算保持比例的缩略图尺寸
                var (newWidth, newHeight) = CalculateNewSize(originalImage.Width, originalImage.Height, maxSize);

                // 生成高质量缩略图
                using (var thumbnail = new Bitmap(newWidth, newHeight))
                using (var graphics = Graphics.FromImage(thumbnail))
                {
                    ConfigureHighQualityGraphics(graphics);
                    graphics.DrawImage(originalImage, 0, 0, newWidth, newHeight);

                    // 保存为JPEG格式并压缩
                    return SaveAsCompressedJpeg(thumbnail);
                }
            }
        }

        private (int width, int height) CalculateNewSize(int originalWidth, int originalHeight, int maxSize)
        {
            if (originalWidth <= maxSize && originalHeight <= maxSize)
                return (originalWidth, originalHeight);

            double ratio = (double)originalWidth / originalHeight;
            return ratio > 1
                ? (maxSize, (int)(maxSize / ratio))
                : ((int)(maxSize * ratio), maxSize);
        }

        private void ConfigureHighQualityGraphics(Graphics graphics)
        {
            graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
            graphics.SmoothingMode = SmoothingMode.HighQuality;
            graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
            graphics.CompositingQuality = CompositingQuality.HighQuality;
        }

        private byte[] SaveAsCompressedJpeg(Image image, long quality = 80L)
        {
            using (var outputStream = new MemoryStream())
            {
                var jpegEncoder = GetJpegEncoder();
                var encoderParams = new EncoderParameters(1)
                {
                    Param = { [0] = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, quality) }
                };

                image.Save(outputStream, jpegEncoder, encoderParams);
                return outputStream.ToArray();
            }
        }

        private ImageCodecInfo GetJpegEncoder()
        {
            return ImageCodecInfo.GetImageEncoders()
                .FirstOrDefault(enc => enc.FormatID == ImageFormat.Jpeg.Guid)
                   ?? throw new InvalidOperationException("JPEG encoder not found");
        }
    }
}