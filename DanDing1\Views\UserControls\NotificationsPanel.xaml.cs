using System;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.Windows;
using System.Windows.Controls;
using DanDing1.Models;
using DanDing1.Services;

namespace DanDing1.Views.UserControls
{
    /// <summary>
    /// NotificationsPanel.xaml 的交互逻辑
    /// </summary>
    public partial class NotificationsPanel : UserControl
    {
        private NotificationService _notificationService;

        public NotificationsPanel()
        {
            InitializeComponent();

            // 获取通知服务实例
            _notificationService = NotificationService.Instance;

            // 监听通知集合变化
            _notificationService.Notifications.CollectionChanged += Notifications_CollectionChanged;

            // 设置最大高度为窗口高度的70%
            Loaded += (s, e) =>
            {
                if (Window.GetWindow(this) is Window parentWindow)
                {
                    MaxHeight = parentWindow.ActualHeight * 0.7;
                    parentWindow.SizeChanged += (sender, args) =>
                    {
                        MaxHeight = parentWindow.ActualHeight * 0.7;
                    };
                }
            };
        }

        private void Notifications_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            // 处理添加通知
            if (e.Action == NotifyCollectionChangedAction.Add)
            {
                foreach (NotificationModel notification in e.NewItems)
                {
                    AddNotificationToPanel(notification);
                }
            }
            // 处理移除通知
            else if (e.Action == NotifyCollectionChangedAction.Remove)
            {
                foreach (NotificationModel notification in e.OldItems)
                {
                    // 查找与此通知模型关联的控件并淡出
                    RemoveNotificationFromPanel(notification);
                }
            }
            // 处理清空通知
            else if (e.Action == NotifyCollectionChangedAction.Reset)
            {
                // 清空前先让所有通知开始淡出
                foreach (NotificationControl control in NotificationsItemsControl.Items)
                {
                    control.BeginClose();
                }
            }
        }

        private void AddNotificationToPanel(NotificationModel notification)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"添加通知: {notification.Title}");
                var control = new NotificationControl
                {
                    DataContext = notification
                };

                // 注册通知关闭完成事件
                control.CloseCompleted += (sender, args) =>
                {
                    Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            if (NotificationsItemsControl.Items.Contains(sender))
                            {
                                NotificationsItemsControl.Items.Remove(sender);
                                System.Diagnostics.Debug.WriteLine("通知控件已从ItemsControl中移除");
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"移除通知控件失败: {ex.Message}");
                        }
                    }));
                };

                // 添加到面板的第一个位置（底部）
                NotificationsItemsControl.Items.Insert(0, control);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加通知到面板失败: {ex.Message}");
            }
        }

        private void RemoveNotificationFromPanel(NotificationModel notification)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine($"尝试移除通知: {notification.Title}");
                    bool found = false;

                    // 查找对应的通知控件
                    for (int i = 0; i < NotificationsItemsControl.Items.Count; i++)
                    {
                        if (NotificationsItemsControl.Items[i] is NotificationControl control &&
                            control.DataContext == notification)
                        {
                            System.Diagnostics.Debug.WriteLine("找到匹配的通知控件，开始关闭");
                            control.BeginClose();
                            found = true;
                            break;
                        }
                    }

                    if (!found)
                    {
                        System.Diagnostics.Debug.WriteLine("未找到匹配的通知控件");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"移除通知错误: {ex.Message}");
                }
            });
        }
    }
}