﻿using DanDing1.Helpers;
using DanDing1.Views.Windows;
using System.Collections.ObjectModel;
using Wpf.Ui.Controls;
using XHelper;

namespace DanDing1.ViewModels.Windows
{
    public partial class MainWindowViewModel : ObservableObject
    {
        /// <summary>
        /// 最大游戏数量
        /// </summary>
        private const int MaxGameCount = 4;

        [ObservableProperty]
        private string _applicationTitle = "DanDing助手-正式版 V";

        [ObservableProperty]
        private ObservableCollection<object> _footerMenuItems = new()
        {
            new NavigationViewItem()
            {
                Content = "偏好",
                Tag = "偏好",
                ToolTip = "偏好",
                Icon = new SymbolIcon { Symbol = SymbolRegular.Cloud24 },
                TargetPageType = typeof(Views.Pages.UserConfigPage)
            },
            new NavigationViewItem()
            {
                Content = "设置",
                Tag = "设置",
                ToolTip = "设置",
                Icon = new SymbolIcon { Symbol = SymbolRegular.Settings24 },
                TargetPageType = typeof(Views.Pages.SettingsPage)
            },
        };

        /// <summary>
        /// 是否运行中
        /// </summary>
        [ObservableProperty]
        private string _IsRunning = "False";

        [ObservableProperty]
        private ObservableCollection<object> _menuItems = new();

        /// <summary>
        /// 是否运行中
        /// </summary>
        [ObservableProperty]
        private string _RunningCount = "0";

        /// <summary>
        /// 托盘图标服务，用于访问调度器视图模型
        /// </summary>
        [ObservableProperty]
        private DanDing1.Services.TrayIconService _trayIconService;

        [ObservableProperty]
        private ObservableCollection<Wpf.Ui.Controls.MenuItem> _trayMenuItems = new()
        {
            new Wpf.Ui.Controls.MenuItem { Header = "Home", Tag = "tray_home" }
        };

        /// <summary>
        /// 当前游戏数量（1-4）
        /// </summary>
        private int GameCount = 1;

        private SuperMultiGamesWindow Superwindow;

        public MainWindowViewModel(IServiceProvider serviceProvider)
        {
            ApplicationTitle += GlobalData.Instance.appConfig.Info.Now_Ver;
            if (GlobalData.IsBetaVersion)
                ApplicationTitle += " Beta";

            // 初始化托盘图标服务
            TrayIconService = DanDing1.Services.TrayIconService.Instance;

            // 加载默认游戏数量设置
            int defaultGameCount = XConfig.LoadValueFromFile<int>("Common", "DefaultGameCount");
            // 确保值在1-4之间，默认为1
            if (defaultGameCount < 1 || defaultGameCount > MaxGameCount)
            {
                defaultGameCount = 1;
            }

            // 根据默认设置初始化游戏数量
            GameCount = defaultGameCount;
            // 更新菜单项
            UpdateMenuItems();
            // 触发游戏数量变更事件
            GameCountChanged?.Invoke(GameCount);
            ServiceProvider = serviceProvider;

            //设置主页超级多开的进行时开关
            GlobalData.Instance.SetRunningUI += (c) =>
            {
                RunningCount = c.ToString();
                if (c == 0)
                    IsRunning = "False";
                else
                    IsRunning = "True";
            };
        }

        /// <summary>
        /// 按钮被按下时
        /// </summary>
        public Action<int> GameCountChanged { get; set; }

        public IServiceProvider ServiceProvider { get; }

        /// <summary>
        /// 应用程序退出时清理资源
        /// </summary>
        public void CleanupOnExit()
        {
            // 清理其他资源
            Superwindow = null;
        }

        /// <summary>
        /// 创建导航菜单项
        /// </summary>
        /// <param name="text">显示文本</param>
        /// <param name="symbol">图标</param>
        /// <param name="targetPageType">目标页面类型</param>
        /// <returns>配置好的NavigationViewItem</returns>
        private NavigationViewItem CreateNavigationViewItem(string text, SymbolRegular symbol, Type targetPageType)
        {
            return new NavigationViewItem
            {
                Content = text,
                Icon = new SymbolIcon { Symbol = symbol },
                Tag = text,
                ToolTip = text,
                TargetPageType = targetPageType
            };
        }

        /// <summary>
        /// 获取指定索引的游戏名称
        /// </summary>
        private string GetGameName(int index)
        {
            return index switch
            {
                1 => Utils.UserGameName1,
                2 => Utils.UserGameName2,
                3 => Utils.UserGameName3,
                4 => Utils.UserGameName4,
                _ => $"游戏{index}"
            };
        }

        /// <summary>
        /// 获取指定索引的游戏页面类型
        /// </summary>
        private Type GetGamePageType(int index)
        {
            return index switch
            {
                1 => typeof(Views.Pages.Game1Page),
                2 => typeof(Views.Pages.Game2Page),
                3 => typeof(Views.Pages.Game3Page),
                4 => typeof(Views.Pages.Game4Page),
                _ => typeof(Views.Pages.Game1Page)
            };
        }

        /// <summary>
        /// 添加多开游戏
        /// </summary>
        [RelayCommand]
        private void OnAddGames()
        {
            if (GameCount == MaxGameCount)
            {
                return;
            }
            GameCount++;
            UpdateMenuItems();
            GameCountChanged?.Invoke(GameCount);
        }

        /// <summary>
        /// 打开定时调度窗口
        /// </summary>
        [RelayCommand]
        private void OnOpenSchedulerWindow()
        {
            if (GlobalData.Instance.appConfig.IsFree)
            {
                Utils.ShowMessage("提示", "当前用户为试用用户，试用用户没有此功能的使用权限，无法使用此功能！");
                return;
            }
            if (!GlobalData.Instance.appConfig.IsLogin)
            {
                Utils.ShowMessage("提示", "当前用户未登录，请先登录！");
                return;
            }
            if (GlobalData.Instance.appConfig.User_Points < 7)
            {
                Utils.ShowMessage("提示", $"当前用户已经登录，但是用户积分只有{GlobalData.Instance.appConfig.User_Points}，还需{7 - GlobalData.Instance.appConfig.User_Points}才能继续使用此功能！");
                return;
            }

            try
            {
                // 调用 TrayIconService 的 ShowSchedulerWindow 方法
                DanDing1.Services.TrayIconService.Instance.ShowSchedulerWindow();
            }
            catch (Exception ex)
            {
                XLogger.Error($"打开定时调度窗口失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 打开超级多开
        /// </summary>
        [RelayCommand]
        private void OnOpenSuperMultiGamesWindow()
        {
            if (GlobalData.Instance.appConfig.IsFree)
            {
                Utils.ShowMessage("提示", "当前用户为试用用户，试用用户没有此功能的使用权限，无法使用此功能！");
                return;
            }
            if (!GlobalData.Instance.appConfig.IsLogin)
            {
                Utils.ShowMessage("提示", "当前用户未登录，请先登录！");
                return;
            }
            if (GlobalData.Instance.appConfig.User_Points < 7)
            {
                Utils.ShowMessage("提示", $"当前用户已经登录，但是用户积分只有{GlobalData.Instance.appConfig.User_Points}，还需{7 - GlobalData.Instance.appConfig.User_Points}才能继续使用此功能！");
                return;
            }

            if (Superwindow is not null)
            {
                Superwindow?.Activate();
                Superwindow?.Show();
                return;
            }
            Superwindow ??= ServiceProvider.GetService(typeof(SuperMultiGamesWindow)) as SuperMultiGamesWindow;
            Superwindow?.Show();
        }

        /// <summary>
        /// 减少游戏数量
        /// </summary>
        [RelayCommand]
        private void OnReduceGames()
        {
            if (GameCount <= 1)
            {
                return;
            }
            GameCount--;
            UpdateMenuItems();
            GameCountChanged?.Invoke(GameCount);
        }

        /// <summary>
        /// 根据当前游戏数量更新菜单项
        /// </summary>
        private void UpdateMenuItems()
        {
            MenuItems.Clear();

            // 添加首页菜单项
            MenuItems.Add(CreateNavigationViewItem("首页", SymbolRegular.Home24, typeof(Views.Pages.LoadPage)));

            // 添加游戏菜单项
            for (int i = 1; i <= GameCount; i++)
            {
                string gameName = GetGameName(i);
                Type pageType = GetGamePageType(i);
                MenuItems.Add(CreateNavigationViewItem(gameName, SymbolRegular.Games24, pageType));
            }

            // 添加日志菜单项
            MenuItems.Add(CreateNavigationViewItem("日志", SymbolRegular.Notebook24, typeof(Views.Pages.LogPage)));
        }
    }
}