﻿using DanDing1.Interface;
using DanDing1.Models;
using DanDing1.Views.Windows;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace DanDing1.Tests
{
    internal class Test_AppStart : ITest
    {
        public void Test()
        {
#if DEBUG
            //new HistoryRecordWindow().Show();
#endif
        }
    }
}