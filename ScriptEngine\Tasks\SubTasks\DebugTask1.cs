﻿using DamoControlKit.Function;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace ScriptEngine.Tasks
{
    internal class DebugTask1 : BaseTask
    {
        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "");
        }

        private List<string> findList = new List<string>();
        private int loopCounter = 0; // 添加循环计数器

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            log.Info("调试任务1开始，监控游戏界面，输出找到的图片名.");
            var _mp = Mp.Filter(".");//
            while (true)
            {
                foreach (var item in _mp.PicList)
                {
                    if (item.Find())
                    {
                        log.Info("找到图片：" + item.Name);
                        if (!findList.Contains(item.Name))
                            findList.Add(item.Name);
                        Sleep(100);
                    }
                }
                Sleep(200);
                log.Info_Green("[汇总] 找到的图数：" + findList.Count + "，当前总图数：" + _mp.PicList.Count);
                log.Info_Green("[场景] 当前游戏所在场景为：" + Scene.NowScene);

                loopCounter++;
                if (loopCounter >= 5)
                {
                    //将未找到的图片名输出到txt文件
                    log.Debug("输出未找到的图片名到txt文件...");
                    File.WriteAllLines("未找到的图片.txt", _mp.PicList.Where(p => !findList.Contains(p.Name)).Select(p => p.Name));
                    loopCounter = 0; // 重置计数器
                }
            }
        }
    }
}