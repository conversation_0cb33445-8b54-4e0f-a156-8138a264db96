using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// 将null值转换为Visibility.Collapsed，非null值转换为Visibility.Visible的转换器
    /// </summary>
    public class NullToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value == null ? Visibility.Collapsed : Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}