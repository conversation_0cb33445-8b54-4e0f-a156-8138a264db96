﻿#pragma checksum "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4E9FA5C88421BABEADF8FD8E12D0F4A41173969B"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Views.Windows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Windows {
    
    
    /// <summary>
    /// UpdateWindow
    /// </summary>
    public partial class UpdateWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 43 "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NewVer;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NowVer;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UpdataLog;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button SkipVersionButton;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button RemindTomorrowButton;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button ManualDownloadButton;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button InstallButton;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar DownloadProgressBar;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/windows/updatewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.NewVer = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.NowVer = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.UpdataLog = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.SkipVersionButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 82 "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml"
            this.SkipVersionButton.Click += new System.Windows.RoutedEventHandler(this.SkipVersionButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.RemindTomorrowButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 93 "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml"
            this.RemindTomorrowButton.Click += new System.Windows.RoutedEventHandler(this.RemindTomorrowButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ManualDownloadButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 102 "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml"
            this.ManualDownloadButton.Click += new System.Windows.RoutedEventHandler(this.ManualDownloadButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.InstallButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 110 "..\..\..\..\..\..\Views\Windows\UpdateWindow.xaml"
            this.InstallButton.Click += new System.Windows.RoutedEventHandler(this.InstallButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.DownloadProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

