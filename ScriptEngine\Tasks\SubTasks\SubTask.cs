﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System.Runtime.InteropServices;

namespace ScriptEngine.Tasks
{
    /// <summary>
    /// 辅助检测任务
    /// </summary>
    public class SubTask : BaseTask
    {
        /// <summary>
        /// 御魂满了通知事件
        /// </summary>
        public PushYuHunFull? OnPushYuHunFull;

        private UserNotificationMessage_Struct _UserNotification = new()
        {
            Message = $"清理御魂次数：{0}，处理悬赏次数：{0}，卡屏次数：{0}，执行定时任务次数：{0}",
            ClearYuHunCount = 0,
            HandleRewardCount = 0,
            CardinalityCount = 0,
            DoTimerTaskCount = 0,
        };

        private int Cardinality_Count = 1;

        private int Cardinality_ExitCount = 0;

        private string Cardinality_LastColor1 = "";

        private string Cardinality_LastColor2 = "";

        private string Cardinality_LastColor3 = "";

        private string Cardinality_LastColor4 = "";

        private int DmisNull_ExitCount = 0;

        /// <summary>
        /// 御魂满了，清理还是退出
        /// 结束任务（True）
        /// 清理御魂（False）
        /// </summary>
        private bool FullYuHun_Exit = false;

        private string? KaPing_Do_String;

        private int now_Hwnd = 0;

        /// <summary>
        /// 扫描间隔 s
        /// </summary>
        private int scan_sec = 1;

        private int Scan_WindowSize_Count = 0;

        /// <summary>
        /// 游戏卡屏时间委托
        /// </summary>
        public delegate void GameStuckEventHandler();

        /// <summary>
        /// 定时任务触发委托
        /// </summary>
        public delegate void TimerTaskRunEventHandler();

        /// <summary>
        /// 游戏已重启事件
        /// </summary>
        public event Action? OnGameRestarted;

        /// <summary>
        /// 游戏卡屏通知事件
        /// </summary>
        public event GameStuckEventHandler? OnGameStuck;

        /// <summary>
        /// 定时任务触发通知事件
        /// </summary>
        public event TimerTaskRunEventHandler? OnTimerTaskRunEvent;

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className = "Sub")
        {
            if ((now_Hwnd = dm.GetBindWindow()) == 0)
                throw new Exception("绑定窗口失败，请重新尝试！");
            base.Init(dB, dm, ct, className);
            Db.ShareData.SubTask = this;
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            //初始化御魂满清理状态
            Db.UserConfigs.TryGetValue("FullYuHun_Do", out object _T);
            FullYuHun_Exit = (string)_T == "结束任务" ? true : false;

            //初始化 KaPing_Do
            Db.UserConfigs.TryGetValue("KaPing_Do", out object _T2);
            KaPing_Do_String = (string)_T2;

            log.Debug($"悬赏接受状态：{Db.GameSetting.XuanShang} 御魂满后结束任务：{FullYuHun_Exit} 卡屏后操作：{KaPing_Do_String}");
            if (Db.GameSetting.IsRecord)
                Db._recorder = new ScreenshotRecorder(Db.GameSetting.RecordQuality);

            while (true)
            {
                Sleep(scan_sec * 1000);
                Do_StartToTingYuan();       //是否需要执行到庭院操作
                Check_TimerTask();          //检查定时任务
                Scan_FullYuHun();           //检测御魂满了
                Scan_YiDiDengLu();          //检测异地登录
                Scan_XuanShang();           //检测悬赏
                Scan_WindowSize();          //检测窗口大小
                ImageRecorder();            //调试图像录制
                DmisNull_Exit();            //检测游戏窗口是否失效
                if (Db.ShareData.MainNowTask != "等待" && Db.ShareData.MainNowTask != "半自动" &&
                    Db.ShareData.MainNowTask != "抽厕纸")
                    Scan_Cardinality();     //检测卡顿

                //更新用户通知信息
                _UserNotification.Message = $"清理御魂次数：{_UserNotification.ClearYuHunCount}，处理悬赏次数：{_UserNotification.HandleRewardCount}，卡屏次数：{_UserNotification.CardinalityCount}，执行定时任务次数：{_UserNotification.DoTimerTaskCount}";
                UserNotificationMessage = _UserNotification.Message;
            }
        }

        /// <summary>
        /// 御魂满了，进入式神录进行御魂清理操作
        /// </summary>
        private void _FullYuHun()
        {
            if (Dm is null || Dm.IsBind(now_Hwnd) == 0) return;
            var pics_on = Mp.Filter("Sub.御魂满");
            Sleep(1000);
            log.Info("当前御魂满了，进入式神录进行御魂清理操作..");
            Fast.Click(1104, 622, 1161, 669);
            Sleep(2000);
            log.Info("进入御魂配置界面..");
            Fast.Click(550, 394, 590, 434);
            log.Info("等待御魂清理提示..");
            if (pics_on.Wait(5))
            {
                log.Info("点击 去清理..");
                Sleep(2000);
                Fast.Click(491, 405, 584, 442);
            }
            Sleep(1000);
            log.Info("点击 奉纳..");
            Fast.Click(1164, 214, 1214, 282);
            Sleep(1000);
            log.Info("点击 贪吃鬼..");
            Fast.Click(1170, 627, 1215, 669);
            Sleep(1000);
            log.Info("点击 进食习惯..");
            Fast.Click(836, 539, 958, 579);
            Sleep(1000);
            log.Info("点击 立即进食..");
            Fast.Click(941, 604, 996, 654);
            Sleep(1000);
            Fast.Click(709, 412, 826, 444);
            Sleep(1500);
            log.Info("退出 贪吃鬼..");
            Fast.Click(1154, 215, 1219, 265);
            Fast.Click(1154, 215, 1219, 265);
            Sleep(2000);
            Fast.Click(935, 214, 965, 240);
            Fast.Click(935, 214, 965, 240);
            Sleep(1000);
            int i = 0;
            bool is_end = false;
            while (!is_end)
            {
                Sleep(1000);
                log.Info($"开始奉纳五星御魂{i + 1}次..请将御魂排序设置为星级升序！");
                Fast.LongClick(144, 278, 2000);
                Sleep(2000);
                //OCR
                string ocr_str = Fast.Ocr_Local(887, 571, 1016, 611);
                if (!ocr_str.Contains("200000"))
                {
                    log.Warn("当前奉纳御魂获得的金币不为200000，结束清理御魂..");
                    is_end = true;
                    continue;
                }
                Fast.Click(835, 646, 916, 688);
                Sleep(5000);
                Fast.Click(1027, 194, 1076, 276);
                Sleep(1000);
                i++;
            }
            log.Info("御魂清理结束，点击返回到庭院..");
            Fast.Click(34, 25, 65, 55);
            Sleep(1500);
            Fast.Click(34, 25, 65, 55);
            Sleep(2000);

            log.Info("通知主任务继续..");
        }

        private void Check_TimerTask()
        {
            if (!(Db?.TimerTask?.isSubScan ?? false)) return;
            if (Db?.TimerTask?.Check_AllTask() ?? false)
            {
                _UserNotification.DoTimerTaskCount++;
                OnTimerTaskRunEvent?.Invoke();
            }
        }

        /// <summary>
        /// 点击进入游戏后的操作
        /// </summary>
        private void ClickToGameed()
        {
            Sleep(2000);
            var X_pics = Mp.Filter("Sub.叉");
            int x, y;
        WaitMain:
            Sleep(1000);
            while (X_pics.FindAll(out x, out y))
            {
                log.Info("关闭牛皮癣广告弹窗..");
                Fast.Click(x, y);
                Sleep(3000);
            }

            var res = Scene.NowScene;
            if (res.Contains("庭院"))
            {
                log.Info_Green("重启恢复游戏场景完成，当前界面为庭院，为您继续完成任务！");
                return;
            }
            Sleep(3000);
            log.Warn("没有识别到相应的游戏场景，尝试再次判断..");
            //是不是没有点进入游戏
            res = Fast.Ocr_String(713, 700, 919, 718);
            if (res.Contains("网易"))
            {
                res = Fast.Ocr_String(529, 509, 679, 542).Replace("\r", null);
                log.Info($"当前默认的服务器为：{res}(非准确)，点击进入游戏..");
                Fast.Click(566, 582, 697, 609);
                Sleep(1000);
            }
            goto WaitMain;
        }

        private int ColorDifference(string color1, string color2)
        {
            if (string.IsNullOrEmpty(color1) || string.IsNullOrEmpty(color2)) return 999;

            try
            {
                // 新增十六进制颜色值处理
                var ParseColor = (string color) =>
                {
                    if (color.Contains(','))
                        return color.Split(',').Select(int.Parse).ToArray();

                    // 处理6位十六进制格式（如6c3434）
                    if (color.Length == 6 && IsHex(color))
                        return new[] {
                            Convert.ToInt32(color.Substring(0,2), 16),
                            Convert.ToInt32(color.Substring(2,2), 16),
                            Convert.ToInt32(color.Substring(4,2), 16)
                        };

                    throw new FormatException();
                };

                var rgb1 = ParseColor(color1);
                var rgb2 = ParseColor(color2);
                return rgb1.Zip(rgb2, (a, b) => Math.Abs(a - b)).Sum();
            }
            catch
            {
                return 999;
            }
        }

        private void DmisNull_Exit()
        {
            if (!(Db?.TimerTask?.isSubScan ?? false)) return;
            if (Dm is null || Dm.IsBind(now_Hwnd) == 0)
                DmisNull_ExitCount++;
            if (DmisNull_ExitCount >= 10)
            {
                log.Error("游戏窗口已失效，任务提前结束！");
                Ct.Cancel();
                throw new TaskCanceledException();
            }
        }

        private void Do_StartToTingYuan()
        {
            if (Db.GameSetting.IsStartYYSToCourtyard)
            {
                log.Info("开始将游戏场景操作至庭院！");
                StartToTingYuan_New();
                Db.GameSetting.IsStartYYSToCourtyard = false;
                Sleep(scan_sec * 1000);
            }
        }

        /// <summary>
        /// 图像录制
        /// </summary>
        private void ImageRecorder()
        {
            try
            {
                if (Db._recorder is null || Dm is null || Dm.IsBind(now_Hwnd) == 0) return;

                // 获取屏幕数据
                Dm.GetScreenDataBmp(0, 0, 2000, 2000, out int data, out int size);

                // 检查返回的指针和大小是否有效
                if (data == 0 || size <= 0)
                    return;

                // 创建一个字节数组来存储图像数据
                byte[] genePic2 = new byte[size];

                // 使用 Marshal.Copy 将非托管内存复制到托管数组
                IntPtr ptr = new IntPtr(data);
                Marshal.Copy(ptr, genePic2, 0, size);

                // 将图像数据添加到录制器
                Db._recorder.Add(genePic2);
            }
            catch (Exception ex)
            {
                // 处理异常，例如记录日志或显示错误消息
                log.Warn($"An error occurred: {ex.Message}");
            }
            finally
            {
                // 如果有必要，释放非托管内存
                // 例如：Dm.FreeScreenData(data);
            }
        }

        private bool IsHex(string input)
        {
            foreach (char c in input)
            {
                if (!Uri.IsHexDigit(c))
                    return false;
            }
            return true;
        }

        /// <summary>
        /// 卡屏重启游戏
        /// </summary>
        private bool RestartGame(bool isFullYuHun = false)
        {
            log.Info("检测到游戏卡屏，正在尝试重开游戏...");
            var mumu = new MuMu.MuMu();
            if (!mumu.ConnectToAdbDevice(Db.GameSetting.AdbPort)) return false;
            mumu.Stop_App("阴阳师");
            log.Info($"游戏已使用ADB端口{Db.GameSetting.AdbPort}关闭，等待5S后再重新开启游戏...");
            Sleep(5000);
            mumu.Start_App("阴阳师");
            log.Info("游戏已重启，等待3S，开始登录、关广告窗口的操作...");
            Sleep(3000);
            StartToTingYuan_New();
            if (isFullYuHun) _FullYuHun();
            OnGameRestarted?.Invoke(); // 通知任务类恢复任务
            return true;
        }

        /// <summary>
        /// 扫描卡顿 检测游戏是否出现卡屏现象
        /// </summary>
        private void Scan_Cardinality()
        {
            if (Dm is null || Dm.IsBind(now_Hwnd) == 0) return;
            Cardinality_Count++;// 每次检测间隔为 scan_sec 秒
            if (Cardinality_Count >= 10) // 改为10次检测（10秒）
            {
                Cardinality_Count = 0;
                // 增加检测区域到4个
                string[] nowColors = {
                    Dm.GetAveRGB(0, 0, 130, 30),       // 左上角
                    Dm.GetAveRGB(1149, 0, 1279, 30),   // 右上角
                    Dm.GetAveRGB(500, 500, 600, 600),  // 新增中间区域
                    Dm.GetAveRGB(0, 600, 200, 700)     // 新增左下区域
                };

                // 计算颜色差异（RGB差值小于1视为相同）
                bool isSame = nowColors.Zip(new[] { Cardinality_LastColor1, Cardinality_LastColor2, Cardinality_LastColor3, Cardinality_LastColor4 })
                    .All(pair => ColorDifference(pair.First, pair.Second) < 1);

                if (isSame)
                {
                    //log.Debug("Debug1");
                    Cardinality_ExitCount++;
                    if (Cardinality_ExitCount >= 10)
                    {
                        Cardinality_ExitCount = 0;
                        _UserNotification.CardinalityCount++;
                        if (KaPing_Do_String == "结束任务")
                        {
                            log.Error("检测到游戏可能卡屏，尝试结束任务...");
                            Ct.Cancel();
                            throw new TaskCanceledException();
                        }
                        log.Error("检测到游戏可能卡屏，尝试重启游戏...");
                        OnGameStuck?.Invoke(); // 触发事件
                        if (!RestartGame()) // 重启游戏 并通知任务类恢复任务
                        {
                            log.Error("游戏重启失败,请检查ADB端口是否正确,暂时无法恢复进程,结束本次任务！");
                            Ct.Cancel();
                            throw new TaskCanceledException();
                        }
                    }
                }
                else
                {
                    Cardinality_ExitCount = 0;
                    // 实时更新参考颜色
                    Cardinality_LastColor1 = nowColors[0];
                    Cardinality_LastColor2 = nowColors[1];
                    Cardinality_LastColor3 = nowColors[2];
                    Cardinality_LastColor4 = nowColors[3];
                }
            }
        }

        /// <summary>
        /// 扫描御魂满了
        /// </summary>
        private void Scan_FullYuHun()
        {
            if (Dm is null || Dm.IsBind(now_Hwnd) == 0) return;
            var pics_on = Mp.Filter("Sub.御魂满");
            if (pics_on.FindAll())
            {
                _UserNotification.ClearYuHunCount++;
                log.Info($"检测到御魂满了，点击确定.. 当前对应设置[结束任务（True）、清理御魂（False）]：{FullYuHun_Exit}");
                Fast.Click(596, 403, 684, 438);
                Sleep(1000);
                if (!FullYuHun_Exit)
                {
                    if (OnPushYuHunFull?.Invoke() ?? false)  // 通知主任务类御魂满了
                    {
                        Sleep(1500);
                        return; // 主任务类已处理御魂满了，退出本次检测
                    }

                    OnGameStuck?.Invoke(); // 触发事件
                    if (!RestartGame(true)) // 重启游戏 并通知任务类恢复任务
                    {
                        log.Error("游戏重启失败,无法清理御魂,请检查ADB端口是否正确,暂时无法恢复进程,结束本次任务！");
                        Ct.Cancel();
                        throw new TaskCanceledException();
                    }
                }
                else
                {
                    //结束任务
                    log.Warn("检测到您当前的御魂满了！提前结束任务！");
                    Ct.Cancel();
                    throw new TaskCanceledException();
                }
                return;
            }
        }

        /// <summary>
        /// 扫描游戏窗口大小
        /// </summary>
        private void Scan_WindowSize()
        {
            if (Dm is null || Dm.IsBind(now_Hwnd) == 0) return;
            Dm.GetClientSize(now_Hwnd, out int width, out int height);
            if (width != 1280 || height != 720)
            {
                Scan_WindowSize_Count++;
                if (Scan_WindowSize_Count >= 50)
                {
                    log.Error("游戏窗口大小异常，请检查游戏窗口是否正常！当前任务已经结束！");
                    Ct.Cancel();
                    throw new Exception("游戏窗口大小异常，请检查游戏窗口是否正常！");
                }
            }
            else Scan_WindowSize_Count = 0;
        }

        /// <summary>
        /// 扫描悬赏
        /// </summary>
        private void Scan_XuanShang()
        {
            if (Dm is null || Dm.IsBind(now_Hwnd) == 0) return;
            MemPics js = Mp.Filter("接受悬赏");
            MemPics jj = Mp.Filter("拒绝悬赏");
            bool haveXuanshang = js.FindAll() && jj.FindAll();
            if (haveXuanshang && Db.GameSetting.XuanShang)
            {
                Fast.Click("825,402,880,438");
                log.Info("接受了一份悬赏，请不要忘记完成！");
                _UserNotification.HandleRewardCount++;
            }
            else if (haveXuanshang && !Db.GameSetting.XuanShang)
            {
                Fast.Click("767,118,800,138");
                log.Info("忽略了一份悬赏！");
                _UserNotification.HandleRewardCount++;
            }

            Sleep(1000);
        }

        /// <summary>
        /// 异地登陆检测
        /// </summary>
        /// <exception cref="TaskCanceledException"></exception>
        private void Scan_YiDiDengLu()
        {
            if (Dm is null || Dm.IsBind(now_Hwnd) == 0) return;
            MemPics pics = Mp.Filter("异地登录");
            if (pics.FindAll())
            {
                log.Info($"检测到异地顶号登录了，等待8min后尝试重新登录..暂停当前的任务！");
                OnGameStuck?.Invoke(); // 触发事件
                Sleep(8 * 60 * 1000);
                if (!RestartGame()) // 重启游戏 并通知任务类恢复任务
                {
                    log.Error("游戏重启失败,无法继续登录并完成剩余任务,请检查ADB端口是否正确,暂时无法恢复进程,结束本次任务！");
                    Ct.Cancel();
                    throw new TaskCanceledException();
                }
                OnGameRestarted?.Invoke();// 通知任务类恢复任务
                Sleep(1000);
                return;
            }
        }

        /// <summary>
        /// 操作游戏点击到进入游戏按钮
        /// </summary>
        private void StartToTingYuan()
        {
            Sleep(5000);
            Point? yys_click = null;
            string ocrstr = Fast.Ocr_String(155, 407, 1123, 613, t =>
            {
                foreach (var txtblock in t)
                {
                    if (txtblock.Text.Contains("阴阳师"))
                    {
                        yys_click = new(txtblock.Center.X + 155, txtblock.Center.Y - 50 + 407);
                        break;
                    }
                }
            });
            if (ocrstr.Contains("系统应用") && ocrstr.Contains("阴阳师") && yys_click is not null)
            {
                log.Info("游戏似乎未启动，尝试点击游戏图标后继续..");
                Fast.Click(yys_click.X, yys_click.Y);
                Sleep(2000);
            }
            log.Info("双击跳过Zen视频..");
            Fast.Click(540, 300, 747, 373);
            Fast.Click(540, 300, 747, 373);
            Sleep(6000);
            log.Info("双击跳过式神视频..");
            Fast.Click(540, 300, 747, 373);
            Fast.Click(540, 300, 747, 373);
            log.Info("判断是否为进入游戏界面..");
        WaitMain:
            var res_notice = Fast.Ocr_String(59, 34, 277, 105);
            if (res_notice.Contains("公告"))
            {
                log.Info($"关闭游戏公告..");
                Fast.Click(1198, 89);
                Sleep(1000);
            }
            Sleep(3000);
            var res = Fast.Ocr_String(943, 672, 1210, 708);
            if (res.Contains("客户端"))
            {
                res = Fast.Ocr_String(494, 506, 726, 543).Replace("\r", null);
                log.Info($"当前默认的服务器为：{res}(非准确)，点击进入游戏..");
                Fast.Click(566, 582, 697, 609);
                Sleep(1000);
                ClickToGameed();
                return;
            }
            Sleep(3000);
            log.Info("没有等到游戏进入界面，尝试再次判断..");
            Fast.Click(540, 300, 747, 373);
            goto WaitMain;
        }

        /// <summary>
        /// 将游戏场景操作至庭院
        /// </summary>
        private void StartToTingYuan_New()
        {
            bool isJoinGame = false;
            int count = 0;
            while (!Scene.NowIsScene("庭院"))
            {
                #region 打开阴阳师app

                Point? yys_click = null;
                string ocrstr = Fast.Ocr_String(155, 407, 1123, 613, t =>
                {
                    foreach (var txtblock in t)
                    {
                        if (txtblock.Text.Contains("阴阳师"))
                        {
                            yys_click = new(txtblock.Center.X + 155, txtblock.Center.Y - 50 + 407);
                            break;
                        }
                    }
                });
                if (ocrstr.Contains("系统应用") && ocrstr.Contains("阴阳师") && yys_click is not null)
                {
                    log.Info("游戏似乎未启动，尝试点击游戏图标后继续..");
                    Fast.Click(yys_click.X, yys_click.Y);
                    Sleep(2000);
                }

                #endregion 打开阴阳师app

                #region 关闭公告窗口

                var res_notice = Fast.Ocr_String(59, 34, 277, 105);
                if (res_notice.Contains("公告"))
                {
                    log.Info($"关闭游戏公告..");
                    Fast.Click(1198, 89);
                    Sleep(1000);
                }

                #endregion 关闭公告窗口

                #region 进入游戏方式1

                var res = Fast.Ocr_String(943, 672, 1210, 708);
                if (res.Contains("客户端"))
                {
                    res = Fast.Ocr_String(494, 506, 726, 543).Replace("\r", null);
                    log.Info($"当前默认的服务器为：{res}(非准确)，点击进入游戏..");
                    Fast.Click(566, 582, 697, 609);
                    Sleep(1000);
                    log.Info($"请确保您的卷轴皮肤为默认皮肤！..");
                    isJoinGame = true;
                }

                #endregion 进入游戏方式1

                #region 进入游戏方式2

                var res2 = Fast.Ocr_String(449, 555, 830, 635);
                if (res2.Contains("进入游戏"))
                {
                    res2 = Fast.Ocr_String(494, 506, 726, 543).Replace("\r", null);
                    log.Info($"当前默认的服务器为：{res2}(非准确)，点击进入游戏..");
                    Fast.Click(566, 582, 697, 609);
                    Sleep(1000);
                    log.Info($"请确保您的卷轴皮肤为默认皮肤！..");
                    isJoinGame = true;
                }

                #endregion 进入游戏方式2

                if (isJoinGame)
                {
                    if (new Pixel(750, 476, "f4b25d-202020", 0.95).Find(Dm))
                    {
                        log.Info("检测到提示弹窗，点击取消...");
                        Fast.Click(524, 475);
                        Sleep(1000);
                    }

                    var X_pics = Mp.Filter("Sub.叉");
                    int x, y;
                    Sleep(1000);
                    while (X_pics.FindAll(out x, out y))
                    {
                        log.Info("关闭牛皮癣广告弹窗..");
                        Fast.Click(x, y);
                        Sleep(3000);
                    }

                    if (Scene.NowIsScene("庭院"))
                    {
                        log.Info_Green("重启恢复游戏场景完成，当前界面为庭院，为您继续完成任务！");
                        return;
                    }
                }
                Sleep(3000);

                if (isJoinGame)
                {
                    // 已进入游戏，但可能卡住，每10次循环点击一次
                    if (++count > 10)
                    {
                        log.Info("可能卡住，尝试点击恢复...");
                        Fast.Click(857, 570, 1077, 632);
                        count = 0;
                    }
                }
                else
                {
                    // 如果未进入游戏，直接点击
                    Fast.Click(857, 570, 1077, 632);
                }
            }
        }

        private struct UserNotificationMessage_Struct
        {
            public int CardinalityCount;
            public int ClearYuHunCount;

            // 卡屏次数
            public int DoTimerTaskCount;

            // 清理御魂次数
            public int HandleRewardCount;

            public string Message; // 消息内容
                                   // 处理悬赏次数
                                   // 执行定时任务
        }
    }
}