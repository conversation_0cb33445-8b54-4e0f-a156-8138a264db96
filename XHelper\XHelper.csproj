﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<Platforms>x86</Platforms>
	</PropertyGroup>
	<PropertyGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Debug|x86&apos;">
		<Optimize>False</Optimize>
		<DebugType>full</DebugType>
		<IsAotCompatible>True</IsAotCompatible>
	</PropertyGroup>
	<PropertyGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Release|x86&apos;">
		<DebugType>full</DebugType>
		<IsAotCompatible>True</IsAotCompatible>
	</PropertyGroup>
	<PropertyGroup>
		<RuntimeIdentifier>win-x86</RuntimeIdentifier>
		<PublishTrimmed>true</PublishTrimmed>
		<PublishSingleFile>true</PublishSingleFile>
	</PropertyGroup>
	<PropertyGroup>
		<ReactorLocation>&quot;&quot;</ReactorLocation>
		<ReactorProject>&quot;&quot;</ReactorProject>
	</PropertyGroup>
	<ItemGroup>
		<None Remove="NLog.Config"/>
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Include="NLog.Config">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</EmbeddedResource>
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Emgu.CV.Bitmap" Version="4.10.0.5680"/>
		<PackageReference Include="Lazy.Captcha.Core" Version="2.1.0"/>
		<PackageReference Include="Microsoft.ML.OnnxRuntime" Version="1.21.0"/>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3"/>
		<PackageReference Include="NLog" Version="5.4.0"/>
		<PackageReference Include="OpenCvSharp4" Version="4.10.0.20241108"/>
		<PackageReference Include="OpenCvSharp4.runtime.win" Version="4.10.0.20241108"/>
		<PackageReference Include="System.Management" Version="9.0.2"/>
		<PackageReference Include="System.Net.WebSockets.Client" Version="4.3.2"/>
		<PackageReference Include="System.Private.Uri" Version="4.3.2"/>
		<PackageReference Include="Tesseract" Version="5.2.0"/>
		<PackageReference Include="YoloSharp" Version="6.0.2"/>
	</ItemGroup>
	<ItemGroup>
		<Reference Include="clipper_library">
			<HintPath>..\lib\clipper_library.dll</HintPath>
		</Reference>
		<Reference Include="OcrLiteLib">
			<HintPath>..\lib\OcrLiteLib.dll</HintPath>
		</Reference>
	</ItemGroup>
	<ItemGroup>
		<None Update="onnxruntime.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="runtimes\cls.onnx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="runtimes\det.onnx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="runtimes\keys.txt">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="runtimes\models\Bg.onnx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="runtimes\models\Cs.onnx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="runtimes\models\Ts.onnx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="runtimes\num.traineddata">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="runtimes\PaddleOCR_cpp.exe">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="runtimes\rec.onnx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="x86\concrt140.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="x86\cvextern.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>
	<ItemGroup>
		<None Remove="C:\Users\<USER>\.nuget\packages\tesseract\5.2.0\build\\..\x86\tesseract50.dll"/>
	</ItemGroup>
	<ItemGroup>
		<None Remove="C:\Users\<USER>\.nuget\packages\tesseract\5.2.0\build\\..\x86\tesseract50.dll"/>
	</ItemGroup>
	<ItemGroup>
		<None Update="C:\Users\<USER>\.nuget\packages\tesseract\5.2.0\build\\..\x86\leptonica-1.82.0.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="x86\msvcp140.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="x86\msvcp140_1.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="x86\msvcp140_2.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="x86\msvcp140_codecvt_ids.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="x86\opencv_videoio_ffmpeg440.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>
	<ItemGroup>
		<None Update="C:\Users\<USER>\.nuget\packages\tesseract\5.2.0\build\\..\x86\tesseract50.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="x86\vcruntime140.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>
</Project>