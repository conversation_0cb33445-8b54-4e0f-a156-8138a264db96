﻿using DanDing1.ViewModels.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using Wpf.Ui.Controls;
using XHelper;

namespace DanDing1.Views.Pages
{
    public class PageBaseEvent
    {
        public PageBaseEvent(GameViewBaseModel viewModel)
        {
            ViewModel = viewModel;
        }

        public GameViewBaseModel ViewModel { get; }

        public void XShang_Status_Checked(object sender, RoutedEventArgs e)
        {
            ToggleSwitch ts = sender as ToggleSwitch ?? throw new Exception();
            XConfig.SaveValueToFile(ViewModel.GameName, "XShang", ts?.IsChecked ?? false);
        }

        public void GameScene_ComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 获取 ComboBox 控件
            ComboBox? comboBox = sender as ComboBox;

            // 获取选中项
            string? selectedItem = comboBox?.SelectedItem as string;

            // 根据选中的内容执行操作
            if (selectedItem != "" && selectedItem != null && ViewModel.GameName != null)
                XConfig.SaveValueToFile(ViewModel.GameName, "TYscene", selectedItem);
        }
    }
}