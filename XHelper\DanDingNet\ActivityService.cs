using System.Net.Http.Headers;
using System.Text.Json;
using XHelper.Models;

namespace XHelper.DanDingNet
{
    public class ActivityService : BaseService
    {
        public ActivityService(HttpClient client, string macCode, string host, string version)
            : base(client, macCode, host, version)
        {
        }

        public async Task<Response_ActivityData?> GetActivityAsync(int activityId)
        {
            if (CheckObjisNull(_client)) return null;

            var message = GetBaseRequest(HttpMethod.Get, _host + DDApi.Api["获取活动"] + $"?id={activityId}");
            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<Response_ActivityData>(body, _jsonOptions);
        }

        public async Task<ResponseBaseData?> DoActivityAsync(int activityId, int userId)
        {
            if (CheckObjisNull(_client)) return null;

            var data = new
            {
                activity_id = activityId,
                user_id = userId,
                user_machine_code = _macCode
            };

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["执行活动"]);
            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<ResponseBaseData>(body, _jsonOptions);
        }

        public async Task<Response_UseKami?> UseKamiAsync(string username, string kami)
        {
            if (CheckObjisNull(_client)) return null;

            var data = new
            {
                username,
                kami
            };

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["卡密充值"]);
            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<Response_UseKami>(body, _jsonOptions);
        }

        public async Task<Response_FreeData?> RegisterFreeUserAsync()
        {
            if (CheckObjisNull(_client)) return null;

            var data = new { computer_Code = _macCode };
            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["试用"]);
            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();

            if (response.Headers.TryGetValues("Set-Cookie", out var cookies))
            {
                XNet.SaveCookiesToFile(cookies, ".\\runtimes\\cookies.data");
                foreach (var cookie in cookies)
                {
                    _client.DefaultRequestHeaders.Add("Cookie", cookie);
                    if (cookie.Contains("X-token"))
                        _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", cookie.Split("Bearer ")[1]);
                }
            }

            return JsonSerializer.Deserialize<Response_FreeData>(body, _jsonOptions);
        }

        public async Task<bool> CheckFreeUserAsync()
        {
            if (CheckObjisNull(_client)) return false;

            var data = new { computer_Code = _macCode };
            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["检查试用"]);
            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            ResponseBaseData? obj = JsonSerializer.Deserialize<ResponseBaseData>(body, _jsonOptions);
            return obj?.IsSuccess ?? false;
        }
    }
}