﻿/*
 * 文件名: SuperMultiGamesWindowViewModel.Events.cs
 * 职责描述: 处理所有事件和命令
 * 该文件实现了UI交互事件的处理逻辑，包括按钮点击、选择变更等用户操作的响应方法
 */

using DanDing1.Models.Super;
using DanDing1.ViewModels.Pages;
using DanDing1.ViewModels.Windows.SuperMultiGamesWindowViewModels;
using ScriptEngine.MuMu;
using XHelper;
using System.Threading;
using DanDing1.Helpers;
using System.Threading.Tasks;
using DanDing1.Views.Windows;
using Microsoft.Extensions.DependencyInjection;
using Wpf.Ui.Controls;
using System.Windows;
using System.IO;

namespace DanDing1.ViewModels.Windows
{
    public partial class SuperMultiGamesWindowViewModel : ObservableObject
    {
        /// <summary>
        /// 自动添加所有模拟器实例到游戏列表
        /// </summary>
        /// <remarks>
        /// 关联UI：添加全部模拟器按钮
        /// 功能：获取所有MuMu模拟器实例，添加到游戏列表中，不管模拟器是否正在运行
        /// 获取模拟器的句柄并初始化游戏数据模型，确保不重复添加已存在的模拟器
        /// </remarks>
        [RelayCommand]
        internal async void AddAllSimulators()
        {
            AddLog("开始自动添加所有模拟器...");

            // 获取MuMu模拟器路径
            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (string.IsNullOrEmpty(path))
            {
                AddLog("MuMu模拟器路径未设置，无法添加模拟器");
                Utils.ShowMessage("添加失败", "MuMu模拟器路径未设置，请先配置模拟器");
                return;
            }

            // 初始化MuMu实例
            MuMu mumu = new();
            if (!mumu.Init(path))
            {
                AddLog("MuMu模拟器初始化失败，无法添加模拟器");
                Utils.ShowMessage("添加失败", "MuMu模拟器初始化失败，请检查路径设置");
                return;
            }

            // 获取最新的模拟器实例信息
            var mumuInstances = await mumu._GetInstancesAsync();
            if (mumuInstances == null || mumuInstances.Instances.Count == 0)
            {
                AddLog("未找到任何MuMu模拟器实例，请确保模拟器已启动");
                Utils.ShowMessage("添加失败", "未找到任何MuMu模拟器实例，请确保模拟器已启动");
                return;
            }

            AddLog($"发现 {mumuInstances.Instances.Count} 个模拟器实例");

            int addedCount = 0;
            int skippedCount = 0;
            List<string> addedNames = new List<string>();

            // 遍历所有模拟器实例
            foreach (var instance in mumuInstances.Instances)
            {
                string instanceName = instance.Value.Name;
                string instanceIndex = instance.Key;

                // 获取模拟器的窗口句柄
                MumuUtils.IsSimulatorRunning(
                    instance.Value,
                    out int mainWndHandle,
                    out int renderWndHandle);

                // 检查是否已经添加了该模拟器（确保唯一性）
                bool alreadyExists = SuperMultiGame_DataModelCollection.Any(g =>
                    (!string.IsNullOrEmpty(g.MumuRealName) && g.MumuRealName == instanceName) ||
                    (g.MumuInstance != null && !string.IsNullOrEmpty(g.MumuInstance.Index) && g.MumuInstance.Index == instanceIndex));

                if (alreadyExists)
                {
                    AddLog($"模拟器 {instanceName} (索引: {instanceIndex}) 已经存在，跳过添加");
                    skippedCount++;
                    continue;
                }

                try
                {
                    // 生成新游戏ID
                    int newId = SuperMultiGame_DataModelCollection.Count > 0 ?
                                SuperMultiGame_DataModelCollection.Max(g => g.GameId) + 1 : 1;

                    // 创建新游戏模型
                    var newGame = new SuperMultiGame_DataModel
                    {
                        GameId = newId,
                        GameName = instanceName, // 直接使用模拟器名称作为游戏名称
                        MumuHandle = mainWndHandle, // 使用MainWnd作为MumuHandle
                        GameHandle = renderWndHandle, // 使用RenderWnd作为GameHandle
                        Identity = Team_Identity_Unit.队员,
                        CurrentTask = "未运行",
                        RunningDuration = TimeSpan.Zero,
                        RunningStatus = "未运行",
                        SimulatorType = "MuMu模拟器",
                        MumuInstance = instance.Value, // 保存MuMuInstance实例
                        MumuRealName = instanceName, // 保存模拟器真实名称
                        TaskConfiguration = new AddTaskPropertyViewModel() // 初始化时创建任务配置
                    };

                    // 添加到游戏列表
                    SuperMultiGame_DataModelCollection.Add(newGame);

                    // 添加游戏日志
                    GameLogs.Add(new GameLogItem(newGame.GameName, newGame.GameId));

                    // 为新添加的游戏分配脚本ID
                    GetScriptId(newGame.GameId);

                    // 尝试加载该游戏的任务配置
                    LoadTaskConfiguration(newGame);

                    // 确保游戏有默认配置
                    EnsureGameConfigExists(newGame);

                    AddLog($"成功添加模拟器：{instanceName}，ID：{newId}，主窗口句柄：{mainWndHandle}，渲染窗口句柄：{renderWndHandle}");
                    addedCount++;
                    addedNames.Add(instanceName);
                }
                catch (Exception ex)
                {
                    AddLog($"添加模拟器 {instanceName} 失败: {ex.Message}");
                }
            }

            // 选中最后一个添加的游戏
            if (addedCount > 0 && SuperMultiGame_DataModelCollection.Count > 0)
            {
                SelectedGameModel = SuperMultiGame_DataModelCollection.Last();

                // 自动切换到新游戏的日志选项卡
                if (GameLogs.Count > 0)
                {
                    SelectedLogTabIndex = GameLogs.Count;
                }
            }

            // 显示结果消息
            string resultMessage;
            if (addedCount > 0)
            {
                resultMessage = $"成功添加 {addedCount} 个模拟器: {string.Join(", ", addedNames)}";
                if (skippedCount > 0)
                {
                    resultMessage += $"\n跳过 {skippedCount} 个已存在的模拟器";
                }
            }
            else if (skippedCount > 0)
            {
                resultMessage = $"所有模拟器 ({skippedCount} 个) 都已经在列表中，没有添加新模拟器";
            }
            else
            {
                resultMessage = "没有添加任何模拟器";
            }

            AddLog(resultMessage);
            Utils.ShowMessage("添加结果", resultMessage);
        }

        /// <summary>
        /// 打开模拟器选择器，添加用户选择的模拟器实例到游戏列表
        /// </summary>
        /// <remarks>
        /// 关联UI：添加模拟器按钮
        /// 功能：弹出模拟器选择窗口，让用户选择要添加的MuMu模拟器实例
        /// 将用户选择的模拟器添加到游戏列表，并获取相关句柄
        /// </remarks>
        [RelayCommand]
        internal async Task AddGame()
        {
            AddLog("正在打开模拟器选择器...");
            await TemporarilyUnsetTopMost(() =>
            {
                // 创建并显示MuMuConfigsWindow窗口（使用选择器模式）
                var configWindow = new Views.Windows.MuMuConfigsWindow(true);
                configWindow.ShowDialog(); // 使用ShowDialog确保用户关闭窗口后才继续

                // 检查用户是否选择了模拟器
                if (!configWindow.HasSelectedSimulator || configWindow.SelectedSimulator == null)
                {
                    AddLog("用户取消了添加游戏");
                    return Task.CompletedTask;
                }

                var selectedSimulator = configWindow.SelectedSimulator;

                // 检查是否已经添加了该模拟器（确保唯一性）
                if (SuperMultiGame_DataModelCollection.Any(g =>
                    (!string.IsNullOrEmpty(g.MumuRealName) && g.MumuRealName == selectedSimulator.Name) ||
                    (g.MumuInstance != null && !string.IsNullOrEmpty(g.MumuInstance.Index) && g.MumuInstance.Index == selectedSimulator.Index.ToString())))
                {
                    AddLog($"模拟器 {selectedSimulator.Name} (索引: {selectedSimulator.Index}) 已经存在，不能重复添加");
                    Utils.ShowMessage("添加失败", $"模拟器 {selectedSimulator.Name} (索引: {selectedSimulator.Index}) 已经存在，不能重复添加");
                    return Task.CompletedTask;
                }

                // 添加新游戏
                AddLog($"正在添加新游戏，使用模拟器 {selectedSimulator.Name} (索引: {selectedSimulator.Index})");

                int newId = SuperMultiGame_DataModelCollection.Count > 0 ?
                            SuperMultiGame_DataModelCollection.Max(g => g.GameId) + 1 : 1;

                // 获取MuMu实例信息
                ScriptEngine.MuMu.MuMu mumu = new();
                var path = XHelper.XConfig.LoadValueFromFile<string>("MuMuPath");
                if (!string.IsNullOrEmpty(path))
                {
                    mumu.Init(path);
                }

                var mumuInstances = mumu._GetInstances();
                if (mumuInstances == null || !mumuInstances.Instances.ContainsKey(selectedSimulator.Index.ToString()))
                {
                    AddLog($"获取模拟器实例信息失败，无法添加游戏");
                    Utils.ShowMessage("添加失败", "获取模拟器实例信息失败，请确保MuMu模拟器已启动");

                    return Task.CompletedTask;
                }

                var mumuInstance = mumuInstances.Instances[selectedSimulator.Index.ToString()];

                // 使用MumuUtils解析模拟器句柄
                MumuUtils.IsSimulatorRunning(mumuInstance, out int mainWndHandle, out int renderWndHandle);

                var newGame = new SuperMultiGame_DataModel
                {
                    GameId = newId,
                    GameName = selectedSimulator.Name, // 直接使用模拟器名称作为游戏名称
                    MumuHandle = mainWndHandle, // 使用MainWnd作为MumuHandle
                    GameHandle = renderWndHandle, // 使用RenderWnd作为GameHandle
                    Identity = Team_Identity_Unit.队员,
                    CurrentTask = "未运行",
                    RunningDuration = TimeSpan.Zero,
                    RunningStatus = "未运行",
                    SimulatorType = "MuMu模拟器",
                    MumuInstance = mumuInstance, // 保存MuMuInstance实例
                    MumuRealName = mumuInstance.Name // 保存模拟器真实名称
                };

                SuperMultiGame_DataModelCollection.Add(newGame);
                SelectedGameModel = newGame;

                // 添加游戏日志
                GameLogs.Add(new GameLogItem(newGame.GameName, newGame.GameId));

                // 为新添加的游戏分配脚本ID
                GetScriptId(newGame.GameId);

                // 尝试加载该游戏的任务配置
                LoadTaskConfiguration(newGame);

                // 确保游戏有默认配置
                EnsureGameConfigExists(newGame);

                // 自动切换到新游戏的日志选项卡
                SelectedLogTabIndex = GameLogs.Count;

                AddLog($"成功添加新游戏：{newGame.GameName}，ID：{newGame.GameId}，使用模拟器：{selectedSimulator.Name}，主窗口句柄：{mainWndHandle}，渲染窗口句柄：{renderWndHandle}");
                return Task.CompletedTask;
            });
        }

        /// <summary>
        /// 仅添加真正运行中（具有有效窗口句柄）的模拟器实例到游戏列表
        /// </summary>
        /// <remarks>
        /// 关联UI：添加运行模拟器按钮
        /// 功能：获取当前真正运行的MuMu模拟器实例（具有有效窗口句柄），添加到游戏列表中
        /// 与AddAllSimulators不同，此方法会额外检查模拟器窗口句柄是否有效
        /// </remarks>
        [RelayCommand]
        internal async void AddOnlyRunningSimulators()
        {
            AddLog("开始添加正在运行的模拟器...");

            // 获取MuMu模拟器路径
            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (string.IsNullOrEmpty(path))
            {
                AddLog("MuMu模拟器路径未设置，无法添加模拟器");
                Utils.ShowMessage("添加失败", "MuMu模拟器路径未设置，请先配置模拟器");
                return;
            }

            // 初始化MuMu实例
            MuMu mumu = new();
            if (!mumu.Init(path))
            {
                AddLog("MuMu模拟器初始化失败，无法添加模拟器");
                Utils.ShowMessage("添加失败", "MuMu模拟器初始化失败，请检查路径设置");
                return;
            }

            // 获取最新的模拟器实例信息
            var mumuInstances = await mumu._GetInstancesAsync();
            if (mumuInstances == null || mumuInstances.Instances.Count == 0)
            {
                AddLog("未找到任何MuMu模拟器实例，请确保模拟器已启动");
                Utils.ShowMessage("添加失败", "未找到任何MuMu模拟器实例，请确保模拟器已启动");
                return;
            }

            AddLog($"发现 {mumuInstances.Instances.Count} 个模拟器实例");

            int runningInstancesCount = 0;
            int addedCount = 0;
            int skippedCount = 0;
            List<string> addedNames = new List<string>();

            // 遍历所有模拟器实例，只处理真正运行中的模拟器
            foreach (var instance in mumuInstances.Instances)
            {
                string instanceName = instance.Value.Name;
                string instanceIndex = instance.Key;

                // 使用MumuUtils检查模拟器是否运行中
                bool isRunning = MumuUtils.IsSimulatorRunning(
                    instance.Value,
                    out int mainWndHandle,
                    out int renderWndHandle);

                // 如果模拟器没有真正运行，跳过该实例
                if (!isRunning)
                {
                    AddLog($"模拟器 {instanceName} (索引: {instanceIndex}) 未真正运行，跳过添加");
                    continue;
                }

                runningInstancesCount++;

                // 检查是否已经添加了该模拟器（确保唯一性）
                bool alreadyExists = SuperMultiGame_DataModelCollection.Any(g =>
                    (!string.IsNullOrEmpty(g.MumuRealName) && g.MumuRealName == instanceName) ||
                    (g.MumuInstance != null && !string.IsNullOrEmpty(g.MumuInstance.Index) && g.MumuInstance.Index == instanceIndex));

                if (alreadyExists)
                {
                    AddLog($"模拟器 {instanceName} (索引: {instanceIndex}) 已经存在，跳过添加");
                    skippedCount++;
                    continue;
                }

                try
                {
                    // 生成新游戏ID
                    int newId = SuperMultiGame_DataModelCollection.Count > 0 ?
                                SuperMultiGame_DataModelCollection.Max(g => g.GameId) + 1 : 1;

                    // 创建新游戏模型
                    var newGame = new SuperMultiGame_DataModel
                    {
                        GameId = newId,
                        GameName = instanceName, // 直接使用模拟器名称作为游戏名称
                        MumuHandle = mainWndHandle, // 使用MainWnd作为MumuHandle
                        GameHandle = renderWndHandle, // 使用RenderWnd作为GameHandle
                        Identity = Team_Identity_Unit.队员,
                        CurrentTask = "未运行",
                        RunningDuration = TimeSpan.Zero,
                        RunningStatus = "未运行",
                        SimulatorType = "MuMu模拟器",
                        MumuInstance = instance.Value, // 保存MuMuInstance实例
                        MumuRealName = instanceName, // 保存模拟器真实名称
                        TaskConfiguration = new AddTaskPropertyViewModel() // 初始化时创建任务配置
                    };

                    // 添加到游戏列表
                    SuperMultiGame_DataModelCollection.Add(newGame);

                    // 添加游戏日志
                    GameLogs.Add(new GameLogItem(newGame.GameName, newGame.GameId));

                    // 为新添加的游戏分配脚本ID
                    GetScriptId(newGame.GameId);

                    // 尝试加载该游戏的任务配置
                    LoadTaskConfiguration(newGame);

                    // 确保游戏有默认配置
                    EnsureGameConfigExists(newGame);

                    AddLog($"成功添加运行中的模拟器：{instanceName}，ID：{newId}，主窗口句柄：{mainWndHandle}，渲染窗口句柄：{renderWndHandle}");
                    addedCount++;
                    addedNames.Add(instanceName);
                }
                catch (Exception ex)
                {
                    AddLog($"添加模拟器 {instanceName} 失败: {ex.Message}");
                }
            }

            // 选中最后一个添加的游戏
            if (addedCount > 0 && SuperMultiGame_DataModelCollection.Count > 0)
            {
                SelectedGameModel = SuperMultiGame_DataModelCollection.Last();

                // 自动切换到新游戏的日志选项卡
                if (GameLogs.Count > 0)
                {
                    SelectedLogTabIndex = GameLogs.Count;
                }
            }

            // 显示结果消息
            string resultMessage;
            if (runningInstancesCount == 0)
            {
                resultMessage = "未发现正在运行的模拟器";
            }
            else if (addedCount > 0)
            {
                resultMessage = $"发现 {runningInstancesCount} 个运行中的模拟器，成功添加 {addedCount} 个: {string.Join(", ", addedNames)}";
                if (skippedCount > 0)
                {
                    resultMessage += $"\n跳过 {skippedCount} 个已存在的模拟器";
                }
            }
            else if (skippedCount > 0)
            {
                resultMessage = $"所有运行中的模拟器 ({skippedCount} 个) 都已经在列表中，没有添加新模拟器";
            }
            else
            {
                resultMessage = "没有添加任何模拟器";
            }

            AddLog(resultMessage);
            Utils.ShowMessage("添加结果", resultMessage);
        }

        /// <summary>
        /// 配置选中的模拟器实例，设置为标准分辨率、DPI和渲染模式
        /// </summary>
        /// <param name="game">要配置的游戏对象</param>
        /// <remarks>
        /// 关联UI：配置模拟器按钮（游戏列表右键菜单或工具栏）
        /// 功能：将选中的模拟器配置为1280x720分辨率、240DPI和DirectX渲染模式
        /// </remarks>
        [RelayCommand]
        internal async void ConfigureSelectedSimulator(SuperMultiGame_DataModel game)
        {
            if (game == null)
            {
                AddLog("未选择游戏，无法配置模拟器");
                return;
            }

            if (game.MumuInstance == null || string.IsNullOrEmpty(game.MumuInstance.Index))
            {
                AddLog($"游戏 {game.GameName} 的模拟器实例信息不完整，无法进行配置");
                return;
            }

            // 解析模拟器索引
            if (!int.TryParse(game.MumuInstance.Index, out int index))
            {
                AddLog($"游戏 {game.GameName} 的模拟器索引无效: {game.MumuInstance.Index}");
                return;
            }

            AddLog($"开始配置模拟器 {game.MumuRealName}（索引: {index}）...");

            // 创建MuMu实例
            ScriptEngine.MuMu.MuMu mumu = new();
            string path = XHelper.XConfig.LoadValueFromFile<string>("MuMuPath");
            if (string.IsNullOrEmpty(path))
            {
                AddLog("MuMu路径未设置，无法配置模拟器");
                return;
            }

            if (!mumu.Init(path))
            {
                AddLog("MuMu实例初始化失败，无法配置模拟器");
                return;
            }

            // 执行配置
            bool result = mumu.SetEmulatorConfig(index);
            if (result)
            {
                AddLog($"模拟器 {game.MumuRealName}（索引: {index}）配置成功！已设置为1280x720分辨率，DPI为240");
                AddGameLog(game.GameId, $"模拟器配置成功！已设置为1280x720分辨率，DPI为240");
                Utils.ShowMessage("配置成功", $"模拟器 {game.MumuRealName}（索引: {index}）配置成功！\n\n已设置为：\n- 分辨率：1280x720\n- DPI：240\n- 渲染模式：DirectX\n\n 请注意，配置后可能需要重启模拟器才能生效！");
            }
            else
            {
                AddLog($"模拟器 {game.MumuRealName}（索引: {index}）配置失败");
                AddGameLog(game.GameId, "模拟器配置失败");
                Utils.ShowMessage("配置失败", $"模拟器 {game.MumuRealName}（索引: {index}）配置失败，请检查模拟器状态或手动配置");
            }
        }

        /// <summary>
        /// 打开模拟器配置窗口，用于管理和配置所有模拟器
        /// </summary>
        /// <remarks>
        /// 关联UI：模拟器设置按钮
        /// 功能：弹出MuMuConfigsWindow窗口，允许用户查看和配置所有模拟器实例
        /// </remarks>
        [RelayCommand]
        internal async Task ConfigureSimulator()
        {
            AddLog("正在打开模拟器配置...");
            await TemporarilyUnsetTopMost(() =>
            {
                // 创建并显示MuMuConfigsWindow窗口（配置模式）
                var configWindow = new Views.Windows.MuMuConfigsWindow(false);
                configWindow.ShowDialog(); // 使用ShowDialog确保用户关闭窗口后才继续
                return Task.CompletedTask;
            });
        }

        /// <summary>
        /// 从游戏列表中删除选中的游戏
        /// </summary>
        /// <param name="game">要删除的游戏对象</param>
        /// <remarks>
        /// 关联UI：删除游戏按钮（游戏列表右键菜单或工具栏）
        /// 功能：从SuperMultiGame_DataModelCollection中移除选中的游戏，并删除相关日志
        /// </remarks>
        [RelayCommand]
        internal void DeleteSelectedGame(SuperMultiGame_DataModel game)
        {
            if (game == null) return;

            if (game.RunningStatus == "运行中")
            {
                Utils.ShowMessage("警告", "不能删除正在运行的游戏，请先停止该游戏任务！");
                return;
            }

            // 确认是否要删除
            var result = System.Windows.MessageBox.Show(
                $"确定要删除游戏 {game.GameName} 吗？",
                "确认删除",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Question);

            if (result == System.Windows.MessageBoxResult.No)
                return;

            // 删除游戏日志
            var gameLog = GameLogs.FirstOrDefault(g => g.GameId == game.GameId);
            if (gameLog != null)
            {
                GameLogs.Remove(gameLog);

                // 触发游戏日志更新事件，确保UI更新
                OnGameLogUpdated?.Invoke();

                // 如果当前选中的是被删除的游戏日志标签页，则切换到主日志标签页
                if (SelectedLogTabIndex > 0 && SelectedLogTabIndex >= GameLogs.Count)
                {
                    SelectedLogTabIndex = 0; // 主日志标签页索引为0
                }
            }

            // 获取保存键值（与SaveConfiguration和AutoSaveTaskConfigs中的逻辑保持一致）
            string saveKey = !string.IsNullOrEmpty(game.MumuRealName)
                ? game.MumuRealName
                : $"MumuId_{game.GameId}";

            // 删除相关配置文件
            try
            {
                // 基础路径
                string basePath = ".\\runtimes\\AppConfig\\";
                // 需要删除的文件路径列表
                string[] filePaths =
                {
                    $"{basePath}SuperMultiGame\\Config\\{saveKey}",
                    $"{basePath}SuperMultiGame\\TaskList\\{saveKey}",
                    $"{basePath}SuperMultiGame\\ExtraConfig\\{saveKey}"
                };

                // 尝试删除每个文件
                foreach (var filePath in filePaths)
                {
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                        AddLog($"已删除配置文件：{filePath}");
                    }
                }
            }
            catch (Exception ex)
            {
                AddLog($"删除配置文件时出错：{ex.Message}");
            }

            // 删除游戏
            SuperMultiGame_DataModelCollection.Remove(game);

            // 如果删除的是当前选中的游戏，清空选中
            if (SelectedGameModel == game)
            {
                SelectedGameModel = null;
            }

            AddLog($"已删除游戏：{game.GameName}，ID：{game.GameId}");
        }

        /// <summary>
        /// 启动选中游戏的模拟器实例
        /// </summary>
        /// <param name="game">要启动模拟器的游戏对象</param>
        /// <remarks>
        /// 关联UI：启动模拟器按钮（游戏列表右键菜单或工具栏）
        /// 功能：根据游戏对象中的模拟器信息，启动对应的MuMu模拟器实例
        /// 启动后自动刷新模拟器状态和句柄信息
        /// </remarks>
        [RelayCommand]
        internal async void LaunchSimulator(SuperMultiGame_DataModel game)
        {
            if (game == null) return;

            AddLog($"正在启动模拟器 {game.GameName}...");

            // 获取MuMu模拟器路径
            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (string.IsNullOrEmpty(path))
            {
                AddLog("MuMu模拟器路径未设置，无法启动模拟器");
                Utils.ShowMessage("启动失败", $"MuMu模拟器路径未设置，请先配置模拟器");
                return;
            }

            try
            {
                // 初始化MuMu实例
                var mumu = new ScriptEngine.MuMu.MuMu();
                if (!mumu.Init(path))
                {
                    AddLog("MuMu模拟器初始化失败，无法启动模拟器");
                    AddGameLog(game.GameId, "MuMu模拟器初始化失败");
                    Utils.ShowMessage("启动失败", $"MuMu模拟器初始化失败，请检查路径设置");
                    return;
                }

                // 获取模拟器实例信息
                var mumuInstances = await mumu._GetInstancesAsync();
                if (mumuInstances == null || mumuInstances.Instances.Count == 0)
                {
                    AddLog("未找到任何MuMu模拟器实例");
                    AddGameLog(game.GameId, "未找到任何MuMu模拟器实例");
                    Utils.ShowMessage("启动失败", $"未找到任何MuMu模拟器实例");
                    return;
                }

                // 获取模拟器实例索引
                if (game.MumuInstance == null || string.IsNullOrEmpty(game.MumuInstance.Index))
                {
                    AddLog($"游戏 {game.GameName} 没有有效的模拟器索引");
                    AddGameLog(game.GameId, "没有有效的模拟器索引");
                    Utils.ShowMessage("启动失败", $"游戏 {game.GameName} 没有有效的模拟器索引");
                    return;
                }

                if (!int.TryParse(game.MumuInstance.Index, out int index))
                {
                    AddLog($"无法解析模拟器索引: {game.MumuInstance.Index}");
                    AddGameLog(game.GameId, $"无法解析模拟器索引: {game.MumuInstance.Index}");
                    Utils.ShowMessage("启动失败", $"无法解析模拟器索引: {game.MumuInstance.Index}");
                    return;
                }

                // 使用OpenByIndex方法启动模拟器
                mumu.OpenByIndex(index);

                AddLog($"已发送启动模拟器 {game.GameName} 的命令，索引: {index}");
                AddGameLog(game.GameId, $"已发送启动模拟器命令，索引: {index}");

                // 等待7秒后开始自动刷新模拟器状态
                AddLog($"等待7秒后将开始自动刷新模拟器状态...");

                // 调用最简单的非嵌套异步方法
                _ = RefreshWithDelaySimple(game);
            }
            catch (Exception ex)
            {
                AddLog($"启动模拟器失败：{ex.Message}");
                AddGameLog(game.GameId, $"启动模拟器失败：{ex.Message}");
                Utils.ShowMessage("启动失败", $"启动模拟器失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 简单的异步延迟刷新方法 - 使用最基本的async/await模式
        /// </summary>
        private async Task RefreshWithDelaySimple(SuperMultiGame_DataModel game)
        {
            try
            {
                // 设置自动刷新标志为true
                IsAutoRefreshing = true;

                // 首先等待7秒
                await Task.Delay(7000);

                // 最多尝试5次刷新
                for (int i = 0; i < 5; i++)
                {
                    // 在主线程上执行刷新（不使用嵌套的异步调用）
                    await Application.Current.Dispatcher.InvokeAsync(() => AddLog($"第 {i + 1} 次刷新模拟器状态..."));

                    // 同步执行刷新
                    bool refreshResult = await RefreshSelectedGameWithResult(game);

                    // 如果刷新成功并且获取到有效句柄，输出日志并退出
                    if (refreshResult && game.MumuHandle != 0 && game.GameHandle != 0)
                    {
                        await Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            AddLog($"成功获取模拟器和游戏句柄，停止自动刷新");
                        });

                        // 直接返回退出整个方法，刷新成功后就不需要继续了
                        return;
                    }

                    // 如果还没到最后一次刷新，等待1秒后继续
                    if (i < 4)
                    {
                        await Task.Delay(1000);
                    }
                }

                // 如果需要，添加完成日志
                if (!IsTaskControlVisible)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() => AddLog("完成自动刷新"));
                }
            }
            catch (Exception ex)
            {
                // 异常处理
                await Application.Current.Dispatcher.InvokeAsync(() =>
                    AddLog($"自动刷新过程中出错: {ex.Message}"));
            }
            finally
            {
                // 确保标志被重置
                IsAutoRefreshing = false;
            }
        }

        /// <summary>
        /// 在选中的模拟器中启动阴阳师游戏
        /// </summary>
        /// <param name="game">要启动阴阳师的游戏对象</param>
        /// <remarks>
        /// 关联UI：启动阴阳师按钮（游戏列表右键菜单或工具栏）
        /// 功能：通过ADB连接到指定的模拟器，并在其中启动阴阳师应用
        /// </remarks>
        [RelayCommand]
        internal void LaunchYys(SuperMultiGame_DataModel game)
        {
            if (game == null) return;

            AddLog($"正在启动游戏 {game.GameName} 的阴阳师...");

            // 检查模拟器是否已启动
            if (game.MumuHandle == 0)
            {
                AddLog($"模拟器 {game.GameName} 未启动，请先启动模拟器");
                AddGameLog(game.GameId, "模拟器未启动，无法启动阴阳师");
                Utils.ShowMessage("启动失败", $"模拟器 {game.GameName} 未启动，请先启动模拟器");
                return;
            }

            // 使用Task.Run执行耗时操作，避免阻塞UI线程
            Task.Run(async () =>
            {
                try
                {
                    // 初始化MuMu实例
                    var mumu = new ScriptEngine.MuMu.MuMu();
                    var path = XConfig.LoadValueFromFile<string>("MuMuPath");
                    if (string.IsNullOrEmpty(path))
                    {
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            AddLog("MuMu模拟器路径未设置，无法启动阴阳师");
                            AddGameLog(game.GameId, "MuMu模拟器路径未设置");
                            Utils.ShowMessage("启动失败", "MuMu模拟器路径未设置，请先配置模拟器");
                        });
                        return;
                    }

                    if (!mumu.Init(path))
                    {
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            AddLog("MuMu模拟器初始化失败，无法启动阴阳师");
                            AddGameLog(game.GameId, "MuMu模拟器初始化失败");
                            Utils.ShowMessage("启动失败", "MuMu模拟器初始化失败，请检查路径设置");
                        });
                        return;
                    }

                    // 获取模拟器实例信息
                    var mumuInstances = await mumu._GetInstancesAsync();
                    if (mumuInstances == null || mumuInstances.Instances.Count == 0)
                    {
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            AddLog("未找到任何MuMu模拟器实例，请确保模拟器已启动");
                            AddGameLog(game.GameId, "未找到模拟器实例");
                            Utils.ShowMessage("启动失败", "未找到任何MuMu模拟器实例，请确保模拟器已启动");
                        });
                        return;
                    }

                    // 查找对应的模拟器实例
                    // 方法1: 首先尝试通过名称匹配
                    var instance = mumuInstances.Instances.Values.FirstOrDefault(i => i.Name == game.MumuRealName);

                    // 方法2: 如果名称匹配失败，尝试通过索引匹配
                    if (instance == null && game.MumuInstance != null && !string.IsNullOrEmpty(game.MumuInstance.Index))
                    {
                        if (mumuInstances.Instances.ContainsKey(game.MumuInstance.Index))
                        {
                            instance = mumuInstances.Instances[game.MumuInstance.Index];
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                AddLog($"通过原始Index匹配成功找到模拟器实例: {game.MumuInstance.Index}");
                                AddGameLog(game.GameId, $"通过索引匹配成功找到模拟器实例: {game.MumuInstance.Index}");
                            });
                        }
                    }

                    if (instance == null)
                    {
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            AddLog($"未找到模拟器 {game.GameName} 的实例信息");
                            AddGameLog(game.GameId, "未找到模拟器实例信息");
                            Utils.ShowMessage("启动失败", $"未找到模拟器 {game.GameName} 的实例信息");
                        });
                        return;
                    }

                    // 获取ADB端口
                    if (!instance.AdbPort.HasValue)
                    {
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            AddLog($"无法获取模拟器 {game.GameName} 的ADB端口");
                            AddGameLog(game.GameId, "无法获取ADB端口");
                            Utils.ShowMessage("启动失败", $"无法获取模拟器 {game.GameName} 的ADB端口");
                        });
                        return;
                    }

                    int adbPort = instance.AdbPort.Value;

                    // 连接到ADB设备
                    if (!mumu.ConnectToAdbDevice(adbPort))
                    {
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            AddLog($"无法连接到模拟器 {game.GameName} 的ADB设备，端口：{adbPort}");
                            AddGameLog(game.GameId, $"无法连接ADB设备，端口：{adbPort}");
                            Utils.ShowMessage("启动失败", $"无法连接到模拟器 {game.GameName} 的ADB设备，端口：{adbPort}\n\n请尝试重启模拟器或重启电脑后再试。");
                        });
                        return;
                    }

                    // 启动阴阳师
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        AddLog($"正在启动阴阳师...");
                        AddGameLog(game.GameId, "正在启动阴阳师");
                    });

                    mumu.Start_App("阴阳师");

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        AddLog($"已启动阴阳师，等待3秒...");
                        AddGameLog(game.GameId, "已启动阴阳师");
                    });

                    await Task.Delay(3000);

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        AddLog($"阴阳师启动成功！");
                        AddGameLog(game.GameId, "阴阳师启动成功");
                    });
                }
                catch (Exception ex)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        AddLog($"启动阴阳师失败：{ex.Message}");
                        AddGameLog(game.GameId, $"启动阴阳师失败：{ex.Message}");
                        Utils.ShowMessage("启动失败", $"启动阴阳师失败：{ex.Message}");
                    });
                }
            });
        }

        /// <summary>
        /// 刷新所有游戏的模拟器数据
        /// </summary>
        /// <param name="showMessageBox">是否显示结果消息框</param>
        /// <remarks>
        /// 关联UI：刷新模拟器数据按钮
        /// 功能：获取最新的MuMu模拟器实例信息，更新所有游戏对象的模拟器句柄、状态和实例信息
        /// 自动匹配模拟器，支持模拟器重启后重新匹配
        /// </remarks>
        [RelayCommand]
        internal async void RefreshMuMuData(string showMessageBoxStr = "True")
        {
            // 解析是否显示消息框
            bool showMessageBox = showMessageBoxStr != "False";

            // 获取MuMu模拟器路径
            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (string.IsNullOrEmpty(path))
            {
                AddLog("MuMu模拟器路径未设置，无法刷新数据");
                Utils.ShowMessage("刷新失败", "MuMu模拟器路径未设置，请先配置模拟器");
                return;
            }

            // 初始化MuMu实例
            MuMu mumu = new();
            if (!mumu.Init(path))
            {
                AddLog("MuMu模拟器初始化失败，无法刷新数据");
                if (showMessageBox)
                {
                    Utils.ShowMessage("刷新失败", "MuMu模拟器初始化失败，请检查路径设置");
                }
                return;
            }

            // 获取最新的模拟器实例信息
            var mumuInstances = await mumu._GetInstancesAsync();
            if (mumuInstances == null || mumuInstances.Instances.Count == 0)
            {
                AddLog("未找到任何MuMu模拟器实例，请确保模拟器已启动");
                if (showMessageBox)
                {
                    Utils.ShowMessage("刷新结果", "未找到任何MuMu模拟器实例，请确保模拟器已启动");
                }
                return;
            }

            AddLog($"发现 {mumuInstances.Instances.Count} 个模拟器实例");
            foreach (var inst in mumuInstances.Instances)
            {
                AddLog($"模拟器实例: 索引={inst.Key}, 名称={inst.Value.Name}, MainWnd={inst.Value.MainWnd}, RenderWnd={inst.Value.RenderWnd}");
            }

            int updatedCount = 0;
            int errorCount = 0;

            // 更新已有游戏的模拟器数据
            foreach (var game in SuperMultiGame_DataModelCollection)
            {
                try
                {
                    AddLog($"尝试刷新游戏: ID={game.GameId}, 名称={game.GameName}, MumuHandle={game.MumuHandle}(十进制), MumuHandle=0x{game.MumuHandle:X}(十六进制)");

                    // 使用MumuUtils查找匹配的模拟器实例
                    var matchResult = MumuUtils.FindMatchingMumuInstance(
                        mumuInstances.Instances,
                        game,
                        message => AddLog(message));

                    if (matchResult.HasValue)
                    {
                        // 更新游戏模型的句柄信息
                        MumuUtils.UpdateGameModelHandles(game, matchResult.Value.Value);

                        AddLog($"成功更新游戏 {game.GameName} 的模拟器数据, 模拟器名称: {matchResult.Value.Value.Name}");
                        AddGameLog(game.GameId, $"成功刷新模拟器数据，主窗口句柄：{game.MumuHandle}，渲染窗口句柄：{game.GameHandle}");
                        updatedCount++;
                    }
                    else
                    {
                        string errorMsg = game.MumuHandle == 0 ?
                            "未找到与游戏对应的模拟器实例，请确保正确启动了模拟器" :
                            $"未找到与游戏对应的模拟器实例，尝试的句柄: {game.MumuHandle}(十进制), 0x{game.MumuHandle:X}(十六进制)";

                        AddLog(errorMsg);
                        AddGameLog(game.GameId, "未找到对应的模拟器实例，可能模拟器已关闭或重启");

                        // 如果句柄不为0，说明之前是有有效句柄的，现在找不到可能是模拟器已关闭
                        if (game.MumuHandle != 0)
                        {
                            AddLog($"检测到模拟器可能已关闭，重置游戏 {game.GameName} 的句柄为0");
                            AddGameLog(game.GameId, "检测到模拟器可能已关闭，重置句柄值");

                            // 重置句柄为0
                            game.MumuHandle = 0;
                            game.GameHandle = 0;
                        }
                        errorCount++;
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"刷新游戏 {game.GameName} 时出错: {ex.Message}");
                    errorCount++;
                }
            }

            // 显示结果消息
            if (showMessageBox)
            {
                string resultMessage;
                if (updatedCount > 0)
                {
                    resultMessage = $"成功刷新 {updatedCount} 个游戏的模拟器数据";
                    if (errorCount > 0)
                    {
                        resultMessage += $"，有 {errorCount} 个游戏更新失败";
                    }
                }
                else if (errorCount > 0)
                {
                    resultMessage = $"所有游戏 ({errorCount} 个) 都刷新失败";
                }
                else
                {
                    resultMessage = "没有任何游戏需要刷新";
                }

                Utils.ShowMessage("刷新结果", resultMessage);
            }

            // 触发模拟器数据刷新完成事件
            MuMuDataRefreshed?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 刷新选中游戏的模拟器数据和状态信息
        /// </summary>
        /// <param name="game">要刷新的游戏对象</param>
        /// <remarks>
        /// 关联UI：刷新按钮（游戏列表右键菜单）
        /// 功能：仅更新选中游戏对象的模拟器句柄、状态和实例信息
        /// </remarks>
        [RelayCommand]
        internal async void RefreshSelectedGame(SuperMultiGame_DataModel game)
        {
            if (game == null) return;

            AddLog($"正在刷新游戏 {game.GameName} 的模拟器数据...");

            // 获取MuMu模拟器路径
            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (string.IsNullOrEmpty(path))
            {
                AddLog("MuMu模拟器路径未设置，无法刷新数据");
                Utils.ShowMessage("刷新失败", "MuMu模拟器路径未设置，请先配置模拟器");
                return;
            }

            // 初始化MuMu实例
            MuMu mumu = new();
            if (!mumu.Init(path))
            {
                AddLog("MuMu模拟器初始化失败，无法刷新数据");
                Utils.ShowMessage("刷新失败", "MuMu模拟器初始化失败，请检查路径设置");
                return;
            }

            // 获取最新的模拟器实例信息
            var mumuInstances = await mumu._GetInstancesAsync();
            if (mumuInstances == null || mumuInstances.Instances.Count == 0)
            {
                AddLog("未找到任何MuMu模拟器实例，请确保模拟器已启动");
                Utils.ShowMessage("刷新结果", "未找到任何MuMu模拟器实例，请确保模拟器已启动");
                return;
            }

            // 使用MumuUtils查找匹配的模拟器实例
            var matchResult = MumuUtils.FindMatchingMumuInstance(
                mumuInstances.Instances,
                game,
                message => AddLog(message));

            if (matchResult.HasValue)
            {
                // 更新游戏模型的句柄信息
                MumuUtils.UpdateGameModelHandles(game, matchResult.Value.Value);

                AddLog($"成功刷新游戏 {game.GameName} 的模拟器数据, 模拟器名称: {matchResult.Value.Value.Name}");
                AddGameLog(game.GameId, "成功刷新模拟器数据");

                // 如果游戏是选中的游戏，也更新预览
                if (game == SelectedGameModel)
                {
                    OnPropertyChanged(nameof(SelectedGameModel));
                }
            }
            else
            {
                AddLog($"未找到游戏 {game.GameName} 对应的模拟器实例");
                AddGameLog(game.GameId, "未找到对应的模拟器实例");

                // 检查是否是之前有有效句柄的游戏
                if (game.MumuHandle != 0)
                {
                    AddLog($"检测到模拟器可能已关闭，重置游戏 {game.GameName} 的句柄为0");
                    AddGameLog(game.GameId, "检测到模拟器可能已关闭，重置句柄值");

                    // 重置句柄为0
                    game.MumuHandle = 0;
                    game.GameHandle = 0;
                }
            }
        }

        /// <summary>
        /// 保存当前游戏列表配置到文件
        /// </summary>
        /// <remarks>
        /// 关联UI：保存按钮
        /// 功能：将当前SuperMultiGame_DataModelCollection中的游戏配置保存到配置文件
        /// 调用SaveConfiguration方法执行实际保存操作
        /// </remarks>
        [RelayCommand]
        internal void SaveGameList()
        {
            SaveConfiguration();
        }

        /// <summary>
        /// 启动所有配置了任务的游戏
        /// </summary>
        /// <remarks>
        /// 关联UI：启动全部按钮
        /// 功能：遍历游戏列表，启动所有未运行且已配置任务的游戏
        /// 跳过已经在运行的游戏和未配置任务的游戏
        /// </remarks>
        [RelayCommand]
        internal async Task StartAll()
        {
            AddLog("正在启动所有游戏...");
            int startedCount = 0;
            int failedCount = 0;
            List<string> failedGames = new List<string>();

            foreach (var game in SuperMultiGame_DataModelCollection)
            {
                if (game.RunningStatus != "运行中")
                {
                    // 检查是否有配置任务
                    if (game.TaskConfiguration == null || game.TaskConfiguration.GameTaskLists.Count == 0)
                    {
                        string skipMessage = $"游戏 {game.GameName} 还没有配置任务，跳过启动";
                        AddLog(skipMessage);
                        AddGameLog(game.GameId, skipMessage);
                        continue;
                    }

                    try
                    {
                        // 确保游戏Config不为空
                        EnsureGameConfigExists(game);

                        // 实际启动游戏任务
                        await StartGameTask(game);
                        startedCount++;
                    }
                    catch (Exception ex)
                    {
                        failedCount++;
                        failedGames.Add(game.GameName);
                        string errorMessage = $"启动游戏 {game.GameName} 失败: {ex.Message}";
                        AddLog(errorMessage);
                        AddGameLog(game.GameId, errorMessage);
                    }
                }
            }

            string resultMessage = $"启动操作完成: 成功启动 {startedCount} 个游戏";
            if (failedCount > 0)
            {
                resultMessage += $", {failedCount} 个游戏启动失败: {string.Join(", ", failedGames)}";
            }

            AddLog(resultMessage);
            Utils.ShowMessage("启动结果", resultMessage);
        }

        /// <summary>
        /// 停止所有正在运行的游戏
        /// </summary>
        /// <remarks>
        /// 关联UI：停止全部按钮
        /// 功能：遍历游戏列表，停止所有正在运行的游戏任务
        /// </remarks>
        [RelayCommand]
        internal async Task StopAll()
        {
            AddLog("正在停止所有游戏...");
            int stoppedCount = 0;
            int failedCount = 0;
            List<string> failedGames = new List<string>();

            foreach (var game in SuperMultiGame_DataModelCollection)
            {
                if (game.RunningStatus == "运行中")
                {
                    try
                    {
                        // 实际停止游戏任务
                        await StopGameTask(game);
                        stoppedCount++;
                    }
                    catch (Exception ex)
                    {
                        failedCount++;
                        failedGames.Add(game.GameName);
                        string errorMessage = $"停止游戏 {game.GameName} 失败: {ex.Message}";
                        AddLog(errorMessage);
                        AddGameLog(game.GameId, errorMessage);
                    }
                }
            }

            string resultMessage = $"停止操作完成: 成功停止 {stoppedCount} 个游戏";
            if (failedCount > 0)
            {
                resultMessage += $", {failedCount} 个游戏停止失败: {string.Join(", ", failedGames)}";
            }

            AddLog(resultMessage);
            Utils.ShowMessage("停止结果", resultMessage);
        }

        /// <summary>
        /// 切换游戏的运行状态（启动/停止）
        /// </summary>
        /// <param name="game">要操作的游戏对象</param>
        /// <remarks>
        /// 关联UI：游戏列表中的开始/停止按钮或切换按钮
        /// 功能：如果游戏当前正在运行，则停止；如果游戏当前未运行，则启动
        /// 启动前会检查游戏是否已配置任务，如果未配置则提示用户
        /// </remarks>
        [RelayCommand]
        internal async void ToggleGameStatus(SuperMultiGame_DataModel game)
        {
            if (game != null)
            {
                if (game.RunningStatus == "运行中")
                {
                    // 停止游戏
                    await StopGameTask(game);
                }
                else
                {
                    // 检查是否有配置任务
                    if (game.TaskConfiguration == null || game.TaskConfiguration.GameTaskLists.Count == 0)
                    {
                        string logMessage = $"游戏 {game.GameName} 还没有配置任务，请先配置任务！";
                        AddLog(logMessage);
                        AddGameLog(game.GameId, logMessage);
                        Utils.ShowMessage(logMessage);
                        // 设置当前选中项为该游戏，这样任务配置面板就会显示该游戏的配置
                        SelectedGameModel = game;
                        // 自动显示任务控制区
                        IsTaskControlVisible = true;
                        return;
                    }

                    // 检查并确保游戏Config不为空
                    EnsureGameConfigExists(game);

                    // 启动游戏任务
                    await StartGameTask(game);

                    // 找到对应游戏的日志标签页索引
                    int gameLogIndex = GameLogs.ToList().FindIndex(log => log.GameId == game.GameId);
                    if (gameLogIndex >= 0)
                    {
                        // 由于主日志标签页占用第一个位置，所以需要加1
                        SelectedLogTabIndex = gameLogIndex + 1;
                    }
                }
            }
        }

        /// <summary>
        /// 切换任务配置控制面板的显示状态
        /// </summary>
        /// <remarks>
        /// 关联UI：任务配置按钮或切换任务配置区按钮
        /// 功能：显示或隐藏任务配置面板区域
        /// 通过IsTaskControlVisible属性控制界面对应区域的可见性
        /// </remarks>
        [RelayCommand]
        internal void ToggleTaskControl()
        {
            // 切换任务控制区显示状态
            IsTaskControlVisible = !IsTaskControlVisible;
            AddLog(IsTaskControlVisible ? "显示任务配置面板" : "隐藏任务配置面板");
        }

        /// <summary>
        /// 切换窗口置顶状态
        /// </summary>
        /// <remarks>
        /// 关联UI：置顶按钮
        /// 功能：切换主窗口的置顶状态（Topmost属性）
        /// 通过IsWindowTopMost属性控制和反映窗口的置顶状态
        /// </remarks>
        [RelayCommand]
        internal void ToggleWindowTopMost()
        {
            // 切换窗口置顶状态
            IsWindowTopMost = !IsWindowTopMost;

            // 如果有窗口引用，则设置窗口的Topmost属性
            if (_windowReference != null && _windowReference.TryGetTarget(out Window window))
            {
                window.Topmost = IsWindowTopMost;
                AddLog(IsWindowTopMost ? "窗口已置顶" : "已取消窗口置顶");
            }
        }

        /// <summary>
        /// 处理副配置按钮点击事件
        /// </summary>
        /// <remarks>
        /// 关联UI：游戏列表中的"副配置"按钮
        /// 功能：打开游戏的副配置面板（功能将在后续实现）
        /// </remarks>
        [RelayCommand]
        internal void SecondaryConfig(SuperMultiGame_DataModel game)
        {
            if (game == null) return;

            // 确保游戏Config不为空
            EnsureGameConfigExists(game);

            // 保存原始配置的副本
            var originalConfig = game.Config.Clone() as SuperMultiGameConfigWindowViewModel;

            // 创建新的窗口实例，使用游戏的Config
            var configWindow = new SuperMultiGameConfigWindow(game.Config);
            configWindow.ShowDialog();

            if (configWindow.DialogResult)
            {
                AutoSaveTaskConfigs(game);
                AddLog($"已保存游戏 {game.GameName} 的副配置");
                AddGameLog(game.GameId, "已保存副配置");
            }
            else
            {
                // 用户点击取消，恢复原始配置
                game.Config = originalConfig;
                AddLog($"取消保存游戏 {game.GameName} 的副配置");
                AddGameLog(game.GameId, "取消保存副配置");
            }
        }
    }
}