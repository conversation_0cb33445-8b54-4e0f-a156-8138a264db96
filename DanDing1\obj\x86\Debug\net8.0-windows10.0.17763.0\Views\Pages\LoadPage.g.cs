﻿#pragma checksum "..\..\..\..\..\..\Views\Pages\LoadPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "738A1AA1ED184E5F2A6C62C5802A6F6E8CA962FC"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1;
using DanDing1.Helpers;
using DanDing1.ViewModels.Pages;
using DanDing1.Views.Pages;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Pages {
    
    
    /// <summary>
    /// LoadPage
    /// </summary>
    public partial class LoadPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 42 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LoadScreen;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.PasswordBox 登录;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.TextBox EmailText;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.TextBox CodeText;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.TextBox Reset_Email;
        
        #line default
        #line hidden
        
        
        #line 675 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button WhyBingQQ;
        
        #line default
        #line hidden
        
        
        #line 686 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button Bind_QQ;
        
        #line default
        #line hidden
        
        
        #line 702 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel WebTokenShow;
        
        #line default
        #line hidden
        
        
        #line 705 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button SetWebTokenButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/pages/loadpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.LoadScreen = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 2:
            this.登录 = ((Wpf.Ui.Controls.PasswordBox)(target));
            
            #line 89 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
            this.登录.KeyDown += new System.Windows.Input.KeyEventHandler(this.TextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 3:
            this.EmailText = ((Wpf.Ui.Controls.TextBox)(target));
            return;
            case 4:
            this.CodeText = ((Wpf.Ui.Controls.TextBox)(target));
            return;
            case 5:
            this.Reset_Email = ((Wpf.Ui.Controls.TextBox)(target));
            return;
            case 6:
            
            #line 490 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
            ((System.Windows.Controls.TextBlock)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.TextBlock_MouseDown);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 566 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
            ((System.Windows.Controls.TabItem)(target)).GotFocus += new System.Windows.RoutedEventHandler(this.KamiTab_GotFocus);
            
            #line default
            #line hidden
            return;
            case 8:
            this.WhyBingQQ = ((Wpf.Ui.Controls.Button)(target));
            
            #line 681 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
            this.WhyBingQQ.Click += new System.Windows.RoutedEventHandler(this.WhyBingQQ_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.Bind_QQ = ((Wpf.Ui.Controls.Button)(target));
            
            #line 688 "..\..\..\..\..\..\Views\Pages\LoadPage.xaml"
            this.Bind_QQ.Click += new System.Windows.RoutedEventHandler(this.Button_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.WebTokenShow = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 11:
            this.SetWebTokenButton = ((Wpf.Ui.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

