﻿<Window x:Class="DanDing1.Views.Windows.TextShareWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        mc:Ignorable="d"
        Title="文本分享" Height="450" Width="600"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5"
        FontFamily="Microsoft YaHei"
        Loaded="Window_Loaded">
    <Window.Resources>
        <Style TargetType="TextBlock">
            <Setter Property="Margin" Value="0,0,0,8" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Foreground" Value="#333333" />
        </Style>
        <Style TargetType="TextBox">
            <Setter Property="Padding" Value="10" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Background" Value="White" />
            <Setter Property="BorderBrush" Value="#DDDDDD" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="Margin" Value="0,0,0,15" />
        </Style>
        <Style TargetType="Button">
            <Setter Property="Padding" Value="15,8" />
            <Setter Property="Background" Value="#2196F3" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Margin="20" Background="White" CornerRadius="8" Padding="25">
        <Border.Effect>
            <DropShadowEffect BlurRadius="15" ShadowDepth="1" Opacity="0.2" Color="#000000" />
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!-- 标题和提示文本 -->
            <StackPanel Grid.Row="0">
                <TextBlock Text="文本分享 - 因置剪贴板失败，需要您手动复制" FontSize="24" FontWeight="Bold" Margin="0,0,0,15" />
                <TextBlock Text="以下是您要分享的文本内容，您可以手动复制此文本：" TextWrapping="Wrap" />
            </StackPanel>

            <!-- 文本编辑框 -->
            <TextBox x:Name="ShareTextBox"
                     Grid.Row="1"
                     TextWrapping="Wrap"
                     AcceptsReturn="True"
                     VerticalScrollBarVisibility="Auto"
                     Margin="0,15"
                     IsReadOnly="True"
                     x:FieldModifier="private" />

            <!-- 底部按钮 -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button x:Name="CloseButton" Width="80" Height="30" Content="关闭窗口" Click="CloseButton_Click" />
            </StackPanel>
        </Grid>
    </Border>
</Window>