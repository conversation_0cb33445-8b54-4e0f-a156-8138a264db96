using DamoControlKit;
using DamoControlKit.Model;
using System;
using System.IO;
using System.Text.RegularExpressions;
using XHelper;

namespace DanDing1.Commands
{
    /// <summary>
    /// 窗口绑定测试命令类
    /// </summary>
    internal class Test_Bind : BaseCommand
    {
        // 定义四种不同的绑定模式
        public static BindModel mumu_model1 = new BindModel()
        {
            Display = Display.dx2,
            Mouse = DamoControlKit.Model.Mouse.dx_mouse_api,
            Keypad = Keypad.windows,
            Mode = 0,
            Public = ""
        };

        public static BindModel mumu_model2 = new BindModel()
        {
            Display = Display.dx2,
            Mouse = DamoControlKit.Model.Mouse.windows,
            Keypad = Keypad.windows,
            Mode = 0,
            Public = ""
        };

        public static BindModel mumu_model4 = new BindModel()
        {
            Display = Display.dx2,
            Mouse = DamoControlKit.Model.Mouse.windows3,
            Keypad = Keypad.windows,
            Mode = 0,
            Public = ""
        };

        public static BindModel mumu_model3 = new BindModel()
        {
            Display = Display.gdi2,
            Mouse = DamoControlKit.Model.Mouse.windows,
            Keypad = Keypad.windows,
            Mode = 0,
            Public = ""
        };

        public override void Execute(string[] parameters)
        {
            if (!ValidateParameterCount(parameters, 3))
            {
                PrintHelp();
                return;
            }

            string mode = parameters[2].ToLower();
            string hwndStr;
            int hwnd;

            // 处理自定义绑定模式
            if (mode == "mumu")
            {
                if (!ValidateParameterCount(parameters, 5))
                {
                    XLogger.Error("自定义绑定模式参数不足");
                    PrintHelp();
                    return;
                }

                string customParams = parameters[3];
                hwndStr = parameters[4];

                // 验证并转换句柄
                if (!int.TryParse(hwndStr, out hwnd) || hwnd <= 0)
                {
                    XLogger.Error("无效的窗口句柄");
                    return;
                }

                // 解析自定义参数
                if (TryParseCustomBindParameters(customParams, out string display, out string mouse, out string keypad, out string publicStr))
                {
                    TestCustomBind(hwnd, display, mouse, keypad, publicStr);
                }
                else
                {
                    XLogger.Error("自定义绑定参数格式错误，应为 (display,mouse,keypad,public)");
                    PrintHelp();
                }
                return;
            }

            // 处理预定义的绑定模式
            if (!ValidateParameterCount(parameters, 4))
            {
                PrintHelp();
                return;
            }

            hwndStr = parameters[3];

            // 验证并转换句柄
            if (!int.TryParse(hwndStr, out hwnd) || hwnd <= 0)
            {
                XLogger.Error("无效的窗口句柄");
                return;
            }

            // 根据模式选择对应的绑定模型
            BindModel bindModel;
            switch (mode)
            {
                case "mode1":
                    bindModel = mumu_model1;
                    TestBind(hwnd, bindModel, "mode1");
                    break;
                case "mode2":
                    bindModel = mumu_model2;
                    TestBind(hwnd, bindModel, "mode2");
                    break;
                case "mode3":
                    bindModel = mumu_model3;
                    TestBind(hwnd, bindModel, "mode3");
                    break;
                case "mode4":
                    bindModel = mumu_model4;
                    TestBind(hwnd, bindModel, "mode4");
                    break;
                default:
                    XLogger.Error($"不支持的绑定模式: {mode}");
                    PrintHelp();
                    return;
            }
        }

        /// <summary>
        /// 尝试解析自定义绑定参数
        /// </summary>
        /// <param name="paramStr">参数字符串，格式：(display,mouse,keypad,public)</param>
        /// <param name="display">屏幕颜色获取方式</param>
        /// <param name="mouse">鼠标仿真模式</param>
        /// <param name="keypad">键盘仿真模式</param>
        /// <param name="publicStr">公共属性</param>
        /// <returns>是否成功解析</returns>
        private bool TryParseCustomBindParameters(string paramStr, out string display, out string mouse, out string keypad, out string publicStr)
        {
            display = string.Empty;
            mouse = string.Empty;
            keypad = string.Empty;
            publicStr = string.Empty;

            // 使用正则表达式匹配格式 (param1,param2,param3,param4)
            var match = Regex.Match(paramStr, @"^\((.*?),(.*?),(.*?),(.*?)\)$");
            if (!match.Success || match.Groups.Count != 5)
            {
                return false;
            }

            display = match.Groups[1].Value.Trim();
            mouse = match.Groups[2].Value.Trim();
            keypad = match.Groups[3].Value.Trim();
            publicStr = match.Groups[4].Value.Trim();

            return true;
        }

        /// <summary>
        /// 测试自定义绑定模式
        /// </summary>
        /// <param name="hwnd">窗口句柄</param>
        /// <param name="display">屏幕颜色获取方式</param>
        /// <param name="mouse">鼠标仿真模式</param>
        /// <param name="keypad">键盘仿真模式</param>
        /// <param name="publicStr">公共属性</param>
        private void TestCustomBind(int hwnd, string display, string mouse, string keypad, string publicStr)
        {
            dmsoft dm = null;
            string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            string imgPath = Path.Combine(desktopPath, $"testbind_custom.jpg");

            XLogger.Info($"开始测试自定义绑定模式，窗口句柄: {hwnd}");
            XLogger.Info($"绑定参数: display={display}, mouse={mouse}, keypad={keypad}, public={publicStr}");

            try
            {
                // 创建大漠对象
                dm = new dmsoft();

                // 直接使用BindWindowEx绑定
                int result = dm.BindWindowEx(hwnd, display, mouse, keypad, publicStr, 0);
                if (result != 1)
                {
                    XLogger.Error($"绑定窗口失败，错误代码: {dm.GetLastError()}");
                    return;
                }

                XLogger.Info($"绑定窗口成功，大漠对象ID: {dm.GetID()}");

                // 截图
                XLogger.Info($"开始截图，保存路径: {imgPath}");
                result = dm.CaptureJpg(0, 0, 2000, 2000, imgPath, 100);
                if (result == 1)
                {
                    XLogger.Info($"截图成功，已保存到: {imgPath}");
                }
                else
                {
                    XLogger.Error($"截图失败，错误代码: {dm.GetLastError()}");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"自定义绑定测试过程中发生异常: {ex.Message}");
            }
            finally
            {
                // 解除绑定
                if (dm != null)
                {
                    try
                    {
                        XLogger.Info("解除窗口绑定");
                        dm.UnBindWindow();
                        XLogger.Info("窗口绑定已解除");
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"解除窗口绑定时发生异常: {ex.Message}");
                    }
                }
            }

            XLogger.Info("自定义绑定模式测试完成");
        }

        /// <summary>
        /// 测试窗口绑定
        /// </summary>
        /// <param name="hwnd">窗口句柄</param>
        /// <param name="bindModel">绑定模型</param>
        /// <param name="modeName">模式名称</param>
        private void TestBind(int hwnd, BindModel bindModel, string modeName)
        {
            dmsoft dm = null;
            string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            string imgPath = Path.Combine(desktopPath, $"testbind{modeName}.jpg");

            XLogger.Info($"开始测试绑定模式 {modeName}，窗口句柄: {hwnd}");
            XLogger.Info($"绑定参数: {bindModel}");

            try
            {
                // 设置句柄
                bindModel.Hwnd = hwnd;

                // 进行绑定
                dm = DamoKit.BindHwnd(bindModel, out string errorStr);
                if (!string.IsNullOrEmpty(errorStr))
                {
                    XLogger.Error($"绑定窗口失败: {errorStr}");
                    return;
                }

                XLogger.Info($"绑定窗口成功，大漠对象ID: {dm.GetID()}");

                // 截图
                XLogger.Info($"开始截图，保存路径: {imgPath}");
                int result = dm.CaptureJpg(0, 0, 2000, 2000, imgPath, 100);
                if (result == 1)
                {
                    XLogger.Info($"截图成功，已保存到: {imgPath}");
                }
                else
                {
                    XLogger.Error($"截图失败，错误代码: {dm.GetLastError()}");
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"绑定测试过程中发生异常: {ex.Message}");
            }
            finally
            {
                // 解除绑定
                if (dm != null)
                {
                    try
                    {
                        XLogger.Info("解除窗口绑定");
                        dm.UnBindWindow();
                        XLogger.Info("窗口绑定已解除");
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"解除窗口绑定时发生异常: {ex.Message}");
                    }
                }
            }

            XLogger.Info($"绑定模式 {modeName} 测试完成");
        }

        /// <summary>
        /// 打印帮助信息
        /// </summary>
        private void PrintHelp()
        {
            XLogger.Info("使用方法: test bind [mode1/mode2/mode3/mode4] [游戏句柄]");
            XLogger.Info("或者: test bind mumu (display,mouse,keypad,public) [游戏句柄]");
            XLogger.Info("示例: test bind mode1 123456");
            XLogger.Info("示例: test bind mumu (dx2,windows,windows,) 123456");
            XLogger.Info("预定义模式说明:");
            XLogger.Info("  mode1: dx2显示 + dx_mouse_api鼠标 + windows键盘");
            XLogger.Info("  mode2: dx2显示 + windows鼠标 + windows键盘");
            XLogger.Info("  mode3: gdi2显示 + windows鼠标 + windows键盘");
            XLogger.Info("  mode4: dx2显示 + windows3鼠标 + windows键盘");
            XLogger.Info("自定义模式参数说明:");
            XLogger.Info("  display: 屏幕颜色获取方式，如dx2、gdi2等");
            XLogger.Info("  mouse: 鼠标仿真模式，如windows、dx.mouse.api等");
            XLogger.Info("  keypad: 键盘仿真模式，如windows等");
            XLogger.Info("  public: 公共属性，可为空");
        }
    }
}