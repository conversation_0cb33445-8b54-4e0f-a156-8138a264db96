﻿using DamoControlKit.Interface;
using DamoControlKit.Model;
using System.Diagnostics;
using XHelper;

namespace DamoControlKit.Function
{
    public class Flow_L_Find_MemPic : IFlow, IDmInterface
    {
        /// <summary>
        /// 结束时间
        /// </summary>
        private int EndTime { get; set; } = 1800;

        /// <summary>
        /// 找到图片执行后延迟时间 ms
        /// </summary>
        private int FindYepDelayTime { get; set; } = 500;

        public dmsoft? dmsoft { get; set; }

        public void Init()
        { }

        public bool SetXsoft() => false;

        /// <summary>
        /// 设置超时时间
        /// 默认结束时间30min
        /// </summary>
        /// <param name="timeSec">秒</param>
        /// <returns></returns>
        public Flow_L_Find_MemPic SetEndTime(int timeSec)
        {
            EndTime = timeSec;
            return this;
        }

        /// <summary>
        /// 设置 找到图片执行后延迟时间
        /// 单位 ms
        /// </summary>
        /// <returns></returns>
        public Flow_L_Find_MemPic SetFindYepDelayTime(int ms)
        {
            FindYepDelayTime = ms;
            return this;
        }

        /// <summary>
        /// 设置所有Pic的dmsoft对象
        /// </summary>
        /// <param name="x"></param>
        /// <returns></returns>
        public bool SetXsoft(dmsoft x)
        {
            PicList.SetAllXsoft(x);
            ExitPicList.SetAllXsoft(x);
            dmsoft = x;
            return true;
        }

        /// <summary>
        /// 循环图片组
        /// </summary>
        private MemPics PicList { get; set; } = new();

        /// <summary>
        /// 循环退出条件组
        /// 循环条件中的图片不支持点击
        /// </summary>
        private MemPics ExitPicList { get; set; } = new();

        /// <summary>
        /// 添加退出条件 图片
        /// </summary>
        /// <param name="pic"></param>
        /// <returns></returns>
        public Flow_L_Find_MemPic AddExitPic(MemPic pic)
        {
            if (dmsoft is not null) pic.SetXsoft(dmsoft);
            ExitPicList.Add(pic);
            PicList.Add(pic);
            return this;
        }

        /// <summary>
        /// 添加退出条件 图片
        /// </summary>
        /// <param name="pic"></param>
        /// <returns></returns>
        public Flow_L_Find_MemPic AddExitPics(MemPics pics)
        {
            if (dmsoft is not null) pics.SetAllXsoft(dmsoft);
            ExitPicList.Add(pics);
            PicList.Add(pics);
            return this;
        }

        /// <summary>
        /// 添加图片
        /// </summary>
        /// <param name="pic"></param>
        /// <param name="pos"></param>
        /// <returns></returns>
        public Flow_L_Find_MemPic AddPic(MemPic pic)
        {
            if (dmsoft is not null)
                pic.SetXsoft(dmsoft);
            PicList.Add(pic);
            return this;
        }

        /// <summary>
        /// 添加图片集合
        /// </summary>
        /// <param name="pic"></param>
        /// <param name="pos"></param>
        /// <returns></returns>
        public Flow_L_Find_MemPic AddPics(MemPics pics)
        {
            if (dmsoft is not null)
                pics.SetAllXsoft(dmsoft);
            PicList.PicList.AddRange(pics.PicList);
            return this;
        }

        /// <summary>
        /// AddPicAndFun函数的回调方法字典
        /// 通过Name索引调用函数
        /// </summary>
        private Dictionary<string, FLFP_PicYep_Delegate> PicYepCallBackDic { get; set; } = new();

        /// <summary>
        /// 添加图片 并执行回调函数
        /// </summary>
        /// <param name="pic"></param>
        /// <returns></returns>
        public Flow_L_Find_MemPic AddPicAndFun(MemPic pic, FLFP_PicYep_Delegate CallBack)
        {
            if (dmsoft is not null) pic.SetXsoft(dmsoft);
            PicList.Add(pic);
            PicYepCallBackDic.Add(pic.Name, CallBack);
            return this;
        }

        /// <summary>
        /// 添加图片 并执行回调函数
        /// </summary>
        /// <param name="pic"></param>
        /// <returns></returns>
        public Flow_L_Find_MemPic AddPicsAndFun(MemPics pics, FLFP_PicYep_Delegate CallBack)
        {
            if (dmsoft is not null) pics.SetAllXsoft(dmsoft);
            PicList.Add(pics);
            foreach (var item in pics.PicList)
                PicYepCallBackDic.Add(item.Name, CallBack);
            return this;
        }

        /// <summary>
        /// 任务结束时调用此函数
        /// </summary>
        private FLFP_PicYep_Delegate EndCallBack { get; set; }

        public Flow_L_Find_MemPic SetEndCallBack(FLFP_PicYep_Delegate callback)
        {
            EndCallBack = callback;
            return this;
        }

        public void Start()
        {
            if (PicList.Count == 0)
            {
                Debug.WriteLine("Flow_L_Find_Pic 循环图片列表为空，无法继续执行！");
                return;
            }
            //if (ExitPicList.Count == 0)
            //{
            //    Debug.WriteLine("Flow_L_Find_Pic 循环退出条件图片列表为空，无法继续执行！");
            //    return;
            //}
            Main();
        }

        /// <summary>
        /// 运行时长计时
        /// </summary>
        private Stopwatch RunningWatch = new();

        /// <summary>
        /// 外部控制结束变量
        /// </summary>
        public bool isStop = false;

        private CancellationTokenSource Ct { get; set; }

        /// <summary>
        /// 延迟
        /// </summary>
        private void Sleep(int ms)
        {
            int delayStep = 1000; // 检查间隔，以毫秒为单位
            int totalTicks = (int)(ms * TimeSpan.TicksPerMillisecond); // 总延迟时间的刻度值

            for (long ticks = 0; ticks < totalTicks; ticks += delayStep * TimeSpan.TicksPerMillisecond)
            {
                if (Ct.Token.IsCancellationRequested)
                {
                    isStop = true;
                }
                Task.Delay(delayStep).Wait();
            }
        }

        public Flow_L_Find_MemPic SetCT(CancellationTokenSource ct)
        {
            Ct = ct;
            return this;
        }

        private void Main()
        {
            bool tmp;
            RunningWatch.Start();
            while (RunningWatch.Elapsed.TotalSeconds <= EndTime)
            {
                foreach (var t in PicList.PicList)
                {
                    if (isStop)
                    {
                        EndCallBack?.Invoke();
                        return;
                    }
                    tmp = t.Find();
                    if (tmp)
                    {
                        XLogger.Debug("Frames—FLFM-FindPic：" + t.Name);
                        FindYep(t.Name, t);
                        if (!RunningWatch.IsRunning)
                        {
                            EndCallBack?.Invoke();
                            return;
                        }
                        continue;
                    }
                }
                Sleep(20);
            }

            RunningWatch.Stop();
        }

        /// <summary>
        /// 查找到图片后操作
        /// </summary>
        private void FindYep(string identifier, MemPic pic)
        {
            if (PicYepCallBackDic.TryGetValue(identifier, out FLFP_PicYep_Delegate? value))
            {
                Debug.WriteLine($"Flow_L_Find_Pic {identifier}执行回调函数");
                value();
            }

            Debug.WriteLine($"Flow_L_Find_Pic {identifier}执行范围点击");
            Sleep(200);
            pic.Click();

            Sleep(FindYepDelayTime);

            if (ExitPicList.PicList.Contains(pic))
            {
                Debug.WriteLine($"Flow_L_Find_Pic {identifier}检测到退出图片，结束流程.");
                RunningWatch.Stop();
            }
        }

        public void Stop()
        { }
    }
}