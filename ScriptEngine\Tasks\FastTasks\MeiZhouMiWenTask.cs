﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Tasks.FastTasks
{
    internal class MeiZhouMiWenTask : BaseTask
    {
        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "每周秘闻");
        }

        /// <summary>
        /// 标记开关
        /// </summary>
        private bool Biaoji = false;

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            GetConfig.Others.TryGetValue("Biaoji", out string? s);
            try { Biaoji = s is null ? false : bool.Parse(s); } catch (Exception) { Biaoji = false; }
            log.Info_Green("您当前正在执行的任务是：每周秘闻，当前结束策略：胜利10次结束；");
            if (!WaitMainScene())
            {
                log.Error("当前不在秘闻主界面，请重新挑战游戏场景后启动！");
                return;
            }
            Main();
        }

        private int Ncount = 10;

        /// <summary>
        /// 主流程
        /// </summary>
        private void Main()
        {
            int count = 0;
        Re:
            while (count < Ncount)
            {
                if (!WaitMainScene()) goto Re;
                //检测是否结束了
                if (Mp.Filter("十层结束").FindAllEx() != null)
                {
                    log.Info_Green($"秘闻快速任务结束，检测到您第10层已经战斗完成！");
                    return;
                }

                if (Combat())
                {
                    count++;
                    log.Info($"秘闻战斗胜利，战斗次数：{count}/{Ncount}");
                }
                else
                {
                    log.Warn($"秘闻战斗失败，请检查您的队伍配置是否正常！战斗次数：{count}/{Ncount}");
                    Defeated();
                }
            }
        }

        private List<string> DontSendLog = ["标记"];

        /// <summary>
        /// 战斗
        /// </summary>
        /// <returns></returns>
        private bool Combat()
        {
            //点击开始
            Fast.Click("1122,566,1203,633");
            log.Info("战斗点击开始");
            var pics = Mp.Filter("每周秘闻");
            bool ret_bol = false;
            bool isbreak = false;
            BiaoJi_Status = false; // 标记状态重置
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                FindOkFun(p.Name, p);
                //检测是否结束了
                if (p.Name.Contains("十层结束"))
                {
                    log.Info_Green($"秘闻快速任务结束，检测到您第10层已经战斗完成！");
                    throw new TaskCanceledException();
                }

                if (!DontSendLog.Any(p.Name.Contains)) log.Info($"执行点击：{p._Name}");
                p.Click();
                if (p.Name.Contains("胜利") || p.Name.Contains("达摩") || p.Name.Contains("秘闻场景"))
                {
                    ret_bol = true;
                    isbreak = true;
                    Sleep(150);
                }
                if (p.Name.Contains("失败"))
                {
                    ret_bol = false;
                    isbreak = true;
                }
            }
            if (ret_bol)
                Combat_End();//等待main界面

            return ret_bol;
        }

        /// <summary>
        /// 胜利收尾工作
        /// </summary>
        private void Combat_End()
        {
            log.Info("战斗胜利(Combat_End)..");
            var pics = Mp.Filter("每周秘闻");
            bool isbreak = false;
            while (!isbreak)
            {
                Sleep(500);
                var p = pics.FindAllEa();
                if (p is null) continue;
                if (p.Name.Contains("挑战")||p.Name.Contains("结束"))
                {
                    isbreak = true;
                    continue;
                }
                log.Info($"执行点击：{p._Name}");
                p.Click();
            }
        }

        private bool BiaoJi_Status = false;

        /// <summary>
        /// 找到图片的调用方法
        /// </summary>
        /// <param name="name"></param>
        private bool FindOkFun(string name, MemPic? pic = null)
        {
            if (Biaoji && !BiaoJi_Status && name.Contains("标记"))
            {
                //点击标记位置
                BiaoJi_Status = true;
                log.Info("等待0.2秒，标记位置：5号位");
                Sleep(200);
                Fast.Click("1013,454,1082,551");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 等待秘闻主界面开始界面
        /// </summary>
        /// <returns></returns>
        private bool WaitMainScene()
        {
            var pics = Mp.Filter("秘闻场景");
            if (pics.Wait()) return true;
            return false;
        }
    }
}