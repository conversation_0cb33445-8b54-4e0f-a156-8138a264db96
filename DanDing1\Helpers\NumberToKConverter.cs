﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DanDing1.Helpers
{
    public class NumberToKConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int number)
            {
                // 优先处理万级单位
                if (number >= 10000)
                {
                    double wValue = Math.Round(number / 10000.0, 1);
                    return $"{wValue:0.0}w"; // 例如：15200 → 1.5w
                }
                // 其次处理千级单位
                else if (number >= 1000)
                {
                    double kValue = Math.Round(number / 1000.0, 1);
                    return $"{kValue:0.0}k"; // 例如：1580 → 1.6k
                }
                // 低于1000直接显示
                return number.ToString();
            }
            return string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}