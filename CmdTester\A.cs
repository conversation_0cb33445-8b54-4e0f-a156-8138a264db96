﻿using DamoControlKit;
using DamoControlKit.Function;
using DamoControlKit.Model;
using DamoControlKit.runtimes;
using ScriptEngine;
using ScriptEngine.Helpers;
using ScriptEngine.Model;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using XHelper;

namespace CmdTester
{
    public class A
    {
        private const int HWND = 459470;

        public A()
        {
            Console.WriteLine("TestA COTR Init...");
        }

        /// <summary>
        /// 注册测试
        /// </summary>
        public void a()
        {
            DamoKit.Reg("", out string Str); // OK 2024年5月13日
        }

        /// <summary>
        /// 绑定测试
        /// </summary>
        /// <exception cref="Exception"></exception>
        public dmsoft? b(int _hwnd = 0)
        {
            int hwnd;
            if (_hwnd == 0)
            {
                Console.Write("输入需要绑定窗口的句柄：");
                hwnd = int.Parse(Console.ReadLine() ?? "");
            }
            else
                hwnd = _hwnd;

            dmsoft x;

            //注册大漠插进
            if (!DamoKit.Reg("", out string errorStr))
                throw new Exception("注册失败：" + errorStr);

            try
            {
                x = DamoKit.BindHwnd(new() { Hwnd = hwnd }, out errorStr);//绑定窗口 最简化版本
            }
            catch (Exception e)
            {
                Console.WriteLine("绑定失败" + e.Message);
                return null;
            }

            Console.WriteLine("注册成功，当前插件版本：" + x.Ver());
            Console.WriteLine("当前对象绑定的窗口为：" + x.GetBindWindow());

            //x.UnBindWindow();
            //Console.WriteLine("解绑完毕..");
            return x;
        }

        /// <summary>
        /// Flow_Q_Find_Pic 流程函数实验
        /// </summary>
        /// <exception cref="Exception"></exception>
        public void c()
        {
            dmsoft x = b(HWND) ?? throw new Exception("X对象没有正确返回！");//绑定

            string Base_Path = @"C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\TestData\";
            Flow_Q_Find_Pic FQFP = new();
            FQFP.SetOutTime(5).SetOutTimeGoBack(true)
            .AddPicAndClick(new Pic(Base_Path + "1.bmp").SetParameter(new()
            {
                {"pos","121,474,155,506" },
            }), null)
            .AddPicAndClick(new Pic(Base_Path + "2.bmp").SetParameter(new()
            {
                {"pos","34,94,107,158" },
            }), null)
            .AddPicAndClick(new Pic(Base_Path + "3.bmp").SetParameter(new()
            {
                {"pos","45,327,76,392" },
            }), null)
            .AddPicAndClick(new Pic(Base_Path + "4.bmp").SetParameter(new()
            {
                {"pos","76,13,155,52" },
            }), new("23,15,60,51"))
            .AddPicNotClick(new Pic(Base_Path + "5.bmp").SetParameter(new()
            {
                {"pos","825,39,870,69" },
            }))
            .SetXsoft(x);
            FQFP.Start();
        }

        /// <summary>
        /// Flow_L_Find_Pic 流程函数实验
        /// </summary>
        /// <exception cref="Exception"></exception>
        public void d()
        {
            dmsoft x = b(HWND) ?? throw new Exception("X对象没有正确返回！");//绑定

            string Base_Path = @"C:\Users\<USER>\source\repos\DanDing1SubProject\CmdTester\bin\x86\Debug\net8.0\TestData\";
            Flow_L_Find_Pic FLFP = new();
            FLFP.SetFindYepDelayTime(3000)
            .AddPicAndClick(new Pic(Base_Path + "11.bmp").SetPosition(new("834,433,924,503")), null)
            .AddPicAndClick(new Pic(Base_Path + "22.bmp").SetPosition(new("838,407,926,483")), null)
            .AddPicAndClick(new Pic(Base_Path + "33.bmp").SetPosition(new("409,372,476,425")), null)
            .AddPicAndFun(new Pic(Base_Path + "44.bmp").SetPosition(new("604,489,642,525")), () => { Debug.WriteLine("当前处于阵容解锁状态！[测试]"); })
            .AddExitPic(new Pic(Base_Path + "55.bmp").SetPosition(new("649,276,694,308")))
            .SetXsoft(x);
            FLFP.Start();
        }

        /// <summary>
        /// 本地OCR识别突破中的突破卷
        /// </summary>
        public void e()
        {
            dmsoft x = b(HWND) ?? throw new Exception("X对象没有正确返回！");//绑定
            x.GetScreenDataBmp(734, 15, 834, 42, out int data, out int size);
            byte[] genePic2 = new byte[(int)size];
            IntPtr ptr = new IntPtr((int)data);
            for (int i = 0; i < (int)size; i++)
                genePic2[i] = Marshal.ReadByte(ptr + 1 * i);
            string txt = XOcr.Local_Ocr_Number(genePic2);
        }

        /// <summary>
        /// FindPicMem 测试
        /// </summary>
        public void f()
        {
            dmsoft x = b(HWND) ?? throw new Exception("X对象没有正确返回！");//绑定
            Data_Pics pics = XSerializer.DeserializeJsonFileToObject<Data_Pics>("test/Pics.json", true);//解析json文件
            int ptr, size;
            size = pics.DataPics[0].ImgBytes.Length;
            //将 pics.DataPics[0].ImgBytes 转为 IntPtr
            ptr = Marshal.AllocHGlobal(size).ToInt32();
            Marshal.Copy(pics.DataPics[0].ImgBytes, 0, new IntPtr(ptr), size);

            string picinfo = $"{ptr},{size}";
            x.FindPicMem(1096, 582, 1216, 695, picinfo, "202020", 0.9, 1, out int x1, out int y1);
            Console.WriteLine($"找到坐标：{x1},{y1}");
        }

        /// <summary>
        /// 测试御魂流程
        /// </summary>
        public void g()
        {
            dmsoft x = b(HWND) ?? throw new Exception("X对象没有正确返回！");//绑定
            Data_Pics pics = XSerializer.DeserializeJsonFileToObject<Data_Pics>("test/Pics.json", true);//解析json文件
            Data_Poss poss = XSerializer.DeserializeJsonFileToObject<Data_Poss>("test/Pos.json", true);//解析json文件
            MergeDataJson merge = new(pics, poss);
            MemPics m = merge.GetMemPics();
            m.SetAllXsoft(x);
            Flow_L_Find_MemPic FLFMP = new();
            FLFMP.SetFindYepDelayTime(1000)
            .AddPics(m.Filter("御魂.八岐大蛇"))
            .AddPics(m.Filter("御魂.探索-御魂"))
            .AddPics(m.Filter("御魂.挑战"))
            .AddPics(m.Filter("通用.准备"))
            .AddPicsAndFun(m.Filter("胜利"), () => { XLogger.Info("御魂战斗胜利！"); })
            .AddExitPics(m.Filter("福袋"))
            .SetXsoft(x);
            FLFMP.Start();
        }

        /// <summary>
        /// 正式脚本流程测试
        /// </summary>
        public async void h()
        {
            DmSettings.Init(true);
            XLogger.Init(true);
            //开始配置任务清单
            TaskConfigsModel tcm = new();
            //tcm.AddConfig("Buff", new()
            //{
            //    Name = "Buff",
            //    Others = new()
            //    {
            //        {"御魂", "1" }
            //    }
            //});
            tcm.Add("御魂", 1).Add("御魂", "Level", "11");
            //tcm.AddConfig("Buff", new()
            //{
            //    Name = "Buff",
            //    Others = new()
            //    {
            //        {"御魂", "0" }
            //    }
            //});

            DDBuilder dBuilder = new();
            dBuilder.SetSimulator("mumu", HWND)
                .SetBindSetting(BindModels.GetBindModel("mumu", 1))
                .SetPicsVer("1.0")
                .SetTaskList(tcm)
                .InitLog("游戏1")
                .SetGameSettings(new() { XuanShang = true });

        Recheck:
            //检查句柄
            if (!dBuilder.Check(""))
            {
                XLogger.Info("句柄已经失效，请重新输入：");
                int it = int.Parse(Console.ReadLine() ?? "0");
                dBuilder.SetSimulator("mumu", it);
                goto Recheck;
            }

            DDScript dScript = new()
            {
                TaskEnded_Data = (t) =>
                    {
                        RecordData data = t as RecordData;
                        XLogger.Info($"任务执行数据：点击了{data.MainClick}次,坐标{data.MainPoints.Count}个");
                    }
            };
            dScript.Start(dBuilder);
            //XLogger.Info("等待5s自动结束任务");
            //await Task.Delay(5000);
            //await dScript.Stop();
            XLogger.Debug("流程结束！");
        }

        /// <summary>
        /// 动态图库缓存初始化
        /// </summary>
        public void i()
        {
            DynamicData.Init("1.0");
        }

        /// <summary>
        /// 批量测试大漠注册码是否可以注册
        /// </summary>
        public void j()
        {
            [DllImport(@"/runtimes/DmReg.dll")]
            static extern int SetDllPathA(string path, int mode);

            string path = System.Environment.CurrentDirectory;

            int res = SetDllPathA(path + @"\runtimes\dm.dll", 0);
            if (res != 1)
                throw new Exception("错误原因，插件注册失败：" + res);
            dmsoft dm = new();
            Console.WriteLine("插件注册免注册完成，版本号：" + dm.Ver() + "\r\n");
        ReS:
            int ret_int;
            Console.WriteLine("输入注册码#附加码：");
            string? s = Console.ReadLine();
            if (s == "" || (!s?.Contains('#') ?? true)) goto ReS;
            var vs = s.Split('#');

            ret_int = dm.Reg(vs[0], vs[1]);
            Console.WriteLine("注册返回值：" + ret_int);
            if (ret_int is 1 or 2 or 3)
            {
                Console.WriteLine("注册码可用！加密后的文本为：");
                var s1 = AesEncryption.Encrypt(vs[0], "blog.x-tools.top", "file.x-tools.top");
                var s2 = AesEncryption.Encrypt(vs[1], "blog.x-tools.top", "file.x-tools.top");
                Console.WriteLine(s1 + "#" + s2);
                dm.ReleaseObj();
                j();
                return;
            }
            Console.WriteLine("相关错误原因：" + DamoKit.GetRegError(ret_int));
            dm.ReleaseObj();
            j();
        }
    }
}