/*
 * 文件名: SuperMultiGamesWindowViewModel.GameControls.cs
 * 职责描述: 实现游戏控制相关功能
 * 该文件包含了所有游戏操作控制逻辑，如游戏启动、停止、状态更新和脚本执行等核心功能
 */

using DamoControlKit;
using DamoControlKit.Model;
using DanDing1.Helpers;
using DanDing1.Models.Super;
using Microsoft.Toolkit.Uwp.Notifications;
using ScriptEngine;
using ScriptEngine.Model;
using XHelper;
using DanDing1.Services;
using static ScriptEngine.DDBuilder;
using ScriptEngine.MuMu;
using System.Threading.Tasks;
using System.Linq;
using System.Windows;
using DanDing1.Views.UserControls;
using System.Windows.Threading;
using System.Windows.Controls;
using DanDing1.Services.Notification;
using XHelper.Interface;

namespace DanDing1.ViewModels.Windows
{
    public partial class SuperMultiGamesWindowViewModel : ObservableObject
    {
        /// <summary>
        /// 完成启动任务
        /// </summary>
        /// <param name="game">游戏模型</param>
        private async Task CompleteStartupTasks(SuperMultiGame_DataModel game)
        {
            // 启动运行时间更新任务
            // 首先移除旧的任务（如果存在）
            if (_gameRuntimeTasks.ContainsKey(game.GameId))
            {
                _gameRuntimeTasks.Remove(game.GameId);
            }

            // 创建新的任务
            var task = Task.Run(() => UpdateGameRunningTime(game));
            _gameRuntimeTasks.Add(game.GameId, task);
        }

        /// <summary>
        /// 创建游戏设置模型
        /// </summary>
        /// <param name="game">游戏模型</param>
        /// <param name="loopCount">循环次数</param>
        /// <returns>游戏设置模型</returns>
        private async Task<GameSettingsModel> CreateGameSettingsModel(SuperMultiGame_DataModel game, int loopCount)
        {
            // 异步获取模拟器实例并计算AdbPort
            var mumuInstances = await new MuMu()._GetInstancesAsync();
            int adbPort = Utils.FilterMuMuAdbPort(game.GameHandle, mumuInstances);

            // 从GameData字典中获取IsStartYYSToCourtyard的值，如果不存在则默认为false
            bool isStartYYSToCourtyard = false;
            if (game.GameData.ContainsKey("IsStartYYSToCourtyard") &&
                game.GameData["IsStartYYSToCourtyard"] is bool startToCourtyard)
            {
                isStartYYSToCourtyard = startToCourtyard;
                // 获取值后，将键值设置为false，避免重复使用
                game.GameData["IsStartYYSToCourtyard"] = false;
            }

            return new GameSettingsModel
            {
                AdbPort = adbPort,
                XuanShang = true, // 默认接受悬赏
                LoopCount = loopCount,
                CourtyardSkin = game.TaskConfiguration.Game_Scene,
                IsRecord = false, // 默认不录制
                IsTifu = game.Config.IsTifu, // 单独适配体服运行脚本
                RecordQuality = "原生质量（不压缩）",
                SpeedSwitch = false, // 默认不加速
                IsStartYYSToCourtyard = isStartYYSToCourtyard // 从GameData中获取的值
            };
        }

        /// <summary>
        /// 获取游戏的脚本ID
        /// </summary>
        /// <param name="gameId">游戏ID</param>
        /// <returns>脚本ID</returns>
        /// <remarks>
        /// 该方法确保每个游戏在添加到超级多开列表时就拥有一个唯一的脚本ID。
        /// 如果游戏ID已经有对应的脚本ID，则直接返回；否则创建一个新的脚本ID并与游戏ID关联。
        /// 这样可以确保在启动脚本前就已经分配好了脚本ID，便于其他功能使用。
        /// </remarks>
        private int GetScriptId(int gameId)
        {
            string scriptName = $"SuperMultiGame_{gameId}";

            if (!_gameScriptIds.ContainsKey(gameId))
            {
                // 添加脚本名称并获取ID
                int scriptId = Scripts.AddScriptName(scriptName);
                if (scriptId != -1) // 如果添加成功（返回值不是-1）
                {
                    _gameScriptIds.Add(gameId, scriptId);
                    return scriptId;
                }
            }

            // 如果脚本ID已存在，直接返回
            return _gameScriptIds.TryGetValue(gameId, out int existingScriptId) ? existingScriptId : -1;
        }

        /// <summary>
        /// 处理启动错误
        /// </summary>
        /// <param name="game">游戏模型</param>
        /// <param name="ex">异常</param>
        private async Task HandleStartupError(SuperMultiGame_DataModel game, Exception ex)
        {
            string errorMessage = $"启动游戏 {game.GameName} 的任务失败: {ex.Message}";
            AddLog(errorMessage);
            AddGameLog(game.GameId, errorMessage);

            game.RunningStatus = "未运行";

            NotificationService.Instance.Error($"启动游戏 {game.GameName} 的任务失败: {ex.Message}", "启动失败", 3000);
            Utils.ShowMessage("启动失败", errorMessage);
        }

        /// <summary>
        /// 初始化大漠构建器
        /// </summary>
        /// <param name="game">游戏模型</param>
        /// <returns>初始化后的DDBuilder实例</returns>
        private async Task<DDBuilder> InitializeDDBuilder(SuperMultiGame_DataModel game)
        {
            try
            {
                // 创建并配置DDBuilder
                var dBuilder = new DDBuilder();

                // 创建针对特定游戏的日志委托
                GameLogDelegate gameLogDelegate = (message) =>
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        AddGameLog(game.GameId, message);
                    });
                };

                // 初始化更新游戏当前任务委托
                UpdateGameTaskDelegate OnUpdateGameTask = (taskName) =>
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        var _game = SuperMultiGame_DataModelCollection.FirstOrDefault(g => g.GameId == game.GameId);
                        if (_game != null)
                        {
                            _game.CurrentTask = taskName;
                        }
                    });
                };

                // 初始化更新游戏模拟器句柄的委托
                UpdateGameHwndDelegate OnUpdateGameHwnd = (mumuhwnd, gamehwnd) =>
                {
                    game.GameHandle = gamehwnd;
                    game.MumuHandle = mumuhwnd;

                    // 在UI线程中执行，同步刷新左侧预览界面的句柄
                    Application.Current.Dispatcher.BeginInvoke(() =>
                    {
                        // 获取当前窗口
                        var currentWindow = Application.Current.Windows.OfType<DanDing1.Views.Windows.SuperMultiGamesWindow>().FirstOrDefault();
                        if (currentWindow == null) return;

                        // 获取预览控件
                        var gamePreview = currentWindow.FindName("GamePreview") as DanDing1.Views.UserControls.MultiGamePreviewControl;
                        if (gamePreview == null) return;

                        // 更新游戏句柄信息
                        gamePreview.UpdateGame(game);

                        // 启动所有预览，确保使用最新的句柄
                        gamePreview.StartAllPreviews();
                    }, DispatcherPriority.Background);
                };

                // 在脚本引擎初始化时 创建通知发送器工厂
                var notificationSenderFactory = new NotificationSenderFactory(_config.dNet.User);
                var notificationService = new TaskNotificationService(notificationSenderFactory);
                SendNotificationDelegate sendNotificationDelegate = async (title, content) =>
                {
                    if (!game?.Config?.Notice_IsChecked ?? false) return false;
                    var type = game?.Config?.Notice_SelectItem ?? "邮件";
                    if (game?.Config?.Notice_SelectItem == "自定义" && GlobalData.Instance.UserConfig.Notice_Ntfy)
                        await notificationService.SendSimpleNotificationAsync("App", title, content);
                    if (game?.Config?.Notice_SelectItem == "自定义" && GlobalData.Instance.UserConfig.Notice_WxPush)
                        await notificationService.SendSimpleNotificationAsync("微信推送", title, content);
                    if (game?.Config?.Notice_SelectItem == "自定义" && GlobalData.Instance.UserConfig.Notice_Pushplus)
                        await notificationService.SendSimpleNotificationAsync("Pushplus推送", title, content);
                    if (game?.Config?.Notice_SelectItem == "自定义" && GlobalData.Instance.UserConfig.Notice_Miaotixing)
                        await notificationService.SendSimpleNotificationAsync("喵提醒", title, content);
                    return true;
                };

                // 获取任务配置列表
                var configs = game.TaskConfiguration.GameTaskLists;

                // 默认循环次数为1
                int loopCount = 1;

                // 获取任务配置
                var tcm = await Task.Run(() => Utils.GetTaskConfigs(configs));

                dBuilder.SetDelegates(OnUpdateGameTask, OnUpdateGameHwnd, sendNotificationDelegate)
                    .SetSimulator("mumu", game.GameHandle)
                    .SetBindSetting(BindModels.GetBindModel("mumu", 1))
                    .SetTaskList(tcm)
                    .InitLog(game.GameName, gameLogDelegate)
                    .SetGameSettings(await CreateGameSettingsModel(game, loopCount))
                    .SetUserConfigs(GlobalData.Instance.UserConfig.Pairs)
                    .SetMuMuIndex(int.Parse(game.MumuInstance.Index))
                    .AddData("Base_Url", GlobalData.Instance.appConfig.dNet.System.GetCurrentHost());

                // 初始化图片服务器
                if (!await InitializePicServer(dBuilder, game.GameId))
                    return null;

                AddLog($"初始化大漠构建器成功: {game.GameName}");
                AddGameLog(game.GameId, "初始化大漠构建器成功");

                return dBuilder;
            }
            catch (Exception ex)
            {
                AddLog($"初始化大漠构建器失败: {ex.Message}");
                AddGameLog(game.GameId, $"初始化大漠构建器失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 初始化图片服务器
        /// </summary>
        /// <param name="dBuilder">DDBuilder实例</param>
        /// <returns>初始化是否成功</returns>
        private async Task<bool> InitializePicServer(DDBuilder dBuilder, int GameID)
        {
            var picServer = await Task.Run(() => XConfig.LoadValueFromFile<string>("PicServer") ?? "默认");
            var picVer = GlobalData.Instance.PicServerVer ?? _config.Info.Now_Ver;

            AddGameLog(GameID, $"图库初始化中. 图库源：{picServer}，图库版本：{picVer}");

            if (!await dBuilder.SetPicsVerAsync(picServer, picVer))
            {
                AddGameLog(GameID, "无法开始任务 云端图库初始化失败！请检查设置图库版本是否正确！");
                return false;
            }

            AddGameLog(GameID, "图库初始完成，" + DynamicData.ToString());
            return true;
        }

        /// <summary>
        /// 启动游戏任务
        /// </summary>
        /// <param name="game">游戏模型</param>
        private async Task StartGameTask(SuperMultiGame_DataModel game)
        {
            try
            {
                // 添加计时器
                var startTime = DateTime.Now;

                // 判断任务是否已经运行
                if (game.RunningStatus == "运行中")
                {
                    Utils.ShowMessage("游戏已经在运行，无法重复启动！");
                    AddLog($"游戏 {game.GameName} 已经在运行，请先停止任务！");
                    AddGameLog(game.GameId, "游戏已经在运行，请先停止任务！");
                    return;
                }

                // 设置启动中状态
                game.RunningStatus = "启动中";
                NotificationService.Instance.Info($"启动游戏：{game.GameName}，ID：{game.GameId}，执行任务：{game.TaskConfiguration.GameTaskLists[0].Name}", "正在启动游戏", 3000);

                // 重置运行时长
                game.RunningDuration = TimeSpan.Zero;

                // 自动保存任务列表
                AutoSaveTaskConfigs(game);

                // 检查并确保游戏Config不为空
                EnsureGameConfigExists(game);

                // 验证启动条件
                if (!await ValidateStartConditions(game))
                {
                    game.RunningStatus = "未运行";
                    return;
                }

                NotificationService.Instance.Info($"启动游戏：{game.GameName}，ID：{game.GameId}，开始初始化构建器、最新图库文件...", "正在启动游戏", 1000);
                // 初始化大漠构建器
                var dBuilder = await InitializeDDBuilder(game);
                if (dBuilder == null)
                {
                    game.RunningStatus = "未运行";
                    return;
                }

                NotificationService.Instance.Info($"启动游戏：{game.GameName}，ID：{game.GameId}，开始注册大漠插件，首次时间较长，请耐心等待...", "正在启动游戏", 3000);
                // 验证大漠插件
                if (!await ValidateDamoPlugin(dBuilder))
                {
                    game.RunningStatus = "未运行";
                    return;
                }

                NotificationService.Instance.Info($"启动游戏：{game.GameName}，ID：{game.GameId}，开始启动脚本执行...", "正在启动游戏", 1000);
                // 启动脚本执行
                if (!await StartScriptExecution(game, dBuilder))
                {
                    game.RunningStatus = "未运行";
                    return;
                }

                // 完成启动任务
                await CompleteStartupTasks(game);

                // 在成功启动后，计算并输出耗时
                var timeSpent = DateTime.Now - startTime;
                string timeMessage = $"游戏 {game.GameName} 启动完成，耗时：{timeSpent.TotalSeconds:F2}秒";
                NotificationService.Instance.Info(timeMessage, "启动完成", 3000);
                AddLog(timeMessage);
                AddGameLog(game.GameId, timeMessage);

                string startLogMessage = $"启动游戏：{game.GameName}，ID：{game.GameId}，执行任务：{game.CurrentTask}";
                AddLog(startLogMessage);
                AddGameLog(game.GameId, startLogMessage);
            }
            catch (Exception ex)
            {
                game.RunningStatus = "未运行";
                await HandleStartupError(game, ex);
            }
        }

        /// <summary>
        /// 启动脚本执行
        /// </summary>
        /// <param name="game">游戏模型</param>
        /// <param name="dBuilder">大漠构建器</param>
        /// <returns>启动是否成功</returns>
        private async Task<bool> StartScriptExecution(SuperMultiGame_DataModel game, DDBuilder dBuilder)
        {
            // 获取脚本ID
            int scriptId = GetScriptId(game.GameId);

            // 设置Builder
            Scripts.SetBuilder(scriptId, dBuilder);

            // 设置任务通知消息回调
            Scripts.SetTaskUserNotificationMessage(scriptId, (t) =>
                TaskNotificationMessages = t
            );

            // 设置脚本结束回调
            Scripts.SetScrpitCallBack_Data(scriptId, (data) => TaskEndCallback(game, data));

            // 启动脚本
            string errorStr = "";
            bool startSuccess = await Task.Run(() => Scripts.Start(scriptId, out errorStr));

            if (!startSuccess)
            {
                AddLog($"启动游戏 {game.GameName} 的任务失败: {errorStr}");
                AddGameLog(game.GameId, $"启动任务失败: {errorStr}");
                return false;
            }

            // 更新游戏状态
            game.RunningStatus = "运行中";

            // 更新当前任务显示
            game.CurrentTask = "启动中";

            // 更新正在运行的任务数量
            UpdateRunningTasksCount();

            AddLog($"游戏 {game.GameName} 的任务启动成功");
            AddGameLog(game.GameId, "任务启动成功");

            return true;
        }

        /// <summary>
        /// 停止游戏任务
        /// </summary>
        /// <param name="game">游戏模型</param>
        private async Task StopGameTask(SuperMultiGame_DataModel game)
        {
            int scriptId = GetScriptId(game.GameId);
            if (Scripts.IsRunning(scriptId, out string errorStr))
            {
                await Scripts.StopAsync(scriptId);

                // 更新游戏状态
                game.RunningStatus = "未运行";

                // 清理运行时任务
                if (_gameRuntimeTasks.ContainsKey(game.GameId))
                {
                    // 任务会自行结束，无需强制停止
                    _gameRuntimeTasks.Remove(game.GameId);
                }

                // 更新正在运行的任务数量
                UpdateRunningTasksCount();

                string logMessage = $"停止游戏：{game.GameName}，ID：{game.GameId}";
                NotificationService.Instance.Info(logMessage, "停止游戏", 3000);
                AddLog(logMessage);
                AddGameLog(game.GameId, logMessage);
            }
            else
            {
                // 游戏可能没有正常运行或已经停止
                game.RunningStatus = "未运行";
                NotificationService.Instance.Error($"游戏 {game.GameName} 可能没有正常运行或已经停止: {errorStr}", "停止失败", 3000);
                AddLog($"游戏 {game.GameName} 可能没有正常运行或已经停止: {errorStr}");
                AddGameLog(game.GameId, $"停止任务失败: {errorStr}");
            }
        }

        /// <summary>
        /// 任务结束回调
        /// </summary>
        /// <param name="game">游戏模型</param>
        /// <param name="data">记录数据</param>
        private async void TaskEndCallback(SuperMultiGame_DataModel game, RecordData data)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                // 更新游戏状态
                game.RunningStatus = "未运行";

                // 更新正在运行的任务数量
                UpdateRunningTasksCount();

                // 记录任务完成信息
                string completeMessage = $"任务已完成，点击次数：{data.MainClick + data.SubClick}，执行任务：{string.Join(",", data.TaskNames)}";
                AddLog(completeMessage);
                AddGameLog(game.GameId, completeMessage);

                // 查看是否有详细信息
                if (TaskNotificationMessages.Count > 0)
                {
                    var messages = TaskNotificationMessages;
                    AddGameLog(game.GameId, "任务详情:");

                    foreach (var message in messages)
                    {
                        AddGameLog(game.GameId, $"{message.Key}：{message.Value}");
                    }
                }

                // 发送系统通知
                new ToastContentBuilder()
                    .AddText($"[{game.GameName}] 任务已完成")
                    .AddText($"用时：{game.RunningDuration}")
                    .Show();
            });
            // 发送邮件通知
            if (game.Config is not null && game.Config.Notice_IsChecked && game.RunningDuration.TotalMinutes >= 3)
                await SendNotice(game, data);// 发送邮件通知

            // 如果设置了任务结束后关闭模拟器
            if (game.Config is not null && game.Config.EndCloseGame && game.MumuHandle != 0)
            {
                try
                {
                    // 获取MuMu模拟器实例
                    if (game.MumuInstance != null && !string.IsNullOrEmpty(game.MumuInstance.Index))
                    {
                        int index = int.Parse(game.MumuInstance.Index);
                        var mumu = new MuMu();
                        string path = XConfig.LoadValueFromFile<string>("MuMuPath");
                        if (!string.IsNullOrEmpty(path) && mumu.Init(path))
                        {
                            AddLog($"任务结束，正在关闭游戏 {game.GameName} 的模拟器...");
                            AddGameLog(game.GameId, "任务结束，正在关闭模拟器...");
                            mumu.CloseByIndex(index);

                            // 重置句柄
                            game.MumuHandle = 0;
                            game.GameHandle = 0;

                            AddLog($"游戏 {game.GameName} 的模拟器已关闭");
                            AddGameLog(game.GameId, "模拟器已关闭");
                        }
                        else
                        {
                            AddLog($"无法初始化MuMu模拟器，无法关闭模拟器");
                            AddGameLog(game.GameId, "无法初始化MuMu模拟器，无法关闭模拟器");
                        }
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"关闭模拟器时出错: {ex.Message}");
                    AddGameLog(game.GameId, $"关闭模拟器时出错: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 发送脚本运行汇总给用户
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private async Task SendNotice(SuperMultiGame_DataModel game, RecordData data)
        {
            await Task.Delay(2000);//等待2秒发通知

            try
            {
                // 创建通知发送器工厂
                var senderFactory = new NotificationSenderFactory(_config.dNet.User);
                var notificationService = new TaskNotificationService(senderFactory);

                // 发送任务完成通知
                bool success = await notificationService.SendTaskCompletionNoticeAsync(
                    game.GameName,
                    game.RunningDuration.ToString(),
                    data.MainClick,
                    data.SubClick,
                    data.TaskNames,
                    TaskNotificationMessages,
                    game?.Config.Notice_SelectItem ?? "邮件");

                if (success)
                {
                    XLogger.Info($"[{game.GameName}] 任务结束通知发送成功！");
                }
                else
                {
                    XLogger.Warn($"[{game.GameName}] 任务结束通知发送失败！");
                }
            }
            catch (Exception e)
            {
                XLogger.Warn($"通知发送失败: {e.Message}");
                XLogger.SaveException(e);
            }
        }

        /// <summary>
        /// 更新正在运行的任务数量
        /// </summary>
        private void UpdateRunningTasksCount()
        {
            // 计算当前正在运行的任务数量
            int count = SuperMultiGame_DataModelCollection?.Count(g => g.RunningStatus == "运行中") ?? 0;
            RunningTasksCount = count;

            GlobalData.Instance.SetRunningUI?.Invoke(RunningTasksCount);
        }

        /// <summary>
        /// 获取当前正在运行的任务数量
        /// </summary>
        /// <returns>正在运行的任务数量</returns>
        public int GetRunningTasksCount()
        {
            // 先更新任务数量，确保数据是最新的
            UpdateRunningTasksCount();
            return RunningTasksCount;
        }

        /// <summary>
        /// 更新游戏运行时间
        /// </summary>
        /// <param name="game">游戏模型</param>
        private async void UpdateGameRunningTime(SuperMultiGame_DataModel game)
        {
            // 每次任务开始时都使用新的开始时间
            DateTime startTime = DateTime.Now;

            while (game.RunningStatus == "运行中")
            {
                // 计算从启动时刻开始的运行时间
                TimeSpan currentDuration = DateTime.Now - startTime;

                // 更新UI，只显示时:分:秒三位
                Application.Current.Dispatcher.Invoke(() =>
                {
                    // 创建只包含时:分:秒的TimeSpan
                    TimeSpan formattedDuration = new TimeSpan(
                        currentDuration.Hours,
                        currentDuration.Minutes,
                        currentDuration.Seconds);

                    game.RunningDuration = formattedDuration;
                });

                // 每秒更新一次
                await Task.Delay(1000);
            }
        }

        /// <summary>
        /// 验证大漠插件状态
        /// </summary>
        /// <param name="dBuilder">大漠构建器</param>
        /// <returns>验证是否通过</returns>
        private async Task<bool> ValidateDamoPlugin(DDBuilder dBuilder)
        {
            try
            {
                var dmsoftCode = await _config.dNet.User.GetDmsoftCodeAsync();

                // 验证大漠插件
                if (!await dBuilder.CheckAsync(dmsoftCode))
                {
                    AddLog("大漠插件验证失败，句柄可能已失效");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                AddLog($"验证大漠插件失败: {ex.Message}");
                if (ex.Message.Contains("REGDB_E_CLASSNOTREG") || ex.Message.Contains("没有注册类"))
                {
                    Utils.ShowMessage("错误", "大漠插件未注册，请尝试以管理员身份运行程序");
                    return false;
                }
                return false;
            }
        }

        /// <summary>
        /// 验证启动条件
        /// </summary>
        /// <param name="game">游戏模型</param>
        /// <returns>验证是否通过</returns>
        private async Task<bool> ValidateStartConditions(SuperMultiGame_DataModel game)
        {
            // 检查任务列表是否为空
            if (game.TaskConfiguration == null || game.TaskConfiguration.GameTaskLists.Count == 0)
            {
                string logMessage = $"游戏 {game.GameName} 还没有配置任务，请先配置任务！";
                AddLog(logMessage);
                AddGameLog(game.GameId, logMessage);
                Utils.ShowMessage(logMessage);
                return false;
            }

            // 如果游戏句柄或模拟器句柄无效，尝试自动启动模拟器
            if (game.GameHandle == 0 || game.MumuHandle == 0)
            {
                AddLog($"游戏 {game.GameName} 的模拟器或游戏句柄无效，尝试自动启动模拟器...");
                AddGameLog(game.GameId, "模拟器或游戏句柄无效，尝试自动启动模拟器...");

                // 尝试启动模拟器并等待句柄有效
                bool startSuccess = await TryStartSimulatorAndWaitForHandle(game);
                if (!startSuccess)
                {
                    // 如果自动启动失败，返回false
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 尝试启动模拟器并等待句柄有效
        /// </summary>
        /// <param name="game">游戏模型</param>
        /// <returns>是否成功启动模拟器并获取有效句柄</returns>
        private async Task<bool> TryStartSimulatorAndWaitForHandle(SuperMultiGame_DataModel game)
        {
            // 检查是否有模拟器实例信息
            if (game.MumuInstance == null || string.IsNullOrEmpty(game.MumuInstance.Index))
            {
                string logMessage = $"游戏 {game.GameName} 没有有效的模拟器索引，无法自动启动";
                AddLog(logMessage);
                AddGameLog(game.GameId, logMessage);
                Utils.ShowMessage("启动失败", logMessage);
                return false;
            }

            // 获取MuMu模拟器路径
            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
            if (string.IsNullOrEmpty(path))
            {
                string logMessage = "MuMu模拟器路径未设置，无法启动模拟器";
                AddLog(logMessage);
                AddGameLog(game.GameId, logMessage);
                Utils.ShowMessage("启动失败", logMessage);
                return false;
            }

            try
            {
                // 初始化MuMu实例
                var mumu = new MuMu();
                if (!mumu.Init(path))
                {
                    string logMessage = "MuMu模拟器初始化失败，无法启动模拟器";
                    AddLog(logMessage);
                    AddGameLog(game.GameId, logMessage);
                    Utils.ShowMessage("启动失败", logMessage);
                    return false;
                }

                // 解析模拟器索引
                if (!int.TryParse(game.MumuInstance.Index, out int index))
                {
                    string logMessage = $"无法解析模拟器索引: {game.MumuInstance.Index}";
                    AddLog(logMessage);
                    AddGameLog(game.GameId, logMessage);
                    Utils.ShowMessage("启动失败", logMessage);
                    return false;
                }

                // 使用OpenByIndex方法启动模拟器
                //mumu.OpenByIndex(index);
                var res = await mumu.OpenByIndexAsync(index);

                if (res.success && res.mainWndHandle != 0 && res.renderWndHandle != 0)
                {
                    await Task.Delay(2000);
                    game.MumuHandle = res.mainWndHandle;
                    game.GameHandle = res.renderWndHandle;
                    // 刷新游戏的模拟器数据
                    await RefreshSelectedGameWithResult(game);
                    // 检查句柄是否已有效
                    if (game.MumuHandle != 0 && game.GameHandle != 0)
                    {
                        string successMessage = $"成功获取模拟器和游戏句柄: MumuHandle={game.MumuHandle}, GameHandle={game.GameHandle}";
                        AddLog(successMessage);
                        AddGameLog(game.GameId, successMessage);

                        // 启动模拟器成功后，自动启动阴阳师应用
                        AddLog($"模拟器启动成功，6S后启动阴阳师应用...");
                        AddGameLog(game.GameId, "模拟器启动成功，6S后启动阴阳师应用...");

                        // 等待额外5秒，确保模拟器已完全启动
                        await Task.Delay(6000);

                        // 自动开始同步预览
                        AutoSyncGamePreview(game);

                        // 启动阴阳师应用
                        //await LaunchYysApp(game);
                        mumu.Start_App("阴阳师", mumu.GetAdbPortByIndex(index).port);
                        await Task.Delay(2000);
                        game.GameData["IsStartYYSToCourtyard"] = true;

                        return true;
                    }
                }
                else
                {
                    AddLog($"模拟器启动失败..请重试！");
                    AddGameLog(game.GameId, "模拟器启动失败..请重试！");
                    Utils.ShowMessage("启动失败", "模拟器启动失败..请重试！");
                    return false;
                }
                //string startMessage = $"已发送启动模拟器 {game.GameName} 的命令，索引: {index}";
                //AddLog(startMessage);
                //AddGameLog(game.GameId, startMessage);

                //// 等待模拟器启动并获取有效句柄
                //AddLog($"等待模拟器 {game.GameName} 启动并获取有效句柄...");
                //AddGameLog(game.GameId, "等待模拟器启动并获取有效句柄...");

                // 设置超时时间（最多等待30秒）
                //const int maxWaitTimeSeconds = 30;
                //const int checkIntervalMs = 1000; // 每秒检查一次
                //const int maxAttempts = maxWaitTimeSeconds * 1000 / checkIntervalMs;

                //for (int attempt = 0; attempt < maxAttempts; attempt++)
                //{
                //    // 刷新游戏的模拟器数据
                //    await RefreshSelectedGameWithResult(game);

                //    // 检查句柄是否已有效
                //    if (game.MumuHandle != 0 && game.GameHandle != 0)
                //    {
                //        string successMessage = $"成功获取模拟器和游戏句柄: MumuHandle={game.MumuHandle}, GameHandle={game.GameHandle}";
                //        AddLog(successMessage);
                //        AddGameLog(game.GameId, successMessage);

                //        // 启动模拟器成功后，自动启动阴阳师应用
                //        AddLog($"模拟器启动成功，6S后启动阴阳师应用...");
                //        AddGameLog(game.GameId, "模拟器启动成功，6S后启动阴阳师应用...");

                //        // 等待额外5秒，确保模拟器已完全启动
                //        await Task.Delay(6000);

                //        // 自动开始同步预览
                //        AutoSyncGamePreview(game);

                //        // 启动阴阳师应用
                //        await LaunchYysApp(game);

                //        return true;
                //    }

                //    // 等待一段时间后再次检查
                //    await Task.Delay(checkIntervalMs);

                //    // 每5秒输出一次等待状态
                //    if (attempt % 5 == 0 && attempt > 0)
                //    {
                //        AddLog($"仍在等待模拟器启动，已等待{attempt}秒...");
                //        AddGameLog(game.GameId, $"仍在等待模拟器启动，已等待{attempt}秒...");
                //    }
                //}

                // 超时处理
                string timeoutMessage = $"等待模拟器启动超时，请手动检查模拟器状态";
                AddLog(timeoutMessage);
                AddGameLog(game.GameId, timeoutMessage);
                Utils.ShowMessage("启动超时", timeoutMessage);
                return false;
            }
            catch (Exception ex)
            {
                string errorMessage = $"启动模拟器过程中出错: {ex.Message}";
                AddLog(errorMessage);
                AddGameLog(game.GameId, errorMessage);
                Utils.ShowMessage("启动失败", errorMessage);
                return false;
            }
        }

        /// <summary>
        /// 自动同步游戏预览
        /// </summary>
        /// <param name="game">要同步预览的游戏模型</param>
        /// <remarks>
        /// 如果预览面板中不存在该游戏，会先同步右侧游戏列表，再开始预览
        /// </remarks>
        private void AutoSyncGamePreview(SuperMultiGame_DataModel game)
        {
            try
            {
                // 获取当前窗口
                var currentWindow = Application.Current.Windows.OfType<DanDing1.Views.Windows.SuperMultiGamesWindow>().FirstOrDefault();
                if (currentWindow == null) return;

                // 获取预览控件
                var gamePreview = currentWindow.FindName("GamePreview") as MultiGamePreviewControl;
                if (gamePreview == null) return;

                // 在UI线程中执行预览操作
                currentWindow.Dispatcher.BeginInvoke(new Action(() =>
                {
                    AddLog($"准备自动同步 {game.GameName} 的预览...");

                    // 1. 先尝试直接调用UpdateGame，因为此方法会检查游戏是否存在
                    //    如果存在则更新，如果不存在则添加新的
                    gamePreview.UpdateGame(game);

                    // 2. 由于无法直接调用SyncGameListButton_Click，使用SetGames方法
                    //    将当前所有游戏设置到预览控件中，确保游戏被添加到预览面板
                    if (SuperMultiGame_DataModelCollection != null && SuperMultiGame_DataModelCollection.Count > 0)
                    {
                        gamePreview.SetGames(SuperMultiGame_DataModelCollection);
                    }

                    // 3. 延迟一小段时间，确保UI更新完成
                    currentWindow.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        // 4. 开始所有未同步的预览
                        gamePreview.StartAllPreviews();

                        AddLog($"已自动同步 {game.GameName} 的预览");
                        AddGameLog(game.GameId, "已自动同步预览");
                    }), DispatcherPriority.Render);
                }));
            }
            catch (Exception ex)
            {
                AddLog($"自动同步预览失败: {ex.Message}");
                AddGameLog(game.GameId, $"自动同步预览失败: {ex.Message}");
            }
        }
    }
}