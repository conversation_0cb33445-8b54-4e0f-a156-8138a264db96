﻿#pragma checksum "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "41124D636C749437679B29469BFFBBB11C9DF252"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Views.UserControls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.UserControls {
    
    
    /// <summary>
    /// MultiGamePreviewControl
    /// </summary>
    public partial class MultiGamePreviewControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 32 "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button SyncGameListButton;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button SyncAllButton;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider SizeSlider;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.ToggleSwitch LayoutModeToggle;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer PreviewScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel PreviewPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/usercontrols/multigamepreviewcontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SyncGameListButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 36 "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml"
            this.SyncGameListButton.Click += new System.Windows.RoutedEventHandler(this.SyncGameListButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SyncAllButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 42 "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml"
            this.SyncAllButton.Click += new System.Windows.RoutedEventHandler(this.SyncAllButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SizeSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 68 "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml"
            this.SizeSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.SizeSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.LayoutModeToggle = ((Wpf.Ui.Controls.ToggleSwitch)(target));
            
            #line 81 "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml"
            this.LayoutModeToggle.Checked += new System.Windows.RoutedEventHandler(this.LayoutModeToggle_Changed);
            
            #line default
            #line hidden
            
            #line 82 "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml"
            this.LayoutModeToggle.Unchecked += new System.Windows.RoutedEventHandler(this.LayoutModeToggle_Changed);
            
            #line default
            #line hidden
            return;
            case 5:
            this.PreviewScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            
            #line 88 "..\..\..\..\..\..\Views\UserControls\MultiGamePreviewControl.xaml"
            this.PreviewScrollViewer.ScrollChanged += new System.Windows.Controls.ScrollChangedEventHandler(this.PreviewScrollViewer_ScrollChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.PreviewPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

