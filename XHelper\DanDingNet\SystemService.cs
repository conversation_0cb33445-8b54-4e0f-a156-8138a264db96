using System.Text.Json;
using XHelper.Models;

namespace XHelper.DanDingNet
{
    public class SystemService : BaseService
    {
        public SystemService(HttpClient client, string macCode, string host, string version)
            : base(client, macCode, host, version)
        {
        }

        public async Task<(string AppInfo, string UpData, string NewVer, string AppNewUrl)> GetInfoAsync(string? tempHost = null)
        {
            try
            {
                string host = tempHost ?? _host;
                string? info = await XNet.GetStringAsync(host + DDApi.Api["公告"]);
                if (string.IsNullOrEmpty(info))
                {
                    XLogger.Error("公告响应为空");
                    return ("", "", "", "");
                }

                // 检查响应内容是否为有效的 JSON
                if (!info.StartsWith("{") || !info.EndsWith("}"))
                {
                    XLogger.Error($"公告响应格式异常: {info}");
                    return ("", "", "", "");
                }

                //输出响应
                if (DanDingNet.EnableDebugLog) XLogger.Debug($"公告响应: {info}");

                Response_InfoData? json = JsonSerializer.Deserialize<Response_InfoData>(info, _jsonOptions);
                if (json?.Data == null)
                {
                    XLogger.Error("公告数据解析失败");
                    return ("", "", "", "");
                }

                return (
                    json.Data.Notice ?? "",
                    json.Data.Updata ?? "",
                    json.Data.Ver ?? "",
                    json.Data.Url ?? ""
                );
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取公告异常: {ex.Message}");
                return ("", "", "", "");
            }
        }

        public async Task<string[]> GetInfoBetaAsync()
        {
            if (CheckObjisNull(_client)) return [];

            string? info = await XNet.GetStringAsync(_host + DDApi.Api["获取内测版本"]);
            Response_InfoBetaData? json = JsonSerializer.Deserialize<Response_InfoBetaData>(info, _jsonOptions);
            return [
                json?.Data?.BetaVer ?? "",
                json?.Data?.BetaUrl ?? "",
                json?.Data?.BetaUpdata ?? "",
                json?.Data?.BetaNotice ?? ""
            ];
        }

        public async Task<bool> InitializeAsync(string hostname = "中转线路2")
        {
            if (CheckObjisNull(_client)) return false;

            try
            {
                string host;
                if (Uri.TryCreate(hostname, UriKind.Absolute, out _))
                {
                    // 如果传入的是完整的 URL，直接使用
                    host = hostname;
                }
                else if (!DDApi.ServerHost.TryGetValue(hostname, out string? configuredHost))
                {
                    XLogger.Error($"无效的服务器标识: {hostname}");
                    return false;
                }
                else
                {
                    host = configuredHost;
                }

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"正在尝试连接主机：{host}");
                var message = GetBaseRequest(HttpMethod.Get, host + DDApi.Api["公告"]);
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    _host = host;
                    if (DanDingNet.EnableDebugLog) XLogger.Debug($"成功连接到服务器：{host}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                XLogger.Error($"连接服务器失败: {ex.Message}");
                return false;
            }
        }

        public async Task<List<string>> GetPicVersionsAsync()
        {
            try
            {
                if (CheckObjisNull(_client)) return new List<string>();

                string? response = await XNet.GetStringAsync(_host + DDApi.Api["获取图库版本"]);
                if (string.IsNullOrEmpty(response))
                {
                    XLogger.Error("获取图库版本响应为空");
                    return new List<string>();
                }

                var json = JsonSerializer.Deserialize<Response_PicVersionData>(response, _jsonOptions);
                if (json?.Data?.Versions == null)
                {
                    XLogger.Error("图库版本数据解析失败");
                    return new List<string>();
                }

                return json.Data.Versions.Select(v => v.Version).ToList();
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取图库版本异常: {ex.Message}");
                return new List<string>();
            }
        }

        public async Task<string?> GetPicFileAsync(string version, string fileName)
        {
            try
            {
                if (CheckObjisNull(_client)) return null;

                // 验证文件名是否合法
                if (!new[] { "Dicts.json", "Pics.json", "Pos.json" }.Contains(fileName))
                {
                    XLogger.Error($"不支持的文件名: {fileName}");
                    return null;
                }

                string url = $"{_host}{DDApi.Api["获取图库文件"]}/{version}/{fileName}";
                string? response = await XNet.GetStringAsync(url);

                if (string.IsNullOrEmpty(response))
                {
                    XLogger.Error("获取图库文件响应为空");
                    return null;
                }

                return response;
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取图库文件异常: {ex.Message}");
                return null;
            }
        }
    }
}