using System.Net.Http.Headers;
using System.Text.Json;
using XHelper.Models;

namespace XHelper.DanDingNet
{
    public class AuthService : BaseService
    {
        public AuthService(HttpClient client, string macCode, string host, string version)
            : base(client, macCode, host, version)
        {
        }

        /// <summary>
        /// 获取当前的授权令牌（Bearer Token）
        /// </summary>
        /// <returns>授权令牌，如果不存在则返回空字符串</returns>
        public string GetToken()
        {
            if (CheckObjisNull(_client)) return string.Empty;

            if (_client.DefaultRequestHeaders.Authorization != null)
            {
                string scheme = _client.DefaultRequestHeaders.Authorization.Scheme;
                string parameter = _client.DefaultRequestHeaders.Authorization.Parameter;

                if (scheme == "Bearer" && !string.IsNullOrEmpty(parameter))
                {
                    return parameter;
                }
            }

            return string.Empty;
        }

        public async Task<Response_LoginData?> LoginAsync(string username, string password)
        {
            if (CheckObjisNull(_client)) return null;

            var data = new Request_LoginData
            {
                username = username,
                password = password,
                computer_Code = _macCode
            };

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["登录"]);
            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();

            if (response.Headers.TryGetValues("Set-Cookie", out var cookies))
            {
                XNet.SaveCookiesToFile(cookies, ".\\runtimes\\cookies.data");
                // 清除已有的Cookie头，防止过期cookie残留
                if (_client.DefaultRequestHeaders.Contains("Cookie"))
                {
                    _client.DefaultRequestHeaders.Remove("Cookie");
                }
                foreach (var cookie in cookies)
                {
                    _client.DefaultRequestHeaders.Add("Cookie", cookie);
                    if (cookie.Contains("X-token"))
                        _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", cookie.Split("Bearer ")[1]);
                }
            }

            return JsonSerializer.Deserialize<Response_LoginData>(body, _jsonOptions);
        }

        public async Task<ResponseBaseData?> RegisterAsync(string username, string password, string kami, string email, string code)
        {
            if (CheckObjisNull(_client)) return null;

            var data = new Request_RegisterData
            {
                username = username,
                password = password,
                kami = kami,
                user_email = email,
                code = code,
                computer_Code = _macCode
            };

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["注册"]);
            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<Response_RegData>(body, _jsonOptions);
        }

        public async Task<bool> SendEmailAsync(string email, string username)
        {
            if (CheckObjisNull(_client)) return false;

            var data = new
            {
                email,
                username
            };

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["发送验证码"]);
            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();

            ResponseBaseData? obj = JsonSerializer.Deserialize<ResponseBaseData>(body, _jsonOptions);
            if (obj?.IsSuccess == false) throw new Exception(obj?.Message ?? "发件[未知错误]");
            return obj?.IsSuccess ?? false;
        }

        public async Task<bool> OutLoginAsync(string username)
        {
            if (CheckObjisNull(_client)) return false;

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["退出登录"]);
            var data = new
            {
                username,
                computer_Code = _macCode
            };

            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            ResponseBaseData? obj = JsonSerializer.Deserialize<ResponseBaseData>(body, _jsonOptions);

            if (obj?.IsSuccess ?? false)
            {
                _client.DefaultRequestHeaders.Clear();
                if (File.Exists(".\\runtimes\\cookies.data"))
                    File.Delete(".\\runtimes\\cookies.data");
            }

            return obj?.IsSuccess ?? false;
        }

        public async Task<bool> CheckLoginStatusAsync()
        {
            if (CheckObjisNull(_client)) return false;

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["检测账号状态"]);

            bool hasCookies = _client.DefaultRequestHeaders.Contains("Cookie");
            if (!hasCookies)
            {
                var cookies = XNet.LoadCookiesFromFile(".\\runtimes\\cookies.data");
                if (!cookies.Any())
                    return false;

                // 清除已有的Cookie头，防止过期cookie残留
                if (_client.DefaultRequestHeaders.Contains("Cookie"))
                {
                    _client.DefaultRequestHeaders.Remove("Cookie");
                }

                foreach (var cookie in cookies)
                {
                    _client.DefaultRequestHeaders.Add("Cookie", cookie);
                    if (cookie.Contains("X-token"))
                    {
                        _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", cookie.Split("Bearer ")[1]);
                        message.Headers.Authorization = _client.DefaultRequestHeaders.Authorization;
                    }
                }
            }

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                ResponseBaseData? obj = JsonSerializer.Deserialize<ResponseBaseData>(body, _jsonOptions);
                if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    if (obj.detail.Contains("无效的凭证") || obj.detail.Contains("过期的凭证"))
                    {
                        isReLoadUser = true;
                        // 保存详细错误信息
                        LastErrorDetail = obj.detail;
                    }
                }

                if (DanDingNet.EnableDebugLog)
                    XLogger.Debug("CheckLoginStatusAsync：" + obj?.ToString());

                if (!obj?.IsSuccess ?? false)
                {
                    if (File.Exists(".\\runtimes\\cookies.data"))
                        File.Delete(".\\runtimes\\cookies.data");
                    _client.DefaultRequestHeaders.Clear();
                }

                return obj?.IsSuccess ?? false;
            }
            catch (HttpRequestException ex) when (ex.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                //XLogger.Error("Unauthorized access - 401");
                //删除 cookies.data 文件
                if (File.Exists(".\\runtimes\\cookies.data"))
                    File.Delete(".\\runtimes\\cookies.data");
                //清除请求头
                _client.DefaultRequestHeaders.Clear();
                // 设置为可以重新登录
                isReLoadUser = true;
                // 保存错误信息
                LastErrorDetail = $"Unauthorized access - 401: {ex.Message}";
                return false;
            }
        }

        private bool IsReLoadUser = false;

        /// <summary>
        /// 是否可以尝试重新登录
        /// </summary>
        public bool isReLoadUser
        {
            get
            {
                if (IsReLoadUser)
                {
                    IsReLoadUser = false;
                    return true;
                }
                return IsReLoadUser;
            }
            set { IsReLoadUser = value; }
        }

        /// <summary>
        /// 获取最后一次登录验证的详细错误信息
        /// </summary>
        public string? LastErrorDetail { get; private set; }

        public async Task<Response_ResetPwdData?> ResetPasswordAsync(string email, string username, string code)
        {
            if (CheckObjisNull(_client)) return null;

            var data = new
            {
                email,
                username,
                code
            };

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["重置密码"]);
            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<Response_ResetPwdData>(body, _jsonOptions);
        }

        public async Task<ResponseBaseData?> ChangePasswordAsync(string username, string currentPassword, string newPassword)
        {
            if (CheckObjisNull(_client)) return null;

            var data = new
            {
                username,
                current_pwd = currentPassword,
                new_pwd = newPassword,
                computer_Code = _macCode
            };

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["修改密码"]);
            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<ResponseBaseData>(body, _jsonOptions);
        }
    }
}