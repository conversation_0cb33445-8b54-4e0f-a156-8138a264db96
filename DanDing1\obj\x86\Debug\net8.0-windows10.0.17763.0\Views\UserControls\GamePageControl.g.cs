﻿#pragma checksum "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "730C409ECC9B4828ACDEDBB1259085A8BB22146E"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Helpers;
using DanDing1.Resources;
using DanDing1.ViewModels.Pages;
using DanDing1.Views.UserControls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.UserControls {
    
    
    /// <summary>
    /// GamePageControl
    /// </summary>
    public partial class GamePageControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 25 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GameModelName;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DanDing1.Views.UserControls.AddTaskControl AddTask;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.ListView GameTaskLists;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button TasksManager;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox OverNotice;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox OverNotice_Type;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.Button AutoBindButton;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Wpf.Ui.Controls.InfoBar InfoBar;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/usercontrols/gamepagecontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.GameModelName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.AddTask = ((DanDing1.Views.UserControls.AddTaskControl)(target));
            return;
            case 3:
            this.GameTaskLists = ((Wpf.Ui.Controls.ListView)(target));
            return;
            case 4:
            this.TasksManager = ((Wpf.Ui.Controls.Button)(target));
            
            #line 103 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
            this.TasksManager.MouseRightButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.TasksManager_MouseRightButtonUp);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 131 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
            ((System.Windows.Controls.CheckBox)(target)).Checked += new System.Windows.RoutedEventHandler(this.OverNotice_Unchecked);
            
            #line default
            #line hidden
            
            #line 136 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
            ((System.Windows.Controls.CheckBox)(target)).Unchecked += new System.Windows.RoutedEventHandler(this.OverNotice_Unchecked);
            
            #line default
            #line hidden
            return;
            case 6:
            this.OverNotice = ((System.Windows.Controls.CheckBox)(target));
            
            #line 142 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
            this.OverNotice.Checked += new System.Windows.RoutedEventHandler(this.OverNotice_Unchecked);
            
            #line default
            #line hidden
            
            #line 145 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
            this.OverNotice.Unchecked += new System.Windows.RoutedEventHandler(this.OverNotice_Unchecked);
            
            #line default
            #line hidden
            return;
            case 7:
            this.OverNotice_Type = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            
            #line 169 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
            ((Wpf.Ui.Controls.Button)(target)).MouseRightButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.TasksManager_MouseRightButtonUp);
            
            #line default
            #line hidden
            return;
            case 9:
            this.AutoBindButton = ((Wpf.Ui.Controls.Button)(target));
            
            #line 212 "..\..\..\..\..\..\Views\UserControls\GamePageControl.xaml"
            this.AutoBindButton.Click += new System.Windows.RoutedEventHandler(this.AutoBindButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.InfoBar = ((Wpf.Ui.Controls.InfoBar)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

