﻿using DanDing1.Helpers;
using DanDing1.Models;
using DanDing1.ViewModels.Services;
using DanDing1.ViewModels.Windows;
using DanDing1.Views.UserControls;
using DanDing1.Views.Windows;
using ScriptEngine.Model;
using System.Collections.ObjectModel;
using System.Text.Json;
using System.Text.Json.Serialization;
using XHelper;
using Newtonsoft.Json;

namespace DanDing1.ViewModels.Pages
{
    public delegate Task FastStartClicked(string gameName);

    public delegate void GameTaskListsChanged(ObservableCollection<TaskConfigsModel.Configs> lists);

    public partial class AddTaskPropertyViewModel : ObservableObject
    {
        /// <summary>
        /// 任务列表
        /// </summary>
        [ObservableProperty]
        public ObservableCollection<TaskConfigsModel.Configs> _GameTaskLists = [];

        /// <summary>
        /// 快速启动按钮点击事件
        /// </summary>
        public FastStartClicked? OnFastStartClicked_ToViewModel;

        /// <summary>
        /// 游戏任务列表变化事件
        /// </summary>
        public GameTaskListsChanged? OnGameTaskListsChanged_ToViewModel;

        /// <summary>
        /// 选中的句柄
        /// </summary>
        public int SelectHwnd = 0;

        /// <summary>
        /// 服务提供者
        /// </summary>
        private readonly IServiceProvider? _serviceProvider;

        /// <summary>
        /// 任务服务
        /// </summary>
        private readonly TaskService? _taskService;

        /// <summary>
        /// 日常_地域鬼王开关
        /// </summary>
        [ObservableProperty]
        private bool _DailyTask_DiGui = true;

        /// <summary>
        /// 日常_免费一抽开关
        /// </summary>
        [ObservableProperty]
        private bool _DailyTask_ChouKa = false;

        /// <summary>
        /// 日常_友情点领取
        /// </summary>
        [ObservableProperty]
        private bool _DailyTask_Befriendly = true;

        /// <summary>
        /// 日常_做悬赏
        /// </summary>
        [ObservableProperty]
        private bool _DailyTask_Reward = true;

        /// <summary>
        /// 日常_每日免费礼包
        /// </summary>
        [ObservableProperty]
        private bool _DailyTask_FreeGift = true;

        /// <summary>
        /// 日常_每日签到
        /// </summary>
        [ObservableProperty]
        private bool _DailyTask_Checkin = true;

        /// <summary>
        /// 日常_寮材料提交
        /// </summary>
        [ObservableProperty]
        private bool _DailyTask_Liao30SE = false;

        /// <summary>
        /// 等待任务时长
        /// </summary>
        [ObservableProperty]
        private string _Delay_Time = "3";

        /// <summary>
        /// 斗技标记开关
        /// </summary>
        [ObservableProperty]
        private string _Dji_Biaoji;

        /// <summary>
        /// 斗技标记开关
        /// </summary>
        [ObservableProperty]
        private List<string> _Dji_Biaojis = ["不标记", "位置1", "位置2", "位置3", "位置4", "位置5"];

        /// <summary>
        /// 斗技胜利次数
        /// </summary>
        [ObservableProperty]
        private string _Dji_Count = "15";

        /// <summary>
        /// 斗技积分满停开关
        /// </summary>
        [ObservableProperty]
        private bool _Dji_ManTing;

        /// <summary>
        /// 斗技积分满3000就停止
        /// </summary>
        [ObservableProperty]
        private bool _Dji_MingShi;

        /// <summary>
        /// 斗技预设切换
        /// </summary>
        [ObservableProperty]
        private string _Dji_YuShe;

        /// <summary>
        /// 庭院皮肤
        /// </summary>
        [ObservableProperty]
        private string _Game_Scene;

        /// <summary>
        /// 庭院皮肤列表
        /// </summary>
        [ObservableProperty]
        private List<string> _Game_SceneLists = ["默认", "枫色秋庭", "织梦莲庭", "笔墨山河", "缘结之庭", "冶武兵道", "琼月华宫", "鏖刀禁府"];

        /// <summary>
        /// 任务列表索引
        /// </summary>
        [ObservableProperty]
        private int _GameTaskListsIndex;

        /// <summary>
        /// 活动标记
        /// </summary>
        [ObservableProperty]
        private bool _HDong_Biaoji = false;

        /// <summary>
        /// 活动次数
        /// </summary>
        [ObservableProperty]
        private string _HDong_Count = "20";

        /// <summary>
        /// 活动模式
        /// </summary>
        [ObservableProperty]
        private bool _HDong_Fake_Mode = false;

        /// <summary>
        /// 活动提前退出时间
        /// </summary>
        [ObservableProperty]
        private string _HDong_Fake_Time = "0";

        /// <summary>
        /// 活动判断条件提前结束
        /// </summary>
        [ObservableProperty]
        private bool _HDong_FirstEnd = false;

        /// <summary>
        /// 活动类型列表
        /// </summary>
        [ObservableProperty]
        private List<string> _HDong_ClassLists = ["日轮之影(爬塔Ⅰ)", "阵练演武(爬塔Ⅱ)", "时间裂缝(退治)"];

        /// <summary>
        /// 活动类型
        /// </summary>
        [ObservableProperty]
        private string _HDong_Class = "日轮之影(爬塔Ⅰ)";

        /// <summary>
        /// 活动预设
        /// </summary>
        [ObservableProperty]
        private string _HDong_YuShe;

        /// <summary>
        /// 日常预设
        /// </summary>
        [ObservableProperty]
        private string _RChang_YuShe;

        /// <summary>
        /// 快捷通知
        /// </summary>
        [ObservableProperty]
        private InfoBarModel _infoBar = new();

        /// <summary>
        /// 录制开关
        /// </summary>
        [ObservableProperty]
        private bool _isRecord = false;

        /// <summary>
        /// 是否为超级多开模式
        /// </summary>
        [ObservableProperty]
        private string _IsSuperMode = "True";

        /// <summary>
        /// 觉醒自动启停Buff
        /// </summary>
        [ObservableProperty]
        private bool _Jxing_AutoBuff = true;

        /// <summary>
        /// 觉醒层数
        /// </summary>
        [ObservableProperty]
        private string _Jxing_Class;

        /// <summary>
        /// 觉醒层数列表
        /// </summary>
        [ObservableProperty]
        private List<string> _Jxing_ClassLists = ["雷", "火", "风", "水"];

        /// <summary>
        /// 觉醒次数
        /// </summary>
        [ObservableProperty]
        private string _Jxing_Count = "10";

        /// <summary>
        /// 半自动标记
        /// </summary>
        [ObservableProperty]
        private bool _Bzidong_Biaoji = false;

        /// <summary>
        /// 半自动定时结束时间（分钟）
        /// </summary>
        [ObservableProperty]
        private string _Bzidong_AutoEndTime = "5";

        /// <summary>
        /// 六道_标记开关
        /// </summary>
        [ObservableProperty]
        private bool _Liudao_Biaoji;

        /// <summary>
        /// 六道_次数
        /// </summary>
        [ObservableProperty]
        private string _Liudao_Count = "1";

        /// <summary>
        /// 六道_额外技能 细雨化屏
        /// </summary>
        [ObservableProperty]
        private bool _Liudao_Extra_Skill_XY = false;

        /// <summary>
        /// 六道_额外技能 妖力化生
        /// </summary>
        [ObservableProperty]
        private bool _Liudao_Extra_Skill_YL = false;

        /// <summary>
        /// 六道_混沌不开宝箱
        /// </summary>
        [ObservableProperty]
        private bool _Liudao_HunDunBuKaiXiang = true;

        /// <summary>
        /// 六道_使用双倍加成(极时)
        /// </summary>
        [ObservableProperty]
        private bool _Liudao_ShuangBeiJiaCheng = true;

        /// <summary>
        /// 六道_双倍加成不足时购买加成
        /// </summary>
        [ObservableProperty]
        private bool _Liudao_ShuangBeiJiaCheng_Buy = false;

        /// <summary>
        /// 六道_星之屿打达摩
        /// </summary>
        [ObservableProperty]
        private bool _Liudao_XingZhiDao_Damo = true;

        /// <summary>
        /// 六道_预设
        /// </summary>
        [ObservableProperty]
        private string _Liudao_YuShe;

        /// <summary>
        /// 任务列表循环次数
        /// </summary>
        [ObservableProperty]
        private string _LoopCount = "1";

        /// <summary>
        /// 寮突破次数
        /// </summary>
        [ObservableProperty]
        private string _LTpo_Count = "6";

        /// <summary>
        /// 每周秘闻开关
        /// </summary>
        [ObservableProperty]
        private bool _MeiZhouMiWen_Biaoji;

        /// <summary>
        /// 契灵_是否自动购买召唤次数
        /// </summary>
        [ObservableProperty]
        private bool _Qiling_AutoBuySummon = true;

        /// <summary>
        /// 契灵_标记开关
        /// </summary>
        [ObservableProperty]
        private bool _Qiling_Biaoji;

        /// <summary>
        /// 契灵_次数
        /// </summary>
        [ObservableProperty]
        private string _Qiling_Count = "1";

        /// <summary>
        /// 契灵_任务类型
        /// </summary>
        [ObservableProperty]
        private string _Qiling_TaskType;

        /// <summary>
        /// 契灵_任务类型列表
        /// </summary>
        [ObservableProperty]
        private List<string> _Qiling_TaskTypeLists = ["镇墓兽", "探查"];

        /// <summary>
        /// 契灵_预设
        /// </summary>
        [ObservableProperty]
        private string _Qiling_YuShe;

        /// <summary>
        /// 其它任务中标记总开关
        /// </summary>
        [ObservableProperty]
        private bool _Qta_Biaoji;

        /// <summary>
        /// 其它任务中预设
        /// </summary>
        [ObservableProperty]
        private string _Qta_YuShe;

        /// <summary>
        /// 日轮次数
        /// </summary>
        [ObservableProperty]
        private string _Rlun_Count = "10";

        /// <summary>
        /// 快速启动按钮是否启用
        /// </summary>
        [ObservableProperty]
        private bool _StartButtonEnabled = true;

        /// <summary>
        /// 定时_寄养开关
        /// </summary>
        [ObservableProperty]
        private bool _Timer_JiYang = true;

        /// <summary>
        /// 定时_离线客户端功能
        /// </summary>
        [ObservableProperty]
        private bool _Timer_OffLine = false;

        /// <summary>
        /// 定时_寄养忽略低等级
        /// </summary>
        [ObservableProperty]
        private bool _Timer_JiYang_PassLowLevel = true;

        /// <summary>
        /// 定时_寄养优先级
        /// </summary>
        [ObservableProperty]
        private string _Timer_JiYang_Priority = "先太鼓-后斗鱼";

        /// <summary>
        /// 定时_寄养优先级列表
        /// </summary>
        [ObservableProperty]
        private List<string> _Timer_JiYang_PriorityLists = ["先太鼓-后斗鱼", "先斗鱼-后太鼓", "只寄养太鼓", "只寄养斗鱼"];

        /// <summary>
        /// 定时_寄养指定好友寄养策略s
        /// </summary>
        [ObservableProperty]
        private List<string> _Timer_JiYang_DesignatedTacticsLists = ["找不到等待10分钟再试", "找不到选其它合适坑位"];

        /// <summary>
        /// 定时_寄养指定好友寄养策略
        /// </summary>
        [ObservableProperty]
        private string _Timer_JiYang_DesignatedTactics = "找不到选其它合适坑位";

        /// <summary>
        /// 定时_寄养指定好友寄养选中名字
        /// </summary>
        [ObservableProperty]
        private string _Timer_JiYang_DesignatedName = "";

        /// <summary>
        /// 放卡_开关
        /// </summary>
        [ObservableProperty]
        private bool _Timer_FangKa = false;

        /// <summary>
        /// 放卡_类型
        /// </summary>
        [ObservableProperty]
        private string _Timer_FangKa_Class = "太鼓";

        /// <summary>
        /// 放卡_类型列表
        /// </summary>
        [ObservableProperty]
        private List<string> _Timer_FangKa_ClassLists = ["太鼓", "斗鱼"];

        /// <summary>
        /// 放卡_等级
        /// </summary>
        [ObservableProperty]
        private string _Timer_FangKa_Level = "≤6x";

        /// <summary>
        /// 放卡_最大滑动次数
        /// </summary>
        [ObservableProperty]
        private string _Timer_FangKa_HuaDongCount = "5";

        /// <summary>
        /// 放卡_等级列表
        /// </summary>
        [ObservableProperty]
        private List<string> _Timer_FangKa_LevelLists = ["≤6x", "≤5x", "4x"];

        /// <summary>
        /// 突破标记开关
        /// </summary>
        [ObservableProperty]
        private string _Tpo_Biaoji;

        /// <summary>
        /// 突破标记开关
        /// </summary>
        [ObservableProperty]
        private List<string> _Tpo_Biaojis = ["不标记", "位置1", "位置2", "位置3", "位置4", "位置5"];

        /// <summary>
        /// 突破次数
        /// </summary>
        [ObservableProperty]
        private string _Tpo_Count = "10";

        /// <summary>
        /// 突破打完是否打寮突开关
        /// </summary>
        [ObservableProperty]
        private bool _Tpo_RunLtu;

        /// <summary>
        /// 突破卡级开关
        /// </summary>
        [ObservableProperty]
        private bool _Tpo_Tui4 = true;

        /// <summary>
        /// 突破刷新前确认
        /// </summary>
        [ObservableProperty]
        private bool _Tpo_Affirm = false;

        /// <summary>
        /// 突破预设
        /// </summary>
        [ObservableProperty]
        private string _Tpo_YuShe;

        /// <summary>
        /// 探索自动启停Buff
        /// </summary>
        [ObservableProperty]
        private bool _Tsuo_AutoBuff = true;

        /// <summary>
        /// 探索战斗目标
        /// </summary>
        [ObservableProperty]
        private string _Tsuo_CombatStr;

        /// <summary>
        /// 探索战斗目标列表
        /// </summary>
        [ObservableProperty]
        private List<string> _Tsuo_CombatStrLists = ["全都打", "达摩", "经验", "金币"];

        /// <summary>
        /// 探索次数
        /// </summary>
        [ObservableProperty]
        private string _Tsuo_Count = "50";

        /// <summary>
        /// 探索任务结束计数模式
        /// True:仅已Boss计数
        /// False:Boss+小怪计数
        /// </summary>
        [ObservableProperty]
        private bool _Tsuo_Counting_Mode = false;

        /// <summary>
        /// 探索预设
        /// </summary>
        [ObservableProperty]
        private string _TSuo_YuShe;

        /// <summary>
        /// 探索组队开关
        /// </summary>
        [ObservableProperty]
        private bool _TSuo_ZuDui_IsChecked;

        /// <summary>
        /// 探索组队身份
        /// </summary>
        [ObservableProperty]
        private string _TSuo_ZuDui_Location;

        /// <summary>
        /// 探索组队身份列表
        /// </summary>
        [ObservableProperty]
        private List<string> _TSuo_ZuDui_Locations = ["队长", "队长-手动邀请", "队员"];

        /// <summary>
        /// 探索组队对象名
        /// </summary>
        [ObservableProperty]
        private string _TSuo_ZuDui_Name;

        /// <summary>
        /// 悬赏开关
        /// </summary>
        [ObservableProperty]
        private bool _XShangChecked;

        /// <summary>
        /// 御魂自动启停Buff
        /// </summary>
        [ObservableProperty]
        private bool _Yhun_AutoBuff = true;

        /// <summary>
        /// 御魂标记开关
        /// </summary>
        [ObservableProperty]
        private bool _Yhun_Biaoji;

        /// <summary>
        /// 御魂次数
        /// </summary>
        [ObservableProperty]
        private string _Yhun_Count = "5";

        /// <summary>
        /// 御魂层数
        /// </summary>
        [ObservableProperty]
        private string _Yhun_Level;

        /// <summary>
        /// 御魂层数列表
        /// </summary>
        [ObservableProperty]
        private List<string> _Yhun_LevelLists = ["不选层", "12", "11", "10", "9", "8", "7", "6", "5", "4", "3", "2", "1"];

        /// <summary>
        /// 御魂预设
        /// </summary>
        [ObservableProperty]
        private string _Yhun_YuShe;

        /// <summary>
        /// 御魂组队开关
        /// </summary>
        [ObservableProperty]
        private bool _Yhun_ZuDui_IsChecked;

        /// <summary>
        /// 御魂组队身份
        /// </summary>
        [ObservableProperty]
        private string _Yhun_ZuDui_Location;

        /// <summary>
        /// 御魂组队身份列表
        /// </summary>
        [ObservableProperty]
        private List<string> _Yhun_ZuDui_Locations = ["队长", "队长-手动邀请", "队员"];

        /// <summary>
        /// 御魂组队对象名
        /// </summary>
        [ObservableProperty]
        private string _Yhun_ZuDui_Name;

        /// <summary>
        /// 御灵标记开关
        /// </summary>
        [ObservableProperty]
        private bool _Yling_Biaoji;

        /// <summary>
        /// 御灵层数
        /// </summary>
        [ObservableProperty]
        private string _Yling_Class;

        /// <summary>
        /// 御灵层数列表
        /// </summary>
        [ObservableProperty]
        private List<string> _Yling_ClassLists = ["智能选择", "暗神龙", "暗白藏主", "暗黑豹", "暗孔雀"];

        /// <summary>
        /// 御灵次数
        /// </summary>
        [ObservableProperty]
        private string _Yling_Count = "10";

        /// <summary>
        /// 御灵预设
        /// </summary>
        [ObservableProperty]
        private string _Yling_YuShe;

        //===========英杰============
        /// <summary>
        /// 英杰标记开关
        /// </summary>
        [ObservableProperty]
        private bool _Yjie_Biaoji;

        /// <summary>
        /// 英杰层数
        /// </summary>
        [ObservableProperty]
        private string _Yjie_Class;

        /// <summary>
        /// 英杰功能列表
        /// </summary>
        [ObservableProperty]
        private List<string> _Yjie_ClassLists = ["鬼兵演武", "兵藏秘境"];

        /// <summary>
        /// 英杰次数
        /// </summary>
        [ObservableProperty]
        private string _Yjie_Count = "10";

        /// <summary>
        /// 英杰预设
        /// </summary>
        [ObservableProperty]
        private string _Yjie_YuShe;

        //===========英杰============

        /// <summary>
        /// 百鬼次数
        /// </summary>
        [ObservableProperty]
        private string _Bgui_Count = "10";

        /// <summary>
        /// 百鬼邀请队友
        /// </summary>
        [ObservableProperty]
        private bool _Bgui_Inv = true;

        /// <summary>
        /// 永生次数
        /// </summary>
        [ObservableProperty]
        private string _Ysheng_Count = "10";

        /// <summary>
        /// 业原火次数
        /// </summary>
        [ObservableProperty]
        private string _Yyhuo_Count = "10";

        private Dictionary<string, string> Tips = new(){
                                                            {"寄养"
                                                            ,"1、卷轴皮肤、结界皮肤以及鲤鱼旗皮肤需要设置为默认；游戏需要开启高清字体；\n" +
                                                            "2、关闭闲庭功能；\n" +
                                                            "3、开始任务前，脚本将对当前游戏的寄养时间进行检查，并自动设置定时寄养；\n" +
                                                            "4、寄养任务默认按收益最高进行寄养；" },
                                                            {"日常"
                                                            ,"1、卷轴皮肤、结界皮肤以及鲤鱼旗皮肤需要设置为默认；游戏需要开启高清字体；\n" +
                                                            "2、地域鬼王：优先打收藏中的鬼王，其次再是热门中的鬼王；"}
                                                            };

        public AddTaskPropertyViewModel(IServiceProvider? serviceProvider = null)
        {
            _serviceProvider = serviceProvider ?? (Application.Current.Resources["ServiceProvider"] as IServiceProvider);
            _taskService = new TaskService(_infoBar);
        }

        /// <summary>
        /// 设置显示模式
        /// 超级多开
        /// 普通
        /// </summary>
        public void SetMode(string Mode, Action? action = null)
        {
            switch (Mode)
            {
                case "超级多开":
                    IsSuperMode = "True";
                    action?.Invoke();
                    break;

                case "普通":
                default:
                    IsSuperMode = "False";
                    action?.Invoke();
                    break;

                case "定时调度":
                    IsSuperMode = "True";
                    action?.Invoke();
                    break;
            }
        }

        /// <summary>
        /// 添加任务命令
        /// </summary>
        [RelayCommand]
        private async Task Addask(string taskType)
        {
            await _taskService.OnAddTask(taskType, this);
            OnPropertyChanged(nameof(GameTaskLists));
            OnGameTaskListsChanged_ToViewModel?.Invoke(GameTaskLists);
        }

        /// <summary>
        /// 清空所有任务命令
        /// </summary>
        [RelayCommand]
        private void DelAllTask()
        {
            // 实现清空所有任务的逻辑
            GameTaskLists.Clear();
            OnPropertyChanged(nameof(GameTaskLists));
            OnGameTaskListsChanged_ToViewModel?.Invoke(GameTaskLists);
        }

        /// <summary>
        /// 删除任务命令
        /// </summary>
        [RelayCommand]
        private void DelTask(int index)
        {
            // 实现删除任务的逻辑
            if (index >= 0 && index < GameTaskLists.Count)
            {
                GameTaskLists.RemoveAt(index);
                OnPropertyChanged(nameof(GameTaskLists));
                OnGameTaskListsChanged_ToViewModel?.Invoke(GameTaskLists);
            }
        }

        /// <summary>
        /// 下移任务命令
        /// </summary>
        [RelayCommand]
        private void MoveDown(int index)
        {
            // 实现下移任务的逻辑
            if (index >= 0 && index < GameTaskLists.Count - 1)
            {
                var item = GameTaskLists[index];
                GameTaskLists.RemoveAt(index);
                GameTaskLists.Insert(index + 1, item);
                GameTaskListsIndex = index + 1;
                OnPropertyChanged(nameof(GameTaskLists));
                OnGameTaskListsChanged_ToViewModel?.Invoke(GameTaskLists);
            }
        }

        /// <summary>
        /// 上移任务命令
        /// </summary>
        [RelayCommand]
        private void MoveUp(int index)
        {
            // 实现上移任务的逻辑
            if (index > 0 && index < GameTaskLists.Count)
            {
                var item = GameTaskLists[index];
                GameTaskLists.RemoveAt(index);
                GameTaskLists.Insert(index - 1, item);
                GameTaskListsIndex = index - 1;
                OnPropertyChanged(nameof(GameTaskLists));

                OnGameTaskListsChanged_ToViewModel?.Invoke(GameTaskLists);
            }
        }

        /// <summary>
        /// 快速启动命令
        /// </summary>
        [RelayCommand]
        private async Task OnFastStart(string taskType)
        {
            // 快速开始逻辑
            await OnFastStartClicked_ToViewModel?.Invoke(taskType);
        }

        /// <summary>
        /// 打开任务详情页面命令
        /// </summary>
        [RelayCommand]
        private void OpenTaskPage()
        {
            TaskConfigWindow? window = null;

            try
            {
                if (_serviceProvider != null)
                {
                    window = _serviceProvider.GetService(typeof(TaskConfigWindow)) as TaskConfigWindow;
                }
            }
            catch
            {
                // 服务获取失败时，忽略异常
            }

            // 如果通过服务提供者无法获取窗口，则直接创建实例
            if (window == null)
            {
                // 直接创建所需的ViewModel和Window
                var viewModel = new TaskConfigWindowViewModel();
                window = new TaskConfigWindow(viewModel);
            }

            // 保存当前列表的引用
            var originalList = GameTaskLists;

            window.InitConfig(GameTaskLists, (t) =>
                  {
                      // 保持集合引用不变，但更新内容
                      originalList.Clear();
                      foreach (var item in t)
                      {
                          originalList.Add(item);
                      }

                      // 显式通知UI更新
                      OnPropertyChanged(nameof(GameTaskLists));
                  });
            window.ShowDialog();
        }

        private void SelectTeamMember(string teamType)
        {
            new TeamWindow(SelectHwnd, (name) =>
                      {
                          if (teamType == "御魂")
                          {
                              Yhun_ZuDui_Name = name;
                          }
                          else if (teamType == "探索")
                          {
                              TSuo_ZuDui_Name = name;
                          }
                          return 1;
                      }).ShowDialog();
        }

        /// <summary>
        /// 选择探索组队队友名称命令
        /// </summary>
        [RelayCommand]
        private void SelectTSuoZuDuiName()
        {
            SelectTeamMember("探索");
        }

        /// <summary>
        /// 选择御魂组队队友名称命令
        /// </summary>
        [RelayCommand]
        private void SelectYHZuDuiName()
        {
            SelectTeamMember("御魂");
        }

        /// <summary>
        /// 开启全部日常任务
        /// </summary>
        [RelayCommand]
        private void DayilyOpenAll()
        {
            DailyTask_DiGui = true;
            DailyTask_ChouKa = true;
            DailyTask_Befriendly = true;
            DailyTask_Reward = true;
            DailyTask_FreeGift = true;
            DailyTask_Checkin = true;
        }

        /// <summary>
        /// 弹出指定提示信息命令
        /// </summary>
        [RelayCommand]
        private async Task ShowTip(string _Class)
        {
            try
            {
                string Text = Tips.ContainsKey(_Class) ? Tips[_Class] : "";

                Utils.ShowMessage($"{_Class}注意点：", Text);
            }
            catch (Exception)
            {
            }
        }


        public void ResetAppStutas()
        {
            // 重置所有任务状态
            StartButtonEnabled = true;
        }

        /// <summary>
        /// 保存当前任务配置到文件
        /// </summary>
        /// <param name="gameName">游戏名称</param>
        /// <returns>是否保存成功</returns>
        public bool SaveConfig(string gameName)
        {
            try
            {
                // 创建一个字典存储所有字段值
                var configDict = new Dictionary<string, object?>();

                // 反射获取所有字段
                var fields = this.GetType().GetFields(System.Reflection.BindingFlags.Public |
                System.Reflection.BindingFlags.NonPublic |
                System.Reflection.BindingFlags.Instance);

                // 排除不需要保存的字段
                var excludeFields = new[] { "OnFastStartClicked_ToViewModel", "OnGameTaskListsChanged_ToViewModel",
                                                                                "SelectHwnd", "_serviceProvider", "_taskService", "Tips","_GameTaskLists" };

                foreach (var field in fields)
                {
                    // 跳过不需要保存的字段
                    if (excludeFields.Contains(field.Name))
                        continue;

                    // 获取字段值
                    var value = field.GetValue(this);

                    // 特殊处理集合类
                    if (field.Name == "_GameTaskLists")
                    {
                        var taskList = value as ObservableCollection<TaskConfigsModel.Configs>;
                        if (taskList != null)
                        {
                            configDict[field.Name] = taskList.ToList();
                        }
                    }
                    else
                    {
                        // 常规字段直接保存
                        configDict[field.Name] = value;
                    }
                }

                // 将字典序列化为JSON
                string json = System.Text.Json.JsonSerializer.Serialize(configDict, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true
                });

                // 保存到文件
                XConfig.SaveValueToFile(gameName, "AddTaskPropertys", json);

                return true;
            }
            catch (Exception ex)
            {
                XLogger.Error($"保存任务配置失败: {ex.Message}");
                return false;
            }
        }


        /// <summary>
        /// 从文件加载任务配置到当前实例
        /// </summary>
        /// <param name="gameName">游戏名称</param>
        /// <returns>是否成功加载配置</returns>
        public bool LoadConfig(string gameName)
        {
            try
            {
                // 读取JSON文件
                string? json = XConfig.LoadValueFromFile<string>(gameName, "AddTaskPropertys");
                if (string.IsNullOrEmpty(json))
                    return false;

                // 反序列化为字典
                var configDict = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                if (configDict == null)
                    return false;

                // 反射获取所有字段
                var fields = this.GetType().GetFields(System.Reflection.BindingFlags.Public |
                System.Reflection.BindingFlags.NonPublic |
                System.Reflection.BindingFlags.Instance);

                foreach (var field in fields)
                {
                    // 跳过不需要设置的字段
                    if (!configDict.ContainsKey(field.Name) ||
                    field.Name == "OnFastStartClicked_ToViewModel" ||
                    field.Name == "OnGameTaskListsChanged_ToViewModel" ||
                    field.Name == "SelectHwnd" ||
                    field.Name == "_serviceProvider" ||
                    field.Name == "_taskService" ||
                    field.Name == "_IsSuperMode" ||
                    field.Name == "Tips" ||
                    field.Name == "StartButtonEnabled" ||
                    field.Name == "_StartButtonEnabled" ||
                    field.Name == "_HDong_ClassLists" ||
                    field.Name == "HDong_ClassLists" ||
                    field.Name == "_HDong_Class" ||
                    field.Name == "HDong_Class" ||
                    field.Name == "_Game_SceneLists" ||
                    field.Name == "Game_SceneLists" ||
                    field.Name == "GameTaskLists" ||
                    field.Name == "_GameTaskLists")
                        continue;

                    try
                    {
                        // 获取JSON元素
                        var jsonElement = configDict[field.Name];

                        // 特殊处理游戏任务列表
                        if (field.Name == "_GameTaskLists" && jsonElement is System.Text.Json.JsonElement element &&
                        element.ValueKind == System.Text.Json.JsonValueKind.Array)
                        {
                            var taskList = System.Text.Json.JsonSerializer.Deserialize<List<TaskConfigsModel.Configs>>(
                                    element.GetRawText());

                            if (taskList != null)
                            {
                                // 清空当前列表
                                var gameTaskLists = field.GetValue(this) as ObservableCollection<TaskConfigsModel.Configs>;
                                if (gameTaskLists != null)
                                {
                                    gameTaskLists.Clear();
                                    foreach (var task in taskList)
                                    {
                                        gameTaskLists.Add(task);
                                    }
                                }
                            }
                        }
                        // 其他字段类型转换并设置
                        else if (jsonElement is System.Text.Json.JsonElement element1)
                        {
                            var fieldType = field.FieldType;
                            object? convertedValue = null;

                            // 根据字段类型进行转换
                            if (fieldType == typeof(string))
                            {
                                convertedValue = element1.ValueKind == System.Text.Json.JsonValueKind.String ?
                                element1.GetString() : element1.ToString();
                            }
                            else if (fieldType == typeof(bool))
                            {
                                convertedValue = element1.ValueKind == System.Text.Json.JsonValueKind.True ||
                                (element1.ValueKind == System.Text.Json.JsonValueKind.String &&
                                bool.TryParse(element1.GetString(), out bool boolValue) && boolValue);
                            }
                            else if (fieldType == typeof(int))
                            {
                                convertedValue = element1.ValueKind == System.Text.Json.JsonValueKind.Number ?
                                element1.GetInt32() :
                                (element1.ValueKind == System.Text.Json.JsonValueKind.String &&
                                int.TryParse(element1.GetString(), out int intValue) ? intValue : 0);
                            }
                            else if (fieldType == typeof(List<string>))
                            {
                                if (element1.ValueKind == System.Text.Json.JsonValueKind.Array)
                                {
                                    convertedValue = System.Text.Json.JsonSerializer.Deserialize<List<string>>(
                                            element1.GetRawText());
                                }
                            }
                            else if (fieldType == typeof(InfoBarModel))
                            {
                                if (element1.ValueKind == System.Text.Json.JsonValueKind.Object)
                                {
                                    convertedValue = System.Text.Json.JsonSerializer.Deserialize<InfoBarModel>(
                                        element1.GetRawText());
                                }
                            }
                            else
                            {
                                // 尝试通用反序列化
                                try
                                {
                                    convertedValue = System.Text.Json.JsonSerializer.Deserialize(
                                    element1.GetRawText(), fieldType);
                                }
                                catch
                                {
                                    // 如果失败，保持默认值
                                }
                            }

                            // 设置字段值
                            if (convertedValue != null)
                            {
                                field.SetValue(this, convertedValue);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Warn($"设置字段 {field.Name} 时出错: {ex.Message}");
                        XConfig.DeleteConfigFile(gameName, "AddTaskPropertys");
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                XLogger.Error($"加载任务配置异常: {ex.Message}");
                XConfig.DeleteConfigFile(gameName, "AddTaskPropertys");
                return false;
            }
        }

        /// <summary>
        /// 获取当前任务配置的JSON字符串，包含任务队列
        /// </summary>
        /// <returns>包含当前配置的JSON字符串</returns>
        public string GetConfigs()
        {
            try
            {
                // 创建一个字典存储所有字段值
                var configDict = new Dictionary<string, object?>();

                // 反射获取所有字段
                var fields = this.GetType().GetFields(System.Reflection.BindingFlags.Public |
                System.Reflection.BindingFlags.NonPublic |
                System.Reflection.BindingFlags.Instance);

                // 排除不需要保存的字段
                var excludeFields = new[] { "OnFastStartClicked_ToViewModel", "OnGameTaskListsChanged_ToViewModel",
                                           "SelectHwnd", "_serviceProvider", "_taskService", "Tips" };

                foreach (var field in fields)
                {
                    // 跳过不需要保存的字段
                    if (excludeFields.Contains(field.Name))
                        continue;

                    // 获取字段值
                    var value = field.GetValue(this);

                    // 特殊处理游戏任务列表
                    if (field.Name == "_GameTaskLists")
                    {
                        var taskList = value as ObservableCollection<TaskConfigsModel.Configs>;
                        if (taskList != null)
                        {
                            configDict[field.Name] = taskList.ToList();
                        }
                    }
                    else
                    {
                        // 常规字段直接保存
                        configDict[field.Name] = value;
                    }
                }

                // 将字典序列化为JSON
                string json = System.Text.Json.JsonSerializer.Serialize(configDict, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true
                });

                return json;
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取任务配置JSON失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 将传入的JSON字符串加载到当前实例
        /// </summary>
        /// <param name="json">JSON格式的配置字符串</param>
        /// <returns>是否成功加载配置</returns>
        public bool SetConfigs(string json)
        {
            try
            {
                if (string.IsNullOrEmpty(json))
                    return false;

                // 使用Newtonsoft.Json反序列化为字典
                var configDict = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
                if (configDict == null)
                    return false;

                // 反射获取所有字段
                var fields = this.GetType().GetFields(System.Reflection.BindingFlags.Public |
                System.Reflection.BindingFlags.NonPublic |
                System.Reflection.BindingFlags.Instance);

                foreach (var field in fields)
                {
                    // 跳过不需要设置的字段
                    if (!configDict.ContainsKey(field.Name) ||
                    field.Name == "OnFastStartClicked_ToViewModel" ||
                    field.Name == "OnGameTaskListsChanged_ToViewModel" ||
                    field.Name == "SelectHwnd" ||
                    field.Name == "_serviceProvider" ||
                    field.Name == "_taskService" ||
                    field.Name == "Tips" ||
                    field.Name == "StartButtonEnabled" ||
                    field.Name == "_StartButtonEnabled" ||
                    // 排除所有RelayCommand和AsyncRelayCommand属性，避免序列化和反序列化
                    field.Name.EndsWith("Command") ||
                    field.Name.StartsWith("_") && field.Name.EndsWith("Command") ||
                    field.FieldType.Name.Contains("RelayCommand"))
                        continue;

                    try
                    {
                        // 获取JSON元素
                        var jsonElement = configDict[field.Name];

                        // 特殊处理游戏任务列表
                        if (field.Name == "_GameTaskLists")
                        {
                            var taskListJson = Newtonsoft.Json.JsonConvert.SerializeObject(jsonElement);
                            var taskList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<TaskConfigsModel.Configs>>(taskListJson);

                            if (taskList != null)
                            {
                                // 清空当前列表
                                var gameTaskLists = field.GetValue(this) as ObservableCollection<TaskConfigsModel.Configs>;
                                if (gameTaskLists != null)
                                {
                                    gameTaskLists.Clear();
                                    foreach (var task in taskList)
                                    {
                                        gameTaskLists.Add(task);
                                    }
                                }

                                // 通知UI更新
                                OnPropertyChanged(nameof(GameTaskLists));
                                OnGameTaskListsChanged_ToViewModel?.Invoke(GameTaskLists);
                            }
                        }
                        // 其他字段类型转换并设置
                        else
                        {
                            var fieldType = field.FieldType;
                            object convertedValue = null;

                            try
                            {
                                // 将JSON元素转换为字符串
                                string jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(jsonElement);

                                // 反序列化为目标类型
                                if (fieldType == typeof(string))
                                {
                                    convertedValue = jsonElement?.ToString();
                                }
                                else if (fieldType == typeof(bool))
                                {
                                    if (jsonElement is bool boolValue)
                                        convertedValue = boolValue;
                                    else if (jsonElement is string strValue && bool.TryParse(strValue, out bool parsedBool))
                                        convertedValue = parsedBool;
                                    else
                                        convertedValue = false;
                                }
                                else if (fieldType == typeof(int))
                                {
                                    if (jsonElement is long longValue)
                                        convertedValue = (int)longValue;
                                    else if (jsonElement is int intValue)
                                        convertedValue = intValue;
                                    else if (jsonElement is string strValue && int.TryParse(strValue, out int parsedInt))
                                        convertedValue = parsedInt;
                                    else
                                        convertedValue = 0;
                                }
                                else if (fieldType == typeof(List<string>))
                                {
                                    convertedValue = Newtonsoft.Json.JsonConvert.DeserializeObject<List<string>>(jsonString);
                                }
                                else if (fieldType == typeof(InfoBarModel))
                                {
                                    convertedValue = Newtonsoft.Json.JsonConvert.DeserializeObject<InfoBarModel>(jsonString);
                                }
                                else
                                {
                                    // 尝试通用反序列化
                                    convertedValue = Newtonsoft.Json.JsonConvert.DeserializeObject(jsonString, fieldType);
                                }
                            }
                            catch (Exception ex)
                            {
                                XLogger.Warn($"反序列化字段 {field.Name} 时出错: {ex.Message}");
                            }

                            // 设置字段值
                            if (convertedValue != null)
                            {
                                field.SetValue(this, convertedValue);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Warn($"设置字段 {field.Name} 时出错: {ex.Message}");
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                XLogger.Error($"加载任务配置JSON异常: {ex.Message}");
                return false;
            }
        }
    }
}