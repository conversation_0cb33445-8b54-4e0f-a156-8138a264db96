﻿using DamoControlKit.Control;
using DamoControlKit.Model;
using ScriptEngine.Model;
using static ScriptEngine.Model.TaskConfigsModel;

namespace ScriptEngine.Tasks.Base
{
    public abstract class BaseTask
    {
        /// <summary>
        /// 应用的预设
        /// </summary>
        internal string? UserConfig_Preset;

        /// <summary>
        /// 突破卷30张 标记
        /// </summary>
        internal string? UserConfig_Tupo30BiaoJi;

        /// <summary>
        /// 突破卷30张 战斗次数
        /// </summary>
        internal int UserConfig_Tupo30Count;

        /// <summary>
        /// 突破卷30张 卡级
        /// </summary>
        internal bool UserConfig_Tupo30KaJi;

        /// <summary>
        /// 突破卷30张 打突破
        /// </summary>
        internal bool UserConfig_Tupo30Run = false;

        /// <summary>
        /// 任务当前失败次数
        /// </summary>
        private int defeated_Count = 0;

        /// <summary>
        /// 任务失败次数上限
        /// </summary>
        private int defeated_NCount = 0;

        /// <summary>
        /// 任务结束时调用
        /// </summary>
        public event Action? TaskCanceledExceptionEnded;

        /// <summary>
        /// 游戏速度
        /// </summary>
        public bool GameSetting_Speed { get; private set; }

        /// <summary>
        /// 游戏卡屏重启中
        /// </summary>
        public bool Restarting { get; set; } = false;

        /// <summary>
        /// 临时执行的一些活动
        /// </summary>
        public TmpActivitys Tmp { get; set; }

        /// <summary>
        /// 用户通知信息
        /// </summary>
        public string UserNotificationMessage { get; set; } = string.Empty;

        /// <summary>
        /// 御魂已经满了
        /// </summary>
        public bool YuHunFulling { get; set; } = false;

        /// <summary>
        /// 反检测对象
        /// </summary>
        internal AntiDetect Anti => Db.Anti;

        /// <summary>
        /// 当前任务名
        /// </summary>
        internal string ClassName { get; set; }

        /// <summary>
        /// 控制任务结束
        /// </summary>
        public CancellationTokenSource Ct { get; set; }

        /// <summary>
        /// 全局用户配置
        /// </summary>
        internal DDBuilder Db { get; set; }

        /// <summary>
        /// 全局大漠插件
        /// </summary>
        internal dmsoft Dm { get; set; }

        /// <summary>
        /// 快速操作对象
        /// </summary>
        internal Fast Fast { get; set; }

        /// <summary>
        /// 当前任务配置
        /// </summary>
        internal TaskConfigsModel.Configs GetConfig { get; set; }

        /// <summary>
        /// 日志对象
        /// </summary>
        internal Log log => Db.Log;

        /// <summary>
        /// 全局图库
        /// </summary>
        internal MemPics Mp { get; set; }

        /// <summary>
        /// 大漠项目的操作类
        /// </summary>
        internal Operational Operational { get; set; }

        /// <summary>
        /// 场景识别辅助对象
        /// </summary>
        internal Scene Scene { get; set; }

        /// <summary>
        /// 重新判断加载预设
        /// </summary>
        private void ReloadPreset(string str, Configs config)
        {
            Dictionary<string, string> topairs = new()
            {
                {"御魂", "Gal_YuShe_Yuhun"},
                {"觉醒", "Gal_YuShe_Juexing"},
                {"探索", "Gal_YuShe_Tansuo"},
                {"日轮", "Gal_YuShe_Rilun"},
                {"永生", "Gal_YuShe_Yongsheng"},
                {"御灵", "Gal_YuShe_Yuling"},
                {"业原火", "Gal_YuShe_Yeyuanhuo"},
                {"突破", "Gal_YuShe_Tupo"},
                {"悬赏", "Gal_YuShe_Xuanshang"},
                {"六道", "Gal_YuShe_Liudao"},
                {"契灵", "Gal_YuShe_Qiling"},
                {"斗技", "Gal_YuShe_Douji"},
                {"英杰", "Gal_YuShe_Yingjie"},
                {"日常", "Gal_YuShe_Daily"}
            };

            if (Db.UserConfigs.TryGetValue("Gal_YuShe", out object? _T) && _T is bool && (bool)_T)
                if (topairs.TryGetValue(str, out string presetKey))
                    if (Db.UserConfigs.TryGetValue(presetKey, out object? _T1) && _T1 is string && !string.IsNullOrEmpty((string)_T1))
                    {
                        config.Others["Preset"] = _T1.ToString();
                        log.Info($"您开启了全局预设，将使用偏好中的预设方案进行任务！{config.Others["Preset"]}");
                    }
        }

        /// <summary>
        /// 初始化
        /// </summary>
        public virtual void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            Db = dB;
            Dm = dm;
            Ct = ct;
            ClassName = className;
            Mp = DynamicData.FilterPicsClass(className);
            Mp.SetAllXsoft(Dm);
            Scene = new(Dm, DynamicData.FilterPicsClass("Sub"), Db.GameSetting.CourtyardSkin, Sleep, dB, ct);
            Fast = new(Dm, Ct, this);
            dB.Anti ??= new(this);
            Tmp = new(this);
            Operational = new(Dm);
            //用户偏好——超过失败次数结束
            object? _T;
            Db.UserConfigs.TryGetValue("DefeatedEnd", out _T);
            defeated_NCount = _T != null ? Convert.ToInt32(_T) : 0;

            // 修复Tupo30run解析
            if (Db.UserConfigs.TryGetValue("Tupo30run", out _T) && _T != null)
            {
                try { UserConfig_Tupo30Run = Convert.ToBoolean(_T); }
                catch { UserConfig_Tupo30Run = false; }
            }

            // 修复Tupo30_Count解析
            if (Db.UserConfigs.TryGetValue("Tupo30_Count", out _T) && _T != null)
            {
                try { UserConfig_Tupo30Count = Convert.ToInt32(_T); }
                catch { UserConfig_Tupo30Count = 0; }
            }

            // 修复Tupo30_Biaoji解析
            if (Db.UserConfigs.TryGetValue("Tupo30_Biaoji", out _T) && _T != null)
            {
                UserConfig_Tupo30BiaoJi = _T.ToString();
            }
            else
            {
                UserConfig_Tupo30BiaoJi = "位置5"; // 默认值
            }

            // 修复Tupo30_Tui4解析
            if (Db.UserConfigs.TryGetValue("Tupo30_Tui4", out _T) && _T != null)
            {
                try { UserConfig_Tupo30KaJi = Convert.ToBoolean(_T); }
                catch { UserConfig_Tupo30KaJi = false; }
            }

            GameSetting_Speed = Db.GameSetting.SpeedSwitch;

            if (className != "Sub")
            {
                Sleep(500);
                var subTask = dB.ShareData.SubTask;
                Db.UserConfigs.TryGetValue("KaPing_Do", out object? _T2);
                if (subTask is not null)
                {
                    // 监听游戏重启事件
                    if (Db.GameSetting.AdbPort > 0)
                    {
                        if (_T2?.ToString() == "重启游戏")
                            log.Info($"任务：[{ClassName}] 重启游戏等事件监听已开启...");
                        subTask.OnGameRestarted += () => Restarting = false; //绑定游戏重启后执行的事件
                        subTask.OnGameStuck += () => Restarting = true; //绑定游戏卡死后执行的事件
                    }
                    else
                    {
                        if (_T2?.ToString() == "重启游戏")
                            log.Warn($"任务：[{ClassName}] 重启游戏相关监听注册失败，请检查模拟器配置是否OK...");
                    }
                    if (ClassName != "Buff")
                        subTask.OnPushYuHunFull += SubTask_OnPushYuHunFull;// 监听御魂满了事件
                    // 监听定时任务触发事件
                    subTask.OnTimerTaskRunEvent += () => Db.PendingTimerTask = true;
                }
            }
        }

        /// <summary>
        /// 开始任务
        /// </summary>
        public virtual void Start(TaskConfigsModel.Configs configs)
        {
            //重新设置用户的全局预设
            ReloadPreset(ClassName, configs);

            if (Db.PendingTimerTask) //执行定时任务
            {
                Db.PendingTimerTask = false;
                log.Info("当前需要执行定时任务，暂时停止主任务..");
                Db?.TimerTask?.DoAllTask();
                Sleep(1000);
                throw new Exception("定时任务执行结束，重新执行当前的主任务..");
            }

            GetConfig = configs;
            //解析Other中是否存在 AutoBuff 字段
            if (ClassName != "Buff")
                if (GetConfig.Others.ContainsKey("AutoBuff"))
                {
                    Dictionary<string, string> classtobuffname = new()
                    {
                        {"御魂","御魂" },
                        {"觉醒","觉醒" },
                        {"探索","经验" },
                    };
                    var AutoBuff = GetConfig.Others["AutoBuff"];
                    if (bool.TryParse(AutoBuff, out bool autoBuff) &&
                        new List<string>() { "御魂", "觉醒", "探索" }.Contains(ClassName))
                    {
                        Db.Data.Set("BuffTypes", new Dictionary<string, string>() { { classtobuffname[ClassName], "1" } });
                        Db.Data.Set("AutoBuff", autoBuff);
                    }
                    else Db.Data.Set("AutoBuff", false);
                }
                else Db.Data.Set("AutoBuff", false);

            //解析Other中是否存在 Preset 字段
            if (GetConfig.Others.ContainsKey("Preset"))
            {
                var preset = GetConfig.Others["Preset"];
                if (preset != null)
                    UserConfig_Preset = preset;
            }

            Sleep(1000);
        }

        /// <summary>
        /// 任务失败时调用
        /// </summary>
        internal void Defeated()
        {
            if (defeated_NCount == 0) return;
            defeated_Count++;
            log.Warn($"用户偏好设置了失败上限，目前：{defeated_Count}/{defeated_NCount}");
            if (defeated_Count >= defeated_NCount)
            {
                //结束停止脚本
                Ct.Cancel();
                Sleep(1000);
                return;
            }
        }

        /// <summary>
        /// 延迟
        /// </summary>
        /// <param name="ms"></param>
        /// <exception cref="TaskCanceledException"></exception>
        internal void Sleep(int ms, bool isreal = false)
        {
            int delayStep = 1000; // 检查间隔，以毫秒为单位
            if (GameSetting_Speed)
            {
                delayStep = 500; // 加快速度
                ms /= 2; // 加快速度
            }

            long totalTicks = (long)(ms * TimeSpan.TicksPerMillisecond); // 总延迟时间的刻度值

            for (long ticks = 0; ticks < totalTicks; ticks += delayStep * TimeSpan.TicksPerMillisecond)
            {
                if (Restarting)
                {
                    log.Warn($"任务:[{ClassName}]执行过程中遇到副线程通知暂停执行主任务..");
                    while (Restarting) Task.Delay(delayStep).Wait();
                    log.Warn($"任务:[{ClassName}]副线程结束了暂停操作，重新执行主任务...");
                    throw new Exception("卡屏重启结束，重新执行主任务..");
                }

                if (Ct.Token.IsCancellationRequested)
                {
                    if (ClassName != "Sub")
                        UserNotificationMessage = $"任务{ClassName}被提前结束..";
                    log.Debug($"任务:[{ClassName}]被取消..");
                    TaskCanceledExceptionEnded?.Invoke();
                    throw new TaskCanceledException();
                }
                Task.Delay(delayStep).Wait();
            }
            //随机延迟20-120
            if (!isreal) Task.Delay(new Random().Next(20, 120)).Wait();
        }

        /// <summary>
        /// 御魂满了 是否需要执行清理
        /// </summary>
        /// <exception cref="NotImplementedException"></exception>
        private bool SubTask_OnPushYuHunFull()
        {
            YuHunFulling = true;
            if (new List<string>() { "御魂", "御灵", "业原火", "探索", "日轮", "觉醒", "活动", "契灵" }.Contains(ClassName))
                return true;
            log.Warn("当前任务不支持快速清理御魂，开始尝试重启游戏并执行御魂清理！");
            return false;
        }
    }
}