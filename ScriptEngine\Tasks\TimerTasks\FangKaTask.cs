﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;

namespace ScriptEngine.Tasks.TimerTasks
{
    internal class FangKaTask : BaseTask
    {
        public FangKaTaskConfig? Config;

        private Dictionary<string, Position> ClickPos = new()
        {
            {"打开选择框",new(390,108,476,134) },
            {"选择太鼓",new(384,253,502,281) },
            {"选择斗鱼",new(394,321,496,350) },
            {"领取收益",new(903,164,936,197) },
        };

        private Dictionary<string, Pixel> Colors = new Dictionary<string, Pixel>()
        {
            {"确定",new Pixel(801,429,"f3b25e-101010",0.96) },
            {"没有卡",new Pixel(949,315,"492c18-101010",0.98) },
        };

        private int MAX_XIALA_COUTN = 5;

        public void CheckAndDo()
        {
            if (!Config.isDo) return;
            // 检查场景-庭院
            if (Scene.NowScene != "庭院")
            {
                log.Info("当前场景不在庭院，尝试前往庭院；");
                if (!Scene.TO.TingYuan())
                {
                    log.Info("无法前往庭院，任务结束；");
                    throw new TaskCanceledException();
                }
            }

            log.Info("进入阴阳寮..");
            Fast.Click(550, 617, 591, 660);
            Sleep(2500);
            // 定义重试计数器
            int retryCount = 0;
            int maxRetries = 3;
        ReTryYYL:
            // 检查重试次数是否超过限制
            if (retryCount >= maxRetries)
            {
                log.Info($"尝试进入阴阳寮已达最大重试次数({maxRetries}次)，任务结束；");
                throw new TaskCanceledException();
            }
            // 检查场景-阴阳寮
            if (Scene.NowScene != "阴阳寮")
            {
                log.Info("当前场景不在阴阳寮，再试一次；");
                if (Scene.NowScene == "庭院")
                {
                    // 增加重试计数
                    retryCount++;
                    log.Info($"第{retryCount}次尝试进入阴阳寮...");
                    Fast.Click(550, 617, 591, 660);
                    Sleep(2500);
                    goto ReTryYYL;
                }
            }

            log.Info("进入结界..");
            Fast.Click(1072, 639, 1123, 686);
            Sleep(2500);
            // 定义结界重试计数器
            int retryJieJieCount = 0;
            int maxJieJieRetries = 3;
        ReTryJieJie:
            // 检查重试次数是否超过限制
            if (retryJieJieCount >= maxJieJieRetries)
            {
                log.Info($"尝试进入结界已达最大重试次数({maxJieJieRetries}次)，任务结束；");
                throw new TaskCanceledException();
            }
            // 检查场景-结界
            if (Scene.NowScene != "寄养")
            {
                log.Info("当前场景不在结界，再试一次；");
                if (Scene.NowScene == "阴阳寮")
                {
                    // 增加重试计数
                    retryJieJieCount++;
                    log.Info($"第{retryJieJieCount}次尝试进入结界...");
                    Fast.Click(1072, 639, 1123, 686);
                    Sleep(2500);
                    goto ReTryJieJie;
                }
            }

            Main();
        }

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, className);
            //初始化组件
            foreach (var item in Colors)
                item.Value.SetXsoft(dm);
        }

        /// <summary>
        /// Main
        /// </summary>
        public void Main()
        {
            // 领取收益
            log.Info("强制领取收益..");
            Fast.Click(ClickPos["领取收益"]);
            Sleep(50, false);
            Fast.Click(ClickPos["领取收益"]);
            Sleep(50, false);

            //还不在寄养界面
            if (Scene.NowScene != "寄养")
            {
                log.Info($"尝试强制点击结界卡..");
                Fast.Click(908, 314);
                Sleep(2500);
            }
            else
            {
                log.Info("点击结界卡..");
                Fast.Click(908, 314);
                Sleep(2500);
            }

            if (!Mp.Filter("放卡界面").FindAll())
            {
                log.Warn("不在放卡界面，无法继续..");
                log.Info("退出结界..");
                Fast.Click(41, 47);
                Sleep(2500);
                log.Info("退出阴阳寮..");
                Fast.Click(41, 47);
                Sleep(2500);
                return;
            }

            //检查卡状态
            if (!Colors["没有卡"].Find(null))
            {
                log.Info_Green("Good，当前结界中已经放卡了，无需继续，结束任务..");
                Config.DoTime = GetNextTime(); // 添加这行代码，设置下一次执行时间
                Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]下次放卡通知", $"\r\n" +
                    $"下次放卡任务触发时间为：{Config.DoTime:yyyy-MM-dd HH:mm:ss}");
            }
            else
                SelectKa();

            log.Info("关闭放卡界面..");
            Fast.Click(1176, 112);
            Sleep(1000);
            log.Info("退出结界..");
            Fast.Click(41, 47);
            Sleep(2500);
            log.Info("退出阴阳寮..");
            Fast.Click(41, 47);
            Sleep(2500);
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            Config = new FangKaTaskConfig(configs);
            CheckAndDo();
        }

        private bool FFangKa(int x, int y)
        {
            Fast.Click(x, y);
            Sleep(1000);

            //检查卡状态
            if (!Colors["没有卡"].Find(null))
            {
                //截图 保存到.\\放卡 文件夹
                var path = Path.Combine(Environment.CurrentDirectory, "放卡");
                if (!Directory.Exists(path))
                    Directory.CreateDirectory(path);
                Dm.CaptureJpg(0, 0, 2000, 2000, path + $"\\[{log.LogClassName}] {DateTime.Now:yyyyMMddHHmmss}.jpg", 90);

                log.Info("点击激活结界卡..");
                Fast.Click(1099, 608);
                Sleep(1500);

                while (Colors["确定"].Find(Dm))
                {
                    log.Info("点击确定..");
                    Fast.Click(760, 434);
                    Sleep(1500);
                }

                Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]放卡完成通知", $"\r\n" +
                        $"下次放卡任务触发时间为：{GetNextTime():yyyy-MM-dd HH:mm:ss}");
                return true;
            }

            return false;
        }

        /// <summary>
        /// 获取下次触发时间
        /// </summary>
        /// <returns></returns>
        private DateTime GetNextTime()
        {
            // 最大重试次数
            const int maxRetries = 5;
            // 当前重试次数
            int retryCount = 0;
            // 是否成功解析时间
            bool parseSuccess = false;
            // 时间跨度对象
            TimeSpan timeSpan = TimeSpan.Zero;

            // 循环尝试解析时间，直到成功或达到最大重试次数
            while (!parseSuccess && retryCount <= maxRetries)
            {
                // 获取OCR识别到的时间字符串
                string timeStr = Fast.Ocr_String_V2(new(923, 257, 1024, 294));
                timeStr = timeStr.Trim();
                log.Info($"识别到的寄养时间: {timeStr} (尝试 {retryCount + 1}/{maxRetries + 1})");

                // 使用正则表达式匹配时间格式 xx:xx:xx
                var match = System.Text.RegularExpressions.Regex.Match(timeStr, @"(\d{1,2}:\d{1,2}:\d{1,2})");
                if (match.Success)
                {
                    // 提取匹配到的时间字符串
                    string cleanTimeStr = match.Groups[1].Value;
                    log.Info($"正则匹配提取的时间: {cleanTimeStr}");
                    timeStr = cleanTimeStr;
                }

                // 尝试解析时间字符串为TimeSpan
                bool tryParseSuccess = false;

                // 处理小时数超过24小时的情况
                var timeParts = timeStr.Split(':');
                if (timeParts.Length == 3 && int.TryParse(timeParts[0], out int hours) &&
                    int.TryParse(timeParts[1], out int minutes) &&
                    int.TryParse(timeParts[2], out int seconds))
                {
                    // 手动构建TimeSpan
                    timeSpan = new TimeSpan(hours / 24, hours % 24, minutes, seconds);
                    tryParseSuccess = true;
                    log.Info($"手动解析时间成功: {timeSpan}");
                }
                else
                {
                    // 尝试标准解析
                    tryParseSuccess = TimeSpan.TryParse(timeStr, out timeSpan);
                }

                if (tryParseSuccess)
                {
                    // 检查时间是否为0或接近0，或超过30小时
                    if (timeSpan.TotalSeconds < 60)
                    {
                        // 时间太短，视为无效，强制设置为默认时间
                        log.Warn($"解析的时间 {timeSpan} 小于1分钟，视为无效，使用默认时间间隔(8小时)");
                        timeSpan = TimeSpan.FromHours(8);
                        parseSuccess = true;
                    }
                    else if (timeSpan.TotalHours <= 30)
                    {
                        // 解析成功且时间合理
                        parseSuccess = true;
                        log.Info($"成功解析时间: {timeSpan}");
                    }
                    else
                    {
                        retryCount++;
                        log.Warn($"解析的时间 {timeSpan} 超过30小时，视为无效，1秒后重试 ({retryCount}/{maxRetries})");
                        Sleep(1000);
                    }
                }
                else
                {
                    // 解析失败，增加重试计数
                    retryCount++;

                    // 如果没有达到最大重试次数，等待1秒后重试
                    if (retryCount <= maxRetries)
                    {
                        log.Warn($"时间格式解析失败: {timeStr}，1秒后重试 ({retryCount}/{maxRetries})");
                        Sleep(1000);
                    }
                }
            }

            DateTime res;
            // 根据解析结果设置下次寄养时间
            if (parseSuccess)
            {
                // 检查解析出的时间是否超过30小时
                if (timeSpan.TotalHours > 30)
                {
                    // 如果超过30小时，将其限制为30小时
                    timeSpan = TimeSpan.FromHours(30);
                    log.Info("解析出的时间超过30小时，已限制为30小时");
                }
                else if (timeSpan.TotalMinutes < 10)
                {
                    // 如果时间太短（少于10分钟），设置为默认8小时
                    timeSpan = TimeSpan.FromHours(8);
                    log.Info("解析出的时间过短（少于10分钟），已设置为默认8小时");
                }
                // 计算下次时间（当前时间 + 识别到的时间间隔）
                DateTime nextTime = DateTime.Now.Add(timeSpan);
                // 设置下次时间
                Config.DoTime = res = nextTime;
            }
            else
            {
                log.Info("解析到期时间失败，8小时后再执行一次");
                // 计算下次时间（当前时间 + 8小时）
                DateTime nextTime = DateTime.Now.AddHours(8);
                // 设置下次时间
                Config.DoTime = res = nextTime;
            }

            log.Info($"下次放卡任务执行的时间为：{res:yyyy-MM-dd HH:mm:ss}");
            return res;
        }

        /// <summary>
        /// 获取星级数量
        /// </summary>
        private int GetStartCount(int start_x, int start_y)
        {
            int res = 0;
            if (new Pixel(start_x, start_y, "e1692f-202020", 0.9).Find(Dm))
                res++;
            if (new Pixel(start_x + 14, start_y, "e1692f-202020", 0.9).Find(Dm))
                res++;
            if (new Pixel(start_x + 27, start_y, "e1692f-202020", 0.9).Find(Dm))
                res++;
            if (new Pixel(start_x + 42, start_y, "e1692f-202020", 0.9).Find(Dm))
                res++;
            if (new Pixel(start_x + 56, start_y, "e1692f-202020", 0.9).Find(Dm))
                res++;
            if (new Pixel(start_x + 70, start_y, "e1692f-202020", 0.9).Find(Dm))
                res++;

            return res;
        }

        /// <summary>
        /// 选卡
        /// </summary>
        private void SelectKa()
        {
            log.Info($"开始选卡，选卡规则：{Config.FangKa_Class}，{Config.FangKa_Level}");
            // 选卡
            log.Info("点击选择框..");
            Fast.Click(ClickPos["打开选择框"]);
            Sleep(1200);
            // 选择卡
            log.Info("点击选择指定类型的卡..");
            Fast.Click(ClickPos[$"选择{Config.FangKa_Class}"]);
            Sleep(1200);
            bool isOK = false;
            int retryCount = 0;
        ReTry:
            //筛选卡
            var res = Dm.FindMultiColorEx(189, 171, 207, 631, "e1692f-151515", "5|3|fa8f26-151515,0|7|f59c1f-151515,5|5|fa6b14-151515,1|2|e7761c-151515,0|3|f26819-151515", 0.95, 0);
            var vs_Count = Dm.GetResultCount(res);
            for (int i = 0; i < vs_Count; i++)
            {
                var vs_Pos = Dm.GetResultPos(res, i, out int x, out int y);
                int startCount = GetStartCount(x, y);
                if (startCount == 0) continue;
                if ((Config.FangKa_Level == "≤6x" && startCount <= 6) ||
                    (Config.FangKa_Level == "≤5x" && startCount <= 5) ||
                    (Config.FangKa_Level == "4x" && startCount == 4))
                {
                    log.Info($"找到符合条件的卡，开始放卡..");
                    if (FFangKa(x, y)) { isOK = true; break; }
                }
            }
            if (!isOK)
            {
                MAX_XIALA_COUTN = Config.HuaDongCount;
                log.Warn($"当前没有找到符合条件的卡，尝试下一页..最多尝试{MAX_XIALA_COUTN}次..");
                if (retryCount < MAX_XIALA_COUTN)
                {
                    retryCount++;
                    log.Info($"第{retryCount}次尝试下一页..");
                    Operational.Slide_Pos(new Position(296, 589, 408, 616), new(301, 331, 430, 359));
                    Sleep(1000);
                    goto ReTry;
                }
                else
                {
                    log.Warn($"尝试{MAX_XIALA_COUTN}次后仍然没有找到符合条件的卡，放弃放卡任务..");
                    // 发送通知
                    Db.OnSendNotification?.Invoke($"[{Db.Log.LogClassName}]放卡任务终止通知", $"\r\n" +
                        $"尝试{MAX_XIALA_COUTN}次后仍然没有找到符合条件的卡，已将放卡任务从执行队列中移除。\r\n" +
                        $"如需继续使用放卡功能，请重新添加放卡定时任务。");

                    // 抛出异常终止任务，这将导致任务从执行队列中移除
                    throw new TaskCanceledException("没有找到符合条件的卡，放卡任务已终止");
                }
            }

            log.Info("放卡结束..");
            Sleep(100);
        }
    }

    internal class FangKaTaskConfig
    {
        public FangKaTaskConfig(TaskConfigsModel.Configs configs)
        {
            // 设置下一次寄养时间为当前时间
            DoTime = DateTime.Now;

            // 设置指定寄养的好友名称
            if (configs.Others.TryGetValue("FangKa_Class", out string fangKa_Class))
                FangKa_Class = fangKa_Class;
            else
                FangKa_Class = "太鼓";

            // 设置指定寄养的策略
            if (configs.Others.TryGetValue("FangKa_Level", out string fangKa_Level))
                FangKa_Level = fangKa_Level;
            else
                FangKa_Level = "≤6x";

            // 设置指定寄养的策略
            if (configs.Others.TryGetValue("FangKa_HuaDongCount", out string huaDongCount))
                if (int.TryParse(huaDongCount, out int _HuaDongCount))
                    HuaDongCount = _HuaDongCount;
                else
                    HuaDongCount = 5;
            else
                HuaDongCount = 5;
        }

        /// <summary>
        /// 下一次寄养时间
        /// </summary>
        public DateTime DoTime { get; set; }

        /// <summary>
        /// 指定寄养的好友名称
        /// </summary>
        public string FangKa_Class { get; set; }

        /// <summary>
        /// 指定寄养的策略
        /// </summary>
        public string FangKa_Level { get; set; }

        /// <summary>
        /// 最大滑动次数
        /// </summary>
        public int HuaDongCount { get; set; }

        /// <summary>
        /// 是否需要执行寄养操作
        /// </summary>
        public bool isDo => DateTime.Now >= DoTime;
    }
}