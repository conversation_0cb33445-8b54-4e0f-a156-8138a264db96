using DanDing1.Services.Notification;
using XHelper;

namespace DanDing1.Commands
{
    /// <summary>
    /// 通知相关命令处理类
    /// </summary>
    internal class NotificationCommands : BaseCommand
    {
        /// <summary>
        /// 执行通知相关命令
        /// </summary>
        /// <param name="args">命令参数</param>
        public override void Execute(string[] args)
        {
            if (args.Length < 3)
            {
                ShowUsage();
                return;
            }

            // 命令格式：test notif [type] [message]
            string subCommand = args[2].ToLower();

            switch (subCommand)
            {
                case "email":
                case "mail":
                    TestEmailNotification(args);
                    break;

                case "app":
                case "ntfy":
                    TestAppNotification(args);
                    break;

                case "wxpush":
                case "wx":
                    TestWxPushNotification(args);
                    break;

                case "custom":
                case "自定义":
                    TestCustomNotification(args);
                    break;

                case "help":
                case "?":
                    ShowUsage();
                    break;

                default:
                    XLogger.Error($"未知的通知类型: {subCommand}");
                    ShowUsage();
                    break;
            }
        }

        /// <summary>
        /// 从命令参数中提取消息内容
        /// </summary>
        /// <param name="args">命令参数</param>
        /// <returns>消息内容</returns>
        private string GetMessageFromArgs(string[] args)
        {
            if (args.Length <= 3)
            {
                return "这是一条测试通知消息";
            }

            // 从第4个参数开始拼接为消息内容
            return string.Join(" ", args, 3, args.Length - 3);
        }

        /// <summary>
        /// 显示命令使用说明
        /// </summary>
        private void ShowUsage()
        {
            XLogger.Info("通知测试命令使用说明:");
            XLogger.Info("  test notif email [消息]  - 测试邮件通知");
            XLogger.Info("  test notif app [消息]    - 测试App通知");
            XLogger.Info("  test notif wxpush [消息] - 测试微信推送通知");
            XLogger.Info("  test notif custom [消息] - 测试自定义通知（根据配置发送多种通知）");
            XLogger.Info("  test notif help         - 显示帮助信息");
            XLogger.Info("示例: test notif email 这是一条测试邮件");
        }

        /// <summary>
        /// 测试App通知
        /// </summary>
        /// <param name="args">命令参数</param>
        private void TestAppNotification(string[] args)
        {
            string message = GetMessageFromArgs(args);
            XLogger.Info("正在发送测试App通知...");

            Task.Run(async () =>
            {
                try
                {
                    var userService = GlobalData.Instance.appConfig.dNet?.User;
                    if (userService == null)
                    {
                        XLogger.Error("用户服务未初始化，无法发送通知");
                        return;
                    }

                    var factory = new NotificationSenderFactory(userService);
                    var service = new TaskNotificationService(factory);

                    bool result = await service.SendTaskCompletionNoticeAsync(
                        "测试",
                        DateTime.Now.ToString("HH:mm:ss"),
                        0,
                        0,
                        new List<string> { "测试任务" },
                        new Dictionary<string, string> { { "测试消息", message } },
                        "App");

                    if (result)
                    {
                        XLogger.Info_Green("App通知发送成功！");
                    }
                    else
                    {
                        XLogger.Error("App通知发送失败！");
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Error($"发送App通知时出错: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 测试自定义通知（根据配置发送多种通知）
        /// </summary>
        /// <param name="args">命令参数</param>
        private void TestCustomNotification(string[] args)
        {
            string message = GetMessageFromArgs(args);
            XLogger.Info("正在发送测试自定义通知...");

            Task.Run(async () =>
            {
                try
                {
                    var userService = GlobalData.Instance.appConfig.dNet?.User;
                    if (userService == null)
                    {
                        XLogger.Error("用户服务未初始化，无法发送通知");
                        return;
                    }

                    // 检查自定义通知配置
                    bool useNtfy = GlobalData.Instance.UserConfig.Notice_Ntfy;
                    bool useWxPush = GlobalData.Instance.UserConfig.Notice_WxPush;

                    //XLogger.Info($"自定义通知配置：App通知={useNtfy}, 微信推送={useWxPush}");

                    if (!useNtfy && !useWxPush)
                    {
                        XLogger.Warn("未启用任何额外通知方式，将只发送邮件通知");
                    }

                    var factory = new NotificationSenderFactory(userService);
                    var service = new TaskNotificationService(factory);

                    bool result = await service.SendTaskCompletionNoticeAsync(
                        "测试",
                        DateTime.Now.ToString("HH:mm:ss"),
                        0,
                        0,
                        new List<string> { "测试任务" },
                        new Dictionary<string, string> { { "测试消息", message } },
                        "自定义");

                    if (result)
                    {
                        XLogger.Info_Green("自定义通知发送成功！");
                    }
                    else
                    {
                        XLogger.Error("自定义通知发送失败！");
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Error($"发送自定义通知时出错: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 测试邮件通知
        /// </summary>
        /// <param name="args">命令参数</param>
        private void TestEmailNotification(string[] args)
        {
            string message = GetMessageFromArgs(args);
            XLogger.Info("正在发送测试邮件通知...");

            Task.Run(async () =>
            {
                try
                {
                    var userService = GlobalData.Instance.appConfig.dNet?.User;
                    if (userService == null)
                    {
                        XLogger.Error("用户服务未初始化，无法发送通知");
                        return;
                    }

                    var factory = new NotificationSenderFactory(userService);
                    var service = new TaskNotificationService(factory);

                    bool result = await service.SendTaskCompletionNoticeAsync(
                        "测试",
                        DateTime.Now.ToString("HH:mm:ss"),
                        0,
                        0,
                        new List<string> { "测试任务" },
                        new Dictionary<string, string> { { "测试消息", message } },
                        "邮件");

                    if (result)
                    {
                        XLogger.Info_Green("邮件通知发送成功！");
                    }
                    else
                    {
                        XLogger.Error("邮件通知发送失败！");
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Error($"发送邮件通知时出错: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 测试微信推送通知
        /// </summary>
        /// <param name="args">命令参数</param>
        private void TestWxPushNotification(string[] args)
        {
            string message = GetMessageFromArgs(args);
            XLogger.Info("正在发送测试微信推送通知...");

            Task.Run(async () =>
            {
                try
                {
                    var userService = GlobalData.Instance.appConfig.dNet?.User;
                    if (userService == null)
                    {
                        XLogger.Error("用户服务未初始化，无法发送通知");
                        return;
                    }

                    // 检查关键配置
                    string appToken = "AT_3VCBuZy9seMzN6bxolf8NA6mm8BsvelW";
                    string uid = GlobalData.Instance.UserConfig.Notice_WxPush_UID;

                    if (string.IsNullOrEmpty(appToken))
                    {
                        XLogger.Error("缺少WxPushAppToken配置，无法发送微信推送");
                        return;
                    }

                    if (string.IsNullOrEmpty(uid))
                    {
                        XLogger.Warn("未配置WxPushUid，将尝试使用主题ID发送");
                    }

                    // 准备微信推送所需的参数
                    Dictionary<string, object> wxPushParams = new Dictionary<string, object>();
                    if (!string.IsNullOrEmpty(uid))
                    {
                        wxPushParams["uids"] = new List<string> { uid };
                    }

                    var factory = new NotificationSenderFactory(userService);
                    var service = new TaskNotificationService(factory);

                    bool result = await service.SendTaskCompletionNoticeAsync(
                        "测试",
                        DateTime.Now.ToString("HH:mm:ss"),
                        0,
                        0,
                        new List<string> { "测试任务" },
                        new Dictionary<string, string> { { "测试消息", message } },
                        "微信推送",
                        wxPushParams);

                    if (result)
                    {
                        XLogger.Info_Green("微信推送通知发送成功！");
                    }
                    else
                    {
                        XLogger.Error("微信推送通知发送失败！");
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Error($"发送微信推送通知时出错: {ex.Message}");
                }
            });
        }
    }
}