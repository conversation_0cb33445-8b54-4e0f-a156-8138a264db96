﻿using ScriptEngine.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace ScriptEngine.Tests
{
    internal class ScriptFrame_Run_Test : Base_Test
    {
        public ScriptFrame_Run_Test(string TestName = "ScriptFrame_Run_Test") : base(TestName)
        { }

        private dmsoft Dm { get; set; }

        public ScriptFrame_Run_Test Init(dmsoft dm)
        {
            Dm = dm;
            return this;
        }

        public override void Test()
        {
            if (!OnOrOff["ScriptFrame_Run_Test"]) return;

            base.Test();
            string color = DynamicData.DictsColor[0];
            XLogger.Debug("字库个数：" + Dm.GetDictCount(0));
            int it1 = Dm.FindStrFast(278, 134, 321, 624, "9", color, 0.9, out int _, out int _);
            XLogger.Debug("FindStrEx 9 结果：" + it1);
            int it2 = Dm.FindStrFast(278, 134, 321, 624, "10", color, 0.9, out int _, out int _);
            XLogger.Debug("FindStrEx 10 结果：" + it2);
            int it3 = Dm.FindStrFast(278, 134, 321, 624, "11", color, 0.9, out int _, out int _);
            XLogger.Debug("FindStrEx 11 结果：" + it3);
            int it4 = Dm.FindStrFast(278, 134, 321, 624, "12", color, 0.9, out int _, out int _);
            XLogger.Debug("FindStrEx 12 结果：" + it4);
        }
    }
}