<Window x:Class="DanDing1.Views.Windows.ShareConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:DanDing1.Views.Windows"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
        ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
        Title="共享配置" Height="320" Width="450" WindowStartupLocation="CenterScreen">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="配置共享设置" 
                   FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- 配置名称/版本 -->
        <TextBlock Grid.Row="1" Text="配置名称:" 
                   FontSize="14" Margin="0,0,0,5"/>
        <TextBox x:Name="ConfigNameTextBox" Grid.Row="2" 
                 Margin="0,0,0,15" Padding="8,5"
                 FontSize="14"/>

        <!-- 有效期选择 -->
        <TextBlock Grid.Row="3" Text="配置有效期:" 
                   FontSize="14" Margin="0,0,0,5"/>
        <ComboBox x:Name="ExpiryComboBox" Grid.Row="4" 
                  Margin="0,0,0,20" Padding="8,5"
                  FontSize="14">
            <ComboBoxItem Content="永不过期" Tag="0" IsSelected="True"/>
            <ComboBoxItem Content="1天" Tag="1"/>
            <ComboBoxItem Content="3天" Tag="3"/>
            <ComboBoxItem Content="7天" Tag="7"/>
            <ComboBoxItem Content="15天" Tag="15"/>
            <ComboBoxItem Content="30天" Tag="30"/>
        </ComboBox>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,10,0,0">
            <Button x:Name="ShareButton" Content="共享" 
                    Width="100" Height="35" Margin="0,0,10,0"
                    Background="{DynamicResource AccentFillColorDefaultBrush}"
                    Foreground="White" FontWeight="SemiBold"
                    Click="ShareButton_Click"/>
            <Button x:Name="CancelButton" Content="取消" 
                    Width="100" Height="35"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window> 