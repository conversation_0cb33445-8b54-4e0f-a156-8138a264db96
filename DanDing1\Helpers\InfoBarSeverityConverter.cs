using System;
using System.Globalization;
using System.Windows.Data;
using Wpf.Ui.Controls;

namespace DanDing1.Helpers
{
    /// <summary>
    /// 将null或空字符串转换为InfoBarSeverity.Informational的转换器
    /// </summary>
    public class InfoBarSeverityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return InfoBarSeverity.Informational;

            if (value is InfoBarSeverity severity)
                return severity;

            return InfoBarSeverity.Informational;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is InfoBarSeverity severity)
                return severity;

            return InfoBarSeverity.Informational;
        }
    }
}