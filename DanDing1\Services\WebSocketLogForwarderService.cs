using System.Collections.Concurrent;
using XHelper; // 引用XWebsocket
using XHelper.Models; // 引用ILogForwarder和RemoteLogEntry

namespace DanDing1.Services
{
    public class WebSocketLogForwarderService : ILogForwarder, IDisposable
    {
        // 新增一个标记，用来识别自己产生的日志
        private const string LOG_FORWARDER_TAG = "WebSocketLogForwarder:";

        // 新增过滤关键字列表
        private static readonly string[] _filterKeywords = new string[]
        {
            LOG_FORWARDER_TAG,              // 日志转发器自身日志
            "LOG_ENTRIES_PUSH",             // 日志推送消息
            "LOG_ENTRIES_RESPONSE",         // 日志响应消息
            "WebSocket连接",                // WebSocket连接相关日志
            "WebSocket未连接",              // WebSocket连接状态日志
            "收到WebSocket消息",            // WebSocket接收消息日志
            "检测到嵌套消息",               // 嵌套消息处理日志
            "成功解析内层消息",             // 消息解析日志
            "找到内层消息类型",             // 消息处理器日志
            "收到日志同步请求",             // 日志同步请求日志
            "日志同步响应",                 // 日志同步响应日志
            "GET_LOGS_COMMAND",             // 日志命令关键字
            "注册消息处理器"                // 消息处理器注册日志
        };

        private readonly TimeSpan _failLogInterval = TimeSpan.FromMinutes(30);
        private readonly ConcurrentQueue<RemoteLogEntry> _logQueue = new ConcurrentQueue<RemoteLogEntry>();
        private readonly int _maxBatchSize;
        private readonly object _processLock = new object();
        private readonly TimeSpan _reenableCheckInterval = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _uploadInterval;
        private bool _isDisposed = false;
        private volatile bool _isSending = false;

        // 确保在线程间可见性
        // 序列化对ProcessQueueAsync的调用
        private DateTime _lastFailLogTime = DateTime.MinValue;

        // 上次记录失败日志的时间
        // 每30分钟最多记录一次失败日志
        private DateTime _lastReenableCheckTime = DateTime.MinValue;

        private Timer _timer; // System.Threading.Timer
                              // 上次检查重新启用的时间
                              // 每5分钟检查一次是否需要重新启用

        // 添加用户登录状态变化的检测标记
        private bool _wasEligibleLastCheck = false;

        // 这些值应该是可配置的（例如通过XConfig或应用程序设置）
        public WebSocketLogForwarderService(TimeSpan? uploadInterval = null, int? maxBatchSize = null)
        {
            _uploadInterval = uploadInterval ?? TimeSpan.FromSeconds(3); // 默认：3秒
            _maxBatchSize = maxBatchSize ?? 100;                         // 默认：每批100条日志
            _timer = new Timer(async (_) => await ProcessQueueAsync(false), null, Timeout.InfiniteTimeSpan, Timeout.InfiniteTimeSpan);
            Debug.WriteLine($"WebSocketLogForwarderService已初始化。间隔：{_uploadInterval.TotalSeconds}秒，最大批量：{_maxBatchSize}。");
        }

        /// <summary>
        /// 检查WebSocket状态并尝试重新启用
        /// </summary>
        /// <returns>当前WebSocket是否可用</returns>
        public async Task<bool> CheckAndReenableWebSocketAsync()
        {
            // 如果已经处理了，则不再处理
            if (_isDisposed) return false;

            // 首先检查用户是否符合日志转发条件
            if (!IsUserEligibleForLogForwarding())
            {
                return false;
            }

            // 如果WebSocket未禁用，则检查连接状态
            if (!XWebsocket.IsDisabled)
            {
                if (XWebsocket.IsConnected)
                {
                    return true; // WebSocket正常
                }
                else if (XWebsocket.State == System.Net.WebSockets.WebSocketState.None ||
                         XWebsocket.State == System.Net.WebSockets.WebSocketState.Closed ||
                         XWebsocket.State == System.Net.WebSockets.WebSocketState.Aborted)
                {
                    // WebSocket未连接但未禁用，可能需要尝试重连
                    Debug.WriteLine($"{LOG_FORWARDER_TAG} WebSocket未连接，尝试重连");
                    return XWebsocket.AutoReconnect; // 如果已设置自动重连，则认为是可用的
                }

                return false; // 其他状态（连接中、关闭中等）暂不处理
            }

            // WebSocket已被禁用，判断是否需要尝试重新启用
            // 这里可以根据实际需求设置重新启用的条件，例如距离上次禁用已经过去了足够长的时间
            if ((DateTime.Now - _lastFailLogTime) > TimeSpan.FromHours(1)) // 至少1小时后才尝试重启
            {
                Debug.WriteLine($"{LOG_FORWARDER_TAG} 尝试重新启用WebSocket功能");
                bool reenabled = await XWebsocket.ReenableWebSocketAsync();
                if (reenabled)
                {
                    Debug.WriteLine($"{LOG_FORWARDER_TAG} WebSocket功能已重新启用");
                    _lastFailLogTime = DateTime.MinValue; // 重置失败日志时间
                    return true;
                }
                else
                {
                    Debug.WriteLine($"{LOG_FORWARDER_TAG} WebSocket功能重新启用失败");
                    _lastFailLogTime = DateTime.Now; // 更新失败日志时间
                    return false;
                }
            }

            return false; // WebSocket已禁用且暂不重新启用
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                _isDisposed = true;
                _timer?.Dispose();
                // 最好确保在应用程序关闭期间调用StopAsync
                // 以刷新剩余的日志。
                Debug.WriteLine("WebSocketLogForwarderService已释放。");
            }
        }

        // 实现ILogForwarder接口的方法
        public void EnqueueLog(DateTime timestamp, string level, string message, string? taskId = null)
        {
            if (_isDisposed) return;

            // 检查用户是否符合日志转发条件
            if (!IsUserEligibleForLogForwarding())
            {
                // 如果用户状态从符合条件变为不符合条件，停止服务
                if (_wasEligibleLastCheck)
                {
                    Debug.WriteLine("用户状态已变更，停止WebSocketLogForwarderService");
                    _ = StopAsync();
                    _wasEligibleLastCheck = false;
                }
                return;
            }
            else if (!_wasEligibleLastCheck)
            {
                // 如果用户状态从不符合条件变为符合条件，尝试重新启动服务
                Debug.WriteLine("用户状态已变更为符合条件，尝试重新启动WebSocketLogForwarderService");
                Start();
            }

            // 过滤掉不应该发送到服务端的日志
            if (ShouldFilter(message, level))
            {
                return;
            }

            var logEntry = new RemoteLogEntry(timestamp, level, message, taskId);
            _logQueue.Enqueue(logEntry);
        }

        // 保留原有方法便于向后兼容
        public void EnqueueLog(RemoteLogEntry logEntry)
        {
            if (_isDisposed || logEntry == null) return;

            // 检查用户是否符合日志转发条件
            if (!IsUserEligibleForLogForwarding())
            {
                // 如果用户状态从符合条件变为不符合条件，停止服务
                if (_wasEligibleLastCheck)
                {
                    Debug.WriteLine("用户状态已变更，停止WebSocketLogForwarderService");
                    _ = StopAsync();
                    _wasEligibleLastCheck = false;
                }
                return;
            }
            else if (!_wasEligibleLastCheck)
            {
                // 如果用户状态从不符合条件变为符合条件，尝试重新启动服务
                Debug.WriteLine("用户状态已变更为符合条件，尝试重新启动WebSocketLogForwarderService");
                Start();
            }

            // 过滤掉不应该发送到服务端的日志
            if (ShouldFilter(logEntry.Message, logEntry.Level))
            {
                return;
            }

            _logQueue.Enqueue(logEntry);
        }

        public void Start()
        {
            if (_isDisposed) throw new ObjectDisposedException(nameof(WebSocketLogForwarderService));

            // 检查用户是否符合日志转发条件
            if (!IsUserEligibleForLogForwarding())
            {
                Debug.WriteLine("WebSocketLogForwarderService未启动：用户未登录或为试用用户");
                _wasEligibleLastCheck = false;
                return;
            }

            Debug.WriteLine("WebSocketLogForwarderService正在启动。");
            _timer.Change(_uploadInterval, _uploadInterval); // 启动定时器
            _wasEligibleLastCheck = true;
        }

        public async Task StopAsync()
        {
            Debug.WriteLine("WebSocketLogForwarderService正在停止。");
            _timer.Change(Timeout.InfiniteTimeSpan, Timeout.InfiniteTimeSpan); // 停止定时器
            await ProcessQueueAsync(true); // 尝试刷新所有剩余日志
            Debug.WriteLine("WebSocketLogForwarderService已停止。");
        }

        /// <summary>
        /// 判断当前用户是否符合日志转发条件
        /// </summary>
        /// <returns>如果用户已登录且不是试用用户返回true，否则返回false</returns>
        private bool IsUserEligibleForLogForwarding()
        {
            // 检查用户是否已登录
            bool isLoggedIn = GlobalData.Instance?.appConfig?.IsLogin != null;

            // 检查是否为试用用户
            bool isFreeUser = GlobalData.Instance?.appConfig?.IsFree ?? true;

            // 只有登录且非试用用户才能使用日志转发
            return isLoggedIn && !isFreeUser;
        }

        private async Task ProcessQueueAsync(bool flushAll)
        {
            if (_isDisposed) return;

            // 检查用户是否符合日志转发条件
            if (!IsUserEligibleForLogForwarding())
            {
                // 如果用户状态从符合条件变为不符合条件，停止服务
                if (_wasEligibleLastCheck)
                {
                    Debug.WriteLine("用户状态已变更，停止WebSocketLogForwarderService");
                    _ = StopAsync();
                    _wasEligibleLastCheck = false;
                }

                // 如果是不符合条件的用户且队列中有数据，清空队列
                if (!_logQueue.IsEmpty)
                {
                    Debug.WriteLine($"{LOG_FORWARDER_TAG} 用户未登录或为试用用户，清空日志队列");
                    while (!_logQueue.IsEmpty)
                    {
                        _logQueue.TryDequeue(out _);
                    }
                }
                return;
            }
            else if (!_wasEligibleLastCheck)
            {
                // 如果用户状态从不符合条件变为符合条件，尝试重新启动服务
                Debug.WriteLine("用户状态已变更为符合条件，尝试重新启动WebSocketLogForwarderService");
                Start();
            }

            // 定期检查并尝试重新启用WebSocket
            if ((DateTime.Now - _lastReenableCheckTime) > _reenableCheckInterval)
            {
                _lastReenableCheckTime = DateTime.Now;
                await CheckAndReenableWebSocketAsync();
            }

            // 首先检查WebSocket功能是否已禁用
            if (XWebsocket.IsDisabled)
            {
                // 如果已禁用并且距离上次记录日志超过了间隔时间，记录一次日志
                if ((DateTime.Now - _lastFailLogTime) > _failLogInterval)
                {
                    Debug.WriteLine($"{LOG_FORWARDER_TAG} WebSocket功能已禁用，日志转发功能已暂停");
                    _lastFailLogTime = DateTime.Now;
                }

                // 无论是否刷新都不进行处理
                return;
            }

            if (!Monitor.TryEnter(_processLock))
            {
                // 另一个线程已经在处理或尝试处理。跳过此次运行。
                Debug.WriteLine($"{LOG_FORWARDER_TAG} ProcessQueueAsync跳过，另一个操作正在进行。");
                return;
            }

            try
            {
                if (_isSending && !flushAll)
                {
                    Debug.WriteLine($"{LOG_FORWARDER_TAG} 发送正在进行，跳过当前周期，除非是全部刷新。");
                    return;
                }
                _isSending = true;

                if (_logQueue.IsEmpty) return;

                List<RemoteLogEntry> logsToSend = new List<RemoteLogEntry>();
                int itemCount = 0;
                while (!_logQueue.IsEmpty && (itemCount < _maxBatchSize || flushAll))
                {
                    if (_logQueue.TryDequeue(out RemoteLogEntry? logEntry) && logEntry != null)
                    {
                        logsToSend.Add(logEntry);
                        itemCount++;
                    }
                    else break; // 队列为空或出队为null
                }

                if (logsToSend.Any())
                {
                    var messagePayload = new
                    {
                        type = "LOG_ENTRIES_PUSH", // 根据您的WebSocket协议
                        payload = new { entries = logsToSend }
                    };

                    Debug.WriteLine($"{LOG_FORWARDER_TAG} 尝试发送{logsToSend.Count}条日志条目。");
                    bool success = await XWebsocket.SendObjectAsync(messagePayload); // 假设XWebsocket已连接

                    if (success)
                    {
                        Debug.WriteLine($"{LOG_FORWARDER_TAG} 成功发送{logsToSend.Count}条日志条目。");
                    }
                    else if (!XWebsocket.IsDisabled) // 只有在WebSocket未被禁用时才记录失败
                    {
                        Debug.WriteLine($"{LOG_FORWARDER_TAG} 无法发送{logsToSend.Count}条日志条目。重新入队等待下次尝试。");
                        // 基本的重入队策略：重新加入。如果不小心可能导致顺序问题。
                        // 更强大的解决方案可能涉及单独的重试队列或死信队列。
                        foreach (var entry in logsToSend.AsEnumerable().Reverse()) // 反转以尝试保持顺序
                        {
                            _logQueue.Enqueue(entry); // 注意：这不是在ConcurrentQueue的前面插入
                        }
                    }

                    // 如果WebSocket已被禁用，检查是否需要清空队列
                    if (XWebsocket.IsDisabled && _logQueue.Count > 1000)
                    {
                        Debug.WriteLine($"{LOG_FORWARDER_TAG} WebSocket已禁用且日志队列过大({_logQueue.Count}条)，清空队列");
                        while (!_logQueue.IsEmpty)
                        {
                            _logQueue.TryDequeue(out _);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"{LOG_FORWARDER_TAG} ProcessQueueAsync异常：{ex.Message} - {ex.StackTrace}");
            }
            finally
            {
                _isSending = false;
                Monitor.Exit(_processLock);
            }
        }

        /// <summary>
        /// 检查消息是否应该被过滤
        /// </summary>
        /// <param name="message">要检查的消息</param>
        /// <param name="level">日志级别</param>
        /// <returns>如果消息应该被过滤返回true，否则返回false</returns>
        private bool ShouldFilter(string message, string level)
        {
            // 如果日志级别是DEBUG，并且包含任何一个过滤关键字，则过滤掉
            if (level == "DEBUG")
                return true;
            return false;
        }
    }
}