using System.Net.Http.Headers;
using System.Text.Json;
using XHelper.Models;

namespace XHelper.DanDingNet
{
    public class UserService : BaseService
    {
        public UserService(HttpClient client, string macCode, string host, string version)
            : base(client, macCode, host, version)
        {
        }

        public async Task<Response_UserData?> GetUserInfoAsync()
        {
            if (CheckObjisNull(_client)) return null;

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["用户信息"]);
            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<Response_UserData>(body, _jsonOptions);
        }

        public async Task<string?> SaveUserConfigAsync(string json)
        {
            if (CheckObjisNull(_client)) return null;

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["保存云端配置"]);
            var data = new { config = json };

            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            ResponseBaseData? obj = JsonSerializer.Deserialize<ResponseBaseData>(body, _jsonOptions);

            if (!(obj?.IsSuccess ?? false))
                return null;

            if (DanDingNet.EnableDebugLog) XLogger.Debug("[SaveUserConfigAsync] is Run Over：" + obj?.Message ?? "");
            return obj?.Message ?? "云端保存成功！";
        }

        public async Task<Response_NtfyKey?> GetNtfyKeyAsync()
        {
            if (CheckObjisNull(_client)) return null;

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["获取app通知key"]);
            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            Response_NtfyKey? obj = JsonSerializer.Deserialize<Response_NtfyKey>(body, _jsonOptions);
            return obj?.IsSuccess ?? false ? obj : null;
        }

        public async Task<bool> SendNoticeAsync(string type, string title, string text)
        {
            if (CheckObjisNull(_client)) return false;

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["发送通知"]);
            var data = new
            {
                type,
                title,
                txt = text
            };

            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            ResponseBaseData? obj = JsonSerializer.Deserialize<ResponseBaseData>(body, _jsonOptions);

            if (!(obj?.IsSuccess ?? false))
            {
                if (DanDingNet.EnableDebugLog) XLogger.Debug($"发送通知请求失败：{obj?.Code}|{obj?.Message}");
                return false;
            }

            return obj?.IsSuccess ?? false;
        }

        public async Task<Response_UserIdData?> QueryUserIdAsync(string username)
        {
            if (CheckObjisNull(_client)) return null;

            var message = GetBaseRequest(HttpMethod.Get, _host + DDApi.Api["查询用户ID"] + $"/{username}");
            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<Response_UserIdData>(body, _jsonOptions);
        }

        public async Task<string> GetDmsoftCodeAsync()
        {
            // 检查本地账号鉴权
            if (CheckObjisNull(_client))
                throw new Exception("初始化失败，服务器连接失败，请排查\r\n" +
                    "1. 是否已经登录或试用?\r\n" +
                    "2. 是否当前网络连接稳定?");

            // 检查账号鉴权状态
            var authService = new AuthService(_client, _macCode, _host, _version);
            bool isAuthenticated = await authService.CheckLoginStatusAsync();
            if (!isAuthenticated)
                throw new Exception("账号未登录或登录已过期，请重新登录后再试");

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["大漠注册码"]);
            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            Response_DmData? obj = JsonSerializer.Deserialize<Response_DmData>(body, _jsonOptions);

            if (!obj?.IsSuccess ?? false)
                throw new Exception("初始化失败，服务器连接失败，请排查\r\n" +
                    "1. 是否已经登录或试用?\r\n" +
                    "2. 是否当前网络连接稳定?");

            string code = obj?.Data?.code ?? "";
            string k = obj?.Data?.k ?? "";
            string i = "file.x-tools.top";

            if (string.IsNullOrEmpty(code))
                throw new Exception("初始化失败，服务器连接失败，请排查\r\n" +
                    "1. 是否已经登录或试用?\r\n" +
                    "2. 是否当前网络连接稳定?");

            return AesEncryption.Decrypt(code, k, i);
        }

        /// <summary>
        /// 查询用户积分信息
        /// </summary>
        /// <returns>用户积分信息结果</returns>
        public async Task<Response_UserPointsData?> GetUserPointsAsync()
        {
            if (CheckObjisNull(_client)) return null;

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["查询用户积分"]);
            var response = await SendRequestWithRetryAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            Response_UserPointsData? obj = JsonSerializer.Deserialize<Response_UserPointsData>(body, _jsonOptions);

            if (DanDingNet.EnableDebugLog) XLogger.Debug($"[GetUserPointsAsync] 查询结果：{obj?.IsSuccess}|{obj?.Message}");

            return obj?.IsSuccess ?? false ? obj : null;
        }

        /// <summary>
        /// 绑定QQ号
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="qq">需要绑定的QQ号</param>
        /// <param name="code">验证码</param>
        /// <returns>绑定QQ结果</returns>
        public async Task<Response_BindQQData?> BindQQAsync(string username, string qq, string code)
        {
            if (CheckObjisNull(_client)) return null;

            // 参数验证
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(qq) || string.IsNullOrEmpty(code))
            {
                XLogger.Error("绑定QQ失败：用户名、QQ或验证码不能为空");
                return null;
            }

            var data = new Request_BindQQData
            {
                username = username,
                qq = qq,
                code = code
            };

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["绑定QQ"]);
            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                Response_BindQQData? obj = JsonSerializer.Deserialize<Response_BindQQData>(body, _jsonOptions);

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"[BindQQAsync] 绑定结果：{obj?.IsSuccess}|{obj?.Message}");

                return obj;
            }
            catch (Exception ex)
            {
                XLogger.Error($"绑定QQ异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 存储用户临时日志到服务器
        /// </summary>
        /// <param name="logContent">日志内容</param>
        /// <returns>存储日志的结果</returns>
        public async Task<Response_StoreLogData?> StoreLogAsync(string logContent)
        {
            if (CheckObjisNull(_client))
            {
                XLogger.Error("存储日志失败：HttpClient为空");
                return null;
            }

            // 参数验证
            if (string.IsNullOrEmpty(logContent) || logContent.Trim() == "")
            {
                XLogger.Error("存储日志失败：日志内容不能为空");
                return null;
            }

            var data = new Request_StoreLogData
            {
                log_content = logContent
            };

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["存储日志"]);
            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                Response_StoreLogData? obj = JsonSerializer.Deserialize<Response_StoreLogData>(body, _jsonOptions);

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"[StoreLogAsync] 存储日志结果：{obj?.IsSuccess}|{obj?.Message}");

                return obj;
            }
            catch (Exception ex)
            {
                XLogger.Error($"存储日志异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 设置WebToken
        /// </summary>
        /// <param name="webToken">用户自定义WebToken，长度8-20个字符</param>
        /// <returns>设置WebToken的结果</returns>
        public async Task<Response_SetWebTokenData?> SetWebTokenAsync(string webToken)
        {
            if (CheckObjisNull(_client))
            {
                XLogger.Error("设置WebToken失败：HttpClient为空");
                return null;
            }

            // 参数验证
            if (string.IsNullOrEmpty(webToken) || webToken.Length < 8 || webToken.Length > 20)
            {
                XLogger.Error("设置WebToken失败：WebToken长度必须在8-20个字符之间");
                return null;
            }

            var data = new Request_SetWebTokenData
            {
                web_token = webToken
            };

            var message = GetBaseRequest(HttpMethod.Post, _host + DDApi.Api["设置WebToken"]);
            message.Content = new StringContent(JsonSerializer.Serialize(data))
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                Response_SetWebTokenData? obj = JsonSerializer.Deserialize<Response_SetWebTokenData>(body, _jsonOptions);

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"[SetWebTokenAsync] 设置结果：{obj?.IsSuccess}|{obj?.Message}");

                return obj;
            }
            catch (Exception ex)
            {
                XLogger.Error($"设置WebToken异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取WebToken状态
        /// </summary>
        /// <returns>WebToken状态信息</returns>
        public async Task<Response_WebTokenStatusData?> GetWebTokenStatusAsync()
        {
            if (CheckObjisNull(_client))
            {
                XLogger.Error("获取WebToken状态失败：HttpClient为空");
                return null;
            }

            var message = GetBaseRequest(HttpMethod.Get, _host + DDApi.Api["获取WebToken状态"]);

            try
            {
                var response = await SendRequestWithRetryAsync(message);
                var body = await response.Content.ReadAsStringAsync();
                Response_WebTokenStatusData? obj = JsonSerializer.Deserialize<Response_WebTokenStatusData>(body, _jsonOptions);

                if (DanDingNet.EnableDebugLog) XLogger.Debug($"[GetWebTokenStatusAsync] 查询结果：{obj?.IsSuccess}|{obj?.Message}");

                return obj?.Data.HasWebToken ?? false ? obj : null;
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取WebToken状态异常: {ex.Message}");
                return null;
            }
        }
    }
}