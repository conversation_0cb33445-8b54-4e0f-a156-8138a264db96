﻿using DanDing1.ViewModels.Pages;
using DanDing1.Views.Windows;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Wpf.Ui.Controls;
using Wpf.Ui.Abstractions.Controls;
using XHelper;
using System.Net;
using Minio;
using Minio.DataModel.Args;
using Minio.Exceptions;
using System.Windows.Controls.Primitives;
using DanDing1.Helpers;
using MessageBox = System.Windows.MessageBox;

namespace DanDing1.Views.Pages
{
    /// <summary>
    /// LogPage.xaml 的交互逻辑
    /// </summary>
    public partial class LogPage : INavigableView<LogViewModel>
    {
        public LogPage(LogViewModel viewModel)
        {
            ViewModel = viewModel;
            DataContext = this;
            InitializeComponent();
            LogFiltter.ItemsSource = new List<string>() { "全部", "游戏1", "游戏2", "游戏3", "游戏4" };
            LogFiltter.SelectedIndex = 0;
            LogFiltter.SelectionChanged += LogFiltter_SelectionChanged;
            //命令控制台
            //CommandBox.KeyDown += CommandBox_KeyDown;
            CommandBox.PreviewKeyDown += CommandBox_KeyDown;
            GlobalData.Instance.logWinControl.ClosedCallBack = ChangeIcon;
        }

        private List<string> commands_old = [];
        private int _historyIndex = -1;

        private async void CommandBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (sender is not System.Windows.Controls.TextBox box) return;

            if (e.Key == Key.Up)
            {
                // 处理向上方向键
                if (commands_old.Count > 0 && _historyIndex < commands_old.Count - 1)
                {
                    _historyIndex++;
                    box.Text = commands_old[_historyIndex];
                    box.CaretIndex = box.Text.Length;  // 将光标移到文本末尾
                }
            }
            else if (e.Key == Key.Down)
            {
                // 处理向下方向键
                if (_historyIndex > 0)
                {
                    _historyIndex--;
                    box.Text = commands_old[_historyIndex];
                    box.CaretIndex = box.Text.Length;  // 将光标移到文本末尾
                }
                else if (_historyIndex == 0)
                {
                    _historyIndex--;
                    box.Text = "";  // 清空输入框
                }
            }
            else if (e.Key == Key.Enter)
            {
                // 处理Enter键
                string command = box.Text;
                box.Text = "";
                _historyIndex = -1;  // 重置索引以便下次正确导航

                if (await ViewModel.CommandControl(command))
                {
                    if (commands_old.Contains(command))
                    {
                        commands_old.Remove(command);
                        commands_old.Insert(0, command);
                    }
                    else
                    {
                        commands_old.Insert(0, command);  // 最新命令添加到列表顶部
                    }
                }
            }
        }

        public LogViewModel ViewModel { get; }

        /// <summary>
        /// 弹出日志小窗
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Button_Click(object sender, RoutedEventArgs e)
        {
            if (ShowLogWindow_Icon.Symbol == SymbolRegular.ArrowDownLeft16)
            {
                //关闭
                GlobalData.Instance.logWinControl.CloseLogWin(LogFiltter.SelectedItem.ToString() ?? "全部");
                ChangeIcon();
                return;
            }
            GlobalData.Instance.logWinControl.ShowLogWin(LogFiltter.SelectedItem.ToString() ?? "全部");
            ChangeIcon();
        }

        private void LogFiltter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ViewModel.UpLogSource();
            //切换图标展示状态
            ChangeIcon();
        }

        /// <summary>
        /// 切换状态
        /// </summary>
        private void ChangeIcon()
        {
            var isOpen = GlobalData.Instance.logWinControl.GetLogWinStatus(LogFiltter.SelectedItem.ToString() ?? "全部");
            if (isOpen)
                ShowLogWindow_Icon.Symbol = SymbolRegular.ArrowDownLeft16;//打开状态 显示左小角落小箭头
            else
                ShowLogWindow_Icon.Symbol = SymbolRegular.ArrowUpRight16;//关闭状态 显示右上角落小箭头
        }

        /// <summary>
        /// 打开日志文件夹
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void OpenLogsFolderButton_Click(object sender, RoutedEventArgs e)
        {
            //获取当前日期的日志文件夹 命名规则 2024-12-25
            var logsFolder = System.IO.Path.Combine(System.AppDomain.CurrentDomain.BaseDirectory, "Logs", DateTime.Now.ToString("yyyy-MM-dd"));
            if (System.IO.Directory.Exists(logsFolder))
            {
                System.Diagnostics.Process.Start("explorer.exe", logsFolder);
            }
            else
                System.Diagnostics.Process.Start("explorer.exe", ".\\Logs");
        }

        /// <summary>
        /// 打包并提交日志
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void SaveLogsButton_Click(object sender, RoutedEventArgs e)
        {
            var endpoint = Decrypt("bC5kYW5kaW5nLnZpcA==");
            var accessKey = Decrypt("ZzVLUXBOaVVERTdHaXplWmtVWk0=");
            var secretKey = Decrypt("Wjl2OEtLeHJhNnNqRlZDa3k2UjdyOFV3VERLeVcxcVZQaEJEeE5PeQ==");

            string Decrypt(string encryptedText)
            {
                byte[] data = Convert.FromBase64String(encryptedText);
                return System.Text.Encoding.UTF8.GetString(data);
            }

            try
            {
                var minio = new MinioClient()
                                    .WithEndpoint(endpoint)
                                    .WithCredentials(accessKey, secretKey)
                                    .WithSSL()
                                    .Build();
                await Run(minio);
            }
            catch (Exception ex)
            {
                XLogger.Warn(ex.Message);
            }
        }

        // File uploader task.
        private static async Task Run(IMinioClient minio)
        {
            var bugtip = "";

            InputWindow input = new("问题点", "请简单描述你的遭遇！！");
            input.ShowDialog();
            if (input.IsOK)
                bugtip = input.InputText;

            // 检查问题描述是否为空或默认值
            if (string.IsNullOrWhiteSpace(bugtip) || bugtip == "问题点")
            {
                MessageBox.Show("请提供有效的问题描述，不能为空或默认值！", "提示", System.Windows.MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            string name = $"[{bugtip}] PackLogs-{DateTime.Now.ToString("yyyy-MM-dd-HH-mm-ss")}";
            string logzipDir = $"{AppDomain.CurrentDomain.BaseDirectory}Logs\\{name}";

            List<string> otherfiles = new()
            {
                $"{AppDomain.CurrentDomain.BaseDirectory}Logs\\mumu_json_output.json"
            };
            // 查找Logs目录下所有以"Error-"开头的文件夹
            string logsDirectory = $"{AppDomain.CurrentDomain.BaseDirectory}Logs\\";
            if (Directory.Exists(logsDirectory))
            {
                try
                {
                    // 获取所有以"Error-"开头的文件夹
                    string[] errorDirectories = Directory.GetDirectories(logsDirectory, "Error-*");

                    foreach (string errorDir in errorDirectories)
                    {
                        // 获取该文件夹下的所有文件
                        string[] errorFiles = Directory.GetFiles(errorDir);

                        // 将所有文件添加到otherfiles列表中
                        foreach (string file in errorFiles)
                            otherfiles.Add(file);
                    }

                    XLogger.Info($"已添加 {otherfiles.Count - 1} 个执行过程中错误日志文件..");
                }
                catch (Exception ex)
                {
                    XLogger.Warn($"查找错误日志文件时出错: {ex.Message}");
                }
            }

            string path = XLogger.PackLogs(logzipDir, otherfiles);

            var bucketName = "dandinglogs";
            var objectName = name + ".zip";
            var filePath = path;
            var contentType = "application/x-zip-compressed";

            try
            {
                // Make a bucket on the server, if not already present.
                var beArgs = new BucketExistsArgs()
                    .WithBucket(bucketName);
                bool found = await minio.BucketExistsAsync(beArgs).ConfigureAwait(false);
                if (!found)
                {
                    var mbArgs = new MakeBucketArgs()
                        .WithBucket(bucketName);
                    await minio.MakeBucketAsync(mbArgs).ConfigureAwait(false);
                }

                // Upload a file to bucket.
                var putObjectArgs = new PutObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectName)
                    .WithFileName(filePath)
                    .WithContentType(contentType);
                await minio.PutObjectAsync(putObjectArgs).ConfigureAwait(false);
                XLogger.Info("日志文件上传成功，我会加急处理您的问题！");
            }
            catch (MinioException e)
            {
                XLogger.Warn($"日志文件上传出了一点问题，请将文件和问题直接私发我，感谢！{e.Message}");
                Utils.OpenFolder(logzipDir);
            }
            finally
            {
                Task.Delay(2000).Wait();
                File.Delete(path);
                Directory.Delete(logzipDir, true);
            }
        }
    }
}