﻿<Page
    x:Class="DanDing1.Views.Pages.SettingsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helpers="clr-namespace:DanDing1.Helpers"
    xmlns:local="clr-namespace:DanDing1.Views.Pages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="SettingsPage"
    d:DataContext="{d:DesignInstance local:SettingsPage,
    IsDesignTimeCreatable=False}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
    ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    mc:Ignorable="d">
    <Page.Resources>
        <helpers:EnumToBooleanConverter x:Key="EnumToBooleanConverter"/>
    </Page.Resources>

    <TabControl>
        <TabItem Width="125">
            <TabItem.Header>
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Margin="0 0 6 0"
                                   Symbol="DocumentHeart24"/>
                    <TextBlock Text="个性化、常用"/>
                </StackPanel>
            </TabItem.Header>
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="16">
                    <StackPanel Margin="0,8,0,0"
                                Orientation="Horizontal">
                        <TextBlock VerticalAlignment="Center"
                                   Text="[个性化] 主题："/>
                        <RadioButton
                            Margin="10,0,0,0"
                            VerticalAlignment="Center"
                            Command="{Binding ViewModel.ChangeThemeCommand, Mode=OneWay}"
                            CommandParameter="theme_light"
                            Content="明亮"
                            GroupName="themeSelect"
                            IsChecked="{Binding ViewModel.CurrentTheme, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=Light, Mode=OneWay}"/>
                        <RadioButton
                            Margin="-40,0,0,0"
                            VerticalAlignment="Center"
                            Command="{Binding ViewModel.ChangeThemeCommand, Mode=OneWay}"
                            CommandParameter="theme_dark"
                            Content="黑暗"
                            GroupName="themeSelect"
                            IsChecked="{Binding ViewModel.CurrentTheme, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=Dark, Mode=OneWay}"/>
                        <ui:Button Margin="-40,0,0,0"
                                   x:Name="MultipleInstanceNameButton"
                                   Click="MultipleInstanceNameButton_Click"
                                   Content="自定义多开设置"/>
                    </StackPanel>
                    <CheckBox
                        x:Name="StartSaveTasks"
                        Margin="-10,8,0,0"
                        Checked="StartSaveTasks_Checked"
                        Unchecked="StartSaveTasks_Checked"
                        Content="开始任务后自动保存当前任务列表"/>
                    <StackPanel Orientation="Horizontal">
                        <CheckBox
                            x:Name="StartOpenLog"
                            Margin="-10,4,0,0"
                            Checked="StartOpenLog_Checked"
                            Content="开始任务后转到日志界面："
                            Unchecked="StartOpenLog_Checked"/>
                        <ComboBox
                            VerticalAlignment="Center"
                            IsEnabled="{Binding ElementName=StartOpenLog,Path=IsChecked}"
                            Margin="0 5 0 0"
                            x:Name="OpenLogItems"/>
                    </StackPanel>
                    <StackPanel Margin="0 3 0 0"
                                Orientation="Horizontal">
                        <ui:TextBlock
                            Margin="0 5 0 0"
                            FontSize="14"
                            VerticalAlignment="Center"
                            Text="[录制] 录制质量："/>
                        <ComboBox x:Name="RecordQuality"/>
                        <ui:Button
                            x:Name="ParseSRFile"
                            Click="ParseSRFile_Click"
                            Margin="8 0 0 0"
                            Height="36"
                            Content="解析sr文件"/>
                        <ui:Button
                            x:Name="MaterialCollector"
                            Margin="8 0 0 0"
                            Height="36"
                            Click="MaterialCollector_Click"
                            Content="素材收集工具"/>
                    </StackPanel>
                    <StackPanel Margin="0 3 0 0"
                                Orientation="Horizontal">
                        <ui:TextBlock
                            Margin="0 5 0 0"
                            FontSize="14"
                            VerticalAlignment="Center"
                            Text="[模拟器配置] ："/>
                        <ui:Button x:Name="OpenMuMuConfig"
                                   Height="36"
                                   Content="MuMu模拟器配置"
                                   Click="OpenMuMuConfig_Click"/>
                    </StackPanel>
                    <StackPanel Margin="0 3 0 0"
                                Orientation="Horizontal">
                        <ui:TextBlock
                            Margin="0 5 0 0"
                            FontSize="14"
                            VerticalAlignment="Center"
                            Text="[云端控制] ："/>
                        <CheckBox Content="蛋定Web监控平台"
                                  ToolTip="开启和关闭后需要重启App生效；"
                                  IsChecked="{Binding ViewModel.EnableWebSocketCommunication}"/>
                        <CheckBox Content="日志定时上报"
                                  ToolTip="仅用于QQ群查询最新日志使用；"
                                  IsChecked="{Binding ViewModel.EnableHttpLogReporting}"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </TabItem>

        <TabItem Width="120">
            <TabItem.Header>
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Margin="0 0 6 0"
                                   Symbol="TextboxSettings24"/>
                    <TextBlock Text="程序配置"/>
                </StackPanel>
            </TabItem.Header>
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="16">
                    <StackPanel Margin="0,8,0,0"
                                Orientation="Horizontal">
                        <TextBlock VerticalAlignment="Center"
                                   Text="快捷操作："/>
                        <ui:Button x:Name="OpenAppFolder"
                                   Height="36"
                                   Content="程序根目录"
                                   Click="OpenAppFolder_Click"/>
                        <ui:Button x:Name="CreateShortcut"
                                   Margin="5 0 0 0"
                                   Height="36"
                                   Content="创建快捷方式"
                                   Click="CreateShortcut_Click"/>
                    </StackPanel>
                    <StackPanel Margin="0,8,0,0"
                                Orientation="Horizontal">
                        <TextBlock VerticalAlignment="Center"
                                   Text="服务器接口："/>
                        <ComboBox x:Name="ServerHost"/>
                        <TextBlock
                            x:Name="ServerHost_Tip"
                            Margin="4,0,0,0"
                            VerticalAlignment="Center"
                            FontSize="15"
                            FontStyle="Italic"/>
                        <ui:HyperlinkButton
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Margin="10,0,0,0"
                            Content="服务状态监控"
                            NavigateUri="https://nz.x-tools.top"/>
                    </StackPanel>
                    <StackPanel Margin="0,8,0,0"
                                Orientation="Horizontal">
                        <TextBlock VerticalAlignment="Center"
                                   Text="图库源："/>
                        <ComboBox x:Name="PicServer"/>
                        <TextBlock
                            Margin="15,0,0,0"
                            VerticalAlignment="Center"
                            Text="图库版本(仅本次运行)："/>
                        <TextBox x:Name="PicServerVer"/>
                        <ui:Button x:Name="DownloadPicServer"
                                   Margin="10,0,0,0"
                                   Height="36"
                                   Content="图库下载到本地"
                                   Click="DownloadPicServer_Click"/>
                    </StackPanel>
                    <CheckBox
                        x:Name="ShowDebug"
                        Margin="-10,8,0,0"
                        Checked="ShowDebug_Checked"
                        Content="日志显示调试信息（需重启App）"
                        Unchecked="ShowDebug_Checked"/>
                </StackPanel>
            </ScrollViewer>
        </TabItem>

        <TabItem Width="120">
            <TabItem.Header>
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Margin="0 0 6 0"
                                   Symbol="Apps24"/>
                    <TextBlock Text="拓展插件"/>
                </StackPanel>
            </TabItem.Header>
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="16">
                    <TextBlock Text="本地插件："
                               FontSize="14"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    <StackPanel Margin="0,8,0,0">
                        <DockPanel LastChildFill="False">
                            <StackPanel>
                                <TextBlock Text="PaddleOCR_cpp"
                                           FontSize="14"
                                           FontWeight="Bold"/>
                                <TextBlock Text="感谢萌新大佬提供的占用更小的OCR命令行应用！（内置插件，六道任务的依赖插件）"
                                           FontSize="12"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           TextWrapping="Wrap"
                                           Margin="0,4,0,0"/>
                                <TextBlock Text="请注意：请确保当前蛋定助手运行的路径中没有空格！"
                                           FontSize="12"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           TextWrapping="Wrap"/>
                            </StackPanel>
                            <ui:Button
                                x:Name="InstallPaddleOCR"
                                DockPanel.Dock="Right"
                                VerticalAlignment="Center"
                                Margin="8,0,0,0"
                                Icon="ArrowDownload24"
                                Appearance="Secondary"
                                Content="{Binding InstallPaddleOCRText}"
                                IsEnabled="{Binding InstallPaddleOCREnabled}"
                                Click="InstallPaddleOCR_Click"/>
                        </DockPanel>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </TabItem>

        <TabItem Width="120">
            <TabItem.Header>
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Margin="0 0 6 0"
                                   Symbol="FoodEgg24"/>
                    <TextBlock Text="关于"/>
                </StackPanel>
            </TabItem.Header>
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="16">
                    <TextBlock Text="蛋定助手一款强大的自动化任务执行器，"/>
                    <TextBlock Text="可以帮助您快速完成重复性的工作，使您的生活更加轻松。"/>
                    <StackPanel Margin="0,10,0,0"
                                Orientation="Horizontal">
                        <TextBlock Text="当前设备标识："/>
                        <TextBlock Margin="5 0 0 0"
                                   Text="{Binding MachineCode}"
                                   MouseDown="CopyMachineCode_Click"
                                   Cursor="Hand"
                                   Foreground="{DynamicResource AccentBrush}"
                                   TextDecorations="Underline"
                                   FontWeight="SemiBold"
                                   ToolTip="点击复制机器码"/>
                    </StackPanel>
                    <StackPanel Margin="0,2,0,0"
                                Orientation="Horizontal">
                        <TextBlock
                            Margin="0,0,10,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            MouseDown="TextBlock_MouseDown"
                            Text="{Binding ViewModel.AppVersion, Mode=OneWay}"
                            ToolTip="双击此处检查版本是否为最新？"/>
                        <ui:Button
                            x:Name="CheckUpdateButton"
                            Content="检查更新"
                            Height="32"
                            Padding="10,5"
                            Click="CheckUpdateButton_Click"
                            Appearance="Secondary"
                            Margin="0,0,10,0"
                            ToolTip="检查是否有新版本可用"/>
                        <CheckBox
                            Margin="-10 0 0 0"
                            Content="加入内测版体验计划★"
                            IsChecked="{Binding ViewModel.UserConfig.BetaTesterToggle}"
                            IsEnabled="{Binding ViewModel.IsLoading}"
                            ToolTip="开启后，可接受内测版更新推送，第一时间体验最新功能和问题修复(需登录)"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </TabItem>
    </TabControl>
</Page>