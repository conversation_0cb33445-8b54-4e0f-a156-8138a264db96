﻿using DamoControlKit.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DamoControlKit.Interface
{

    /// <summary>
    /// 点击功能 接口
    /// </summary>
    interface IClick : IDmInterface
    {
        /// <summary>
        /// 点击
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        void Click();

        /// <summary>
        /// 精准点击
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        void Click(int x, int y);

        /// <summary>
        /// 范围点击
        /// </summary>
        void Click(Position position);

        /// <summary>
        /// 范围点击
        /// </summary>
        /// <param name="position">范围</param>
        /// <param name="count">点击次数</param>
        /// <param name="delay">间隔</param>
        void Click(Position position, int count, int delay = 25);

        /// <summary>
        /// 范围点击_防封有时count-1点击
        /// </summary>
        /// <param name="position">范围</param>
        /// <param name="count">点击次数</param>
        /// <param name="delay">间隔</param>
        void ClickEx(Position position, int count, int delay = 25);
    }
}
