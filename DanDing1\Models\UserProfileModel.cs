using DanDing1.Helpers;
using CommunityToolkit.Mvvm.ComponentModel;

namespace DanDing1.Models;

/// <summary>
/// 用户行为统计数据模型
/// </summary>
public partial class UserProfileModel : ObservableObject
{
    private const string STORAGE_FILE_NAME = "user_profile.json";

    /// <summary>
    /// 累计开始任务次数
    /// </summary>
    [ObservableProperty]
    private int _totalTaskStartCount;

    /// <summary>
    /// 累计脚本点击次数
    /// </summary>
    [ObservableProperty]
    private int _totalScriptClickCount;

    /// <summary>
    /// 累计脚本运行时长（分钟）
    /// </summary>
    [ObservableProperty]
    private int _totalScriptRunningMinutes;

    /// <summary>
    /// 累积处理悬赏次数
    /// </summary>
    [ObservableProperty]
    private int _totalBountyCount;

    /// <summary>
    /// 最长的一次脚本运行时长（分钟）
    /// </summary>
    [ObservableProperty]
    private int _longestScriptRunningMinutes;

    /// <summary>
    /// 从本地存储加载用户配置，如果不存在则创建新的实例
    /// </summary>
    public static UserProfileModel Load()
    {
        var profile = LocalStorage.LoadJson<UserProfileModel>(STORAGE_FILE_NAME);
        return profile ?? new UserProfileModel();
    }

    /// <summary>
    /// 保存当前实例到本地存储
    /// </summary>
    public void Save()
    {
        LocalStorage.SaveJson(STORAGE_FILE_NAME, this);
    }

    /// <summary>
    /// 更新脚本运行时长，如果超过历史最长记录则更新最长记录
    /// </summary>
    public void UpdateScriptRunningTime(int minutes)
    {
        TotalScriptRunningMinutes += minutes;
        if (minutes > LongestScriptRunningMinutes)
        {
            LongestScriptRunningMinutes = minutes;
        }
        Save();
    }

    /// <summary>
    /// 增加任务开始次数
    /// </summary>
    public void IncrementTaskStartCount(int count = 1)
    {
        TotalTaskStartCount += count;
        Save();
    }

    /// <summary>
    /// 增加脚本点击次数
    /// </summary>
    public void IncrementScriptClickCount(int count = 1)
    {
        TotalScriptClickCount += count;
        Save();
    }

    /// <summary>
    /// 增加悬赏处理次数
    /// </summary>
    public void IncrementBountyCount(int count = 1)
    {
        TotalBountyCount += count;
        Save();
    }

    /// <summary>
    /// 重置所有统计数据为0
    /// </summary>
    public void Reset()
    {
        TotalTaskStartCount = 0;
        TotalScriptClickCount = 0;
        TotalScriptRunningMinutes = 0;
        TotalBountyCount = 0;
        LongestScriptRunningMinutes = 0;
        Save();
    }
}