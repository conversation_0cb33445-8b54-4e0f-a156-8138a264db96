﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Management;
using System.Text;
using System.Threading.Tasks;
using XHelper;

namespace ScriptEngine.Core
{
    internal static class Utils
    {
        public static string UserGameName1 => XConfig.LoadValueFromFile<string>("游戏1", "GameName") ?? "游戏1";
        public static string UserGameName2 => XConfig.LoadValueFromFile<string>("游戏2", "GameName") ?? "游戏2";
        public static string UserGameName3 => XConfig.LoadValueFromFile<string>("游戏3", "GameName") ?? "游戏3";
        public static string UserGameName4 => XConfig.LoadValueFromFile<string>("游戏4", "GameName") ?? "游戏4";

        /// <summary>
        /// 获取临时文件夹路径
        /// </summary>
        internal static string GetTempFolder => Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Temp");

        public static string GetOSInfo()
        {
            FileVersionInfo versionInfo1 = FileVersionInfo.GetVersionInfo(Environment.SystemDirectory + @"\kernel32.dll");
            return versionInfo1?.FileVersion ?? "未知";
        }

        /// <summary>
        /// 清空临时文件夹
        /// </summary>
        internal static void ClearTempFolder()
        {
            //临时文件夹 .\\Temp
            string tempFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Temp");
            if (Directory.Exists(tempFolder))
                Directory.Delete(tempFolder, true);
        }

        /// <summary>
        /// 获取CPU型号
        /// </summary>
        /// <returns></returns>
        public static string GetCPUModel()
        {
            string cpuModel = "";
            ManagementClass mc = new("Win32_Processor");
            ManagementObjectCollection moc = mc.GetInstances();
            foreach (ManagementObject mo in moc.Cast<ManagementObject>())
            {
                // 获取CPU的名称，这通常包含了型号信息
                cpuModel = mo["Name"]?.ToString() ?? "型号未知";
                break; // 通常只需要第一个CPU的信息
            }
            return cpuModel;
        }

        /// <summary>
        /// 获取显卡型号
        /// </summary>
        /// <returns></returns>
        public static string GetGPUName()
        {
            try
            {
                ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController");
                string gpuList = "";

                foreach (ManagementObject obj in searcher.Get())
                {
                    gpuList += obj["Name"].ToString() + "\n";
                }

                return string.IsNullOrEmpty(gpuList) ? "未找到GPU信息" : gpuList.TrimEnd();
            }
            catch (Exception ex)
            {
                return $"获取GPU信息出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 输出系统信息
        /// </summary>
        /// <param name="dm"></param>
        /// <returns></returns>
        public static string GetSystemInfo(dmsoft dm)
        {
            string retstr = """
                当前系统CPU：{0} {4}，显卡信息：{1}，DPI是否为100%：{2}，系统OS版本：{3}
                """;
            string CpuType = dm.GetCpuType() switch
            {
                1 => "Intel",
                2 => "AMD",
                0 => "未知",
                _ => "未知"
            };
            string DisplayInfo = GetGPUName().Replace("\n", "").Replace("GameViewer Virtual Display Adapter", "").Replace("OrayIddDriver Device", "");
            int DPIisOK = dm.GetDPI();
            string Osinfo = GetOSInfo();
            return string.Format(retstr, CpuType, DisplayInfo, DPIisOK, Osinfo, GetCPUModel());
        }
    }
}