﻿using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScriptEngine.Tasks.FastTasks
{
    internal class ChouCeZhi : BaseTask
    {
        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, "抽厕纸");
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            log.Info_Green("您当前正在执行的任务是：抽厕纸；");
            //new Tests.DoubleClick_Test(log, this).Start();
            if (!WaitMainScene())
            {
                log.Error("当前不在抽卡主界面（默认主题），请重新挑战游戏场景后启动！");
                return;
            }
            Main();
        }

        private bool WaitMainScene()
        {
            var pics = Mp.Filter("主场景");
            if (pics.Wait()) return true;
            return false;
        }

        private void Main()
        {
            var pics_Con = Mp.Filter("再次召唤");
            log.Info("点击-普通召唤");
            Fast.Click(445, 612, 492, 660);
        Next:
            Sleep(1000);
            if (!pics_Con.Wait())
                if (!WaitMainScene())
                {
                    log.Error("当前不在抽卡主界面（默认主题），请重新挑战游戏场景后启动！");
                    return;
                }
            Sleep(1000);
            string st = Fast.Ocr_Local(773, 572, 875, 604);
            string now_count = st.Split("/")[0];
            log.Info("识别剩余票数：" + now_count);
            if (now_count == "" || now_count == "0")
            {
                log.Error("识别剩余票数为0或为空，无法继续进行抽厕纸！结束任务...");
                return;
            }
            Sleep(666);
            log.Info("点击-再次召唤");
            Fast.Click(707, 628, 838, 663);
            goto Next;
        }
    }
}