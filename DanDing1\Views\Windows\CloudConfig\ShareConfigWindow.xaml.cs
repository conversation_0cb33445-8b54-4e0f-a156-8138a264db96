using System.Windows.Controls;

namespace DanDing1.Views.Windows
{
    /// <summary>
    /// ShareConfigWindow.xaml 的交互逻辑
    /// </summary>
    public partial class ShareConfigWindow : Window
    {
        public ShareConfigWindow(string defaultName = "")
        {
            InitializeComponent();

            // 设置默认配置名称
            ConfigNameTextBox.Text = defaultName;
            ConfigNameTextBox.Focus();
            ConfigNameTextBox.SelectAll();
        }

        public string ConfigName { get; private set; } = string.Empty;
        public int ExpiryDays { get; private set; } = 0;
        public bool IsConfirmed { get; private set; } = false;

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            IsConfirmed = false;
            DialogResult = false;
        }

        private void ShareButton_Click(object sender, RoutedEventArgs e)
        {
            // 验证配置名称不能为空
            if (string.IsNullOrWhiteSpace(ConfigNameTextBox.Text))
            {
                MessageBox.Show("请输入配置名称", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                ConfigNameTextBox.Focus();
                return;
            }

            string configNameText = ConfigNameTextBox.Text.Trim();

            // 验证配置名称不能包含系统默认配置的特征字符串
            if (configNameText.Contains("|上次运行"))
            {
                MessageBox.Show("配置名称不能包含\"|上次运行\"字样，这是系统保留的命名格式", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                ConfigNameTextBox.Focus();
                return;
            }

            ConfigName = configNameText;

            // 获取有效期天数
            if (ExpiryComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                if (selectedItem.Tag != null && int.TryParse(selectedItem.Tag.ToString(), out int days))
                {
                    ExpiryDays = days;
                }
            }

            IsConfirmed = true;
            DialogResult = true;
        }
    }
}