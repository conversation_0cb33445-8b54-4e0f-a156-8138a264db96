﻿using DamoControlKit.Model;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Security.Cryptography.X509Certificates;
using System.IO;
using XHelper;

namespace DamoControlKit
{
    public static class DamoKit
    {
        /// <summary>
        /// 是否已经处于注册成功状态
        /// True:已经注册 False:没有注册
        /// </summary>
        public static bool IsReg
        {
            get
            {
                CheckCOMobj();
                return _isReg;
            }
            set => _isReg = value;
        }

        private static bool _isReg = false;

        [DllImport(@"/runtimes/DmReg.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int SetDllPathA(string path, int mode);

        /// <summary>
        /// 注册Dll
        /// </summary>
        /// <param name="errorStr">错误描述字符串</param>
        /// <returns></returns>
        private static bool InitDll(out string errorStr)
        {
            string path = System.Environment.CurrentDirectory;
            errorStr = "";

            // 使用 Task.Run 创建并运行一个任务，强制将其设置为 MTA
            return Task.Run(() =>
            {
                // 如果是Windows,设置当前线程为 MTA
                //if (OperatingSystem.IsWindows())
                //    Thread.CurrentThread.SetApartmentState(ApartmentState.MTA);

                //Debug.WriteLine("[InitDll] MTA 线程开始，当前 ApartmentState: " + Thread.CurrentThread.GetApartmentState());
                int res = SetDllPathA(path + @"\runtimes\dm.dll", 1);
                if (res != 1)
                    throw new Exception("错误原因，插件注册失败：" + res);
                //Debug.WriteLine("[InitDll] MTA 线程结束");
                return true;
            }).Result;
        }

        /// <summary>
        /// 获取注册错误的描述匹配
        /// </summary>
        /// <param name="ret_Code">错误ID</param>
        /// <returns>错误描述</returns>
        public static string GetRegError(int ret_Code)
        {
            Dictionary<int, string> ret_Dic = new()
            {
                { -1, "无法连接网络,(可能防火墙拦截, 如果可以正常访问大漠插件网站，那就可以肯定是被防火墙拦截)" },
                { -2, "进程没有以管理员方式运行. (出现在win7 win8 vista 2008.建议关闭uac)" },
                { 0, "失败(未知错误)" },
                { 1, "成功" },
                { 2, "余额不足" },
                { 3, "绑定了本机器，但是账户余额不足50元." },
                { 4, "注册码错误" },
                { 5, "你的机器或者IP在黑名单列表中或者不在白名单列表中." },
                { 6, "非法使用插件.一般出现在定制插件时，使用了和绑定的用户名不同的注册码.也有可能是系统的语言设置不是中文简体,也可能有这个错误." },
                { 7, "你的帐号因为非法使用被封禁. （如果是在虚拟机中使用插件，必须使用Reg或者RegEx，不能使用RegNoMac或者RegExNoMac,否则可能会造成封号，或者封禁机器）" },
                { 8, "ver_info不在你设置的附加白名单中." },
                { 77, "机器码或者IP因为非法使用，而被封禁. （如果是在虚拟机中使用插件，必须使用Reg或者RegEx，不能使用RegNoMac或者RegExNoMac,否则可能会造成封号，或者封禁机器）" },
                { 777, "同一个机器码注册次数超过了服务器限制,被暂时封禁.请登录后台，插件今日详细消费记录里，相应的机器码是否有次数异常，并立刻优化解决.如果还有问题，可以联系我来解决." },
                { -8, "版本附加信息长度超过了20" },
                { -9, "版本附加信息里包含了非法字母." },
                { -10, "非法的参数ip" }
            };
            return ret_Dic?[ret_Code] ?? "UnKnown Error ID";
        }

        /// <summary>
        /// 获取最后错误的描述匹配
        /// </summary>
        /// <param name="ret_Code">错误ID</param>
        /// <returns>错误描述</returns>
        public static string GetLastErrorStr(int ret_Code)
        {
            Dictionary<int, string> ret_Dic = new Dictionary<int, string>
            {
                { -1, "表示你使用了绑定里的收费功能，但是没注册，无法使用." },
                { -2, "使用模式0 2时出现，因为目标窗口有保护. 常见于win7以上系统.或者有安全软件拦截插件.解决办法: 关闭所有安全软件，然后再重新尝试. 如果还不行就可以肯定是目标窗口有特殊保护." },
                { -3, "使用模式0 2时出现，可能目标窗口有保护，也可能是异常错误. 可以尝试换绑定模式或许可以解决." },
                { -4, "使用模式101 103时出现，这是异常错误." },
                { -5, "使用模式101 103时出现, 这个错误的解决办法就是关闭目标窗口，重新打开再绑定即可. 也可能是运行脚本的进程没有管理员权限." },
                { -11, "使用模式101 103时出现, 目标进程有保护. 告诉我解决。" },
                { -6, "被安全软件拦截。典型的是金山.360等. 如果是360关闭即可。 如果是金山，必须卸载，关闭是没用的." },
                { -7, "使用模式101 103时出现,异常错误. 还有可能是安全软件的问题，比如360等。尝试卸载360." },
                { -9, "使用模式101 103时出现,异常错误. 还有可能是安全软件的问题，比如360等。尝试卸载360." },
                { -8, "使用模式101 103时出现, 目标进程可能有保护,也可能是插件版本过老，试试新的或许可以解决. -8可以尝试使用DmGuard中的np2盾配合." },
                { -10, "使用模式101 103时出现, 目标进程可能有保护,也可能是插件版本过老，试试新的或许可以解决. -8可以尝试使用DmGuard中的np2盾配合." },
                { -12, "使用模式101 103时出现, 目标进程有保护. 告诉我解决。" },
                { -13, "使用模式101 103时出现, 目标进程有保护. 或者是因为上次的绑定没有解绑导致。 尝试在绑定前调用ForceUnBindWindow. " },
                { -37, "使用模式101 103时出现, 目标进程有保护. 告诉我解决。" },
                { -14, "可能系统缺少部分DLL,尝试安装d3d. 或者是鼠标或者键盘使用了dx.mouse.api或者dx.keypad.api，但实际系统没有插鼠标和键盘. 也有可能是图色中有dx.graphic.3d之类的,但相应的图色被占用,比如全屏D3D程序." },
                { -16, "可能使用了绑定模式 0 和 101，然后可能指定了一个子窗口.导致不支持.可以换模式2或者103来尝试. 另外也可以考虑使用父窗口或者顶级窗口.来避免这个错误。还有可能是目标窗口没有正常解绑 然后再次绑定的时候." },
                { -17, "模式101 103时出现. 这个是异常错误. 告诉我解决." },
                { -18, "句柄无效." },
                { -19, "使用模式0 11 101时出现,这是异常错误,告诉我解决." },
                { -20, "使用模式101 103 时出现,说明目标进程里没有解绑，并且子绑定达到了最大. 尝试在返回这个错误时，调用ForceUnBindWindow来强制解除绑定." },
                { -21, "使用模式任何模式时出现,说明目标进程已经存在了绑定(没有正确解绑就退出了?被其它软件绑定?,或者多个线程同时进行了绑定?). 尝试在返回这个错误时，调用ForceUnBindWindow来强制解除绑定.或者检查自己的代码." },
                { -22, "使用模式0 2,绑定64位进程窗口时出现,因为安全软件拦截插件释放的EXE文件导致." },
                { -23, "使用模式0 2,绑定64位进程窗口时出现,因为安全软件拦截插件释放的DLL文件导致." },
                { -24 , "使用模式0 2,绑定64位进程窗口时出现,因为安全软件拦截插件运行释放的EXE." },
                { -25 , "使用模式0 2,绑定64位进程窗口时出现,因为安全软件拦截插件运行释放的EXE." },
                { -26 , "使用模式0 2,绑定64位进程窗口时出现, 因为目标窗口有保护. 常见于win7以上系统.或者有安全软件拦截插件.解决办法: 关闭所有安全软件，然后再重新尝试. 如果还不行就可以肯定是目标窗口有特殊保护." },
                { -27 , "绑定64位进程窗口时出现，因为使用了不支持的模式，目前暂时只支持模式0 2 11 13 101 103" },
                { -28 , "绑定32位进程窗口时出现，因为使用了不支持的模式，目前暂时只支持模式0 2 11 13 101 103" },
                { -38 , "是用了大于2的绑定模式,并且使用了dx.public.inject.c时，分配内存失败. 可以考虑开启memory系列盾来尝试" },
                { -39 , "是用了大于2的绑定模式,并且使用了dx.public.inject.c时的异常错误. 可以联系我解决." },
                { -40 , "是用了大于2的绑定模式,并且使用了dx.public.inject.c时, 写入内存失败. 可以考虑开启memory系列盾来尝试." },
                { -41 , "是用了大于2的绑定模式,并且使用了dx.public.inject.c时的异常错误. 可以联系我解决." },
                { -42 , "绑定时,创建映射内存失败. 这是个异常错误. 一般不会出现. 如果出现了，检查下代码是不是有同个对象同时绑定的情况.还有可能是你的进程有句柄泄露导致无法创建句柄会出这个错误." },
                { -43 , "绑定时,映射内存失败. 这是个异常错误. 一般不会出现. 如果出现了，一般是你的进程内存不足,检查下你的进程是不是内存泄漏了. " },
                { -44 , "无效的参数,通常是传递了不支持的参数." },
                { -45 , "绑定时,创建互斥信号失败. 这个是一场错误. 一般不会出现. 如果出现了检查进程是否有句柄泄漏的情况." },
                { -100, "调用读写内存函数后，发现无效的窗口句柄" },
                { -101, "读写内存函数失败" },
                { -200, "AsmCall失败" },
                { -202, "AsmCall平台兼容问题.联系我解决." }
            };
            return ret_Dic?[ret_Code] ?? "UnKnown Error ID";
        }

        /// <summary>
        /// 全局保留的一个dmsoft对象
        /// </summary>
        private static dmsoft _dmsoft = null;

        /// <summary>
        /// 注册插件
        /// </summary>
        /// <param name="code">注册码</param>
        /// <param name="errorStr">错误字符串</param>
        /// <returns></returns>
        public static bool Reg(string code, out string errorStr, string fujia = "")
        {
            CheckCOMobj();
            if (IsReg)
            {
                errorStr = "";
                return true;
            }
            try
            {
                if (!InitDll(out errorStr))
                    return false;
                dmsoft dm = new();
                XLogger.Debug("插件注册免注册完成，版本号：" + dm.Ver());

                int ret_int;
                ret_int = dm.RegEx(code, fujia, "121.204.249.29|121.204.253.161|125.77.165.62|125.77.165.131");
                XLogger.Debug("插件收费注册完成，返回值：" + ret_int);
                if (ret_int is 1 or 2 or 3)
                {
                    dm.ReleaseObj();
                    GC.Collect();
                    IsReg = true;
                    errorStr = "";
                    return true;
                }
                _dmsoft = dm;
                errorStr = GetRegError(ret_int);
                XLogger.Debug("插件注册失败值：" + errorStr);
                return false;
            }
            catch (Exception ex)
            {
                // 捕获COM类未注册异常
                if (ex.Message.Contains("80040154") || ex.Message.Contains("REGDB_E_CLASSNOTREG"))
                {
                    XLogger.Error("发生COM组件未注册异常: " + ex.Message);
                    XLogger.Error("请直接调用 TryManualRegisterAndRegAsync 方法再试一次");
                }

                // 记录详细的异常堆栈
                XLogger.Error("注册过程中发生异常: " + ex.Message);
                XLogger.Error("异常堆栈: " + ex.StackTrace);

                errorStr = "注册失败: " + ex.Message;
                return false;
            }
        }

        private static Type obj = null;
        private static object obj_object = null;

        private static void CheckCOMobj()
        {
            dmsoft? dm1 = null;
            try
            {
                dm1 = new();
            }
            catch (Exception ex)
            {
                //Debug.WriteLine("大漠插件未注册", ex);
                //XLogger.SaveException(ex);
                //XLogger.Debug("大漠插件检测到未注册，尝试重新注册...");
                IsReg = false;
                _dmsoft?.ReleaseObj();
                _dmsoft = null;

                // 记录COM注册失败的详细信息
                if (ex.Message.Contains("80040154") || ex.Message.Contains("REGDB_E_CLASSNOTREG"))
                {
                    XLogger.Error("大漠插件COM组件未注册: " + ex.Message);
                    XLogger.Error("请直接调用 TryManualRegisterAndRegAsync 方法再试一次");
                }
                else
                {
                    Debug.WriteLine("大漠插件初始化失败: " + ex.Message);
                }
            }
            dm1?.ReleaseObj();
        }

        /// <summary>
        /// 手动注册DLL方法
        /// </summary>
        /// <param name="dllPath">DLL的完整路径，默认为当前目录下的runtimes/dm.dll</param>
        /// <returns>注册是否成功</returns>
        public static bool ManualRegisterDll(string dllPath = "")
        {
            try
            {
                if (string.IsNullOrEmpty(dllPath))
                {
                    dllPath = Path.Combine(Environment.CurrentDirectory, "runtimes", "dm.dll");
                }

                if (!File.Exists(dllPath))
                {
                    XLogger.Error($"找不到DLL文件: {dllPath}");
                    return false;
                }

                XLogger.Debug($"正在手动注册DLL: {dllPath}");

                // 使用regsvr32注册DLL
                Process process = new Process();
                process.StartInfo.FileName = "regsvr32.exe";
                process.StartInfo.Arguments = $"/s \"{dllPath}\"";
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.CreateNoWindow = true;
                process.StartInfo.Verb = "runas"; // 请求管理员权限

                process.Start();
                process.WaitForExit();

                int exitCode = process.ExitCode;
                if (exitCode == 0)
                {
                    XLogger.Debug("DLL手动注册成功");
                    return true;
                }
                else
                {
                    XLogger.Error($"DLL手动注册失败，退出代码: {exitCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"手动注册DLL时发生异常: {ex.Message}");
                XLogger.Error("请尝试以管理员身份运行命令提示符，并执行: regsvr32 " + (string.IsNullOrEmpty(dllPath) ?
                    Path.Combine(Environment.CurrentDirectory, "runtimes", "dm.dll") : dllPath));
                return false;
            }
        }

        public static async Task<bool> RegAsync(string code, string fujia = "")
        {
            CheckCOMobj();
            if (IsReg)
                return true;

            if (!InitDll(out string errorStr))
                throw new Exception(errorStr);

            return await Task.Run<bool>(() =>
            {
                try
                {
                    dmsoft dm = new();
                    XLogger.Debug("插件注册免注册完成，版本号：" + dm.Ver());

                    int ret_int;
                    ret_int = dm.RegEx(code, fujia, "121.204.252.143|121.204.253.161|125.77.165.62|125.77.165.131");
                    XLogger.Debug("插件收费注册完成，返回值：" + ret_int);
                    if (ret_int is 1 or 2 or 3)
                    {
                        _dmsoft = dm;
                        //dm.ReleaseObj();
                        //GC.Collect();
                        IsReg = true;
                        return true;
                    }
                    //_dmsoft = dm;
                    errorStr = GetRegError(ret_int);
                    XLogger.Debug("插件注册失败值：" + errorStr);
                    throw new Exception(errorStr);
                }
                catch (Exception ex)
                {
                    // 捕获COM类未注册异常
                    if (ex.Message.Contains("80040154") || ex.Message.Contains("REGDB_E_CLASSNOTREG"))
                    {
                        XLogger.Error("发生COM组件未注册异常: " + ex.Message);
                        XLogger.Error("请直接调用 TryManualRegisterAndRegAsync 方法再试一次");
                    }

                    // 记录详细的异常堆栈
                    XLogger.Error("注册过程中发生异常: " + ex.Message);
                    XLogger.Error("异常堆栈: " + ex.StackTrace);

                    throw; // 重新抛出异常以便上层处理
                }
            });
        }

        // 静态对象用于线程同步
        private static readonly object _bindLock = new object();

        /// <summary>
        /// 绑定窗口
        /// </summary>
        /// <param name="bindModel">绑定对象</param>
        /// <param name="errorStr">错误描述字符串</param>
        /// <returns>X对象</returns>
        /// <exception cref="Exception">绑定发生的错误</exception>
        public static dmsoft BindHwnd(BindModel bindModel, out string errorStr)
        {
            // 使用lock语句确保同一时间只有一个线程能执行绑定操作
            lock (_bindLock)
            {
                if (!IsReg)
                    throw new Exception("Program Error[1]：您并没有注册插件！");

                if (!bindModel.IsReady)
                    throw new Exception("绑定发生错误：句柄已失效，请重新获取句柄！");

                dmsoft x = new();
                int ret = x.BindWindowEx(bindModel.Hwnd,
                    Enum.GetName(typeof(Display), bindModel.Display) ?? "dx2",
                    (Enum.GetName(typeof(Mouse), bindModel.Mouse))?.Replace('_', '.') ?? "windows",
                    Enum.GetName(typeof(Keypad), bindModel.Keypad) ?? "windows",
                    bindModel.Public,
                    bindModel.Mode);
                if (ret != 1)
                {
                    errorStr = GetLastErrorStr(x.GetLastError());
                    throw new Exception("绑定发生错误：" + errorStr);
                }

                errorStr = "";
                return x;
            }
        }

        /// <summary>
        /// 获取一个X对象
        /// </summary>
        /// <returns>X对象</returns>
        public static dmsoft GetXsoft()
        {
            if (!IsReg)
                throw new Exception("Program Error[1]：您并没有注册插件！");
            return new();
        }

        /// <summary>
        /// 使用已有的dmsoft对象绑定窗口
        /// </summary>
        /// <param name="dm">已存在的dmsoft对象</param>
        /// <param name="bindModel">绑定对象</param>
        /// <param name="errorStr">错误描述字符串</param>
        /// <returns>绑定是否成功</returns>
        /// <exception cref="Exception">绑定发生的错误</exception>
        public static bool BindHwndWithObject(dmsoft dm, BindModel bindModel, out string errorStr)
        {
            // 使用lock语句确保同一时间只有一个线程能执行绑定操作
            lock (_bindLock)
            {
                if (!IsReg)
                    throw new Exception("Program Error[1]：您并没有注册插件！");

                if (!bindModel.IsReady)
                    throw new Exception("绑定发生错误：句柄已失效，请重新获取句柄！");

                int ret = dm.BindWindowEx(bindModel.Hwnd,
                    Enum.GetName(typeof(Display), bindModel.Display) ?? "dx2",
                    (Enum.GetName(typeof(Mouse), bindModel.Mouse))?.Replace('_', '.') ?? "windows",
                    Enum.GetName(typeof(Keypad), bindModel.Keypad) ?? "windows",
                    bindModel.Public,
                    bindModel.Mode);
                if (ret != 1)
                {
                    errorStr = GetLastErrorStr(dm.GetLastError());
                    throw new Exception("绑定发生错误：" + errorStr);
                }

                errorStr = "";
                return true;
            }
        }

        /// <summary>
        /// 尝试手动注册并重新注册插件
        /// </summary>
        /// <param name="code">注册码</param>
        /// <param name="fujia">附加参数</param>
        /// <returns>注册是否成功</returns>
        public static async Task<bool> TryManualRegisterAndRegAsync(string code, string fujia = "")
        {
            try
            {
                // 先尝试手动注册DLL
                XLogger.Debug("尝试手动注册DLL并重新注册插件...");
                bool registerResult = ManualRegisterDll();

                if (!registerResult)
                {
                    XLogger.Error("手动注册DLL失败，无法继续注册过程");
                    return false;
                }

                // 手动注册成功后，重新尝试注册插件
                XLogger.Debug("手动注册DLL成功，正在重新注册插件...");

                // 重置注册状态
                IsReg = false;
                _dmsoft?.ReleaseObj();
                _dmsoft = null;

                // 重新注册
                return await RegAsync(code, fujia);
            }
            catch (Exception ex)
            {
                XLogger.Error("尝试手动注册并重新注册插件时发生异常: " + ex.Message);
                XLogger.Error("异常堆栈: " + ex.StackTrace);
                return false;
            }
        }
    }
}