﻿<UserControl x:Class="DanDing1.Views.UserControls.AddTaskControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:DanDing1.Views.UserControls"
             xmlns:localcs="clr-namespace:DanDing1.ViewModels.Pages"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:mystr="clr-namespace:DanDing1.Resources"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:converters="clr-namespace:DanDing1.Helpers"
             d:DataContext="{d:DesignInstance local:AddTaskControl, IsDesignTimeCreatable=True}"
             ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
             ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             ScrollViewer.CanContentScroll="False"
             mc:Ignorable="d">
    <UserControl.Resources>
        <localcs:ViewVisibilityConverter x:Key="ViewVisibilityConverter"/>
        <localcs:MultiStringConcatConverter x:Key="MultiStringConcatConverter"/>
        <converters:InfoBarSeverityConverter x:Key="InfoBarSeverityConverter"/>

        <!-- 卡片样式 -->
        <Style x:Key="CardStyle"
               TargetType="Border">
            <Setter Property="Background"
                    Value="{DynamicResource ControlFillColorDefaultBrush}"/>
            <Setter Property="BorderBrush"
                    Value="{DynamicResource ControlElevationBorderBrush}"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="CornerRadius"
                    Value="8"/>
        </Style>

        <!-- 卡片标题样式 -->
        <Style x:Key="CardTitleStyle"
               TargetType="TextBlock">
            <Setter Property="FontSize"
                    Value="16"/>
            <Setter Property="FontWeight"
                    Value="SemiBold"/>
            <Setter Property="Margin"
                    Value="0,0,0,8"/>
        </Style>
    </UserControl.Resources>
    <Grid x:Name="NomalMode1"
          Margin="5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="5"/>
            <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>
        <Border
            x:Name="NomalMode2"
            Width="650"
            Height="235"
            Style="{StaticResource CardStyle}">
            <TabControl>
                <TabControl.Template>
                    <ControlTemplate TargetType="TabControl">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <ScrollViewer
                                Grid.Row="0"
                                HorizontalScrollBarVisibility="Auto"
                                PreviewMouseWheel="ScrollViewer_PreviewMouseWheel"
                                VerticalScrollBarVisibility="Disabled">
                                <TabPanel
                                    Name="HeaderPanel"
                                    Margin="2,-2,2,2"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Bottom"
                                    Panel.ZIndex="1"
                                    IsItemsHost="True"
                                    KeyboardNavigation.TabIndex="1"
                                    SnapsToDevicePixels="true"/>
                            </ScrollViewer>
                            <ContentPresenter Grid.Row="1"
                                              ContentSource="SelectedContent"/>
                        </Grid>
                    </ControlTemplate>
                </TabControl.Template>
                <!--  全局  -->
                <TabItem FrameworkElement.Width="35">
                    <HeaderedContentControl.Header>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       Text="全局"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <StackPanel Orientation="Horizontal">
                        <StackPanel Margin="10,10,0,0">
                            <StackPanel Margin="0,5,0,0"
                                        Orientation="Horizontal">
                                <TextBlock VerticalAlignment="Center"
                                           Text="庭院皮肤："/>
                                <ComboBox
                                    x:Name="GameScene_ComboBox"
                                    ItemsSource="{Binding Game_SceneLists}"
                                    SelectedItem="{Binding Game_Scene, Mode=TwoWay}"/>
                                <ui:ToggleSwitch
                                    x:Name="XShang_Status"
                                    Margin="40,5,0,0"
                                    OffContent="不接受悬赏"
                                    OnContent="接受悬赏"
                                    IsChecked="{Binding XShangChecked, Mode=TwoWay}"/>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    Margin="10,5,5,0">
                            <Border
                                BorderThickness="1"
                                CornerRadius="4"
                                Padding="5,0,0,0"
                                Height="220"
                                Width="250">
                                <StackPanel>
                                    <TextBlock
                                        Margin="0,5,0,0"
                                        FontSize="15"
                                        FontWeight="Black"
                                        Text="注意事项："/>
                                    <TextBlock
                                        Margin="5,8,5,5"
                                        FontSize="15"
                                        Text="{x:Static mystr:MyString.GameTip}"
                                        TextWrapping="Wrap"/>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock
                                            Margin='0 -1 0 0'
                                            FontSize="15"
                                            FontWeight="Black"
                                            Text="软件使用文档："/>
                                        <ui:HyperlinkButton
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="0 -6 0 0"
                                            Content="文档链接"
                                            NavigateUri="https://www.danding.vip"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </TabItem>
                <!--  半自动  -->
                <TabItem Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                         FrameworkElement.Width="48">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       Text="半自动"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <TabControl>
                        <!--半自动模式-->
                        <TabItem Height="30"
                                 Width="75">
                            <HeaderedContentControl.Header>
                                <StackPanel StackPanel.Orientation="Horizontal">
                                    <TextBlock  FontSize="12"
                                                TextBlock.Text="半自动模式"/>
                                </StackPanel>
                            </HeaderedContentControl.Header>
                            <Grid>
                                <StackPanel Margin="10 0 0 0">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock
                                            FrameworkElement.Margin="0,5,0,0"
                                            TextBlock.FontSize="15"
                                            TextBlock.FontWeight="Black"
                                            TextBlock.Text="注意事项（半自动）："/>
                                    </StackPanel>
                                    <TextBlock
                                        FrameworkElement.Margin="5,8,5,5"
                                        TextBlock.FontSize="15"
                                        TextBlock.Text="{x:Static mystr:MyString.Fast_BanZdTip}"
                                        TextBlock.TextWrapping="Wrap"/>
                                </StackPanel>

                                <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                            FrameworkElement.Margin="10">
                                    <CheckBox IsChecked="{Binding Bzidong_Biaoji}"
                                              FontSize="16"
                                              Content="启用道馆标记（五号位）"
                                              ToolTipService.ToolTip="开启后默认标记5号位 (从左往右第五个)"/>
                                    <TextBlock x:Name="ResourcesTip"
                                               FontSize="16"
                                               Text="当前用户自己的图片数量：5张"/>
                                    <StackPanel Margin="0 5 0 0"
                                                HorizontalAlignment="Center"
                                                Orientation="Horizontal">
                                        <ui:Button x:Name="OpenResources"
                                                   Click="OpenResources_Click"
                                                   Content="打开资源目录"/>
                                        <ui:Button x:Name="ReloadResources"
                                                   Click="ReloadResources_Click"
                                                   Margin="5 0 0 0"
                                                   Content="重载"/>
                                    </StackPanel>
                                </StackPanel>


                                <StackPanel HorizontalAlignment="Right"
                                            Orientation="Horizontal"
                                            VerticalAlignment="Bottom">
                                    <StackPanel Margin="10"
                                                Orientation="Horizontal">
                                        <TextBlock VerticalAlignment="Center"
                                                   FontSize="16"
                                                   Text="定时结束时间："/>
                                        <TextBox Text="{Binding Bzidong_AutoEndTime, Mode=TwoWay}"
                                                 ToolTipService.ToolTip="设置后，半自动任务将在指定时间后自动结束（单位：分钟）"/>
                                        <TextBlock VerticalAlignment="Center"
                                                   Margin="3,0,0,0"
                                                   Text="分钟"/>
                                    </StackPanel>
                                    <Button
                                        Margin="10"
                                        IsEnabled="{Binding StartButtonEnabled}"
                                        Command="{Binding FastStartCommand}"
                                        CommandParameter="半自动"
                                        ToolTip="快速启动半自动任务！">
                                        <StackPanel Orientation="Horizontal">
                                            <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                                            <TextBlock Text="开始执行"
                                                       Margin="5 0 0 0"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </TabItem>
                        <!--每周秘闻-->
                        <TabItem Height="30"
                                 Width="60">
                            <HeaderedContentControl.Header>
                                <StackPanel StackPanel.Orientation="Horizontal">
                                    <TextBlock FontSize="12"
                                               TextBlock.Text="每周秘闻"/>
                                </StackPanel>
                            </HeaderedContentControl.Header>
                            <Grid>
                                <StackPanel Margin="10 0 0 0">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock
                                            FrameworkElement.Margin="0,5,0,0"
                                            TextBlock.FontSize="15"
                                            TextBlock.FontWeight="Black"
                                            TextBlock.Text="注意事项（每周秘闻）："/>
                                    </StackPanel>
                                    <TextBlock
                                        FrameworkElement.Margin="5,8,5,5"
                                        TextBlock.FontSize="15"
                                        TextBlock.Text="{x:Static mystr:MyString.Fast_MiWenTip}"
                                        TextBlock.TextWrapping="Wrap"/>
                                </StackPanel>
                                <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                            FrameworkElement.Margin="0,5,5,0">
                                    <CheckBox IsChecked="{Binding MeiZhouMiWen_Biaoji}"
                                              Content="启用标记"
                                              ToolTipService.ToolTip="开启后默认标记5号位 (从左往右第五个)"/>
                                </StackPanel>
                                <StackPanel HorizontalAlignment="Right"
                                            Orientation="Horizontal"
                                            VerticalAlignment="Bottom">
                                    <Button
                                        Margin="10"
                                        IsEnabled="{Binding StartButtonEnabled}"
                                        Command="{Binding FastStartCommand}"
                                        CommandParameter="每周秘闻"
                                        ToolTip="快速启动半自动任务！">
                                        <StackPanel Orientation="Horizontal">
                                            <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                                            <TextBlock Text="开始执行"
                                                       Margin="5 0 0 0"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </TabItem>
                        <!--抽厕纸-->
                        <TabItem Height="30"
                                 Width="50">
                            <HeaderedContentControl.Header>
                                <StackPanel StackPanel.Orientation="Horizontal">
                                    <TextBlock  FontSize="12"
                                                TextBlock.Text="抽厕纸"/>
                                </StackPanel>
                            </HeaderedContentControl.Header>
                            <Grid>
                                <StackPanel Margin="10 0 0 0">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock
                                            FrameworkElement.Margin="0,5,0,0"
                                            TextBlock.FontSize="15"
                                            TextBlock.FontWeight="Black"
                                            TextBlock.Text="注意事项（抽厕纸）："/>
                                    </StackPanel>
                                    <TextBlock
                                        FrameworkElement.Margin="5,8,5,5"
                                        TextBlock.FontSize="15"
                                        TextBlock.Text="{x:Static mystr:MyString.Fast_CeZhiTip}"
                                        TextBlock.TextWrapping="Wrap"/>
                                </StackPanel>
                                <StackPanel HorizontalAlignment="Right"
                                            Orientation="Horizontal"
                                            VerticalAlignment="Bottom">
                                    <Button
                                        Margin="10"
                                        IsEnabled="{Binding StartButtonEnabled}"
                                        Command="{Binding FastStartCommand}"
                                        CommandParameter="抽厕纸"
                                        ToolTip="快速启动半自动任务！">
                                        <StackPanel Orientation="Horizontal">
                                            <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                                            <TextBlock Text="开始执行"
                                                       Margin="5 0 0 0"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </TabItem>
                    </TabControl>
                </TabItem>
                <!--  御魂  -->
                <TabItem FrameworkElement.Width="35">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       TextBlock.Text="御魂"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">
                        <StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="御魂战斗层数："/>
                                <ComboBox
                                    x:Name="Yhun_Level"
                                    ItemsControl.ItemsSource="{Binding Yhun_LevelLists}"
                                    Selector.SelectedItem="{Binding Yhun_Level}"/>
                                <CheckBox Content="自动启停Buff (针对单人)"
                                          IsChecked="{Binding Yhun_AutoBuff}"/>
                            </StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="御魂战斗次数："/>
                                <TextBox TextBox.Text="{Binding Yhun_Count, Mode=TwoWay}"/>
                            </StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <CheckBox
                                    Margin=" -10 0 0 0"
                                    x:Name="Yhun_ZuDui"
                                    ContentControl.Content="组队模式"
                                    FrameworkElement.VerticalAlignment="Center"
                                    ToggleButton.IsChecked="{Binding Yhun_ZuDui_IsChecked}"/>
                                <ComboBox
                                    x:Name="Yhun_ZuDui_Location"
                                    Margin="-15,0,0,0"
                                    IsEnabled="{Binding ElementName=Yhun_ZuDui, Path=IsChecked}"
                                    ItemsSource="{Binding Yhun_ZuDui_Locations}"
                                    SelectedItem="{Binding Yhun_ZuDui_Location}"/>
                                <Button
                                    x:Name="Yhun_ZuDui_SelectButton"
                                    Margin="5,0,0,0"
                                    Command="{Binding SelectYHZuDuiNameCommand}"
                                    Content="选择队友"/>
                                <TextBlock
                                    FrameworkElement.Margin="5,0,0,0"
                                    FrameworkElement.VerticalAlignment="Center"
                                    TextBlock.Text="{Binding Yhun_ZuDui_Name}"/>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                    FrameworkElement.Margin="0,5,5,0">
                            <CheckBox IsChecked="{Binding Yhun_Biaoji}"
                                      Content="启用标记"
                                      ToolTipService.ToolTip="开启后默认标记5号位 (从左往右第五个)"/>
                            <!--御魂-任务应用预设-->
                            <ComboBox x:Name="YuShe_YuHun"
                                      SelectedItem="{Binding Yhun_YuShe}"
                                      DropDownOpened="YuShe_YuHun_DropDownOpened"
                                      MaxWidth="150"
                                      ToolTipService.ShowDuration="5000"
                                      ToolTip="{Binding ElementName=YuShe_YuHun, Path=SelectedItem}"
                                      Loaded="YuShe_YuHun_Loaded">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}"
                                                   TextTrimming="CharacterEllipsis"
                                                   ToolTip="{Binding}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Bottom">
                            <Button
                                Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                Margin="0 0 3 0"
                                IsEnabled="{Binding StartButtonEnabled}"
                                Command="{Binding FastStartCommand}"
                                CommandParameter="御魂"
                                ToolTip="快速启动当前设置完参数的单个任务！">
                                <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                            </Button>
                            <Button
                                Command="{Binding AddaskCommand}"
                                CommandParameter="御魂"
                                Content="添加当前任务"/>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  突破  -->
                <TabItem FrameworkElement.Width="35">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       TextBlock.Text="突破"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">
                        <StackPanel>
                            <StackPanel Margin="0,5,0,0"
                                        Orientation="Horizontal">
                                <TextBlock VerticalAlignment="Center"
                                           TextBlock.Text="个人突破胜利次数："/>
                                <TextBox Text="{Binding Tpo_Count, Mode=TwoWay}"/>
                                <CheckBox Content="卡56级"
                                          IsChecked="{Binding Tpo_Tui4}"
                                          ToolTipService.ToolTip="开启后最后一个目标默认失败4次"/>
                                <CheckBox Margin="-30 0 0 0"
                                          Content="刷新前等待确认"
                                          IsChecked="{Binding Tpo_Affirm}"
                                          ToolTipService.ToolTip="执行刷新操作前，等待您检查确认后，待您确认完成即可继续脚本.."/>
                            </StackPanel>
                            <CheckBox  IsChecked="{Binding Tpo_RunLtu}"
                                       Margin="-10 5 0 0"
                                       Content="个人突破结束时，继续打寮突 ↓"/>
                            <StackPanel  FrameworkElement.Margin="0,5,0,0"
                                         StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="寮突胜利次数："/>
                                <TextBox TextBox.Text="{Binding LTpo_Count, Mode=TwoWay}"/>
                                <Button
                                    Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                    Height="35.7"
                                    Margin="5 0 0 0"
                                    IsEnabled="{Binding StartButtonEnabled}"
                                    Command="{Binding FastStartCommand}"
                                    CommandParameter="寮突"
                                    ToolTip="快速启动当前设置完参数的单个任务！">
                                    <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                                </Button>
                                <Button
                                    Command="{Binding AddaskCommand}"
                                    CommandParameter="寮突"
                                    Content="仅添加寮突"
                                    Height="35.7"
                                    Margin="3,0,0,0"/>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                    FrameworkElement.Margin="0,5,5,0">

                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <ComboBox ItemsSource="{Binding Tpo_Biaojis}"
                                          SelectedItem="{Binding Tpo_Biaoji}"/>
                                <!--突破-任务应用预设-->
                                <ComboBox Margin="5 0 0 0"
                                          x:Name="YuShe_Tpo"
                                          SelectedItem="{Binding Tpo_YuShe}"
                                          DropDownOpened="YuShe_YuHun_DropDownOpened"
                                          MaxWidth="150"
                                          ToolTipService.ShowDuration="5000"
                                          ToolTip="{Binding ElementName=YuShe_Tpo, Path=SelectedItem}"
                                          Loaded="YuShe_YuHun_Loaded">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding}"
                                                       TextTrimming="CharacterEllipsis"
                                                       ToolTip="{Binding}"/>
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Bottom">
                            <Button
                                Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                Margin="0 0 3 0"
                                IsEnabled="{Binding StartButtonEnabled}"
                                Command="{Binding FastStartCommand}"
                                CommandParameter="突破"
                                ToolTip="快速启动当前设置完参数的单个任务！">
                                <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                            </Button>
                            <Button
                                Command="{Binding AddaskCommand}"
                                CommandParameter="突破"
                                Content="仅添加个突任务"/>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  探索  -->
                <TabItem FrameworkElement.Width="35">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       TextBlock.Text="探索"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">
                        <StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="K28探索战斗次数："/>
                                <TextBox TextBox.Text="{Binding Tsuo_Count, Mode=TwoWay}"/>
                                <ui:ToggleSwitch
                                    Margin="10,0,0,0"
                                    IsChecked="{Binding Tsuo_Counting_Mode}"
                                    OffContent="任务以小怪+Boss计数"
                                    OnContent="任务以仅Boss计数"/>
                            </StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="战斗目标选择："/>
                                <ComboBox ItemsControl.ItemsSource="{Binding Tsuo_CombatStrLists}"
                                          Selector.SelectedItem="{Binding Tsuo_CombatStr}"/>
                                <CheckBox Content="自动启停Buff (针对单人)"
                                          IsChecked="{Binding Tsuo_AutoBuff}"/>
                            </StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <CheckBox
                                    Margin="-10 0 0 0"
                                    x:Name="TSuo_ZuDui"
                                    ContentControl.Content="组队模式"
                                    FrameworkElement.VerticalAlignment="Center"
                                    ToggleButton.IsChecked="{Binding TSuo_ZuDui_IsChecked}"/>
                                <ComboBox
                                    x:Name="TSuo_ZuDui_Location"
                                    Margin="-15,0,0,0"
                                    IsEnabled="{Binding ElementName=TSuo_ZuDui, Path=IsChecked}"
                                    ItemsSource="{Binding TSuo_ZuDui_Locations}"
                                    SelectedItem="{Binding TSuo_ZuDui_Location}"/>
                                <Button
                                    x:Name="TSuo_ZuDui_SelectButton"
                                    Margin="5,0,0,0"
                                    Command="{Binding SelectTSuoZuDuiNameCommand}"
                                    Content="选择队友"/>
                                <TextBlock
                                    FrameworkElement.Margin="5,0,0,0"
                                    FrameworkElement.VerticalAlignment="Center"
                                    TextBlock.Text="{Binding TSuo_ZuDui_Name}"/>
                            </StackPanel>
                        </StackPanel>

                        <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                    FrameworkElement.Margin="0,5,5,0">

                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <!--探索-任务应用预设-->
                                <ComboBox Margin="5 0 0 0"
                                          x:Name="YuShe_TSuo"
                                          SelectedItem="{Binding TSuo_YuShe}"
                                          DropDownOpened="YuShe_YuHun_DropDownOpened"
                                          MaxWidth="150"
                                          ToolTipService.ShowDuration="5000"
                                          ToolTip="{Binding ElementName=YuShe_TSuo, Path=SelectedItem}"
                                          Loaded="YuShe_YuHun_Loaded">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding}"
                                                       TextTrimming="CharacterEllipsis"
                                                       ToolTip="{Binding}"/>
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                            </StackPanel>
                        </StackPanel>

                        <StackPanel HorizontalAlignment="Right"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Bottom">
                            <Button
                                Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                Margin="0 0 3 0"
                                IsEnabled="{Binding StartButtonEnabled}"
                                Command="{Binding FastStartCommand}"
                                CommandParameter="探索"
                                ToolTip="快速启动当前设置完参数的单个任务！">
                                <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                            </Button>
                            <Button
                                ButtonBase.Command="{Binding AddaskCommand}"
                                ButtonBase.CommandParameter="探索"
                                ContentControl.Content="添加当前任务"/>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  御灵  -->
                <TabItem FrameworkElement.Width="35">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       TextBlock.Text="御灵"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">
                        <StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="御灵战斗类别："/>
                                <ComboBox ItemsControl.ItemsSource="{Binding Yling_ClassLists}"
                                          Selector.SelectedItem="{Binding Yling_Class}"/>
                            </StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="御灵战斗次数："/>
                                <TextBox TextBox.Text="{Binding Yling_Count, Mode=TwoWay}"/>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                    FrameworkElement.Margin="0,5,5,0">
                            <CheckBox IsChecked="{Binding Yling_Biaoji}"
                                      Content="启用标记"
                                      ToolTipService.ToolTip="开启后默认标记5号位 (从左往右第五个)"/>
                            <!--御灵-任务应用预设-->
                            <ComboBox x:Name="YuShe_Yling"
                                      SelectedItem="{Binding Yling_YuShe}"
                                      DropDownOpened="YuShe_YuHun_DropDownOpened"
                                      MaxWidth="150"
                                      ToolTipService.ShowDuration="5000"
                                      ToolTip="{Binding ElementName=YuShe_Yling, Path=SelectedItem}"
                                      Loaded="YuShe_YuHun_Loaded">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}"
                                                   TextTrimming="CharacterEllipsis"
                                                   ToolTip="{Binding}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Bottom">
                            <Button
                                Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                Margin="0 0 3 0"
                                IsEnabled="{Binding StartButtonEnabled}"
                                Command="{Binding FastStartCommand}"
                                CommandParameter="御灵"
                                ToolTip="快速启动当前设置完参数的单个任务！">
                                <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                            </Button>
                            <Button
                                ButtonBase.Command="{Binding AddaskCommand}"
                                ButtonBase.CommandParameter="御灵"
                                ContentControl.Content="添加当前任务"/>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  斗技  -->
                <TabItem FrameworkElement.Width="35">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       TextBlock.Text="斗技"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">
                        <StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="斗技胜利次数："/>
                                <TextBox TextBox.Text="{Binding Dji_Count, Mode=TwoWay}"/>
                            </StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <CheckBox Margin="-10 0 0 0"
                                          Content="积分打满结束"
                                          IsChecked="{Binding Dji_ManTing}"/>
                                <CheckBox Margin="-10 0 0 0"
                                          Content="段位积分≥3k分结束"
                                          IsChecked="{Binding Dji_MingShi}"/>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                    FrameworkElement.Margin="0,5,5,0">
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="标记开关："/>
                                <ComboBox ItemsSource="{Binding Dji_Biaojis}"
                                          SelectedItem="{Binding Dji_Biaoji}"/>
                                <!--斗技-任务应用预设-->
                                <ComboBox Margin="5 0 0 0"
                                          x:Name="YuShe_Dji"
                                          SelectedItem="{Binding Dji_YuShe}"
                                          DropDownOpened="YuShe_YuHun_DropDownOpened"
                                          MaxWidth="150"
                                          ToolTipService.ShowDuration="5000"
                                          ToolTip="{Binding ElementName=YuShe_Dji, Path=SelectedItem}"
                                          Loaded="YuShe_YuHun_Loaded">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding}"
                                                       TextTrimming="CharacterEllipsis"
                                                       ToolTip="{Binding}"/>
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Bottom">
                            <Button
                                Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                Margin="0 0 3 0"
                                IsEnabled="{Binding StartButtonEnabled}"
                                Command="{Binding FastStartCommand}"
                                CommandParameter="斗技"
                                ToolTip="快速启动当前设置完参数的单个任务！">
                                <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                            </Button>
                            <Button
                                ButtonBase.Command="{Binding AddaskCommand}"
                                ButtonBase.CommandParameter="斗技"
                                ContentControl.Content="添加当前任务"/>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  英杰  -->
                <TabItem FrameworkElement.Width="35">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       TextBlock.Text="英杰"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">
                        <StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="英杰试炼战斗类别："/>
                                <ComboBox ItemsControl.ItemsSource="{Binding Yjie_ClassLists}"
                                          Selector.SelectedItem="{Binding Yjie_Class}"/>
                            </StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="英杰试炼战斗次数："/>
                                <TextBox TextBox.Text="{Binding Yjie_Count, Mode=TwoWay}"/>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                    FrameworkElement.Margin="0,5,5,0">
                            <CheckBox IsChecked="{Binding Yjie_Biaoji}"
                                      Content="启用标记"
                                      ToolTipService.ToolTip="开启后默认标记5号位 (从左往右第五个)"/>
                            <!--英杰-任务应用预设-->
                            <ComboBox x:Name="YingJie_Yling"
                                      SelectedItem="{Binding Yjie_YuShe}"
                                      DropDownOpened="YuShe_YuHun_DropDownOpened"
                                      MaxWidth="150"
                                      ToolTipService.ShowDuration="5000"
                                      ToolTip="{Binding ElementName=YingJie_Yling, Path=SelectedItem}"
                                      Loaded="YuShe_YuHun_Loaded">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}"
                                                   TextTrimming="CharacterEllipsis"
                                                   ToolTip="{Binding}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Bottom">
                            <Button
                                Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                Margin="0 0 3 0"
                                IsEnabled="{Binding StartButtonEnabled}"
                                Command="{Binding FastStartCommand}"
                                CommandParameter="英杰"
                                ToolTip="快速启动当前设置完参数的单个任务！">
                                <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                            </Button>
                            <Button
                                ButtonBase.Command="{Binding AddaskCommand}"
                                ButtonBase.CommandParameter="英杰"
                                ContentControl.Content="添加当前任务"/>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  百鬼  -->
                <TabItem FrameworkElement.Width="35">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       TextBlock.Text="百鬼"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">
                        <StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="百鬼次数："/>
                                <TextBox TextBox.Text="{Binding Bgui_Count, Mode=TwoWay}"/>
                                <CheckBox IsChecked="{Binding Bgui_Inv,Mode=TwoWay}"
                                          Content="邀请队友"
                                          ToolTip="默认第一个队友"/>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                    FrameworkElement.Margin="0,5,5,0">
                            <ui:TextBlock Text="百鬼注意点："
                                          FontWeight="Black"/>
                            <ui:TextBlock Text="1. 不涉及内存读取、修改操作；"/>
                            <ui:TextBlock Text="2. 并非专业百鬼砸豆脚本；"/>
                            <ui:TextBlock Text="3. 请勿单次次数设置过大；"/>
                            <ui:TextBlock Text="4. Ai砸豆需检测yolo功能是否可用；"/>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Bottom">
                            <Button
                                Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                Margin="0 0 3 0"
                                IsEnabled="{Binding StartButtonEnabled}"
                                Command="{Binding FastStartCommand}"
                                CommandParameter="百鬼"
                                ToolTip="快速启动当前设置完参数的单个任务！">
                                <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                            </Button>
                            <Button
                                ButtonBase.Command="{Binding AddaskCommand}"
                                ButtonBase.CommandParameter="百鬼"
                                ContentControl.Content="添加当前任务"/>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  六道  -->
                <TabItem FrameworkElement.Width="35">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       TextBlock.Text="六道"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">
                        <StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="六道战斗次数："/>
                                <TextBox TextBox.Text="{Binding Liudao_Count, Mode=TwoWay}"/>
                            </StackPanel>
                            <StackPanel Margin="0,5,0,0"
                                        Orientation="Horizontal">
                                <CheckBox Margin="-10 0 0 0"
                                          Content="星之屿打达摩"
                                          IsChecked="{Binding Liudao_XingZhiDao_Damo}"/>
                                <CheckBox Margin="-10 0 0 0"
                                          Content="混沌不开宝箱"
                                          IsChecked="{Binding Liudao_HunDunBuKaiXiang}"/>
                            </StackPanel>
                            <StackPanel Margin="0,-5,0,0"
                                        Orientation="Horizontal">
                                <CheckBox Margin="-10 0 0 0"
                                          Content="使用双倍加成(极时)"
                                          IsChecked="{Binding Liudao_ShuangBeiJiaCheng}"/>
                                <CheckBox Margin="-10 0 0 0"
                                          Content="双倍加成不足时购买加成"
                                          IsChecked="{Binding Liudao_ShuangBeiJiaCheng_Buy}"/>
                            </StackPanel>

                            <StackPanel  Orientation="Horizontal">
                                <TextBlock VerticalAlignment="Center"
                                           Text="额外技能选择(低练度)："/>
                                <CheckBox Content="妖力化生"
                                          IsChecked="{Binding Liudao_Extra_Skill_YL}"/>
                                <CheckBox Margin="-25 0 0 0"
                                          Content="细雨化屏"
                                          IsChecked="{Binding Liudao_Extra_Skill_XY}"/>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    VerticalAlignment="Top"
                                    Margin="0,5,5,0"
                                    Orientation="Horizontal">
                            <CheckBox Margin="0 0 -10 0"
                                      IsChecked="{Binding Liudao_Biaoji}"
                                      Content="启用标记"
                                      ToolTipService.ToolTip="开启后默认标记5号位 (从左往右第五个)"/>
                            <!--六道-任务应用预设-->
                            <ComboBox x:Name="YuShe_Liudao"
                                      SelectedItem="{Binding Liudao_YuShe}"
                                      DropDownOpened="YuShe_YuHun_DropDownOpened"
                                      MaxWidth="150"
                                      ToolTipService.ShowDuration="5000"
                                      ToolTip="{Binding ElementName=YuShe_Liudao, Path=SelectedItem}"
                                      Loaded="YuShe_YuHun_Loaded">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}"
                                                   TextTrimming="CharacterEllipsis"
                                                   ToolTip="{Binding}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Bottom">
                            <Button
                                Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                Margin="0 0 3 0"
                                IsEnabled="{Binding StartButtonEnabled}"
                                Command="{Binding FastStartCommand}"
                                CommandParameter="六道"
                                ToolTip="快速启动当前设置完参数的单个任务！">
                                <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                            </Button>
                            <Button
                                ButtonBase.Command="{Binding AddaskCommand}"
                                ButtonBase.CommandParameter="六道"
                                ContentControl.Content="添加当前任务"/>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  契灵  -->
                <TabItem FrameworkElement.Width="35">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       TextBlock.Text="契灵"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">
                        <StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="契灵战斗次数："/>
                                <TextBox TextBox.Text="{Binding Qiling_Count, Mode=TwoWay}"/>
                                <TextBlock
                                    FrameworkElement.Margin="10,0,0,0"
                                    FrameworkElement.VerticalAlignment="Center"
                                    TextBlock.Text="任务战斗类型："/>
                                <ComboBox
                                    ItemsControl.ItemsSource="{Binding Qiling_TaskTypeLists}"
                                    Selector.SelectedItem="{Binding Qiling_TaskType}"/>
                            </StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <CheckBox Margin="-10 0 0 0"
                                          Content="没有鸣契石时购买"
                                          IsChecked="{Binding Qiling_AutoBuySummon}"/>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                    FrameworkElement.Margin="0,5,5,0">
                            <CheckBox IsChecked="{Binding Qiling_Biaoji}"
                                      Content="启用标记"
                                      ToolTipService.ToolTip="开启后默认标记5号位 (从左往右第五个)"/>
                            <!--契灵-任务应用预设-->
                            <ComboBox x:Name="YuShe_Qiling"
                                      SelectedItem="{Binding Qiling_YuShe}"
                                      DropDownOpened="YuShe_YuHun_DropDownOpened"
                                      MaxWidth="150"
                                      ToolTipService.ShowDuration="5000"
                                      ToolTip="{Binding ElementName=YuShe_Qiling, Path=SelectedItem}"
                                      Loaded="YuShe_YuHun_Loaded">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}"
                                                   TextTrimming="CharacterEllipsis"
                                                   ToolTip="{Binding}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Bottom">
                            <Button
                                Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                Margin="0 0 3 0"
                                IsEnabled="{Binding StartButtonEnabled}"
                                Command="{Binding FastStartCommand}"
                                CommandParameter="契灵"
                                ToolTip="快速启动当前设置完参数的单个任务！">
                                <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                            </Button>
                            <Button
                                ButtonBase.Command="{Binding AddaskCommand}"
                                ButtonBase.CommandParameter="契灵"
                                ContentControl.Content="添加当前任务"/>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  日常  -->
                <TabItem FrameworkElement.Width="35">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       FontWeight="Black"
                                       TextBlock.Text="日常"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0">
                                <CheckBox Margin="0 -4 0 0"
                                          FontWeight="Black"
                                          IsChecked="{Binding DailyTask_DiGui}"
                                          Content="每日地域鬼王"/>
                                <CheckBox Margin="0 -4 0 0"
                                          FontWeight="Black"
                                          IsChecked="{Binding DailyTask_ChouKa}"
                                          Content="每日一抽"/>
                                <CheckBox Margin="0 -4 0 0"
                                          FontWeight="Black"
                                          IsChecked="{Binding DailyTask_Befriendly}"
                                          Content="友情点领取"/>
                                <CheckBox Margin="0 -4 0 0"
                                          FontWeight="Black"
                                          IsChecked="{Binding DailyTask_Reward}"
                                          Content="做悬赏"/>
                                <CheckBox Margin="0 -4 0 0"
                                          FontWeight="Black"
                                          IsChecked="{Binding DailyTask_FreeGift}"
                                          Content="每日商店免费礼包"
                                          ToolTip="位置在：商店-礼物包-推荐-免费中！"/>
                                <CheckBox Margin="0 -4 0 0"
                                          FontWeight="Black"
                                          IsChecked="{Binding DailyTask_Checkin}"
                                          Content="每日签到"
                                          ToolTip="如果检测签字时，会执行签到.."/>
                            </StackPanel>
                            <StackPanel Grid.Column="1">
                                <CheckBox Margin="0 -4 0 0"
                                          FontWeight="Black"
                                          IsChecked="{Binding DailyTask_Liao30SE}"
                                          Content="寮材料提交"/>
                            </StackPanel>
                        </Grid>

                        <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                    FrameworkElement.Margin="0,5,5,0">
                            <!--日常-任务应用预设-->
                            <ComboBox x:Name="YuShe_RiChang"
                                      SelectedItem="{Binding RChang_YuShe}"
                                      DropDownOpened="YuShe_YuHun_DropDownOpened"
                                      MaxWidth="150"
                                      ToolTipService.ShowDuration="5000"
                                      ToolTip="{Binding ElementName=YuShe_RiChang, Path=SelectedItem}"
                                      Loaded="YuShe_YuHun_Loaded">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}"
                                                   TextTrimming="CharacterEllipsis"
                                                   ToolTip="{Binding}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                            <ui:Button HorizontalAlignment="Right"
                                       Command="{Binding DayilyOpenAllCommand}"
                                       Margin="0 5 0 5"
                                       Content="全部勾选"/>
                            <ui:Button HorizontalAlignment="Right"
                                       Command="{Binding ShowTipCommand}"
                                       CommandParameter="日常"
                                       Content="日常注意"/>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Bottom">
                            <Button
                                Command="{Binding AddaskCommand}"
                                CommandParameter="日常"
                                ContentControl.Content="应用到当前任务"/>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  定时  -->
                <TabItem FrameworkElement.Width="35">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       FontWeight="Black"
                                       TextBlock.Text="定时"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">

                        <StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <CheckBox Margin="0 -8 0 0"
                                          IsChecked="{Binding Timer_OffLine}"
                                          FontWeight="Black"
                                          Content="定时任务值守时关闭模拟器，执行任务时自动开启模拟器；"/>
                            </StackPanel>

                            <StackPanel Orientation="Horizontal">
                                <CheckBox Margin="0 -4 0 0"
                                          FontWeight="Black"
                                          IsChecked="{Binding Timer_JiYang}"
                                          Content="自动寄养"/>
                                <ui:TextBlock Margin="-18 0 0 0"
                                              VerticalAlignment="Center"
                                              Text="优先顺序："/>
                                <ComboBox ItemsSource="{Binding Timer_JiYang_PriorityLists}"
                                          SelectedItem="{Binding Timer_JiYang_Priority}"/>
                                <CheckBox IsChecked="{Binding Timer_JiYang_PassLowLevel}"
                                          Content="忽略≤3星结界卡"/>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <ui:TextBlock Margin="42 3 0 0"
                                              VerticalAlignment="Center"
                                              FontWeight="Black"
                                              Text="指定好友寄养(为空则不启用)"/>
                                <ComboBox Margin="5 3 0 0"
                                          ItemsSource="{Binding Timer_JiYang_DesignatedTacticsLists}"
                                          SelectedItem="{Binding Timer_JiYang_DesignatedTactics}"/>
                                <ui:TextBlock Margin="5 3 0 0"
                                              VerticalAlignment="Center"
                                              Text="指定好友名："/>
                                <TextBox MaxWidth="100"
                                         Text="{Binding Timer_JiYang_DesignatedName}"/>

                                <!--<ui:Button Margin="10 2 0 0" Height="28" Width="70" Command="{Binding ShowFriendListCommand}" Content="选择" />-->
                                <!--<ui:TextBlock Margin="5 3 0 0" VerticalAlignment="Center" Text="已选择：" />
                                <ui:TextBlock Margin="5 3 0 0" VerticalAlignment="Center" Text="{Binding Timer_JiYang_DesignatedName}" />-->
                            </StackPanel>
                            <StackPanel Margin="0 7 0 0 "
                                        Orientation="Horizontal">
                                <CheckBox Margin="0 -1 0 0"
                                          FontWeight="Black"
                                          IsChecked="{Binding Timer_FangKa}"
                                          Content="自动放卡"/>
                                <ui:TextBlock Margin="-18 0 0 0"
                                              VerticalAlignment="Center"
                                              Text="类型："/>
                                <ComboBox ItemsSource="{Binding Timer_FangKa_ClassLists}"
                                          SelectedItem="{Binding Timer_FangKa_Class}"/>
                                <ui:TextBlock Margin="5 0 0 0"
                                              VerticalAlignment="Center"
                                              Text="等级："/>
                                <ComboBox ItemsSource="{Binding Timer_FangKa_LevelLists}"
                                          SelectedItem="{Binding Timer_FangKa_Level}"/>
                                <ui:TextBlock Margin="5 0 0 0"
                                              VerticalAlignment="Center"
                                              Text="最多滑动次数："/>
                                <TextBox MaxWidth="100"
                                         Text="{Binding Timer_FangKa_HuaDongCount}"/>
                            </StackPanel>
                        </StackPanel>

                        <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                    FrameworkElement.Margin="0,5,5,0">
                            <ui:Button Command="{Binding ShowTipCommand}"
                                       CommandParameter="寄养"
                                       Content="寄养&#x0A;注意"/>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Bottom">
                            <Button
                                Command="{Binding AddaskCommand}"
                                CommandParameter="定时"
                                ContentControl.Content="应用到当前任务"/>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  活动  -->
                <TabItem FrameworkElement.Width="48">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <ui:SymbolIcon Margin="-3 0 0 0"
                                           Foreground="Red"
                                           Symbol="Fire16"/>
                            <TextBlock Foreground="Red"
                                       TextBlock.Text="活动"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">
                        <StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="月曜永恒活动 - 战斗次数："/>
                                <TextBox TextBox.Text="{Binding HDong_Count, Mode=TwoWay}"/>
                                <ComboBox Margin="5 0 0 0"
                                          ItemsSource="{Binding HDong_ClassLists}"
                                          SelectedItem="{Binding HDong_Class}"/>
                                <!--<ui:ToggleSwitch Margin="10,0,0,0" IsChecked="{Binding HDong_Fake_Mode}" OffContent="使用时间之砂战斗" OnContent="使用体力战斗" />-->
                                <!--<CheckBox Margin="10,0,0,0"
                                Content="花札满50提前结束"
                                IsChecked="{Binding HDong_FirstEnd}" />-->
                            </StackPanel>
                            <TextBlock Margin="0 10 0 0"
                                       FrameworkElement.VerticalAlignment="Center"
                                       FontWeight="Black"
                                       Foreground="Red"
                                       FontSize="16"
                                       TextBlock.Text="注意点提醒(5.15日)："/>
                            <TextBlock FrameworkElement.VerticalAlignment="Center"
                                       Foreground="Red"
                                       TextBlock.Text="1、本分类中的任务会定期更新！"/>
                            <TextBlock FrameworkElement.VerticalAlignment="Center"
                                       Foreground="Red"
                                       TextBlock.Text="2、一定请提前配好阵容后开始，为了防止动画卡住脚本！"/>
                            <TextBlock FrameworkElement.VerticalAlignment="Center"
                                       Foreground="Red"
                                       TextBlock.Text="3、如果当前时间不在活动时间内，则无法添加任务！"/>
                        </StackPanel>
                        <StackPanel HorizontalAlignment="Right"
                                    Margin="0,5,5,0">
                            <CheckBox IsChecked="{Binding HDong_Biaoji}"
                                      Content="启用标记"
                                      ToolTipService.ToolTip="开启后默认标记5号位 (从左往右第五个)"/>
                            <!--活动-任务应用预设-->
                            <ComboBox x:Name="YuShe_HDong"
                                      SelectedItem="{Binding HDong_YuShe}"
                                      DropDownOpened="YuShe_YuHun_DropDownOpened"
                                      MaxWidth="150"
                                      ToolTipService.ShowDuration="5000"
                                      ToolTip="{Binding ElementName=YuShe_HDong, Path=SelectedItem}"
                                      Loaded="YuShe_YuHun_Loaded">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}"
                                                   TextTrimming="CharacterEllipsis"
                                                   ToolTip="{Binding}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>
                        <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                    FrameworkElement.VerticalAlignment="Bottom">
                            <!--<StackPanel Orientation="Horizontal">
                            <TextBlock Text="只战斗" Margin="0 0 3 0" VerticalAlignment="Center" />
                            <TextBox  Text="{Binding HDong_Fake_Time}" />
                            <TextBlock Margin="3 0 5 0" Text="分钟" VerticalAlignment="Center" />
                            <Button
                            Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                            Margin="2 0 0 2"
                            IsEnabled="{Binding StartButtonEnabled}"
                            Command="{Binding FastStartCommand}"
                            CommandParameter="活动"
                            ToolTip="快速启动当前设置完参数的单个任务！">
                            <StackPanel Orientation="Horizontal">
                            <TextBlock Margin="0 0 3 0"
                            Text="半自动Boss战" />
                            <ui:SymbolIcon Symbol="AirplaneTakeOff16" />
                            </StackPanel>
                            </Button>
                            </StackPanel>-->
                            <Button
                                HorizontalAlignment="Right"
                                ButtonBase.Command="{Binding AddaskCommand}"
                                ButtonBase.CommandParameter="活动"
                                ContentControl.Content="添加当前任务 "/>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  其它  -->
                <TabItem FrameworkElement.Width="35">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       Text="其它"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10,5,5,5">
                        <StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock
                                    FrameworkElement.VerticalAlignment="Center"
                                    TextBlock.FontSize="16"
                                    TextBlock.FontWeight="Black"
                                    TextBlock.Text="觉醒| "/>
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="战斗次数："/>
                                <TextBox TextBox.Text="{Binding Jxing_Count, Mode=TwoWay}"/>
                                <ComboBox
                                    FrameworkElement.Margin="5,0,0,0"
                                    ItemsControl.ItemsSource="{Binding Jxing_ClassLists}"
                                    Selector.SelectedItem="{Binding Jxing_Class}"/>
                                <Button
                                    ButtonBase.Command="{Binding AddaskCommand}"
                                    ButtonBase.CommandParameter="觉醒"
                                    ContentControl.Content="添加"
                                    FrameworkElement.Height="35.7"
                                    FrameworkElement.Margin="5,0,0,0"/>
                                <CheckBox Content="自动启停Buff"
                                          IsChecked="{Binding Jxing_AutoBuff}"/>
                                <Button
                                    Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                    Height="35.7"
                                    Margin="3 0 0 0"
                                    IsEnabled="{Binding StartButtonEnabled}"
                                    Command="{Binding FastStartCommand}"
                                    CommandParameter="觉醒"
                                    ToolTip="快速启动当前设置完参数的单个任务！">
                                    <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                                </Button>
                            </StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock
                                    FrameworkElement.VerticalAlignment="Center"
                                    TextBlock.FontSize="16"
                                    TextBlock.FontWeight="Black"
                                    TextBlock.Text="业原火| "/>
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="战斗次数："/>
                                <TextBox TextBox.Text="{Binding Yyhuo_Count, Mode=TwoWay}"/>
                                <Button
                                    ButtonBase.Command="{Binding AddaskCommand}"
                                    ButtonBase.CommandParameter="业原火"
                                    ContentControl.Content="添加"
                                    FrameworkElement.Height="35.7"
                                    FrameworkElement.Margin="5,0,0,0"/>
                                <Button
                                    Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                    Height="35.7"
                                    Margin="3 0 0 0"
                                    IsEnabled="{Binding StartButtonEnabled}"
                                    Command="{Binding FastStartCommand}"
                                    CommandParameter="业原火"
                                    ToolTip="快速启动当前设置完参数的单个任务！">
                                    <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                                </Button>
                            </StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock
                                    FrameworkElement.VerticalAlignment="Center"
                                    TextBlock.FontSize="16"
                                    TextBlock.FontWeight="Black"
                                    TextBlock.Text="日轮之陨| "/>
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="战斗次数："/>
                                <TextBox TextBox.Text="{Binding Rlun_Count, Mode=TwoWay}"/>
                                <Button
                                    ButtonBase.Command="{Binding AddaskCommand}"
                                    ButtonBase.CommandParameter="日轮"
                                    ContentControl.Content="添加"
                                    FrameworkElement.Height="35.7"
                                    FrameworkElement.Margin="5,0,0,0"/>
                                <Button
                                    Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                    Height="35.7"
                                    Margin="3 0 0 0"
                                    IsEnabled="{Binding StartButtonEnabled}"
                                    Command="{Binding FastStartCommand}"
                                    CommandParameter="日轮"
                                    ToolTip="快速启动当前设置完参数的单个任务！">
                                    <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                                </Button>
                            </StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock
                                    FrameworkElement.VerticalAlignment="Center"
                                    TextBlock.FontSize="16"
                                    TextBlock.FontWeight="Black"
                                    TextBlock.Text="永生之海| "/>
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="战斗次数："/>
                                <TextBox TextBox.Text="{Binding Ysheng_Count, Mode=TwoWay}"/>
                                <Button
                                    ButtonBase.Command="{Binding AddaskCommand}"
                                    ButtonBase.CommandParameter="永生"
                                    ContentControl.Content="添加"
                                    FrameworkElement.Height="35.7"
                                    FrameworkElement.Margin="5,0,0,0"
                                    IsEnabled="False"/>
                                <!--IsEnabled="{Binding StartButtonEnabled}"-->
                                <Button
                                    Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=False}"
                                    IsEnabled="False"
                                    Height="35.7"
                                    Margin="3 0 0 0"
                                    Command="{Binding FastStartCommand}"
                                    CommandParameter="永生"
                                    ToolTip="快速启动当前设置完参数的单个任务！">
                                    <ui:SymbolIcon Symbol="AirplaneTakeOff16"/>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                    FrameworkElement.Margin="0,5,5,0">
                            <CheckBox IsChecked="{Binding Qta_Biaoji}"
                                      Content="启用标记"
                                      ToolTipService.ToolTip="开启后默认标记5号位 (从左往右第五个)"/>
                            <!--其它-任务应用预设-->
                            <ComboBox x:Name="YuShe_Qta"
                                      SelectedItem="{Binding Qta_YuShe}"
                                      DropDownOpened="YuShe_YuHun_DropDownOpened"
                                      MaxWidth="150"
                                      ToolTipService.ShowDuration="5000"
                                      ToolTip="{Binding ElementName=YuShe_Qta, Path=SelectedItem}"
                                      Loaded="YuShe_YuHun_Loaded">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}"
                                                   TextTrimming="CharacterEllipsis"
                                                   ToolTip="{Binding}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  Buff  -->
                <TabItem FrameworkElement.Width="38">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       TextBlock.Text="Buff"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">
                        <StackPanel>
                            <RadioButton
                                x:Name="Open_Buff"
                                ContentControl.Content="开启Buff"
                                FrameworkElement.VerticalAlignment="Center"
                                ToggleButton.IsChecked="True"/>
                            <StackPanel
                                FrameworkElement.Margin="20,0,0,0"
                                StackPanel.Orientation="Horizontal"
                                UIElement.IsEnabled="{Binding ElementName=Open_Buff, Path=IsChecked}">
                                <CheckBox x:Name="Buff_jb"
                                          ContentControl.Content="金币"/>
                                <CheckBox x:Name="Buff_jy"
                                          ContentControl.Content="经验"/>
                            </StackPanel>
                            <StackPanel
                                FrameworkElement.Margin="20,0,0,0"
                                StackPanel.Orientation="Horizontal"
                                UIElement.IsEnabled="{Binding ElementName=Open_Buff, Path=IsChecked}">
                                <CheckBox x:Name="Buff_jx"
                                          ContentControl.Content="觉醒"/>
                                <CheckBox
                                    x:Name="Buff_yh"
                                    ContentControl.Content="御魂"
                                    ToggleButton.IsChecked="True"/>
                            </StackPanel>
                            <RadioButton
                                x:Name="Close_Buff"
                                ContentControl.Content="关闭所有Buff"
                                FrameworkElement.Margin="0,15,0,0"
                                FrameworkElement.VerticalAlignment="Center"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Bottom">
                            <TextBlock x:Name="OnlyNextTip"
                                       MouseDown="OnlyNextTip_MouseDown"
                                       Cursor="Hand"
                                       Margin="0 0 -6 0"
                                       VerticalAlignment="Center"
                                       Text="仅用于下一个任务"/>
                            <CheckBox Margin="0 0 -5 0"
                                      x:Name="OnlyNext"
                                      Content=""/>
                            <Button Command="{Binding AddaskCommand}"
                                    Content="添加Buff任务">
                                <ButtonBase.CommandParameter>
                                    <MultiBinding Converter="{StaticResource MultiStringConcatConverter}">
                                        <Binding Source="Buff|"/>
                                        <Binding ElementName="Open_Buff"
                                                 Path="IsChecked"/>
                                        <Binding Source="|"/>
                                        <Binding ElementName="Buff_jb"
                                                 Path="IsChecked"/>
                                        <Binding ElementName="Buff_jy"
                                                 Path="IsChecked"/>
                                        <Binding ElementName="Buff_jx"
                                                 Path="IsChecked"/>
                                        <Binding ElementName="Buff_yh"
                                                 Path="IsChecked"/>
                                        <Binding Source="|"/>
                                        <Binding ElementName="Close_Buff"
                                                 Path="IsChecked"/>
                                        <Binding Source="|"/>
                                        <Binding ElementName="OnlyNext"
                                                 Path="IsChecked"/>
                                    </MultiBinding>
                                </ButtonBase.CommandParameter>
                            </Button>
                        </StackPanel>
                    </Grid>
                </TabItem>
                <!--  等待  -->
                <TabItem FrameworkElement.Width="38">
                    <HeaderedContentControl.Header>
                        <StackPanel StackPanel.Orientation="Horizontal">
                            <TextBlock Margin="-2.5 0 0 0"
                                       Text="等待"/>
                        </StackPanel>
                    </HeaderedContentControl.Header>
                    <Grid FrameworkElement.Margin="10">
                        <StackPanel>
                            <StackPanel FrameworkElement.Margin="0,5,0,0"
                                        StackPanel.Orientation="Horizontal">
                                <TextBlock FrameworkElement.VerticalAlignment="Center"
                                           TextBlock.Text="等待时长（单位：分钟）："/>
                                <TextBox TextBox.Text="{Binding Delay_Time, Mode=TwoWay}"/>
                                <TextBlock
                                    FrameworkElement.Margin="10,5,0,0"
                                    FrameworkElement.VerticalAlignment="Center"
                                    TextBlock.Text="接受小数"/>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel FrameworkElement.HorizontalAlignment="Right"
                                    FrameworkElement.VerticalAlignment="Bottom">
                            <Button
                                ButtonBase.Command="{Binding AddaskCommand}"
                                ButtonBase.CommandParameter="等待"
                                ContentControl.Content="添加当前任务"/>
                        </StackPanel>
                    </Grid>
                </TabItem>
            </TabControl>
        </Border>
        <Border
            Visibility="{Binding IsSuperMode, Converter={StaticResource ViewVisibilityConverter}, ConverterParameter=True}"
            Grid.Column="2"
            Height="235"
            Width="246"
            Style="{StaticResource CardStyle}">
            <StackPanel>
                <StackPanel Margin="10,2,0,0"
                            Orientation="Horizontal">
                    <ui:TextBlock x:Name="GameName"
                                  Text="{Binding DataContext.ViewModel.SelectedGameModel.GameName, RelativeSource={RelativeSource AncestorType=Window}}"/>
                    <ui:TextBlock Margin="5 0 0 0"
                                  Text=" 任务队列："/>
                </StackPanel>

                <ui:ListView
                    x:Name="GameTaskLists"
                    Height="165"
                    ItemsSource="{Binding GameTaskLists}"
                    SelectedIndex="{Binding GameTaskListsIndex}">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <StackPanel VerticalAlignment="Center"
                                        Orientation="Horizontal">
                                <ui:TextBlock
                                    Margin="10,0,0,0"
                                    FontSize="15"
                                    FontWeight="Bold"
                                    Text="{Binding ShowName}"/>
                                <ui:TextBlock
                                    Margin="7,0,0,0"
                                    FontSize="15"
                                    Text="{Binding Count}"/>
                            </StackPanel>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ui:ListView>
                <StackPanel
                    Margin="0,5,0,0"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal">
                    <ui:Button
                        Command="{Binding MoveUpCommand}"
                        CommandParameter="{Binding ElementName=GameTaskLists, Path=SelectedIndex}"
                        Content="↑"
                        ToolTip="上移选中"/>
                    <ui:Button
                        Margin="5,0,0,0"
                        Command="{Binding MoveDownCommand}"
                        CommandParameter="{Binding ElementName=GameTaskLists, Path=SelectedIndex}"
                        Content="↓"
                        ToolTip="下移选中"/>
                    <ui:Button
                        Margin="5,0,0,0"
                        Command="{Binding DelTaskCommand}"
                        CommandParameter="{Binding ElementName=GameTaskLists, Path=SelectedIndex}"
                        Content="×"
                        ToolTip="删除选中"/>
                    <ui:Button
                        Margin="5,0,0,0"
                        Command="{Binding DelAllTaskCommand}"
                        Content="清空"
                        ToolTip="清空任务列表"/>
                    <ui:Button
                        x:Name="TasksManager"
                        Margin="5,0,0,0"
                        Command="{Binding OpenTaskPageCommand}"
                        Content="详情"
                        MouseRightButtonUp="TasksManager_MouseRightButtonUp"
                        ToolTip="打开详细任务列表，现在右键此按钮，支持快速导入任务列表哦！"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <StackPanel
            Margin="0,0,0,20"
            HorizontalAlignment="Center"
            VerticalAlignment="Bottom">
            <ui:InfoBar
                x:Name="InfoBar"
                Title="{Binding InfoBar.Title, Mode=TwoWay}"
                IsOpen="{Binding InfoBar.IsOpen, Mode=TwoWay}"
                Message="{Binding InfoBar.Message, Mode=TwoWay}"
                Severity="{Binding InfoBar.Severity, Mode=TwoWay, Converter={StaticResource InfoBarSeverityConverter}, FallbackValue=Informational}"/>
        </StackPanel>
    </Grid>
</UserControl>