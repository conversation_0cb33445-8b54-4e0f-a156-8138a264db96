﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DanDing1
{
    /// <summary>
    /// 任务开始成功后回调
    /// </summary>
    internal delegate void LogGameControl_TaskStartedCallBack();

    /// <summary>
    /// 任务结束后回调
    /// </summary>
    internal delegate void LogGameControl_TaskEndedCallBack();

    /// <summary>
    /// 设置主页超级多开的进行时开关CallBack
    /// </summary>
    internal delegate void SetRunningUICallBack(int Runningcount);
}