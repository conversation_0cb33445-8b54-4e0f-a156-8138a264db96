﻿using Emgu.CV.Dai;
using ScriptEngine.Core;
using SixLabors.ImageSharp.Processing.Processors;
using System.Drawing;
using System.Drawing.Imaging;
using XHelper;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace ScriptEngine.Model
{
    public class ScreenshotRecorder
    {
        private readonly string _cacheFilePrefix;
        private readonly string _tempFolder;

        public string _logtxt = "";

        /// <summary>
        /// 录制质量 是否压缩
        /// </summary>
        public string _recordQuality = "";

        /// <summary>
        /// 缓存文件前缀
        /// </summary>
        private int _cacheFileIndex = 0;

        /// <summary>
        /// 使用字典存储截图数据（时间作为键，图片数据作为值）
        /// </summary>
        public Dictionary<DateTime, byte[]> _screenshots;

        /// <summary>
        /// 使用字典存储日志数据（时间作为键，日志文本作为值）
        /// </summary>
        public Dictionary<DateTime, string> _logEntries;

        public ScreenshotRecorder(string recordQuality = "原生质量（不压缩）")
        {
            _screenshots = [];
            _logEntries = [];
            _tempFolder = Utils.GetTempFolder; // 获取临时目录
            Directory.CreateDirectory(_tempFolder); // 确保临时目录存在
            _recordQuality = recordQuality; //录制质量
            // 生成唯一的缓存文件前缀
            _cacheFilePrefix = $"screenshots_{DateTime.Now:yyyyMMdd_HHmmss_ffff}";

            var logsFolder = System.IO.Path.Combine(System.AppDomain.CurrentDomain.BaseDirectory, "Logs", DateTime.Now.ToString("yyyy-MM-dd"));
        }

        /// <summary>
        /// 添加截图数据
        /// </summary>
        public void Add(byte[] imageData)
        {
            // 检查是否与最后一张截图数据相同
            if (_screenshots.Count > 0 && IsImageDataEqual(_screenshots[GetLastKey()], imageData))
            {
                XLogger.Debug($"[{DateTime.Now}] Pass the same screenshot data.");
                return;
            }
            // 压缩图片 CompressImage
            imageData = CompressImage(imageData);

            // 添加新的截图数据
            DateTime currentTime = DateTime.Now;
            _screenshots.Add(currentTime, imageData);

            // 如果截图数量超过 10 张，缓存最早的 10 张图片
            if (_screenshots.Count > 10)
            {
                CacheOldestScreenshots();
            }

            // 缓存日志到 _logtxt
            try
            {
                _logtxt = XLogger.GetAllLog();
            }
            catch (Exception ex)
            {
                XLogger.Warn($"[{DateTime.Now}]ScreenshotRecorder.Add 缓存日志时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 压缩图片
        /// </summary>
        /// <param name="imageData"></param>
        /// <returns></returns>
        public byte[] CompressImage(byte[] imageData)
        {
            if (_recordQuality.Contains("原生")) return imageData;
            else if (_recordQuality.Contains("缩略图"))
            {
                //压缩
                var xip = new XImageProcessor();
                byte[] thumbnailBytes = xip.GenerateThumbnail(imageData, 500);
                return thumbnailBytes;
            }
            return imageData;
        }

        /// <summary>
        /// 删除所有截图
        /// </summary>
        public void ClearAllScreenshots()
        {
            _screenshots.Clear();
        }

        /// <summary>
        /// 实现 IDisposable 接口，清理缓存文件
        /// </summary>
        public void Dispose()
        {
            ClearAllScreenshots();
            CleanupCacheFiles();
        }

        /// <summary>
        /// 导出所有截图到指定文件夹
        /// </summary>
        public void ExportToFolder(string outputFolder)
        {
            try
            {
                // 确保输出文件夹存在
                if (!Directory.Exists(outputFolder))
                    Directory.CreateDirectory(outputFolder);

                // 加载所有缓存文件中的数据
                var allScreenshots = LoadAllCachedScreenshots();

                // 合并内存中的截图数据
                foreach (var entry in _screenshots)
                {
                    allScreenshots.Add(entry.Key, entry.Value);
                }

                // 导出所有截图
                foreach (var entry in allScreenshots)
                {
                    try
                    {
                        if (!IsValidImageData(entry.Value))
                        {
                            XLogger.Debug($"[{entry.Key}] 无效的图片数据，跳过。");
                            continue;
                        }

                        using var ms = new MemoryStream(entry.Value);
                        using var bitmap = new Bitmap(ms);

                        string fileName;
                        string filePath;

                        //判断质量
                        if (_recordQuality.Contains("原生"))
                        {
                            fileName = $"{entry.Key:yyyyMMdd_HHmmss}.bmp";
                            filePath = Path.Combine(outputFolder, fileName);
                            bitmap.Save(filePath, ImageFormat.Bmp);
                        }
                        else if (_recordQuality.Contains("缩略图"))
                        {
                            fileName = $"{entry.Key:yyyyMMdd_HHmmss}.jpg";
                            filePath = Path.Combine(outputFolder, fileName);
                            bitmap.Save(filePath, ImageFormat.Jpeg);
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Debug($"[{entry.Key}] 导出截图时发生错误：{ex.Message}");
                    }
                }
                allScreenshots = null;
                GC.Collect(); // 手动触发垃圾回收，确保内存被释放
                XLogger.Debug($"所有截图已导出到文件夹：{outputFolder}");
            }
            catch (Exception ex)
            {
                XLogger.Debug($"导出截图到文件夹时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取最早的截图时间
        /// </summary>
        /// <returns>最早的截图时间</returns>
        public DateTime GetEarliestScreenshotTime()
        {
            if (_screenshots.Count == 0)
                throw new InvalidOperationException("没有截图数据。");

            return _screenshots.Keys.Min();
        }

        /// <summary>
        /// 获取指定时间的图片数据
        /// </summary>
        /// <param name="time">指定的时间</param>
        /// <returns>对应的图片数据，如果不存在则返回 null</returns>
        public byte[]? GetImageDataByTime(DateTime time)
        {
            if (_screenshots.TryGetValue(time, out byte[]? imageData))
            {
                return imageData;
            }
            return null;
        }

        /// <summary>
        /// 获取所有截图的图片数据列表
        /// </summary>
        public List<byte[]> GetImageDataList()
        {
            return new List<byte[]>(_screenshots.Values);
        }

        /// <summary>
        /// 获取最新的截图时间
        /// </summary>
        /// <returns>最新的截图时间</returns>
        public DateTime GetLatestScreenshotTime()
        {
            if (_screenshots.Count == 0)
                throw new InvalidOperationException("没有截图数据。");

            return _screenshots.Keys.Max();
        }

        /// <summary>
        /// 获取当前图片数据的数量
        /// </summary>
        /// <returns>截图数量</returns>
        public int GetScreenshotCount()
        {
            return _screenshots.Count;
        }

        /// <summary>
        /// 获取指定时间范围内的截图数量
        /// </summary>
        /// <param name="startTime">起始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>截图数量</returns>
        public int GetScreenshotCountInRange(DateTime startTime, DateTime endTime)
        {
            return _screenshots.Count(entry => entry.Key >= startTime && entry.Key <= endTime);
        }

        /// <summary>
        /// 获取某个时间范围内的截图
        /// </summary>
        /// <param name="startTime">起始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>符合条件的截图数据（时间-图片数据字典）</returns>
        public Dictionary<DateTime, byte[]> GetScreenshotsInRange(DateTime startTime, DateTime endTime)
        {
            var result = new Dictionary<DateTime, byte[]>();
            foreach (var entry in _screenshots)
            {
                if (entry.Key >= startTime && entry.Key <= endTime)
                {
                    result.Add(entry.Key, entry.Value);
                }
            }
            return result;
        }

        /// <summary>
        /// 获取所有截图的时间范围
        /// </summary>
        /// <returns>包含最早时间和最晚时间的元组</returns>
        public (DateTime EarliestTime, DateTime LatestTime) GetScreenshotTimeRange()
        {
            if (_screenshots.Count == 0)
                throw new InvalidOperationException("没有截图数据。");

            var earliestTime = _screenshots.Keys.Min();
            var latestTime = _screenshots.Keys.Max();
            return (earliestTime, latestTime);
        }

        /// <summary>
        /// 获取所有截图的时间列表
        /// </summary>
        public List<DateTime> GetTimeList()
        {
            return new List<DateTime>(_screenshots.Keys);
        }

        /// <summary>
        /// 获取总图片数据的字节大小
        /// </summary>
        /// <returns>总字节大小</returns>
        public long GetTotalImageDataSize()
        {
            return _screenshots.Values.Sum(imageData => imageData.Length);
        }

        /// <summary>
        /// 匹配日志与截图时间
        /// </summary>
        /// <param name="logTxt">日志文本</param>
        /// <returns>匹配结果字典（截图时间 -> 日志文本）</returns>
        public Dictionary<DateTime, string> MatchLogsWithScreenshots_String(string logTxt)
        {
            var matchedLogs = new Dictionary<DateTime, string>();

            // 解析日志文本
            var logEntries = new List<(DateTime Time, string LogText)>();
            foreach (var line in logTxt.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries))
            {
                if (TryParseLogLine(line, out DateTime logTime, out string logText))
                {
                    logEntries.Add((logTime, logText));
                }
            }

            // 遍历截图时间，匹配最近的日志
            foreach (var screenshotTime in _screenshots.Keys)
            {
                // 找到与截图时间最接近的日志
                var closestLog = logEntries
                    .OrderBy(entry => Math.Abs((entry.Time - screenshotTime).Ticks))
                    .FirstOrDefault();

                // 检查时间差是否在 0.5 秒以内
                if (closestLog.LogText != null && Math.Abs((closestLog.Time - screenshotTime).TotalSeconds) <= 0.5)
                {
                    matchedLogs.Add(screenshotTime, closestLog.LogText);
                }
            }

            return matchedLogs;
        }

        /// <summary>
        /// 匹配日志与截图时间
        /// </summary>
        /// <param name="logFilePath">日志文件路径</param>
        /// <returns>匹配结果字典（截图时间 -> 日志文本）</returns>
        public Dictionary<DateTime, string> MatchLogsWithScreenshots_File(string logFilePath)
        {
            var matchedLogs = new Dictionary<DateTime, string>();

            // 读取日志文件
            var logEntries = new List<(DateTime Time, string LogText)>();
            foreach (var line in File.ReadAllLines(logFilePath))
            {
                if (TryParseLogLine(line, out DateTime logTime, out string logText))
                {
                    logEntries.Add((logTime, logText));
                }
            }

            // 遍历截图时间，匹配最近的日志
            foreach (var screenshotTime in _screenshots.Keys)
            {
                // 找到与截图时间最接近的日志
                var closestLog = logEntries
                    .OrderBy(entry => Math.Abs((entry.Time - screenshotTime).Ticks))
                    .FirstOrDefault();

                // 检查时间差是否在 1 秒以内
                if (closestLog.LogText != null && Math.Abs((closestLog.Time - screenshotTime).TotalSeconds) <= 0.5)
                {
                    matchedLogs.Add(screenshotTime, closestLog.LogText);
                }
            }

            return matchedLogs;
        }

        /// <summary>
        /// 从二进制文件读取数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        public void ReadFromFile(string filePath)
        {
            if (!File.Exists(filePath))
                return;
            _screenshots.Clear(); // 清空当前数据

            using (FileStream fs = new FileStream(filePath, FileMode.Open))
            using (BinaryReader reader = new BinaryReader(fs))
            {
                // 读取 _recordQuality
                _recordQuality = reader.ReadString();

                // 读取截图数量
                int count = reader.ReadInt32();

                // 读取每个截图数据
                for (int i = 0; i < count; i++)
                {
                    long ticks = reader.ReadInt64(); // 读取时间（Ticks）
                    DateTime time = new DateTime(ticks);

                    int dataLength = reader.ReadInt32(); // 读取图片数据长度
                    byte[] imageData = reader.ReadBytes(dataLength); // 读取图片数据

                    _screenshots.Add(time, imageData);
                }

                // 读取 _logtxt 的长度和内容
                int logTextLength = reader.ReadInt32(); // 读取日志文本的长度
                _logtxt = reader.ReadString(); // 读取日志文本
            }
            //匹配日志
            _logEntries = MatchLogsWithScreenshots_String(_logtxt);
            XLogger.Debug($"数据已从文件加载：{filePath}");

            //判断结果
            if (_screenshots.Count == 0)
                throw new InvalidOperationException("没有截图数据。或数据文件错误！");
        }

        /// <summary>
        /// 将数据写入二进制文件（包括缓存文件和内存中的数据）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        public void WriteToFile(string filePath)
        {
            try
            {
                // 加载所有缓存文件中的数据
                var allScreenshots = LoadAllCachedScreenshots();

                // 合并内存中的截图数据
                foreach (var entry in _screenshots)
                {
                    allScreenshots.Add(entry.Key, entry.Value);
                }

                // 写入到目标文件
                using FileStream fs = new FileStream(filePath, FileMode.Create);
                using BinaryWriter writer = new BinaryWriter(fs);
                // 写入 _recordQuality
                writer.Write(_recordQuality);

                // 写入截图数量
                writer.Write(allScreenshots.Count);

                // 写入每个截图数据
                foreach (var entry in allScreenshots)
                {
                    writer.Write(entry.Key.Ticks); // 写入时间（Ticks）
                    writer.Write(entry.Value.Length); // 写入图片数据长度
                    writer.Write(entry.Value); // 写入图片数据
                }

                // 写入 _logtxt 的长度和内容
                writer.Write(_logtxt.Length); // 写入日志文本的长度
                writer.Write(_logtxt); // 写入日志文本

                allScreenshots = null;
                GC.Collect(); // 手动触发垃圾回收，确保内存被释放
                XLogger.Debug($"数据已保存到文件：{filePath}");
            }
            catch (Exception ex)
            {
                XLogger.Debug($"保存数据到文件时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 缓存最早的 10 张截图到临时目录
        /// </summary>
        private void CacheOldestScreenshots()
        {
            // 获取最早的 10 张截图
            var oldestEntries = _screenshots.OrderBy(entry => entry.Key).Take(10).ToList();

            // 创建缓存文件
            string cacheFilePath = Path.Combine(_tempFolder, $"{_cacheFilePrefix}.sr{_cacheFileIndex}");
            using (FileStream fs = new FileStream(cacheFilePath, FileMode.Create))
            using (BinaryWriter writer = new BinaryWriter(fs))
            {
                writer.Write(oldestEntries.Count); // 写入截图数量
                foreach (var entry in oldestEntries)
                {
                    writer.Write(entry.Key.Ticks); // 写入时间
                    writer.Write(entry.Value.Length); // 写入图片数据长度
                    writer.Write(entry.Value); // 写入图片数据
                }
            }

            XLogger.Debug($"已缓存截图到文件：{cacheFilePath}");

            // 从内存中移除已缓存的截图
            foreach (var entry in oldestEntries)
            {
                _screenshots.Remove(entry.Key);
            }

            _cacheFileIndex++; // 更新缓存文件索引
        }

        /// <summary>
        /// 清理该实例生成的缓存文件
        /// </summary>
        private void CleanupCacheFiles()
        {
            foreach (var cacheFilePath in Directory.GetFiles(_tempFolder, $"{_cacheFilePrefix}.sr*"))
            {
                try
                {
                    File.Delete(cacheFilePath);
                    XLogger.Debug($"已删除缓存文件：{cacheFilePath}");
                }
                catch (Exception ex)
                {
                    XLogger.Debug($"删除缓存文件时发生错误：{ex.Message}");
                }
            }
        }

        /// <summary>
        /// 获取字典中最后一个键
        /// </summary>
        private DateTime GetLastKey()
        {
            DateTime lastKey = DateTime.MinValue;
            foreach (var key in _screenshots.Keys)
            {
                lastKey = key;
            }
            return lastKey;
        }

        /// <summary>
        /// 比较两个图片数据是否相同
        /// </summary>
        private bool IsImageDataEqual(byte[] data1, byte[] data2)
        {
            if (data1.Length != data2.Length)
                return false;

            for (int i = 0; i < data1.Length; i++)
            {
                if (data1[i] != data2[i])
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 验证字节数组是否为有效的图片数据
        /// </summary>
        private bool IsValidImageData(byte[] imageData)
        {
            try
            {
                using var ms = new MemoryStream(imageData);
                using var bitmap = new Bitmap(ms);
                return true; // 如果没有抛出异常，说明是有效的图片数据
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 加载所有缓存文件中的数据
        /// </summary>
        private Dictionary<DateTime, byte[]> LoadAllCachedScreenshots()
        {
            var allScreenshots = new Dictionary<DateTime, byte[]>();

            // 遍历临时目录中与该实例相关的缓存文件
            foreach (var cacheFilePath in Directory.GetFiles(_tempFolder, $"{_cacheFilePrefix}.sr*"))
            {
                using (FileStream fs = new FileStream(cacheFilePath, FileMode.Open))
                using (BinaryReader reader = new BinaryReader(fs))
                {
                    int count = reader.ReadInt32(); // 读取截图数量
                    for (int i = 0; i < count; i++)
                    {
                        long ticks = reader.ReadInt64(); // 读取时间
                        DateTime time = new DateTime(ticks);

                        int dataLength = reader.ReadInt32(); // 读取图片数据长度
                        byte[] imageData = reader.ReadBytes(dataLength); // 读取图片数据

                        allScreenshots.Add(time, imageData);
                    }
                }
            }

            return allScreenshots;
        }

        /// <summary>
        /// 解析日志行
        /// </summary>
        /// <param name="line">日志行</param>
        /// <param name="logTime">解析出的日志时间</param>
        /// <param name="logText">解析出的日志文本</param>
        /// <returns>是否解析成功</returns>
        private bool TryParseLogLine(string line, out DateTime logTime, out string logText)
        {
            logTime = DateTime.MinValue;
            logText = null;

            // 日志格式：[时间][日志等级][客户端] 日志文本
            if (string.IsNullOrEmpty(line) || line.Length < 30 || line[0] != '[')
                return false;

            // 解析时间部分
            int timeEndIndex = line.IndexOf(']', 1);
            if (timeEndIndex == -1)
                return false;

            string timePart = line.Substring(1, timeEndIndex - 1); // 提取时间部分
            if (!DateTime.TryParseExact(timePart, "yyyy-MM-dd HH:mm:ss.ffff", null, System.Globalization.DateTimeStyles.None, out logTime))
                return false;

            // 提取日志文本
            int logTextStartIndex = line.IndexOf(']', timeEndIndex + 1) + 1;
            if (logTextStartIndex == 0)
                return false;

            logText = line.Substring(logTextStartIndex).Trim();
            return true;
        }
    }
}