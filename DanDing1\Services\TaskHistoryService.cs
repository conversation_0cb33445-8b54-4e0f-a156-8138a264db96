using DanDing1.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using XHelper;

namespace DanDing1.Services
{
    /// <summary>
    /// 定时任务历史记录服务
    /// </summary>
    public class TaskHistoryService
    {
        #region 字段

        private readonly string _historyRootPath;
        private readonly string _dailyHistoryPath;
        private readonly string _summaryPath;
        private readonly Dictionary<DateTime, DailyTaskHistoryCollection> _cachedDailyCollections;
        private ObservableCollection<TaskHistoryRecord> _currentSessionRecords;

        // JSON序列化选项
        private readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        #endregion

        #region 属性

        /// <summary>
        /// 当前会话记录集合
        /// </summary>
        public ObservableCollection<TaskHistoryRecord> CurrentSessionRecords => _currentSessionRecords;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public TaskHistoryService()
        {
            try
            {
                // 初始化存储路径
                _historyRootPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "DanDing1",
                    "SchedulerHistory");

                _dailyHistoryPath = Path.Combine(_historyRootPath, "Daily");
                _summaryPath = Path.Combine(_historyRootPath, "Summary");

                // 确保目录存在
                Directory.CreateDirectory(_historyRootPath);
                Directory.CreateDirectory(_dailyHistoryPath);
                Directory.CreateDirectory(_summaryPath);

                _currentSessionRecords = new ObservableCollection<TaskHistoryRecord>();
                _cachedDailyCollections = new Dictionary<DateTime, DailyTaskHistoryCollection>();

                XLogger.Info($"任务历史记录服务初始化完成，存储路径：{_historyRootPath}");
            }
            catch (Exception ex)
            {
                XLogger.Error($"初始化任务历史记录服务失败: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 添加历史记录
        /// </summary>
        /// <param name="record">历史记录</param>
        /// <returns>记录ID</returns>
        public string AddRecord(TaskHistoryRecord record)
        {
            try
            {
                // 确保记录有ID
                if (string.IsNullOrEmpty(record.Id))
                {
                    record.Id = Guid.NewGuid().ToString();
                }

                // 添加到当前会话记录
                _currentSessionRecords.Add(record);

                // 获取记录日期（使用UTC日期避免时区问题）
                DateTime recordDate = record.StartTime.Date;

                // 获取或创建日期集合
                var dailyCollection = GetDailyCollection(recordDate);

                // 添加记录到日期集合
                dailyCollection.AddRecord(record);

                // 保存日期集合
                SaveDailyCollection(dailyCollection);

                XLogger.Debug($"添加任务历史记录：{record.TaskName}，ID：{record.Id}");

                return record.Id;
            }
            catch (Exception ex)
            {
                XLogger.Error($"添加历史记录失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 更新记录状态
        /// </summary>
        /// <param name="recordId">记录ID</param>
        /// <param name="status">新状态</param>
        /// <param name="errorMessage">错误信息（可选）</param>
        /// <returns>是否更新成功</returns>
        public bool UpdateRecord(string recordId, string status, string errorMessage = null)
        {
            try
            {
                // 查找记录
                var record = _currentSessionRecords.FirstOrDefault(r => r.Id == recordId);
                if (record == null)
                {
                    XLogger.Warn($"未找到要更新的任务历史记录：{recordId}");
                    return false;
                }

                // 更新记录状态
                record.Status = status;
                record.EndTime = DateTime.Now;

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    record.ErrorMessage = errorMessage;
                }

                // 获取记录日期
                DateTime recordDate = record.StartTime.Date;

                // 获取日期集合
                var dailyCollection = GetDailyCollection(recordDate);

                // 更新日期集合中的记录状态
                bool updated = dailyCollection.UpdateRecordStatus(recordId, status, errorMessage);

                // 保存日期集合
                if (updated)
                {
                    SaveDailyCollection(dailyCollection);
                    XLogger.Debug($"更新任务历史记录状态：{recordId}，新状态：{status}");
                }

                return updated;
            }
            catch (Exception ex)
            {
                XLogger.Error($"更新历史记录状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 加载特定日期的历史记录
        /// </summary>
        /// <param name="date">日期</param>
        /// <returns>历史记录列表</returns>
        public List<TaskHistoryRecord> LoadRecordsByDate(DateTime date)
        {
            try
            {
                // 获取日期集合
                var dailyCollection = GetDailyCollection(date);
                return dailyCollection.Records.ToList();
            }
            catch (Exception ex)
            {
                XLogger.Error($"加载{date:yyyy-MM-dd}的历史记录失败: {ex.Message}");
                return new List<TaskHistoryRecord>();
            }
        }

        /// <summary>
        /// 加载日期范围内的历史记录
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>历史记录列表</returns>
        public List<TaskHistoryRecord> LoadRecordsByDateRange(DateTime startDate, DateTime endDate)
        {
            try
            {
                var result = new List<TaskHistoryRecord>();

                // 确保开始日期不晚于结束日期
                if (startDate > endDate)
                {
                    var temp = startDate;
                    startDate = endDate;
                    endDate = temp;
                }

                // 遍历日期范围
                for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
                {
                    // 获取日期集合
                    var dailyCollection = GetDailyCollection(date);
                    if (dailyCollection != null && dailyCollection.Records.Count > 0)
                    {
                        result.AddRange(dailyCollection.Records);
                    }
                }

                XLogger.Debug($"加载{startDate:yyyy-MM-dd}至{endDate:yyyy-MM-dd}的历史记录，共{result.Count}条");
                return result;
            }
            catch (Exception ex)
            {
                XLogger.Error($"加载日期范围历史记录失败: {ex.Message}");
                return new List<TaskHistoryRecord>();
            }
        }

        /// <summary>
        /// 导出历史记录到CSV文件
        /// </summary>
        /// <param name="records">要导出的记录</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否导出成功</returns>
        public bool ExportToCsv(List<TaskHistoryRecord> records, string filePath)
        {
            try
            {
                if (records == null || records.Count == 0)
                {
                    XLogger.Warn("没有记录可供导出");
                    return false;
                }

                var sb = new StringBuilder();

                // 添加CSV标题行
                sb.AppendLine("任务ID,任务名称,模拟器名称,开始时间,结束时间,持续时间,状态,调度类型,任务类型,重试次数,错误信息");

                // 添加数据行
                foreach (var record in records)
                {
                    sb.AppendLine(string.Join(",",
                        EscapeCsvField(record.TaskId),
                        EscapeCsvField(record.TaskName),
                        EscapeCsvField(record.EmulatorName),
                        EscapeCsvField(record.StartTime.ToString("yyyy-MM-dd HH:mm:ss")),
                        EscapeCsvField(record.EndTime.ToString("yyyy-MM-dd HH:mm:ss")),
                        EscapeCsvField(record.FormattedDuration),
                        EscapeCsvField(record.Status),
                        EscapeCsvField(record.ScheduleType),
                        EscapeCsvField(record.TaskType),
                        record.RetryCount.ToString(),
                        EscapeCsvField(record.ErrorMessage)
                    ));
                }

                // 写入文件
                File.WriteAllText(filePath, sb.ToString(), Encoding.UTF8);

                XLogger.Info($"成功导出{records.Count}条历史记录到：{filePath}");

                return true;
            }
            catch (Exception ex)
            {
                XLogger.Error($"导出历史记录到CSV失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 清理过期记录
        /// </summary>
        /// <param name="daysToKeep">保留天数</param>
        /// <returns>已清理的文件数</returns>
        public int CleanupOldRecords(int daysToKeep = 30)
        {
            try
            {
                // 计算截止日期
                DateTime cutoffDate = DateTime.Today.AddDays(-daysToKeep);

                int cleanedFilesCount = 0;

                // 获取所有日期历史记录文件
                string[] historyFiles = Directory.GetFiles(_dailyHistoryPath, "*.json");

                foreach (string filePath in historyFiles)
                {
                    try
                    {
                        // 从文件名提取日期（文件名为yyyyMMdd.json格式）
                        string fileName = Path.GetFileNameWithoutExtension(filePath);

                        if (DateTime.TryParseExact(fileName, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out DateTime fileDate))
                        {
                            // 如果文件日期早于截止日期，则删除
                            if (fileDate < cutoffDate)
                            {
                                File.Delete(filePath);
                                cleanedFilesCount++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Warn($"清理文件时出错: {filePath}, {ex.Message}");
                    }
                }

                // 清除缓存中的过期集合
                var expiredDates = _cachedDailyCollections.Keys.Where(date => date < cutoffDate).ToList();
                foreach (var date in expiredDates)
                {
                    _cachedDailyCollections.Remove(date);
                }

                XLogger.Info($"已清理{cleanedFilesCount}个过期历史记录文件（保留{daysToKeep}天）");

                return cleanedFilesCount;
            }
            catch (Exception ex)
            {
                XLogger.Error($"清理过期记录失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 获取历史记录统计信息
        /// </summary>
        /// <param name="days">最近几天</param>
        /// <returns>统计信息字典</returns>
        public Dictionary<string, object> GetStatistics(int days = 7)
        {
            try
            {
                var result = new Dictionary<string, object>();

                // 计算日期范围
                DateTime endDate = DateTime.Today;
                DateTime startDate = endDate.AddDays(-(days - 1));

                // 加载日期范围内的记录
                var records = LoadRecordsByDateRange(startDate, endDate);

                // 计算统计信息
                result["总记录数"] = records.Count;
                result["成功记录数"] = records.Count(r => r.Status == "成功");
                result["失败记录数"] = records.Count(r => r.Status == "失败");
                result["中断记录数"] = records.Count(r => r.Status == "中断");
                result["执行中记录数"] = records.Count(r => r.Status == "执行中");

                // 计算成功率
                double successRate = records.Count > 0
                    ? (double)records.Count(r => r.Status == "成功") / records.Count * 100
                    : 0;
                result["成功率"] = Math.Round(successRate, 2);

                // 按模拟器分组
                var byEmulator = records
                    .GroupBy(r => r.EmulatorName)
                    .Select(g => new
                    {
                        EmulatorName = g.Key,
                        Count = g.Count(),
                        SuccessCount = g.Count(r => r.Status == "成功"),
                        FailureCount = g.Count(r => r.Status == "失败")
                    })
                    .OrderByDescending(g => g.Count)
                    .ToList();

                result["按模拟器统计"] = byEmulator;

                // 按任务类型分组
                var byTaskType = records
                    .GroupBy(r => r.TaskType)
                    .Select(g => new
                    {
                        TaskType = g.Key,
                        Count = g.Count(),
                        SuccessCount = g.Count(r => r.Status == "成功"),
                        FailureCount = g.Count(r => r.Status == "失败")
                    })
                    .OrderByDescending(g => g.Count)
                    .ToList();

                result["按任务类型统计"] = byTaskType;

                // 按调度类型分组
                var byScheduleType = records
                    .GroupBy(r => r.ScheduleType)
                    .Select(g => new
                    {
                        ScheduleType = g.Key,
                        Count = g.Count(),
                        SuccessCount = g.Count(r => r.Status == "成功"),
                        FailureCount = g.Count(r => r.Status == "失败")
                    })
                    .OrderByDescending(g => g.Count)
                    .ToList();

                result["按调度类型统计"] = byScheduleType;

                // 按日期分组
                var byDate = records
                    .GroupBy(r => r.StartTime.Date)
                    .Select(g => new
                    {
                        Date = g.Key.ToString("yyyy-MM-dd"),
                        Count = g.Count(),
                        SuccessCount = g.Count(r => r.Status == "成功"),
                        FailureCount = g.Count(r => r.Status == "失败")
                    })
                    .OrderBy(g => g.Date)
                    .ToList();

                result["按日期统计"] = byDate;

                return result;
            }
            catch (Exception ex)
            {
                XLogger.Error($"获取历史记录统计信息失败: {ex.Message}");
                return new Dictionary<string, object> { { "错误", ex.Message } };
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取日期集合
        /// </summary>
        private DailyTaskHistoryCollection GetDailyCollection(DateTime date)
        {
            // 只保留日期部分
            date = date.Date;

            // 检查缓存
            if (_cachedDailyCollections.TryGetValue(date, out var cachedCollection))
            {
                return cachedCollection;
            }

            // 尝试从文件加载
            string filePath = GetDailyHistoryFilePath(date);
            DailyTaskHistoryCollection collection;

            if (File.Exists(filePath))
            {
                try
                {
                    string json = File.ReadAllText(filePath);
                    collection = JsonSerializer.Deserialize<DailyTaskHistoryCollection>(json, _jsonOptions);
                    if (collection == null)
                    {
                        collection = new DailyTaskHistoryCollection { Date = date };
                    }
                }
                catch (Exception ex)
                {
                    XLogger.Warn($"加载日期{date:yyyy-MM-dd}的历史记录失败: {ex.Message}，将创建新集合");
                    collection = new DailyTaskHistoryCollection { Date = date };
                }
            }
            else
            {
                // 创建新集合
                collection = new DailyTaskHistoryCollection { Date = date };
            }

            // 添加到缓存
            _cachedDailyCollections[date] = collection;

            return collection;
        }

        /// <summary>
        /// 保存日期集合
        /// </summary>
        private void SaveDailyCollection(DailyTaskHistoryCollection collection)
        {
            try
            {
                string filePath = GetDailyHistoryFilePath(collection.Date);
                string json = JsonSerializer.Serialize(collection, _jsonOptions);

                // 先写入临时文件，再替换原始文件，避免写入过程中的文件损坏
                string tempFilePath = filePath + ".temp";
                File.WriteAllText(tempFilePath, json);

                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                File.Move(tempFilePath, filePath);

                // 更新缓存
                _cachedDailyCollections[collection.Date.Date] = collection;
            }
            catch (Exception ex)
            {
                XLogger.Error($"保存日期{collection.Date:yyyy-MM-dd}的历史记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取日期历史文件路径
        /// </summary>
        private string GetDailyHistoryFilePath(DateTime date)
        {
            return Path.Combine(_dailyHistoryPath, $"{date:yyyyMMdd}.json");
        }

        /// <summary>
        /// 转义CSV字段
        /// </summary>
        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
            {
                return "";
            }

            // 如果字段包含逗号、引号或换行符，则需要用引号括起来
            if (field.Contains(",") || field.Contains("\"") || field.Contains("\n"))
            {
                // 将字段中的引号替换为两个引号
                field = field.Replace("\"", "\"\"");
                // 用引号括起来
                field = $"\"{field}\"";
            }

            return field;
        }

        #endregion
    }
}