﻿using DanDing1.Helpers;
using DanDing1.Views.Windows;
using System.Text.Json;
using XHelper;

namespace DanDing1
{
    internal class Timers
    {
        /// <summary>
        /// 上次上传的日志内容
        /// </summary>
        private static string lastUploadedLogs = "";

        /// <summary>
        /// 重新登录成功次数
        /// </summary>
        private static int reLoadCount = 0;

        /// <summary>
        /// 检查用户状态定时
        /// </summary>
        /// <param name="state"></param>
        public async void CheckUserStatus(object? state)
        {
            XLogger.Debug("CheckUserStatus|Running...");
            int recount = 0;
            int MaxreLoadCount =
                GlobalData.Instance.appConfig.User_Points >= 100 ? 3 : 1; // 积分大于等于100时最多尝试2次重新登录
        Re:
            try
            {
                if (GlobalData.Instance.appConfig.IsLogin || GlobalData.Instance.appConfig.IsFree)
                {
                    // 检查登录状态，只有当isReLoadUser为true(401错误)时才尝试重新登录
                    bool loginStatus = await GlobalData.Instance.appConfig.dNet.Auth.CheckLoginStatusAsync();
                    if (!loginStatus && GlobalData.Instance.appConfig.dNet.Auth.isReLoadUser && reLoadCount < MaxreLoadCount)
                    {
                        // 记录详细的401错误信息
                        string errorDetail = GlobalData.Instance.appConfig.dNet.Auth.LastErrorDetail ?? "未获取到具体错误信息";
                        XLogger.Warn($"账号状态异常(401 Unauthorized)，详细信息：{errorDetail}，5S后尝试自动登录...");
                        Task.Delay(5000).Wait();
                        // 尝试自动登录
                        try
                        {
                            // 从本地配置文件读取账号密码
                            string username = XConfig.LoadValueFromFile<string>("Account_User") ?? "";
                            Utils.GetAppKeyAndIV(out string key, out string iv);
                            string password = XConfig.LoadValueFromFile_Decrypt("Account_Pwd", key, iv) ?? "";

                            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                            {
                                XLogger.Error("本地未保存账号密码，无法自动登录");
                                GlobalData.Instance.appConfig.IsLogin = false;
                                GlobalData.Instance.appConfig.IsFree = false;

                                // 停止所有脚本线程
                                int stoppedCount = Scripts.StopAllScripts();
                                XLogger.Info($"已停止 {stoppedCount} 个脚本任务");

                                // 断开WebSocket连接
                                await XWebsocket.DisconnectAsync();
                                XLogger.Debug("登录状态检查失败，WebSocket连接已断开");

                                // 通知UI更新状态
                                Application.Current.Dispatcher.Invoke(() =>
                                {
                                    MessageBox.Show("登录状态失效且本地未保存账号密码，所有脚本已停止运行。", "警告",
                                        MessageBoxButton.OK, MessageBoxImage.Warning);
                                });

                                return;
                            }

                            var loginResponse = await GlobalData.Instance.appConfig.dNet.Auth.LoginAsync(username, password);
                            bool loginResult = loginResponse?.IsSuccess ?? false;

                            if (!loginResult)
                            {
                                XLogger.Error("自动登录失败，关闭所有脚本线程");
                                GlobalData.Instance.appConfig.IsLogin = false;
                                GlobalData.Instance.appConfig.IsFree = false;

                                // 停止所有脚本线程
                                int stoppedCount = Scripts.StopAllScripts();
                                XLogger.Info($"已停止 {stoppedCount} 个脚本任务");

                                // 断开WebSocket连接
                                await XWebsocket.DisconnectAsync();
                                XLogger.Debug("登录状态检查失败，WebSocket连接已断开");

                                // 通知UI更新状态
                                Application.Current.Dispatcher.Invoke(() =>
                                {
                                    MessageBox.Show("登录状态失效且尝试自动登录失败，所有脚本已停止运行。", "警告",
                                        MessageBoxButton.OK, MessageBoxImage.Warning);
                                });
                                return;
                            }

                            GlobalData.Instance.appConfig.IsLogin = true;
                            reLoadCount++;
                            XLogger.Info($"自动登录成功，已尝试重新登录 {reLoadCount}/{MaxreLoadCount} 次");
                        }
                        catch (Exception loginEx)
                        {
                            XLogger.Error($"自动登录异常: {loginEx.Message}");
                            GlobalData.Instance.appConfig.IsLogin = false;
                            GlobalData.Instance.appConfig.IsFree = false;

                            // 停止所有脚本线程
                            int stoppedCount = Scripts.StopAllScripts();
                            XLogger.Info($"已停止 {stoppedCount} 个脚本任务");

                            // 断开WebSocket连接
                            await XWebsocket.DisconnectAsync();
                            XLogger.Debug("登录状态检查失败，WebSocket连接已断开");

                            // 通知UI更新状态
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                MessageBox.Show("登录状态失效且尝试自动登录失败，所有脚本已停止运行。", "警告",
                                    MessageBoxButton.OK, MessageBoxImage.Warning);
                            });

                            return;
                        }
                    }
                    else if (!loginStatus)
                    {
                        // 登录状态检查失败，但不是401错误，记录日志但不做其他处理
                        XLogger.Debug("CheckUserStatus|登录状态检查失败，但不是401错误，忽略处理");
                    }
                }
            }
            catch (Exception e)
            {
                XLogger.Warn("CheckUserStatus|Exception|{0}", e.Message);
                recount++;
                Task.Delay(1000).Wait();
                if (recount > 2)
                {
                    // 仅在确认为401错误时才进行重置和脚本停止
                    if (GlobalData.Instance.appConfig.dNet.Auth.isReLoadUser)
                    {
                        string errorDetail = GlobalData.Instance.appConfig.dNet.Auth.LastErrorDetail ?? "未获取到具体错误信息";
                        XLogger.Warn($"CheckUserStatus|持续失败|401错误细节：{errorDetail}");
                        GlobalData.Instance.appConfig.IsLogin = false;
                        GlobalData.Instance.appConfig.IsFree = false;
                        //停止所有脚本
                        int stoppedCount = Scripts.StopAllScripts();
                        XLogger.Info($"已停止 {stoppedCount} 个脚本任务");
                        //提示用户
                        MessageBox.Show($"服务器连接遇到了问题：{e.Message}\r\n" +
                        $"401错误详情：{errorDetail}\r\n" +
                        "所有脚本已停止运行。请检查网络连接或稍后再试。若当前节点不稳定，请于设置中切换节点。", "警告",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                    else
                    {
                        XLogger.Debug($"CheckUserStatus|连接异常但不是401错误，不停止脚本: {e.Message}");
                        return;
                    }
                }
                goto Re;
            }

            XLogger.Debug("CheckUserStatus|Ended|OK");
        }

        /// <summary>
        /// 初始化MuMu模拟器配置
        /// </summary>
        public void InitMuMuConfigs(object? state)
        {
            try
            {
                XLogger.Debug("InitMuMuConfigs|Running...");
                // 检查是否已有保存的路径
                var existingPath = XConfig.LoadValueFromFile<string>("MuMuPath");
                if (!string.IsNullOrEmpty(existingPath) && File.Exists(existingPath))
                {
                    XLogger.Debug("InitMuMuConfigs|已有MuMu路径配置且路径有效");
                    return;
                }

                // 尝试查找MuMu12安装路径
                var defaultPath = Path.Combine("C:\\Program Files\\Netease\\MuMu Player 12\\shell", "MuMuPlayer.exe");
                if (File.Exists(defaultPath))
                {
                    XLogger.Debug("InitMuMuConfigs|找到默认安装路径");
                    XConfig.SaveValueToFile("MuMuPath", defaultPath);
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        new MuMuConfigsWindow().InitData_DontShow();
                    });
                    return;
                }

                // 从配置文件读取路径
                var configPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "Netease", "MuMuPlayer-12.0", "install_config.json");

                if (File.Exists(configPath))
                {
                    try
                    {
                        var jsonContent = File.ReadAllText(configPath);
                        using JsonDocument document = JsonDocument.Parse(jsonContent);
                        var playerInstallDir = document.RootElement
                            .GetProperty("player")
                            .GetProperty("install_dir")
                            .GetString();

                        if (!string.IsNullOrEmpty(playerInstallDir))
                        {
                            var mumuPath = Path.Combine(playerInstallDir, "MuMuPlayer.exe");
                            if (File.Exists(mumuPath))
                            {
                                XLogger.Debug("InitMuMuConfigs|从配置文件找到MuMu路径");
                                XConfig.SaveValueToFile("MuMuPath", mumuPath);
                                Application.Current.Dispatcher.Invoke(() =>
                                {
                                    new MuMuConfigsWindow().InitData_DontShow();
                                });
                                return;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error("InitMuMuConfigs|读取配置文件失败: " + ex.Message);
                    }
                }

                XLogger.Warn("InitMuMuConfigs|程序未找到MuMu12安装路径");
            }
            catch (Exception ex)
            {
                XLogger.Error("InitMuMuConfigs|Exception: " + ex.Message);
            }
        }

        /// <summary>
        /// 定时上传用户日志
        /// </summary>
        /// <param name="state"></param>
        public async void UploadUserLogs(object? state)
        {
            //XLogger.Debug("UploadUserLogs|Running...");
            if (!GlobalData.Instance.appConfig.EnableHttpLogReporting)
                return;

            try
            {
                // 检查用户是否已登录且不是试用状态
                if (!GlobalData.Instance.appConfig.IsLogin || GlobalData.Instance.appConfig.IsFree)
                {
                    //XLogger.Debug("UploadUserLogs|用户未登录或处于试用状态，不上传日志");
                    return;
                }

                // 获取最新的日志，限制为20条
                string[] recentLogs = XLogger.GetRecentLogs(20);

                // 如果没有日志，直接返回
                if (recentLogs.Length == 0)
                {
                    //XLogger.Debug("UploadUserLogs|没有新日志可上传");
                    return;
                }

                // 获取前10条日志（如果有的话）
                string[] logsToUpload = recentLogs.Take(Math.Min(10, recentLogs.Length)).ToArray();

                // 合并日志内容，使用\r\n分隔
                string combinedLogs = string.Join("\r\n", logsToUpload);

                // 检查是否与上次上传内容相同
                if (combinedLogs == lastUploadedLogs)
                {
                    //XLogger.Debug("UploadUserLogs|日志内容未更新，不再重复上传");
                    return;
                }

                // 上传日志
                var result = await GlobalData.Instance.appConfig.dNet.StoreLogAsync(combinedLogs);
                lastUploadedLogs = combinedLogs;

                // 检查上传结果
                if (result?.IsSuccess == true)
                {
                    // 更新上次上传的日志内容
                    //XLogger.Debug("UploadUserLogs|日志上传成功");
                }
                else
                {
                    XLogger.Debug($"UploadUserLogs|日志上传失败: {result?.Message}");
                    CheckUserStatus(null);
                }
            }
            catch (Exception ex)
            {
                XLogger.Debug($"UploadUserLogs|异常: {ex.Message}");
            }

            //XLogger.Debug("UploadUserLogs|Ended");
        }
    }
}