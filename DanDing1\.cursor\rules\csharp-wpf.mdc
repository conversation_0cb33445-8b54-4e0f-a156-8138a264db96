---
description: 
globs: 
---
架构设计

分层架构：确保界面层、业务逻辑层、数据访问层分离。这样可以增强代码的可维护性和扩展性。
MVVM 模式：推荐使用 MVVM 架构，以分离 UI 与业务逻辑。View 仅负责展示，而 ViewModel 则处理所有逻辑。
资源管理

统一资源管理：将所有控件样式、模板和资源定义在统一的资源字典中，便于集中管理和全局共享。
动态资源更新：对于主题或配色方案的更改，建议使用动态资源绑定，实现实时更新。
性能优化

虚拟化技术：在展示大量数据时，应使用 ItemsControl 的虚拟化功能，避免一次性加载所有项。
数据绑定性能：使用合适的数据绑定方式，减少不必要的绑定更新和内存泄漏。
事件和命令处理

命令模式：通过 ICommand 接口处理用户操作，避免代码中直接使用事件处理，提升测试性和复用性。
消息机制：在组件间通信时，建议采用消息传递机制（如 MVVM Light Messenger 或 Prism EventAggregator），以避免紧耦合。
界面交互设计

响应式设计：确保界面在不同分辨率下均能良好显示，使用自适应布局和合理的控件比例。
用户体验：关注控件的交互反馈，例如鼠标悬停、点击等状态变化，提升用户体验。
调试与日志

日志记录：使用统一的日志记录类，在开发和运行过程中记录关键操作和错误。
异常处理：全局捕捉和处理异常，确保应用在异常情况下能安全运行，并能给出合理提示。
可测试性

单元测试：在 ViewModel 层编写单元测试，确保业务逻辑正确。
依赖注入：利用依赖注入框架（如 Unity 或 Autofac），减少组件间直接依赖，提升测试灵活性。
项目管理和版本控制

代码规范：制定并遵循统一的编码规范和注释标准，确保团队协作时代码风格一致。
版本控制：使用 Git 等版本控制工具，确保代码有良好的历史记录和分支管理策略。
安全性

数据加密：对于敏感数据，采用适当的加密技术进行保护。
权限控制：设计细致的权限验证机制，防止未授权访问和操作。
持续集成和部署

自动化构建：配置自动化构建流程，保证每次提交代码后能快速验证和部署。
部署策略：确保在不同环境（开发、测试、生产）中均能顺利部署，并保留回滚机制。
