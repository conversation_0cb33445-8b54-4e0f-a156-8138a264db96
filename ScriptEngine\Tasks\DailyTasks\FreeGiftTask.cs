﻿using DamoControlKit.Model;
using ScriptEngine.Model;
using ScriptEngine.Tasks.Base;

namespace ScriptEngine.Tasks.DailyTasks
{
    /// <summary>
    /// 每日免费礼包领取
    /// </summary>
    internal class FreeGiftTask : BaseTask
    {
        private Dictionary<string, Position> ClickPos_Pairs = new Dictionary<string, Position>()
        {
            {"打开商店",new(660,620,703,666) },
            {"切换礼包屋",new(1148,650,1188,701) },
            {"打开推荐",new(1187,192,1235,256) },
            {"领取礼包",new(267,212,451,326) },
            {"随机范围",new(1183,420,1265,560) },
            {"返回",new(19,11,56,45) },
        };

        private Dictionary<string, Position> Find_Pairs = new()
        {
            {"是否可以领取",new(279,115,432,346) }, // OCR结果中如果包含 "领取"、"免费" 则可以领取
        };

        public override void Init(DDBuilder dB, dmsoft dm, CancellationTokenSource ct, string className)
        {
            base.Init(dB, dm, ct, className);
        }

        public override void Start(TaskConfigsModel.Configs configs)
        {
            base.Start(configs);
            log.Info("开始执行任务：" + configs.Name);

            // 判断场景是否为庭院
            GoToTingYuan();

            // 执行领取免费礼包逻辑
            CollectFreeGift();
        }

        /// <summary>
        /// 领取免费礼包
        /// </summary>
        private bool CollectFreeGift()
        {
            // 打开商店
            log.Info("点击打开商店");
            Fast.Click(ClickPos_Pairs["打开商店"]);
            Sleep(2000); // 等待2秒

            // 切换到礼包屋
            log.Info("切换到礼包屋");
            Fast.Click(ClickPos_Pairs["切换礼包屋"]);
            Sleep(2000); // 等待2秒

            // 打开推荐
            log.Info("打开推荐");
            Fast.Click(ClickPos_Pairs["打开推荐"]);
            Sleep(2000); // 等待2秒

            // 检查是否可以领取
            log.Info("检查是否可以领取免费礼包");
            string result = Fast.Ocr_String(Find_Pairs["是否可以领取"]);
            bool canCollect = result.Contains("领取") || result.Contains("免费");

            if (canCollect)
            {
                log.Info("检测到可领取的免费礼包，开始领取");
                Fast.Click(ClickPos_Pairs["领取礼包"]);
                Sleep(2000); // 等待2秒

                //截图 保存到.\\礼包 文件夹
                var path = Path.Combine(Environment.CurrentDirectory, "礼包");
                if (!Directory.Exists(path))
                    Directory.CreateDirectory(path);
                Dm.CaptureJpg(0, 0, 2000, 2000, path + $"\\[{log.LogClassName}] {DateTime.Now:yyyyMMddHHmmss}.jpg", 100);
                // 领取后点击随机范围
                log.Info("礼包领取完成，已截图，点击随机范围");
                Fast.Click(ClickPos_Pairs["随机范围"]);
                Sleep(1500); // 等待1.5秒
            }
            else
            {
                log.Info("暂无可领取的免费礼包");
            }

            // 返回两次
            log.Info("点击返回按钮");
            Fast.Click(ClickPos_Pairs["返回"]);
            Sleep(1500); // 等待1.5秒

            log.Info("再次点击返回按钮");
            Fast.Click(ClickPos_Pairs["返回"]);
            Sleep(1500); // 等待1.5秒

            log.Info("免费礼包任务完成");
            return canCollect;
        }

        /// <summary>
        /// 确保角色在庭院场景
        /// </summary>
        private bool GoToTingYuan()
        {
        Retry:
            // 判断庭院位置
            if (Scene.NowScene != "庭院")
            {
                log.Info("当前不在庭院，尝试前往庭院");
                if (!Scene.TO.TingYuan())
                {
                    if (!Scene.TO.ResetScene(out var s))
                    {
                        log.Error("无法进入庭院，结束任务");
                        return false;
                    }
                    else
                        goto Retry;
                }
                Sleep(1500); // 等待1.5秒

                // 再次确认是否在庭院
                if (Scene.NowScene != "庭院")
                {
                    if (!Scene.TO.ResetScene(out var s))
                    {
                        log.Error("无法进入庭院，结束任务");
                        return false;
                    }
                    else
                        goto Retry;
                }
            }

            log.Info("已确认在庭院场景");
            return true;
        }
    }
}