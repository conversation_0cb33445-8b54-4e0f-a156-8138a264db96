using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using System.Linq;
using Compunet.YoloSharp.Data;
using XHelper;

namespace DanDing1.Commands
{
    /// <summary>
    /// YOLO测试命令类
    /// </summary>
    internal class Test_Yolo : BaseCommand
    {
        // 测试图片URL
        private const string DetectImageUrl = "https://image.180402.xyz/2025/03/26/67e34e37cc616.jpg";
        private const string ClassImageUrl = "https://image.180402.xyz/2025/03/26/67e34e3c7f1a7.png";

        // 临时文件夹路径
        private readonly string _tempFolder;

        public Test_Yolo()
        {
            _tempFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "temp");
            if (!Directory.Exists(_tempFolder))
            {
                Directory.CreateDirectory(_tempFolder);
            }
        }

        public override void Execute(string[] parameters)
        {
            if (!ValidateParameterCount(parameters, 3))
            {
                PrintHelp();
                return;
            }

            string subCommand = parameters[2].ToLower();

            switch (subCommand)
            {
                case "class":
                case "classify":
                case "场景":
                    XLogger.Info("开始执行YOLO场景分类测试...");
                    _ = Task.Run(TestClassify);
                    break;

                case "detect":
                case "检测":
                case "加成":
                    XLogger.Info("开始执行YOLO对象检测测试...");
                    _ = Task.Run(TestDetect);
                    break;

                case "both":
                case "all":
                case "全部":
                    XLogger.Info("开始执行YOLO全部测试...");
                    _ = Task.Run(async () =>
                    {
                        await TestClassify();
                        await TestDetect();
                    });
                    break;

                case "reset":
                case "重置":
                    XLogger.Info("重置YOLO功能状态...");
                    XYoloV8.Reset();
                    XLogger.Info($"YOLO功能状态已重置，当前状态: {(XYoloV8.IsEnabled ? "启用" : "禁用")}");
                    break;

                case "status":
                case "状态":
                    XLogger.Info($"YOLO功能当前状态: {(XYoloV8.IsEnabled ? "启用" : "禁用")}");
                    break;

                default:
                    PrintHelp();
                    break;
            }
        }

        /// <summary>
        /// 测试YOLO分类功能
        /// </summary>
        private async Task TestClassify()
        {
            try
            {
                // 下载测试图片
                string imagePath = await DownloadImage(ClassImageUrl, "yolo_classify_test.png");
                if (string.IsNullOrEmpty(imagePath))
                {
                    XLogger.Error("下载分类测试图片失败");
                    return;
                }

                XLogger.Info($"分类测试图片已下载: {imagePath}");

                // 确保YOLO功能已启用
                if (!XYoloV8.IsEnabled)
                {
                    XLogger.Warn("YOLO功能已禁用，尝试重新启用...");
                    if (!XYoloV8.TryEnable())
                    {
                        XLogger.Error("无法启用YOLO功能，测试中止");
                        return;
                    }
                }

                // 读取图片数据
                byte[] imageData = File.ReadAllBytes(imagePath);

                // 执行YOLO分类
                XLogger.Info("开始执行YOLO场景分类...");
                var result = XYoloV8.yoloV8(YoloModels.Classify, imageData) as YoloResult<Classification>;

                if (result == null)
                {
                    XLogger.Error("YOLO分类结果为空");
                    return;
                }

                // 输出分类结果
                int count = 0;
                foreach (var item in result)
                {
                    count++;
                    XLogger.Info($"分类: {item.Name}, 置信度: {item.Confidence:P2}");
                }
                XLogger.Info($"YOLO场景分类结果: 检测到 {count} 个分类");

                // 获取置信度最高的分类
                string topCategory = result.YoloClass_GetTopName(0.45);
                XLogger.Info($"置信度最高的分类: {topCategory}");
            }
            catch (Exception ex)
            {
                XLogger.Error($"YOLO分类测试异常: {ex.Message}");
                XLogger.SaveException(ex);
            }
        }

        /// <summary>
        /// 测试YOLO检测功能
        /// </summary>
        private async Task TestDetect()
        {
            try
            {
                // 下载测试图片
                string imagePath = await DownloadImage(DetectImageUrl, "yolo_detect_test.jpg");
                if (string.IsNullOrEmpty(imagePath))
                {
                    XLogger.Error("下载检测测试图片失败");
                    return;
                }

                XLogger.Info($"检测测试图片已下载: {imagePath}");

                // 确保YOLO功能已启用
                if (!XYoloV8.IsEnabled)
                {
                    XLogger.Warn("YOLO功能已禁用，尝试重新启用...");
                    if (!XYoloV8.TryEnable())
                    {
                        XLogger.Error("无法启用YOLO功能，测试中止");
                        return;
                    }
                }

                // 读取图片数据
                byte[] imageData = File.ReadAllBytes(imagePath);

                // 执行YOLO检测
                XLogger.Info("开始执行YOLO对象检测...");
                var result = XYoloV8.yoloV8(YoloModels.Detect, imageData) as YoloResult<Detection>;

                if (result == null)
                {
                    XLogger.Error("YOLO检测结果为空");
                    return;
                }

                // 输出检测结果
                int count = 0;
                foreach (var item in result)
                {
                    count++;
                    // 由于不确定Detection的确切属性，我们只输出可能确定的信息
                    XLogger.Info($"检测对象 #{count}, 置信度: {item.Confidence:P2}");

                    // 尝试以安全的方式获取更多属性信息（如果有的话）
                    try
                    {
                        var properties = item.GetType().GetProperties();
                        foreach (var prop in properties)
                        {
                            if (prop.Name != "Confidence") // 已经输出过置信度
                            {
                                var value = prop.GetValue(item);
                                if (value != null)
                                {
                                    XLogger.Info($"    - {prop.Name}: {value}");
                                }
                            }
                        }
                    }
                    catch
                    {
                        // 如果反射失败，我们至少输出了基本信息
                    }
                }
                XLogger.Info($"YOLO对象检测结果: 检测到 {count} 个对象");
            }
            catch (Exception ex)
            {
                XLogger.Error($"YOLO检测测试异常: {ex.Message}");
                XLogger.SaveException(ex);
            }
        }

        /// <summary>
        /// 下载图片到临时目录
        /// </summary>
        /// <param name="url">图片URL</param>
        /// <param name="fileName">保存的文件名</param>
        /// <returns>下载后的图片路径</returns>
        private async Task<string> DownloadImage(string url, string fileName)
        {
            string filePath = Path.Combine(_tempFolder, fileName);

            try
            {
                // 检查文件是否已存在
                if (File.Exists(filePath))
                {
                    XLogger.Debug($"图片已存在: {filePath}");
                    return filePath;
                }

                XLogger.Debug($"开始下载图片: {url}");
                using (HttpClient client = new HttpClient())
                {
                    // 设置超时
                    client.Timeout = TimeSpan.FromSeconds(30);

                    // 下载图片
                    byte[] imageBytes = await client.GetByteArrayAsync(url);

                    // 保存图片
                    await File.WriteAllBytesAsync(filePath, imageBytes);

                    XLogger.Debug($"图片下载完成，保存到: {filePath}");
                    return filePath;
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"下载图片失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 打印帮助信息
        /// </summary>
        private void PrintHelp()
        {
            XLogger.Info("YOLO测试命令帮助:");
            XLogger.Info("test yolo class - 测试YOLO场景分类");
            XLogger.Info("test yolo detect - 测试YOLO对象检测");
            XLogger.Info("test yolo both - 测试两种模式");
            XLogger.Info("test yolo reset - 重置YOLO功能状态");
            XLogger.Info("test yolo status - 查看YOLO功能状态");
        }
    }
}