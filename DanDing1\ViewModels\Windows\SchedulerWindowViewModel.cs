using DanDing1.Helpers;
using DanDing1.Services;
using DanDing1.Views.Windows;
using Microsoft.Win32;
using System.Collections.ObjectModel;
using System.Text.Json;
using System.Text.Json.Nodes;
using XHelper;
using JsonSerializer = System.Text.Json.JsonSerializer;
using DanDing1.Models;
using System.IO;

namespace DanDing1.ViewModels.Windows
{
    /// <summary>
    /// 模拟器数据模型
    /// </summary>
    public partial class EmulatorItem : ObservableObject
    {
        /// <summary>
        /// 游戏配置信息（用于脚本运行时的配置）
        /// </summary>
        [ObservableProperty]
        private DanDing1.ViewModels.Windows.SuperMultiGameConfigWindowViewModel _config;

        [ObservableProperty]
        private string _currentTask;

        [ObservableProperty]
        private bool _enabled;

        [ObservableProperty]
        private string _id;

        /// <summary>
        /// 模拟器最后活动时间
        /// </summary>
        [ObservableProperty]
        private DateTime _lastActivityTime = DateTime.Now;

        /// <summary>
        /// 上一次的状态，用于检测状态变化
        /// </summary>
        [ObservableProperty]
        private string _lastStatusChange = "";

        [ObservableProperty]
        private string _name;

        [ObservableProperty]
        private string _nextTask = "无待执行任务";

        [ObservableProperty]
        private string _runningDuration;

        /// <summary>
        /// 模拟器配置信息（用于启动、结束模拟器）
        /// </summary>
        [ObservableProperty]
        private Models.SimulatorConfig _simulatorConfig;

        /// <summary>
        /// 模拟器序号，用于标识MuMu模拟器实例索引
        /// </summary>
        [ObservableProperty]
        private string _simulatorIndex;

        [ObservableProperty]
        private string _status;
    }

    /// <summary>
    /// 定时任务数据模型
    /// </summary>
    public partial class ScheduledTask : ObservableObject
    {
        [ObservableProperty]
        private string _emulatorName;

        private bool _enabled;

        [ObservableProperty]
        private string _id;

        [ObservableProperty]
        private string _name;

        [ObservableProperty]
        private string _nextExecutionTime;

        [ObservableProperty]
        private string _scheduleType;

        [ObservableProperty]
        private string _status;

        /// <summary>
        /// 任务参数（JSON格式）
        /// </summary>
        [ObservableProperty]
        private string _taskParameters;

        /// <summary>
        /// 时间间隔（分钟）- 用于"间隔执行"类型
        /// </summary>
        [ObservableProperty]
        private int _intervalMinutes = 60; // 默认60分钟

        /// <summary>
        /// Cron表达式 - 用于"循环执行"类型
        /// </summary>
        [ObservableProperty]
        private string _cronExpression = "0 0 * * * *"; // 默认每小时执行一次

        /// <summary>
        /// 任务启用状态
        /// </summary>
        public bool Enabled
        {
            get => _enabled;
            set
            {
                // 如果试图从禁用状态切换到启用状态（即value为true且当前为false）
                if (value && !_enabled)
                {
                    // 检查一次性任务的执行时间是否已经过期
                    if (_scheduleType == "一次性" && DateTime.TryParse(_nextExecutionTime, out DateTime nextTime))
                    {
                        // 如果下次执行时间已经过期（小于当前时间）
                        if (nextTime < DateTime.Now)
                        {
                            // 不允许启用，给出提示信息
                            MessageBox.Show(
                                $"一次性任务 \"{_name}\" 的执行时间 ({_nextExecutionTime}) 已过期，无法启用。\n\n请编辑任务设置新的执行时间。",
                                "无法启用任务",
                                MessageBoxButton.OK,
                                MessageBoxImage.Warning);

                            // 日志记录，但因为我们在实例内部，不能直接调用ViewModel的LogInfo方法
                            Debug.WriteLine($"无法启用一次性任务 \"{_name}\"，执行时间已过期");

                            // 不改变启用状态
                            return;
                        }
                    }
                }

                // 如果通过检查或当前操作是禁用任务，则正常设置值
                SetProperty(ref _enabled, value);
            }
        }
    }

    /// <summary>
    /// 调度器配置
    /// </summary>
    public class SchedulerConfig
    {
        public int AutoShutdownIdleTime { get; set; } = 300;
        public bool AutoStartScheduler { get; set; } = false;
        public int DefaultTaskTimeout { get; set; } = 3600;
        public bool EnableTaskRetry { get; set; } = true;
        public bool IdleShutdownEnabled { get; set; } = true;
        public int MaxConcurrentEmulators { get; set; } = 4;
        public int MaxRetryCount { get; set; } = 3;
    }

    /// <summary>
    /// 定时调度窗口视图模型
    /// </summary>
    public partial class SchedulerWindowViewModel : ObservableObject
    {
        #region 单例实现

        // 添加静态单例实例
        private static SchedulerWindowViewModel _instance;
        private static readonly object _instanceLock = new object();

        // 提供公共访问器
        public static SchedulerWindowViewModel Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_instanceLock)
                    {
                        if (_instance == null)
                            _instance = new SchedulerWindowViewModel();
                    }
                }
                return _instance;
            }
        }

        #endregion 单例实现

        #region 字段

        // 日志最大条目数
        private const int MaxLogEntries = 500;

        // 性能计数器
        private PerformanceCounter _cpuCounter;

        // 空闲模拟器检查定时器
        private System.Timers.Timer _idleEmulatorCheckTimer;

        // 修改为internal，允许内部访问
        internal bool _isInitialized = false;

        // 记录上一次活跃模拟器的数量，用于检测变化
        private int _lastActiveEmulatorsCount = 0;

        // 日志条目列表，用于管理日志的添加和删除
        private List<string> _logEntries = new List<string>();

        private PerformanceCounter _ramCounter;

        // 资源监控定时器
        private System.Timers.Timer _resourceMonitorTimer;

        private DanDing1.Services.SchedulerService _schedulerService;
        private SchedulerWindow _window;

        // 添加一个属性来控制是否执行自动保存
        private bool _enableAutoSave = true;

        // 添加任务历史记录服务
        private TaskHistoryService _historyService;

        #endregion 字段

        #region 属性

        /// <summary>
        /// 活跃模拟器数量
        /// </summary>
        [ObservableProperty]
        private string _activeEmulators = "0";

        /// <summary>
        /// 应用标题
        /// </summary>
        [ObservableProperty]
        private string _applicationTitle = "蛋定定时调度系统";

        /// <summary>
        /// 空闲自动关闭时间（秒）
        /// </summary>
        [ObservableProperty]
        private int _autoShutdownIdleTime = 300;

        /// <summary>
        /// 软件启动时是否自动开启调度器
        /// </summary>
        [ObservableProperty]
        private bool _autoStartScheduler = false;

        /// <summary>
        /// 是否可以启动调度器
        /// </summary>
        [ObservableProperty]
        private bool _canStartScheduler = true;

        /// <summary>
        /// 是否可以停止调度器
        /// </summary>
        [ObservableProperty]
        private bool _canStopScheduler = false;

        /// <summary>
        /// CPU使用率
        /// </summary>
        [ObservableProperty]
        private string _cpuUsage = "0%";

        /// <summary>
        /// 当前选中的模拟器名称
        /// </summary>
        [ObservableProperty]
        private string _currentEmulatorName = "全部模拟器";

        /// <summary>
        /// 默认任务超时时间（秒）
        /// </summary>
        [ObservableProperty]
        private int _defaultTaskTimeout = 3600;

        /// <summary>
        /// 模拟器集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<EmulatorItem> _emulators = new();

        /// <summary>
        /// 是否启用任务失败重试
        /// </summary>
        [ObservableProperty]
        private bool _enableTaskRetry = true;

        /// <summary>
        /// 过滤后的任务集合（当前选中模拟器的任务）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ScheduledTask> _filteredTasks = new();

        /// <summary>
        /// 空闲自动关闭功能是否已启用
        /// </summary>
        [ObservableProperty]
        private bool _idleShutdownEnabled = true;

        /// <summary>
        /// 正在加载标志，用于界面显示加载状态
        /// </summary>
        [ObservableProperty]
        private bool _isLoading = false;

        /// <summary>
        /// 加载过程中显示的消息
        /// </summary>
        [ObservableProperty]
        private string _loadingMessage = "请稍候...";

        /// <summary>
        /// 主日志
        /// </summary>
        [ObservableProperty]
        private string _mainLog = "";

        /// <summary>
        /// 最大并发模拟器数量
        /// </summary>
        [ObservableProperty]
        private int _maxConcurrentEmulators = 4;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        [ObservableProperty]
        private int _maxRetryCount = 3;

        /// <summary>
        /// 内存使用率
        /// </summary>
        [ObservableProperty]
        private string _memoryUsage = "0 MB";

        /// <summary>
        /// 下一个任务信息
        /// </summary>
        [ObservableProperty]
        private string _nextTaskInfo = "无待执行任务";

        /// <summary>
        /// 定时任务集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ScheduledTask> _scheduledTasks = new();

        /// <summary>
        /// 调度器状态
        /// </summary>
        [ObservableProperty]
        private string _schedulerStatus = "未启动";

        /// <summary>
        /// 选中的模拟器
        /// </summary>
        [ObservableProperty]
        private object _selectedEmulator;

        /// <summary>
        /// 选中的任务
        /// </summary>
        [ObservableProperty]
        private object _selectedTask;

        /// <summary>
        /// 调度器是否正在运行
        /// </summary>
        [ObservableProperty]
        private bool _isSchedulerRunning;

        partial void OnActiveEmulatorsChanged(string value)
        {
            // 活跃模拟器数量变化时，通知托盘图标服务更新提示信息
            try
            {
                var trayIconService = DanDing1.Services.TrayIconService.Instance;
                trayIconService?.ForceUpdateTooltipText();
            }
            catch (Exception ex)
            {
                XLogger.Error($"通知托盘图标服务更新状态时发生错误: {ex.Message}");
            }
        }

        partial void OnAutoShutdownIdleTimeChanged(int value) => AutoSaveSettings();

        partial void OnAutoStartSchedulerChanged(bool value) => AutoSaveSettings();

        partial void OnDefaultTaskTimeoutChanged(int value) => AutoSaveSettings();

        partial void OnEnableTaskRetryChanged(bool value) => AutoSaveSettings();

        partial void OnIdleShutdownEnabledChanged(bool value) => AutoSaveSettings();

        // 添加属性变更通知处理
        partial void OnMaxConcurrentEmulatorsChanged(int value) => AutoSaveSettings();

        partial void OnMaxRetryCountChanged(int value) => AutoSaveSettings();

        partial void OnNextTaskInfoChanged(string value)
        {
            // 下一个任务信息变化时，通知托盘图标服务更新提示信息
            try
            {
                var trayIconService = DanDing1.Services.TrayIconService.Instance;
                trayIconService?.ForceUpdateTooltipText();
            }
            catch (Exception ex)
            {
                XLogger.Error($"通知托盘图标服务更新状态时发生错误: {ex.Message}");
            }
        }

        partial void OnSchedulerStatusChanged(string value)
        {
            // 调度器状态变化时，通知托盘图标服务更新提示信息
            try
            {
                var trayIconService = DanDing1.Services.TrayIconService.Instance;
                trayIconService?.ForceUpdateTooltipText();
            }
            catch (Exception ex)
            {
                XLogger.Error($"通知托盘图标服务更新状态时发生错误: {ex.Message}");
            }

            // 更新调度器运行状态布尔属性
            IsSchedulerRunning = value == "运行中";
        }

        // 添加选中模拟器变更通知处理
        partial void OnSelectedEmulatorChanged(object value)
        {
            UpdateFilteredTasks();

            // 更新当前选中的模拟器名称
            if (value != null)
            {
                var emulator = value as EmulatorItem;
                if (emulator != null)
                {
                    CurrentEmulatorName = emulator.Name;
                }
            }
            else
            {
                CurrentEmulatorName = "全部模拟器";
            }
        }

        /// <summary>
        /// 更新过滤后的任务列表
        /// </summary>
        private void UpdateFilteredTasks()
        {
            FilteredTasks.Clear();

            if (SelectedEmulator == null)
            {
                // 如果没有选中模拟器，显示所有任务
                foreach (var task in ScheduledTasks)
                {
                    FilteredTasks.Add(task);
                }
            }
            else
            {
                // 如果选中了模拟器，只显示该模拟器的任务
                var emulator = (EmulatorItem)SelectedEmulator;
                foreach (var task in ScheduledTasks)
                {
                    if (task.EmulatorName == emulator.Name)
                    {
                        FilteredTasks.Add(task);
                    }
                }
            }
        }

        #endregion 属性

        #region 事件

        /// <summary>
        /// 主日志更新事件
        /// </summary>
        public event Action OnMainLogUpdated;

        #endregion 事件

        #region 构造函数

        // 修改构造函数为公共访问级别，兼容依赖注入，同时保持单例逻辑
        public SchedulerWindowViewModel()
        {
            // 确保单例模式
            if (_instance != null)
            {
                LogInfo("通过依赖注入创建了新的实例，但使用的是现有单例");
                // 不进行额外的初始化
                return;
            }

            // 如果是第一次创建实例，则执行初始化并保存为单例
            lock (_instanceLock)
            {
                if (_instance == null)
                {
                    // 订阅模拟器状态变更事件
                    GameControlService.EmulatorStatusChanged += OnEmulatorStatusChanged;
                    LogInfo("已订阅模拟器状态变更事件");
                    _instance = this;
                }
            }
        }

        /// <summary>
        /// 模拟器状态变更事件处理
        /// </summary>
        private void OnEmulatorStatusChanged()
        {
            // 防止在初始化完成前处理事件
            if (!_isInitialized)
            {
                return;
            }

            // 更新活跃模拟器数量
            UpdateActiveEmulatorsCount();

            // 检查模拟器状态变化，对新上线的模拟器重置活动时间
            try
            {
                // 获取当前上线的模拟器
                var onlineEmulators = Emulators.Where(e => e.Status == "在线").ToList();

                foreach (var emulator in onlineEmulators)
                {
                    // 如果模拟器状态从离线变为在线，重置其LastActivityTime
                    if (emulator.LastStatusChange != "在线")
                    {
                        emulator.LastStatusChange = "在线";
                        emulator.LastActivityTime = DateTime.Now;
                        LogInfo($"模拟器 \"{emulator.Name}\" 状态变更为在线，已重置空闲计时");

                        // 单个模拟器状态变更后，通知UI更新此项
                        OnPropertyChanged(nameof(Emulators));
                    }
                }

                // 再次调用UpdateActiveEmulatorsCount确保UI一致性
                UpdateActiveEmulatorsCount();
            }
            catch (Exception ex)
            {
                LogError($"处理模拟器状态变更时出错: {ex.Message}");
            }
        }

        #endregion 构造函数

        #region 公共方法

        /// <summary>
        /// 清理资源
        /// </summary>
        public async Task CleanupAsync()
        {
            LogInfo("定时调度系统关闭中...");

            try
            {
                // 防止重复清理
                if (!_isInitialized)
                {
                    LogInfo("调度系统未初始化或已清理，跳过清理过程");
                    return;
                }

                // 取消订阅模拟器状态变更事件
                try
                {
                    GameControlService.EmulatorStatusChanged -= OnEmulatorStatusChanged;
                    LogInfo("已取消订阅模拟器状态变更事件");
                }
                catch (Exception ex)
                {
                    LogError($"取消订阅事件时出错: {ex.Message}");
                }

                // 如果调度器正在运行，先停止调度器
                if (IsSchedulerRunning)
                {
                    await StopScheduler(); // 使用修改后的StopScheduler方法，不会关闭模拟器
                }
                else if (_schedulerService != null)
                {
                    // 如果调度器已经停止但服务实例仍存在，确保资源被释放
                    await _schedulerService.Stop(); // 使用修改后的Stop方法，不会关闭模拟器
                    _schedulerService = null; // 清空引用
                }

                // 停止资源监控
                StopResourceMonitoring();

                // 停止空闲模拟器监控
                StopIdleEmulatorMonitoring();

                // 保存所有配置
                SaveAllSchedulerConfigurations();
                LogInfo("所有配置已保存，模拟器保持运行状态");

                // 标记为未初始化，允许重新初始化
                _isInitialized = false;
            }
            catch (Exception ex)
            {
                LogError($"清理资源时出错: {ex.Message}");
            }
        }

        // 保留原有方法以保持兼容性
        public void Cleanup()
        {
            // 异步调用CleanupAsync，但不等待结果
            _ = CleanupAsync();
        }

        /// <summary>
        /// 处理任务执行异常的方法，针对特定错误类型提供特殊处理
        /// </summary>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>是否继续重试任务</returns>
        public async Task<bool> HandleTaskExecutionError(string errorMessage)
        {
            try
            {
                // 检查是否是大漠账号验证失败
                if (errorMessage.Contains("账号未登录或登录已过期"))
                {
                    LogWarning("检测到大漠插件验证失败，尝试重新登录...");

                    try
                    {
                        // 参考Timers中的CheckUserStatus方法实现
                        LogInfo("尝试重新登录用户账号...");
                        // 从本地配置文件读取账号密码
                        string username = XConfig.LoadValueFromFile<string>("Account_User") ?? "";
                        Utils.GetAppKeyAndIV(out string key, out string iv);
                        string password = XConfig.LoadValueFromFile_Decrypt("Account_Pwd", key, iv) ?? "";

                        if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                        {
                            XLogger.Error("本地未保存账号密码，无法自动登录");
                            GlobalData.Instance.appConfig.IsLogin = false;
                            GlobalData.Instance.appConfig.IsFree = false;

                            // 停止所有脚本线程
                            int stoppedCount = Scripts.StopAllScripts();
                            XLogger.Info($"已停止 {stoppedCount} 个脚本任务");

                            // 断开WebSocket连接
                            await XWebsocket.DisconnectAsync();
                            XLogger.Debug("登录状态检查失败，WebSocket连接已断开");

                            // 通知UI更新状态
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                MessageBox.Show("登录状态失效且本地未保存账号密码，所有脚本已停止运行。", "警告",
                                    MessageBoxButton.OK, MessageBoxImage.Warning);
                            });

                            return false;
                        }

                        var loginResponse = await GlobalData.Instance.appConfig.dNet.Auth.LoginAsync(username, password);
                        bool loginResult = loginResponse?.IsSuccess ?? false;
                        if (loginResult)
                        {
                            GlobalData.Instance.appConfig.IsLogin = true;

                            return true; // 继续执行任务
                        }
                        else
                        {
                            LogError("用户重新登录后大漠插件验证仍然失败，将停止调度器");
                            // 在UI线程上执行停止调度器操作
                            await _window.Dispatcher.InvokeAsync(() =>
                            {
                                StopScheduler();
                                MessageBox.Show(
                                    "蛋定账号登录已过期且自动重新登录后验证仍然失败，调度器已停止运行。\n请重新登录后再启动调度器。",
                                    "登录失败",
                                    MessageBoxButton.OK,
                                    MessageBoxImage.Warning);
                            });

                            return false; // 停止执行任务
                        }
                    }
                    catch (Exception ex)
                    {
                        LogError($"尝试重新登录时发生异常: {ex.Message}");
                        // 在UI线程上执行停止调度器操作
                        await _window.Dispatcher.InvokeAsync(() => StopScheduler());
                        return false; // 停止执行任务
                    }
                }

                // 其他类型的错误，使用常规重试策略
                return EnableTaskRetry; // 根据用户设置决定是否重试
            }
            catch (Exception ex)
            {
                LogError($"处理任务执行异常时出错: {ex.Message}");
                return EnableTaskRetry; // 发生异常时，回退到默认重试策略
            }
        }

        /// <summary>
        /// 原始的初始化方法（保留向后兼容性）
        /// </summary>
        public void Initialize()
        {
            // 修改为检查是否已初始化，避免重复调用
            if (_isInitialized)
            {
                //LogInfo("调度系统已经初始化，跳过重复初始化");
                return;
            }

            // 异步调用InitializeAsync，但不等待结果
            _ = InitializeAsync();
        }

        /// <summary>
        /// 初始化视图模型
        /// </summary>
        public async Task InitializeAsync()
        {
            // 使用更严格的初始化检查，防止并发初始化
            if (_isInitialized)
            {
                //LogInfo("调度系统已经初始化，跳过重复初始化");
                return;
            }

            // 设置标志，防止重复初始化
            _isInitialized = true;

            LogInfo("定时调度系统初始化中...");
            LoadSettings();
            StartResourceMonitoring();
            StartIdleEmulatorMonitoring();

            // 初始化任务历史记录服务
            try
            {
                _historyService = new TaskHistoryService();
                LogInfo("任务历史记录服务初始化完成");
            }
            catch (Exception ex)
            {
                LogError($"初始化任务历史记录服务失败: {ex.Message}");
            }

            // 检查并更新所有模拟器的在线状态
            UpdateEmulatorsStatus();

            // 默认选中第一个模拟器（如果存在）
            if (Emulators.Count > 0)
            {
                SelectedEmulator = Emulators[0];
                CurrentEmulatorName = ((EmulatorItem)SelectedEmulator).Name;
                LogInfo($"默认选中模拟器: {((EmulatorItem)SelectedEmulator).Name}");
            }
            else
            {
                CurrentEmulatorName = "全部模拟器";
            }

            UpdateFilteredTasks(); // 初始化过滤后的任务列表
            UpdateEmulatorsNextTask(); // 初始化模拟器的下一个任务信息
            LogInfo("定时调度系统初始化完成");

            // 如果设置了自动启动调度器，安排延迟检查用户登录状态
            if (AutoStartScheduler && !IsSchedulerRunning)
            {
                //LogInfo("根据用户设置，将在1分钟后检查用户登录状态并决定是否自动启动调度器...");
                // 创建计时器，1分钟后检查用户登录状态
                var loginCheckTimer = new System.Timers.Timer(60000); // 60秒 = 1分钟
                loginCheckTimer.Elapsed += async (sender, e) =>
                {
                    loginCheckTimer.Stop();
                    loginCheckTimer.Dispose();
                    await CheckLoginAndStartScheduler();
                };
                loginCheckTimer.AutoReset = false; // 只执行一次
                loginCheckTimer.Start();
            }
            else if (IsSchedulerRunning)
            {
                LogInfo("调度器已在运行中，跳过自动启动");
            }
        }

        /// <summary>
        /// 检查用户登录状态并决定是否启动调度器
        /// </summary>
        private async Task CheckLoginAndStartScheduler()
        {
            try
            {
                // 检查用户是否登录且不是试用状态
                bool isLoggedIn = GlobalData.Instance.appConfig.IsLogin;
                bool isFreeVersion = GlobalData.Instance.appConfig.IsFree;

                if (isLoggedIn && !isFreeVersion)
                {
                    LogInfo("检测到用户已登录且非试用状态，自动启动调度器...");
                    await StartSchedulerAsync();
                }
                else
                {
                    LogInfo($"用户未登录或处于试用状态(登录状态: {isLoggedIn}, 试用状态: {isFreeVersion})，不启动调度器");
                }
            }
            catch (Exception ex)
            {
                LogError($"检查登录状态时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 提供给外部调用的全面刷新方法，用于在导入配置后更新所有数据
        /// </summary>
        public void RefreshAll()
        {
            LogInfo("执行全面数据刷新...");

            try
            {
                // 重新加载所有配置
                LoadSettings();

                // 刷新模拟器状态
                RefreshEmulatorsCommand.Execute(null);

                // 刷新任务
                RefreshTasksCommand.Execute(null);

                // 更新过滤后的任务列表
                UpdateFilteredTasks();

                // 更新模拟器的下一个任务信息
                UpdateEmulatorsNextTask();

                // 保存所有配置
                SaveAllSchedulerConfigurations();

                // 确保选中第一个模拟器（如果存在）
                if (Emulators.Count > 0 && SelectedEmulator == null)
                {
                    SelectedEmulator = Emulators[0];
                    CurrentEmulatorName = ((EmulatorItem)SelectedEmulator).Name;
                    LogInfo($"全面刷新后自动选中模拟器: {((EmulatorItem)SelectedEmulator).Name}");
                }

                LogInfo("全面数据刷新完成");
            }
            catch (Exception ex)
            {
                LogError($"全面数据刷新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置窗口引用
        /// </summary>
        public void SetWindowReference(SchedulerWindow window)
        {
            // 如果已经设置了窗口引用且是同一个窗口，则跳过
            if (_window == window)
            {
                //LogInfo("窗口引用已经设置，跳过重复设置");
                return;
            }

            // 如果之前设置过其他窗口，先取消订阅事件
            if (_window != null)
            {
                _window.Closing -= Window_Closing;
                //LogInfo("已取消订阅旧窗口的关闭事件");
            }

            _window = window;
            // 注册窗口关闭事件
            _window.Closing += Window_Closing;
            //LogInfo("已设置新的窗口引用并订阅关闭事件");
        }

        /// <summary>
        /// 显示窗口
        /// </summary>
        public void ShowWindow()
        {
            if (_window != null && !_window.IsVisible)
            {
                // 确保窗口状态与调度器状态匹配
                if (IsSchedulerRunning)
                {
                    // 如果调度器已在运行，确保UI状态正确
                    IsLoading = false; // 确保不显示加载状态
                    CanStartScheduler = false;
                    CanStopScheduler = true;
                    LogInfo("调度器已在运行，更新UI状态");
                }

                _window.Show();
                LogInfo("调度系统窗口已显示");
            }
        }

        /// <summary>
        /// 处理窗口关闭事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        public void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // 取消关闭操作
            e.Cancel = true;

            // 隐藏窗口而不是关闭
            _window.Hide();

            // 隐藏窗口时自动保存设置
            SaveSettingsToFile();
            LogInfo("调度系统窗口已隐藏，调度器状态保持不变");
        }

        /// <summary>
        /// 更新所有模拟器的在线状态
        /// </summary>
        private async void UpdateEmulatorsStatus()
        {
            try
            {
                if (Emulators.Count == 0)
                {
                    LogInfo("没有模拟器需要更新状态");
                    return;
                }

                // 获取MuMu模拟器路径
                var path = XConfig.LoadValueFromFile<string>("MuMuPath");
                if (string.IsNullOrEmpty(path))
                {
                    LogInfo("MuMu模拟器路径未设置，无法检测模拟器状态");
                    return;
                }

                // 初始化MuMu实例
                ScriptEngine.MuMu.MuMu mumu = new();
                if (!mumu.Init(path))
                {
                    LogInfo("MuMu模拟器初始化失败，无法检测模拟器状态");
                    return;
                }

                // 获取最新的模拟器实例信息
                var mumuInstances = await mumu._GetInstancesAsync();
                if (mumuInstances == null || mumuInstances.Instances.Count == 0)
                {
                    LogInfo("未找到任何MuMu模拟器实例，所有模拟器将标记为离线");
                    // 将所有模拟器标记为离线
                    foreach (var emulator in Emulators)
                    {
                        // 保存旧状态用于比较
                        string oldStatus = emulator.Status;
                        emulator.Status = "离线";

                        // 如果状态发生变化，更新LastStatusChange
                        if (oldStatus != "离线")
                        {
                            emulator.LastStatusChange = "离线";
                            LogInfo($"模拟器 \"{emulator.Name}\" 状态变更为离线");
                        }
                    }
                    return;
                }

                int onlineCount = 0;
                int offlineCount = 0;

                // 更新每个模拟器的状态
                foreach (var emulator in Emulators)
                {
                    bool isOnline = false;

                    // 如果模拟器有配置信息，通过配置信息查找
                    if (emulator.SimulatorConfig != null)
                    {
                        // 查找匹配的模拟器实例
                        var matchingInstance = mumuInstances.Instances.Values
                            .FirstOrDefault(i => i.Name == emulator.Name);

                        if (matchingInstance != null)
                        {
                            // 检查模拟器是否正在运行
                            isOnline = MumuUtils.IsSimulatorRunning(
                                matchingInstance,
                                out int mainWndHandle,
                                out int renderWndHandle);
                        }
                    }

                    // 保存旧状态用于比较
                    string oldStatus = emulator.Status;

                    // 更新模拟器状态
                    emulator.Status = isOnline ? "在线" : "离线";

                    // 如果状态发生变化，更新LastStatusChange
                    if (oldStatus != emulator.Status)
                    {
                        emulator.LastStatusChange = emulator.Status;
                        LogInfo($"模拟器 \"{emulator.Name}\" 状态变更为 {emulator.Status}");

                        // 如果模拟器上线，重置其运行时长和最后活动时间
                        if (emulator.Status == "在线")
                        {
                            emulator.RunningDuration = "0:00:00";
                            emulator.LastActivityTime = DateTime.Now;
                            LogInfo($"已重置模拟器 \"{emulator.Name}\" 的空闲计时和运行时长");
                        }
                    }

                    // 更新计数器
                    if (isOnline) onlineCount++; else offlineCount++;
                }

                // 更新当前活跃模拟器数量指示器
                ActiveEmulators = onlineCount.ToString();

                LogInfo($"模拟器状态检测完成: {onlineCount} 个在线, {offlineCount} 个离线");

                // 确保UI刷新
                OnPropertyChanged(nameof(Emulators));

                // 强制刷新活跃模拟器计数显示
                UpdateActiveEmulatorsCount();
            }
            catch (Exception ex)
            {
                LogError($"检测模拟器状态时出错: {ex.Message}");
            }
        }

        #endregion 公共方法

        #region 命令

        /// <summary>
        /// 打开任务历史记录窗口命令
        /// </summary>
        [RelayCommand]
        private void OpenTaskHistory()
        {
            LogInfo("打开任务历史记录窗口");

            try
            {
                // 获取所有模拟器名称列表
                List<string> emulatorNames = Emulators.Select(e => e.Name).ToList();

                // 创建并显示历史记录窗口
                var historyWindow = new Views.Windows.TaskHistoryWindow();

                // 初始化窗口，传递模拟器名称列表
                historyWindow.Initialize(emulatorNames);

                // 设置窗口的Owner为当前窗口
                if (_window != null)
                {
                    historyWindow.Owner = _window;
                }

                // 显示为对话框，但不阻塞主窗口
                historyWindow.Show();

                LogInfo("任务历史记录窗口已打开");
            }
            catch (Exception ex)
            {
                LogError($"打开任务历史记录窗口失败: {ex.Message}");
                MessageBox.Show($"打开任务历史记录窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 添加模拟器命令
        /// </summary>
        [RelayCommand]
        private async void AddEmulator()
        {
            LogInfo("准备添加新的模拟器");

            try
            {
                // 创建并显示模拟器选择窗口
                var configWindow = new Views.Windows.MuMuConfigsWindow(true);
                configWindow.ShowDialog(); // 使用ShowDialog确保用户关闭窗口后才继续

                // 检查用户是否选择了模拟器
                if (!configWindow.HasSelectedSimulator || configWindow.SelectedSimulator == null)
                {
                    LogInfo("用户取消了添加模拟器");
                    return;
                }

                var selectedSimulator = configWindow.SelectedSimulator;

                // 检查是否已经添加了该模拟器（确保唯一性）
                if (Emulators.Any(e => e.Name == selectedSimulator.Name))
                {
                    LogInfo($"模拟器 {selectedSimulator.Name} 已经存在，不能重复添加");
                    MessageBox.Show($"模拟器 {selectedSimulator.Name} 已经存在，不能重复添加", "添加失败", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 生成新的模拟器ID
                string newId = Emulators.Count > 0 ? (int.Parse(Emulators.Max(e => e.Id)) + 1).ToString() : "1";

                // 检查模拟器是否在线
                bool isOnline = false;
                int mainWndHandle = 0;
                int renderWndHandle = 0;

                // 获取MuMu模拟器路径
                var path = XConfig.LoadValueFromFile<string>("MuMuPath");
                if (!string.IsNullOrEmpty(path))
                {
                    // 初始化MuMu实例
                    ScriptEngine.MuMu.MuMu mumu = new();
                    if (mumu.Init(path))
                    {
                        // 获取最新的模拟器实例信息
                        var mumuInstances = await mumu._GetInstancesAsync();
                        if (mumuInstances != null && mumuInstances.Instances.Count > 0)
                        {
                            // 查找匹配的模拟器实例
                            var matchingInstance = mumuInstances.Instances.Values
                                .FirstOrDefault(i => i.Name == selectedSimulator.Name);

                            if (matchingInstance != null)
                            {
                                // 检查模拟器是否正在运行
                                isOnline = MumuUtils.IsSimulatorRunning(
                                    matchingInstance,
                                    out mainWndHandle,
                                    out renderWndHandle);
                            }
                        }
                    }
                }

                // 创建新的模拟器项
                var newEmulator = new EmulatorItem
                {
                    Id = newId,
                    Name = selectedSimulator.Name,
                    Status = isOnline ? "在线" : "离线",
                    CurrentTask = "无",
                    NextTask = "无待执行任务",
                    RunningDuration = "0:00:00",
                    Enabled = true,
                    SimulatorIndex = selectedSimulator.Index.ToString(),
                    SimulatorConfig = selectedSimulator,
                    Config = new DanDing1.ViewModels.Windows.SuperMultiGameConfigWindowViewModel()
                };

                // 添加到模拟器集合
                Emulators.Add(newEmulator);

                // 更新活跃模拟器计数
                UpdateActiveEmulatorsCount();

                // 保存模拟器配置
                SaveEmulatorsConfigToFile();

                LogInfo($"成功添加模拟器：{newEmulator.Name}，ID：{newId}，状态：{newEmulator.Status}");

                // 如果这是第一个添加的模拟器，则自动选中它
                if (Emulators.Count == 1)
                {
                    SelectedEmulator = newEmulator;
                }
            }
            catch (Exception ex)
            {
                LogError($"添加模拟器失败: {ex.Message}");
                MessageBox.Show($"添加模拟器失败: {ex.Message}", "添加失败", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 添加日志条目，并控制日志条目数量
        /// </summary>
        private void AddLogEntry(string logEntry)
        {
            // 添加到日志条目列表
            _logEntries.Add(logEntry);

            // 如果超过最大条目数，移除最旧的条目
            while (_logEntries.Count > MaxLogEntries)
            {
                _logEntries.RemoveAt(0);
            }

            // 更新日志显示内容
            MainLog = string.Join(Environment.NewLine, _logEntries);

            // 触发日志更新事件
            OnMainLogUpdated?.Invoke();
        }

        /// <summary>
        /// 添加定时任务命令
        /// </summary>
        [RelayCommand]
        private void AddScheduledTask()
        {
            LogInfo("准备添加新的定时任务");

            try
            {
                // 创建并配置任务编辑窗口
                var taskEditWindow = new Views.Windows.SchedulerTaskEditWindow();
                var viewModel = taskEditWindow.DataContext as SchedulerTaskEditViewModel;

                // 设置可用模拟器列表
                viewModel.AvailableEmulators = new ObservableCollection<EmulatorItem>(Emulators);

                // 设置当前选中的模拟器
                if (SelectedEmulator != null && SelectedEmulator is EmulatorItem)
                    viewModel.SelectedEmulator = (EmulatorItem)SelectedEmulator;
                else if (Emulators.Count > 0)
                    viewModel.SelectedEmulator = Emulators[0];
                else
                {
                    MessageBox.Show("没有可用的模拟器，请先添加模拟器！", "添加任务", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 显示任务编辑窗口
                taskEditWindow.Owner = _window;
                var result = taskEditWindow.ShowDialog();

                if (result == true)
                {
                    // 获取创建的调度任务
                    var task = taskEditWindow.GetScheduledTask();

                    // 检查任务是否为null（未选择任务类型或创建失败）
                    if (task == null)
                    {
                        LogInfo("任务创建失败，未添加任务");
                        return;
                    }

                    // 添加到任务列表
                    ScheduledTasks.Add(task);
                    LogInfo($"成功添加任务：{task.Name}，调度类型：{task.ScheduleType}，下次执行时间：{task.NextExecutionTime}");

                    // 保存任务配置
                    SaveTasksConfigToFile();

                    // 更新过滤后的任务列表
                    UpdateFilteredTasks();
                    // 更新模拟器的下一个任务信息
                    UpdateEmulatorsNextTask();
                }
                else
                {
                    LogInfo("用户取消了添加任务");
                }
            }
            catch (Exception ex)
            {
                LogError($"添加任务失败: {ex.Message}");
                MessageBox.Show($"添加任务失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除选中的模拟器
        /// </summary>
        [RelayCommand]
        private void DeleteSelectedEmulator(object emulator)
        {
            if (emulator == null)
                return;

            var emulatorItem = (EmulatorItem)emulator;
            LogInfo($"删除模拟器: {emulatorItem.Name}");

            // 检查有多少任务与该模拟器关联
            var relatedTasks = ScheduledTasks.Where(t => t.EmulatorName == emulatorItem.Name).ToList();
            int taskCount = relatedTasks.Count;

            string confirmMessage = $"确定要删除模拟器 \"{emulatorItem.Name}\" 吗？";
            if (taskCount > 0)
            {
                confirmMessage += $"\n\n注意：将同时删除与该模拟器关联的 {taskCount} 个定时任务！";
            }

            if (MessageBox.Show(confirmMessage, "确认删除",
                MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes)
            {
                // 先删除关联的任务
                if (taskCount > 0)
                {
                    foreach (var task in relatedTasks)
                    {
                        ScheduledTasks.Remove(task);
                    }
                    LogInfo($"已删除与模拟器 \"{emulatorItem.Name}\" 关联的 {taskCount} 个任务");

                    // 保存任务配置
                    SaveTasksConfigToFile();

                    // 更新过滤后的任务列表
                    UpdateFilteredTasks();
                }

                // 删除模拟器
                Emulators.Remove(emulatorItem);
                LogInfo($"模拟器 \"{emulatorItem.Name}\" 已删除");

                // 保存模拟器配置
                SaveEmulatorsConfigToFile();

                // 如果删除的是当前选中的模拟器，清除选中状态
                if (SelectedEmulator == emulatorItem)
                {
                    SelectedEmulator = Emulators.Count > 0 ? Emulators[0] : null;
                }

                // 更新模拟器的下一个任务信息
                UpdateEmulatorsNextTask();
            }
        }

        /// <summary>
        /// 删除选中任务命令
        /// </summary>
        [RelayCommand]
        private void DeleteSelectedTask(object task)
        {
            if (task == null)
                return;

            var scheduledTask = (ScheduledTask)task;
            LogInfo($"删除任务: {scheduledTask.Name}");

            if (MessageBox.Show($"确定要删除任务 \"{scheduledTask.Name}\" 吗？", "确认删除",
                MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes)
            {
                ScheduledTasks.Remove(scheduledTask);
                LogInfo($"任务 \"{scheduledTask.Name}\" 已删除");

                // 保存任务配置
                SaveTasksConfigToFile();

                // 更新过滤后的任务列表
                UpdateFilteredTasks();
                // 更新模拟器的下一个任务信息
                UpdateEmulatorsNextTask();
            }
        }

        /// <summary>
        /// 编辑模拟器的游戏配置
        /// </summary>
        [RelayCommand]
        private void EditEmulatorConfig(object emulator)
        {
            if (emulator is EmulatorItem emulatorItem)
            {
                try
                {
                    // 确保Config不为空
                    emulatorItem.Config ??= new DanDing1.ViewModels.Windows.SuperMultiGameConfigWindowViewModel();

                    // 保存原始配置的副本
                    var originalConfig = emulatorItem.Config.Clone() as SuperMultiGameConfigWindowViewModel;

                    // 创建新的配置窗口实例，使用emulatorItem的Config
                    var configWindow = new SuperMultiGameConfigWindow(emulatorItem.Config);
                    configWindow.ShowDialog();

                    if (configWindow.DialogResult)
                    {
                        // 用户确认了配置，保存
                        LogInfo($"模拟器 \"{emulatorItem.Name}\" 的游戏配置已更新");
                        SaveEmulatorsConfigToFile();
                    }
                    else
                    {
                        // 用户取消了配置，恢复原始配置
                        emulatorItem.Config = originalConfig;
                        LogInfo($"取消保存模拟器 \"{emulatorItem.Name}\" 的游戏配置");
                    }
                }
                catch (Exception ex)
                {
                    LogError($"编辑模拟器配置时出错: {ex.Message}");
                    MessageBox.Show($"编辑模拟器配置时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 编辑选中任务命令
        /// </summary>
        [RelayCommand]
        private void EditSelectedTask(object task)
        {
            if (task == null)
                return;

            var scheduledTask = (ScheduledTask)task;
            LogInfo($"准备编辑任务: {scheduledTask.Name}");

            try
            {
                // 创建并配置任务编辑窗口
                var taskEditWindow = new Views.Windows.SchedulerTaskEditWindow();
                var viewModel = taskEditWindow.DataContext as SchedulerTaskEditViewModel;

                // 设置可用模拟器列表
                viewModel.AvailableEmulators = new ObservableCollection<EmulatorItem>(Emulators);

                // 预填充任务信息
                viewModel.LoadExistingTask(scheduledTask);

                // 设置任务编辑窗口的任务列表
                if (!string.IsNullOrEmpty(scheduledTask.TaskParameters))
                {
                    try
                    {
                        taskEditWindow.SetTaskParameters(scheduledTask.TaskParameters);
                    }
                    catch (Exception ex)
                    {
                        LogError($"设置任务参数失败: {ex.Message}");
                    }
                }

                // 显示任务编辑窗口
                taskEditWindow.Owner = _window;
                var result = taskEditWindow.ShowDialog();

                if (result == true)
                {
                    // 获取编辑后的任务
                    var updatedTask = taskEditWindow.GetScheduledTask();

                    // 检查任务是否为null
                    if (updatedTask == null)
                    {
                        LogInfo("任务编辑失败，未更新任务");
                        return;
                    }

                    // 保留原任务ID
                    updatedTask.Id = scheduledTask.Id;

                    // 更新任务列表中的任务
                    int index = ScheduledTasks.IndexOf(scheduledTask);
                    if (index >= 0)
                    {
                        ScheduledTasks[index] = updatedTask;
                        LogInfo($"成功更新任务：{updatedTask.Name}，调度类型：{updatedTask.ScheduleType}，下次执行时间：{updatedTask.NextExecutionTime}");

                        // 保存任务配置
                        SaveTasksConfigToFile();

                        // 更新过滤后的任务列表
                        UpdateFilteredTasks();
                        // 更新模拟器的下一个任务信息
                        UpdateEmulatorsNextTask();
                    }
                }
                else
                {
                    LogInfo("用户取消了编辑任务");
                }
            }
            catch (Exception ex)
            {
                LogError($"编辑任务失败: {ex.Message}");
                MessageBox.Show($"编辑任务失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 立即执行任务命令
        /// </summary>
        [RelayCommand]
        private void ExecuteTaskNow(object task)
        {
            if (task == null)
                return;

            var scheduledTask = (ScheduledTask)task;
            LogInfo($"立即执行任务: {scheduledTask.Name}");
            // TODO: 实现任务执行逻辑
            MessageBox.Show("立即执行任务功能尚未实现，敬请期待！", "功能开发中", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 将所有配置导出为单个文件的命令
        /// </summary>
        [RelayCommand]
        private void ExportMergedConfig()
        {
            LogInfo("开始导出配置文件");

            try
            {
                // 创建合并配置对象
                var mergedConfig = new
                {
                    Version = "1.0",
                    ExportTime = DateTime.Now,
                    SystemConfig = new
                    {
                        MaxConcurrentEmulators = MaxConcurrentEmulators,
                        DefaultTaskTimeout = DefaultTaskTimeout,
                        AutoShutdownIdleTime = AutoShutdownIdleTime,
                        EnableTaskRetry = EnableTaskRetry,
                        MaxRetryCount = MaxRetryCount,
                        IdleShutdownEnabled = IdleShutdownEnabled,
                        AutoStartScheduler = AutoStartScheduler
                    },
                    Emulators = Emulators,
                    ScheduledTasks = ScheduledTasks
                };

                // 显示保存文件对话框
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "JSON文件|*.json",
                    Title = "导出配置",
                    FileName = $"蛋定调度配置_{DateTime.Now:yyyyMMdd}.json"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // 序列化配置为JSON并保存到文件
                    var options = new JsonSerializerOptions
                    {
                        WriteIndented = true,
                        IgnoreNullValues = true
                    };

                    string jsonString = JsonSerializer.Serialize(mergedConfig, options);
                    File.WriteAllText(saveFileDialog.FileName, jsonString, Encoding.UTF8);

                    LogInfo($"配置已成功导出到: {saveFileDialog.FileName}");
                    MessageBox.Show($"配置已成功导出到: {saveFileDialog.FileName}", "导出成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                LogError($"导出配置失败: {ex.Message}");
                MessageBox.Show($"导出配置失败: {ex.Message}", "导出失败", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 获取调度器配置目录路径，如不存在则创建
        /// </summary>
        private string GetSchedulerConfigDirectory()
        {
            try
            {
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                string configDir = Path.Combine(baseDir, "runtimes", "AppConfig", "Scheduler");

                // 确保目录存在
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                    LogInfo($"创建调度器配置目录: {configDir}");

                    // 同时创建一个默认配置文件
                    SaveSettingsToFile();
                }

                return configDir;
            }
            catch (Exception ex)
            {
                LogError($"获取或创建配置目录时出错: {ex.Message}");

                // 如果无法创建指定目录，则尝试在临时目录中创建
                try
                {
                    string tempDir = Path.Combine(Path.GetTempPath(), "DanDing1", "Scheduler");
                    if (!Directory.Exists(tempDir))
                    {
                        Directory.CreateDirectory(tempDir);
                        LogInfo($"创建备用配置目录: {tempDir}");
                    }
                    return tempDir;
                }
                catch
                {
                    // 如果还是失败，则返回当前目录
                    LogError("无法创建配置目录，将使用当前目录");
                    return Directory.GetCurrentDirectory();
                }
            }
        }

        /// <summary>
        /// 从合并配置文件导入
        /// </summary>
        [RelayCommand]
        private async void ImportMergedConfig()
        {
            LogInfo("开始导入配置文件");

            try
            {
                // 显示打开文件对话框
                var openFileDialog = new OpenFileDialog
                {
                    Filter = "JSON文件|*.json",
                    Title = "导入配置"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    // 显示确认提示
                    var result = MessageBox.Show(
                        "导入配置将覆盖现有设置，是否继续？",
                        "确认导入",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result != MessageBoxResult.Yes)
                        return;

                    // 读取并解析JSON文件
                    string jsonString = File.ReadAllText(openFileDialog.FileName, Encoding.UTF8);
                    var importedConfig = JsonNode.Parse(jsonString);

                    if (importedConfig != null)
                    {
                        // 导入系统配置
                        var systemConfig = importedConfig["SystemConfig"];
                        if (systemConfig != null)
                        {
                            MaxConcurrentEmulators = (int)systemConfig["MaxConcurrentEmulators"];
                            DefaultTaskTimeout = (int)systemConfig["DefaultTaskTimeout"];
                            AutoShutdownIdleTime = (int)systemConfig["AutoShutdownIdleTime"];
                            EnableTaskRetry = (bool)systemConfig["EnableTaskRetry"];
                            MaxRetryCount = (int)systemConfig["MaxRetryCount"];

                            // IdleShutdownEnabled属性可能在旧配置中不存在
                            if (systemConfig["IdleShutdownEnabled"] != null)
                            {
                                IdleShutdownEnabled = (bool)systemConfig["IdleShutdownEnabled"];
                            }

                            // AutoStartScheduler属性可能在旧配置中不存在
                            if (systemConfig["AutoStartScheduler"] != null)
                            {
                                AutoStartScheduler = (bool)systemConfig["AutoStartScheduler"];
                            }
                        }

                        // 导入模拟器
                        var emulatorsJson = importedConfig["Emulators"].ToJsonString();
                        var emulators = JsonSerializer.Deserialize<ObservableCollection<EmulatorItem>>(emulatorsJson);

                        // 导入任务
                        var tasksJson = importedConfig["ScheduledTasks"].ToJsonString();
                        var tasks = JsonSerializer.Deserialize<ObservableCollection<ScheduledTask>>(tasksJson);

                        if (emulators != null && tasks != null)
                        {
                            // 获取当前系统中的MuMu模拟器实例
                            Dictionary<string, Models.SimulatorConfig> localMumuInstances = new Dictionary<string, Models.SimulatorConfig>();

                            // 获取MuMu模拟器路径
                            var path = XConfig.LoadValueFromFile<string>("MuMuPath");
                            if (!string.IsNullOrEmpty(path))
                            {
                                // 初始化MuMu实例
                                ScriptEngine.MuMu.MuMu mumu = new();
                                if (mumu.Init(path))
                                {
                                    try
                                    {
                                        // 获取最新的模拟器实例信息
                                        var mumuInstancesInfo = await mumu._GetInstancesAsync();
                                        if (mumuInstancesInfo != null && mumuInstancesInfo.Instances.Count > 0)
                                        {
                                            LogInfo($"成功获取当前系统中的 {mumuInstancesInfo.Instances.Count} 个MuMu模拟器实例");

                                            // 获取所有配置的模拟器
                                            ObservableCollection<Models.SimulatorConfig> configs =
                                                XConfig.LoadValueFromFile<ObservableCollection<Models.SimulatorConfig>>("MuMuConfigs");

                                            if (configs != null && configs.Count > 0)
                                            {
                                                foreach (var config in configs)
                                                {
                                                    localMumuInstances[config.Name] = config;
                                                }
                                                LogInfo($"成功加载 {configs.Count} 个本地模拟器配置");
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        LogError($"获取本地MuMu模拟器实例时出错: {ex.Message}");
                                    }
                                }
                            }

                            // 更新模拟器列表
                            Emulators.Clear();
                            foreach (var emulator in emulators)
                            {
                                // 确保每个模拟器都有Config属性
                                if (emulator.Config == null)
                                {
                                    emulator.Config = new SuperMultiGameConfigWindowViewModel();
                                    LogInfo($"为模拟器 {emulator.Name} 创建默认游戏配置");
                                }

                                // 尝试用本地模拟器实例数据更新导入的模拟器
                                if (localMumuInstances.ContainsKey(emulator.Name))
                                {
                                    var localConfig = localMumuInstances[emulator.Name];
                                    LogInfo($"发现本地匹配模拟器: {emulator.Name}，使用本地模拟器配置更新");

                                    // 保留原ID和名称，更新模拟器配置
                                    string originalId = emulator.Id;
                                    string originalName = emulator.Name;

                                    // 更新模拟器配置信息
                                    emulator.SimulatorConfig = localConfig;
                                    emulator.SimulatorIndex = localConfig.Index.ToString();

                                    // 确保ID和名称不变
                                    emulator.Id = originalId;
                                    emulator.Name = originalName;
                                }
                                else
                                {
                                    LogInfo($"未找到本地匹配的模拟器: {emulator.Name}，使用导入的配置");
                                }

                                Emulators.Add(emulator);
                            }

                            // 更新任务列表
                            ScheduledTasks.Clear();
                            foreach (var task in tasks)
                            {
                                ScheduledTasks.Add(task);
                            }

                            // 保存配置到文件
                            SaveAllSchedulerConfigurations();

                            // 刷新UI
                            RefreshAll();

                            LogInfo("配置导入成功");
                            MessageBox.Show("配置已成功导入", "导入成功", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"导入配置失败: {ex.Message}");
                MessageBox.Show($"导入配置失败: {ex.Message}\n\n请确保导入的是有效的蛋定调度配置文件。", "导入失败", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        private void LogError(string message)
        {
            string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [ERROR] {message}";
            AddLogEntry(logEntry);
            Debug.WriteLine($"[调度系统] {message}");
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        private void LogInfo(string message)
        {
            string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [INFO] {message}";
            AddLogEntry(logEntry);
            Debug.WriteLine($"[调度系统] {message}");
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        private void LogWarning(string message)
        {
            string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [WARN] {message}";
            AddLogEntry(logEntry);
            Debug.WriteLine($"[调度系统] {message}");
        }

        /// <summary>
        /// 刷新模拟器列表命令
        /// </summary>
        [RelayCommand]
        private async void RefreshEmulators()
        {
            LogInfo("刷新模拟器列表");

            try
            {
                // 首先从配置文件重新加载模拟器配置
                string configDir = GetSchedulerConfigDirectory();
                string configPath = Path.Combine(configDir, "SchedulerEmulators.json");
                if (File.Exists(configPath))
                {
                    string json = File.ReadAllText(configPath);
                    var loadedEmulators = JsonSerializer.Deserialize<ObservableCollection<EmulatorItem>>(json);

                    if (loadedEmulators != null)
                    {
                        // 保存现有模拟器ID与对象的映射
                        var existingEmulatorsById = Emulators.ToDictionary(e => e.Id);

                        // 清空当前模拟器列表
                        Emulators.Clear();

                        // 添加从配置文件加载的模拟器
                        foreach (var emulator in loadedEmulators)
                        {
                            // 确保每个模拟器都有Config属性
                            if (emulator.Config == null)
                            {
                                emulator.Config = new SuperMultiGameConfigWindowViewModel();
                                LogInfo($"为模拟器 {emulator.Name} 创建默认游戏配置");
                            }

                            Emulators.Add(emulator);
                        }

                        LogInfo($"已从配置文件重新加载 {loadedEmulators.Count} 个模拟器配置");
                    }
                }
                else
                {
                    LogInfo("未找到模拟器配置文件，无法从配置加载模拟器列表");
                }

                // 获取MuMu模拟器路径
                var path = XConfig.LoadValueFromFile<string>("MuMuPath");
                if (string.IsNullOrEmpty(path))
                {
                    LogInfo("MuMu模拟器路径未设置，无法刷新模拟器状态");
                    MessageBox.Show("MuMu模拟器路径未设置，请先配置模拟器", "刷新失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 初始化MuMu实例
                ScriptEngine.MuMu.MuMu mumu = new();
                if (!mumu.Init(path))
                {
                    LogInfo("MuMu模拟器初始化失败，无法刷新模拟器状态");
                    MessageBox.Show("MuMu模拟器初始化失败，请检查路径设置", "刷新失败", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 获取最新的模拟器实例信息
                var mumuInstances = await mumu._GetInstancesAsync();
                if (mumuInstances == null || mumuInstances.Instances.Count == 0)
                {
                    LogInfo("未找到任何MuMu模拟器实例");

                    // 将所有模拟器标记为离线
                    foreach (var emulator in Emulators)
                    {
                        string oldStatus = emulator.Status;
                        emulator.Status = "离线";

                        // 如果状态从在线变为离线，更新LastStatusChange
                        if (oldStatus == "在线")
                        {
                            emulator.LastStatusChange = "离线";
                            LogInfo($"模拟器 \"{emulator.Name}\" 状态变更为离线");
                        }
                    }

                    // 更新活跃模拟器数量
                    ActiveEmulators = "0";

                    return;
                }

                // 更新每个模拟器的状态
                int onlineCount = 0;
                int offlineCount = 0;

                foreach (var emulator in Emulators)
                {
                    // 查找匹配的模拟器实例
                    var matchingInstance = mumuInstances.Instances.Values
                        .FirstOrDefault(i => i.Name == emulator.Name);

                    string oldStatus = emulator.Status;
                    bool isOnline = false;

                    if (matchingInstance != null)
                    {
                        // 检查模拟器是否正在运行
                        isOnline = MumuUtils.IsSimulatorRunning(
                            matchingInstance,
                            out int mainWndHandle,
                            out int renderWndHandle);
                    }

                    // 更新模拟器状态
                    emulator.Status = isOnline ? "在线" : "离线";

                    // 如果状态发生了变化
                    if (oldStatus != emulator.Status)
                    {
                        emulator.LastStatusChange = emulator.Status;
                        LogInfo($"模拟器 \"{emulator.Name}\" 状态变更为 {emulator.Status}");

                        // 如果模拟器新上线，重置计时
                        if (emulator.Status == "在线")
                        {
                            emulator.RunningDuration = "0:00:00";
                            emulator.LastActivityTime = DateTime.Now;
                            LogInfo($"已重置模拟器 \"{emulator.Name}\" 的空闲计时和运行时长");
                        }
                    }

                    // 更新计数
                    if (isOnline)
                        onlineCount++;
                    else
                        offlineCount++;
                }

                // 更新活跃模拟器数量
                ActiveEmulators = onlineCount.ToString();

                // 保存更新后的模拟器配置
                SaveEmulatorsConfigToFile();

                LogInfo($"成功刷新模拟器状态: {onlineCount} 个在线, {offlineCount} 个离线");

                // 强制刷新UI
                _window?.Dispatcher.Invoke(() =>
                {
                    OnPropertyChanged(nameof(Emulators));
                    // 强制活跃模拟器数量刷新
                    UpdateActiveEmulatorsCount();

                    // 自动选中第一个模拟器（如果存在）
                    if (Emulators.Count > 0 && SelectedEmulator == null)
                    {
                        SelectedEmulator = Emulators[0];
                        CurrentEmulatorName = ((EmulatorItem)SelectedEmulator).Name;
                        LogInfo($"刷新后自动选中模拟器: {((EmulatorItem)SelectedEmulator).Name}");
                    }
                    else if (Emulators.Count == 0)
                    {
                        SelectedEmulator = null;
                        CurrentEmulatorName = "全部模拟器";
                    }
                });
            }
            catch (Exception ex)
            {
                LogError($"刷新模拟器列表失败: {ex.Message}");
                MessageBox.Show($"刷新模拟器列表失败: {ex.Message}", "刷新失败", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 刷新任务列表命令
        /// </summary>
        [RelayCommand]
        private void RefreshTasks()
        {
            LogInfo("刷新任务列表");

            try
            {
                // 从配置文件重新加载任务
                string configDir = GetSchedulerConfigDirectory();
                string configPath = Path.Combine(configDir, "SchedulerTasks.json");
                if (File.Exists(configPath))
                {
                    string json = File.ReadAllText(configPath);
                    var tasks = JsonSerializer.Deserialize<ObservableCollection<ScheduledTask>>(json);
                    if (tasks != null)
                    {
                        // 保存任务ID与对象的映射，用于保留未在配置文件中但在内存中的任务
                        var existingTasksById = ScheduledTasks.ToDictionary(t => t.Id);

                        // 清空当前任务列表
                        ScheduledTasks.Clear();

                        // 添加从配置文件加载的任务
                        foreach (var task in tasks)
                        {
                            ScheduledTasks.Add(task);
                        }

                        LogInfo($"已从配置文件重新加载 {tasks.Count} 个任务");
                    }
                }
                else
                {
                    LogInfo("未找到任务配置文件，无法刷新任务列表");
                }
            }
            catch (Exception ex)
            {
                LogError($"从配置文件刷新任务列表失败: {ex.Message}");
            }

            // 更新过滤后的任务列表
            UpdateFilteredTasks();
            // 更新模拟器的下一个任务信息
            UpdateEmulatorsNextTask();
        }

        /// <summary>
        /// 恢复默认设置命令
        /// </summary>
        [RelayCommand]
        private void RestoreDefaultSettings()
        {
            LogInfo("恢复系统默认设置");

            // 显示确认对话框
            if (MessageBox.Show("确定要恢复所有设置为系统默认值吗？", "确认恢复默认设置",
                MessageBoxButton.YesNo, MessageBoxImage.Question) != MessageBoxResult.Yes)
            {
                return;
            }

            // 恢复所有设置为默认值
            MaxConcurrentEmulators = 4;
            DefaultTaskTimeout = 3600;
            AutoShutdownIdleTime = 300;
            EnableTaskRetry = true;
            MaxRetryCount = 3;
            IdleShutdownEnabled = true;
            AutoStartScheduler = false;

            // 保存恢复后的设置
            SaveSettingsToFile();
            LogInfo("已将所有设置恢复为默认值");
        }

        /// <summary>
        /// 保存所有调度配置
        /// </summary>
        private void SaveAllSchedulerConfigurations()
        {
            try
            {
                // 1. 保存系统配置
                SaveSettingsToFile();

                // 2. 保存任务配置
                SaveTasksConfigToFile();

                // 3. 保存模拟器配置
                SaveEmulatorsConfigToFile();
            }
            catch (Exception ex)
            {
                LogError($"自动保存调度配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存模拟器配置到文件
        /// </summary>
        private void SaveEmulatorsConfigToFile()
        {
            try
            {
                string configDir = GetSchedulerConfigDirectory();
                string configPath = Path.Combine(configDir, "SchedulerEmulators.json");
                string json = JsonSerializer.Serialize(Emulators, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(configPath, json);
            }
            catch (Exception ex)
            {
                LogError($"保存模拟器配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存设置命令
        /// </summary>
        [RelayCommand]
        private void SaveSettings()
        {
            LogInfo("保存系统设置");
            // 保存所有配置
            SaveAllSchedulerConfigurations();
        }

        /// <summary>
        /// 保存设置到文件
        /// </summary>
        private void SaveSettingsToFile()
        {
            try
            {
                var config = new SchedulerConfig
                {
                    MaxConcurrentEmulators = MaxConcurrentEmulators,
                    DefaultTaskTimeout = DefaultTaskTimeout,
                    AutoShutdownIdleTime = AutoShutdownIdleTime,
                    EnableTaskRetry = EnableTaskRetry,
                    MaxRetryCount = MaxRetryCount,
                    IdleShutdownEnabled = IdleShutdownEnabled,
                    AutoStartScheduler = AutoStartScheduler
                };

                string configDir = GetSchedulerConfigDirectory();

                // 确保目录存在
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                    LogInfo($"创建配置目录: {configDir}");
                }

                string configPath = Path.Combine(configDir, "SchedulerConfig.json");
                string json = JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true });

                // 先将配置写入临时文件，然后重命名，以避免写入过程中文件损坏
                string tempFilePath = Path.Combine(configDir, $"SchedulerConfig.temp.{Guid.NewGuid()}.json");
                File.WriteAllText(tempFilePath, json);

                // 如果目标文件存在，先删除
                if (File.Exists(configPath))
                {
                    // 尝试备份原有配置
                    try
                    {
                        string backupPath = Path.Combine(configDir, "SchedulerConfig.backup.json");
                        File.Copy(configPath, backupPath, true);
                    }
                    catch (Exception ex)
                    {
                        LogInfo($"创建配置备份失败: {ex.Message}");
                    }

                    File.Delete(configPath);
                }

                // 将临时文件重命名为正式文件名
                File.Move(tempFilePath, configPath);

                // 删除成功保存配置的日志提示
                // LogInfo($"配置已成功保存到: {configPath}");
            }
            catch (Exception ex)
            {
                LogError($"保存配置失败: {ex.Message}");

                try
                {
                    // 尝试保存到临时文件
                    var config = new SchedulerConfig
                    {
                        MaxConcurrentEmulators = MaxConcurrentEmulators,
                        DefaultTaskTimeout = DefaultTaskTimeout,
                        AutoShutdownIdleTime = AutoShutdownIdleTime,
                        EnableTaskRetry = EnableTaskRetry,
                        MaxRetryCount = MaxRetryCount,
                        IdleShutdownEnabled = IdleShutdownEnabled,
                        AutoStartScheduler = AutoStartScheduler
                    };

                    string tempDir = Path.Combine(Path.GetTempPath(), "DanDing1_Scheduler_Backup");
                    if (!Directory.Exists(tempDir))
                        Directory.CreateDirectory(tempDir);

                    string tempPath = Path.Combine(tempDir, $"SchedulerConfig_{DateTime.Now:yyyyMMdd_HHmmss}.json");
                    string json = JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true });
                    File.WriteAllText(tempPath, json);

                    // 删除备份配置成功的日志提示
                    // LogInfo($"配置已备份到临时文件: {tempPath}");
                }
                catch (Exception backupEx)
                {
                    LogError($"备份配置到临时文件也失败: {backupEx.Message}");
                }
            }
        }

        /// <summary>
        /// 保存任务配置到文件
        /// </summary>
        private void SaveTasksConfigToFile()
        {
            try
            {
                string configDir = GetSchedulerConfigDirectory();
                string configPath = Path.Combine(configDir, "SchedulerTasks.json");
                string json = JsonSerializer.Serialize(ScheduledTasks, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(configPath, json);
            }
            catch (Exception ex)
            {
                LogError($"保存任务配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 开始资源监控
        /// </summary>
        private void StartResourceMonitoring()
        {
            LogInfo("开始资源监控");
            try
            {
                // 在后台线程初始化性能计数器和更新第一次资源使用率
                Task.Run(() =>
                {
                    try
                    {
                        // 初始化性能计数器
                        _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                        _ramCounter = new PerformanceCounter("Memory", "Available MBytes");

                        // 第一次读取CPU计数器通常会返回0，这里预先调用一次
                        _cpuCounter.NextValue();
                        System.Threading.Thread.Sleep(100);

                        // 创建并启动定时器
                        _resourceMonitorTimer = new System.Timers.Timer(3000);
                        _resourceMonitorTimer.Elapsed += (sender, e) =>
                        {
                            UpdateResourceUsage();
                        };
                        _resourceMonitorTimer.AutoReset = true;
                        _resourceMonitorTimer.Enabled = true;

                        // 在后台线程完成初始化后，调用一次更新
                        UpdateResourceUsage();
                    }
                    catch (Exception ex)
                    {
                        // 使用 XLogger 记录错误，确保即使在窗口引用不存在的情况下也能记录错误
                        XLogger.Error($"资源监控初始化失败: {ex.Message}");

                        // 如果窗口引用存在，也在UI线程上记录错误
                        if (_window != null && !_window.Dispatcher.HasShutdownStarted)
                        {
                            try
                            {
                                _window.Dispatcher.Invoke(() =>
                                {
                                    LogError($"资源监控初始化失败: {ex.Message}");
                                });
                            }
                            catch
                            {
                                // 忽略 UI 线程调用失败
                            }
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                // 使用 XLogger 记录错误
                XLogger.Error($"启动资源监控失败: {ex.Message}");
                LogError($"启动资源监控失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动调度器（异步方法）
        /// </summary>
        [RelayCommand]
        public async Task StartSchedulerAsync()
        {
            // 检查调度器是否已在运行，防止重复启动
            if (IsSchedulerRunning || _schedulerService != null)
            {
                LogInfo("调度器已经在运行中，跳过重复启动");
                return;
            }

            // 设置加载状态
            IsLoading = true;
            LoadingMessage = "正在启动调度器，请稍候...";
            LogInfo("启动调度器");

            try
            {
                // 重置所有模拟器的最后活动时间为当前时间，确保空闲计时从0开始
                ResetAllEmulatorsActivityTime();

                // 保存当前所有调度配置
                SaveAllSchedulerConfigurations();

                // 确保之前的调度服务已完全清理
                if (_schedulerService != null)
                {
                    LogInfo("发现现有调度服务实例，先进行清理...");
                    try
                    {
                        await _schedulerService.Stop();
                        _schedulerService = null;
                        LogInfo("已清理旧的调度服务实例");
                    }
                    catch (Exception ex)
                    {
                        LogError($"清理旧调度服务时出错: {ex.Message}");
                    }
                }

                // 创建并启动调度服务
                _schedulerService = new DanDing1.Services.SchedulerService(
                    ScheduledTasks,
                    Emulators,
                    LogInfo,
                    this);  // 传入自身引用作为ViewModel

                // 确保调度服务使用最新的配置
                try
                {
                    string configDir = GetSchedulerConfigDirectory();
                    string configPath = Path.Combine(configDir, "SchedulerConfig.json");

                    // 如果配置文件不存在，则创建一个新的
                    if (!File.Exists(configPath))
                    {
                        LogInfo($"未找到配置文件，将创建新配置");
                        // 创建新的配置文件
                        SaveSettingsToFile();
                    }

                    // 不输出配置文件的完整路径
                    LogInfo($"已加载调度器配置");

                    // 读取并更新配置文件中的值
                    var config = new SchedulerConfig
                    {
                        MaxConcurrentEmulators = MaxConcurrentEmulators,
                        DefaultTaskTimeout = DefaultTaskTimeout,
                        AutoShutdownIdleTime = AutoShutdownIdleTime,
                        EnableTaskRetry = EnableTaskRetry,
                        MaxRetryCount = MaxRetryCount,
                        IdleShutdownEnabled = IdleShutdownEnabled,
                        AutoStartScheduler = AutoStartScheduler
                    };

                    string updatedJson = JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true });
                    File.WriteAllText(configPath, updatedJson);

                    LogInfo($"已更新调度器配置: 最大并发数={MaxConcurrentEmulators}");
                }
                catch (Exception ex)
                {
                    LogError($"更新调度器配置时出错: {ex.Message}");
                    LogInfo("将使用默认配置继续启动调度器");
                }

                // 异步启动调度服务
                await _schedulerService.StartAsync();
                LogInfo($"调度器已启动，最大并发模拟器数量: {MaxConcurrentEmulators}");

                // 更新UI状态
                SchedulerStatus = "运行中";
                IsSchedulerRunning = true;
                CanStartScheduler = false;
                CanStopScheduler = true;

                // 更新模拟器的下一个任务信息
                UpdateEmulatorsNextTask();

                // 确保根据当前选中的模拟器更新过滤后的任务列表
                UpdateFilteredTasks();
                LogInfo($"已根据选中模拟器 \"{CurrentEmulatorName}\" 更新任务显示");
            }
            catch (Exception ex)
            {
                LogError($"启动调度器失败: {ex.Message}");
            }
            finally
            {
                // 无论成功失败都重置加载状态
                IsLoading = false;

                // 强制通知UI更新关键状态属性
                OnPropertyChanged(nameof(IsLoading));
                OnPropertyChanged(nameof(CanStartScheduler));
                OnPropertyChanged(nameof(CanStopScheduler));
                OnPropertyChanged(nameof(IsSchedulerRunning));
                OnPropertyChanged(nameof(SchedulerStatus));
            }
        }

        /// <summary>
        /// 停止资源监控
        /// </summary>
        private void StopResourceMonitoring()
        {
            try
            {
                if (_resourceMonitorTimer != null)
                {
                    _resourceMonitorTimer.Enabled = false;
                    _resourceMonitorTimer.Dispose();
                    _resourceMonitorTimer = null;
                }

                if (_cpuCounter != null)
                {
                    _cpuCounter.Dispose();
                    _cpuCounter = null;
                }

                if (_ramCounter != null)
                {
                    _ramCounter.Dispose();
                    _ramCounter = null;
                }

                LogInfo("资源监控已停止");
            }
            catch (Exception ex)
            {
                LogError($"停止资源监控失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止调度器命令
        /// </summary>
        [RelayCommand]
        private async Task StopScheduler()
        {
            // 检查调度器是否已停止，防止重复停止
            if (!IsSchedulerRunning || _schedulerService == null)
            {
                LogInfo("调度器已经停止，无需再次停止");
                return;
            }

            LogInfo("正在停止调度器，请稍候...");
            IsLoading = true; // 显示加载遮罩
            LoadingMessage = "正在停止调度器，请稍候...";

            try
            {
                // 停止调度服务
                if (_schedulerService != null)
                {
                    LogInfo("正在停止所有定时任务...");
                    await _schedulerService.Stop();

                    // 等待额外的时间，确保所有异步操作都有机会完成
                    LogInfo("等待所有任务完全停止...");
                    await Task.Delay(2000);

                    // 注意：不再关闭模拟器，只停止调度任务
                    LogInfo("调度器已停止，模拟器保持运行状态");
                }

                // 更新UI状态
                SchedulerStatus = "已停止";
                IsSchedulerRunning = false;
                CanStartScheduler = true;
                CanStopScheduler = false;

                // 更新任务状态，但保留模拟器状态
                foreach (var task in ScheduledTasks)
                {
                    if (task.Status == "执行中" || task.Status == "重试中" || task.Status == "重试执行中")
                    {
                        task.Status = "已中断";
                    }
                }

                // 确保根据当前选中的模拟器更新过滤后的任务列表
                UpdateFilteredTasks();
                LogInfo($"已根据选中模拟器 \"{CurrentEmulatorName}\" 更新任务显示");

                LogInfo("调度器已完全停止，所有定时任务已关闭");
            }
            catch (Exception ex)
            {
                LogError($"停止调度器时出错: {ex.Message}");
            }
            finally
            {
                IsLoading = false; // 隐藏加载遮罩
            }
        }

        /// <summary>
        /// 切换任务启用状态命令
        /// </summary>
        [RelayCommand]
        private void ToggleTaskEnabled(object task)
        {
            if (task == null)
                return;

            var scheduledTask = (ScheduledTask)task;

            // 如果用户试图启用任务（当前状态为禁用）
            if (!scheduledTask.Enabled)
            {
                // 检查一次性任务的执行时间是否已经过期
                if (scheduledTask.ScheduleType == "一次性" && DateTime.TryParse(scheduledTask.NextExecutionTime, out DateTime nextTime))
                {
                    // 如果下次执行时间已经过期（小于当前时间）
                    if (nextTime < DateTime.Now)
                    {
                        // 不允许启用，给出提示信息
                        MessageBox.Show(
                            $"一次性任务 \"{scheduledTask.Name}\" 的执行时间 ({scheduledTask.NextExecutionTime}) 已过期，无法启用。\n\n请编辑任务设置新的执行时间。",
                            "无法启用任务",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);

                        LogInfo($"无法启用一次性任务 \"{scheduledTask.Name}\"，执行时间已过期");
                        return;
                    }
                }
            }

            // 如果通过检查或不是一次性任务，则正常切换状态
            scheduledTask.Enabled = !scheduledTask.Enabled;
            LogInfo($"任务 \"{scheduledTask.Name}\" 已{(scheduledTask.Enabled ? "启用" : "禁用")}");

            // 保存任务配置
            SaveTasksConfigToFile();

            // 更新模拟器的下一个任务信息
            UpdateEmulatorsNextTask();
        }

        /// <summary>
        /// 更新活跃模拟器计数
        /// </summary>
        private void UpdateActiveEmulatorsCount()
        {
            try
            {
                int onlineCount = Emulators.Count(e => e.Status == "在线");

                // 更新UI显示
                ActiveEmulators = onlineCount.ToString();

                // 始终输出最新的活跃模拟器状态日志，确保UI和日志一致
                // 移除仅在数量变化时才记录日志的限制
                LogInfo($"当前活跃模拟器: {onlineCount}/{MaxConcurrentEmulators}");

                // 更新上次记录的数量
                _lastActiveEmulatorsCount = onlineCount;

                // 强制刷新UI显示
                if (_window != null)
                {
                    _window.Dispatcher.Invoke(() =>
                    {
                        // 通知属性变更以强制刷新UI
                        OnPropertyChanged(nameof(ActiveEmulators));

                        // 强制模拟器列表刷新
                        var tempCollection = new ObservableCollection<EmulatorItem>(Emulators);
                        Emulators.Clear();
                        foreach (var emulator in tempCollection)
                        {
                            Emulators.Add(emulator);
                        }

                        // 通知托盘图标服务更新提示信息
                        try
                        {
                            var trayIconService = DanDing1.Services.TrayIconService.Instance;
                            trayIconService?.ForceUpdateTooltipText();
                        }
                        catch (Exception ex)
                        {
                            LogError($"强制更新托盘图标状态时发生错误: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                LogError($"更新活跃模拟器计数时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新资源使用率
        /// </summary>
        private void UpdateResourceUsage()
        {
            try
            {
                // 在后台线程上获取性能数据
                float cpuUsage = 0;
                float availableMemory = 0;
                long totalMemory = 0;

                if (_cpuCounter != null && _ramCounter != null)
                {
                    // 获取CPU使用率
                    cpuUsage = _cpuCounter.NextValue();

                    // 获取可用内存
                    availableMemory = _ramCounter.NextValue();

                    // 获取总物理内存
                    ulong totalPhysicalMemory = new Microsoft.VisualBasic.Devices.ComputerInfo().TotalPhysicalMemory;
                    totalMemory = (long)(totalPhysicalMemory / (1024 * 1024));
                }

                // 计算内存使用率和已用内存
                float usedMemory = totalMemory - availableMemory;
                float memoryUsagePercent = totalMemory > 0 ? (usedMemory / totalMemory) * 100 : 0;

                // 检测高资源使用情况
                bool isCpuHigh = cpuUsage > 90;
                bool isMemoryHigh = memoryUsagePercent > 90;

                // 检查窗口引用是否存在
                if (_window == null)
                {
                    // 如果窗口引用不存在，直接更新属性值，不做UI更新
                    CpuUsage = $"{cpuUsage:F1}%";
                    MemoryUsage = $"{usedMemory:F0} MB ({memoryUsagePercent:F1}%)";

                    // 记录高资源使用警告到日志
                    if (isCpuHigh)
                    {
                        XLogger.Warn($"CPU使用率过高: {cpuUsage:F1}%");
                    }

                    if (isMemoryHigh)
                    {
                        XLogger.Warn($"内存使用率过高: {memoryUsagePercent:F1}%");
                    }
                    return;
                }

                // 只在UI线程上更新UI和记录日志
                _window.Dispatcher.Invoke(() =>
                {
                    // 更新UI显示
                    CpuUsage = $"{cpuUsage:F1}%";
                    MemoryUsage = $"{usedMemory:F0} MB ({memoryUsagePercent:F1}%)";

                    // 记录高资源使用警告
                    if (isCpuHigh)
                    {
                        LogWarning($"CPU使用率过高: {cpuUsage:F1}%");
                    }

                    if (isMemoryHigh)
                    {
                        LogWarning($"内存使用率过高: {memoryUsagePercent:F1}%");
                    }
                });
            }
            catch (Exception ex)
            {
                // 使用XLogger替代内部日志，避免依赖_window
                XLogger.Error($"更新资源使用率失败: {ex.Message}");
            }
        }

        #endregion 命令

        #region 私有方法

        /// <summary>
        /// 更新模拟器最后活动时间
        /// </summary>
        public void UpdateEmulatorActivityTime(string emulatorName)
        {
            try
            {
                var emulator = Emulators.FirstOrDefault(e => e.Name == emulatorName);
                if (emulator != null)
                {
                    emulator.LastActivityTime = DateTime.Now;
                    LogInfo($"已更新模拟器 \"{emulatorName}\" 的最后活动时间");
                }
            }
            catch (Exception ex)
            {
                LogError($"更新模拟器活动时间时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加模拟数据（用于UI设计和测试）
        /// </summary>
        private void AddDummyData()
        {
            // 在正式版中不添加测试数据
            // AddDummyTasks();
            // AddDummyEmulators();
        }

        /// <summary>
        /// 添加模拟模拟器数据
        /// </summary>
        private void AddDummyEmulators()
        {
            // 测试数据已移除
        }

        /// <summary>
        /// 添加模拟任务数据
        /// </summary>
        private void AddDummyTasks()
        {
            // 测试数据已移除
        }

        /// <summary>
        /// 自动保存设置
        /// </summary>
        private void AutoSaveSettings()
        {
            // 如果禁用了自动保存，则直接返回
            if (!_enableAutoSave)
                return;

            try
            {
                // 保存设置到文件
                SaveSettingsToFile();

                // 如果调度器正在运行中，立即应用新设置
                if (_schedulerService != null && SchedulerStatus == "运行中")
                {
                    // 读取并更新配置文件中的值
                    var config = new SchedulerConfig
                    {
                        MaxConcurrentEmulators = MaxConcurrentEmulators,
                        DefaultTaskTimeout = DefaultTaskTimeout,
                        AutoShutdownIdleTime = AutoShutdownIdleTime,
                        EnableTaskRetry = EnableTaskRetry,
                        MaxRetryCount = MaxRetryCount,
                        IdleShutdownEnabled = IdleShutdownEnabled,
                        AutoStartScheduler = AutoStartScheduler
                    };

                    string configDir = GetSchedulerConfigDirectory();
                    string configPath = Path.Combine(configDir, "SchedulerConfig.json");
                    string updatedJson = JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true });
                    File.WriteAllText(configPath, updatedJson);

                    // 输出一次日志，包含运行中更新的信息
                    LogInfo($"已自动保存设置更改并更新运行中的调度器配置 (最大并发数={MaxConcurrentEmulators})");
                }
                else
                {
                    // 未运行时只输出一次普通日志
                    LogInfo($"已自动保存设置更改 (最大并发数={MaxConcurrentEmulators})");
                }
            }
            catch (Exception ex)
            {
                LogError($"自动保存设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查并关闭空闲模拟器
        /// </summary>
        private async void CheckIdleEmulators()
        {
            try
            {
                // 首先更新并检测所有模拟器状态，确保活跃模拟器数量显示准确
                await Task.Run(() => UpdateEmulatorStatusesAndCount());

                // 如果空闲关闭功能未启用，只更新状态但不关闭模拟器
                if (!IdleShutdownEnabled)
                    return;

                // 获取当前时间
                DateTime now = DateTime.Now;

                // 遍历所有模拟器
                foreach (var emulator in Emulators)
                {
                    // 只处理在线且启用的模拟器
                    if (emulator.Status == "在线" && emulator.Enabled)
                    {
                        // 检查模拟器是否空闲（无当前任务）
                        if (emulator.CurrentTask == "无" || emulator.CurrentTask == "空闲" || string.IsNullOrEmpty(emulator.CurrentTask))
                        {
                            // 确保LastActivityTime有值
                            if (emulator.LastActivityTime == default(DateTime))
                            {
                                emulator.LastActivityTime = now;
                                LogInfo($"初始化模拟器 \"{emulator.Name}\" 的最后活动时间");
                                continue;
                            }

                            // 计算空闲时间（秒）
                            TimeSpan idleTime = now - emulator.LastActivityTime;
                            int idleSeconds = (int)idleTime.TotalSeconds;

                            // 更新运行时长显示
                            TimeSpan runningTime = now - emulator.LastActivityTime;
                            emulator.RunningDuration = $"{(int)runningTime.TotalHours}:{runningTime.Minutes:D2}:{runningTime.Seconds:D2}";

                            // 如果空闲时间超过设定阈值，关闭模拟器
                            if (idleSeconds >= AutoShutdownIdleTime)
                            {
                                await ShutdownIdleEmulator(emulator, idleSeconds);
                            }
                            else if (idleSeconds % 60 == 0) // 每分钟记录一次空闲时间
                            {
                                LogInfo($"模拟器 \"{emulator.Name}\" 已空闲 {idleSeconds} 秒");
                            }
                        }
                        else
                        {
                            // 如果模拟器有任务在执行，更新其最后活动时间为当前时间
                            emulator.LastActivityTime = now;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"检查空闲模拟器时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新所有模拟器状态并更新活跃计数
        /// </summary>
        private async Task UpdateEmulatorStatusesAndCount()
        {
            try
            {
                if (Emulators.Count == 0)
                {
                    return;
                }

                // 获取MuMu模拟器路径
                var path = XConfig.LoadValueFromFile<string>("MuMuPath");
                if (string.IsNullOrEmpty(path))
                {
                    return;
                }

                // 初始化MuMu实例
                ScriptEngine.MuMu.MuMu mumu = new();
                if (!mumu.Init(path))
                {
                    return;
                }

                // 获取最新的模拟器实例信息
                var mumuInstances = await mumu._GetInstancesAsync();
                if (mumuInstances == null || mumuInstances.Instances.Count == 0)
                {
                    // 将所有模拟器标记为离线
                    foreach (var emulator in Emulators)
                    {
                        string oldStatus = emulator.Status;
                        emulator.Status = "离线";

                        // 如果状态发生变化，更新LastStatusChange
                        if (oldStatus != "离线")
                        {
                            emulator.LastStatusChange = "离线";
                        }
                    }
                    // 更新活跃模拟器计数
                    UpdateActiveEmulatorsCount();
                    return;
                }

                // 更新每个模拟器的状态
                bool statusChanged = false;
                foreach (var emulator in Emulators)
                {
                    bool isOnline = false;

                    // 查找匹配的模拟器实例
                    var matchingInstance = mumuInstances.Instances.Values
                        .FirstOrDefault(i => i.Name == emulator.Name);

                    if (matchingInstance != null)
                    {
                        // 检查模拟器是否正在运行
                        isOnline = MumuUtils.IsSimulatorRunning(
                            matchingInstance,
                            out int mainWndHandle,
                            out int renderWndHandle);
                    }

                    // 保存旧状态用于比较
                    string oldStatus = emulator.Status;

                    // 更新模拟器状态
                    emulator.Status = isOnline ? "在线" : "离线";

                    // 如果状态发生变化，更新LastStatusChange
                    if (oldStatus != emulator.Status)
                    {
                        emulator.LastStatusChange = emulator.Status;
                        statusChanged = true;
                    }
                }

                // 如果任何模拟器状态发生变化，记录日志并强制刷新UI
                if (statusChanged)
                {
                    // 更新活跃模拟器计数
                    UpdateActiveEmulatorsCount();
                }
                else
                {
                    // 即使状态没变，也确保计数正确
                    int onlineCount = Emulators.Count(e => e.Status == "在线");
                    if (int.TryParse(ActiveEmulators, out int currentCount) && currentCount != onlineCount)
                    {
                        UpdateActiveEmulatorsCount();
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"更新模拟器状态时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载模拟器配置
        /// </summary>
        private void LoadEmulatorsConfig()
        {
            try
            {
                string configDir = GetSchedulerConfigDirectory();
                string configPath = Path.Combine(configDir, "SchedulerEmulators.json");
                if (File.Exists(configPath))
                {
                    string json = File.ReadAllText(configPath);
                    var emulators = JsonSerializer.Deserialize<ObservableCollection<EmulatorItem>>(json);
                    if (emulators != null && emulators.Count > 0)
                    {
                        Emulators.Clear();
                        foreach (var emulator in emulators)
                        {
                            // 确保每个模拟器都有Config属性
                            if (emulator.Config == null)
                            {
                                emulator.Config = new SuperMultiGameConfigWindowViewModel();
                                LogInfo($"为模拟器 {emulator.Name} 创建默认游戏配置");
                            }
                            Emulators.Add(emulator);
                        }
                        LogInfo($"已成功加载 {emulators.Count} 个模拟器配置");

                        // 自动选中第一个模拟器（如果存在）
                        if (Emulators.Count > 0)
                        {
                            SelectedEmulator = Emulators[0];
                            CurrentEmulatorName = ((EmulatorItem)SelectedEmulator).Name;
                            LogInfo($"自动选中模拟器: {((EmulatorItem)SelectedEmulator).Name}");
                        }
                        else
                        {
                            CurrentEmulatorName = "全部模拟器";
                        }
                    }
                }
                else
                {
                    LogInfo("未找到模拟器配置文件，使用默认设置");
                    // 如果没有配置文件，保留示例数据
                    if (Emulators.Count == 0)
                    {
                        AddDummyEmulators();
                    }
                }

                // 确保已有的模拟器都有Config属性
                foreach (var emulator in Emulators)
                {
                    if (emulator.Config == null)
                    {
                        emulator.Config = new SuperMultiGameConfigWindowViewModel();
                        LogInfo($"为模拟器 {emulator.Name} 创建默认游戏配置");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"加载模拟器配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        private void LoadSettings()
        {
            // 加载系统配置
            LoadSystemConfig();

            // 加载任务配置
            LoadTasksConfig();

            // 加载模拟器配置
            LoadEmulatorsConfig();
        }

        /// <summary>
        /// 加载系统配置
        /// </summary>
        private void LoadSystemConfig()
        {
            try
            {
                // 临时禁用自动保存，避免加载过程中多次触发
                _enableAutoSave = false;

                string configDir = GetSchedulerConfigDirectory();
                string configPath = Path.Combine(configDir, "SchedulerConfig.json");
                if (File.Exists(configPath))
                {
                    string json = File.ReadAllText(configPath);
                    var config = JsonSerializer.Deserialize<SchedulerConfig>(json);
                    if (config != null)
                    {
                        MaxConcurrentEmulators = config.MaxConcurrentEmulators;
                        DefaultTaskTimeout = config.DefaultTaskTimeout;
                        AutoShutdownIdleTime = config.AutoShutdownIdleTime;
                        EnableTaskRetry = config.EnableTaskRetry;
                        MaxRetryCount = config.MaxRetryCount;
                        IdleShutdownEnabled = config.IdleShutdownEnabled;
                        AutoStartScheduler = config.AutoStartScheduler;
                        LogInfo("已成功加载系统配置");
                    }
                }
                else
                {
                    LogInfo("未找到系统配置文件，使用默认设置");
                }

                // 加载完成后重新启用自动保存
                _enableAutoSave = true;
            }
            catch (Exception ex)
            {
                // 确保在发生异常时也重新启用自动保存
                _enableAutoSave = true;
                LogError($"加载系统配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载任务配置
        /// </summary>
        private void LoadTasksConfig()
        {
            try
            {
                string configDir = GetSchedulerConfigDirectory();
                string configPath = Path.Combine(configDir, "SchedulerTasks.json");
                if (File.Exists(configPath))
                {
                    string json = File.ReadAllText(configPath);
                    var tasks = JsonSerializer.Deserialize<ObservableCollection<ScheduledTask>>(json);
                    if (tasks != null && tasks.Count > 0)
                    {
                        ScheduledTasks.Clear();
                        foreach (var task in tasks)
                        {
                            ScheduledTasks.Add(task);
                        }
                        LogInfo($"已成功加载 {tasks.Count} 个任务配置");
                    }
                }
                else
                {
                    LogInfo("未找到任务配置文件，使用默认设置");
                    // 如果没有配置文件，保留示例数据
                    if (ScheduledTasks.Count == 0)
                    {
                        AddDummyTasks();
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"加载任务配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置所有模拟器的最后活动时间
        /// </summary>
        private void ResetAllEmulatorsActivityTime()
        {
            try
            {
                DateTime now = DateTime.Now;
                foreach (var emulator in Emulators)
                {
                    emulator.LastActivityTime = now;
                    LogInfo($"已重置模拟器 \"{emulator.Name}\" 的空闲计时");
                }
            }
            catch (Exception ex)
            {
                LogError($"重置模拟器活动时间时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 关闭空闲模拟器
        /// </summary>
        private async Task ShutdownIdleEmulator(EmulatorItem emulator, int idleSeconds)
        {
            try
            {
                LogInfo($"模拟器 \"{emulator.Name}\" 已空闲 {idleSeconds} 秒，准备自动关闭");

                // 如果模拟器配置不存在，无法关闭
                if (emulator.SimulatorConfig == null)
                {
                    LogError($"模拟器 \"{emulator.Name}\" 配置信息缺失，无法自动关闭");
                    return;
                }

                // 获取MuMu模拟器路径
                var path = XConfig.LoadValueFromFile<string>("MuMuPath");
                if (string.IsNullOrEmpty(path))
                {
                    LogError($"MuMu模拟器路径未设置，无法关闭模拟器 \"{emulator.Name}\"");
                    return;
                }

                // 初始化MuMu实例
                ScriptEngine.MuMu.MuMu mumu = new();
                if (!mumu.Init(path))
                {
                    LogError($"MuMu模拟器初始化失败，无法关闭模拟器 \"{emulator.Name}\"");
                    return;
                }

                // 获取模拟器实例
                var mumuInstances = await mumu._GetInstancesAsync();
                if (mumuInstances == null || mumuInstances.Instances.Count == 0)
                {
                    LogError($"未找到任何MuMu模拟器实例，无法关闭模拟器 \"{emulator.Name}\"");
                    return;
                }

                // 查找匹配的模拟器实例
                var matchingInstance = mumuInstances.Instances.Values
                    .FirstOrDefault(i => i.Name == emulator.Name);

                if (matchingInstance == null)
                {
                    LogError($"未找到匹配的模拟器实例，无法关闭模拟器 \"{emulator.Name}\"");
                    return;
                }

                // 关闭模拟器
                mumu.CloseByIndex(int.Parse(matchingInstance.Index));

                LogInfo($"已自动关闭空闲模拟器 \"{emulator.Name}\"");

                // 更新模拟器状态为离线
                emulator.Status = "离线";

                // 重置最后活动时间
                emulator.LastActivityTime = DateTime.Now;

                // 更新活跃模拟器数量
                UpdateActiveEmulatorsCount();
            }
            catch (Exception ex)
            {
                LogError($"关闭空闲模拟器 \"{emulator.Name}\" 时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动空闲模拟器监控
        /// </summary>
        private void StartIdleEmulatorMonitoring()
        {
            try
            {
                // 创建空闲模拟器检查定时器
                _idleEmulatorCheckTimer = new System.Timers.Timer(60000); // 每60秒检查一次
                _idleEmulatorCheckTimer.Elapsed += (sender, e) =>
                {
                    CheckIdleEmulators();
                };
                _idleEmulatorCheckTimer.AutoReset = true;
                _idleEmulatorCheckTimer.Enabled = true;

                LogInfo("空闲模拟器监控已启动，检测间隔为1分钟");
            }
            catch (Exception ex)
            {
                LogError($"启动空闲模拟器监控失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止空闲模拟器监控
        /// </summary>
        private void StopIdleEmulatorMonitoring()
        {
            try
            {
                if (_idleEmulatorCheckTimer != null)
                {
                    _idleEmulatorCheckTimer.Enabled = false;
                    _idleEmulatorCheckTimer.Dispose();
                    _idleEmulatorCheckTimer = null;

                    LogInfo("空闲模拟器监控已停止");
                }
            }
            catch (Exception ex)
            {
                LogError($"停止空闲模拟器监控失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新模拟器的下一个任务信息
        /// </summary>
        private void UpdateEmulatorsNextTask()
        {
            // 为每个模拟器找到最早的下一个任务
            foreach (var emulator in Emulators)
            {
                DateTime earliestTime = DateTime.MaxValue;
                string nextTaskInfo = "无待执行任务";

                foreach (var task in ScheduledTasks)
                {
                    // 跳过未启用的任务
                    if (!task.Enabled)
                        continue;

                    // 跳过不属于此模拟器的任务
                    if (task.EmulatorName != emulator.Name)
                        continue;

                    // 解析下次执行时间
                    if (DateTime.TryParse(task.NextExecutionTime, out DateTime nextTime))
                    {
                        if (nextTime < earliestTime)
                        {
                            earliestTime = nextTime;
                            nextTaskInfo = $"{task.Name} ({nextTime.ToString("HH:mm")})";
                        }
                    }
                }

                emulator.NextTask = nextTaskInfo;
            }
        }

        #endregion 私有方法

        /// <summary>
        /// 启动调度器（同步版本）
        /// </summary>
        [RelayCommand]
        private async void StartSchedulerSync()
        {
            await StartSchedulerAsync();
        }

        /// <summary>
        /// 显示全部任务命令
        /// </summary>
        [RelayCommand]
        private void ShowAllTasks()
        {
            LogInfo("显示全部任务");

            // 清除当前模拟器选择
            SelectedEmulator = null;
            CurrentEmulatorName = "全部模拟器";

            // 更新过滤后的任务列表显示所有任务
            UpdateFilteredTasks();

            LogInfo("已显示来自所有模拟器的全部任务");
        }

        /// <summary>
        /// 记录任务开始执行 - 基本版本
        /// </summary>
        /// <param name="recordId">记录ID（可为null表示新记录）</param>
        /// <param name="taskName">任务名称</param>
        /// <param name="emulatorName">模拟器名称</param>
        /// <returns>记录ID，用于后续更新记录</returns>
        public string RecordTaskStarted(string recordId, string taskName, string emulatorName)
        {
            try
            {
                // 如果历史记录服务未初始化，则返回null
                if (_historyService == null)
                {
                    LogInfo("任务历史记录服务未初始化，无法记录任务开始");
                    return null;
                }

                // 创建新的任务历史记录
                var record = new Models.TaskHistoryRecord
                {
                    // 如果提供了recordId，使用它；否则生成新ID
                    Id = string.IsNullOrEmpty(recordId) ? Guid.NewGuid().ToString() : recordId,
                    TaskId = "N/A", // 没有直接的任务对象，使用占位符
                    TaskName = taskName,
                    EmulatorName = emulatorName,
                    StartTime = DateTime.Now,
                    Status = "执行中",
                    ScheduleType = "未知", // 没有直接的任务对象，使用占位符
                    RetryCount = 0, // 初始无重试
                    TaskType = "未知类型" // 没有直接的任务参数，使用默认值
                };

                // 添加记录到历史服务
                string newRecordId = _historyService.AddRecord(record);
                LogInfo($"已记录任务开始执行: {taskName}, 记录ID: {newRecordId}");
                return newRecordId;
            }
            catch (Exception ex)
            {
                LogError($"记录任务开始执行时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 记录任务开始执行 - 带有调度类型信息
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="taskName">任务名称</param>
        /// <param name="emulatorName">模拟器名称</param>
        /// <param name="scheduleType">调度类型</param>
        /// <returns>记录ID，用于后续更新记录</returns>
        public string RecordTaskStarted(string taskId, string taskName, string emulatorName, string scheduleType)
        {
            try
            {
                // 如果历史记录服务未初始化，则返回null
                if (_historyService == null)
                {
                    LogInfo("任务历史记录服务未初始化，无法记录任务开始");
                    return null;
                }

                // 创建新的任务历史记录
                var record = new Models.TaskHistoryRecord
                {
                    TaskId = taskId,
                    TaskName = taskName,
                    EmulatorName = emulatorName,
                    StartTime = DateTime.Now,
                    Status = "执行中",
                    ScheduleType = scheduleType,
                    RetryCount = 0,
                    TaskType = "未知类型"
                };

                // 添加记录到历史服务
                string newRecordId = _historyService.AddRecord(record);
                LogInfo($"已记录任务开始执行: {taskName}, 记录ID: {newRecordId}");
                return newRecordId;
            }
            catch (Exception ex)
            {
                LogError($"记录任务开始执行时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 记录任务开始执行 - 使用任务对象
        /// </summary>
        /// <param name="task">任务对象</param>
        /// <param name="emulator">模拟器对象</param>
        /// <returns>记录ID，用于后续更新记录</returns>
        public string RecordTaskStarted(ScheduledTask task, EmulatorItem emulator)
        {
            try
            {
                // 如果历史记录服务未初始化，则返回null
                if (_historyService == null)
                {
                    LogInfo("任务历史记录服务未初始化，无法记录任务开始");
                    return null;
                }

                // 创建新的任务历史记录
                var record = new Models.TaskHistoryRecord
                {
                    TaskId = task.Id,
                    TaskName = task.Name,
                    EmulatorName = emulator.Name,
                    StartTime = DateTime.Now,
                    Status = "执行中",
                    ScheduleType = task.ScheduleType,
                    RetryCount = 0 // 初始无重试
                };

                // 尝试解析任务参数以获取任务类型
                try
                {
                    if (!string.IsNullOrEmpty(task.TaskParameters))
                    {
                        // 尝试从JSON中提取任务类型信息
                        var taskParams = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(task.TaskParameters);
                        if (taskParams != null && taskParams.ContainsKey("TaskType"))
                        {
                            record.TaskType = taskParams["TaskType"].ToString();
                        }
                        else
                        {
                            record.TaskType = "未知类型";
                        }
                    }
                    else
                    {
                        record.TaskType = "未知类型";
                    }
                }
                catch
                {
                    record.TaskType = "未知类型";
                }

                // 添加记录到历史服务
                string recordId = _historyService.AddRecord(record);
                LogInfo($"已记录任务开始执行: {task.Name}, 记录ID: {recordId}");
                return recordId;
            }
            catch (Exception ex)
            {
                LogError($"记录任务开始执行时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 记录任务成功完成
        /// </summary>
        /// <param name="recordId">任务开始时返回的记录ID</param>
        /// <param name="success">是否成功</param>
        /// <param name="message">完成消息</param>
        /// <param name="retryCount">重试次数</param>
        /// <returns>更新是否成功</returns>
        public bool RecordTaskCompleted(string recordId, bool success, string message = null, int retryCount = 0)
        {
            try
            {
                // 如果历史记录服务未初始化，则返回false
                if (_historyService == null)
                {
                    LogInfo("任务历史记录服务未初始化，无法记录任务完成");
                    return false;
                }

                // 确定状态
                string status = success ? "成功" : "失败";

                // 更新现有记录
                bool updated = _historyService.UpdateRecord(recordId, status, message);
                LogInfo($"已更新任务记录状态为{status}，记录ID: {recordId}");
                return updated;
            }
            catch (Exception ex)
            {
                LogError($"记录任务完成时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 记录任务取消
        /// </summary>
        /// <param name="recordId">任务开始时返回的记录ID</param>
        /// <param name="success">是否成功（用于兼容现有调用）</param>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="retryCount">重试次数</param>
        /// <returns>更新是否成功</returns>
        public bool RecordTaskCancelled(string recordId, bool success, string errorMessage = null, int retryCount = 0)
        {
            try
            {
                // 如果历史记录服务未初始化，则返回false
                if (_historyService == null)
                {
                    LogInfo("任务历史记录服务未初始化，无法记录任务取消");
                    return false;
                }

                // 中断状态
                string status = "中断";

                // 更新现有记录
                bool updated = _historyService.UpdateRecord(recordId, status, errorMessage);
                LogInfo($"已更新任务记录状态为中断，记录ID: {recordId}");
                return updated;
            }
            catch (Exception ex)
            {
                LogError($"记录任务取消时出错: {ex.Message}");
                return false;
            }
        }
    }
}