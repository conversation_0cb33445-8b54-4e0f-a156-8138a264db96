﻿#pragma checksum "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D3AFAACAA73FB70F8A16CDB7CC2134B511BA38C4"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using DanDing1.Models;
using DanDing1.Views.Pages;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using Wpf.Ui;
using Wpf.Ui.Controls;
using Wpf.Ui.Converters;
using Wpf.Ui.Markup;


namespace DanDing1.Views.Pages {
    
    
    /// <summary>
    /// UserConfigPage
    /// </summary>
    public partial class UserConfigPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DanDing1;component/views/pages/userconfigpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 74 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowQrCodeImage_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 85 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestNotification_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 111 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowNtfyHelp_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 122 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestNotification_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 148 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowPushplusHelp_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 159 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestNotification_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 185 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowMiaotixingHelp_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 196 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestNotification_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 477 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            ((System.Windows.Controls.TextBox)(target)).LostFocus += new System.Windows.RoutedEventHandler(this.TextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 529 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            ((System.Windows.Controls.TextBox)(target)).LostFocus += new System.Windows.RoutedEventHandler(this.DecimalTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 554 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            ((System.Windows.Controls.TextBox)(target)).LostFocus += new System.Windows.RoutedEventHandler(this.DecimalTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 626 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowDamoHelpImage_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 646 "..\..\..\..\..\..\Views\Pages\UserConfigPage.xaml"
            ((Wpf.Ui.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowResultHelpImage_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

