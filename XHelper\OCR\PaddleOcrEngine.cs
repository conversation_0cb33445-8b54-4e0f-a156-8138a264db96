using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;
using XHelper.Models;
using OpenCvSharp;
using System.Windows;

namespace XHelper.OCR
{
    /// <summary>
    /// PaddleOCR引擎实现类，用于高级文本识别
    /// </summary>
    internal class PaddleOcrEngine : IOcrEngine
    {
        private readonly OcrConfiguration _config;
        private readonly PaddleOcrImageProcessor _imageProcessor;
        private bool _initialized = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">OCR配置</param>
        /// <param name="imageProcessor">图像处理器</param>
        public PaddleOcrEngine(OcrConfiguration config, IImageProcessor imageProcessor)
        {
            _config = config;
            _imageProcessor = imageProcessor as PaddleOcrImageProcessor ?? OcrManager.Instance.PaddleImageProcessor;
        }

        /// <summary>
        /// 初始化OCR引擎
        /// </summary>
        /// <returns>是否初始化成功</returns>
        public bool Initialize()
        {
            // 已初始化则直接返回
            if (_initialized)
                return true;

            try
            {
                // 检查PaddleOCR文件是否存在
                if (!_config.CheckModelFilesExist("paddle"))
                {
                    return false;
                }

                _initialized = true;
                return true;
            }
            catch (Exception ex)
            {
                XLogger.Error($"PaddleOCR引擎初始化异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从图像路径识别文本
        /// </summary>
        /// <param name="imagePath">图像路径</param>
        /// <param name="callback">可选的回调函数，用于接收文本块信息</param>
        /// <returns>识别结果文本</returns>
        public string RecognizeText(string imagePath, Action<List<XOcr_TextBlock>>? callback = null)
        {
            try
            {
                if (!Initialize())
                {
                    XLogger.Warn("PaddleOCR引擎初始化失败");
                    return "-1";
                }

                if (string.IsNullOrEmpty(imagePath))
                {
                    XLogger.Warn("输入图像路径为空");
                    return "-1";
                }

                // 移除路径中可能的引号
                imagePath = imagePath.Trim('"', '\'');

                if (!File.Exists(imagePath))
                {
                    XLogger.Warn($"输入图像文件不存在: {imagePath}");
                    return "-1";
                }

                //XLogger.Debug($"开始使用PaddleOCR识别图片: {imagePath}");

                // 使用专属图像处理器优化图像，添加25像素的黑色边框
                byte[] optimizedImageBytes = _imageProcessor.OptimizeImageForPaddleOcr(imagePath);
                if (optimizedImageBytes == null || optimizedImageBytes.Length == 0)
                {
                    XLogger.Error("图像优化失败");
                    return "-1";
                }

                // 将优化后的图像保存为临时文件
                string tempOptimizedPath = Path.Combine(Path.GetTempPath(), $"paddle_ocr_optimized_{Guid.NewGuid()}.jpg");
                try
                {
                    File.WriteAllBytes(tempOptimizedPath, optimizedImageBytes);

                    string res = "";
                    res = ExecutePaddleOcr(tempOptimizedPath);

                    if (string.IsNullOrEmpty(res) || res == "-1")
                    {
                        //XLogger.Warn("PaddleOCR返回结果为空或错误");
                        return "-1";
                    }

                    // 直接使用返回的JSON创建PaddleOcr_Model
                    Models.PaddleOcr_Model paddleModel;
                    try
                    {
                        paddleModel = new Models.PaddleOcr_Model(res);
                    }
                    catch (ArgumentException ex)
                    {
                        XLogger.Error($"JSON解析失败: {ex.Message}");
                        return "-1";
                    }

                    // 如果需要兼容原有的回调方式
                    if (callback != null && paddleModel.Results != null)
                    {
                        try
                        {
                            // 创建原始TextBlock列表
                            var blocks_list = paddleModel.Results.Select(r =>
                                new XOcr_TextBlock(r.Text ?? string.Empty,
                                    new System.Drawing.Rectangle(r.X, r.Y - r.Height, r.Width, r.Height))
                            ).ToList();
                            blocks_list.Reverse();

                            // 使用图像处理器矫正坐标
                            // 注意：PaddleOCR本身没有额外的内部Padding，所以只需使用图像处理器的边框值
                            var correctedBlocks = OcrCoordinateCorrector.CorrectCoordinates(blocks_list, _imageProcessor);
                            callback(blocks_list);
                        }
                        catch (Exception ex)
                        {
                            XLogger.Error($"执行回调时出错: {ex.Message}");
                        }
                    }

                    return paddleModel.GetAllText();
                }
                finally
                {
                    // 删除临时文件
                    try
                    {
                        //if (File.Exists(tempOptimizedPath))
                        //{
                        //    File.Delete(tempOptimizedPath);
                        //}
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"删除优化图像临时文件失败: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"PaddleOCR识别异常: {ex.Message}");
                return "-1";
            }
        }

        /// <summary>
        /// 从图像字节数组识别文本
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <param name="callback">可选的回调函数，用于接收文本块信息</param>
        /// <returns>识别结果文本</returns>
        public string RecognizeText(byte[] imageData, Action<List<XOcr_TextBlock>>? callback = null)
        {
            try
            {
                if (!Initialize())
                {
                    XLogger.Warn("PaddleOCR引擎初始化失败");
                    return "-1";
                }

                if (imageData == null || imageData.Length == 0)
                {
                    XLogger.Warn("输入图像数据为空");
                    return "-1";
                }

                // 使用专属图像处理器优化图像，添加25像素的黑色边框
                byte[] optimizedImageBytes = _imageProcessor.OptimizeImageForPaddleOcr(imageData);
                if (optimizedImageBytes == null || optimizedImageBytes.Length == 0)
                {
                    XLogger.Error("图像优化失败");
                    return "-1";
                }

                // 将优化后的图像数据保存为临时文件
                string tempImagePath = Path.Combine(Path.GetTempPath(), $"paddle_ocr_temp_{Guid.NewGuid()}.png");
                try
                {
                    File.WriteAllBytes(tempImagePath, optimizedImageBytes);
                    string result = RecognizeText(tempImagePath, callback);
                    return result;
                }
                finally
                {
                    // 删除临时文件
                    try
                    {
                        if (File.Exists(tempImagePath))
                        {
                            File.Delete(tempImagePath);
                        }
                    }
                    catch (Exception ex)
                    {
                        XLogger.Error($"删除临时文件失败: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                XLogger.Error($"PaddleOCR识别异常: {ex.Message}");
                return "-1";
            }
        }

        /// <summary>
        /// 执行PaddleOCR命令行程序
        /// </summary>
        /// <param name="imagePath">图像路径</param>
        /// <returns>识别结果JSON</returns>
        private string ExecutePaddleOcr(string imagePath)
        {
            string exePath = _config.PaddleExePath;
            if (!File.Exists(exePath))
            {
                XLogger.Error($"PaddleOCR执行文件不存在: {exePath}");
                return "-1";
            }

            try
            {
                // 处理图片路径，确保路径中的空格能被正确处理
                string cmdArgument = $"\"{imagePath}\"";

                //XLogger.Debug($"PaddleOCR处理图片: {cmdArgument}");

                ProcessStartInfo processStartInfo = new ProcessStartInfo
                {
                    FileName = exePath,
                    Arguments = cmdArgument,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    StandardOutputEncoding = Encoding.UTF8,
                    StandardErrorEncoding = Encoding.UTF8,
                    UseShellExecute = false
                };

                using Process? process = Process.Start(processStartInfo);
                if (process == null)
                {
                    XLogger.Error("无法启动PaddleOCR进程");
                    return "-1";
                }

                string? output = process.StandardOutput.ReadToEnd();
                string? error = process.StandardError.ReadToEnd();

                // 等待进程完成，设置超时
                bool exited = process.WaitForExit(_config.ProcessTimeoutMs);
                if (!exited)
                {
                    try
                    {
                        process.Kill();
                        XLogger.Error("PaddleOCR进程执行超时，已强制终止");
                    }
                    catch { }
                    return "-1";
                }

                // 检查是否有错误输出
                if (!string.IsNullOrEmpty(error))
                {
                    XLogger.Error($"PaddleOCR进程错误输出: {error}");
                    return "-1";
                }

                // 检查进程退出代码
                if (process.ExitCode != 0)
                {
                    //XLogger.Error($"PaddleOCR进程退出代码: {process.ExitCode}");
                    //if (process.ExitCode == -1073741819)
                    //{
                    //    XLogger.Error("PaddleOCR进程发生访问冲突 (0xC0000005)，可能是系统兼容性问题");
                    //}
                    return "-1";
                }

                return output ?? "";
            }
            catch (Exception ex)
            {
                XLogger.Error($"执行PaddleOCR命令时异常: {ex.Message}");
                return "-1";
            }
        }
    }
}