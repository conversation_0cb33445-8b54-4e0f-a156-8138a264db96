﻿using Compunet.YoloSharp.Data;
using DamoControlKit.Control;
using DamoControlKit.Model;
using ScriptEngine.Factorys;
using System.Runtime.InteropServices;
using XHelper;

namespace ScriptEngine.Tasks.Base
{
    /// <summary>
    /// 场景助手类
    /// </summary>
    internal class Scene
    {
        internal readonly Log log;
        private bool _hasHandledBuffInCurrentTask = false;  // 添加标志位

        private List<string> NameLists = new() { "町中", "庭院", "探索", "御魂", "寮突", "突破", "御灵", "斗技", "觉醒", "业原火", "契灵", "六道", "阴阳寮", "寄养" };

        private Dictionary<string, string> YoloScene = new()
        {
            {"douji","斗技" },
            {"jiejietupo","突破" },
            {"juexing","觉醒" },
            {"rilun","日轮" },
            {"tansuo","探索" },
            {"tansuotask","探索任务" },
            {"tingyuan","庭院" },
            {"tingzhong","町中" },
            {"yeyuanhuo","业原火" },
            {"yongsheng","永生" },
            {"yuhun","御魂" },
            {"jiyang","寄养" },
            {"","未知[YOLO]" },
            {"未知","未知[YOLO]" },
        };

        public Scene(dmsoft dm, MemPics mp, string courtyardSkin, Action<int, bool> sleep, DDBuilder Db, CancellationTokenSource ct)
        {
            Dm = dm;
            Mp = mp;
            Mp.SetAllXsoft(Dm);
            CourtyardSkin = courtyardSkin;
            Sleep = sleep;
            this.Db = Db;
            Ct = ct;
            this.log = Db.Log;
            TO = new(this);
        }

        /// <summary>
        /// 庭院皮肤
        /// </summary>
        public string CourtyardSkin { get; }

        /// <summary>
        /// 当前场景
        /// </summary>
        public string NowScene => GetScene();

        /// <summary>
        /// 去场景
        /// </summary>
        public To TO { get; }

        internal CancellationTokenSource Ct { get; }

        /// <summary>
        /// 延迟函数
        /// </summary>
        internal Action<int, bool> Sleep { get; }

        private DDBuilder Db { get; }
        private dmsoft Dm { get; }
        private MemPics Mp { get; }

        /// <summary>
        /// 当前场景
        /// </summary>
        /// <returns></returns>
        public string GetScene(string sceneName = "")
        {
            if (Mp.Filter("开启卷轴").FindAllAndClick()) Sleep.Invoke(1000, false);

            string name = "";
            if (!string.IsNullOrEmpty(sceneName))
                if (NowIsScene(sceneName)) return sceneName;
            if (Mp.FindAllE(out var pics))
            {
                foreach (var _pic in pics)
                {
                    foreach (var scName in NameLists)
                    {
                        name = _pic.Contains(scName) ? scName : "";
                        if (name is not "")
                        {
                            // 记录场景识别结果
                            XLogger.Debug($"场景识别成功：{name}");
                            return name;
                        }
                    }
                }
            }

            if (name == "")
            {
                //Ai 查找
                Dm.GetScreenDataBmp(0, 0, 2000, 2000, out int data, out int size);
                byte[] genePic2 = new byte[size];
                nint ptr = new(data);
                for (int i = 0; i < size; i++)
                    genePic2[i] = Marshal.ReadByte(ptr + 1 * i);
                var obj = (YoloResult<Classification>?)XYoloV8.yoloV8(YoloModels.Classify, genePic2);
                name = obj?.YoloClass_GetTopName(0.99) ?? "";
                if (name is not "")
                {
                    XLogger.Debug("YoloClassfly：" + name);
                    name = YoloScene[name];
                    // 记录AI场景识别结果
                    XLogger.Debug($"AI场景识别成功：{name}");
                }
                else
                {
                    // 场景识别失败，添加额外检查
                    XLogger.Debug("场景识别失败，尝试额外检查");
                    // 可以添加额外的检查逻辑，比如特定UI元素的检测
                }
            }
            return name;
        }

        /// <summary>
        /// 当前场景是不是指定场景
        /// </summary>
        /// <param name="sceneName"></param>
        /// <returns></returns>
        public bool NowIsScene(string sceneName)
        {
            var pic = Mp.Filter(sceneName).FindAllEa();
            if (pic is not null)
                return true;
            return false;
        }

        /// <summary>
        /// 前往场景
        /// </summary>
        public class To
        {
            /// <summary>
            /// 多庭院适配活动场景
            /// </summary>
            private Dictionary<string, Point> GoTanHuoDongPosition = new Dictionary<string, Point>()
            {
                {"默认",new(265,172) },
                {"枫色秋庭",new(0,245) },
                {"织梦莲庭",new(172,305) },
                {"笔墨山河",new(417,243) },
                {"缘结之庭",new(22,275) },

                {"琼月华宫",new(256,299) },
                {"鏖刀禁府",new(215,197) },
                {"冶武兵道",new(215,197) },
            };

            /// <summary>
            /// 多庭院适配
            /// </summary>
            private Dictionary<string, Position> GoTanSuoPosition = new Dictionary<string, Position>()
            {
                {"默认",new(479,123,525,184) },
                {"枫色秋庭",new(247,116,294,176) },
                {"织梦莲庭",new(358,159,390,198) },
                {"笔墨山河",new(186,242,206,275) },
                {"缘结之庭",new(448,123,492,162) },

                {"琼月华宫",new(194,172,218,198) },
                {"鏖刀禁府",new(357,118,375,146) },
                {"冶武兵道",new(357,118,375,146) },
            };

            /// <summary>
            /// 多庭院适配
            /// </summary>
            private Dictionary<string, Position> GoTingZhongPosition = new Dictionary<string, Position>()
            {
                {"默认",new(570,257,605,300) },
                {"枫色秋庭",new(613,291,642,324) },
                {"织梦莲庭",new(417,354,433,393) },
                {"笔墨山河",new(620,246,648,280) },
                {"缘结之庭",new(604,343,630,381) },

                {"琼月华宫",new(537,313,590,350) },
                {"鏖刀禁府",new(603,209,672,249) },
                {"冶武兵道",new(603,209,672,249) },
            };

            public To(Scene scene)
            {
                Scene = scene;
                Operational = new(scene.Dm);
                Sleep = scene.Sleep;
                Db = scene.Db;
            }

            private DDBuilder Db { get; }
            private dmsoft Dm => Scene.Dm;
            private Log log => Scene.log;
            private MemPics Mp => Scene.Mp;
            private Operational Operational { get; }
            private Scene Scene { get; }
            private Action<int, bool> Sleep { get; }

            /// <summary>
            /// 去斗技
            /// </summary>
            public bool DouJi()
            {
                log.Debug($"[DouJi] 当前场景:{Scene.NowScene}");
                if (Scene.NowScene.Contains("斗技")) return true;
                TingZhong();
                Sleep(1000, false);
                if (Scene.GetScene("町中") == "町中")
                {
                    log.Info("当前：町中，进入斗技场景.");
                    Operational.Click(new Position("762,141,793,214"));
                    Sleep(2500, false);
                }
                return true;
            }

            /// <summary>
            /// 去觉醒场景
            /// </summary>
            public void JueXing(string _Class)
            {
                log.Debug($"[YuHun] 当前场景:{Scene.NowScene}");
                if (Scene.NowScene.Contains("觉醒")) return;
                log.Info("点击进入探索-觉醒.");
                Operational.Click(new Position("63,639,112,691"));
                Sleep(1999, false);
                Dictionary<string, string> poss = new()
                {
                    {"火","121,193,266,476" },
                    {"风","436,217,571,527" },
                    {"水","719,245,835,525" },
                    {"雷","1012,268,1138,501" },
                };
                log.Info($"点击进入觉醒-{_Class}.");
                Operational.Click(new Position(poss[_Class]));
                Sleep(1500, false);
                if (Scene.NowScene.Contains("御魂")) return;
                JueXing(_Class);
            }

            /// <summary>
            /// 去寮突 有体服适配
            /// </summary>
            public void LiaoTu(int retryCount = 0)
            {
                log.Debug($"[LiaoTu] 当前场景:{Scene.NowScene}");
                if (Scene.NowScene.Contains("寮突")) return;

                // 添加重试次数限制，防止无限循环
                if (retryCount >= 3)
                {
                    log.Warn("尝试进入寮突场景失败,重试次数已达上限，尝试重置场景...");

                    // 尝试重置场景
                    if (ResetScene(out string resetSceneName))
                    {
                        log.Info($"场景重置成功，当前场景：{resetSceneName}，继续尝试进入寮突场景");
                        Sleep(1500, false);

                        // 重置计数器，从头开始尝试
                        LiaoTu(0);
                        return;
                    }
                    else
                    {
                        log.Error("场景重置失败，无法进入寮突场景");
                        return;
                    }
                }

                TanSuo();
                log.Info("点击进入探索-突破.");
                if (Db.GameSetting.IsTifu)
                    Operational.Click(new Position(361, 644, 404, 689));
                else
                    Operational.Click(new Position(253, 634, 306, 686));

                // 增加延时，给系统更多时间切换场景
                Sleep(3000, false);

                log.Info("点击进入突破-寮突.");
                Operational.Click(new Position("1212,374,1243,448"));
                Sleep(3000, false);

                if (Scene.NowScene.Contains("寮突")) return;

                // 重试进入寮突场景
                LiaoTu(retryCount + 1);
            }

            /// <summary>
            /// 去六道场景 有体服适配
            /// </summary>
            /// <returns></returns>
            public bool LiuDao(int retryCount = 0)
            {
                log.Debug($"[LiuDao] 当前场景:{Scene.NowScene}");
                if (Scene.NowScene.Contains("六道")) return true;
                if (retryCount >= 3)
                {
                    log.Error("尝试进入六道场景失败,重试次数已达上限");
                    return false;
                }

                if (Scene.NowScene.Contains("庭院"))
                    if (!TanSuo()) return false;//去探索
                if (Scene.NowScene.Contains("探索"))
                {
                    log.Info("点击进入六道.");
                    if (Db.GameSetting.IsTifu)
                        Operational.Click(1039, 644, 1079, 681);
                    else
                        Operational.Click(946, 640, 987, 686);

                    Sleep(3000, false);
                    log.Info("点击月之海.");
                    Operational.Click(347, 167, 374, 271);
                    Sleep(3000, false);
                }
                //判断当前场景是否为六道
                if (Scene.NowScene.Contains("六道")) return true;
                return LiuDao(retryCount + 1);
            }

            /// <summary>
            /// 当游戏界面未知时，尝试恢复到任意可识别的场景
            /// </summary>
            /// <returns></returns>
            public bool ResetScene(out string sceneName)
            {
                log.Warn("当前游戏场景位置未知，尝试恢复到已知场景..");
                int x, y;
                x = y = 0;
                while (Mp.Filter("场景重置").FindAllAndClick() || Mp.Filter("Sub.叉").FindAll(out x, out y))
                {
                    if (x > 0 && y > 0) Operational.Click(x, y);
                    Sleep(2500, false);
                    if (Scene.NowScene != "" && Scene.NowScene != "未知[YOLO]")
                    {
                        var s = Scene.NowScene;
                        log.Info_Green("场景重置成功，当前场景：" + s);
                        sceneName = s;
                        return true;
                    }
                }
                sceneName = "";
                return false;
            }

            /// <summary>
            /// 去探索场景
            /// </summary>
            public bool TanSuo(bool DontAutoBuff = false, int retryCount = 0)
            {
                log.Debug($"[TanSuo] 当前场景:{Scene.NowScene}");
                if (Scene.NowScene.Contains("探索"))
                {
                    if (!DontAutoBuff)
                    {
                        HandleAutoBuff();
                    }
                    return true;
                }

                // 添加重试次数限制，防止无限循环
                if (retryCount >= 5)
                {
                    log.Warn("尝试进入探索场景失败，重试次数已达上限，尝试重置场景...");

                    // 尝试重置场景
                    if (ResetScene(out string resetSceneName))
                    {
                        log.Info($"场景重置成功，当前场景：{resetSceneName}，继续尝试进入探索场景");
                        Sleep(1500, false);

                        // 重置计数器，从头开始尝试
                        return TanSuo(DontAutoBuff, 0);
                    }
                    else
                    {
                        log.Error("场景重置失败，无法进入探索场景");
                        return false;
                    }
                }

                if (Scene.NowScene.Contains("庭院"))
                {
                    QiangZhiReset();
                    log.Info("滑动一下屏幕.");
                    Operational.Slide_Pos(new Position(1169, 86, 1196, 105), new(159, 134, 186, 155));
                    Sleep(1000, false);
                    log.Info("点击进入探索.");
                    try
                    {
                        Position pos = GoTanSuoPosition[Scene.CourtyardSkin];
                        Operational.Click(pos);
                    }
                    catch (KeyNotFoundException)
                    {
                        log.Error($"无法找到皮肤[{Scene.CourtyardSkin}]的坐标配置，使用默认值");
                        Position pos = GoTanSuoPosition["默认"];
                        Operational.Click(pos);
                    }
                    Sleep(3500, false); // 增加延时，给系统更多时间切换场景

                    // 检查是否成功进入探索场景
                    if (Scene.NowScene.Contains("探索"))
                    {
                        if (!DontAutoBuff)
                        {
                            HandleAutoBuff();
                        }
                        return true;
                    }

                    // 尝试点击可能出现的弹窗
                    int x = 0, y = 0;
                    if (Mp.Filter("Sub.叉").FindAll(out x, out y) || Mp.Filter("场景重置").FindAllAndClick())
                    {
                        if (x > 0 && y > 0) Operational.Click(x, y);
                        Sleep(1500, false);
                    }

                    // 递归调用，增加重试计数
                    return TanSuo(DontAutoBuff, retryCount + 1);
                }

                // 如果当前不在庭院，先回到庭院
                log.Info("当前不在庭院，尝试返回庭院...");
                TingYuan();
                return TanSuo(DontAutoBuff, retryCount);
            }

            /// <summary>
            /// 去庭院场景
            /// </summary>
            public bool TingYuan()
            {
                log.Debug($"[TingYuan] 当前场景:{Scene.NowScene}");
                if (Scene.NowScene.Contains("庭院"))
                {
                    QiangZhiReset();
                    return true;
                }
                if (Scene.NowScene.Contains("探索"))
                {
                ReDo:
                    log.Info("当前：探索，退出到庭院.");
                    Operational.Click(new Position(36, 38, 70, 77));
                    Sleep(3000, false);
                    if (Scene.NowScene.Contains("探索")) goto ReDo;
                    if (Scene.NowScene.Contains("庭院")) return true;
                    TingYuan();
                }
                return false;
            }

            /// <summary>
            /// 去町中
            /// </summary>
            public void TingZhong()
            {
                log.Debug($"[TingZhong] 当前场景:{Scene.NowScene}");
                if (Scene.GetScene("町中") == "町中") return;
                if (Scene.NowScene.Contains("探索")) TingYuan();

                if (Scene.NowScene.Contains("庭院"))
                {
                    QiangZhiReset();
                    log.Info("滑动一下屏幕.");
                    Operational.Slide_Pos(new Position("1169,86,1196,105"), new("159,134,186,155"));
                    Sleep(1000, false);
                    log.Info("点击进入町中.");
                    Operational.Click(GoTingZhongPosition[Scene.CourtyardSkin]);
                    Sleep(2500, false);
                    if (Scene.GetScene("町中") == "町中") return;
                    TingZhong();
                }
            }

            /// <summary>
            /// 去突破 有体服适配
            /// </summary>
            public void TuPo(int retryCount = 0)
            {
                log.Debug($"[TuPo] 当前场景:{Scene.NowScene}");
                if (Scene.NowScene.Contains("突破")) return;

                // 添加重试次数限制，防止无限循环
                if (retryCount >= 3)
                {
                    log.Warn("尝试进入突破场景失败,重试次数已达上限，尝试重置场景...");

                    // 尝试重置场景
                    if (ResetScene(out string resetSceneName))
                    {
                        log.Info($"场景重置成功，当前场景：{resetSceneName}，继续尝试进入突破场景");
                        Sleep(1500, false);

                        // 重置计数器，从头开始尝试
                        TuPo(0);
                        return;
                    }
                    else
                    {
                        log.Error("场景重置失败，无法进入突破场景");
                        return;
                    }
                }

                TanSuo();
                log.Info("点击进入探索-突破.");
                if (Db.GameSetting.IsTifu)
                    Operational.Click(new Position(361, 644, 404, 689));
                else
                    Operational.Click(new Position(253, 634, 306, 686));

                // 增加延时，给系统更多时间切换场景
                Sleep(3000, false);

                // 再次检查是否成功进入突破场景
                if (Scene.NowScene.Contains("突破")) return;

                // 重试进入突破场景
                TuPo(retryCount + 1);
            }

            /// <summary>
            /// 去御魂场景
            /// </summary>
            public void YuHun(int retryCount = 0)
            {
                log.Debug($"[YuHun] 当前场景:{Scene.NowScene}");
                if (Scene.NowScene.Contains("御魂")) return;

                // 添加重试次数限制，防止无限循环
                if (retryCount >= 3)
                {
                    log.Warn("尝试进入御魂场景失败,重试次数已达上限，尝试重置场景...");

                    // 尝试重置场景
                    if (ResetScene(out string resetSceneName))
                    {
                        log.Info($"场景重置成功，当前场景：{resetSceneName}，继续尝试进入御魂场景");
                        Sleep(1500, false);

                        // 重置计数器，从头开始尝试
                        YuHun(0);
                        return;
                    }
                    else
                    {
                        log.Error("场景重置失败，无法进入御魂场景");
                        return;
                    }
                }

                TanSuo();
                log.Info("点击进入探索-御魂.");
                Operational.Click(166, 642, 206, 690);
                Sleep(3000, false);
                log.Info("点击进入御魂-八岐大蛇.");
                Operational.Click(111, 167, 265, 415);
                Sleep(3000, false);
                if (Scene.NowScene.Contains("御魂")) return;

                // 重试进入御魂场景
                YuHun(retryCount + 1);
            }

            /// <summary>
            /// 去御灵 有体服适配
            /// </summary>
            public void YuLing(string _class, int retryCount = 0)
            {
                log.Debug($"[YuLing] 当前场景:{Scene.NowScene}");
                if (Scene.NowScene.Contains("御灵")) return;

                // 添加重试次数限制，防止无限循环
                if (retryCount >= 3)
                {
                    log.Warn("尝试进入御灵场景失败,重试次数已达上限，尝试重置场景...");

                    // 尝试重置场景
                    if (ResetScene(out string resetSceneName))
                    {
                        log.Info($"场景重置成功，当前场景：{resetSceneName}，继续尝试进入御灵场景");
                        Sleep(1500, false);

                        // 重置计数器，从头开始尝试
                        YuLing(_class, 0);
                        return;
                    }
                    else
                    {
                        log.Error("场景重置失败，无法进入御灵场景");
                        return;
                    }
                }

                Dictionary<string, string> keys = new(){
                {"暗神龙","138,175,267,496" },
                {"暗白藏主","428,197,564,512" },
                {"暗黑豹", "738,236,844,522"},
                {"暗孔雀", "1024,221,1127,516"}};

                TanSuo();
                log.Info("点击进入探索-御灵.");
                if (Db.GameSetting.IsTifu)
                    Operational.Click(new Position(454, 644, 494, 681));
                else
                    Operational.Click(new Position(354, 640, 401, 688));

                Sleep(3000, false);
                log.Info($"点击进入御灵-{_class}.");
                Operational.Click(new Position(keys[_class]));
                Sleep(3000, false);
                if (Scene.NowScene.Contains("御灵")) return;

                // 重试进入御灵场景
                YuLing(_class, retryCount + 1);
            }

            /// <summary>
            /// 去活动场景
            /// </summary>
            internal void Activity(string _class = "")
            {
                log.Debug($"[Activity] 当前场景:{Scene.NowScene}");
                if (Scene.NowScene.Contains("庭院"))
                {
                    QiangZhiReset();

                    //if (new List<string>() { "琼月华宫", "鏖刀禁府", "冶武兵道" }.Contains(Scene.CourtyardSkin))
                    //{
                    //    log.Info("当前设置的皮肤为特殊皮肤：先进入探索再返回到庭院后继续..");
                    //    TanSuo();
                    //    TingYuan();
                    //}
                    //else
                    //{
                    log.Info("滑动一下屏幕.");
                    Operational.Slide_Pos(new Position(1169, 86, 1196, 105), new(159, 134, 186, 155));
                    //}
                    Sleep(1000, false);
                    log.Info("点击进入活动场景.");
                    //Operational.Click(GoTanHuoDongPosition[Scene.CourtyardSkin]);

                    Dm.MoveTo(GoTanHuoDongPosition[Scene.CourtyardSkin].X, GoTanHuoDongPosition[Scene.CourtyardSkin].Y);
                    Dm.LeftDown();
                    Dm.delay(200);
                    Dm.LeftUp();

                    log.Info("首次进入活动，请手动跳过动画，并提前配置好爬塔阵容！10s后继续.");
                    Sleep(6000, false);
                    Operational.Click(1081, 122, 1119, 156);
                    Operational.Click(1081, 122, 1119, 156);
                    Sleep(1000, false);
                    Operational.Click(1081, 122, 1119, 156);
                    Sleep(4000, false);
                    if (_class.Contains("时间裂缝"))
                        Operational.Click(813, 216, 843, 354); //时间裂缝(退治Ⅰ)
                    else if (_class.Contains("日轮之影"))
                    {
                        Operational.Click(1091, 327, 1107, 398); //日轮之影(爬塔Ⅱ)
                        Sleep(1000, false);
                        Operational.Click(58, 168, 194, 207); //战场攻占
                        Sleep(1000, false);
                    }
                    else if (_class.Contains("阵练演武"))
                    {
                        Operational.Click(1091, 327, 1107, 398); //阵练演武(爬塔Ⅱ)
                        Sleep(1000, false);
                        Operational.Click(84, 263, 210, 293); //阵练演武
                        Sleep(1000, false);
                    }
                    else
                    {
                        Operational.Click(1091, 327, 1107, 398); //日轮之影(爬塔)
                        Sleep(1000, false);
                        Operational.Click(58, 168, 194, 207); //战场攻占
                        Sleep(1000, false);
                    }

                    Sleep(1200, false);
                }
            }

            /// <summary>
            /// 去契灵 有体服适配
            /// </summary>
            internal void QiLing(int retryCount = 0)
            {
                log.Debug($"[QiLing] 当前场景:{Scene.NowScene}");
                if (Scene.NowScene.Contains("契灵")) return;

                // 添加重试次数限制，防止无限循环
                if (retryCount >= 3)
                {
                    log.Warn("尝试进入契灵场景失败,重试次数已达上限，尝试重置场景...");

                    // 尝试重置场景
                    if (ResetScene(out string resetSceneName))
                    {
                        log.Info($"场景重置成功，当前场景：{resetSceneName}，继续尝试进入契灵场景");
                        Sleep(1500, false);

                        // 重置计数器，从头开始尝试
                        QiLing(0);
                        return;
                    }
                    else
                    {
                        log.Error("场景重置失败，无法进入契灵场景");
                        return;
                    }
                }

                TanSuo();
                log.Info("点击进入探索-契灵.");
                if (Db.GameSetting.IsTifu)
                    Operational.Click(1138, 641, 1184, 690);
                else
                    Operational.Click(1044, 639, 1090, 686);
                Sleep(3000, false);
                if (Scene.NowScene.Contains("契灵")) return;

                // 重试进入契灵场景
                QiLing(retryCount + 1);
            }

            /// <summary>
            /// 去日轮
            /// </summary>
            internal void RiLun(int retryCount = 0)
            {
                log.Debug($"[RiLun] 当前场景:{Scene.NowScene}");
                if (Scene.NowIsScene("日轮")) return;

                // 添加重试次数限制，防止无限循环
                if (retryCount >= 3)
                {
                    log.Warn("尝试进入日轮场景失败,重试次数已达上限，尝试重置场景...");

                    // 尝试重置场景
                    if (ResetScene(out string resetSceneName))
                    {
                        log.Info($"场景重置成功，当前场景：{resetSceneName}，继续尝试进入日轮场景");
                        Sleep(1500, false);

                        // 重置计数器，从头开始尝试
                        RiLun(0);
                        return;
                    }
                    else
                    {
                        log.Error("场景重置失败，无法进入日轮场景");
                        return;
                    }
                }

                TanSuo();
                log.Info("点击进入探索-御魂.");
                Operational.Click(new Position("166,642,206,690"));
                Sleep(3000, false);
                log.Info("点击进入御魂-日轮.");
                Operational.Click(new Position("855,179,1007,428"));
                Sleep(3000, false);
                if (Scene.NowIsScene("日轮")) return;

                // 重试进入日轮场景
                RiLun(retryCount + 1);
            }

            /// <summary>
            /// 去业原火
            /// </summary>
            internal void YeYuanHuo(int retryCount = 0)
            {
                log.Debug($"[YeYuanHuo] 当前场景:{Scene.NowScene}");
                if (Scene.NowIsScene("业原火")) return;

                // 添加重试次数限制，防止无限循环
                if (retryCount >= 3)
                {
                    log.Warn("尝试进入业原火场景失败,重试次数已达上限，尝试重置场景...");

                    // 尝试重置场景
                    if (ResetScene(out string resetSceneName))
                    {
                        log.Info($"场景重置成功，当前场景：{resetSceneName}，继续尝试进入业原火场景");
                        Sleep(1500, false);

                        // 重置计数器，从头开始尝试
                        YeYuanHuo(0);
                        return;
                    }
                    else
                    {
                        log.Error("场景重置失败，无法进入业原火场景");
                        return;
                    }
                }

                TanSuo();
                log.Info("点击进入探索-御魂.");
                Operational.Click(new Position("166,642,206,690"));
                Sleep(3000, false);
                log.Info("点击进入御魂-业原火.");
                Operational.Click(new Position("473,188,625,480"));
                Sleep(3000, false);
                if (Scene.NowIsScene("业原火")) return;

                // 重试进入业原火场景
                YeYuanHuo(retryCount + 1);
            }

            /// <summary>
            /// 处理自动Buff
            /// </summary>
            private void HandleAutoBuff()
            {
                if (Scene._hasHandledBuffInCurrentTask) return;  // 如果已经处理过，直接返回

                if (Db.Data.Get("AutoBuff") is bool autoBuff && autoBuff)
                {
                    log.Info("您开启了自动启停Buff，执行开Buff...");
                    Sleep(3000, false);

                    var buffConfig = new Dictionary<string, string>();
                    var buffTypes = Db.Data.Get("BuffTypes") as Dictionary<string, string>;
                    if (buffTypes != null && buffTypes.Count > 0)
                    {
                        buffConfig = buffTypes;
                        RunTaskFactory.Run<BuffTask>(Db, Scene.Dm, Scene.Ct, 1, "自动Buff", buffConfig);
                        Scene._hasHandledBuffInCurrentTask = true;  // 标记为已处理
                    }
                }
            }

            /// <summary>
            /// 庭院强制恢复
            /// </summary>
            private void QiangZhiReset()
            {
                var obj = Db.Data.Get("FirstStart");
                if (obj is null)
                {
                    log.Info("首次启动，庭院尚未初始化，进入图鉴初始化人物位置..");
                    Operational.Click(116, 628);
                    Sleep(2000, false);
                    Operational.Click(608, 19);
                    Sleep(1000, false);
                    Operational.Click(51, 41);
                    Sleep(1000, false);
                    Db.Data.Set("FirstStart", true);
                    Sleep(2000, false);
                }
            }
        }
    }
}