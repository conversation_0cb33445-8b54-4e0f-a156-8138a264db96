using System;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using DanDing1.Helpers;
using DanDing1.Models.Super;

namespace DanDing1.Views.UserControls
{
    public partial class SingleGamePreviewItem : UserControl, INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private DwmPreviewHelper _previewHelper;
        private bool _isPreviewActive;
        public bool IsPreviewActive
        {
            get => _isPreviewActive;
            private set
            {
                if (_isPreviewActive != value)
                {
                    _isPreviewActive = value;
                    PreviewStatusChanged?.Invoke(this, value);
                    OnPropertyChanged(nameof(IsPreviewActive));
                }
            }
        }

        private double _lastWidth;
        private double _lastHeight;
        private IntPtr _sourceHandle;
        private SuperMultiGame_DataModel _game;
        private bool _useSimulatorHandle = true;

        // 游戏日志变化监听器
        private PropertyChangedEventHandler _gamePropertyChangedHandler;

        public SuperMultiGame_DataModel Game
        {
            get => _game;
            set
            {
                // 如果已有游戏，取消对其属性变化的监听
                if (_game != null && _gamePropertyChangedHandler != null)
                {
                    _game.PropertyChanged -= _gamePropertyChangedHandler;
                }

                _game = value;
                if (_game != null)
                {
                    GameNameText.Text = _game.GameName;
                    _sourceHandle = _useSimulatorHandle ? new IntPtr(_game.MumuHandle) : new IntPtr(_game.GameHandle);

                    // 更新最新日志显示
                    UpdateLastLogText();

                    // 监听游戏属性变化，更新日志显示
                    _gamePropertyChangedHandler = new PropertyChangedEventHandler(Game_PropertyChanged);
                    _game.PropertyChanged += _gamePropertyChangedHandler;
                }
            }
        }

        /// <summary>
        /// 监听游戏属性变化，当LastLogMessage变化时更新UI
        /// </summary>
        private void Game_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(SuperMultiGame_DataModel.LastLogMessage))
            {
                // 在UI线程上更新日志文本
                Dispatcher.BeginInvoke(new Action(UpdateLastLogText));
            }
        }

        /// <summary>
        /// 更新最新日志文本显示
        /// </summary>
        private void UpdateLastLogText()
        {
            if (_game != null)
            {
                // 通过FindName查找控件
                var lastLogText = this.FindName("LastLogText") as TextBlock;
                if (lastLogText != null)
                {
                    lastLogText.Text = string.IsNullOrEmpty(_game.LastLogMessage)
                        ? "暂无日志"
                        : _game.LastLogMessage;
                }
            }
        }

        public bool UseSimulatorHandle
        {
            get => _useSimulatorHandle;
            set
            {
                _useSimulatorHandle = value;
                if (_game != null)
                {
                    _sourceHandle = _useSimulatorHandle ? new IntPtr(_game.MumuHandle) : new IntPtr(_game.GameHandle);
                }
            }
        }

        public event EventHandler<bool> PreviewStatusChanged;

        public SingleGamePreviewItem()
        {
            InitializeComponent();
            SizeChanged += SingleGamePreviewItem_SizeChanged;
        }

        private void SingleGamePreviewItem_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (IsPreviewActive && PreviewBorder != null)
            {
                // 获取实际尺寸
                var actualWidth = PreviewBorder.ActualWidth;
                var actualHeight = PreviewBorder.ActualHeight;

                // 确保尺寸有效
                if (actualWidth < 1 || actualHeight < 1)
                    return;

                // 如果是明显的大小变化（通常是由于布局变化），则重新启动预览
                if (Math.Abs(actualWidth - _lastWidth) > 5 || Math.Abs(actualHeight - _lastHeight) > 5)
                {
                    // 使用延迟执行确保布局已更新
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        RestartPreview();
                    }), System.Windows.Threading.DispatcherPriority.Render);
                }
                else if (_previewHelper != null)
                {
                    // 对于小的大小变化，仅更新大小
                    _previewHelper.UpdateSize(actualWidth, actualHeight);
                }

                _lastWidth = actualWidth;
                _lastHeight = actualHeight;
            }
        }

        public void StartPreview()
        {
            try
            {
                if (_game == null)
                {
                    ShowError("未设置游戏");
                    return;
                }

                _sourceHandle = _useSimulatorHandle ? new IntPtr(_game.MumuHandle) : new IntPtr(_game.GameHandle);
                if (_sourceHandle == IntPtr.Zero)
                {
                    ShowError(_useSimulatorHandle ? "模拟器句柄无效" : "游戏句柄无效");
                    return;
                }

                var window = Window.GetWindow(this);
                if (window == null)
                {
                    ShowError("无法获取窗口句柄");
                    return;
                }

                // 停止现有预览
                StopPreview();

                _previewHelper = new DwmPreviewHelper(window, _sourceHandle, PreviewBorder);

                if (_previewHelper.StartPreview())
                {
                    IsPreviewActive = true;
                    NoPreviewText.Visibility = Visibility.Collapsed;
                    PreviewErrorText.Visibility = Visibility.Collapsed;

                    // 设置活跃状态的视觉效果
                    PreviewBorder.BorderThickness = new Thickness(2);
                    PreviewBorder.BorderBrush = System.Windows.Media.Brushes.LightGreen;
                }
                else
                {
                    ShowError($"启动预览失败: {_previewHelper.LastError}");
                }
            }
            catch (Exception ex)
            {
                ShowError($"启动预览失败: {ex.Message}");
            }
        }

        public void StopPreview()
        {
            if (_previewHelper != null)
            {
                _previewHelper.StopPreview();
                _previewHelper = null;
            }
            IsPreviewActive = false;
            NoPreviewText.Visibility = Visibility.Visible;
            PreviewErrorText.Visibility = Visibility.Collapsed;

            // 恢复默认视觉效果
            PreviewBorder.BorderThickness = new Thickness(1);
            PreviewBorder.BorderBrush = (System.Windows.Media.Brush)FindResource("ControlElevationBorderBrush");
        }

        public void RestartPreview()
        {
            if (IsPreviewActive)
            {
                // 保存当前窗口句柄，避免重新获取可能引起的变化
                var currentHandle = _sourceHandle;
                StopPreview();

                // 使用延迟执行确保UI更新完成
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    _sourceHandle = currentHandle;
                    StartPreview();
                }), System.Windows.Threading.DispatcherPriority.Render);
            }
        }

        // 仅更新位置，不重启预览
        public void UpdatePosition()
        {
            if (IsPreviewActive && _previewHelper != null)
            {
                // 直接更新位置，不重新创建缩略图
                _previewHelper.UpdatePosition();
            }
        }

        private void ShowError(string message)
        {
            PreviewErrorText.Text = message;
            PreviewErrorText.Visibility = Visibility.Visible;
            NoPreviewText.Visibility = Visibility.Collapsed;
        }

        public void Dispose()
        {
            // 移除属性变化监听器
            if (_game != null && _gamePropertyChangedHandler != null)
            {
                _game.PropertyChanged -= _gamePropertyChangedHandler;
            }

            StopPreview();
            _previewHelper = null;
        }

        private void SyncButton_Click(object sender, RoutedEventArgs e)
        {
            if (IsPreviewActive)
            {
                StopPreview();
            }
            else
            {
                // 先刷新句柄，确保使用最新的句柄值
                if (_game != null)
                {
                    // 重新从游戏对象获取最新的句柄
                    _sourceHandle = _useSimulatorHandle ? new IntPtr(_game.MumuHandle) : new IntPtr(_game.GameHandle);
                }

                // 检查刷新后的句柄是否有效
                if (_sourceHandle == IntPtr.Zero)
                {
                    ShowError(_useSimulatorHandle ? "模拟器句柄无效" : "游戏句柄无效");
                }
                else
                {
                    StartPreview();
                }
            }
        }
    }
}