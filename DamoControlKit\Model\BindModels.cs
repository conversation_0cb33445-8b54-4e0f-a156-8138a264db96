﻿using DamoControlKit.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DamoControlKit
{
    /// <summary>
    /// 获取内置的绑定模式
    /// </summary>
    public static class BindModels
    {
        public static BindModel mumu_model1 = new BindModel()
        {
            Display = Display.dx2,
            Mouse = Mouse.dx_mouse_api,
            Keypad = Keypad.windows,
            Mode = 0,
            Public = ""
        };

        public static BindModel mumu_model2 = new BindModel()
        {
            Display = Display.dx2,
            Mouse = Mouse.windows,
            Keypad = Keypad.windows,
            Mode = 0,
            Public = ""
        };

        public static BindModel mumu_model4 = new BindModel()
        {
            Display = Display.dx2,
            Mouse = Mouse.windows3,
            Keypad = Keypad.windows,
            Mode = 0,
            Public = ""
        };

        public static BindModel mumu_model3 = new BindModel()
        {
            Display = Display.gdi2,
            Mouse = Mouse.windows,
            Keypad = Keypad.windows,
            Mode = 0,
            Public = ""
        };

        public static BindModel GetBindModel(string name, int mode)
        {
            switch (mode)
            {
                case 1:
                    if (name == "mumu")
                        return mumu_model1;
                    break;

                case 2:
                    if (name == "mumu")
                        return mumu_model2;
                    break;

                case 3:
                    if (name == "mumu")
                        return mumu_model3;
                    break;

                default:
                    break;
            }
            throw new Exception("尚未支持的绑定模式组合！请从1-3中选择一个模式！");
        }
    }
}