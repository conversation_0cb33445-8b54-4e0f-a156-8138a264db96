﻿using DanDing1.Helpers;
using DanDing1.Services;
using DanDing1.ViewModels.Pages;
using DanDing1.ViewModels.Windows;
using DanDing1.Views.Pages;
using DanDing1.Views.Windows;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Toolkit.Uwp.Notifications;
using System.IO;
using System.Reflection;
using System.Windows.Threading;
using Wpf.Ui;
using Wpf.Ui.Abstractions;
using System.Windows;
using XHelper;
using DanDing1.Views.UserControls;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace DanDing1
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App
    {
        // The.NET Generic Host provides dependency injection, configuration, logging, and other services.
        // https://docs.microsoft.com/dotnet/core/extensions/generic-host
        // https://docs.microsoft.com/dotnet/core/extensions/dependency-injection
        // https://docs.microsoft.com/dotnet/core/extensions/configuration
        // https://docs.microsoft.com/dotnet/core/extensions/logging
        private static readonly IHost _host = Host
            .CreateDefaultBuilder()
            .ConfigureAppConfiguration(c => { c.SetBasePath(Path.GetDirectoryName(Assembly.GetEntryAssembly()!.Location)); })
            .ConfigureServices((context, services) =>
            {
                services.AddHostedService<ApplicationHostService>();

                // Page resolver service
                services.AddSingleton<INavigationViewPageProvider, PageService>();

                // Theme manipulation
                services.AddSingleton<IThemeService, ThemeService>();

                // TaskBar manipulation
                services.AddSingleton<ITaskBarService, TaskBarService>();

                // Service containing navigation, same as INavigationWindow... but without window
                services.AddSingleton<INavigationService, NavigationService>();

                //注册弹窗服务
                services.AddSingleton<IContentDialogService, ContentDialogService>();

                // Main window with navigation
                services.AddSingleton<INavigationWindow, MainWindow>();
                services.AddSingleton<MainWindowViewModel>();

                //任务添加页面
                services.AddTransient<AddTaskPropertyViewModel>();

                //任务详情窗口
                services.AddTransient<TaskConfigWindow>();
                services.AddSingleton<TaskConfigWindowViewModel>();

                //超级多开窗口
                services.AddSingleton<SuperMultiGamesWindow>();
                services.AddSingleton<SuperMultiGamesWindowViewModel>();

                //超级多开窗口 - 游戏配置窗口
                services.AddSingleton<SuperMultiGameConfigWindow>();
                services.AddSingleton<SuperMultiGameConfigWindowViewModel>();

                //登录页面
                services.AddSingleton<LoadPage>();
                services.AddSingleton<LoadViewModel>();

                //游戏脚本主页面
                services.AddSingleton<Game1Page>();
                services.AddSingleton<Game1ViewModel>();
                services.AddSingleton<Game2Page>();
                services.AddSingleton<Game2ViewModel>();
                services.AddSingleton<Game3Page>();
                services.AddSingleton<Game3ViewModel>();
                services.AddSingleton<Game4Page>();
                services.AddSingleton<Game4ViewModel>();

                //日志页面
                services.AddSingleton<LogPage>();
                services.AddSingleton<LogViewModel>();
                //用户偏好页面
                services.AddSingleton<UserConfigPage>();
                services.AddSingleton<UserConfigViewModel>();
                //设置页面
                services.AddSingleton<SettingsPage>();
                services.AddSingleton<SettingsViewModel>();

                //注册日志存储和同步服务
                services.AddSingleton<ILogStorage, LogStorageService>();
                services.AddSingleton<LogSyncService>();

                // 注册状态上报服务
                services.AddSingleton<ScriptStatusReporterService>();
                // 注册脚本状态同步服务
                services.AddSingleton<ScriptSyncService>();

                // 注册托盘图标服务
                services.AddSingleton<TrayIconService>();

                // 注册定时调度窗口
                services.AddSingleton<SchedulerWindowViewModel>();
                services.AddSingleton<Views.Windows.SchedulerWindow>();
            }).Build();

        /// <summary>
        /// Gets registered service.
        /// </summary>
        /// <typeparam name="T">Type of the service to get.</typeparam>
        /// <returns>Instance of the service or <see langword="null"/>.</returns>
        public static T GetService<T>()
            where T : class
        {
            return _host.Services.GetService(typeof(T)) as T;
        }

        /// <summary>
        /// Occurs when the application is loading.
        /// </summary>
        private void OnStartup(object sender, StartupEventArgs e)
        {
            _host.Start();
            //提示用户导入缓存的配置文件
            Utils.PromptImportConfigIfNeeded();
            //开启定时器
            if (Debugger.IsAttached)
                GlobalData.Instance.CheckTimer = new Timer(new Timers().CheckUserStatus, null, 1000 * 60 * 2, 0);
            else
                GlobalData.Instance.CheckTimer = new Timer(new Timers().CheckUserStatus, null, 1000 * 60 * 5, 1000 * 60 * 60 * 1);

            //延迟触发的定时器 5秒后执行 InitMuMuConfigs
            GlobalData.Instance.InitMuMuConfigsTimer = new Timer(new TimerCallback(new Timers().InitMuMuConfigs), null, 1000 * 5, Timeout.Infinite);
            //注册日志上传定时器，10秒执行一次
            GlobalData.Instance.LogUploadTimer = new Timer(new Timers().UploadUserLogs, null, 1000 * 5, 1000 * 10);
            //清空通知
            ToastNotificationManagerCompat.History.Clear();
            //清空临时文件夹
            Utils.ClearTempFolder();

            var services = new ServiceCollection();
            ConfigureServices(services);
            var serviceProvider = services.BuildServiceProvider();
            // 将服务提供者存储在资源中，供全局访问
            Application.Current.Resources["ServiceProvider"] = serviceProvider;

            // 初始化托盘图标服务 - 使用单例模式
            var trayIconService = TrayIconService.Instance;
        }

        private void ConfigureServices(ServiceCollection services)
        {
            services.AddTransient<TaskConfigWindowViewModel>();
            services.AddTransient<TaskConfigWindow>();
            services.AddSingleton<SchedulerWindowViewModel>();
            services.AddSingleton<Views.Windows.SchedulerWindow>();
        }

        /// <summary>
        /// Occurs when the application is closing.
        /// </summary>
        private async void OnExit(object sender, ExitEventArgs e)
        {
            // 清理MainWindowViewModel资源
            var mainWindowViewModel = GetService<MainWindowViewModel>();
            mainWindowViewModel?.CleanupOnExit();

            // 释放托盘图标资源
            var trayIconService = TrayIconService.Instance;
            if (trayIconService != null)
            {
                trayIconService.Dispose();
                XLogger.Info("应用退出时已释放托盘图标资源");
            }

            // 备份AppConfig配置文件到AppData路径
            Utils.BackupAppConfig();
            await _host.StopAsync();
            _host.Dispose();
        }

        private Dictionary<string, string> ErrorTip = new()
        {
            {"络连接检查失败，请确保网络正常并且可以","该问题在切换完节点后没有重启的情况下也会发生哦~您可以继续操作也可以可以尝试重启！不要担心~" }
        };

        /// <summary>
        /// Occurs when an exception is thrown by an application but not handled.
        /// </summary>
        private void OnDispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            // For more info see https://docs.microsoft.com/en-us/dotnet/api/system.windows.application.dispatcherunhandledexception?view=windowsdesktop-6.0
            e.Handled = true;
            if (GlobalData.Instance.Global_Data.TryGetValue("自动更新", out object? autoUpdate) && (bool)autoUpdate)
                return;
            XLogger.SaveException(e.Exception);
            string errorMessage = $"发生未处理的异常: \r\n" +
                $"{e.Exception.Message}\r\n\r\n";

            // 检查是否有匹配的错误提示
            foreach (var tip in ErrorTip)
            {
                if (e.Exception.Message.Contains(tip.Key))
                {
                    errorMessage += $"Tip:\r\n{tip.Value}\r\n\r\n";
                    break;
                }
            }

            errorMessage += $"你可以继续操作程序，错误已被记录，请及时向我反馈，记得带上日志！";

            Utils.ShowMessage("程序错误，你可以继续操作程序，错误已被记录！", errorMessage);
        }
    }
}